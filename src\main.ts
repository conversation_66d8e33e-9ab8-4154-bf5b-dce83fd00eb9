import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'
import './styles/index.scss'
import { useSettingsStore } from '@/stores/settings'
import { mqttService } from '@/services/mqtt-service'
import { mobileAdapter } from './utils/mobile-optimization'

const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

const pinia = createPinia()
app.use(pinia)
app.use(router)
app.use(ElementPlus)

// 初始化移动端适配
mobileAdapter.init()

// 挂载应用
app.mount('#app')

// 异步初始化数据库
const initializeApp = async () => {
  try {
    const settingsStore = useSettingsStore()
    await settingsStore.initializeDatabase()
    console.log('Application initialized successfully')
  } catch (error) {
    console.error('Failed to initialize application:', error)
  }
}

// 延迟初始化数据库，确保应用先渲染
setTimeout(initializeApp, 100)
