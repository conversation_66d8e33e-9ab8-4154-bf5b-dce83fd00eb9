import { dbInitializer } from './databaseConfig'

/**
 * 数据备份和恢复工具
 */
export class DataBackupManager {
  /**
   * 导出所有数据
   */
  static async exportAllData(): Promise<Blob> {
    try {
      const data = await dbInitializer.exportData()
      const jsonString = JSON.stringify(data, null, 2)
      return new Blob([jsonString], { type: 'application/json' })
    } catch (error) {
      console.error('导出数据失败:', error)
      throw new Error('数据导出失败')
    }
  }

  /**
   * 导入数据
   */
  static async importData(file: File): Promise<void> {
    try {
      const text = await file.text()
      const data = JSON.parse(text)
      await dbInitializer.importData(data)
    } catch (error) {
      console.error('导入数据失败:', error)
      throw new Error('数据导入失败')
    }
  }

  /**
   * 下载备份文件
   */
  static async downloadBackup(): Promise<void> {
    try {
      const blob = await this.exportAllData()
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `smart_water_backup_${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('下载备份失败:', error)
      throw new Error('备份下载失败')
    }
  }

  /**
   * 清空所有数据
   */
  static async clearAllData(): Promise<void> {
    try {
      await dbInitializer.clearAllData()
    } catch (error) {
      console.error('清空数据失败:', error)
      throw new Error('数据清空失败')
    }
  }

  /**
   * 重置为默认数据
   */
  static async resetToDefault(): Promise<void> {
    try {
      await this.clearAllData()
      // 重新初始化会自动填充默认数据
      await dbInitializer.initialize()
    } catch (error) {
      console.error('重置数据失败:', error)
      throw new Error('数据重置失败')
    }
  }
}

/**
 * 自动备份管理器
 */
export class AutoBackupManager {
  private static interval: NodeJS.Timeout | null = null
  private static isEnabled = false

  /**
   * 启动自动备份
   */
  static start(intervalHours: number = 24): void {
    if (this.interval) {
      this.stop()
    }

    this.isEnabled = true
    const intervalMs = intervalHours * 60 * 60 * 1000

    this.interval = setInterval(async () => {
      try {
        await this.performAutoBackup()
      } catch (error) {
        console.error('自动备份失败:', error)
      }
    }, intervalMs)

    console.log(`自动备份已启动，间隔: ${intervalHours} 小时`)
  }

  /**
   * 停止自动备份
   */
  static stop(): void {
    if (this.interval) {
      clearInterval(this.interval)
      this.interval = null
    }
    this.isEnabled = false
    console.log('自动备份已停止')
  }

  /**
   * 执行自动备份
   */
  private static async performAutoBackup(): Promise<void> {
    try {
      const data = await dbInitializer.exportData()
      const backupKey = `auto_backup_${Date.now()}`
      
      // 存储到 localStorage（实际应用中可能需要其他存储方案）
      localStorage.setItem(backupKey, JSON.stringify(data))
      
      // 清理旧的自动备份（保留最近5个）
      this.cleanOldBackups()
      
      console.log('自动备份完成:', backupKey)
    } catch (error) {
      console.error('自动备份失败:', error)
    }
  }

  /**
   * 清理旧的备份
   */
  private static cleanOldBackups(): void {
    try {
      const backupKeys = Object.keys(localStorage)
        .filter(key => key.startsWith('auto_backup_'))
        .sort()

      // 保留最近的5个备份
      if (backupKeys.length > 5) {
        const keysToRemove = backupKeys.slice(0, backupKeys.length - 5)
        keysToRemove.forEach(key => localStorage.removeItem(key))
      }
    } catch (error) {
      console.error('清理旧备份失败:', error)
    }
  }

  /**
   * 获取自动备份列表
   */
  static getAutoBackups(): Array<{ key: string; date: Date; size: string }> {
    try {
      const backupKeys = Object.keys(localStorage)
        .filter(key => key.startsWith('auto_backup_'))
        .sort()

      return backupKeys.map(key => {
        const timestamp = parseInt(key.replace('auto_backup_', ''))
        const data = localStorage.getItem(key) || ''
        const size = (data.length / 1024).toFixed(2) + ' KB'
        
        return {
          key,
          date: new Date(timestamp),
          size
        }
      })
    } catch (error) {
      console.error('获取自动备份列表失败:', error)
      return []
    }
  }

  /**
   * 恢复自动备份
   */
  static async restoreAutoBackup(backupKey: string): Promise<void> {
    try {
      const backupData = localStorage.getItem(backupKey)
      if (!backupData) {
        throw new Error('备份数据不存在')
      }

      const data = JSON.parse(backupData)
      await dbInitializer.importData(data)
      
      console.log('自动备份恢复成功:', backupKey)
    } catch (error) {
      console.error('恢复自动备份失败:', error)
      throw new Error('备份恢复失败')
    }
  }

  /**
   * 删除自动备份
   */
  static deleteAutoBackup(backupKey: string): void {
    try {
      localStorage.removeItem(backupKey)
      console.log('自动备份已删除:', backupKey)
    } catch (error) {
      console.error('删除自动备份失败:', error)
    }
  }

  /**
   * 检查是否启用
   */
  static get enabled(): boolean {
    return this.isEnabled
  }
}

/**
 * 数据同步管理器
 */
export class DataSyncManager {
  /**
   * 检查数据完整性
   */
  static async checkDataIntegrity(): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
  }> {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      // 检查数据库连接
      // 这里可以添加更多的数据完整性检查
      
      return {
        isValid: errors.length === 0,
        errors,
        warnings
      }
    } catch (error) {
      errors.push('数据库连接失败')
      return {
        isValid: false,
        errors,
        warnings
      }
    }
  }

  /**
   * 修复数据
   */
  static async repairData(): Promise<boolean> {
    try {
      // 这里可以添加数据修复逻辑
      console.log('数据修复完成')
      return true
    } catch (error) {
      console.error('数据修复失败:', error)
      return false
    }
  }

  /**
   * 优化数据库
   */
  static async optimizeDatabase(): Promise<void> {
    try {
      // 这里可以添加数据库优化逻辑
      console.log('数据库优化完成')
    } catch (error) {
      console.error('数据库优化失败:', error)
      throw new Error('数据库优化失败')
    }
  }
}

// 导出实例
export const dataBackup = DataBackupManager
export const autoBackup = AutoBackupManager
export const dataSync = DataSyncManager
