/*!************************************************************************
*
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2013 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
if(typeof s7viewers=="undefined"){s7viewers={}}else{if(typeof s7viewers!="object"){throw new Error("Cannot initialize a root 's7viewers' package. s7viewers is not an object")}}if(!s7viewers.MixedMediaViewer){(function(){var a;s7viewers.MixedMediaViewer=function(c){this.sdkBasePath="../../s7viewersdk/2025.5/MixedMediaViewer/";this.containerId=null;this.params={};this.handlers=[];this.onInitFail=null;this.initializationComplete=false;this.initCalled=false;this.firstMediasetParsed=false;this.isDisposed=false;this.utilsScriptElm=null;this.fixinputmarker=null;this.indicatormode="page";this.numberOfItems=null;this.sdkProvided=false;this.lockurldomains=true;this.defaultCSS="MixedMediaViewer_light.css";if(typeof c=="object"){if(c.containerId){this.setContainerId(c.containerId)}if(c.params){for(var d in c.params){if(c.params.hasOwnProperty(d)&&c.params.propertyIsEnumerable(d)){this.setParam(d,c.params[d])}}}if(c.handlers){this.setHandlers(c.handlers)}if(c.localizedTexts){this.setLocalizedTexts(c.localizedTexts)}}var b=this;this.onChangeZoomState=function(e){if(e.s7event.state.hasCapability(a.ZoomCapabilityState.ZOOM_IN)){b.zoomInButton.activate()}else{b.zoomInButton.deactivate()}if(e.s7event.state.hasCapability(a.ZoomCapabilityState.ZOOM_OUT)){b.zoomOutButton.activate()}else{b.zoomOutButton.deactivate()}if(e.s7event.state.hasCapability(a.ZoomCapabilityState.ZOOM_RESET)){b.zoomResetButton.activate()}else{b.zoomResetButton.deactivate()}}};s7viewers.MixedMediaViewer.cssClassName="s7mixedmediaviewer";s7viewers.MixedMediaViewer.prototype.modifiers={zoommode:{params:["zoommode"],defaults:["continuous"],ranges:[["auto","continuous","inline"]]},indicatormode:{params:["indicatormode"],defaults:["page"],ranges:[["item","page"]]},swatchoverlay:{params:["enabled"],defaults:[true]}};s7viewers.MixedMediaViewer.prototype.setContainerId=function(b){if(this.isDisposed){return}this.containerId=b||null};s7viewers.MixedMediaViewer.getCodeBase=function(){var h="";var c="";var f=null;if(document.scripts){f=document.scripts}else{f=document.getElementsByTagName("script")}for(var e=0;e<f.length;e++){var g=f[e].src;var b=/^\s*(http[s]?:\/\/[^\/]*)?(.*)(\/(js|js_orig)\/MixedMediaViewer\.js)/.exec(g);if(b&&b.length==5){if(typeof b[1]!=="undefined"){h=b[1]}h+=b[2];c=g;break}}if((h!="")&&(h.lastIndexOf("/")!=h.length-1)){h+="/"}var d=/\/etc\/dam\/viewers\//;s7viewers.MixedMediaViewer.codebase={contentUrl:h,isDAM:d.test(c)}};s7viewers.MixedMediaViewer.getCodeBase();s7viewers.MixedMediaViewer.prototype.getContentUrl=function(){return s7viewers.MixedMediaViewer.codebase.contentUrl};s7viewers.MixedMediaViewer.prototype.symbols={"Container.LABEL":"Mixed media viewer","PanRightButton.TOOLTIP":"Spin East","PanLeftButton.TOOLTIP":"Spin West"};s7viewers.MixedMediaViewer.prototype.includeViewer=function(){a.Util.lib.include("s7sdk.event.Event");a.Util.lib.include("s7sdk.common.Button");a.Util.lib.include("s7sdk.common.Container");a.Util.lib.include("s7sdk.image.ZoomView");a.Util.lib.include("s7sdk.image.FlyoutZoomView");a.Util.lib.include("s7sdk.set.SpinView");a.Util.lib.include("s7sdk.set.MediaSet");a.Util.lib.include("s7sdk.set.Swatches");a.Util.lib.include("s7sdk.video.VideoControls");a.Util.lib.include("s7sdk.video.VideoPlayer");a.Util.lib.include("s7sdk.common.ControlBar");a.Util.lib.include("s7sdk.set.SetIndicator");this.trackingManager=new a.TrackingManager();var d={en:this.symbols,defaultLocale:"en"};this.s7params=new a.ParameterManager(null,null,{asset:"MediaSet.asset"},this.getContentUrl()+this.defaultCSS,this.lockurldomains);var f="";if(this.s7params.params.config&&(typeof(this.s7params.params.config)=="string")){f=",";if(this.s7params.params.config.indexOf("/")>-1){f+=this.s7params.params.config.split("/")[1]}else{f+=this.s7params.params.config}}this.s7params.setViewer("505,2025.5.0"+f);this.s7params.setDefaultLocalizedTexts(d);for(var b in this.params){if(b!="localizedtexts"){this.s7params.push(b,this.params[b])}else{this.s7params.setLocalizedTexts(this.params[b])}}this.s7params.push("OOTBPresetCSSFileToClassMap",{html5_mixedmediaviewer_dark:"s7mixedmediaviewer_dark",html5_inlinemixedmediaviewer_dark:"s7mixedmediaviewer_dark",html5_mixedmediaviewer_light:""});this.container=null;this.zoomView=null;this.flyoutZoomView=null;this.spinView=null;this.videoPlayer=null;this.activeView=null;this.zoommode=null;this.isFlyoutView=null;this.toolbarContainer=null;this.imageViewContainer=null;this.zoomInButton=null;this.zoomOutButton=null;this.zoomResetButton=null;this.spinLeftButton=null;this.spinRightButton=null;this.fullScreenButton=null;this.videoFullScreenButton=null;this.closeButton=null;this.closedCaptionButton=null;this.videoControls=null;this.playPauseButton=null;this.videoScrubber=null;this.videoTime=null;this.mutableVolume=null;this.bcr_videoControls=null;this.bcr_playPauseButton=null;this.bcr_videoTime=null;this.bcr_videoScrubber=null;this.storedPlayingState=false;this.mediaSet=null;this.s7mediasetDesc=null;this.singleImage=null;this.colorSwatches=null;this.currentColorSwatchesFrame=null;this.colorSwatchesActive=false;this.swatches=null;this.currentSwatchesFrame=null;this.containerHeight=null;this.setindicator=null;this.visibilityManagerZoom=null;this.visibilityManagerSpin=null;this.visibilityManagerVideo=null;this.captionButtonPosition=null;this.volumeButtonPosition=null;this.videoTimePosition=null;this.captionSpecified=true;this.curCaption=null;this.storedCaptionEnabled=true;this.isCSSforCaptionButton=true;this.needsRebuild=false;this.prevAsset=null;this.isPosterImage=null;this.initialFrame=0;this.setPages=true;this.isOrientationMarkerForcedChanged=false;var c=this;function g(){c.s7params.params.aemmode=s7viewers.MixedMediaViewer.codebase.isDAM?"1":"0";c.s7params.push("Swatches.tmblayout","0,1");c.s7params.push("Swatches.textpos","none");c.s7params.push("VideoPlayer.autoplay","0");c.s7params.push("VideoPlayer.iconeffect","1,-1,0.3,0");c.s7params.push("ZoomView.frametransition","slide");c.s7params.push("FlyoutZoomView.frametransition","fade");c.s7params.push("initialbitrate","1400");c.s7params.push("FlyoutZoomView.enablehd","never");if(!c.s7params.get("caption")){c.captionSpecified=false;c.storedCaptionEnabled=false}else{c.curCaption=c.s7params.get("caption");c.storedCaptionEnabled=(c.curCaption.split(",")[1]=="1")}c.s7params.push("VideoPlayer.caption",",0");if(a.browser.device.name=="desktop"){c.s7params.push("ZoomView.singleclick","zoomReset")}if(a.browser.device.name=="desktop"){c.s7params.push("ZoomView.doubleclick","reset")}if(a.browser.device.name=="desktop"){c.s7params.push("SpinView.singleclick","zoomReset")}if(a.browser.device.name=="desktop"){c.s7params.push("SpinView.doubleclick","reset")}if(a.browser.device.name!="desktop"||a.browser.supportsTouch()){c.s7params.push("Swatches.enablescrollbuttons","0")}var l=c.getParam("fixinputmarker");if(l){c.fixinputmarker=(l=="s7touchinput"||l=="s7mouseinput")?c.fixinputmarker=l:null}var h=c.getURLParameter("fixinputmarker");if(h){c.fixinputmarker=(h=="s7touchinput"||h=="s7mouseinput")?c.fixinputmarker=h:null}if(c.fixinputmarker){if(c.fixinputmarker==="s7mouseinput"){c.addClass(c.containerId,"s7mouseinput")}else{if(c.fixinputmarker==="s7touchinput"){c.addClass(c.containerId,"s7touchinput")}}}else{if(a.browser.supportsTouch()){c.addClass(c.containerId,"s7touchinput")}else{c.addClass(c.containerId,"s7mouseinput")}}var k=c.s7params.get("presetClasses");if(k&&k.length>0){k.forEach(function(m){c.addClass(c.containerId,m)})}var j=c.getParam("indicatormode");if(j){c.indicatormode=(j=="page"||j=="item")?c.indicatormode=j:"page"}var i=c.getURLParameter("indicatormode");if(i){c.indicatormode=(i=="page"||i=="item")?c.indicatormode=i:"page"}c.parseMods();c.container=new a.common.Container(c.containerId,c.s7params,c.containerId+"_container");if(c.container.isInLayout()){e()}else{c.container.addEventListener(a.event.ResizeEvent.ADDED_TO_LAYOUT,e,false)}}function e(){c.container.removeEventListener(a.event.ResizeEvent.ADDED_TO_LAYOUT,e,false);var H=document.getElementById(c.containerId);var A=H.style.minHeight;H.style.minHeight="1px";var K=document.createElement("div");K.style.position="relative";K.style.width="100%";K.style.height="100%";H.appendChild(K);var aa=K.offsetHeight;if(K.offsetHeight<=1){H.style.height="100%";aa=K.offsetHeight}H.removeChild(K);H.style.minHeight=A;var S=false;switch(c.s7params.get("responsive","auto")){case"fit":S=false;break;case"constrain":S=true;break;default:S=aa==0;break}c.updateCSSMarkers();c.updateOrientationMarkers();if(c.container.isFixedSize()){c.viewerMode="fixed"}else{if(S){c.viewerMode="ratio"}else{c.viewerMode="free"}}c.containerHeight=c.container.getHeight();c.imageViewContainer=document.createElement("div");c.imageViewContainer.setAttribute("id",c.containerId+"_imageViewContainer");var j=document.getElementById(c.container.getInnerContainerId());j.appendChild(c.imageViewContainer);var u=!(a.browser.device.name=="desktop");if(c.zoommode=="auto"){if(!u){c.zoommode="inline"}else{c.zoommode="continuous"}}if(c.zoommode=="inline"){c.s7params.push("MediaSet.flattenSets","1,1")}c.flyoutZoomView=new a.image.FlyoutZoomView(c.containerId+"_imageViewContainer",c.s7params,c.containerId+"_flyoutZoomView");c.zoomView=new a.image.ZoomView(c.containerId+"_imageViewContainer",c.s7params,c.containerId+"_zoomView");c.isFlyoutView=(c.zoommode=="inline"||(c.zoommode=="auto"&&!u));if(c.isFlyoutView){c.trackingManager.attach(c.flyoutZoomView);c.zoomView.setCSS(".s7zoomview","display","none")}else{c.trackingManager.attach(c.zoomView);c.flyoutZoomView.setCSS(".s7flyoutzoomview","display","none")}c.spinView=new a.set.SpinView(c.container,c.s7params,c.containerId+"_spinView");c.trackingManager.attach(c.spinView);c.setindicator=new a.set.SetIndicator(c.container,c.s7params,c.containerId+"_setIndicator");if(!u){c.setindicator.setCSS(".s7setindicator","display","none")}c.videoPlayer=new a.video.VideoPlayer(c.container,c.s7params,c.containerId+"_videoPlayer");c.videoPlayer.getObj().setAttribute("aria-live","polite");c.trackingManager.attach(c.videoPlayer);c.videoControls=new a.common.ControlBar(c.container,c.s7params,c.containerId+"_controls");c.bcr_videoControls=document.getElementById(c.containerId+"_controls").getBoundingClientRect();c.videoControlsHeight=c.videoControls.getHeight();c.videoControls.attachView(c.videoPlayer,false);c.playPauseButton=new a.common.PlayPauseButton(c.containerId+"_controls",c.s7params,c.containerId+"_playPauseButton");c.videoScrubber=new a.video.VideoScrubber(c.containerId+"_controls",c.s7params,c.containerId+"_videoScrubber");c.videoTime=new a.video.VideoTime(c.containerId+"_controls",c.s7params,c.containerId+"_videoTime");c.bcr_playPauseButton=document.getElementById(c.containerId+"_playPauseButton").getBoundingClientRect();c.bcr_videoTime=document.getElementById(c.containerId+"_videoTime").getBoundingClientRect();c.bcr_videoScrubber=document.getElementById(c.containerId+"_videoScrubber").getBoundingClientRect();c.closedCaptionButton=new a.common.ClosedCaptionButton(c.containerId+"_controls",c.s7params,c.containerId+"_closedCaptionButton");c.closedCaptionButton.addEventListener("click",O);c.audioCaptionsSelector=new a.video.AudioCaptions(c.containerId+"_controls",c.s7params,c.containerId+"_audioCaptionsSelector");c.mutableVolume=new a.video.MutableVolume(c.containerId+"_controls",c.s7params,c.containerId+"_mutableVolume");c.mutableVolume.setSelected(c.videoPlayer.muted());var o=a.Util.getStyle(document.getElementById(c.containerId+"_closedCaptionButton"),"left");var ab=a.Util.getStyle(document.getElementById(c.containerId+"_closedCaptionButton"),"right");c.isCSSforCaptionButton=((!isNaN(ab.substring(0,ab.length-2))&&Number(o.substring(0,o.length-2))!=0));c.videoFullScreenButton=new a.common.FullScreenButton(c.containerId+"_controls",c.s7params,c.containerId+"_videofullScreenButton");c.supportsInline=c.videoPlayer.supportsInline();if(!c.supportsInline){c.videoControls.setCSS(".s7controlbar","display","none")}c.swatches=new a.set.Swatches(c.container,c.s7params,c.containerId+"_swatches");c.trackingManager.attach(c.swatches);c.toolbarContainer=document.createElement("div");c.toolbarContainer.className="s7toolbarcontainer";c.toolbarContainer.setAttribute("id",c.containerId+"_toolbarContainer");c.toolbarContainer.style.position="absolute";c.toolbarContainer.style.width=c.container.getWidth()+"px";c.toolbarContainer.style.top=c.containerHeight-c.swatches.getHeight()+"px";c.toolbarContainer.style.height="0px";c.toolbarContainer.style.zIndex="1";j.insertBefore(c.toolbarContainer,document.getElementById(c.containerId+"_swatches"));c.zoomInButton=new a.common.ZoomInButton(c.containerId+"_toolbarContainer",c.s7params,c.containerId+"_zoomInButton");c.zoomOutButton=new a.common.ZoomOutButton(c.containerId+"_toolbarContainer",c.s7params,c.containerId+"_zoomOutButton");c.zoomResetButton=new a.common.ZoomResetButton(c.containerId+"_toolbarContainer",c.s7params,c.containerId+"_zoomResetButton");c.divSpinButton=document.createElement("div");c.divSpinButton.setAttribute("id",c.containerId+"_divSpinButton");c.divSpinButton.className="s7spinbuttons";c.divSpinButton.style.position="absolute";c.divSpinButton.style.top=c.containerHeight-c.swatches.getHeight()+"px";j.insertBefore(c.divSpinButton,document.getElementById(c.containerId+"_swatches"));c.spinLeftButton=new a.common.PanLeftButton(c.containerId+"_divSpinButton",c.s7params,c.containerId+"_spinLeftButton");c.spinRightButton=new a.common.PanRightButton(c.containerId+"_divSpinButton",c.s7params,c.containerId+"_spinRightButton");if((c.s7params.get("closeButton","0")=="1")||(c.s7params.get("closeButton","0").toLowerCase()=="true")){c.closeButton=new a.common.CloseButton(c.container,c.s7params,c.containerId+"_closeButton");c.closeButton.addEventListener("click",J)}c.divColorSwatches=document.createElement("div");c.divColorSwatches.setAttribute("id",c.containerId+"_divColorSwatches");c.divColorSwatches.className="s7colorswatches";c.divColorSwatches.style.position="absolute";c.divColorSwatches.style.zIndex="1";c.divColorSwatches.style.top=c.containerHeight-c.swatches.getHeight()+"px";j.insertBefore(c.divColorSwatches,document.getElementById(c.containerId+"_swatches"));c.colorSwatches=new a.set.Swatches(c.containerId+"_divColorSwatches",c.s7params,c.containerId+"_colorSwatches");if(a.browser.device.name!="desktop"){c.colorSwatches.setCSS(".s7swatches","pointer-events","none")}c.trackingManager.attach(c.colorSwatches);c.fullScreenButton=new a.common.FullScreenButton(c.containerId+"_toolbarContainer",c.s7params,c.containerId+"_fullScreenButton");c.notCustomSize=c.container.isPopup()&&!c.container.isFixedSize();if(c.notCustomSize&&!c.container.supportsNativeFullScreen()){c.fullScreenButton.setCSS(".s7fullscreenbutton","display","none")}if(!c.videoPlayer.supportsVolumeControl()){c.mutableVolume.setCSS(".s7mutablevolume","display","none")}if(c.viewerMode=="ratio"){H.style.height="auto"}function C(ag,af){var ae,ad,ah;if(ag&&ag.style){af=af.toLowerCase();ad=af.replace(/\-([a-z])/g,function(aj,ai){return ai.toUpperCase()});ah=ag.style[ad];if(!ah){ae=document.defaultView||window;if(ae.getComputedStyle){ah=ae.getComputedStyle(ag,"").getPropertyValue(af)}else{if(ag.currentStyle){ah=ag.currentStyle[ad]}}}}return ah||""}U();c.mediaSet=new a.set.MediaSet(null,c.s7params,c.containerId+"_mediaSet");c.trackingManager.attach(c.mediaSet);if(u&&c.swatchoverlay){c.visibilityManagerZoom=new a.VisibilityManager();c.visibilityManagerSpin=new a.VisibilityManager();if(c.isFlyoutView){c.visibilityManagerZoom.reference(c.flyoutZoomView)}else{c.visibilityManagerZoom.reference(c.zoomView)}c.visibilityManagerSpin.reference(c.spinView);c.visibilityManagerZoom.attach(c.closeButton);c.visibilityManagerSpin.attach(c.closeButton);if(!c.isFlyoutView){c.visibilityManagerZoom.attach(c.zoomInButton);c.visibilityManagerZoom.attach(c.zoomOutButton);c.visibilityManagerZoom.attach(c.zoomResetButton)}if(!c.notCustomSize||c.container.supportsNativeFullScreen()){c.visibilityManagerZoom.attach(c.fullScreenButton)}c.visibilityManagerSpin.attach(c.zoomInButton);c.visibilityManagerSpin.attach(c.zoomOutButton);c.visibilityManagerSpin.attach(c.zoomResetButton);c.visibilityManagerSpin.attach(c.spinLeftButton);c.visibilityManagerSpin.attach(c.spinRightButton);if(!c.notCustomSize||c.container.supportsNativeFullScreen()){c.visibilityManagerSpin.attach(c.fullScreenButton)}c.visibilityManagerZoom.attach(c.colorSwatches);c.visibilityManagerZoom.attach(c.swatches);c.visibilityManagerSpin.attach(c.swatches);c.visibilityManagerZoom.attach(c.setindicator);c.visibilityManagerSpin.attach(c.setindicator);if(c.supportsInline){c.visibilityManagerVideo=new a.VisibilityManager();c.visibilityManagerVideo.reference(c.videoPlayer);c.visibilityManagerVideo.attach(c.closeButton);c.visibilityManagerVideo.attach(c.videoControls);c.visibilityManagerVideo.attach(c.swatches);c.visibilityManagerVideo.attach(c.setindicator)}}c.swatches.addEventListener(a.AssetEvent.SWATCH_SELECTED_EVENT,m,false);c.colorSwatches.addEventListener(a.AssetEvent.SWATCH_SELECTED_EVENT,h,false);c.mediaSet.addEventListener(a.AssetEvent.NOTF_SET_PARSED,i,false);c.container.addEventListener(a.event.ResizeEvent.COMPONENT_RESIZE,Y,false);c.container.addEventListener(a.event.ResizeEvent.FULLSCREEN_RESIZE,w,false);c.container.addEventListener(a.event.ResizeEvent.REMOVED_FROM_LAYOUT,M,false);c.container.addEventListener(a.event.ResizeEvent.ADDED_TO_LAYOUT,X,false);c.container.addEventListener(a.event.ResizeEvent.SIZE_MARKER_CHANGE,p,false);if(u){c.swatches.addEventListener(a.event.SwatchEvent.SWATCH_PAGE_CHANGE,R,false)}c.zoomInButton.addEventListener("click",Q,false);c.zoomOutButton.addEventListener("click",t,false);c.zoomResetButton.addEventListener("click",x,false);c.spinLeftButton.addEventListener("click",L,false);c.spinRightButton.addEventListener("click",G,false);c.fullScreenButton.addEventListener("click",q);c.videoFullScreenButton.addEventListener("click",q);if(!c.isFlyoutView){c.zoomView.addEventListener(a.event.AssetEvent.ASSET_CHANGED,E,false);c.zoomView.addEventListener(a.event.CapabilityStateEvent.NOTF_ZOOM_CAPABILITY_STATE,c.onChangeZoomState,false)}c.spinView.addEventListener(a.event.CapabilityStateEvent.NOTF_SPIN_CAPABILITY_STATE,c.onChangeZoomState,false);c.videoPlayer.addEventListener(a.event.CapabilityStateEvent.NOTF_VIDEO_CAPABILITY_STATE,P,false);c.videoPlayer.addEventListener(a.event.VideoEvent.NOTF_DURATION,ac,false);c.videoPlayer.addEventListener(a.event.VideoEvent.NOTF_LOAD_PROGRESS,V,false);c.videoPlayer.addEventListener(a.event.VideoEvent.NOTF_CURRENT_TIME,W,false);c.videoPlayer.addEventListener(a.event.VideoEvent.NOTF_METADATA_LOAD_COMPLETE,s);c.playPauseButton.addEventListener("click",T);c.videoScrubber.addEventListener(a.SliderEvent.NOTF_SLIDER_UP,v,false);c.mutableVolume.addEventListener("click",F);c.mutableVolume.addEventListener(a.SliderEvent.NOTF_SLIDER_DOWN,z,false);c.mutableVolume.addEventListener(a.SliderEvent.NOTF_SLIDER_MOVE,N,false);c.mutableVolume.addEventListener(a.SliderEvent.NOTF_SLIDER_UP,N,false);c.audioCaptionsSelector.addEventListener(a.event.AudioCaptionEvent.NOTF_AUDIO_CHANGE,function(ad){c.videoPlayer.selectAudioTrack(ad.s7event.data)});c.audioCaptionsSelector.addEventListener(a.event.AudioCaptionEvent.NOTF_SUBTITLE_CHANGE,function(ad){var ae=ad.s7event.data;if(ae==="off"){c.videoPlayer.disableTextTrack();return}c.videoPlayer.selectTextTrack(ad.s7event.data);c.closedCaptionButton.setSelected(false)});c.audioCaptionsSelector.addEventListener(a.event.AudioCaptionEvent.NOTF_MENU_STATE_CHANGE,function(ae){var ad=ae.s7event.data;c.videoControls.allowAutoHide(!ad)});if(("onorientationchange" in window)&&a.browser.device!=""){window.addEventListener("orientationchange",r)}c.trackingManager.setCallback(B);if((typeof(AppMeasurementBridge)=="function")&&(c.isConfig2Exist==true)){c.appMeasurementBridge=new AppMeasurementBridge(c.trackingParams);c.appMeasurementBridge.setVideoPlayer(c.videoPlayer)}function r(ad){}function Q(){if(c.activeView&&(c.activeView===c.zoomView||c.activeView===c.spinView)){c.activeView.zoomIn()}}function t(){if(c.activeView&&(c.activeView===c.zoomView||c.activeView===c.spinView)){c.activeView.zoomOut()}}function L(){if(c.activeView&&c.activeView===c.spinView){c.activeView.moveFrame(a.Enum.SPIN_DIRECTION.WEST)}}function G(){if(c.activeView&&c.activeView===c.spinView){c.activeView.moveFrame(a.Enum.SPIN_DIRECTION.EAST)}}function x(){if(c.activeView&&(c.activeView===c.zoomView||c.activeView===c.spinView)){c.activeView.zoomReset()}}function i(af){c.s7mediasetDesc=af.s7event.asset;c.numberOfItems=c.s7mediasetDesc.items.length;c.currentSwatchesFrame=null;c.initialFrame=Math.max(0,parseInt((typeof(c.s7params.get("initialframe"))!="undefined")?c.s7params.get("initialframe"):0));if(c.initialFrame<c.s7mediasetDesc.items.length){}else{c.initialFrame=0}var ag=false;if(c.s7mediasetDesc.items.length>1&&((c.s7mediasetDesc.type==a.ItemDescType.VIDEO_SET)||(c.s7mediasetDesc.type==a.ItemDescType.VIDEO_GROUP)||(c.s7mediasetDesc.type==a.ItemDescType.SPIN_SET))){ag=true}var ae=1;if(c.viewerMode=="ratio"){var ai=c.s7mediasetDesc.items[0];while(ai instanceof a.MediaSetDesc&&ai.items&&(ai.itemslength!=0)){ai=ai.items[0]}if(ai&&ai.height){ae=ai.width/ai.height}}if(c.s7mediasetDesc.items.length==1||ag){c.singleImage=true;c.swatches.setCSS(".s7swatches","visibility","hidden");if(c.viewerMode=="fixed"){c.container.resize(c.container.getWidth(),c.containerHeight-c.swatches.getHeight())}else{if(c.viewerMode=="ratio"){c.container.setModifier({aspect:ae})}else{I(c.container.getWidth(),c.container.getHeight(),c.container.getHeight())}}}else{c.singleImage=false;if(c.viewerMode=="fixed"){c.container.resize(c.container.getWidth(),c.containerHeight);I(c.container.getWidth(),c.containerHeight,c.containerHeight-c.swatches.getHeight())}else{if(c.viewerMode=="ratio"){var ad=c.container.getWidth();if(a.browser.device.name=="desktop"||(a.browser.device.name!="desktop"&&!c.swatchoverlay)){c.container.setModifier({aspect:ad/(ad/ae+c.swatches.getHeight())})}else{c.container.setModifier({aspect:ae})}}else{I(c.container.getWidth(),c.containerHeight,c.containerHeight-c.swatches.getHeight())}}c.swatches.setCSS(".s7swatches","visibility","inherit")}var ah={};ah.posterimage=c.isPosterImage?c.isPosterImage:"";c.videoPlayer.setModifier(ah);k(c.container.getWidth(),c.container.getHeight());if(c.s7mediasetDesc.items.length==1||ag){if(ag){var af={};af.s7event={};af.s7event.asset=c.s7mediasetDesc;af.s7event.frame=0;m(af)}else{c.swatches.setMediaSet(c.s7mediasetDesc)}c.swatches.selectSwatch(0,true)}else{c.swatches.setMediaSet(c.s7mediasetDesc);c.swatches.selectSwatch(c.initialFrame,true)}if(c.setindicator){Z()}if((c.handlers.initComplete!=null)&&(typeof c.handlers.initComplete=="function")&&!c.firstMediasetParsed){if(typeof window.s7sdk=="undefined"){window.s7sdk=a}c.handlers.initComplete()}c.firstMediasetParsed=true;n(c.prevAsset)}function Z(){if(c.swatches.getPageCount().x<=1){c.setindicator.setCSS(".s7setindicator","visibility","hidden")}else{c.setindicator.setNumberOfPages(c.swatches.getPageCount().x);c.setindicator.setCSS(".s7setindicator","visibility","inherit")}var ad=c.swatches.getPageCount();if(c.indicatormode==="item"){c.setindicator.setNumberOfPages(c.numberOfItems)}else{c.setindicator.setNumberOfPages(ad.x)}}function D(ae,ad){c.setindicator.setCSS(".s7setindicator","top",ad+"px");c.setindicator.setCSS(".s7setindicator","left",ae+"px")}function R(ad){var ae=ad.s7event.page.x;if(c.setindicator&&c.indicatormode==="page"){c.setindicator.setSelectedPage(ae)}}function q(){if(!c.container.isFullScreen()){if(c.closeButton){c.closeButton.setCSS(".s7closebutton","display","none")}c.container.requestFullScreen()}else{if(c.closeButton){c.closeButton.setCSS(".s7closebutton","display","block")}c.container.cancelFullScreen()}}function I(ai,ah,ae){c.toolbarContainer.style.top=ae+"px";c.toolbarContainer.style.width=ai+"px";c.divColorSwatches.style.top=ae+"px";c.divColorSwatches.style.left=parseInt(ai/2-c.colorSwatches.getWidth()/2)+"px";c.divSpinButton.style.top=ae+"px";var ak=document.getElementById(c.containerId+"_controls");var aj=a.Util.getStyle(ak,"display");ak.style.display="block";c.videoControls.setCSS(".s7controlbar","top",ae-c.videoControlsHeight+"px");c.videoControls.setCSS(".s7controlbar","width",ai+"px");var ag=document.getElementById(c.containerId+"_playPauseButton").getBoundingClientRect();var af=document.getElementById(c.containerId+"_videoTime").getBoundingClientRect();var ad=document.getElementById(c.containerId+"_videoScrubber").getBoundingClientRect();c.videoScrubber.resize(af.left-ag.right-10,(ad.bottom-ad.top));ak.style.display=aj;if(a.browser.device.name!="desktop"&&c.swatchoverlay){if(!c.isFlyoutView){c.zoomView.resize(ai,ah)}else{c.flyoutZoomView.resize(ai,ah)}c.spinView.resize(ai,ah);c.videoPlayer.resize(ai,ah)}else{if(!c.isFlyoutView){c.zoomView.resize(ai,ae)}else{c.flyoutZoomView.resize(ai,ae)}c.spinView.resize(ai,ae);c.videoPlayer.resize(ai,ae)}c.swatches.resize(ai,c.swatches.getHeight());if(c.colorSwatches){c.colorSwatches.reload()}if(u){Z();c.setindicator.resize(ai,c.setindicator.getHeight());D((ai*0.5)-(c.setindicator.getWidth()*0.5),ah-c.swatches.getHeight()-10)}}function n(al){var ai=c.captionSpecified&&l(al,c.swatches.getFrame())&&c.isCSSforCaptionButton;var ap=5;var ag=2;var ah=c.container.isPopup()&&!c.container.isFixedSize()&&!c.container.supportsNativeFullScreen();var ad=c.firstMediasetParsed&&c.videoPlayer.hasMultiAudioMultiCaptions();var ao=c.firstMediasetParsed&&ai&&!ad;var aj=[{id:c.containerId+"_videofullScreenButton",isEnabled:!ah,hide:function(){c.videoFullScreenButton.setCSS(".s7fullscreenbutton","display","none")},setPosition:function(aq){c.videoFullScreenButton.setCSS(".s7fullscreenbutton","right",aq)},show:function(){c.videoFullScreenButton.setCSS(".s7fullscreenbutton","display","block")},},{id:c.containerId+"_mutableVolume",isEnabled:c.videoPlayer.supportsVolumeControl(),hide:function(){c.mutableVolume.setCSS(".s7mutablevolume","display","none")},setPosition:function(aq){c.mutableVolume.setCSS(".s7mutablevolume","right",aq)},show:function(){c.mutableVolume.setCSS(".s7mutablevolume","display","block")},},{id:c.containerId+"_audioCaptionsSelector",isEnabled:ad,hide:function(){c.audioCaptionsSelector.setCSS(".s7audiocaptions","display","none")},setPosition:function(aq){c.audioCaptionsSelector.setCSS(".s7audiocaptions","right",aq)},show:function(){c.audioCaptionsSelector.setCSS(".s7audiocaptions","display","block")},},{id:c.containerId+"_closedCaptionButton",isEnabled:ao,hide:function(){c.closedCaptionButton.setCSS(".s7closedcaptionbutton","display","none")},setPosition:function(aq){c.closedCaptionButton.setCSS(".s7closedcaptionbutton","right",aq)},show:function(){c.closedCaptionButton.setCSS(".s7closedcaptionbutton","display","block")},},{id:c.containerId+"_videoTime",isEnabled:true,hide:function(){c.videoTime.setCSS(".s7videotime","display","none")},setPosition:function(aq){c.videoTime.setCSS(".s7videotime","right",aq)},show:function(){c.videoTime.setCSS(".s7videotime","display","block")},}];var af=ap;for(var am=0;am<aj.length;am++){var an=aj[am];if(an.isEnabled){an.setPosition(af+"px");an.show();var ae=C(document.getElementById(an.id),"width");ae=Number(ae.substring(0,ae.length-2));af+=ae+ag}else{an.hide()}}var ak=document.getElementById(c.containerId+"_playPauseButton").getBoundingClientRect();c.videoScrubber.resize(document.getElementById(c.containerId+"_videoTime").getBoundingClientRect().left-ak.right-10,document.getElementById(c.containerId+"_videoScrubber").getBoundingClientRect().height)}function k(ad,ae){c.updateOrientationMarkers();var af=ae;af=Math.max(c.singleImage?ae:ae-c.swatches.getHeight(),1);c.videoScrubber.resize(0,0);c.videoControls.resize(ad,c.videoControls.getHeight());if(c.closeButton){if(c.container.isFullScreen()){c.closeButton.setCSS(".s7closebutton","display","none")}else{c.closeButton.setCSS(".s7closebutton","display","block")}}I(ad,ae,af)}function Y(ad){if((typeof(ad.target)=="undefined")||(ad.target==document.getElementById(c.containerId+"_container"))){if(!c.container.isInLayout()){return}k(ad.s7event.w,ad.s7event.h)}}function w(ad){if(c.closeButton){if(c.container.isFullScreen()){c.closeButton.setCSS(".s7closebutton","display","none")}else{c.closeButton.setCSS(".s7closebutton","display","block")}}c.fullScreenButton.setSelected(c.container.isFullScreen());c.videoFullScreenButton.setSelected(c.container.isFullScreen());k(ad.s7event.w,ad.s7event.h)}function p(ad){c.updateCSSMarkers()}function X(ad){if(a.browser.device.name!="desktop"){}else{if(c.storedPlayingState){c.videoPlayer.play();c.storedPlayingState=false}}}function M(ad){if(a.browser.device.name!="desktop"){}else{}if(c.videoPlayer.getCapabilityState().hasCapability(a.VideoCapabilityState.PAUSE)){c.storedPlayingState=true;a.Logger.log(a.Logger.INFO,"Pause video");c.videoPlayer.pause()}}function P(ae){var ad=ae.s7event.state;if(ad.hasCapability(a.VideoCapabilityState.PAUSE)){c.playPauseButton.setSelected(false)}else{if(ad.hasCapability(a.VideoCapabilityState.PLAY)||ad.hasCapability(a.VideoCapabilityState.REPLAY)){c.playPauseButton.setSelected(true)}}c.playPauseButton.enableReplay(ad.hasCapability(a.VideoCapabilityState.REPLAY))}function ac(ad){c.videoTime.setDuration(ad.s7event.data);c.videoScrubber.setDuration(ad.s7event.data)}function V(ad){c.videoScrubber.setLoadedPosition(ad.s7event.data)}function W(ad){c.videoTime.setPlayedTime(ad.s7event.data);c.videoScrubber.setPlayedTime(ad.s7event.data)}function s(){var ae=c.videoPlayer.getAudioTracks();var ad=c.videoPlayer.getSubtitlesAndCaptions();c.audioCaptionsSelector.setTracks(ae,ad);n(c.prevAsset)}function T(ad){var ae=c.videoPlayer.getObj();if(!c.playPauseButton.isSelected()){var af=c.videoPlayer.getDuration()-c.videoPlayer.getCurrentTime();if(af<=1){c.videoPlayer.seek(0)}c.videoPlayer.play();ae.setAttribute("aria-label","video is playing")}else{c.videoPlayer.pause();ae.setAttribute("aria-label","video is paused")}}function v(ad){c.videoPlayer.seek(ad.s7event.position*c.videoPlayer.getDuration())}function F(ad){var ae=c.videoPlayer.getObj();if(c.mutableVolume.isSelected()){c.videoPlayer.mute();ae.setAttribute("aria-label","video is muted")}else{c.videoPlayer.unmute();ae.setAttribute("aria-label","video is unmuted");c.videoPlayer.setVolume(c.mutableVolume.getPosition())}}function z(ad){c.videoPlayer.unmute()}function N(ad){c.videoPlayer.setVolume(ad.s7event.position)}function y(ad){c.zoomResetButton.setCSS(".s7zoomresetbutton","display",ad?"":"none");c.zoomInButton.setCSS(".s7zoominbutton","display",ad?"":"none");c.zoomOutButton.setCSS(".s7zoomoutbutton","display",ad?"":"none")}function U(){c.flyoutZoomView.setCSS(".s7flyoutzoomview","display","none");c.zoomView.setCSS(".s7zoomview","display","none");c.spinView.setCSS(".s7spinview","display","none");c.spinLeftButton.setCSS(".s7panleftbutton","display","none");c.spinRightButton.setCSS(".s7panrightbutton","display","none");c.toolbarContainer.style.display="none";var ad=c.videoPlayer.getCapabilityState();if(ad.hasCapability(a.VideoCapabilityState.STOP)||ad.hasCapability(a.VideoCapabilityState.REPLAY)||ad.hasCapability(a.VideoCapabilityState.PAUSE)){c.videoPlayer.stop()}c.videoControls.setCSS(".s7controlbar","position","absolute");c.videoControls.setCSS(".s7controlbar","left","-99999px");c.videoControls.setCSS(".s7controlbar","visibility","hidden");c.videoPlayer.setCSS(".s7videoplayer","position","absolute");c.videoPlayer.setCSS(".s7videoplayer","left","-99999px");c.divColorSwatches.style.display="none";c.colorSwatchesActive=false;c.currentColorSwatchesFrame=null}function m(ah){var af=ah.s7event.asset;if(c.currentSwatchesFrame!=ah.s7event.frame||af!=c.prevAsset||c.needsRebuild){U();if(c.setindicator){if(c.indicatormode==="item"){c.setindicator.setSelectedPage(ah.s7event.frame)}else{c.setindicator.setSelectedPage(c.swatches.getCurrentPage().x)}}switch(af.type){case a.ItemDescType.IMG:if(c.flyoutZoomView||c.zoomView){var ae=new a.MediaSetDesc();ae.name=new Date().getTime();var ad=new a.ImageDesc(ae,af.type,af.name,af.swatch,af.width,af.height,af.version,af.isDefault,af.mod,af.pmod,af.label,null,null,null,(af.maps&&af.maps.length)?true:false,false,true);ae.items.push(ad);if(c.isFlyoutView){y(false);c.activeView=c.flyoutZoomView;c.flyoutZoomView.setCSS(".s7flyoutzoomview","display","block");c.flyoutZoomView.setItem(ad)}else{y(true);c.activeView=c.zoomView;c.zoomView.setItem(ad);c.zoomView.setCSS(".s7zoomview","display","block");var ag=c.zoomView.getCapabilityState();if(typeof(ag)!="undefined"){c.onChangeZoomState({s7event:{state:ag}})}}if(c.visibilityManagerZoom){c.visibilityManagerZoom.detach(c.colorSwatches)}c.toolbarContainer.style.display="block"}break;case a.ItemDescType.IMAGE_SET:if(c.flyoutZoomView||c.zoomView){if(c.isFlyoutView){y(false);c.activeView=c.flyoutZoomView;c.colorSwatchesActive=false;c.flyoutZoomView.setCSS(".s7flyoutzoomview","display","block")}else{y(true);c.activeView=c.zoomView;c.colorSwatchesActive=true;c.zoomView.setCSS(".s7zoomview","display","block");var ag=c.zoomView.getCapabilityState();if(typeof(ag)!="undefined"){c.onChangeZoomState({s7event:{state:ag}})}}c.colorSwatches.setMediaSet(af);c.colorSwatches.selectSwatch(0,false);c.toolbarContainer.style.display="block";c.divColorSwatches.style.display="";if(c.visibilityManagerZoom){c.visibilityManagerZoom.attach(c.colorSwatches)}}break;case a.ItemDescType.SPIN_SET:if(c.spinView){y(true);c.activeView=c.spinView;c.spinLeftButton.setCSS(".s7panleftbutton","display","block");c.spinRightButton.setCSS(".s7panrightbutton","display","block");c.spinView.setMediaSet(af);c.spinView.setCSS(".s7spinview","display","block");c.toolbarContainer.style.display="block";var ag=c.spinView.getCapabilityState();if(typeof(ag)!="undefined"){c.onChangeZoomState({s7event:{state:ag}})}}break;case a.ItemDescType.VIDEO:case a.ItemDescType.VIDEO_GROUP:if(c.videoPlayer){y(false);c.activeView=c.videoPlayer;c.videoPlayer.resetQosMetric();c.videoPlayer.setItem(af);if(c.captionSpecified){c.videoPlayer.setCaption(c.curCaption.split(",")[0]);c.closedCaptionButton.setSelected(c.storedCaptionEnabled)}c.videoPlayer.setCaptionEnabled(l(af,c.swatches.getFrame())&&c.captionSpecified&&c.storedCaptionEnabled);if(c.videoPlayer.supportsInline()){c.videoControls.setCSS(".s7controlbar","visibility","inherit")}else{c.toolbarContainer.style.display="block"}c.videoControls.setCSS(".s7controlbar","position","absolute");c.videoControls.setCSS(".s7controlbar","left","0px");c.videoPlayer.setCSS(".s7videoplayer","position","absolute");c.videoPlayer.setCSS(".s7videoplayer","left","0px");n(af)}break;case a.ItemDescType.VIDEO_SET:if(c.videoPlayer){y(false);c.activeView=c.videoPlayer;c.videoPlayer.resetQosMetric();c.videoPlayer.setItem(af);if(c.captionSpecified){c.videoPlayer.setCaption(c.curCaption.split(",")[0]);c.closedCaptionButton.setSelected(c.storedCaptionEnabled)}c.videoPlayer.setCaptionEnabled(l(af,c.swatches.getFrame())&&c.captionSpecified&&c.storedCaptionEnabled);if(c.videoPlayer.supportsInline()){c.videoControls.setCSS(".s7controlbar","visibility","inherit")}else{c.toolbarContainer.style.display="block"}c.videoControls.setCSS(".s7controlbar","position","absolute");c.videoControls.setCSS(".s7controlbar","left","0px");c.videoPlayer.setCSS(".s7videoplayer","position","absolute");c.videoPlayer.setCSS(".s7videoplayer","left","0px");n(af)}break;default:break}}c.updateCSSMarkers();c.currentSwatchesFrame=ah.s7event.frame;c.prevAsset=af;c.needsRebuild=false}function l(ae,af){for(var ad=0;ad<af;ad++){if((ae.parent.items[ad].type==a.ItemDescType.VIDEO)||(ae.parent.items[ad].type==a.ItemDescType.VIDEO_SET)||(ae.parent.items[ad].type==a.ItemDescType.VIDEO_GROUP)){return false}}return true}function h(ae){var ad=ae.s7event.asset;if(c.activeView&&(c.activeView===c.zoomView)){if(c.activeView){c.activeView.setItem(ad)}}}function E(ad){if(c.colorSwatches&&c.colorSwatchesActive&&ad.s7event.frame!=c.colorSwatches.getFrame()){c.currentColorSwatchesFrame=ad.s7event.frame;c.colorSwatches.selectSwatch(ad.s7event.frame,true)}}function J(){try{if(a.browser.name!="firefox"){window.open(c.getContentUrl()+"s7sdkclose.html","_self")}else{window.close()}}catch(ad){a.Logger.log(a.Logger.WARN,"Cannot close the window")}}function B(af,ae,ai,ad,ag){if(!c.handlers.trackEvent&&c.isConfig2Exist!=true&&a.Modifier.parse(c.s7params.get("launch","true"),[true]).values[0]){if(typeof(_satellite)!="undefined"&&_satellite._dmviewers_v001){c.handlers.trackEvent=_satellite._dmviewers_v001().trackingFn}}if(c.appMeasurementBridge){c.appMeasurementBridge.track(af,ae,ai,ad,ag)}if(c.handlers.trackEvent){if(typeof window.s7sdk=="undefined"){window.s7sdk=a}var ah=c.containerId;c.handlers.trackEvent(ah,ae,ai,ad,ag)}if("s7ComponentEvent" in window){s7ComponentEvent(af,ae,ai,ad,ag)}}function O(){c.videoPlayer.setCaptionEnabled(c.closedCaptionButton.isSelected());c.storedCaptionEnabled=c.closedCaptionButton.isSelected()}}this.s7params.addEventListener(a.Event.SDK_READY,function(){c.initSiteCatalyst(c.s7params,g)},false);this.s7params.setProvidedSdk(this.sdkProvided);this.s7params.init()};s7viewers.MixedMediaViewer.prototype.setParam=function(b,c){if(this.isDisposed){return}this.params[b]=c};s7viewers.MixedMediaViewer.prototype.getParam=function(c){var d=c.toLowerCase();for(var b in this.params){if(b.toLowerCase()==d){return this.params[b]}}return null};s7viewers.MixedMediaViewer.prototype.setParams=function(b){if(this.isDisposed){return}var e=b.split("&");for(var c=0;c<e.length;c++){var d=e[c].split("=");if(d.length>1){this.setParam(d[0],decodeURIComponent(e[c].split("=")[1]))}}};s7viewers.MixedMediaViewer.prototype.s7sdkUtilsAvailable=function(){if(s7viewers.MixedMediaViewer.codebase.isDAM){return typeof(s7viewers.s7sdk)!="undefined"}else{return(typeof(s7classic)!="undefined")&&(typeof(s7classic.s7sdk)!="undefined")}};s7viewers.MixedMediaViewer.prototype.init=function(){if(this.isDisposed){return}if(this.initCalled){return}this.initCalled=true;if(this.initializationComplete){return this}this.lockurldomains=(Boolean(Number(this.params.lockurldomains))||typeof this.params.lockurldomains=="undefined")?1:0;var i=document.getElementById(this.containerId);if(i.className!=""){if(i.className.indexOf(s7viewers.MixedMediaViewer.cssClassName)!=-1){}else{i.className+=" "+s7viewers.MixedMediaViewer.cssClassName}}else{i.className=s7viewers.MixedMediaViewer.cssClassName}this.s7sdkNamespace=s7viewers.MixedMediaViewer.codebase.isDAM?"s7viewers":"s7classic";var d=this.getContentUrl()+this.sdkBasePath+"js/s7sdk/utils/Utils.js?namespace="+this.s7sdkNamespace;var f=null;if(document.scripts){f=document.scripts}else{f=document.getElementsByTagName("script")}if(this.s7sdkUtilsAvailable()){a=(s7viewers.MixedMediaViewer.codebase.isDAM?s7viewers.s7sdk:s7classic.s7sdk);this.sdkProvided=true;if(this.isDisposed){return}a.Util.init();this.includeViewer();this.initializationComplete=true}else{if(!this.s7sdkUtilsAvailable()&&(s7viewers.MixedMediaViewer.codebase.isDAM?s7viewers.S7SDK_S7VIEWERS_LOAD_STARTED:s7viewers.S7SDK_S7CLASSIC_LOAD_STARTED)){this.sdkProvided=true;var h=this;var g=setInterval(function(){if(h.s7sdkUtilsAvailable()){clearInterval(g);a=(s7viewers.MixedMediaViewer.codebase.isDAM?s7viewers.s7sdk:s7classic.s7sdk);if(h.isDisposed){return}a.Util.init();h.includeViewer();h.initializationComplete=true}},100)}else{this.utilsScriptElm=document.createElement("script");this.utilsScriptElm.setAttribute("language","javascript");this.utilsScriptElm.setAttribute("type","text/javascript");var e=document.getElementsByTagName("head")[0];var c=this;function b(){if(!c.utilsScriptElm.executed){c.utilsScriptElm.executed=true;a=(s7viewers.MixedMediaViewer.codebase.isDAM?s7viewers.s7sdk:s7classic.s7sdk);if(c.s7sdkUtilsAvailable()&&a.Util){if(c.isDisposed){return}a.Util.init();c.includeViewer();c.initializationComplete=true;c.utilsScriptElm.onreadystatechange=null;c.utilsScriptElm.onload=null;c.utilsScriptElm.onerror=null}}}if(typeof(c.utilsScriptElm.readyState)!="undefined"){c.utilsScriptElm.onreadystatechange=function(){if(c.utilsScriptElm.readyState=="loaded"){e.appendChild(c.utilsScriptElm)}else{if(c.utilsScriptElm.readyState=="complete"){b()}}};c.utilsScriptElm.setAttribute("src",d)}else{c.utilsScriptElm.onload=function(){b()};c.utilsScriptElm.onerror=function(){};c.utilsScriptElm.setAttribute("src",d);e.appendChild(c.utilsScriptElm);c.utilsScriptElm.setAttribute("data-src",c.utilsScriptElm.getAttribute("src"));c.utilsScriptElm.setAttribute("src","?namespace="+this.s7sdkNamespace)}if(s7viewers.MixedMediaViewer.codebase.isDAM){s7viewers.S7SDK_S7VIEWERS_LOAD_STARTED=true}else{s7viewers.S7SDK_S7CLASSIC_LOAD_STARTED=true}}}return this};s7viewers.MixedMediaViewer.prototype.getDomain=function(b){var c=/(^http[s]?:\/\/[^\/]+)/i.exec(b);if(c==null){return""}else{return c[1]}};s7viewers.MixedMediaViewer.prototype.setAsset=function(b,d){if(this.isDisposed){return}var c=null,e=null;if(d){if(Object.prototype.toString.apply(d)==="[object String]"){c=d}else{if(typeof d=="object"){if(d.caption){c=d.caption}if(d.posterimage){e=d.posterimage}}}}if(this.mediaSet){this.mediaSet.setAsset(b);if(c){this.captionSpecified=true;this.curCaption=c+",1";this.videoPlayer.setCaption(c);this.videoPlayer.setCaptionEnabled(this.storedCaptionEnabled)}else{this.captionSpecified=false;this.curCaption=null;this.videoPlayer.setCaptionEnabled(false)}this.isPosterImage=(e)?e:null}else{this.setParam("asset",b)}};s7viewers.MixedMediaViewer.prototype.setLocalizedTexts=function(b){if(this.isDisposed){return}if(this.s7params){this.s7params.setLocalizedTexts(b)}else{this.setParam("localizedtexts",b)}};s7viewers.MixedMediaViewer.prototype.initSiteCatalyst=function(i,c){var f=i.get("asset",null,"MediaSet").split(",")[0].split(":")[0];this.isConfig2Exist=false;if(f.indexOf("/")!=-1){var d=a.MediaSetParser.findCompanyNameInAsset(f);var h=i.get("config2");this.isConfig2Exist=(h!=""&&typeof h!="undefined");if(this.isConfig2Exist){this.trackingParams={siteCatalystCompany:d,config2:h,isRoot:i.get("serverurl"),contentUrl:this.getContentUrl()};var b=this.getContentUrl()+"../AppMeasurementBridge.jsp?company="+d+(h==""?"":"&preset="+h);if(i.get("serverurl",null)){b+="&isRoot="+i.get("serverurl")}var g=document.createElement("script");g.setAttribute("language","javascript");g.setAttribute("type","text/javascript");g.setAttribute("src",b);var e=document.getElementsByTagName("head");g.onload=g.onerror=function(){if(!g.executed){g.executed=true;if(typeof c=="function"){c()}g.onreadystatechange=null;g.onload=null;g.onerror=null}};g.onreadystatechange=function(){if(g.readyState=="complete"||g.readyState=="loaded"){setTimeout(function(){if(!g.executed){g.executed=true;if(typeof c=="function"){c()}}g.onreadystatechange=null;g.onload=null;g.onerror=null},0)}};e[0].appendChild(g)}else{if(typeof c=="function"){c()}}}};s7viewers.MixedMediaViewer.prototype.getComponent=function(b){if(this.isDisposed){return null}switch(b){case"container":return this.container||null;case"mediaSet":return this.mediaSet||null;case"flyoutZoomView":return this.flyoutZoomView||null;case"zoomView":return this.zoomView||null;case"spinView":return this.spinView||null;case"videoPlayer":return this.videoPlayer||null;case"controls":return this.videoControls||null;case"videoScrubber":return this.videoScrubber||null;case"videoTime":return this.videoTime||null;case"swatches":return this.swatches||null;case"colorSwatches":return this.colorSwatches||null;case"setIndicator":return this.setindicator||null;case"zoomInButton":return this.zoomInButton||null;case"zoomOutButton":return this.zoomOutButton||null;case"zoomResetButton":return this.zoomResetButton||null;case"spinLeftButton":return this.spinLeftButton||null;case"spinRightButton":return this.spinRightButton||null;case"mutableVolume":return this.mutableVolume||null;case"playPauseButton":return this.playPauseButton||null;case"fullScreenButton":return this.fullScreenButton||null;case"closeButton":return this.closeButton||null;case"videoFullScreenButton":return this.videoFullScreenButton||null;case"closedCaptionButton":return this.closedCaptionButton||null;case"parameterManager":return this.s7params||null;default:return null}};s7viewers.MixedMediaViewer.prototype.setHandlers=function(c){if(this.isDisposed){return}if(this.initCalled){return}this.handlers=[];for(var b in c){if(!c.hasOwnProperty(b)){continue}if(typeof c[b]!="function"){continue}this.handlers[b]=c[b]}};s7viewers.MixedMediaViewer.prototype.setModifier=function(d){function l(p,o){var n=[];for(var m in p){if(p.hasOwnProperty(m)){n.push(m)}}var q;while(q=n.pop()){if(q.toLowerCase()===o.toLowerCase()){return true}}return false}if(this.isDisposed){return}var j,f,b,k,e,g;for(j in d){if(!l(this.modifiers,j)){continue}f=this.modifiers[j.toLowerCase()];try{k=d[j];if(f.parseParams===false){e=new a.Modifier([k!=""?k:f.defaults[0]])}else{e=a.Modifier.parse(k,f.defaults,f.ranges)}if(e.values.length==1){var c=e.values[0];if(c!==this[j.toLowerCase()]){this[j.toLowerCase()]=c;this.setModifierInternal(j.toLowerCase())}}else{if(e.values.length>1){b={};for(g=0;g<e.values.length;g++){b[f.params[g]]=e.values[g]}if(b!==this[j.toLowerCase()]){this[j.toLowerCase()]=b;this.setModifierInternal(j.toLowerCase())}}}}catch(h){throw new Error("Unable to process modifier: '"+j+"'. "+h)}}};s7viewers.MixedMediaViewer.prototype.setModifierInternal=function(b){switch(b){case"zoommode":if(this.zoommode=="auto"){if(a.browser.device.name=="desktop"){this.zoommode="inline"}else{this.zoommode="continuous"}}this.needsRebuild=true;if(this.zoommode=="inline"){this.isFlyoutView=true;if(this.zoomView){this.trackingManager.detach(this.zoomView);this.zoomView.removeEventListener(a.event.CapabilityStateEvent.NOTF_ZOOM_CAPABILITY_STATE,self.onChangeZoomState,false);this.zoomView.setCSS(".s7zoomview","display","none")}this.trackingManager.attach(this.flyoutZoomView);if(!(a.browser.device.name=="desktop")){this.visibilityManagerZoom.reference(this.flyoutZoomView)}this.mediaSet.setModifier({flattenSets:"1,1"})}else{this.isFlyoutView=false;if(this.flyoutZoomView){this.trackingManager.detach(this.flyoutZoomView);this.flyoutZoomView.setCSS(".s7flyoutzoomview","display","none")}this.zoomView.addEventListener(a.event.CapabilityStateEvent.NOTF_ZOOM_CAPABILITY_STATE,this.onChangeZoomState,false);if(!(a.browser.device.name=="desktop")){this.visibilityManagerZoom.reference(this.zoomView)}this.trackingManager.attach(this.zoomView);this.mediaSet.setModifier({flattenSets:"0,0"})}break;default:break}};s7viewers.MixedMediaViewer.prototype.parseMods=function(){var g,c,h,b,f,e;for(g in this.modifiers){if(!this.modifiers.hasOwnProperty(g)){continue}c=this.modifiers[g];try{b=this.s7params.get(g,"");if(c.parseParams===false){f=new a.Modifier([b!=""?b:c.defaults[0]])}else{f=a.Modifier.parse(b,c.defaults,c.ranges)}if(f.values.length==1){this[g]=f.values[0]}else{if(f.values.length>1){h={};for(e=0;e<f.values.length;e++){h[c.params[e]]=f.values[e]}this[g]=h}}}catch(d){throw new Error("Unable to process modifier: '"+g+"'. "+d)}}};s7viewers.MixedMediaViewer.prototype.updateCSSMarkers=function(){var c=this.container.getSizeMarker();var b;if(c==a.common.Container.SIZE_MARKER_NONE){return}if(c==a.common.Container.SIZE_MARKER_LARGE){b="s7size_large"}else{if(c==a.common.Container.SIZE_MARKER_SMALL){b="s7size_small"}else{if(c==a.common.Container.SIZE_MARKER_MEDIUM){b="s7size_medium"}}}if(this.containerId){this.setNewSizeMarker(this.containerId,b)}this.reloadInnerComponents()};s7viewers.MixedMediaViewer.prototype.reloadInnerComponents=function(){var c=this.s7params.getRegisteredComponents();for(var b=0;b<c.length;b++){var d=c[b];if(d&&d.restrictedStylesInvalidated()){d.reload()}}};s7viewers.MixedMediaViewer.prototype.setNewSizeMarker=function(f,c){var b=document.getElementById(f).className;var d=/^(.*)(s7size_small|s7size_medium|s7size_large)(.*)$/gi;var e;if(b.match(d)){e=b.replace(d,"$1"+c+"$3")}else{e=b+" "+c}if(b!=e){document.getElementById(f).className=e}};s7viewers.MixedMediaViewer.prototype.dispose=function(){window.removeEventListener("orientationchange",this.onOrientationChange);if(this.zoomView){this.zoomView.removeEventListener(a.event.CapabilityStateEvent.NOTF_ZOOM_CAPABILITY_STATE,this.onChangeZoomState,false)}if(this.spinView){this.spinView.removeEventListener(a.event.CapabilityStateEvent.NOTF_SPIN_CAPABILITY_STATE,this.onChangeZoomState,false)}this.onChangeZoomState=null;if(this.appMeasurementBridge){this.appMeasurementBridge.dispose();this.appMeasurementBridge=null}if(this.trackingManager){this.trackingManager.dispose();this.trackingManager=null}if(this.visibilityManagerZoom){this.visibilityManagerZoom.dispose();this.visibilityManagerZoom=null}if(this.visibilityManagerSpin){this.visibilityManagerSpin.dispose();this.visibilityManagerSpin=null}if(this.visibilityManagerVideo){this.visibilityManagerVideo.dispose();this.visibilityManagerVideo=null}if(this.setindicator){this.setindicator.dispose();this.setindicator=null}if(this.colorSwatches){this.colorSwatches.dispose();this.colorSwatches=null}if(this.swatches){this.swatches.dispose();this.swatches=null}if(this.zoomView){this.zoomView.dispose();this.zoomView=null}if(this.divColorSwatches){this.divColorSwatches.parentNode.removeChild(this.divColorSwatches);delete this.divColorSwatches}if(this.zoomInButton){this.zoomInButton.dispose();this.zoomInButton=null}if(this.zoomOutButton){this.zoomOutButton.dispose();this.zoomOutButton=null}if(this.zoomResetButton){this.zoomResetButton.dispose();this.zoomResetButton=null}if(this.fullScreenButton){this.fullScreenButton.dispose();this.fullScreenButton=null}if(this.closeButton){this.closeButton.dispose();this.closeButton=null}if(this.flyoutZoomView){this.flyoutZoomView.dispose();this.flyoutZoomView=null}if(this.spinLeftButton){this.spinLeftButton.dispose();this.spinLeftButton=null}if(this.spinRightButton){this.spinRightButton.dispose();this.spinRightButton=null}if(this.divSpinButton){this.divSpinButton.parentNode.removeChild(this.divSpinButton);delete this.divSpinButton}if(this.spinView){this.spinView.dispose();this.spinView=null}if(this.videoTime){this.videoTime.dispose();this.videoTime=null}if(this.videoScrubber){this.videoScrubber.dispose();this.videoScrubber=null}if(this.playPauseButton){this.playPauseButton.dispose();this.playPauseButton=null}if(this.mutableVolume){this.mutableVolume.dispose();this.mutableVolume=null}if(this.videoFullScreenButton){this.videoFullScreenButton.dispose();this.videoFullScreenButton=null}if(this.closedCaptionButton){this.closedCaptionButton.dispose();this.closedCaptionButton=null}if(this.videoPlayer){this.videoPlayer.dispose();this.videoPlayer=null}if(this.videoControls){this.videoControls.dispose();this.videoControls=null}if(this.toolbarContainer){this.toolbarContainer.parentNode.removeChild(this.toolbarContainer);delete this.toolbarContainer}if(this.imageViewContainer){this.imageViewContainer.parentNode.removeChild(this.imageViewContainer);delete this.imageViewContainer}if(this.mediaSet){this.mediaSet.dispose();this.mediaSet=null}if(this.s7params){this.s7params.dispose();this.s7params=null}if(this.container){var e=[s7viewers.MixedMediaViewer.cssClassName,"s7touchinput","s7mouseinput","s7size_large","s7size_small","s7size_medium"];var c=document.getElementById(this.containerId).className.split(" ");for(var d=0;d<e.length;d++){var b=c.indexOf(e[d]);if(b!=-1){c.splice(b,1)}}document.getElementById(this.containerId).className=c.join(" ");this.container.dispose();this.container=null}this.prevAsset=null;this.s7mediasetDesc=null;this.currentColorSwatchesFrame=null;this.currentSwatchesFrame=null;this.bcr_videoControls=null;this.bcr_playPauseButton=null;this.bcr_videoTime=null;this.bcr_videoScrubber=null;this.activeView=null;this.handlers=[];this.isDisposed=true};s7viewers.MixedMediaViewer.prototype.updateOrientationMarkers=function(){if(!this.isOrientationMarkerForcedChanged){var b;if(window.innerWidth>window.innerHeight){b="s7device_landscape"}else{b="s7device_portrait"}if(document.getElementById(this.containerId).className.indexOf(b)==-1){this.setNewOrientationMarker(this.containerId,b);this.reloadInnerComponents()}}};s7viewers.MixedMediaViewer.prototype.setNewOrientationMarker=function(f,c){var b=document.getElementById(f).className;var d=/^(.*)(s7device_landscape|s7device_portrait)(.*)$/gi;var e;if(b.match(d)){e=b.replace(d,"$1"+c+"$3")}else{e=b+" "+c}if(b!=e){document.getElementById(f).className=e}};s7viewers.MixedMediaViewer.prototype.forceDeviceOrientationMarker=function(b){switch(b){case"s7device_portrait":case"s7device_landscape":this.isOrientationMarkerForcedChanged=true;if(this.containerId){this.setNewOrientationMarker(this.containerId,b)}this.reloadInnerComponents();break;case null:this.isOrientationMarkerForcedChanged=false;this.updateOrientationMarkers();break;default:break}};s7viewers.MixedMediaViewer.prototype.getURLParameter=function(c){var b=a.ParameterManager.getSanitizedParameters(a.query.params,this.lockurldomains);return b[c]};s7viewers.MixedMediaViewer.prototype.addClass=function(d,c){var b=document.getElementById(d).className.split(" ");if(b.indexOf(c)==-1){b[b.length]=c;document.getElementById(d).className=b.join(" ")}}})()};