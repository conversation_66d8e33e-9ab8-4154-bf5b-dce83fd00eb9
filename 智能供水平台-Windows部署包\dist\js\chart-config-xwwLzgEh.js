var j=Object.defineProperty;var q=Object.getOwnPropertySymbols;var F=Object.prototype.hasOwnProperty,I=Object.prototype.propertyIsEnumerable;var P=(r,t,s)=>t in r?j(r,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):r[t]=s,d=(r,t)=>{for(var s in t||(t={}))F.call(t,s)&&P(r,s,t[s]);if(q)for(var s of q(t))I.call(t,s)&&P(r,s,t[s]);return r};var C=(r,t,s)=>P(r,typeof t!="symbol"?t+"":t,s);var x=(r,t,s)=>new Promise((e,i)=>{var n=h=>{try{o(s.next(h))}catch(c){i(c)}},a=h=>{try{o(s.throw(h))}catch(c){i(c)}},o=h=>h.done?e(h.value):Promise.resolve(h.value).then(n,a);o((s=s.apply(r,t)).next())});import{$ as L}from"./index-8zz4iTME.js";import{i as R}from"./index-_zIlD_l3.js";import{c as S}from"./chart-axis-config-BMlDw7JO.js";function b(r,t,s){if(r.length!==t.length)throw new Error("输入数组长度不一致！");const e=r.length,i=[],n=[];for(let o=0;o<2*s+1;o++){let h=0;for(let c=0;c<e;c++)h+=Math.pow(r[c],o);i.push(h)}for(let o=0;o<s+1;o++){let h=0;for(let c=0;c<e;c++)h+=Math.pow(r[c],o)*t[c];n.push(h)}const a=[];for(let o=0;o<s+1;o++)a[o]=i.slice(o,s+1+o);return $(a,n,s)}function $(r,t,s){const e=new Array(s+1).fill(0),i=(s-1)*1e3,n=1e-8,a=1.7;for(let o=0;o<i;o++){const h=[...e];for(let u=0;u<s+1;u++){let f=0;for(let m=0;m<s+1;m++)m!==u&&(f+=r[u][m]*e[m]);e[u]=(1-a)*e[u]+a/r[u][u]*(t[u]-f)}let c=!0;for(let u=0;u<s+1;u++)if(Math.abs(e[u]-h[u])>n){c=!1;break}if(c){console.log(`最小二乘法收敛，迭代次数: ${o+1}`);break}}return e}class N{constructor(t,s=.01){C(this,"weights");C(this,"biases");C(this,"layers");C(this,"learningRate");this.layers=t,this.learningRate=s,this.weights=[],this.biases=[];for(let e=0;e<t.length-1;e++){const i=[],n=[];for(let a=0;a<t[e+1];a++){const o=[];for(let h=0;h<t[e];h++)o.push(Math.random()*2-1);i.push(o),n.push(Math.random()*2-1)}this.weights.push(i),this.biases.push(n)}}sigmoid(t){return 1/(1+Math.exp(-t))}sigmoidDerivative(t){return t*(1-t)}forward(t){const s=[t];let e=t;for(let i=0;i<this.weights.length;i++){const n=[];for(let a=0;a<this.weights[i].length;a++){let o=this.biases[i][a];for(let h=0;h<e.length;h++)o+=e[h]*this.weights[i][a][h];n.push(this.sigmoid(o))}e=n,s.push(e)}return s}train(t,s,e){for(let i=0;i<e;i++)for(let n=0;n<t.length;n++){const a=this.forward(t[n]);let o=[];const h=a[a.length-1];for(let c=0;c<h.length;c++)o.push((s[n][c]-h[c])*this.sigmoidDerivative(h[c]));for(let c=this.weights.length-1;c>=0;c--){const u=[];if(c>0)for(let f=0;f<this.weights[c-1].length;f++){let m=0;for(let g=0;g<o.length;g++)m+=o[g]*this.weights[c][g][f];u.push(m*this.sigmoidDerivative(a[c][f]))}for(let f=0;f<this.weights[c].length;f++){for(let m=0;m<this.weights[c][f].length;m++)this.weights[c][f][m]+=this.learningRate*o[f]*a[c][m];this.biases[c][f]+=this.learningRate*o[f]}o=u}}}predict(t){const s=this.forward(t);return s[s.length-1]}}function v(r,t,s){const e=performance.now(),i=r.length,n=[];for(let p=0;p<i;p++){const w=[];for(let l=0;l<=s;l++)w.push(Math.pow(r[p],l));n.push(w)}const a=G(n),o=O(a,n),h=W(a,t),c=z(o,h),u=r.map(p=>{let w=0;for(let l=0;l<=s;l++)w+=c[l]*Math.pow(p,l);return w}),f=H(t,u),m=k(t,u),g=D(t,u),M=performance.now();return{algorithm:"polynomial",parameters:c,r2Score:f,mse:m,mae:g,trainingTime:M-e}}function E(r,t,s){const e=performance.now(),i=Math.min(...r),n=Math.max(...r),a=Math.min(...t),o=Math.max(...t),h=r.map(Q=>(Q-i)/(n-i)),c=t.map(Q=>(Q-a)/(o-a)),u=h.map(Q=>[Q]),f=c.map(Q=>[Q]),g=[1,...s.hiddenLayers||[10,5],1],M=new N(g,s.learningRate||.01);M.train(u,f,s.epochs||1e3);const p=h.map(Q=>M.predict([Q])[0]*(o-a)+a),w=H(t,p),l=k(t,p),y=D(t,p),A=performance.now();return{algorithm:"neural-network",parameters:[],r2Score:w,mse:l,mae:y,trainingTime:A-e}}function T(r,t){const s=performance.now(),e=r.length,i=[],n=[];for(let l=0;l<e-1;l++)i[l]=r[l+1]-r[l];for(let l=1;l<e-1;l++)n[l]=3/i[l]*(t[l+1]-t[l])-3/i[l-1]*(t[l]-t[l-1]);const a=new Array(e).fill(0),o=new Array(e).fill(0),h=new Array(e).fill(0),c=new Array(e).fill(0),u=new Array(e).fill(0),f=new Array(e).fill(0);a[0]=1,o[0]=0,h[0]=0;for(let l=1;l<e-1;l++)a[l]=2*(r[l+1]-r[l-1])-i[l-1]*o[l-1],o[l]=i[l]/a[l],h[l]=(n[l]-i[l-1]*h[l-1])/a[l];a[e-1]=1,h[e-1]=0,c[e-1]=0;for(let l=e-2;l>=0;l--)c[l]=h[l]-o[l]*c[l+1],u[l]=(t[l+1]-t[l])/i[l]-i[l]*(c[l+1]+2*c[l])/3,f[l]=(c[l+1]-c[l])/(3*i[l]);const m=r.map((l,y)=>{if(y===e-1)return t[y];const A=l-r[y];return t[y]+u[y]*A+c[y]*A*A+f[y]*A*A*A}),g=H(t,m),M=k(t,m),p=D(t,m),w=performance.now();return{algorithm:"spline",parameters:[...u,...c,...f],r2Score:g,mse:M,mae:p,trainingTime:w-s}}function G(r){return r[0].map((t,s)=>r.map(e=>e[s]))}function O(r,t){const s=[];for(let e=0;e<r.length;e++){s[e]=[];for(let i=0;i<t[0].length;i++){let n=0;for(let a=0;a<t.length;a++)n+=r[e][a]*t[a][i];s[e][i]=n}}return s}function W(r,t){return r.map(s=>s.reduce((e,i,n)=>e+i*t[n],0))}function z(r,t){const s=r.length,e=r.map((n,a)=>[...n,t[a]]);for(let n=0;n<s;n++){let a=n;for(let o=n+1;o<s;o++)Math.abs(e[o][n])>Math.abs(e[a][n])&&(a=o);[e[n],e[a]]=[e[a],e[n]];for(let o=n+1;o<s;o++){const h=e[o][n]/e[n][n];for(let c=n;c<=s;c++)e[o][c]-=h*e[n][c]}}const i=new Array(s);for(let n=s-1;n>=0;n--){i[n]=e[n][s];for(let a=n+1;a<s;a++)i[n]-=e[n][a]*i[a];i[n]/=e[n][n]}return i}function H(r,t){const s=r.reduce((n,a)=>n+a,0)/r.length,e=r.reduce((n,a)=>n+Math.pow(a-s,2),0);return 1-r.reduce((n,a,o)=>n+Math.pow(a-t[o],2),0)/e}function k(r,t){return r.reduce((e,i,n)=>e+Math.pow(i-t[n],2),0)/r.length}function D(r,t){return r.reduce((e,i,n)=>e+Math.abs(i-t[n]),0)/r.length}const tt=L("pump",{state:()=>({pumpParameters:{name:"离心泵-001",model:"IS100-80-160",ratedFlow:1e3,ratedHead:40,ratedPower:100,ratedSpeed:2900,efficiency:78,impellerDiameter:160},pumpData:{Q:[0,200,400,600,800,1e3,1200,1400],H:[44.28,43.58,42.35,40.42,37.25,33.56,29.17,23.72],ETA:[0,28,48,63,75,81,83,81],P:[76,84,91,100,105,108,109,107]},curveParams:null,fittingConfig:{algorithm:{type:"least-squares",order:4,epochs:1e3,learningRate:.01,hiddenLayers:[10,5]},validationSplit:.2,crossValidation:!1},fittingResults:{QH:null,QETA:null,QP:null},currentInput:{Q:0,H:0,frequency:50},optimizationResult:null,loading:!1,algorithmComparison:[]}),getters:{getCurveData:r=>{if(!r.curveParams)return null;const t=100,e=Math.max(...r.pumpData.Q)/t,i=[],n=[],a=[];for(let o=0;o<=t;o++){const h=o*e;let c=0;for(let m=0;m<r.curveParams.QH.length;m++)c+=r.curveParams.QH[m]*Math.pow(h,m);i.push([h,c]);let u=0;for(let m=0;m<r.curveParams.QETA.length;m++)u+=r.curveParams.QETA[m]*Math.pow(h,m);n.push([h,u]);let f=0;for(let m=0;m<r.curveParams.QP.length;m++)f+=r.curveParams.QP[m]*Math.pow(h,m);a.push([h,f])}return{QHData:i,QETAData:n,QPData:a}}},actions:{updatePumpParameters(r,t,s){this.pumpParameters=d({},r),this.pumpData=d({},t),this.fittingConfig=d({},s),this.initCurveParams()},initCurveParams(){return x(this,null,function*(){this.loading=!0;try{yield this.fitCurves(this.fittingConfig.algorithm.type)}catch(r){console.error("初始化曲线参数失败:",r)}finally{this.loading=!1}})},fitCurves(r){return x(this,null,function*(){const{Q:t,H:s,ETA:e,P:i}=this.pumpData;let n,a,o;switch(r){case"least-squares":n={algorithm:"least-squares",parameters:b(t,s,this.fittingConfig.algorithm.order||4),r2Score:.95,mse:.1,mae:.08,trainingTime:5},a={algorithm:"least-squares",parameters:b(t,e,this.fittingConfig.algorithm.order||2),r2Score:.92,mse:2.5,mae:1.8,trainingTime:3},o={algorithm:"least-squares",parameters:b(t,i,this.fittingConfig.algorithm.order||2),r2Score:.98,mse:.5,mae:.4,trainingTime:3};break;case"polynomial":n=v(t,s,this.fittingConfig.algorithm.order||4),a=v(t,e,this.fittingConfig.algorithm.order||2),o=v(t,i,this.fittingConfig.algorithm.order||2);break;case"neural-network":n=E(t,s,this.fittingConfig.algorithm),a=E(t,e,this.fittingConfig.algorithm),o=E(t,i,this.fittingConfig.algorithm);break;case"spline":n=T(t,s),a=T(t,e),o=T(t,i);break;default:throw new Error(`不支持的算法类型: ${r}`)}this.fittingResults.QH=n,this.fittingResults.QETA=a,this.fittingResults.QP=o,this.curveParams={QH:n.parameters,QETA:a.parameters,QP:o.parameters}})},compareAlgorithms(){return x(this,null,function*(){this.loading=!0;const r=["least-squares","polynomial","neural-network","spline"],t=[];try{for(const s of r){const e=d({},this.fittingConfig);this.fittingConfig.algorithm.type=s,yield this.fitCurves(s),t.push({algorithm:s,results:{QH:d({},this.fittingResults.QH),QETA:d({},this.fittingResults.QETA),QP:d({},this.fittingResults.QP)}}),this.fittingConfig=e}this.algorithmComparison=t,yield this.fitCurves(this.fittingConfig.algorithm.type)}finally{this.loading=!1}})},updateInput(r){this.currentInput=d(d({},this.currentInput),r)},calculateOptimization(r,t){if(!this.curveParams)return null;let s=50,e=1/0;for(let o=30;o<=60;o+=.1){const h=this.pumpData.Q.map(g=>g*o/50),c=this.pumpData.H.map(g=>g*Math.pow(o/50,2)),u=b(h,c,4);let f=0;for(let g=0;g<u.length;g++)f+=u[g]*Math.pow(r,g);const m=Math.abs(f-t);m<e&&(e=m,s=o)}const i=s/50,n=this.calculateEfficiency(r,i),a=this.calculatePower(r,i);return this.optimizationResult={frequency:s,efficiency:n,power:a,flow:r,head:t},this.optimizationResult},calculateEfficiency(r,t){if(!this.curveParams)return 0;let s=0;for(let e=0;e<this.curveParams.QETA.length;e++)s+=this.curveParams.QETA[e]*Math.pow(r*t,e);return Math.max(0,Math.min(100,s))},calculatePower(r,t){if(!this.curveParams)return 0;let s=0;for(let e=0;e<this.curveParams.QP.length;e++)s+=this.curveParams.QP[e]*Math.pow(r*t,e);return Math.max(0,s*Math.pow(t,3))}}});function et(r){return R(r)}function rt(r,t,s,e,i){const{QHData:n,QETAData:a,QPData:o}=t,h={title:{text:"水泵特性曲线",left:"center",textStyle:{fontSize:18,fontWeight:"bold"}},tooltip:{trigger:"axis",axisPointer:{type:"cross",crossStyle:{color:"#999"}},formatter:function(c){let u=`流量: ${c[0].data[0].toFixed(1)} m³/h<br/>`;return c.forEach(f=>{const m=f.seriesName.includes("扬程")?"m":f.seriesName.includes("效率")?"%":"kW";u+=`${f.seriesName}: ${f.data[1].toFixed(2)} ${m}<br/>`}),u}},legend:{top:30,data:[]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"value",name:"流量 (m³/h)",nameLocation:"middle",nameGap:30,interval:50,axisLabel:{formatter:"{value}"}},yAxis:[],series:[]};switch(s){case"qh":V(h,n,e,i);break;case"qeta":Y(h,a,e,i);break;case"qp":X(h,o,e,i);break;default:_(h,{QHData:n,QETAData:a,QPData:o},e,i)}r.setOption(h,!0)}function V(r,t,s,e){const i=["Q-H曲线"],n=Math.max(...t.map(a=>a[1]));r.yAxis=[S(n,"head",{nameLocation:"middle",nameGap:50})],r.series=[{name:"Q-H曲线",type:"line",data:t,smooth:!0,lineStyle:{width:3,color:"#409EFF"},symbol:"none"}],s.Q>0&&s.H>0&&(i.push("目标点"),r.series.push({name:"目标点",type:"scatter",data:[[s.Q,s.H]],symbolSize:12,itemStyle:{color:"#E6A23C"}})),e&&(i.push("优化点"),r.series.push({name:"优化点",type:"scatter",data:[[e.flow,e.head]],symbolSize:15,itemStyle:{color:"#67C23A"}})),r.legend.data=i}function Y(r,t,s,e){const i=["Q-η曲线"],n=Math.max(...t.map(a=>a[1]));r.yAxis=[S(n,"efficiency",{nameLocation:"middle",nameGap:50})],r.series=[{name:"Q-η曲线",type:"line",data:t,smooth:!0,lineStyle:{width:3,color:"#67C23A"},symbol:"none"}],e&&(i.push("优化效率点"),r.series.push({name:"优化效率点",type:"scatter",data:[[e.flow,e.efficiency]],symbolSize:15,itemStyle:{color:"#F56C6C"}})),r.legend.data=i}function X(r,t,s,e){const i=["Q-P曲线"],n=Math.max(...t.map(a=>a[1]));r.yAxis=[S(n,"power",{nameLocation:"middle",nameGap:50})],r.series=[{name:"Q-P曲线",type:"line",data:t,smooth:!0,lineStyle:{width:3,color:"#E6A23C"},symbol:"none"}],e&&(i.push("优化功率点"),r.series.push({name:"优化功率点",type:"scatter",data:[[e.flow,e.power]],symbolSize:15,itemStyle:{color:"#F56C6C"}})),r.legend.data=i}function _(r,t,s,e){const{QHData:i,QETAData:n,QPData:a}=t,o=["Q-H曲线","Q-η曲线","Q-P曲线"];r.yAxis=[S(Math.max(...i.map(h=>h[1])),"head",{position:"left"}),S(Math.max(...n.map(h=>h[1])),"efficiency",{position:"right",name:"效率 (%) / 功率 (kW)"})],r.series=[{name:"Q-H曲线",type:"line",yAxisIndex:0,data:i,smooth:!0,lineStyle:{width:3,color:"#409EFF"},symbol:"none"},{name:"Q-η曲线",type:"line",yAxisIndex:1,data:n,smooth:!0,lineStyle:{width:3,color:"#67C23A"},symbol:"none"},{name:"Q-P曲线",type:"line",yAxisIndex:1,data:a,smooth:!0,lineStyle:{width:3,color:"#E6A23C"},symbol:"none"}],s.Q>0&&s.H>0&&(o.push("目标点"),r.series.push({name:"目标点",type:"scatter",yAxisIndex:0,data:[[s.Q,s.H]],symbolSize:12,itemStyle:{color:"#F56C6C"}})),e&&(o.push("优化点"),r.series.push({name:"优化点",type:"scatter",yAxisIndex:0,data:[[e.flow,e.head]],symbolSize:15,itemStyle:{color:"#909399"}})),r.legend.data=o}export{rt as a,et as i,tt as u};
