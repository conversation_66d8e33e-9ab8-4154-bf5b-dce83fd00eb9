// import {LS} from './ls.js'

var isData = JSON.parse(localStorage.getItem("isData"))
// console.log(isData)

var QHSeries = []
var QETASeries = []
var QPSeries = []
var QISeries = []


//  获取数据、拟合参数、生成等间隔点、绘制曲线
var QHParams = LS(isData["Q"], isData["H"], 4)
var QETAParams = LS(isData["Q"], isData["ETA"], 2)
var QPParams = LS(isData["Q"], isData["P"], 2)
// console.log("曲线参数", QHParams, QETAParams, QPParams)



var isDataLen = isData["Q"][isData["Q"].length - 1 ] // 最后一个数值
for ( var itemQ = 0; itemQ < isDataLen; itemQ+=isDataLen/30 ){
    var QHSum = 0
    var QETASum = 0
    var QPSum = 0
    for (var QHParamsItem = 0; QHParamsItem < QHParams.length; QHParamsItem++ ){
        QHSum += QHParams[QHParamsItem] * itemQ**QHParamsItem
    }
    for (var QETAParamsItem = 0; QETAParamsItem < QETAParams.length; QETAParamsItem++ ){
        QETASum += QETAParams[QETAParamsItem] * itemQ**QETAParamsItem
    }
    for (var QPParamsItem = 0; QPParamsItem < QPParams.length; QPParamsItem++ ){
        QPSum += QPParams[QPParamsItem] * itemQ**QPParamsItem
    }

    QHSeries.push([itemQ, QHSum])
    QETASeries.push([itemQ, QETASum])
    QPSeries.push([itemQ, QPSum])
    QISeries.push([itemQ, ])
}




Pfunc()

function Pfunc(){
    var freq = document.getElementById("inputF").value;

    // var freq = 45 

    var labelQP = document.getElementById("inputP").value;

    var dom = document.getElementById("main");
    var myChart = echarts.init(dom);
    var app = {};

    var option;



    echarts.registerTransform(ecStat.transform.regression);

    const colors = ['#000000', 'red'];

    const data1 = QHSeries
          
    const data2 = QETASeries

    const data3 = QPSeries

    //电流，在下面自动补全
    const data4 = [
            [0, ],
            [200, ],
            [400, ],
            [600, ],
            [800, ],
            [1000, ],
            [1200, ],
            [1400, ],
        ];

    const data5 = []
    const data6 = []
    const data7 = []





    //鼠标位置，全局变量
    var qValue;
    var hValue;
    var etaValue;
    var qhOrder = 3;
    var qetaOrder = 2;
    var qpOrder = 2;

    var dataQ1 = []
    var dataH1 = []

    for(var i=0; i<isData["Q"].length; i++){
        dataQ1.push(isData["Q"][i] * freq/50);
        dataH1.push(isData["H"][i] * (freq/50)**2);
    }

    var QHSeries1 = []
    var QHParams1 = LS(dataQ1, dataH1, 4)


    var isDataLen1 = dataQ1[dataQ1.length - 1 ] // 最后一个数值
    for ( var itemQ1 = 0; itemQ1 < isDataLen1; itemQ1+=isDataLen1/30 ){
        var QHSum1 = 0
        for (var QHParamsItem1 = 0; QHParamsItem1 < QHParams1.length; QHParamsItem1++ ){
            QHSum1 += QHParams1[QHParamsItem1] * itemQ1**QHParamsItem1
        }

        QHSeries1.push([itemQ1, QHSum1])
    }
    

    //坐标轴尺度
    var x1data1max = 0
    var y11data1max = 0
    var y21data1max = 0

    for(var i=0; i<data1.length; i++){
        if(data1[i][0] > x1data1max){
            x1data1max = data1[i][0]
        }
        // if(data1[i][1] > y11data1max){
        //     y11data1max = data1[i][1]
        // }
        if(QHSeries1.length != 0){
            if(QHSeries1[i][1] > y11data1max){
                y11data1max = QHSeries1[i][1]
            }
        }else{
            if(data1[i][1] > y11data1max){
                y11data1max = data1[i][1]
                // y11data1max = 20
            }
        }
        if(data3[i][1] > y21data1max){
            y21data1max = data3[i][1]
        }

        //电流
        // data4[i][1] = data3[i][1] * 1000 /(1.732 * 380 * 0.85)
    }
    console.log(y11data1max)
    x1data1max = Math.floor(Math.ceil(x1data1max*(1+1/7)));
    y11data1max = Math.floor(Math.ceil(y11data1max/10)*10);

    var itemstimes = 0;
    while (y21data1max > 10){
        y21data1max = y21data1max/10
        itemstimes += 1
    }
    y21data1max = Math.floor(Math.ceil(y21data1max/6)*6*10*itemstimes);


    option = {
    
        
        dataset: [
            {source: data1},
            {source: data2},
            {source: data3},
            {source: data4},
            {source: data5},
            {source: data6},
            {source: data7}
            
        ],

        
        color: colors,
        tooltip: {
            trigger: 'item',
            showContent: true,
            triggerOn: 'mousemove',
            formatter: function (params,value) {
            //console.log(params)
            return '流量：' + qValue + ' m<br>扬程：' + hValue + ' m<sup>3</sup>/s'
            },
            
            snap: false,
            axisPointer: {
            type: 'cross',
            snap: false,
            label:{show:false}
            }
        },
        
        
        
        grid: [
            { left: '10%', right: '10%', top: '6%', height: '50%' },
            { left: '10%', right: '10%', bottom: '6%', height: '30%' },
        ],
        
        
        toolbox: {
            right: '15%',
            feature: {
            restore: { show: true },
            saveAsImage: { show: true }
            }
        },
        
        
        legend: {
            data: ['流量-扬程',  '流量-效率', '等效线']
        },
        
        
        
        xAxis: [
            //0
            {
                type: 'value',
                splitNumber: 8,
                min: 0,
                max: x1data1max,
                
                gridIndex: 0,
                name: '流量 (Q)',
                nameLocation: 'middle',

                axisTick: {
                    alignWithLabel: true
                },
                


                axisPointer: {
                    label: {
                        formatter: function (params) {
                            qValue = params.value.toFixed(2);
                        }
                    }
                }
            },

            //1
            {
                type: 'value',
                splitNumber: 8,
                min: 0,
                max: x1data1max,
                
                gridIndex: 1,
                name: '流量 (Q)',
                nameLocation: 'middle',

                axisTick: {
                    alignWithLabel: true
                },

            }
            
        ],
        
        
        
        yAxis: [
            

            //0扬程轴
            {
                type: 'value',
                name: '扬程 (H)',
                //maxInterval:10,
                min: 0,
                max: y11data1max,

                gridIndex: 0,
                splitNumber: 12,
                axisLabel: {
                    formatter: '{value} m'
                },
                
                
                axisPointer: {
                    label: {
                        formatter: function (params) {
                            hValue = params.value.toFixed(2);
                        }
                    }
                }
            },
            
            

            //1效率轴
            {
                type: 'value',
                name: '效率 (η)',
                min: 0,
                max: y11data1max * 10,
                //maxInterval:40,

                gridIndex: 0,
                splitNumber: 12,
                

                axisLabel: {
                    show: true,
                    lineStyle: {
                        color: 'colors[0]',
                    },
                    formatter: function(value, index) {
                        //console.log(value, index);
                        // Y轴的自定义刻度值，对应图上的y轴的值
                        
                        var g = ''
                        if (value <= 100) {
                            g = value + '%'
                        }
                            return g;
                    }
                },
                
                
                axisTick: {show: false},  //不显示刻度
                axisPointer: {
                    label: {
                        formatter: function (params) {
                            if (params.value <= 100 ){
                            etaValue = params.value.toFixed(2);
                            }
                        }
                    }
                }
            },


            //2功率轴
            {
                type: 'value',
                name: '功率 (P)',
                //maxInterval:10,
                min: 0,
                max: y21data1max,

                gridIndex: 1,
                splitNumber: 6,
                axisLabel: {
                    formatter: '{value} kW'
                },
                
                
                axisPointer: {
                    label: {
                        formatter: function (params) {
                            hValue = params.value.toFixed(2);
                        }
                    }
                }
            },

            //3电流轴
            {
                type: 'value',
                name: '电流 (I)',
                min: 0,
                max: y21data1max * 3,
                // maxInterval:60,

                gridIndex: 1,
                splitNumber: 6,
                

                axisLabel: {
                    show: true,
                    lineStyle: {
                    color: 'colors[0]',
                    },
                    // formatter: function(value, index) {
                    //   // console.log(value, index);
                    //   // Y轴的自定义刻度值，对应图上的y轴的值
                    
                    //   if (value <= 100) {
                    //     g = value + 'A'
                    //   }else{
                    //     g =  ''
                    //   }
                    //     return g;
                    // }
                },
                
                
                axisTick: {show: false},  //不显示刻度
                axisPointer: {
                    label: {
                    formatter: function (params) {
                        if (params.value <= 100 ){
                        etaValue = params.value.toFixed(2);
                        }
                    }
                    }
                }
            },

            
        
        
        
        ],
        
        
        
        

        series: [
            

            
            {
                name: '流量-扬程',
                type: 'line',
                smooth: true,
                symbolSize: 1,
                symbol: 'circle',
                lineStyle: {
                    width: 3, //线宽
                },
                datasetIndex: 0, //数据0
                xAxisIndex: 0,
                yAxisIndex: 0, //左轴

            },
            
            {
                name: '数据2',
                type: 'scatter',
                datasetIndex: 4,  //数据3
                symbolSize: 15, 
                xAxisIndex: 0,
                yAxisIndex: 0,//右轴
            },
            
            // {
            //     name: '数据1',
            //     type: 'scatter',
            //     symbolSize:2000, //调大触发tooltip
            //     xAxisIndex: 0,
            //     yAxisIndex: 0,
            // },
            
            {
                name: '流量-效率',
                type: 'line',
                smooth: true,
                symbolSize: 1, 
                symbol: 'circle',
                lineStyle: {
                    width: 3,
                },
                datasetIndex: 1, //数据3
                xAxisIndex: 0,
                yAxisIndex: 1, //右轴
            },
            

            {
                name: '数据12',
                type: 'scatter',
                datasetIndex: 5,  //数据3
                symbolSize: 15, 
                xAxisIndex: 0,
                yAxisIndex: 1,//右轴
            },
            


            {
                name: '流量-功率',
                type: 'line',
                smooth: true,
                symbolSize: 1,
                symbol: 'circle',
                lineStyle: {
                    width: 3, //线宽
                },
                datasetIndex: 2, //数据5
                xAxisIndex: 1,
                yAxisIndex: 2, //左轴

            },
            {
                name: '数据23',
                type: 'scatter',
                datasetIndex: 6,  //数据3
                symbolSize: 15, 
                xAxisIndex: 1,
                yAxisIndex: 2,//右轴
            },


            // {
            //     name: '流量-电流',
            //     type: 'line',
            //     smooth: true,
            //     symbolSize: 8, 
            //     symbol: 'circle',
            //     lineStyle: {
            //         width: 3,
            //     },
            //     datasetIndex: 3, //数据7
            //     xAxisIndex: 1,
            //     yAxisIndex: 3, //右轴
            // }

            // {
            //   name: '等效线',
            //   type: 'line',
            //   smooth: true,
            //   symbolSize: 8,
            //   symbol: 'circle',
            //   showSymbol:true,
            //   lineStyle: {
            //     width: 3,
            //   },
            //   datasetIndex: 3, //数据2
            //   yAxisIndex: 0, //左轴

            // },
        ]
    };

    if (option && typeof option === 'object') {
        myChart.setOption(option);
    }

    

    if ( labelQP > -1 ){

        var dataQ = []
        var dataH = []
        var dataETA = []
        var dataP = []

        for(var i=0; i<isData["Q"].length; i++){
            dataQ.push(isData["Q"][i] * freq/50);
            dataH.push(isData["H"][i] * (freq/50)**2);
            dataETA.push(isData["ETA"][i])
            dataP.push(isData["P"][i] * (freq/50)**3);
        }

        var QHSeries0 = []
        var QETASeries0 = []
        var QPSeries0 = []
        var QISeries0 = []


        //  获取数据、拟合参数、生成等间隔点、绘制曲线
        var QHParams0 = LS(dataQ, dataH, 4)
        var QETAParams0 = LS(dataQ, dataETA, 2)
        var QPParams0 = LS(dataQ, dataP, 2)
        // console.log("曲线参数", QHParams, QETAParams, QPParams)


        var isDataLen0 = dataQ[dataQ.length - 1 ] // 最后一个数值
        for ( var itemQ0 = 0; itemQ0 < isDataLen0; itemQ0+=isDataLen0/30 ){
            var QHSum0 = 0
            var QETASum0 = 0
            var QPSum0 = 0
            for (var QHParamsItem0 = 0; QHParamsItem0 < QHParams0.length; QHParamsItem0++ ){
                QHSum0 += QHParams0[QHParamsItem0] * itemQ0**QHParamsItem0
            }
            for (var QETAParamsItem0 = 0; QETAParamsItem0 < QETAParams0.length; QETAParamsItem0++ ){
                QETASum0 += QETAParams0[QETAParamsItem0] * itemQ0**QETAParamsItem0
            }
            for (var QPParamsItem0 = 0; QPParamsItem0 < QPParams0.length; QPParamsItem0++ ){
                QPSum0 += QPParams0[QPParamsItem0] * itemQ0**QPParamsItem0
            }

            QHSeries0.push([itemQ0, QHSum0])
            QETASeries0.push([itemQ0, QETASum0])
            QPSeries0.push([itemQ0, QPSum0])
            QISeries0.push([itemQ0, ])
        }

        // 以功率寻遍历
        var FindQmin = 0
        var FindQmax = dataQ[dataQ.length - 1 ]
        var FindQmedian = ( FindQmin + FindQmax ) / 2



        console.log("已知功率：", labelQP)
        var times = 0;
        while (times < 30){

            var predQP = 0
            for (var QPParamsItem = 0; QPParamsItem < QPParams0.length; QPParamsItem++ ){
                predQP += QPParams0[QPParamsItem] * FindQmedian**QPParamsItem
            }

            if(Math.abs(predQP - labelQP) < 0.0001){



                console.log("求得：" );
                console.log("\t流量" , FindQmedian);

                var predQH = 0
                var predQETA = 0

                for (var QHParamsItem = 0; QHParamsItem < QHParams0.length; QHParamsItem++ ){
                    predQH += QHParams0[QHParamsItem] * FindQmedian**QHParamsItem
                }
                for (var QETAParamsItem = 0; QETAParamsItem < QETAParams0.length; QETAParamsItem++ ){
                    predQETA += QETAParams0[QETAParamsItem] * FindQmedian**QETAParamsItem
                }
                
                console.log("\t扬程" , predQH);
                console.log("\t效率" , predQETA);

                data5.push([FindQmedian, predQH])
                data6.push([FindQmedian, predQETA])
                data7.push([FindQmedian, labelQP])

                myChart.setOption({dataset:[{source: QHSeries0}, {source: QETASeries0}, {source: QPSeries0}, {}, {source: data5}, {source: data6}, {source: data7}]})
                

                break;
            }else if(predQP < labelQP){
                FindQmin = FindQmedian;
                FindQmedian = (FindQmin + FindQmax) /2;
            }else if(predQP > labelQP){
                FindQmax = FindQmedian;
                FindQmedian = (FindQmin + FindQmax) /2;
            }
            times += 1
        }
    }

}

function LS(x, y, m){

    if ( x.length != y.length ){
        alert("输入长度不一致！")

    }else{
        var ATA = []
        var ATy = []

        for ( var m_ = 0; m_ < 2*m+1; m_++ ){
            var sum = 0
            for ( var i = 0; i < x.length; i++ ){
                sum += x[i] ** m_
            }

            ATA.push(sum)
        }

        for ( var m_ = 0; m_ < m+1; m_++ ){
            var sum = 0
            for ( i = 0; i < x.length; i++ ){
                sum += (x[i] ** m_) * y[i]
            }

            ATy.push(sum)
        }

        // console.log("系数：", ATA)
        // console.log("偏置：", ATy)

        var a = new Array(m+1)
        for ( var i = 0; i < a.length; i++ ){
            a[i] = ATA.slice(i, (m+1)+i )
        }

        // console.log("系数矩阵", a)

        var back_x = gaussSeidel(a, ATy, m)

    }
    return back_x

}


function gaussSeidel(a, b, m){

    var x = new Array(m+1)
    var xCopy = new Array(m+1)
    for ( var item = 0; item < x.length; item++ ){
        x[item] = 0
        xCopy[item] = 0
    }

    for ( var k = 0; k < (m-1)*1000; k++ ){
        xCopy = x

        for ( var i = 0; i < m+1; i++ ){
            var sumk1 = 0
            for ( var j = 0; j < m+1; j++ ){
                if ( j == i ){
                    continue
                }
                sumk1 += a[i][j] * x[j]
            }

            x[i] = (1-1.7)*x[i] + 1.7/a[i][i]*(b[i]-sumk1)
        }

        var sum = 0
        for ( var i = 0; i < m+1; i++ ){
            if ( x[i] - xCopy < 1e-8 ){
                sum += 1
            }
        }

        if ( sum == m+1 ){
            // console.log("迭代了： " , k, " 次")
            break
        }
    }

    // console.log("参数：", x)
    var sumfd = 0
    for (var ir=0; ir<x.length;ir++){
        sumfd += 700**(ir) * x[ir]
    }
    // console.log(sumfd)
    return x
}