<template>
  <el-dialog
    v-model="visible"
    :title="device?.name || '设备详情'"
    width="800px"
    :before-close="handleClose"
  >
    <div v-if="device" class="device-detail">
      <!-- 基本信息 -->
      <el-card class="info-card">
        <template #header>
          <span>基本信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="设备名称">{{ device.name }}</el-descriptions-item>
          <el-descriptions-item label="设备类型">{{ device.type }}</el-descriptions-item>
          <el-descriptions-item label="设备ID">{{ device.id }}</el-descriptions-item>
          <el-descriptions-item label="运行状态">
            <el-tag :type="getStatusType(device.status)">
              {{ getStatusText(device.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="健康度">
            <div class="health-display">
              <el-progress 
                :percentage="device.health" 
                :color="getHealthColor(device.health)"
                :stroke-width="12"
              />
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="下次维护">
            <span :class="getMaintenanceClass(device.nextMaintenance)">
              {{ formatMaintenanceDate(device.nextMaintenance) }}
            </span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 实时参数 -->
      <el-card class="params-card">
        <template #header>
          <div class="card-header">
            <span>实时参数</span>
            <el-button size="small" @click="refreshParams">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8" v-for="param in parameterList" :key="param.key">
            <div class="param-card" :class="param.status">
              <div class="param-header">
                <el-icon :size="20" :color="param.color">
                  <component :is="param.icon" />
                </el-icon>
                <span class="param-name">{{ param.name }}</span>
              </div>
              <div class="param-value">
                {{ param.value }} <span class="param-unit">{{ param.unit }}</span>
              </div>
              <div class="param-status">
                <el-tag :type="param.statusType" size="small">
                  {{ param.statusText }}
                </el-tag>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 参数趋势图 -->
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>参数趋势</span>
            <el-radio-group v-model="selectedParam" size="small">
<el-radio-button value="temperature">温度</el-radio-button>
<el-radio-button value="vibration">振动</el-radio-button>
<el-radio-button value="efficiency">效率</el-radio-button>
<el-radio-button value="power">功率</el-radio-button>
</el-radio-group>
          </div>
        </template>
        
        <div ref="chartRef" class="trend-chart" />
      </el-card>

      <!-- 维护记录 -->
      <el-card class="maintenance-card">
        <template #header>
          <span>维护记录</span>
        </template>
        
        <el-timeline>
          <el-timeline-item
            v-for="record in maintenanceRecords"
            :key="record.id"
            :timestamp="record.date"
            :type="record.type"
          >
            <div class="maintenance-record">
              <h4>{{ record.title }}</h4>
              <p>{{ record.description }}</p>
              <div class="record-meta">
                <span>维护人员: {{ record.technician }}</span>
                <span>耗时: {{ record.duration }}</span>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="scheduleMaintenance">
          <el-icon><Tools /></el-icon>
          安排维护
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { Refresh, Tools, DataLine, Lightning, Warning } from '@element-plus/icons-vue'
import type { DeviceStatus } from '@/types'
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'

interface Props {
  modelValue: boolean
  device: DeviceStatus | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const chartRef = ref<HTMLElement>()
const selectedParam = ref('temperature')

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const parameterList = computed(() => {
  if (!props.device) return []
  
  const params = props.device.parameters
  return [
    {
      key: 'temperature',
      name: '温度',
      value: params.temperature.toFixed(1),
      unit: '°C',
      icon: 'Warning',
      color: params.temperature > 80 ? '#F56C6C' : params.temperature > 70 ? '#E6A23C' : '#67C23A',
      status: params.temperature > 80 ? 'danger' : params.temperature > 70 ? 'warning' : 'normal',
      statusType: params.temperature > 80 ? 'danger' : params.temperature > 70 ? 'warning' : 'success',
      statusText: params.temperature > 80 ? '过高' : params.temperature > 70 ? '偏高' : '正常'
    },
    {
      key: 'vibration',
      name: '振动',
      value: params.vibration.toFixed(2),
      unit: 'mm/s',
      icon: 'DataLine',
      color: params.vibration > 2.5 ? '#F56C6C' : params.vibration > 2.0 ? '#E6A23C' : '#67C23A',
      status: params.vibration > 2.5 ? 'danger' : params.vibration > 2.0 ? 'warning' : 'normal',
      statusType: params.vibration > 2.5 ? 'danger' : params.vibration > 2.0 ? 'warning' : 'success',
      statusText: params.vibration > 2.5 ? '异常' : params.vibration > 2.0 ? '偏高' : '正常'
    },
    {
      key: 'efficiency',
      name: '效率',
      value: params.efficiency.toFixed(1),
      unit: '%',
      icon: 'DataLine',
      color: params.efficiency < 70 ? '#F56C6C' : params.efficiency < 80 ? '#E6A23C' : '#67C23A',
      status: params.efficiency < 70 ? 'danger' : params.efficiency < 80 ? 'warning' : 'normal',
      statusType: params.efficiency < 70 ? 'danger' : params.efficiency < 80 ? 'warning' : 'success',
      statusText: params.efficiency < 70 ? '偏低' : params.efficiency < 80 ? '一般' : '良好'
    },
    {
      key: 'power',
      name: '功率',
      value: params.power.toFixed(1),
      unit: 'kW',
      icon: 'Lightning',
      color: '#409EFF',
      status: 'normal',
      statusType: 'primary',
      statusText: '正常'
    },
    {
      key: 'pressure',
      name: '压力',
      value: params.pressure.toFixed(2),
      unit: 'MPa',
      icon: 'DataLine',
      color: '#909399',
      status: 'normal',
      statusType: 'info',
      statusText: '正常'
    },
    {
      key: 'flow',
      name: '流量',
      value: params.flow.toFixed(1),
      unit: 'm³/h',
      icon: 'DataLine',
      color: '#67C23A',
      status: 'normal',
      statusType: 'success',
      statusText: '正常'
    }
  ]
})

// 模拟维护记录
const maintenanceRecords = [
  {
    id: 1,
    date: '2024-01-15 14:30',
    title: '定期保养',
    description: '更换润滑油，检查轴承状态，清洁设备外观',
    technician: '张师傅',
    duration: '2小时',
    type: 'primary'
  },
  {
    id: 2,
    date: '2024-01-08 09:15',
    title: '故障维修',
    description: '更换损坏的密封圈，调整叶轮间隙',
    technician: '李师傅',
    duration: '4小时',
    type: 'warning'
  },
  {
    id: 3,
    date: '2024-01-01 16:45',
    title: '年度大修',
    description: '全面检修设备，更换易损件，性能测试',
    technician: '王师傅',
    duration: '8小时',
    type: 'success'
  }
]

let chartInstance: any = null

// 方法
const getStatusType = (status: DeviceStatus['status']) => {
  const types = { online: 'success', fault: 'danger', maintenance: 'warning', offline: 'info' }
  return types[status] || 'info'
}

const getStatusText = (status: DeviceStatus['status']) => {
  const texts = { online: '在线', fault: '故障', maintenance: '维护中', offline: '离线' }
  return texts[status] || status
}

const getHealthColor = (health: number) => {
  if (health >= 90) return '#67C23A'
  if (health >= 80) return '#409EFF'
  if (health >= 70) return '#E6A23C'
  return '#F56C6C'
}

const getMaintenanceClass = (maintenanceDate: string) => {
  const days = dayjs(maintenanceDate).diff(dayjs(), 'day')
  if (days <= 3) return 'maintenance-urgent'
  if (days <= 7) return 'maintenance-warning'
  return 'maintenance-normal'
}

const formatMaintenanceDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD')
}

const refreshParams = () => {
  ElMessage.success('参数已刷新')
}

const scheduleMaintenance = () => {
  ElMessage.success('维护计划已安排')
}

const handleClose = () => {
  visible.value = false
}

const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance || !props.device) return
  
  // 生成模拟趋势数据
  const data = []
  const now = dayjs()
  
  for (let i = 23; i >= 0; i--) {
    const time = now.subtract(i, 'hour')
    let value = 0
    
    switch (selectedParam.value) {
      case 'temperature':
        value = 45 + Math.random() * 10
        break
      case 'vibration':
        value = 1.2 + Math.random() * 0.8
        break
      case 'efficiency':
        value = 75 + Math.random() * 15
        break
      case 'power':
        value = 80 + Math.random() * 20
        break
    }
    
    data.push([time.format('HH:mm'), value])
  }
  
  const option = {
    title: {
      text: `${selectedParam.value === 'temperature' ? '温度' : 
             selectedParam.value === 'vibration' ? '振动' : 
             selectedParam.value === 'efficiency' ? '效率' : '功率'}趋势`,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item[0])
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: data.map(item => item[1]),
      type: 'line',
      smooth: true,
      areaStyle: {}
    }]
  }
  
  chartInstance.setOption(option)
}

// 监听参数变化
watch(selectedParam, updateChart)

// 监听对话框显示
watch(visible, (newVal) => {
  if (newVal) {
    nextTick(() => {
      initChart()
    })
  }
})
</script>

<style lang="scss" scoped>
.device-detail {
  .info-card, .params-card, .chart-card, .maintenance-card {
    margin-bottom: 20px;
  }
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .health-display {
    width: 200px;
  }
  
  .maintenance-urgent { color: #F56C6C; font-weight: 600; }
  .maintenance-warning { color: #E6A23C; font-weight: 600; }
  .maintenance-normal { color: var(--el-text-color-regular); }
  
  .param-card {
    padding: 16px;
    border-radius: 8px;
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    text-align: center;
    
    .param-header {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      margin-bottom: 12px;
      
      .param-name {
        font-weight: 600;
      }
    }
    
    .param-value {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 8px;
      
      .param-unit {
        font-size: 14px;
        font-weight: normal;
        color: var(--el-text-color-regular);
      }
    }
    
    &.danger { border-left: 4px solid #F56C6C; }
    &.warning { border-left: 4px solid #E6A23C; }
    &.normal { border-left: 4px solid #67C23A; }
  }
  
  .trend-chart {
    height: 300px;
    width: 100%;
  }
  
  .maintenance-record {
    h4 {
      margin: 0 0 8px 0;
      color: var(--el-text-color-primary);
    }
    
    p {
      margin: 0 0 8px 0;
      color: var(--el-text-color-regular);
      line-height: 1.5;
    }
    
    .record-meta {
      display: flex;
      gap: 16px;
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
