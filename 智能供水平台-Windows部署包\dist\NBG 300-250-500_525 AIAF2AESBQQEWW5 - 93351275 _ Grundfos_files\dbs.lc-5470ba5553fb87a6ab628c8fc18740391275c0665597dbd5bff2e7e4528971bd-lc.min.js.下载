var gt=Object.defineProperty,ft=Object.defineProperties;var vt=Object.getOwnPropertyDescriptors;var me=Object.getOwnPropertySymbols;var De=Object.prototype.hasOwnProperty,Le=Object.prototype.propertyIsEnumerable;var Be=(t,e,s)=>e in t?gt(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,A=(t,e)=>{for(var s in e||(e={}))De.call(e,s)&&Be(t,s,e[s]);if(me)for(var s of me(e))Le.call(e,s)&&Be(t,s,e[s]);return t},K=(t,e)=>ft(t,vt(e));var Ne=(t,e)=>{var s={};for(var n in t)De.call(t,n)&&e.indexOf(n)<0&&(s[n]=t[n]);if(t!=null&&me)for(var n of me(t))e.indexOf(n)<0&&Le.call(t,n)&&(s[n]=t[n]);return s};var yt=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports);var J=(t,e,s)=>new Promise((n,r)=>{var c=l=>{try{p(s.next(l))}catch(h){r(h)}},u=l=>{try{p(s.throw(l))}catch(h){r(h)}},p=l=>l.done?n(l.value):Promise.resolve(l.value).then(c,u);p((s=s.apply(t,e)).next())});import{w as L,$ as w,O as _,f,q as a,Y as be,x as v,h as j,i as V,K as x,j as b,X as B,B as M,F as D,G as W,m as Y,n as I,o as Q,t as ie,u as Ct,v as wt,z as Et,y as Tt,g as nt,W as ot,l as ye,A as kt,C as Ot,E as Me,H as Pt,J as It}from"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/vue.9a86ba66.js";import{g as Ie,W as Rt,t as At,I as St,E as Ut,a as rt,A as $t,L as Bt}from"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/grundfos-aem-base.be112f5b.js";import{d as at,c as Dt}from"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/pinia.81760f65.js";import{l as qe,a as Ce}from"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/qs.02e78e1a.js";import{a as Lt}from"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/axios.85a8cbd2.js";import{c as Nt}from"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/cookie.e5f5d17e.js";import{p as Mt}from"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/dompurify.7b42697f.js";import{y as qt}from"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/vue-dompurify-html.87db9e22.js";import{p as Qt,d as Wt}from"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/@formkit.f93bdbfd.js";import"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/lodash.7638abdc.js";import"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/call-bind.b1a21258.js";import"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/get-intrinsic.e97e065b.js";import"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/es-errors.e2207e59.js";import"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/has-symbols.60e0eb4c.js";import"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/has-proto.ca62ef74.js";import"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/function-bind.6d175bed.js";import"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/hasown.02679629.js";import"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/set-function-length.0eec6755.js";import"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/define-data-property.96b3fdb8.js";import"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/es-define-property.1d5ce3c7.js";import"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/gopd.42c4cc56.js";import"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/has-property-descriptors.983f9945.js";import"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/vue-demi.12b564f9.js";import"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/side-channel.d5d21e25.js";import"/etc.clientlibs/aem-selectiontools/clientlibs/dbs/resources/js/chunks/object-inspect.47d75ecf.js";var Wo=yt(ht=>{var H=(t=>(t.productFinder="CmpDbsProductFinder",t.heatingHwr="CmpDbsHeatingHwr",t.waterPressureBoosting="CmpDbsWaterPressureBoosting",t.waterPressureBoostingConditions="CmpDbsWaterPressureBoostingConditions",t.resultsPage="CmpDbsResultsPage",t))(H||{});const Ht="DbsSelectionToolWizard_Area",Ft="DbsSelectionToolWizard_Heating",Gt="DbsSelectionToolWizard_HotWaterRecirculation",lt="DBS_ST_HOT_WATER_RECIRCULATION",zt="DBS_ST_WATER_PRESSURE_BOOSTING",we="DBS_ST_Q_APPLICATION_AREA",Qe="DBS_ST_Q_WATER_SOURCE_INSTALLATION",jt="DbsSelectionToolWizard_PressureBoosting",xt="DbsSelectionToolWizard_PressureBoostingAdditional",it="DBS_ST_HEATING",Kt="elm-button--highlight",ve={heating:"heating",hotWaterRecirculation:"hot water recirculation",waterPressureBoosting:"water pressure boosting"},Ee="CmpDbsHeating",We="CmpDbsHotWaterRecirculation",Jt={class:"cmp-dbs-container__header"},Vt={class:"cmp-dbs-container__main-content"},Yt={class:"cmp-dbs-container__footer"},ce=L({__name:"CmpDbsContainer",setup(t){const e=w();return(s,n)=>(_(),f("div",{ref_key:"container",ref:e,class:"cmp-dbs-container"},[a("div",Jt,[be(s.$slots,"header-content")]),a("div",Vt,[be(s.$slots,"main-content")]),a("div",Yt,[be(s.$slots,"footer-content")])],512))}}),Xt={class:"cmp-dbs-question-template"},Zt=["src"],es={class:"cmp-dbs-question-template__question-content"},ts={class:"cmp-dbs-container__title--small"},ss={key:0,class:"cmp-form-text"},ns=a("input",{id:"text-field",type:"text",class:"cmp-form-text__text",name:"text-field"},null,-1),os=[ns],rs={key:1,class:"cmp-dbs-question-template__button-group"},as=["onClick"],ls={class:"elm-button__text"},is=1,cs=3,us=4,ds="/etc.clientlibs/settings/wcm/design/aembase/head/resources/img/icons/help-circle_outline.svg",ct=L({__name:"CmpDbsQuestionTemplate",props:{icons:{},answers:{},questions:{},disableButtonMinWidth:{type:Boolean}},emits:["update-answers","update-questions"],setup(t,{emit:e}){const s=e,n=t,r=v(()=>{var i;const d={};return(i=n.answers)==null||i.forEach(g=>{d[g.label]=g}),d}),c=d=>d===is||d===us,u=d=>d===cs,p=(d,i)=>{let g;return Array.isArray(n.answers)&&(g=n.answers.find(T=>T.label===d.label&&T.convalue===i.key)),!!g},l=d=>{var g,T,k;const i=(g=d==null?void 0:d.links)==null?void 0:g.find(O=>O.rel==="icon");if(i&&!((T=n.icons)!=null&&T.length))return i.href;if(!i&&((k=n.icons)!=null&&k.length)){const O=n.icons.find(C=>C.label===d.label);return O==null?void 0:O.icon}return ds},h=(d,i)=>{const g=K(A({},r.value),{[d.label]:{label:d.label,convalue:i.key}});s("update-answers",Object.values(g)),d.updateneeded&&s("update-questions",[{label:d.label,convalue:i.key}])};return(d,i)=>(_(),f("div",Xt,[(_(!0),f(j,null,V(d.questions,(g,T)=>{var k;return _(),f("div",{key:T,class:"cmp-dbs-question-template__question"},[(k=n.icons)!=null&&k.length?(_(),f("div",{key:1,class:x([`cmp-dbs-question-template__question-icon--${l(g)}`,"cmp-dbs-question-template__question-icon"])},null,2)):(_(),f("img",{key:0,src:l(g),alt:"question-icon",class:"cmp-dbs-question-template__question-icon"},null,8,Zt)),a("div",es,[a("h4",ts,b(g.text),1),c(g.type)?(_(),f("div",ss,os)):B("",!0),u(g.type)?(_(),f("div",rs,[(_(!0),f(j,null,V(g.options,(O,C)=>(_(),f("button",{key:C,class:x([{"elm-button--min-width-auto":d.disableButtonMinWidth,"elm-button--ghost":!O.selected&&!p(g,O)},"elm-button elm-button--small"]),type:"button",onClick:E=>h(g,O)},[a("span",ls,b(O.value),1)],10,as))),128))])):B("",!0)])])}),128))]))}});var Ze,et;const ps=!!((et=(Ze=window==null?void 0:window.CQ)==null?void 0:Ze.WCM)!=null&&et.isEditMode()),re=Nt.parse(document.cookie),ms="crm-token",_s="login-state",bs="partnerId",He="user-type",F=at("baseStore",{state:()=>({confirmation:null,notification:null,tooltipComponent:null,overlayComponent:null,dbsOverlayComponent:null,isEditMode:ps,user:{isConfirmed:He in re&&re[He]==="2",isCrm:ms in re,isLoggedIn:_s in re,isGrundfosEmployee:!1,isPartner:Object.keys(re).some(t=>t.includes(bs))}}),actions:{setConfirmation(t){this.confirmation=t},setNotification(t){this.notification=t},setIsEditMode(t){this.isEditMode=t},setIsLoggedIn(t){this.user.isLoggedIn=t},setIsGrundfosEmployee(t){this.user.isGrundfosEmployee=t},setTooltipComponent(t){this.tooltipComponent=t},setOverlayComponent(t){this.overlayComponent=t},setDbsOverlayComponent(t){this.dbsOverlayComponent=t}},getters:{isGrundfosEmployee(t){return t.user.isLoggedIn&&t.user.isGrundfosEmployee}}}),Te=t=>t?Mt.sanitize(t):void 0,ke=()=>{},ue=t=>t==null||typeof t=="object"&&Object.keys(t).length===0||typeof t=="string"&&t.trim().length===0,he=(t,e)=>!!(Array.isArray(e)?e:e.match(/([^[.\]])+/g)).reduce((n,r)=>n&&n[r],t),hs=(t,...e)=>(...s)=>t(...e,...s),Re=t=>Object.prototype.toString.call(t)==="[object Object]",gs=t=>e=>Re(e)?t.every(hs(he,e)):!1,fs=(t,e)=>de(t,{params:e}),Fe={loginMessage:"Only logged in users can use this feature.  Click confirm below to proceed to login page.",employeeMessage:"Only employees can use this feature.  Click confirm below to proceed to login page."},vs=()=>{window!=null&&window.GlobalTopBar||console.warn('Global "GlobalTopBar" was not found.'),window==null||window.GlobalTopBar.login()},Ge=(t,e,s=!1)=>{var r;F().setConfirmation({message:t||(s?Fe.employeeMessage:Fe.loginMessage),origin:{target:(r=e==null?void 0:e.target)!=null?r:null},confirmHandler:vs})},ys=()=>{const t=F(),e=v(()=>t.isEditMode);return{loginChecker:h=>J(ht,[h],function*({evt:n,action:r,actionParam:c,label:u,checkEmployee:p,force:l}){if(e.value)t.setIsLoggedIn(!0),t.setIsGrundfosEmployee(!0),r==null||r(c);else if(l)t.setIsLoggedIn(!1),t.setIsGrundfosEmployee(!1),Ge(u,n,p);else{const d=window.grundfos.apiUrls.selectionTools.userSessionDetails,i=yield fs(d,{});t.setIsLoggedIn(i.data.isLoggedIn),t.setIsGrundfosEmployee(i.data.isGrundfosEmployee),(p?!i.data.isGrundfosEmployee:!i.data.isLoggedIn)?(t.setIsLoggedIn(!1),t.setIsGrundfosEmployee(!1),Ge(u,n,p)):r==null||r(c)}})}};function Cs(t,...e){const s={};return e.forEach(n=>{t.hasOwnProperty(n)&&(s[n]=t[n])}),s}var tt,st;const ws=(st=(tt=window.grundfos)==null?void 0:tt.overrideParams)!=null?st:[],Es=t=>Lt.create(K(A({},t.config),{paramsSerializer:{encode:qe.parse,serialize:qe.stringify},withCredentials:!0}));function ze(t={}){var n;const e=Cs(Ie(window.location.search),ws),s=A(A({},(n=t==null?void 0:t.params)!=null?n:{}),e);return K(A({},t),{params:s})}function je(t,...e){const s=t==="post"?e[2]:e[1];return Es(s)[t](...e).then(n=>({data:n==null?void 0:n.data})).catch(n=>J(this,null,function*(){var r,c,u,p,l,h,d;return n.__CANCEL__?Promise.reject({error:{message:(r=n.message)!=null?r:"Request Cancelled."}}):(((c=n==null?void 0:n.response)==null?void 0:c.status)===401&&(yield ys().loginChecker({force:!0})),Promise.reject({error:(d=(p=(u=n==null?void 0:n.response)==null?void 0:u.data)==null?void 0:p.error)!=null?d:{message:(l=n.message)!=null?l:"Unknown Error",statusCode:(h=n==null?void 0:n.response)==null?void 0:h.status}}))}))}function Ts(t={}){var l;const e=F(),p=t,{isCrm:s,warningMessage:n}=p,r=Ne(p,["isCrm","warningMessage"]),c=(l=e==null?void 0:e.user)!=null?l:{};return s&&e.user.isCrm===s||Object.keys(r).every(h=>r[h]===c[h])?Promise.resolve():(n&&e.setNotification({message:n,type:Rt}),Promise.reject({error:{message:n||"Invalid User"}}))}const ut=t=>(e,...s)=>{const n=t==="post",[r,c,u]=s,p=n?c:r,l=n?u||{}:c||{};return Ts(p.user).then(()=>n?je(t,e,s[0],ze(A(A({},p),l))):je(t,e,ze(A(A({},p),l))))},dt=ut("post"),de=ut("get"),ae="data",le="error",ks="loading",pt="message",Os=t=>{var e;return t===null?!0:Re(t)?he(t,ae)||((e=t==null?void 0:t[`${le}`])==null?void 0:e[`${pt}`])||he(t,ks):!1},Ps=t=>{var e;return t===null?!0:Re(t)?!gs([ae,le])(t)&&(he(t,ae)||((e=t==null?void 0:t[`${le}`])==null?void 0:e[`${pt}`])):!1},xe=(t,e,s=!1)=>A(A({loading:s},t&&{data:t}),e&&{error:e}),_e=(t,e=null)=>{var u,p,l,h;if(!Ps(t)||!Os(e))return xe(null,{message:"Invalid state."});const s=(u=t==null?void 0:t[`${ae}`])!=null?u:null,n=(p=t==null?void 0:t[`${le}`])!=null?p:null,r=(l=e==null?void 0:e[`${ae}`])!=null?l:null,c=(h=e==null?void 0:e[`${le}`])!=null?h:null;return xe(s||r,n||(s?null:c),!(s||n))},Is=180,Rs=[1,2,5,10,15,30],As=5,Ke=1e3,Oe={POST:"POST",GET:"GET"};function Ss(t,e,s){const n=e.params||{},r=e.postBody||{};return s===Oe.GET?de(t,{params:n}):dt(t,r,{params:n})}function mt(t,e,s,n,r,c){const u=Ke*(n.totalTimeout||Is),p=n.retryBatchSize||As,l=n.waitTimeIntervals||Rs,h=`${n.baseUrl}.ticketstatus.json`;de(h,{params:{jobid:s}}).then(({data:{stoptime:d}})=>{if(d)t({jobid:s});else if(u<Date.now()-r)e(new Error("Polling timeout."));else{let i=Math.trunc(c/p);i>l.length-1&&(i=l.length-1),setTimeout(()=>{mt(t,e,s,n,r,c+1)},l[i]*Ke)}}).catch(()=>{e(new Error("Unable to get status from job queue"))})}function Us(t,e){return new Promise((s,n)=>{mt(s,n,t,e,Date.now(),0)})}function $s(t,e){const s=`${t.baseUrl}.ticketresults.json`,n=K(A({},t.params),{jobid:e});return de(s,{params:n},t.requestConfig)}function Bs(t,e,s){const n=`${s.baseUrl}.ticketcreate.json`;let r;const c=s.method?s.method:Oe.GET;Oe[c]?Ss(n,s,c).then(({data:{jobid:u}})=>(r=u,Us(u,s))).then(()=>{s.isFileDownload?t(`${s.baseUrl}.ticketresults.${s.resultsUrlExtension}?jobid=${r}`):t($s(s,r))}).catch(()=>{e(new Error("Could not create ticket."))}):e(new Error("Invalid Method."))}function Ds(t){return new Promise((e,s)=>{Bs(e,s,t)})}const Je="DBS_CUSTOM_MESSAGE",te=at("dbs",{state:()=>({config:{qcUrl:"",resultUrl:"",marketoUrl:"",productUrl:"",dbsResultsBaseApiUrl:"",dbsOptionsUrl:""},dbsComponent:"CmpDbsProductFinder",breadcrumb:null,breadcrumbText:null,qcId:null,qc:null,qcList:{},waterPressureBoostingAnswers:[],results:null,isQcComplete:!1,isConsistent:!0,journey:"",installerDetails:{text:"",link:""}}),actions:{postQc(t){return J(this,null,function*(){const{qcId:e,input:s,component:n}=t,r=Ce.stringify({body:JSON.stringify(s||{})},{encode:!0});let c={};return c={qcid:e||""},dt(this.config.qcUrl,r,{params:c}).then(u=>{const p=_e({data:u.data});if(p.data){const l=p.data.id||null;this.updateQcId(l),this.updateQc(p),this.updateQcComplete(p.data.complete),this.setQcConsistent(p.data.consistent),n&&this.updateQcList({payload:p,component:n})}}).catch(u=>{this.updateQc(_e(u,this.qc))})})},setQc(t){const e=t.data.id||null;e&&(this.updateQcId(e),this.updateQc(t))},getResults(t){return J(this,null,function*(){const e=t||this.qcId;let s={};return e&&(s={qcid:e}),this.updateResults(_e(null,this.results)),Ds({baseUrl:this.config.dbsResultsBaseApiUrl,params:s}).then(n=>{this.updateResults(_e(n,this.results))}).catch(()=>{this.updateResults(null)})})},updateResults(t){this.results=t},updateQcId(t){this.qcId=t},updateQc(t){this.qc=t},updateQcList(t){(t.payload.data.id||null)&&(this.qcList[t.component]=t.payload)},updateQcComplete(t){this.isQcComplete=t},setQcConsistent(t){this.isConsistent=t},updateComponent(t){this.dbsComponent=t},updateJourney(t){this.journey=t},updateBreadcrumb(t){this.breadcrumb=t},updateBreadcrumbText(t){this.breadcrumbText=t},updateWaterPressureBoostingAnswers(t){this.waterPressureBoostingAnswers=t},setConfig(t){this.config.qcUrl=t.qcUrl,this.config.resultUrl=t.resultUrl,this.config.marketoUrl=t.marketoUrl,this.config.productUrl=t.productUrl,this.config.dbsResultsBaseApiUrl=t.dbsResultsBaseApiUrl,this.config.dbsOptionsUrl=t.dbsOptionsUrl},backtrackComponent(t){const e=this.componentQc(t);this.updateComponent(t),e&&this.setQc(e)},getInstallerDetails(){de(this.config.dbsOptionsUrl,{params:{}}).then(t=>{this.installerDetails.text=t.data.options.installer.text,this.installerDetails.link=t.data.options.installer.link})}},getters:{groups(t){var e,s;return((s=(e=t.qc)==null?void 0:e.data)==null?void 0:s.groups)||[]},group(){return t=>{var e;return(e=this.groups)==null?void 0:e.find(s=>s.label===t)}},questions(){return t=>{const e=this.group(t);return((e==null?void 0:e.questions)||[]).filter(n=>n.label!==Je)}},question(){return(t,e)=>{const s=this.questions(t);return s!=null&&s.length?s.find(n=>n.label===e):null}},componentQc(t){return e=>t.qcList[e]||null},unfilteredQuestions(){return t=>{const e=this.group(t);return(e==null?void 0:e.questions)||[]}},contactMessage(){return t=>{const e=this.unfilteredQuestions(t);return ue(e)?null:e==null?void 0:e.find(s=>s.label===Je)}},recommended(t){var e,s;return((s=(e=t.results)==null?void 0:e.data)==null?void 0:s.recommended)||{}},alternatives(t){var e,s;return((s=(e=t.results)==null?void 0:e.data)==null?void 0:s.alternatives)||[]}}});function ee(t){const e=t.stepType||"mainPage",s=t.request||"Product finder widget pageview",n={userJourney:{journey:"sizing",subjourney:"productFinderWidget",stepType:e,data:t.data}};At(n,s)}const Ls=a("span",{class:"cmp-dbs-container-breadcrumb__icon"},null,-1),Ns={class:"cmp-dbs-container__title"},Ms={class:"cmp-dbs-container__description"},qs={key:0,class:"cmp-dbs-container__loader"},Qs=["disabled"],Ws={class:"elm-button__text"},Hs={key:1,class:"cmp-dbs-container__loader"},Fs="CmpDbsProductFinder",Gs=L({__name:"CmpDbsHeatingHwr",props:{labels:{}},setup(t){const e=te(),{setNotification:s}=F(),n=w([]),r=w(!1),c=w(!1),u=t,p=v(()=>e.journey===Ee),l=v(()=>p.value?u.labels.heating:u.labels.hotWaterRecirculation),h=v(()=>p.value?e.questions(Ft):e.questions(Gt)),d=v(()=>p.value?it:lt),i=O=>{r.value=!0,e.isConsistent?e.postQc({input:O,qcId:e.qcId}).then(()=>{r.value=!1}):e.postQc({input:[{label:we,convalue:d.value}]}).then(()=>{s({message:u.labels.notifications.consistentMessage,type:St})}).finally(()=>{r.value=!1})},g=O=>{n.value.push(O[0])},T=()=>{e.backtrackComponent(Fs),e.updateQcComplete(!1)},k=()=>{e.isQcComplete&&(c.value=!0,e.getResults(e.qcId).then(()=>{e.updateBreadcrumb(e.dbsComponent),e.updateBreadcrumbText(l.value.header),e.updateComponent(H.resultsPage),ee({stepType:"resultPage",data:{answers:n.value}})}).finally(()=>{c.value=!1}))};return(O,C)=>c.value?(_(),f("div",Hs)):(_(),M(ce,{key:0,class:"cmp-dbs-heating-hwr"},{"header-content":D(()=>[a("button",{type:"button",class:"cmp-dbs-container__breadcrumb",onClick:T},[Ls,W(" "+b(l.value.breadcrumb),1)])]),"main-content":D(()=>[a("h3",Ns,b(l.value.header),1),a("p",Ms,b(l.value.subHeader),1),Y(ct,{questions:h.value,onUpdateAnswers:g,onUpdateQuestions:i},null,8,["questions"]),r.value?(_(),f("div",qs)):B("",!0)]),"footer-content":D(()=>[a("button",{disabled:!I(e).isQcComplete,type:"button",class:"elm-button cmp-dbs-heating-hwr__button-see-results",onClick:Q(k,["prevent"])},[a("span",Ws,b(O.labels.buttons.seeResults),1)],8,Qs)]),_:1}))}}),zs={class:"elm-button__text"},_t=L({__name:"ElmDbsActionButton",props:{buttonText:{default:""},buttonClass:{default:""},url:{default:""},errorMessage:{default:""},handleButtonClick:{type:Function,default:ke},openInNewTab:{type:Boolean,default:!1}},setup(t){const e=t,{setNotification:s}=F(),n=v(()=>ue(e.buttonClass)),r=()=>{var u;e.url?e.openInNewTab?(u=window.open(e.url,"_blank"))==null||u.focus():window.location.href=e.url:s({message:e.errorMessage,type:Ut})},c=()=>{e.handleButtonClick===ke?r():e.handleButtonClick()};return(u,p)=>(_(),f("button",{class:x([n.value?"elm-button":u.buttonClass]),type:"button",onClick:c},[a("span",zs,b(u.buttonText),1)],2))}}),js={class:"cmp-dbs-container__content"},xs={class:"cmp-dbs-product-finder__main-content"},Ks={class:"cmp-dbs-product-finder__main-header cmp-dbs-container__title"},Js={class:"cmp-dbs-product-finder__main-sub-header cmp-dbs-container__description"},Vs={class:"cmp-dbs-container__title--small"},Ys=["onClick"],Xs={class:"cmp-dbs-product-finder_button-text"},Zs=a("span",{class:"cmp-dbs-product-finder_button-icon"},null,-1),en={key:0,class:"cmp-dbs-container__loader"},tn={class:"cmp-dbs-container__disclaimer-wrapper"},sn={class:"cmp-dbs-container__disclaimer-text"},nn={class:"cmp-dbs-container__disclaimer-text"},on=["href"],rn=["href"],an={class:"cmp-dbs-product-finder__footer"},ln={class:"cmp-dbs-product-finder__footer-header cmp-dbs-container__title--small"},cn={class:"cmp-dbs-product-finder__footer-sub-header cmp-dbs-container__description"},un={key:1,class:"cmp-dbs-container__loader"},dn=L({__name:"CmpDbsProductFinder",props:{labels:{},compareProductsUrl:{},sizingUrl:{},productReplacementUrl:{},termsOfUseUrl:{},privacyPolicyUrl:{},highlightJourney:{}},setup(t){const e=te(),s=t,n=w(!1),r=w(!1),c=w({[it]:{componentName:Ee,applicationName:ve.heating},[lt]:{componentName:We,applicationName:ve.hotWaterRecirculation},[zt]:{componentName:H.waterPressureBoosting,applicationName:ve.waterPressureBoosting}}),u=v(()=>{const i=e.question(Ht,we);return((i==null?void 0:i.options)||[]).filter(T=>T.key)}),p=v(()=>Ie(window.location.search).qcId||null);ie(()=>{ee({}),e.getInstallerDetails(),e.componentQc("CmpDbsProductFinder")||(r.value=!0,e.postQc({component:"CmpDbsProductFinder"}).then(()=>{r.value=!1}))});const l=i=>i===s.highlightJourney?Kt:"",h=i=>{const{componentName:g}=c.value[i.key];n.value=!0,e.postQc({qcId:e.qcId,input:[{label:we,convalue:i.key}],component:g}).then(()=>{n.value=!1,g===Ee||g===We?e.updateComponent(H.heatingHwr):e.updateComponent(g),e.updateJourney(g),ee({stepType:"applicationPage",data:{application:c.value[i.key].applicationName}})})},d=i=>ue(p.value)?i:rt(i,{qcid:p.value});return(i,g)=>n.value?(_(),f("div",un)):(_(),M(ce,{key:0,class:"cmp-dbs-product-finder"},{"main-content":D(()=>[a("div",js,[a("div",xs,[a("h3",Ks,b(i.labels.productFinder.header),1),a("p",Js,b(i.labels.productFinder.subHeader),1),a("h4",Vs,b(i.labels.productFinder.contentHeader),1),(_(!0),f(j,null,V(u.value,T=>(_(),f("button",{key:T.key,type:"button",class:x([`cmp-dbs-product-finder__${T.key}`,"elm-button elm-button--ghost elm-button--small",l(T.key)]),onClick:Q(k=>h(T),["prevent"])},[a("h4",Xs,b(T.value),1),Zs],10,Ys))),128)),r.value?(_(),f("div",en)):B("",!0)]),a("div",tn,[a("span",sn,b(i.labels.productFinder.disclaimer),1),a("span",nn,[W(b(i.labels.productFinder.moreInformation)+" ",1),a("a",{href:I(Te)(i.termsOfUseUrl),target:"_blank",rel:"noopener noreferrer"},b(i.labels.productFinder.termsOfUse),9,on),W(" "+b(i.labels.productFinder.orOur)+" ",1),a("a",{href:I(Te)(i.privacyPolicyUrl),target:"_blank",rel:"noopener noreferrer"},b(i.labels.productFinder.privacyPolicy),9,rn),W(" . ")])])])]),"footer-content":D(()=>[a("div",an,[a("h4",ln,b(i.labels.productFinder.footerHeader),1),a("p",cn,b(i.labels.productFinder.footerSubHeader),1),Y(_t,{url:d(s.productReplacementUrl),"button-class":"elm-button elm-button--ghost cmp-dbs-action-button-go-to-product-replacement","button-text":i.labels.buttons.goToProductPlacement,"error-message":i.labels.notifications.error},null,8,["url","button-text","error-message"])])]),_:1}))}}),pn=a("span",{class:"cmp-dbs-container-breadcrumb__icon"},null,-1),mn={class:"cmp-dbs-container__content"},_n={class:"cmp-dbs-water-pressure-boosting__main-content"},bn={class:"cmp-dbs-container__title"},hn={class:"cmp-dbs-container__description"},gn={class:"cmp-dbs-container__title cmp-dbs-container__title--small"},fn={class:"cmp-dbs-water-pressure-boosting__card--container"},vn=["onClick"],yn={class:"cmp-dbs-water-pressure-boosting__card-heading"},Cn={class:"cmp-dbs-container__disclaimer-wrapper"},wn={class:"cmp-dbs-container__disclaimer-text"},En={key:1,class:"cmp-dbs-container__loader"},Tn=L({__name:"CmpDbsWaterPressureBoosting",props:{labels:{}},setup(t){const e=te(),s=w("CmpDbsProductFinder"),n=w(!1),r=t,c=v(()=>e.question(jt,Qe)),u=v(()=>{var d;return(d=c.value)==null?void 0:d.options.filter(i=>!ue(i.key))}),p=v(()=>{var d;return((d=c.value)==null?void 0:d.text)||""}),l=d=>{const i=r.labels.buttons.waterPressureSelection.find(g=>g.id===d.key);return(i==null?void 0:i.icon)||""},h=d=>{n.value=!0,e.updateBreadcrumb(H.waterPressureBoostingConditions),e.updateBreadcrumbText(d.value),e.postQc({qcId:e.qcId,input:[{label:Qe,convalue:d.key}],component:H.waterPressureBoostingConditions}).then(()=>{ee({stepType:"applicationPage",data:{application:"water pressure boosting",DBS_ST_Q_WATER_SOURCE_INSTALLATION:d.key}}),e.updateComponent(H.waterPressureBoostingConditions),n.value=!1}).catch(()=>{n.value=!1})};return(d,i)=>n.value?(_(),f("div",En)):(_(),M(ce,{key:0,class:"cmp-dbs-water-pressure-boosting"},{"header-content":D(()=>[a("button",{type:"button",class:"cmp-dbs-container__breadcrumb",onClick:i[0]||(i[0]=Q(g=>I(e).backtrackComponent(s.value),["prevent"]))},[pn,W(" "+b(d.labels.waterPressureBoosting.breadcrumb),1)])]),"main-content":D(()=>[a("div",mn,[a("div",_n,[a("h3",bn,b(r.labels.waterPressureBoosting.header),1),a("p",hn,b(r.labels.waterPressureBoosting.subHeader),1),a("h4",gn,b(p.value),1),a("div",fn,[(_(!0),f(j,null,V(u.value,g=>(_(),f("button",{key:g.key,type:"button",class:"cmp-dbs-water-pressure-boosting__card",onClick:Q(T=>h(g),["prevent"])},[a("div",{class:x([`cmp-dbs-water-pressure-boosting__card-icon--${l(g)}`,"cmp-dbs-water-pressure-boosting__card-icon"])},null,2),a("div",yn,b(g.value),1)],8,vn))),128))])]),a("div",Cn,[a("span",wn,b(r.labels.waterPressureBoosting.disclaimer),1)])])]),_:1}))}}),kn={class:"cmp-dbs-share-results__wrapper"},On={class:"cmp-dbs-share-results__container"},Pn={class:"cmp-dbs-share-results__title"},In={class:"cmp-dbs-share-results__description"},Rn={class:"cmp-dbs-share-results__email-wrapper"},An=["placeholder"],Sn={class:"cmp-dbs-share-results__email-button-container"},Un={key:0,class:"cmp-dbs-share-results__email-icon--loading"},$n={key:1,class:"cmp-dbs-share-results__send-button"},Bn={class:"cmp-dbs-container__disclaimer-text"},Dn=["href"],Ln="loading",Nn=L({__name:"CmpDbsShareResults",props:{labels:{},products:{},status:{},privacyPolicyUrl:{},pumpSystemId:{}},emits:["share-all-result-close","share-result-close"],setup(t){const e=w(""),s=w(Ln);return(n,r)=>(_(),f("div",kn,[a("button",{type:"button",class:"elm-button cmp-dbs-share-results__button cmp-dbs-share-results__close-button",onClick:r[0]||(r[0]=c=>n.$emit("share-all-result-close"))},b(n.labels.buttons.close),1),a("div",On,[a("h3",Pn,b(n.labels.shareResults.header),1),a("p",In,b(n.labels.shareResults.subHeader),1),a("div",Rn,[Ct(a("input",{"onUpdate:modelValue":r[1]||(r[1]=c=>e.value=c),placeholder:n.labels.shareResults.enterEmail,class:"cmp-form-text__text",type:"text"},null,8,An),[[wt,e.value]]),a("div",Sn,[n.status===s.value?(_(),f("span",Un)):(_(),f("span",$n,b(n.labels.shareResults.send),1))])]),a("span",Bn,[W(b(n.labels.shareResults.shareProductDisclaimer)+" ",1),a("a",{href:I(Te)(n.privacyPolicyUrl),target:"_blank",rel:"noopener noreferrer"},b(n.labels.shareResults.here),9,Dn),W(" . ")])])]))}}),Mn=()=>{const t=()=>({height:document.documentElement.clientHeight||window.innerHeight,width:document.documentElement.clientWidth||window.innerWidth}),e=w(t()),s=()=>{e.value=t()},n=()=>{e.value=A({},e.value)};return ie(()=>window.addEventListener("resize",s)),Et(()=>window.removeEventListener("resize",s)),{viewportSize:e,resetViewportSize:n}};var Pe=(t=>(t.COVER="cover",t.CONTAIN="contain",t))(Pe||{});const qn=["alt","src"],Ve=L({__name:"ElmImg",props:{alt:{},fillMode:{default:null},imageLoadErrorHandler:{type:Function,default:()=>{}},src:{},srcset:{default:null},srcFormat:{default:null},useDpr:{type:Boolean,default:!0},x:{default:.5},y:{default:.5}},setup(t){const e=w(null),s=w(null),n=w(null),r=w(null),c=w(!1),u=w(null),p=w(null),l=t,h=F(),{viewportSize:d}=Mn(),i=Tt(),g=v(()=>h.isEditMode),T=v(()=>!!g.value&&!!l.fillMode),k=v(()=>!!i.default),O=v(()=>k.value?"":l.src!==null&&typeof l.src=="object"?R(l.src).src:l.src),C=v(()=>k.value?[]:Array.isArray(l.srcset)?l.srcset.map(R).sort((o,m)=>Number(o.width)-Number(m.width)):typeof l.srcset=="string"?l.srcset.split(",").map(o=>{const[m,y]=o.trim().split(" ");return{src:m,width:parseInt(y.replace(/\D+/g,""),10)}}).sort((o,m)=>o.width-m.width):[]),E=v(()=>{var o;return k.value?(o=n.value)==null?void 0:o.querySelector("img"):s.value}),R=o=>{const{src:m,width:y,height:P}=o;if(!l.srcFormat||!m)return o;const S=m.split("?")[0],U=Ce.parse(l.srcFormat.replace(" ","").replace("?","").replace("{width}",y.toString()).replace("{height}",P.toString()));return{src:`${S}?${Ce.stringify(A(A({},Ie(m)),U),{encode:!1})}`,width:y}},N=(o,m,y)=>{const P=(o-m)/o/2,S=.5-P,U=.5+P;return S>y?S:U<y?U:y},G=(o,m)=>{const y="translate(-50%, -50%)",P=o>m?"100%":"auto",S=o>m?"auto":"100%";return{height:P,transform:y,width:S}},se=(o,m,y,P)=>{let S,U,$;if(o>m){S="auto",U="100%";const q=o/m*y;$=`translate(-50%, ${N(q,y,l.y)*-100}%)`}else{S="100%",U="auto";const q=m/o*P;$=`translate(${N(q,P,l.x)*-100}%, -50%)`}return{height:S,transform:$,width:U}},z=()=>{var q,pe,Se,Ue,$e;if(((q=e.value)==null?void 0:q.offsetParent)===null||!E.value)return;if(!T.value){p.value={opacity:1};return}const{naturalWidth:o,naturalHeight:m}=E.value,y=(Se=(pe=e.value)==null?void 0:pe.getBoundingClientRect().width)!=null?Se:0,P=($e=(Ue=e.value)==null?void 0:Ue.getBoundingClientRect().height)!=null?$e:0,S=y/P,U=o/m;let $;switch(l.fillMode){case Pe.CONTAIN:$=G(S,U);break;case Pe.COVER:$=se(S,U,P,y);break;default:$={};break}p.value=A({opacity:1},$)},X=()=>{var S,U;if(((S=e.value)==null?void 0:S.offsetParent)===null||!E.value)return;const o=l.useDpr?window.devicePixelRatio:1,m=(U=e.value)==null?void 0:U.getBoundingClientRect().width,y=m?m*o:0,P=C.value.reduce(($,q,pe)=>$.width?$:q.width>=y||pe===C.value.length-1&&y>q.width?q:$,{src:O.value,width:0}).src;P!==u.value?(p.value={opacity:0},u.value=P):z()},ne=o=>{var m;u.value=(m=o==null?void 0:o.target.src)!=null?m:null,c.value=!1},Z=()=>{var o;c.value=!1,(o=l.imageLoadErrorHandler)==null||o.call(l)},oe=o=>{const m=o.tagName==="IMG"?o:o.querySelector("img");m&&(c.value=!0,m.addEventListener("load",ne),m.complete&&z(),m.addEventListener("error",Z))},fe=o=>{const m=o.tagName==="IMG"?o:o.querySelector("img");m&&(m.removeEventListener("load",ne),m.removeEventListener("error",Z))};return ie(()=>J(this,null,function*(){var o;E.value&&oe(E.value),k.value?(r.value=new MutationObserver(m=>{m.forEach(y=>{Array.from(y.addedNodes).forEach(oe),Array.from(y.removedNodes).forEach(fe)})}),n.value&&((o=r.value)==null||o.observe(n.value,{subtree:!0,childList:!0,characterData:!0}))):yield nt().then(()=>{X()})})),ot(()=>{r.value&&r.value.disconnect()}),ye(()=>[l.src,l.srcset],()=>{k.value||X()}),ye(()=>d,()=>{k.value?z():X()}),(o,m)=>{var y;return _(),f("div",{class:x([T.value&&I($t),c.value&&I(Bt)])},[a("img",{ref_key:"img",ref:s,alt:o.alt,src:(y=u.value)!=null?y:"",style:kt(p.value),class:"elm-img__asset",loading:"lazy",onLoad:z},null,44,qn),k.value?(_(),f("div",{key:0,ref_key:"hidden",ref:n,class:"elm-img__hidden"},[be(o.$slots,"default")],512)):B("",!0)],2)}}}),Qn=a("span",{class:"cmp-dbs-container-breadcrumb__icon"},null,-1),Wn={class:"cmp-dbs-container__title"},Hn={class:"cmp-dbs-results-page-card__wrapper"},Fn={class:"cmp-product-card__container"},Gn={class:"cmp-product-card__info-wrapper"},zn={class:"cmp-dbs-results-page-card__description"},jn={class:"cmp-dbs-results-page-card__title"},xn={class:"cmp-dbs-results-page-card__sub-title"},Kn={class:"cmp-dbs-results-page-card__description-list"},Jn={key:0,class:"cmp-dbs-container__title"},Vn={class:"cmp-dbs-results-page__cards"},Yn=["onClick"],Xn={class:"cmp-product-card__container"},Zn={class:"cmp-product-card__info-wrapper"},eo={class:"cmp-dbs-results-page-card__description"},to={class:"cmp-dbs-results-page-card__title"},so={class:"cmp-dbs-results-page-card__sub-title"},no={class:"cmp-dbs-results-page-card__description-list"},oo={class:"elm-button__text"},ro=a("div",{class:"cmp-dbs-results-page-footer__divider"},null,-1),ao={class:"elm-button__text"},lo="Recommended",io="Alternative",co="LINK",uo="LABEL",po=L({__name:"CmpDbsResultsPage",props:{labels:{},compareProductsUrl:{},sizingUrl:{},privacyPolicyUrl:{}},setup(t){const e=te(),s=t,n=w({features:[],links:[],name:"",productnumber:"",pumpsystemid:"",rank:"",isShareResult:!1,status:null}),r=w([]),c=w(!1),u=w(""),p=v(()=>[n.value,...r.value]),l=v(()=>{const{link:C,text:E}=e.installerDetails;return C&&E&&C!==co&&E!==uo}),h=()=>{e.breadcrumb===H.waterPressureBoostingConditions?e.backtrackComponent(e.breadcrumb):e.updateComponent(e.breadcrumb)},d=()=>{n.value=K(A({},e.recommended),{rank:lo,isShareResult:!1,status:null}),Object.values(e.alternatives).forEach(C=>{r.value.push(K(A({},C),{rank:io,isShareResult:!1,status:null}))})},i=C=>{var R,N;const E=(R=C.links)==null?void 0:R.find(G=>G.rel==="productimage");return(N=E==null?void 0:E.href)!=null?N:""},g=()=>{c.value=!c.value,c.value&&ee({stepType:"resultPage",request:"Product finder widget share"})},T=()=>{window.open(s.compareProductsUrl,"_blank")},k=C=>{window.open(C,"_blank")},O=(C,E)=>ue(E)?C:rt(C,{pumpsystemid:E});return ie(()=>{d()}),(C,E)=>(_(),M(ce,{class:"cmp-dbs-results-page"},{"header-content":D(()=>[I(e).breadcrumb?(_(),f("button",{key:0,type:"button",class:"cmp-dbs-container__breadcrumb",onClick:Q(h,["prevent"])},[Qn,W(" "+b(I(e).breadcrumbText),1)])):B("",!0)]),"main-content":D(()=>[a("h3",Wn,b(s.labels.resultsPage.header),1),a("div",Hn,[a("div",{class:"cmp-product-card__wrapper",onClick:E[0]||(E[0]=R=>k(O(I(e).config.productUrl,n.value.pumpsystemid)))},[a("div",Fn,[a("div",Gn,[Y(Ve,{src:{src:i(n.value),width:200,height:300},alt:n.value.name,class:"elm-img elm-img--4-3 cmp-dbs-results-page__product-image","src-format":"w={width}&h={height}"},null,8,["src","alt"]),a("div",zn,[a("h4",jn,b(n.value.name),1),a("h5",xn,b(n.value.productnumber),1),a("ul",Kn,[(_(!0),f(j,null,V(n.value.features,(R,N)=>(_(),f("li",{key:N},b(R),1))),128))])])])])])]),r.value.length?(_(),f("h3",Jn,b(s.labels.resultsPage.alternatives),1)):B("",!0),a("div",Vn,[(_(!0),f(j,null,V(r.value,(R,N)=>(_(),f("div",{key:N,class:"cmp-dbs-results-page-card__wrapper cmp-dbs-results-page-card__alternatives"},[a("div",{class:"cmp-product-card__wrapper",onClick:G=>k(O(I(e).config.productUrl,R.pumpsystemid))},[a("div",Xn,[a("div",Zn,[Y(Ve,{src:{src:i(R),width:200,height:300},alt:R.name,class:"elm-img elm-img--4-3 cmp-dbs-results-page__product-image","src-format":"w={width}&h={height}"},null,8,["src","alt"]),a("div",eo,[a("h4",to,b(R.name),1),a("h5",so,b(R.productnumber),1),a("ul",no,[(_(!0),f(j,null,V(R.features,(G,se)=>(_(),f("li",{key:se},b(G),1))),128))])])])])],8,Yn)]))),128))])]),"footer-content":D(()=>[l.value?(_(),f("button",{key:0,type:"button",class:"elm-button elm-button--small",onClick:E[1]||(E[1]=R=>k(I(e).installerDetails.link))},[a("span",oo,b(I(e).installerDetails.text),1)])):B("",!0),Y(_t,{"button-class":"elm-button cmp-dbs-action-button-go-to-detailed-comparison","button-text":C.labels.buttons.goToDetailedComparison,"error-message":C.labels.notifications.error,"handle-button-click":T},null,8,["button-text","error-message"]),ro,c.value?(_(),M(Nn,{key:1,labels:C.labels,products:p.value,status:u.value,"share-all-results":!0,"privacy-policy-url":C.privacyPolicyUrl,onShareAllResultClose:E[2]||(E[2]=R=>g())},null,8,["labels","products","status","privacy-policy-url"])):B("",!0),c.value?B("",!0):(_(),f("button",{key:2,type:"button",class:"elm-button elm-button--small cmp-dbs-results-page__share-button cmp-dbs-results-page__share-all-button",onClick:E[3]||(E[3]=R=>g())},[a("span",ao,b(C.labels.buttons.shareResults),1)]))]),_:1}))}}),mo=[{label:"DBS_ST_Q_SURFACE_WATER_DEPTH",icon:"waves_outline"},{label:"DBS_ST_Q_FLOOR_COUNT",icon:"house-building_outline"},{label:"DBS_ST_Q_BATHROOM_COUNT",icon:"toilet_outline"},{label:"DBS_ST_Q_KITCHEN_COUNT",icon:"dish-washer_outline"},{label:"DBS_ST_Q_EXTRA_PRESSURE_IRRIGATION",icon:"sprinkler_outline"},{label:"DBS_ST_Q_EXTRA_PRESSURE_RAINSHOWER",icon:"pressure-high_outline"},{label:"DBS_ST_Q_ADDITIONAL_PUMP_CONTROL",icon:"info-circle_outline"}],_o=a("span",{class:"cmp-dbs-container-breadcrumb__icon"},null,-1),bo={class:"cmp-dbs-container__title"},ho=["disabled"],go={class:"elm-button__text"},fo={key:1,class:"cmp-dbs-container__loader"},vo="CmpDbsWaterPressureBoosting",yo=L({__name:"CmpDbsWaterPressureBoostingConditions",props:{labels:{}},setup(t){const e=te(),s=w(!1),n=w(mo),r=v(()=>e.questions(xt)),c=v(()=>r.value.length!==e.waterPressureBoostingAnswers.length),u=()=>{e.updateWaterPressureBoostingAnswers([]),e.backtrackComponent(vo)},p=()=>{s.value=!0,e.postQc({qcId:e.qcId,input:e.waterPressureBoostingAnswers,component:"CmpDbsResultsPage"}).then(()=>{e.getResults(e.qcId).then(()=>{s.value=!1,e.updateComponent("CmpDbsResultsPage")});const l=e.waterPressureBoostingAnswers.map(h=>({label:h.label.replace("DBS_ST_Q_",""),convalue:h.convalue.replace("DBS_ST_","")}));ee({stepType:"resultPage",data:{answers:l}})})};return(l,h)=>s.value?(_(),f("div",fo)):(_(),M(ce,{key:0,class:"cmp-dbs-water-pressure-boosting-conditions"},{"header-content":D(()=>[a("button",{type:"button",class:"cmp-dbs-container__breadcrumb",onClick:h[0]||(h[0]=Q(d=>u(),["prevent"]))},[_o,W(" "+b(l.labels.waterPressureBoostingConditions.breadcrumb),1)])]),"main-content":D(()=>[a("h3",bo,b(I(e).breadcrumbText),1),Y(ct,{questions:r.value,answers:I(e).waterPressureBoostingAnswers,icons:n.value,onUpdateAnswers:I(e).updateWaterPressureBoostingAnswers},null,8,["questions","answers","icons","onUpdateAnswers"])]),"footer-content":D(()=>[a("button",{disabled:c.value,type:"button",class:"cmp-dbs-water-pressure-boosting-conditions__button-see-results elm-button",onClick:h[1]||(h[1]=Q(d=>p(),["prevent"]))},[a("span",go,b(l.labels.buttons.seeResults),1)],8,ho)]),_:1}))}}),bt=L({__name:"ModDbs",props:{labels:{},config:{},compareProductsUrl:{},sizingUrl:{},productReplacementUrl:{},termsOfUseUrl:{},privacyPolicyUrl:{},highlightJourney:{}},setup(t){const e=te(),s=t;Ot(()=>{e.setConfig(s.config)});const n=v(()=>H);return(r,c)=>(_(),f(j,null,[I(e).dbsComponent===n.value.productFinder?(_(),M(dn,{key:0,labels:s.labels,"compare-products-url":s.compareProductsUrl,"sizing-url":s.sizingUrl,"product-replacement-url":s.productReplacementUrl,"terms-of-use-url":s.termsOfUseUrl,"privacy-policy-url":s.privacyPolicyUrl,"highlight-journey":s.highlightJourney},null,8,["labels","compare-products-url","sizing-url","product-replacement-url","terms-of-use-url","privacy-policy-url","highlight-journey"])):I(e).dbsComponent===n.value.heatingHwr?(_(),M(Gs,{key:1,labels:s.labels},null,8,["labels"])):I(e).dbsComponent===n.value.waterPressureBoosting?(_(),M(Tn,{key:2,labels:s.labels},null,8,["labels"])):I(e).dbsComponent===n.value.waterPressureBoostingConditions?(_(),M(yo,{key:3,labels:s.labels},null,8,["labels"])):B("",!0),I(e).dbsComponent===n.value.resultsPage?(_(),M(po,{key:4,labels:s.labels,"compare-products-url":s.compareProductsUrl,"sizing-url":s.sizingUrl,"privacy-policy-url":s.privacyPolicyUrl},null,8,["labels","compare-products-url","sizing-url","privacy-policy-url"])):B("",!0)],64))}}),Co=L({__name:"ModDbsButton",props:{buttonClass:{},buttonLabel:{},compareProductsUrl:{},sizingUrl:{},productReplacementUrl:{},termsOfUseUrl:{},privacyPolicyUrl:{},highlightJourney:{}},setup(t){const e=t,s=F(),n=()=>{s.setDbsOverlayComponent({props:{compareProductsUrl:e.compareProductsUrl,sizingUrl:e.sizingUrl,productReplacementUrl:e.productReplacementUrl,termsOfUseUrl:e.termsOfUseUrl,privacyPolicyUrl:e.privacyPolicyUrl,highlightJourney:e.highlightJourney},useSlideIn:!0,contentClassName:"cmp-dbs-container__overlay"})};return(r,c)=>(_(),f("div",null,[a("button",{id:"dbs-menu",class:x(["elm-button",r.buttonClass]),type:"button",onClick:c[0]||(c[0]=()=>{n()})},b(r.buttonLabel),3)]))}}),wo=()=>({isMobile:v(()=>window.innerWidth<768||/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent))}),Eo={class:"elm-square-button__text"},To={key:0,class:"cmp-overlay__heading"},ko={class:"elm-square-button__text"},Oo="desktop",Po="mobile",Io=-320,Ro=L({__name:"CmpDbsOverlay",props:{labels:{},config:{}},setup(t){const e=F(),{isMobile:s}=wo(),n=w(),r=w(null),c=w(0),u=w(!1),p=w(!1),l=w(null),h=v(()=>e.dbsOverlayComponent),d=v(()=>{var o;return(o=e.dbsOverlayComponent)==null?void 0:o.props}),i=v(()=>{var o;return((o=e.dbsOverlayComponent)==null?void 0:o.autoFocus)||!0}),g=v(()=>{var o;return((o=e.dbsOverlayComponent)==null?void 0:o.isFullScreen)||!1}),T=v(()=>{var o;return((o=e.dbsOverlayComponent)==null?void 0:o.isTransparent)||!1}),k=v(()=>{var o;return((o=e.dbsOverlayComponent)==null?void 0:o.useSlideIn)||!1}),O=v(()=>{var o;return((o=e.dbsOverlayComponent)==null?void 0:o.preventCloseOnOutsideClick)||!0}),C=v(()=>{var o;return(o=e.dbsOverlayComponent)==null?void 0:o.heading}),E=v(()=>{var o;return((o=e.dbsOverlayComponent)==null?void 0:o.contentClassName)||""}),R=v(()=>{var o;return((o=e.dbsOverlayComponent)==null?void 0:o.noContentAttrs)||!1}),N=v(()=>{var m;const o=(m=e.dbsOverlayComponent)==null?void 0:m.showToggle;return o===Oo?!s.value:o===Po?s.value:o||!0}),G=()=>{var o;return(o=n.value)==null?void 0:o.offsetHeight},se=()=>{let o=1;return p.value?o=u.value?1:0:u.value||(o=0),{style:{display:p.value||u.value?"block":"none",opacity:o}}},z=(o=null)=>{var m,y;o&&o.target!==n.value||(p.value=!1,u.value||((y=(m=e.dbsOverlayComponent)==null?void 0:m.closeHandler)==null||y.call(m),e.setDbsOverlayComponent(null)))},X=()=>{u.value&&r instanceof HTMLButtonElement?r.focus():!u.value&&l.value instanceof HTMLElement&&(l.value.focus(),l.value=null)},ne=o=>p.value?(u.value=o,z(),Promise.resolve()):(p.value=!0,nt().then(()=>{u.value=o,G()})),Z=()=>{u.value&&ne(!1).then(X)},oe=()=>{O.value||Z()},fe=()=>{var y,P,S,U;if(R.value)return"";if(k.value){let $;return u.value?$=0:s.value?$="-100%":$=`${Io}px`,{style:{right:$,top:0}}}const o=!u.value&&((P=(y=h.value)==null?void 0:y.origin)==null?void 0:P.x),m=!u.value&&((U=(S=h.value)==null?void 0:S.origin)==null?void 0:U.y);return{style:{left:o?`${o}px`:"50%",top:m?`${m}px`:"50%",transform:`translate(-50%, -50%) scale(${u.value?"1":"0"})`}}};return ye(h,()=>{var o,m;h.value&&(l.value=(m=(o=h.value.origin)==null?void 0:o.target)!=null?m:document.querySelector("[data-site-wrapper]"),ne(!0).then(i.value?X:ke))},{immediate:!0}),ie(()=>{var o;(o=n.value)==null||o.addEventListener("transitionend",z,!1)}),ot(()=>{var o;(o=n.value)==null||o.removeEventListener("transitionend",z,!1)}),(o,m)=>{var y,P;return _(),f("div",Me({ref_key:"container",ref:n,key:c.value,class:{"cmp-overlay--full-screen":g.value,"cmp-overlay--transparent":T.value,"cmp-overlay--slide-in":k.value}},se(),{"aria-modal":"true",role:"dialog",onClick:oe,onKeydown:Pt(oe,["esc"])}),[!C.value&&N.value?(_(),f("button",{key:0,ref:"toggle",class:"elm-square-button elm-square-button--medium elm-square-button--icon-close_outline cmp-overlay__toggle",type:"button",onClick:Q(Z,["stop"])},[a("span",Eo,b((y=o.labels)==null?void 0:y.close),1)],512)):B("",!0),a("section",Me(fe(),{class:[E.value,"cmp-overlay__content"],"aria-live":"polite",onClick:m[0]||(m[0]=Q(()=>{},["stop"]))}),[C.value?(_(),f("header",{key:0,class:x(["cmp-overlay__header",!C.value&&"cmp-overlay__header--subtle"])},[C.value?(_(),f("h2",To,b(C.value),1)):B("",!0),N.value?(_(),f("button",{key:1,ref:"toggle",class:"elm-square-button elm-square-button--medium elm-square-button--icon-close_outline",type:"button",onClick:Q(Z,["stop"])},[a("span",ko,b((P=o.labels)==null?void 0:P.close),1)],512)):B("",!0)],2)):B("",!0),Y(bt,{labels:o.labels,config:o.config,"compare-products-url":d.value.compareProductsUrl,"sizing-url":d.value.sizingUrl,"product-replacement-url":d.value.productReplacementUrl,"terms-of-use-url":d.value.termsOfUseUrl,"privacy-policy-url":d.value.privacyPolicyUrl,"highlight-journey":d.value.highlightJourney,class:"cmp-overlay__body"},null,8,["labels","config","compare-products-url","sizing-url","product-replacement-url","terms-of-use-url","privacy-policy-url","highlight-journey"])],16)],16)}}}),Ao={tooltipAttr:"data-v-tooltip",addClass(t,e=""){t.classList.add(`${this.tooltipAttr}${e}`)},updateTooltip(t,{value:e,modifiers:s}){if(!e)return null;if(typeof e=="string")t.setAttribute(this.tooltipAttr,e),s.arrow&&this.addClass(t,"__arrow");else{const{text:n,displayArrow:r,position:c}=e;n&&t.setAttribute(this.tooltipAttr,n),(r||s.arrow)&&this.addClass(t,"__arrow"),c&&typeof c=="string"&&this.addClass(t,`--position-${c}`)}},mounted(t,{value:e,dir:s,modifiers:n}){(typeof e=="object"&&(e!=null&&e.text)||typeof e=="string")&&s.addClass(t),s.updateTooltip(t,{value:e,modifiers:n})},updated(t,{value:e,dir:s,modifiers:n}){s.updateTooltip(t,{value:e,modifiers:n})}},So={beforeMount(t,e){t.clickOutsideEvent=s=>J(this,null,function*(){t===s.target||t.contains(s.target)||e.value(s,t)}),document.body.addEventListener("click",t.clickOutsideEvent)},unmounted(t){document.body.removeEventListener("click",t.clickOutsideEvent)}},Uo=Dt(),$o=t=>It(A({},t)).use(Qt,Wt).use(Uo).use(qt).directive("tooltip",Ao).directive("clickOutside",So);let ge="data-selectiontools-component-root",Ae=`[${ge}]`;const Bo=t=>{ge=t,Ae=`[${ge}]`},Ye=document.querySelector("body"),Xe=new Map;function Do(t){return!!t.parentElement&&!t.parentElement.closest(Ae)}function Lo(t,e,s){const n=`${ge}-${s}`;if(Xe.has(n))return;const r=$o(t).mount(e);Xe.set(n,r)}const No=t=>{const{rootComponents:e,rootNode:s}=t;Bo(s),window.addEventListener("load",()=>{var n,r,c,u;if(Ye){Array.from(Ye.querySelectorAll(Ae)).filter(Do).forEach((l,h)=>Lo(e,l,h));const p=()=>{var h,d;const l=!!((d=(h=window==null?void 0:window.CQ)==null?void 0:h.WCM)!=null&&d.isEditMode(!0));document.body.classList.toggle("b-edit-mode",l),F().setIsEditMode(l)};(r=(n=window==null?void 0:window.CQ)==null?void 0:n.WCM)!=null&&r.isEditMode&&((c=window==null?void 0:window.parent)!=null&&c.$)&&(p(),window==null||window.parent.$((u=window==null?void 0:window.parent)==null?void 0:u.document).on("editor-frame-mode-changed",p))}})},Mo="data-dbs-vue3-component-root",qo={components:{ModDbs:bt,ModDbsButton:Co,CmpDbsOverlay:Ro}},Qo={rootComponents:qo,rootNode:Mo};No(Qo)});export default Wo();
