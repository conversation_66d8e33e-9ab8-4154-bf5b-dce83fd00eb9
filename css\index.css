body {
    font-family: "Lato", sans-serif;
    height: 100%;
}

.sidenav {
    height: 100%;
    width: 200px;
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    background-color: #00FF7F;
    overflow-x: hidden;
    padding-top: 20px;
}

.sidenav a {
    padding: 5% 5%;
    text-decoration: none;
    display: block;
}



.sidenav span {
    padding: 16px 16px 16px 16px;
    text-decoration: none;
    font-size: 25px;
    color: black;
    text-align: center;
    border-radius: 10px;
    background-color:cornflowerblue;
    display: block;
}

.sidenav span:hover {

    background-color:#818181;
}





#main {
    /* margin-left: 20%; 与sidenav的宽度相同 */
    font-size: 28px; /* 字体放大，让页面可滚动 */
    /* padding: 0px 10px; */
    width: 85%;
}

@media screen and (max-height: 450px) {
    .sidenav {padding-top: 15px;}
    .sidenav a {font-size: 18px;}
}