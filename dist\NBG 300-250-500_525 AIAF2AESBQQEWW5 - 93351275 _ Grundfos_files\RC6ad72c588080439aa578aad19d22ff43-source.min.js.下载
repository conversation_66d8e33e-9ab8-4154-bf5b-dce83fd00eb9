// For license information, see `https://assets.adobedtm.com/5e107551ef5d/89d0c5ed902a/4021f82e766f/RC6ad72c588080439aa578aad19d22ff43-source.js`.
_satellite.__registerScript('https://assets.adobedtm.com/5e107551ef5d/89d0c5ed902a/4021f82e766f/RC6ad72c588080439aa578aad19d22ff43-source.min.js', "try{if(\"product\"==dataLayer.userJourney.stepType&&void 0!==dataLayer.product&&void 0!==dataLayer.product.product&&void 0!==dataLayer.product.range&&\"undefined\"!=dataLayer.companyCode&&\"undefined\"!=dataLayer.pageTags.applications){var imgUrl=document.querySelector(\".catalogue-hero img\").src,descriptionInnerText=document.querySelector(\".cmp-catalogue-hero__description\").innerText,productName=document.querySelector(\"h1.cmp-catalogue-hero__heading\").innerHTML;adobe.target.getOffer({mbox:\"data-collection-mbox\",params:{\"entity.id\":dataLayer.companyCode+\"_\"+dataLayer.product.product,\"entity.collectionType\":\"productApplications\",\"entity.salesCompany\":dataLayer.companyCode,\"entity.name\":productName,\"entity.categoryId\":dataLayer.pageTags.applications,\"entity.productRange\":dataLayer.product.range,\"entity.pageUrl\":window.location.href,\"entity.thumbnailUrl\":imgUrl,\"entity.message\":descriptionInnerText,\"profile.salesCompanyCode\":dataLayer.companyCode},success:function(e){adobe.target.applyOffer({mbox:\"data-collection-mbox\",offer:e})},error:function(e,a){console.log(\"Error \",e,a)}})}}catch(e){}");