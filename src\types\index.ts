// 水泵数据类型
export interface PumpData {
  Q: number[]  // 流量
  H: number[]  // 扬程
  ETA: number[] // 效率
  P: number[]  // 功率
  I?: number[] // 电流
}

// 导出网关设备类型
export * from './gateway';

// 水泵基础参数类型
export interface PumpParameters {
  name: string          // 水泵名称
  model: string         // 型号
  ratedFlow: number     // 额定流量 (m³/h)
  ratedHead: number     // 额定扬程 (m)
  ratedPower: number    // 额定功率 (kW)
  ratedSpeed: number    // 额定转速 (rpm)
  efficiency: number    // 额定效率 (%)
  impellerDiameter: number // 叶轮直径 (mm)
}

// 算法类型
export type AlgorithmType = 'least-squares' | 'neural-network' | 'polynomial' | 'spline'

// 算法配置
export interface AlgorithmConfig {
  type: AlgorithmType
  order?: number        // 多项式阶数
  epochs?: number       // 神经网络训练轮数
  learningRate?: number // 学习率
  hiddenLayers?: number[] // 隐藏层配置
}

// 曲线参数类型
export interface CurveParams {
  QH: number[]    // Q-H曲线参数
  QETA: number[]  // Q-ETA曲线参数
  QP: number[]    // Q-P曲线参数
  QI?: number[]   // Q-I曲线参数
}

// 拟合结果类型
export interface FittingResult {
  algorithm: AlgorithmType
  parameters: number[]
  r2Score: number       // 决定系数
  mse: number          // 均方误差
  mae: number          // 平均绝对误差
  trainingTime: number // 训练时间(ms)
}

// 曲线拟合配置
export interface CurveFittingConfig {
  algorithm: AlgorithmConfig
  validationSplit: number  // 验证集比例
  crossValidation: boolean // 是否使用交叉验证
}

// 菜单项类型
export interface MenuItem {
  title: string
  path: string
  icon: string
  children?: MenuItem[]
}

// 图表数据点类型
export interface ChartDataPoint {
  x: number
  y: number
}

// 图表系列数据类型
export interface ChartSeries {
  name: string
  data: ChartDataPoint[]
  type: string
  color?: string
}

// 优化结果类型
export interface OptimizationResult {
  frequency: number
  efficiency: number
  power: number
  flow: number
  head: number
}

// 系统配置类型
export interface SystemConfig {
  theme: 'light' | 'dark'
  language: 'zh-CN' | 'en-US'
  autoRefresh: boolean
  refreshInterval: number
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页参数类型
export interface PaginationParams {
  page: number
  pageSize: number
  total?: number
}

// 能耗数据类型
export interface EnergyData {
  timestamp: string
  power: number        // 功率 (kW)
  energy: number       // 能耗 (kWh)
  efficiency: number   // 效率 (%)
  cost: number        // 成本 (元)
  flow: number        // 流量 (m³/h)
  head: number        // 扬程 (m)
  frequency: number   // 频率 (Hz)
  temperature: number // 温度 (°C)
  vibration: number   // 振动 (mm/s)
}

// 统计时间范围类型
export type StatisticsPeriod = 'day' | 'week' | 'month' | 'quarter' | 'year'

// 能耗统计数据类型
export interface EnergyStatistics {
  period: StatisticsPeriod
  startDate: string
  endDate: string
  totalEnergy: number      // 总能耗 (kWh)
  totalCost: number        // 总成本 (元)
  avgPower: number         // 平均功率 (kW)
  maxPower: number         // 最大功率 (kW)
  minPower: number         // 最小功率 (kW)
  avgEfficiency: number    // 平均效率 (%)
  maxEfficiency: number    // 最大效率 (%)
  minEfficiency: number    // 最小效率 (%)
  runningHours: number     // 运行小时数
  energyPerUnit: number    // 单位能耗 (kWh/m³)
  costPerUnit: number      // 单位成本 (元/m³)
  carbonEmission: number   // 碳排放 (kg CO2)
  peakDemand: number       // 峰值需求 (kW)
  loadFactor: number       // 负荷因子
}

// 系统设置相关类型
export interface SystemSettings {
  general: GeneralSettings
  notification: NotificationSettings
  security: SecuritySettings
  backup: BackupSettings
  theme: ThemeSettings
  language: LanguageSettings
}

// 通用设置
export interface GeneralSettings {
  systemName: string
  companyName: string
  contactEmail: string
  contactPhone: string
  timezone: string
  dateFormat: string
  timeFormat: string
  currency: string
  dataRetentionDays: number
  autoRefreshInterval: number
  enableDebugMode: boolean
}

// 通知设置
export interface NotificationSettings {
  email: EmailNotificationSettings
  sms: SmsNotificationSettings
  push: PushNotificationSettings
  sound: SoundSettings
}

export interface EmailNotificationSettings {
  enabled: boolean
  smtpServer: string
  smtpPort: number
  smtpSecurity: 'none' | 'ssl' | 'tls'
  username: string
  password: string
  fromAddress: string
  fromName: string
  recipients: string[]
}

export interface SmsNotificationSettings {
  enabled: boolean
  provider: string
  apiKey: string
  apiSecret: string
  defaultRecipients: string[]
}

export interface PushNotificationSettings {
  enabled: boolean
  webPush: boolean
  mobile: boolean
  desktop: boolean
}

export interface SoundSettings {
  enabled: boolean
  volume: number
  criticalAlertSound: string
  warningAlertSound: string
  infoAlertSound: string
}

// 安全设置
export interface SecuritySettings {
  passwordPolicy: PasswordPolicy
  sessionTimeout: number
  maxLoginAttempts: number
  lockoutDuration: number
  twoFactorAuth: boolean
  ipWhitelist: string[]
  auditLog: boolean
  encryptionEnabled: boolean
}

export interface PasswordPolicy {
  minLength: number
  requireUppercase: boolean
  requireLowercase: boolean
  requireNumbers: boolean
  requireSpecialChars: boolean
  passwordExpiry: number
  preventReuse: number
}

// 备份设置
export interface BackupSettings {
  autoBackup: boolean
  backupInterval: 'daily' | 'weekly' | 'monthly'
  backupTime: string
  retentionPeriod: number
  backupLocation: string
  includeFiles: boolean
  includeDatabase: boolean
  includeSettings: boolean
  compression: boolean
  encryption: boolean
}

// 主题设置
export interface ThemeSettings {
  mode: 'light' | 'dark' | 'auto'
  primaryColor: string
  accentColor: string
  fontSize: 'small' | 'medium' | 'large'
  compactMode: boolean
  animations: boolean
  customCss: string
}

// 语言设置
export interface LanguageSettings {
  locale: string
  dateLocale: string
  numberFormat: string
  rtlSupport: boolean
}

// 用户管理
export interface User {
  id: string
  username: string
  email: string
  fullName: string
  role: UserRole
  department: string
  phone: string
  avatar: string
  status: 'active' | 'inactive' | 'locked'
  lastLogin: string
  createdAt: string
  updatedAt: string
  permissions: string[]
}

export type UserRole = 'admin' | 'operator' | 'viewer' | 'maintenance'

// 权限管理
export interface Permission {
  id: string
  name: string
  description: string
  category: string
  resource: string
  actions: string[]
}

export interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
  isSystem: boolean
  createdAt: string
  updatedAt: string
}

// 系统日志
export interface SystemLog {
  id: string
  timestamp: string
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal'
  category: string
  message: string
  details: Record<string, any>
  userId?: string
  ip?: string
  userAgent?: string
}

// 系统信息
export interface SystemInfo {
  version: string
  buildDate: string
  environment: string
  uptime: number
  cpuUsage: number
  memoryUsage: number
  diskUsage: number
  networkStatus: string
  databaseStatus: string
  lastBackup: string
  activeUsers: number
  totalDevices: number
  totalWarnings: number
}

// 能耗对比数据类型
export interface EnergyComparison {
  current: EnergyStatistics
  previous: EnergyStatistics
  changePercent: {
    totalEnergy: number
    totalCost: number
    avgEfficiency: number
    energyPerUnit: number
  }
}

// 能耗趋势数据类型
export interface EnergyTrend {
  date: string
  energy: number
  cost: number
  efficiency: number
  power: number
}

// 报表配置类型
export interface ReportConfig {
  period: StatisticsPeriod
  startDate: string
  endDate: string
  includeCharts: boolean
  includeComparison: boolean
  includeTrends: boolean
  format: 'pdf' | 'excel' | 'csv'
}

// 能耗预警类型
export interface EnergyAlert {
  id: string
  type: 'high_consumption' | 'low_efficiency' | 'cost_overrun' | 'abnormal_pattern'
  level: 'info' | 'warning' | 'critical'
  title: string
  message: string
  value: number
  threshold: number
  timestamp: string
  resolved: boolean
}

// 预警信息类型
export interface WarningInfo {
  id: string
  type: 'error' | 'warning' | 'info'
  title: string
  message: string
  timestamp: string
  resolved: boolean
  category: WarningCategory
  severity: WarningSeverity
  source: string
  parameters?: Record<string, any>
  resolvedBy?: string
  resolvedAt?: string
  actions?: WarningAction[]
}

// 预警类别
export type WarningCategory =
  | 'equipment_fault'      // 设备故障
  | 'performance_anomaly'  // 性能异常
  | 'maintenance_due'      // 维护到期
  | 'energy_consumption'   // 能耗异常
  | 'efficiency_drop'      // 效率下降
  | 'vibration_high'       // 振动过高
  | 'temperature_abnormal' // 温度异常
  | 'pressure_abnormal'    // 压力异常
  | 'flow_abnormal'        // 流量异常
  | 'system_error'         // 系统错误

// 预警严重程度
export type WarningSeverity = 'critical' | 'high' | 'medium' | 'low'

// 预警动作
export interface WarningAction {
  id: string
  name: string
  description: string
  type: 'manual' | 'automatic'
  executed: boolean
  executedAt?: string
  result?: string
}

// 预警规则
export interface WarningRule {
  id: string
  name: string
  description: string
  category: WarningCategory
  severity: WarningSeverity
  enabled: boolean
  conditions: WarningCondition[]
  actions: string[]
  cooldownMinutes: number
  createdAt: string
  updatedAt: string
}

// 预警条件
export interface WarningCondition {
  parameter: string
  operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte' | 'between'
  value: number | [number, number]
  duration?: number // 持续时间（分钟）
}

// 预警统计
export interface WarningStatistics {
  total: number
  resolved: number
  unresolved: number
  byCategory: Record<WarningCategory, number>
  bySeverity: Record<WarningSeverity, number>
  avgResolutionTime: number // 平均处理时间（分钟）
  todayCount: number
  weekCount: number
  monthCount: number
}

// 设备状态
export interface DeviceStatus {
  id: string
  name: string
  type: string
  status: 'online' | 'offline' | 'fault' | 'maintenance'
  lastUpdate: string
  parameters: {
    temperature: number
    vibration: number
    pressure: number
    flow: number
    power: number
    efficiency: number
  }
  health: number // 健康度 0-100
  nextMaintenance: string
}

// 水泵曲线数据点
export interface PumpCurvePoint {
  flowRate: number  // 流量 (m³/h)
  value: number     // 对应的值（扬程、功率、效率等）
}

// 水泵曲线
export interface PumpCurve {
  id: number
  pumpModelId: number
  curveType: 'QH' | 'QP1' | 'QP2' | 'QETA' | 'QNPSH'  // 曲线类型
  flowRate: number
  value: number
  frequency: number     // 频率 (Hz)
  liquidType: string    // 液体类型
  temperature: number   // 温度 (°C)
}

// 水泵配置
export interface PumpConfiguration {
  frequency: number     // 频率 (Hz)
  liquidType: string    // 液体类型
  temperature: number   // 温度 (°C)
  impellerDiameter?: number // 叶轮直径 (mm)
}

// 水泵模型
export interface PumpModel {
  id: number
  name: string
  manufacturer: string
  series: string
  specifications: PumpParameters
}

// 水泵曲线数据
export interface PumpCurveData {
  id: number
  pumpModelId: number
  curveType: 'QH' | 'QP1' | 'QP2' | 'QETA' | 'QNPSH'
  flowRate: number
  value: number
  frequency: number
  liquidType: string
  temperature: number
}

// 优化数据
export interface OptimizationData {
  id: string
  timestamp: string
  parameters: Record<string, number>
  results: OptimizationResult
  algorithm: string
  convergence: boolean
  iterations: number
}

// 数据库配置类型
export interface DatabaseConfig {
  host: string
  port: number
  database: string
  username: string
  password: string
  ssl: boolean
  connectionTimeout: number
  maxConnections: number
}

// 图表配置选项类型
export interface ChartConfigOptions {
  dataPoints: any[]
  npshPoints: any[]
  singlePumpData: any[]
  pumpParameters: PumpParameters
  settings: any
  systemPerformance: any
  [key: string]: any
}

// MQTT配置类型
export interface MQTTConfig {
  brokerUrl: string
  clientId: string
  username?: string
  password?: string
  topicPrefix: string
  publishTopic: string
  subscribeTopic: string
  qos: 0 | 1 | 2
  keepAlive: number
  reconnectPeriod: number
  port?: number
  topics?: string[]
}

// 设备错误日志类型
export interface DeviceErrorLog {
  id: string
  deviceId: string
  timestamp: number
  errorCode: number
  errorMessage: string
  message?: string  // 兼容旧属性
  code?: number     // 兼容旧属性
  severity: 'warning' | 'info' | 'critical'
  resolved: boolean
  resolvedTime?: number
}

// 图表数据类型
export interface ChartData {
  statistics: EnergyStatistics
  [key: string]: any
}