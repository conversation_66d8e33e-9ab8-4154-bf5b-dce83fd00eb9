<template>
  <div class="warnings-list">
    <el-card>
      <template #header>
        <div class="list-header">
          <span>预警列表</span>
          <div class="header-actions">
            <el-button size="small" @click="exportReport">
              <el-icon><Download /></el-icon>
              导出报告
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 筛选条件 -->
      <div class="filters">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-select v-model="filters.severity" placeholder="选择严重程度" clearable>
              <el-option label="严重" value="critical" />
              <el-option label="高" value="high" />
              <el-option label="中" value="medium" />
              <el-option label="低" value="low" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filters.category" placeholder="选择预警类别" clearable>
              <el-option label="设备故障" value="equipment_fault" />
              <el-option label="性能异常" value="performance_anomaly" />
              <el-option label="维护到期" value="maintenance_due" />
              <el-option label="能耗异常" value="energy_consumption" />
              <el-option label="效率下降" value="efficiency_drop" />
              <el-option label="振动过高" value="vibration_high" />
              <el-option label="温度异常" value="temperature_abnormal" />
              <el-option label="压力异常" value="pressure_abnormal" />
              <el-option label="流量异常" value="flow_abnormal" />
              <el-option label="系统错误" value="system_error" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filters.resolved" placeholder="选择处理状态" clearable>
              <el-option label="已处理" :value="true" />
              <el-option label="未处理" :value="false" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button @click="clearFilters">清除筛选</el-button>
          </el-col>
        </el-row>
      </div>
      
      <!-- 预警表格 -->
      <el-table 
        :data="filteredWarnings" 
        stripe 
        v-loading="warningStore.loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="severity" label="级别" width="80">
          <template #default="{ row }">
            <el-tag :type="getSeverityType(row.severity)" size="small">
              {{ getSeverityText(row.severity) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="title" label="预警信息" min-width="250">
          <template #default="{ row }">
            <div class="warning-info">
              <div class="warning-title">{{ row.title }}</div>
              <div class="warning-message">{{ row.message }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="category" label="类别" width="120">
          <template #default="{ row }">
            <el-tag :type="getCategoryType(row.category)" size="small">
              {{ getCategoryText(row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="source" label="设备" width="120" />
        
        <el-table-column prop="timestamp" label="发生时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.timestamp) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="resolved" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.resolved ? 'success' : 'danger'" size="small">
              {{ row.resolved ? '已处理' : '待处理' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button
              v-if="!row.resolved"
              size="small"
              type="primary"
              @click="resolveWarning(row.id)"
            >
              处理
            </el-button>
            <el-button
              size="small"
              @click="showDetails(row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 批量操作 -->
      <div v-if="selectedWarnings.length > 0" class="batch-actions">
        <el-alert
          :title="`已选择 ${selectedWarnings.length} 条预警`"
          type="info"
          :closable="false"
        >
          <template #default>
            <el-button size="small" type="primary" @click="batchResolve">
              批量处理
            </el-button>
          </template>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Download } from '@element-plus/icons-vue'
import { useWarningStore } from '@/stores/warning'
import type { WarningInfo, WarningCategory, WarningSeverity } from '@/types'
import dayjs from 'dayjs'
import { ElMessage, ElMessageBox } from 'element-plus'

const warningStore = useWarningStore()

// 响应式数据
const selectedWarnings = ref<WarningInfo[]>([])
const filters = ref({
  severity: null as WarningSeverity | null,
  category: null as WarningCategory | null,
  resolved: null as boolean | null
})

// 计算属性
const filteredWarnings = computed(() => {
  let warnings = [...warningStore.warnings]
  
  if (filters.value.severity) {
    warnings = warnings.filter(w => w.severity === filters.value.severity)
  }
  
  if (filters.value.category) {
    warnings = warnings.filter(w => w.category === filters.value.category)
  }
  
  if (filters.value.resolved !== null) {
    warnings = warnings.filter(w => w.resolved === filters.value.resolved)
  }
  
  return warnings.sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  )
})

// 方法
const getSeverityType = (severity: WarningSeverity) => {
  const types = {
    critical: 'danger',
    high: 'warning',
    medium: 'primary',
    low: 'info'
  }
  return types[severity] || 'info'
}

const getSeverityText = (severity: WarningSeverity) => {
  const texts = {
    critical: '严重',
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[severity] || severity
}

const getCategoryType = (category: WarningCategory) => {
  const types = {
    equipment_fault: 'danger',
    performance_anomaly: 'warning',
    maintenance_due: 'info',
    energy_consumption: 'primary',
    efficiency_drop: 'warning',
    vibration_high: 'danger',
    temperature_abnormal: 'danger',
    pressure_abnormal: 'warning',
    flow_abnormal: 'warning',
    system_error: 'danger'
  }
  return types[category] || 'info'
}

const getCategoryText = (category: WarningCategory) => {
  const texts = {
    equipment_fault: '设备故障',
    performance_anomaly: '性能异常',
    maintenance_due: '维护到期',
    energy_consumption: '能耗异常',
    efficiency_drop: '效率下降',
    vibration_high: '振动过高',
    temperature_abnormal: '温度异常',
    pressure_abnormal: '压力异常',
    flow_abnormal: '流量异常',
    system_error: '系统错误'
  }
  return texts[category] || category
}

const formatTime = (timestamp: string) => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

const handleSelectionChange = (selection: WarningInfo[]) => {
  selectedWarnings.value = selection
}

const clearFilters = () => {
  filters.value = {
    severity: null,
    category: null,
    resolved: null
  }
}

const resolveWarning = async (warningId: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要处理这个预警吗？',
      '确认处理',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const success = await warningStore.resolveWarning(warningId)
    if (success) {
      ElMessage.success('预警已处理')
    } else {
      ElMessage.error('处理失败')
    }
  } catch {
    // 用户取消
  }
}

const batchResolve = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要批量处理选中的 ${selectedWarnings.value.length} 条预警吗？`,
      '确认批量处理',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const warningIds = selectedWarnings.value.map(w => w.id)
    const successCount = await warningStore.batchResolveWarnings(warningIds)
    
    ElMessage.success(`成功处理 ${successCount} 条预警`)
    selectedWarnings.value = []
  } catch {
    // 用户取消
  }
}

const showDetails = (warning: WarningInfo) => {
  warningStore.showWarningDetails(warning)
}

const exportReport = async () => {
  try {
    const result = await warningStore.exportWarningReport({
      format: 'excel',
      includeResolved: true
    })
    ElMessage.success('报告导出成功')
  } catch (error) {
    ElMessage.error('报告导出失败')
  }
}
</script>

<style lang="scss" scoped>
.warnings-list {
  .list-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
  }
  
  .filters {
    margin-bottom: 16px;
  }
  
  .warning-info {
    .warning-title {
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 4px;
    }
    
    .warning-message {
      font-size: 12px;
      color: var(--el-text-color-regular);
      line-height: 1.4;
    }
  }
  
  .batch-actions {
    margin-top: 16px;
  }
}
</style>
