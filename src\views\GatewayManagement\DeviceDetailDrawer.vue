<script setup lang="ts">
import { ref, computed, watch, onBeforeUnmount } from 'vue';
import { useGatewayStore } from '../../stores/gateway';
import MqttConfigPanel from './MqttConfigPanel.vue';
import DeviceDataPanel from './DeviceDataPanel.vue';
import DeviceErrorsPanel from './DeviceErrorsPanel.vue';
import { Refresh, Edit, Delete } from '@element-plus/icons-vue';
import { ElMessageBox } from 'element-plus';
import type { GatewayDevice } from '../../types/gateway';

const props = defineProps<{
  visible: boolean;
  device: GatewayDevice | null;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'update', deviceId: string, updates: Partial<GatewayDevice>): void;
  (e: 'delete', deviceId: string): void;
  (e: 'connect', deviceId: string): void;
  (e: 'disconnect', deviceId: string): void;
  (e: 'restart', deviceId: string): void;
}>();

const gatewayStore = useGatewayStore();
const activeTab = ref('info');
const autoRefresh = ref(false);
const refreshInterval = ref(5000); // 默认刷新间隔5秒
let refreshTimer: ReturnType<typeof setInterval> | null = null;

// 计算属性：当前设备
const device = computed(() => {
  return gatewayStore.selectedDevice;
});

// 计算属性：当前设备的MQTT状态
const mqttConnected = computed(() => {
  return gatewayStore.mqttConnected;
});

const mqttStatus = computed(() => {
  return gatewayStore.mqttStatus;
});

// 计算属性：最近接收到的数据
const lastReceivedData = computed(() => {
  return gatewayStore.lastReceivedData;
});

// 监听设备ID变化，加载设备数据
watch(() => props.device?.id, async (newDeviceId) => {
  if (newDeviceId) {
    await gatewayStore.selectDevice(newDeviceId);
  }
}, { immediate: true });

// 关闭抽屉
const closeDrawer = () => {
  emit('update:visible', false);
  stopAutoRefresh();
};

// 删除设备
const deleteDevice = () => {
  if (props.device?.id) {
    ElMessageBox.confirm(
      `确定要删除设备 ${props.device.name} 吗？此操作不可恢复。`,
      '删除设备',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      if (props.device) {
        emit('delete', props.device.id);
      }
      closeDrawer();
    }).catch(() => {
      // 取消删除
    });
  }
};

// 重启设备
const restartDevice = () => {
  if (props.device?.id) {
    ElMessageBox.confirm(
      `确定要重启设备 ${props.device.name} 吗？`,
      '重启设备',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      if (props.device) {
        emit('restart', props.device.id);
      }
    }).catch(() => {
      // 取消重启
    });
  }
};

// 测试连接
const testConnection = () => {
  if (props.device?.id) {
    emit('connect', props.device.id);
  }
};

// 手动刷新数据
const refreshData = async () => {
  if (props.device?.id) {
    await gatewayStore.fetchDeviceById(props.device.id);
  }
};

// 开始自动刷新
const startAutoRefresh = () => {
  stopAutoRefresh(); // 先停止现有的定时器
  
  refreshTimer = setInterval(() => {
    refreshData();
  }, refreshInterval.value);
};

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

// 监听自动刷新开关
watch(autoRefresh, (newValue) => {
  if (newValue) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
});

// 格式化日期时间
const formatDateTime = (dateString: string | Date | undefined) => {
  if (!dateString) return '--';
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  return date.toLocaleString('zh-CN');
};

// 在组件销毁前停止自动刷新
onBeforeUnmount(() => {
  stopAutoRefresh();
});

// 处理编辑
const handleEdit = () => {
  // 这里可以打开编辑对话框
  // 或者发出事件，让父组件处理
};

// 处理连接/断开操作
const handleConnectionAction = () => {
  if (props.device?.id) {
    if (props.device.status === 'online') {
      emit('disconnect', props.device.id);
    } else {
      emit('connect', props.device.id);
    }
  }
};

// 处理MQTT配置更新
const handleMQTTUpdate = (config: any) => {
  if (props.device?.id) {
    emit('update', props.device.id, { mqttConfig: config });
  }
};

// 获取状态类型
const getStatusType = (status?: string) => {
  switch (status) {
    case 'online':
      return 'success';
    case 'offline':
      return 'info';
    case 'error':
      return 'danger';
    default:
      return 'info';
  }
};

// 获取状态文本
const getStatusText = (status?: string) => {
  switch (status) {
    case 'online':
      return '在线';
    case 'offline':
      return '离线';
    case 'error':
      return '错误';
    default:
      return '未知';
  }
};
</script>

<template>
  <el-drawer
    v-model="visible"
    :title="`设备详情: ${device?.name}`"
    direction="rtl"
    size="600px"
    destroy-on-close
    @close="closeDrawer"
  >
    <template #header>
      <div class="drawer-header">
        <h2>{{ device?.name }}</h2>
        <el-tag :type="getStatusType(device?.status)" effect="light">{{ getStatusText(device?.status) }}</el-tag>
      </div>
    </template>
    
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="基本信息" name="info">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="设备ID">{{ device?.id }}</el-descriptions-item>
          <el-descriptions-item label="设备名称">{{ device?.name }}</el-descriptions-item>
          <el-descriptions-item label="IP地址">{{ device?.ip }}</el-descriptions-item>
          <el-descriptions-item label="端口">{{ device?.port }}</el-descriptions-item>
          <el-descriptions-item label="设备型号">{{ device?.modelName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="制造商">{{ device?.manufacturer || '-' }}</el-descriptions-item>
          <el-descriptions-item label="固件版本">{{ device?.firmwareVersion || '-' }}</el-descriptions-item>
          <el-descriptions-item label="位置">{{ device?.location || '-' }}</el-descriptions-item>
          <el-descriptions-item label="最后连接时间">
            {{ device?.lastConnectTime ? formatDateTime(device.lastConnectTime) : '未连接过' }}
          </el-descriptions-item>
          <el-descriptions-item label="设备描述" :span="1">
            <div class="description-text">
              {{ device?.description || '无描述' }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="actions">
          <el-button type="primary" @click="handleEdit">
            <el-icon><Edit /></el-icon> 编辑信息
          </el-button>
          <el-button type="danger" @click="deleteDevice">
            <el-icon><Delete /></el-icon> 删除设备
          </el-button>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="实时数据" name="data">
        <device-data-panel :device-id="device?.id" />
      </el-tab-pane>
      
      <el-tab-pane label="MQTT配置" name="mqtt">
        <mqtt-config-panel :device-id="device?.id" @update="handleMQTTUpdate" />
      </el-tab-pane>
      
      <el-tab-pane label="错误日志" name="errors">
        <device-errors-panel :device-id="device?.id || ''" />
      </el-tab-pane>
    </el-tabs>
    
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="closeDrawer">关闭</el-button>
        <el-button 
          :type="device?.status === 'online' ? 'danger' : 'primary'"
          @click="handleConnectionAction"
        >
          {{ device?.status === 'online' ? '断开连接' : '连接设备' }}
        </el-button>
        <el-button 
          type="warning" 
          :disabled="device?.status !== 'online'" 
          @click="restartDevice"
        >
          重启设备
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<style lang="scss" scoped>
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  
  h2 {
    margin: 0;
    font-size: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.description-text {
  max-height: 100px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

.actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  width: 100%;
}

:deep(.el-descriptions__label) {
  width: 120px;
}
</style> 