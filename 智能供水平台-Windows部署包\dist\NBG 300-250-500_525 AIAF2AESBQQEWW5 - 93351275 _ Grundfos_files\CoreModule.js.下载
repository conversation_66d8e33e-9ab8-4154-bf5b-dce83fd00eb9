
/*@preserve
***Version 2.30.0***
*/

/*@license
 *                       Copyright 2002 - 2018 Qualtrics, LLC.
 *                                All rights reserved.
 *
 * Notice: All code, text, concepts, and other information herein (collectively, the
 * "Materials") are the sole property of Qualtrics, LLC, except to the extent
 * otherwise indicated. The Materials are proprietary to Qualtrics and are protected
 * under all applicable laws, including copyright, patent (as applicable), trade
 * secret, and contract law. Disclosure or reproduction of any Materials is strictly
 * prohibited without the express prior written consent of an authorized signatory
 * of Qualtrics. For disclosure requests, <NAME_EMAIL>.
 */

try {
  !function(e){var t={};function n(i){if(t[i])return t[i].exports;var r=t[i]={i:i,l:!1,exports:{}};return e[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}n.m=e,n.c=t,n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(i,r,function(t){return e[t]}.bind(null,r));return i},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=4)}([function(e,t,n){"use strict";n.r(t),n.d(t,"creativeTypes",(function(){return o})),n.d(t,"originalDocumentOverflow",(function(){return a})),n.d(t,"$",(function(){return s})),n.d(t,"setStyle",(function(){return u})),n.d(t,"patch",(function(){return c})),n.d(t,"getBrandDC",(function(){return d})),n.d(t,"getStyle",(function(){return l})),n.d(t,"getStyles",(function(){return p})),n.d(t,"getTempID",(function(){return f})),n.d(t,"getDateNow",(function(){return g})),n.d(t,"getElementHTML",(function(){return w})),n.d(t,"getOriginInterceptOfMessage",(function(){return h})),n.d(t,"validatePostMessage",(function(){return S})),n.d(t,"addToStyleElements",(function(){return I})),n.d(t,"buildQueryString",(function(){return v})),n.d(t,"sendFocusToFirstTabbableElement",(function(){return m})),n.d(t,"sendFocusToFirstTabbableElementOnKeydown",(function(){return E})),n.d(t,"waitForFocus",(function(){return y})),n.d(t,"observe",(function(){return b})),n.d(t,"removeObservers",(function(){return T})),n.d(t,"stopObserving",(function(){return Q})),n.d(t,"pushTimeout",(function(){return N})),n.d(t,"removeTimeouts",(function(){return A})),n.d(t,"waitForExitIntent",(function(){return O})),n.d(t,"truncateString",(function(){return R})),n.d(t,"build",(function(){return _})),n.d(t,"isString",(function(){return D})),n.d(t,"isArray",(function(){return C})),n.d(t,"isFunction",(function(){return L})),n.d(t,"isIE8",(function(){return P})),n.d(t,"isIE",(function(){return x})),n.d(t,"isEdge",(function(){return M})),n.d(t,"isChrome",(function(){return F})),n.d(t,"isOpera",(function(){return k})),n.d(t,"isFF",(function(){return H})),n.d(t,"isAndroid",(function(){return U})),n.d(t,"createArrayFromArguments",(function(){return V})),n.d(t,"forOwn",(function(){return B})),n.d(t,"getPageSize",(function(){return G})),n.d(t,"openWin",(function(){return W})),n.d(t,"startScrolling",(function(){return j})),n.d(t,"stopScrolling",(function(){return J})),n.d(t,"preventDefault",(function(){return q})),n.d(t,"remove",(function(){return Y})),n.d(t,"usePostToStart",(function(){return z})),n.d(t,"closeResponsiveEmbeddedTarget",(function(){return K})),n.d(t,"isObjLiteral",(function(){return X})),n.d(t,"strIncludes",(function(){return Z})),n.d(t,"arrIncludes",(function(){return $})),n.d(t,"detectClickInIframe",(function(){return ee})),n.d(t,"detectClickInIframeListener",(function(){return te}));var i=[],r=[],o={EMPTY:"Empty",FEEDBACK_LINK:"FeedbackLink",HTTP_REDIRECT:"HTTPRedirect",IFRAME:"IFrame",INFO_BAR:"InfoBar",LINK:"Link",NO_CREATIVE:"NoCreative",POP_OVER:"PopOver",POP_UNDER:"PopUnder",POP_UP:"PopUp",RELAY:"Relay",SLIDER:"Slider",USER_DEFINED_HTML:"UserDefinedHTML"},a="auto",s=function(e){var t=e;return"string"==typeof e&&(t=document.getElementById(e)),t},u=function(e,t){var n=window.QSI.dbg;for(var i in t)try{e.style[i]=t[i]}catch(e){n.e(e)}};function c(e,t,n){try{var i=e[t];return e[t]=n(i),function(){e[t]=i}}catch(e){return function(){}}}var d=function(e){return e?e.match(/(.*).qualtrics.com$/)[1]:window.QSI.global.brandDC.match(/(.*).qualtrics.com$/)[1]},l=function(e,t){var n,i=p(e);try{var r=i?i[t]||i.getPropertyValue(t):void 0;n=void 0!==r?r.toString():void 0}catch(e){n=void 0}return void 0===n?n:n+""},p=function(e){return e.ownerDocument.defaultView.opener?e.ownerDocument.defaultView.getComputedStyle(e,null):window.getComputedStyle(e,null)},f=function(){return"QSI_"+Math.floor(1e5*Math.random()+1)},g=function(){return Date.now?Date.now().valueOf():(new Date).valueOf()},w=function(e){var t=document.createElement("div");return t.appendChild(e.cloneNode(!0)),t.innerHTML},h=function(e){var t=window.QSI.reg;if(!t)return null;for(var n in t)if(t[n]){var i=t[n];if(i.embeddedTargets)for(var r=0;r<i.embeddedTargets.length;r++)if(i.embeddedTargets[r].contentWindow===e)return t[n];if(i.embeddedWindows)for(var o=0;o<i.embeddedWindows.length;o++)if(i.embeddedWindows[o].targetIframe.contentWindow===e)return t[n]}var a=document.querySelectorAll('iframe:not([data-interceptId=""])');for(r=0;r<a.length;r++){if(a[r].contentWindow===e)return t[n=a[r].getAttribute("data-interceptId")]}return null},S=function(e){if(window.QSI.Orchestrator&&window.QSI.Orchestrator.isMessageEventOriginAllowed&&!window.QSI.Orchestrator.isMessageEventOriginAllowed(e.origin))return null;if(!window.QSI.util.getOriginInterceptOfMessage(e.source))return null;var t=e.data;if("string"==typeof t)try{t=JSON.parse(t)}catch(e){return null}return t},I=function(e){var t=window.QSI;t.styleElements=t.styleElements||[],t.styleElements.push(e)},v=function(e){var t=[];for(var n in e){var i=e[n];void 0===i?t.push(encodeURIComponent(n)):t.push(encodeURIComponent(n)+"="+encodeURIComponent(i))}return t.join("&")},m=function(){var e=document.querySelector('button, a[href], input, select, textarea, [tabindex]:not([tabindex="-1"])');e&&e.focus()},E=function(e,t){13!==e.which&&32!==e.which||(e.preventDefault(),m(),t())},y=function(){var e=window.QSI.dbg;return new Promise((function(t){document.hasFocus()?t():b(window,"focus",(function(){try{t()}catch(t){e.e(t)}}))}))},b=function(e,t,n,r){void 0===r&&(r=!1),i=i||[],e&&(i.push({elementToObserve:e,eventName:t,eventHandler:n,preventRemove:r||!1}),e.addEventListener(t,n,!1))},T=function(){i.forEach((function(e){e.preventRemove||Q(e.elementToObserve,e.eventName,e.eventHandler)}))},Q=function(e,t,n){e.removeEventListener(t,n,!1)},N=function(e){(r=r||[]).push(e)},A=function(){r.forEach((function(e){clearTimeout(e)}))},O=function(){var e=window.QSI,t=e.dbg,n=e.config,i=e.Browser;return new Promise((function(e){b(window,"mouseout",(function(r){try{if(!document.hasFocus())return;if(M()||x()){if(!r.relatedTarget&&!r.toElement){if(r.clientY/window.innerHeight*100>(n.ieEdgeMouseOutRange||5))return;e()}}else{if(r.clientY>0)return;if("Firefox"===i.name&&"SELECT"===r.target.tagName)return;e()}}catch(e){t.e(e)}}))}))},R=function(e,t){return null==e?null:("string"!=typeof e&&(e=JSON.stringify(e)),e.length>t&&(e=e.slice(0,t-"...".length)+"..."),e)},_=function(e,t,n){var i=document.createElement(e);if(t)for(var r in t)switch(r){case"style":u(i,t[r]);break;case"className":i.className=t[r];break;case"id":i.id=t[r];break;default:i.setAttribute(r,t[r])}if(n)if(D(n))i.appendChild(document.createTextNode(String(n)));else if(C(n))for(var o=0,a=n.length;o<a;o++){var s=n[o];"string"==typeof s||"number"==typeof s?i.appendChild(document.createTextNode(String(s))):s&&s.nodeType&&i.appendChild(s)}return"style"===e&&window.QSI.config.nonce&&(i.nonce=window.QSI.config.nonce),i},D=function(e){return"string"==typeof e},C=function(e){return"object"==typeof e&&e instanceof Array},L=function(e){return"function"==typeof e||!1},P=function(){return x(8)},x=function(e){var t=window.QSI.Browser;return"Internet Explorer"===t.name&&(!e||e===t.version)},M=function(){var e=window.QSI.Browser;return"Microsoft Edge"===e.name||"Edge"===e.name},F=function(){return"Chrome"===window.QSI.Browser.name},k=function(){return"Opera"===window.QSI.Browser.name},H=function(){return"Firefox"===window.QSI.Browser.name},U=function(){return"Android"===window.QSI.OS.name},V=function(e){return e?Array.prototype.slice.call(e):[]},B=function(e,t){if(e&&e instanceof Object&&L(t))for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t(e[n],n,e)},G=function(e){void 0===e&&(e=window);var t=e.document.documentElement||{};return{width:t.clientWidth||e.innerWidth,height:t.clientHeight||e.innerHeight}},W=function(e,t,n,i,r,o){void 0===n&&(n={});var a=[];for(var s in n)a.push(s+"="+n[s]);var u=a.join(",");return z(o)?window.QSI.WindowUtils.getOpenNewWindowOnclickHandler(e,i,r,u,null,t).onclickHandler():window.open(e,t,u)},j=function(){u(document.body,{overflow:a}),document.removeEventListener("touchmove",q)},J=function(){a=a||l(document.body,"overflow"),u(document.body,{overflow:"hidden"}),document.addEventListener("touchmove",q,{passive:!1})},q=function(e){e.preventDefault()},Y=function(e){e&&e.parentNode&&e.parentNode.removeChild(e)},z=function(e){return!(!window.QSI.global.featureFlags["DX.PostToStart"]||"Survey"!==e)},K=function(e,t){Y(e),t&&t.focus(),j()},X=function(e){var t=e;return"object"==typeof e&&null!==e&&function(){for(var n=!0;n;)null===Object.getPrototypeOf(t=Object.getPrototypeOf(t))&&(n=!1);return Object.getPrototypeOf(e)===t}()},Z=function(e,t,n){return void 0===n&&(n=0),-1!==e.indexOf(t,n)},$=function(e,t,n){void 0===n&&(n=0);var i=e.length>>>0;if(0===i)return!1;var r,o,a=n,s=Math.max(a>=0?a:i-Math.abs(a),0);for(;s<i;){if((r=e[s])===(o=t)||"number"==typeof r&&"number"==typeof o&&isNaN(r)&&isNaN(o))return!0;s++}return!1},ee=function(e){setTimeout((function(){"IFRAME"===document.activeElement.tagName&&document.activeElement.getAttribute("data-name")===e.dataName&&(e.click(),Q(window,"blur",e.detectClickInIframeListenerFunction))}))},te=function(e){e.detectClickInIframeListenerFunction=function(){window.QSI.util.detectClickInIframe(e)},b(window,"blur",e.detectClickInIframeListenerFunction)}},function(e,t,n){var i;!function(r){"use strict";var o,a="[big.js] ",s=a+"Invalid ",u=s+"decimal places",c={},d=/^-?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i;function l(e,t,n,i){var r=e.c,o=e.e+t+1;if(o<r.length){if(1===n)i=r[o]>=5;else if(2===n)i=r[o]>5||5==r[o]&&(i||o<0||void 0!==r[o+1]||1&r[o-1]);else if(3===n)i=i||!!r[0];else if(i=!1,0!==n)throw Error("[big.js] Invalid rounding mode");if(o<1)r.length=1,i?(e.e=-t,r[0]=1):r[0]=e.e=0;else{if(r.length=o--,i)for(;++r[o]>9;)r[o]=0,o--||(++e.e,r.unshift(1));for(o=r.length;!r[--o];)r.pop()}}else if(n<0||n>3||n!==~~n)throw Error("[big.js] Invalid rounding mode");return e}function p(e,t,n,i){var r,o,a=e.constructor,c=!e.c[0];if(void 0!==n){if(n!==~~n||n<(3==t)||n>1e6)throw Error(3==t?s+"precision":u);for(n=i-(e=new a(e)).e,e.c.length>++i&&l(e,n,a.RM),2==t&&(i=e.e+n+1);e.c.length<i;)e.c.push(0)}if(r=e.e,n=(o=e.c.join("")).length,2!=t&&(1==t||3==t&&i<=r||r<=a.NE||r>=a.PE))o=o.charAt(0)+(n>1?"."+o.slice(1):"")+(r<0?"e":"e+")+r;else if(r<0){for(;++r;)o="0"+o;o="0."+o}else if(r>0)if(++r>n)for(r-=n;r--;)o+="0";else r<n&&(o=o.slice(0,r)+"."+o.slice(r));else n>1&&(o=o.charAt(0)+"."+o.slice(1));return e.s<0&&(!c||4==t)?"-"+o:o}c.abs=function(){var e=new this.constructor(this);return e.s=1,e},c.cmp=function(e){var t,n=this,i=n.c,r=(e=new n.constructor(e)).c,o=n.s,a=e.s,s=n.e,u=e.e;if(!i[0]||!r[0])return i[0]?o:r[0]?-a:0;if(o!=a)return o;if(t=o<0,s!=u)return s>u^t?1:-1;for(a=(s=i.length)<(u=r.length)?s:u,o=-1;++o<a;)if(i[o]!=r[o])return i[o]>r[o]^t?1:-1;return s==u?0:s>u^t?1:-1},c.div=function(e){var t=this,n=t.constructor,i=t.c,r=(e=new n(e)).c,o=t.s==e.s?1:-1,a=n.DP;if(a!==~~a||a<0||a>1e6)throw Error(u);if(!r[0])throw Error("[big.js] Division by zero");if(!i[0])return new n(0*o);var s,c,d,p,f,g=r.slice(),w=s=r.length,h=i.length,S=i.slice(0,s),I=S.length,v=e,m=v.c=[],E=0,y=a+(v.e=t.e-e.e)+1;for(v.s=o,o=y<0?0:y,g.unshift(0);I++<s;)S.push(0);do{for(d=0;d<10;d++){if(s!=(I=S.length))p=s>I?1:-1;else for(f=-1,p=0;++f<s;)if(r[f]!=S[f]){p=r[f]>S[f]?1:-1;break}if(!(p<0))break;for(c=I==s?r:g;I;){if(S[--I]<c[I]){for(f=I;f&&!S[--f];)S[f]=9;--S[f],S[I]+=10}S[I]-=c[I]}for(;!S[0];)S.shift()}m[E++]=p?d:++d,S[0]&&p?S[I]=i[w]||0:S=[i[w]]}while((w++<h||void 0!==S[0])&&o--);return m[0]||1==E||(m.shift(),v.e--),E>y&&l(v,a,n.RM,void 0!==S[0]),v},c.eq=function(e){return!this.cmp(e)},c.gt=function(e){return this.cmp(e)>0},c.gte=function(e){return this.cmp(e)>-1},c.lt=function(e){return this.cmp(e)<0},c.lte=function(e){return this.cmp(e)<1},c.minus=c.sub=function(e){var t,n,i,r,o=this,a=o.constructor,s=o.s,u=(e=new a(e)).s;if(s!=u)return e.s=-u,o.plus(e);var c=o.c.slice(),d=o.e,l=e.c,p=e.e;if(!c[0]||!l[0])return l[0]?(e.s=-u,e):new a(c[0]?o:0);if(s=d-p){for((r=s<0)?(s=-s,i=c):(p=d,i=l),i.reverse(),u=s;u--;)i.push(0);i.reverse()}else for(n=((r=c.length<l.length)?c:l).length,s=u=0;u<n;u++)if(c[u]!=l[u]){r=c[u]<l[u];break}if(r&&(i=c,c=l,l=i,e.s=-e.s),(u=(n=l.length)-(t=c.length))>0)for(;u--;)c[t++]=0;for(u=t;n>s;){if(c[--n]<l[n]){for(t=n;t&&!c[--t];)c[t]=9;--c[t],c[n]+=10}c[n]-=l[n]}for(;0===c[--u];)c.pop();for(;0===c[0];)c.shift(),--p;return c[0]||(e.s=1,c=[p=0]),e.c=c,e.e=p,e},c.mod=function(e){var t,n=this,i=n.constructor,r=n.s,o=(e=new i(e)).s;if(!e.c[0])throw Error("[big.js] Division by zero");return n.s=e.s=1,t=1==e.cmp(n),n.s=r,e.s=o,t?new i(n):(r=i.DP,o=i.RM,i.DP=i.RM=0,n=n.div(e),i.DP=r,i.RM=o,this.minus(n.times(e)))},c.plus=c.add=function(e){var t,n=this,i=n.constructor,r=n.s,o=(e=new i(e)).s;if(r!=o)return e.s=-o,n.minus(e);var a=n.e,s=n.c,u=e.e,c=e.c;if(!s[0]||!c[0])return c[0]?e:new i(s[0]?n:0*r);if(s=s.slice(),r=a-u){for(r>0?(u=a,t=c):(r=-r,t=s),t.reverse();r--;)t.push(0);t.reverse()}for(s.length-c.length<0&&(t=c,c=s,s=t),r=c.length,o=0;r;s[r]%=10)o=(s[--r]=s[r]+c[r]+o)/10|0;for(o&&(s.unshift(o),++u),r=s.length;0===s[--r];)s.pop();return e.c=s,e.e=u,e},c.pow=function(e){var t=this,n=new t.constructor(1),i=n,r=e<0;if(e!==~~e||e<-1e6||e>1e6)throw Error(s+"exponent");for(r&&(e=-e);1&e&&(i=i.times(t)),e>>=1;)t=t.times(t);return r?n.div(i):i},c.round=function(e,t){var n=this.constructor;if(void 0===e)e=0;else if(e!==~~e||e<-1e6||e>1e6)throw Error(u);return l(new n(this),e,void 0===t?n.RM:t)},c.sqrt=function(){var e,t,n,i=this,r=i.constructor,o=i.s,s=i.e,u=new r(.5);if(!i.c[0])return new r(i);if(o<0)throw Error(a+"No square root");0===(o=Math.sqrt(i+""))||o===1/0?((t=i.c.join("")).length+s&1||(t+="0"),s=((s+1)/2|0)-(s<0||1&s),e=new r(((o=Math.sqrt(t))==1/0?"1e":(o=o.toExponential()).slice(0,o.indexOf("e")+1))+s)):e=new r(o),s=e.e+(r.DP+=4);do{n=e,e=u.times(n.plus(i.div(n)))}while(n.c.slice(0,s).join("")!==e.c.slice(0,s).join(""));return l(e,r.DP-=4,r.RM)},c.times=c.mul=function(e){var t,n=this,i=n.constructor,r=n.c,o=(e=new i(e)).c,a=r.length,s=o.length,u=n.e,c=e.e;if(e.s=n.s==e.s?1:-1,!r[0]||!o[0])return new i(0*e.s);for(e.e=u+c,a<s&&(t=r,r=o,o=t,c=a,a=s,s=c),t=new Array(c=a+s);c--;)t[c]=0;for(u=s;u--;){for(s=0,c=a+u;c>u;)s=t[c]+o[u]*r[c-u-1]+s,t[c--]=s%10,s=s/10|0;t[c]=(t[c]+s)%10}for(s?++e.e:t.shift(),u=t.length;!t[--u];)t.pop();return e.c=t,e},c.toExponential=function(e){return p(this,1,e,e)},c.toFixed=function(e){return p(this,2,e,this.e+e)},c.toPrecision=function(e){return p(this,3,e,e-1)},c.toString=function(){return p(this)},c.valueOf=c.toJSON=function(){return p(this,4)},(o=function e(){function t(n){var i=this;if(!(i instanceof t))return void 0===n?e():new t(n);n instanceof t?(i.s=n.s,i.e=n.e,i.c=n.c.slice()):function(e,t){var n,i,r;if(0===t&&1/t<0)t="-0";else if(!d.test(t+=""))throw Error(s+"number");e.s="-"==t.charAt(0)?(t=t.slice(1),-1):1,(n=t.indexOf("."))>-1&&(t=t.replace(".",""));(i=t.search(/e/i))>0?(n<0&&(n=i),n+=+t.slice(i+1),t=t.substring(0,i)):n<0&&(n=t.length);for(r=t.length,i=0;i<r&&"0"==t.charAt(i);)++i;if(i==r)e.c=[e.e=0];else{for(;r>0&&"0"==t.charAt(--r););for(e.e=n-i-1,e.c=[],n=0;i<=r;)e.c[n++]=+t.charAt(i++)}}(i,n),i.constructor=t}return t.prototype=c,t.DP=20,t.RM=1,t.NE=-7,t.PE=21,t.version="5.2.2",t}()).default=o.Big=o,void 0===(i=function(){return o}.call(t,n,t,e))||(e.exports=i)}()},function(e,t,n){"use strict";n.r(t),n.d(t,"isLocalStorageEnabled",(function(){return i}));var i=function(){try{return localStorage.setItem("qsi_test_local_storage","local_storage_test_value"),localStorage.removeItem("qsi_test_local_storage"),!0}catch(e){return!1}}},function(e,t,n){"use strict";function i(e,t,n,i,r,o){return{onclickHandler:function(){var a=o||e,s=document.createElement("form");s.setAttribute("method","post"),s.setAttribute("action",e),s.setAttribute("accept-charset","utf-8"),s.setAttribute("target",a),window.QSI.EmbeddedData.getEmbeddedDataAsArray(t,n,null,!0).forEach((function(e){var t=document.createElement("input");t.type="hidden",t.name=e.key,t.value=e.value,s.appendChild(t)})),document.body.appendChild(s);var u=window.open("",a,i);return s.target=a,s.submit(),document.body.removeChild(s),r&&r(),window.QSI.targetWindows.push(u),u}}}function r(e,t,n){try{window.QSI.targetWindows.push(document.getElementsByName(e)[0])}catch(e){window.QSI.dbg.e&&window.QSI.dbg.e(e)}var i=function(e,t,n){var i=document.createElement("form");return i.setAttribute("method","post"),i.setAttribute("action",e),i.setAttribute("accept-charset","utf-8"),i.setAttribute("target",t),n.forEach((function(e){var t=document.createElement("input");t.type="hidden",t.name=e.key,t.value=e.value,i.appendChild(t)})),i}(t,e,n);document.body.appendChild(i),i.submit(),document.body.removeChild(i)}n.r(t),n.d(t,"getOpenNewWindowOnclickHandler",(function(){return i})),n.d(t,"postToIframe",(function(){return r}))},function(e,t,n){n(5),n(30),n(6),n(7),n(8),n(9),n(10),n(11),n(12),n(13),n(2),n(14),n(15),n(16),n(17),n(18),n(19),n(20),n(21),n(22),n(23),n(24),n(25),n(26),n(27),n(28),n(32),n(29),n(31),e.exports=n(3)},function(module,exports){QSI.doEvalJS=function(js){return eval(js)}},function(e,t){QSI.windowHandler={optInIDsAndTargetOrigins:"QSI_OptInIDsAndTargetOrigins",optInIDsAndWindowNames:"QSI_OptInIDsAndWindowNames",getWin:function(e,t){if("string"!=typeof e)return null;var n=QSI.windows;if(n){var i=n[e];if(i)return i.closed?(QSI.windowHandler.removeWindowHandle(e),null):i}return t?(QSI.dbg.c("PopUnder window handler not found in QSI.windows"),window.open("",e)):null},getWindowOrigin:function(e){var t;try{if(null!=e&&!(t=e.origin)&&e.location)if(e.location.origin)t=e.location.origin;else{var n=e.location;t=n.protocol+"//"+n.hostname+(n.port?":"+n.port:"")}}catch(e){QSI.dbg.e(e)}return t},addOptInIDAndWindowName:function(e,t,n){var i=this.getOptInIDsAndWindowNames()||{},r=this.getOptInIDsAndTargetOrigins()||{};i[e]=t,i=JSON.stringify(i),r[e]=n,r=JSON.stringify(r),window.sessionStorage.setItem(this.optInIDsAndWindowNames,i),window.sessionStorage.setItem(this.optInIDsAndTargetOrigins,r)},getOptInIDsAndWindowNames:function(){var e;try{"string"==typeof(e=window.sessionStorage.getItem(this.optInIDsAndWindowNames)||{})&&(e=JSON.parse(e))}catch(t){QSI.dbg.e(t),e={}}return e},getOptInIDsAndTargetOrigins:function(){var e;try{"string"==typeof(e=window.sessionStorage.getItem(this.optInIDsAndTargetOrigins)||{})&&(e=JSON.parse(e))}catch(t){QSI.dbg.e(t),e={}}return e},removeOptInIDAndWindowName:function(e){var t=this.getOptInIDsAndWindowNames()||{},n=this.getOptInIDsAndTargetOrigins()||{};for(var i in t)t[i]===e&&(delete t[i],delete n[i]);window.sessionStorage.setItem(this.optInIDsAndWindowNames,JSON.stringify(t)),window.sessionStorage.setItem(this.optInIDsAndTargetOrigins,JSON.stringify(n))},setupWindowHandles:function(){var e=this.getOptInIDsAndWindowNames()||{};for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t)){var n=e[t],i=QSI.windowHandler.getWin(n,!0);i?QSI.windowHandler.addWindowHandle(n,i):QSI.windowHandler.removeOptInIDAndWindowName(n)}},addWindowHandle:function(e,t){var n=QSI.windows||{};n[e]=t,QSI.windows=n},removeWindowHandle:function(e){var t=QSI.windows;t&&delete t[e]},removeClosedWindowHandles:function(){var e=QSI.windows;if(e)for(var t in e)e[t].closed&&(delete e[t],QSI.windowHandler.removeOptInIDAndWindowName(t))}}},function(e,t){QSI.EmbeddedData&&!window.QTestLib||(QSI.EmbeddedData={getHTMLFromDOM:function(e){var t=QSI.util.$(e),n="";if(t)switch(t.tagName){case"TEXTAREA":case"INPUT":n=t.value;break;default:n=t.innerHTML}return n},getDataLayerVal:function(e){var t=window.QSI.InitializeDataLayerHelper().get(e);return t&&"object"==typeof t&&(t=JSON.stringify(t)),t||""},getCookieVal:function(e){var t="",n=QSI.cookie.get(e);return n&&(t=n),t},getURLParameter:function(e){return QSI.util.getQueryParam(window.location.href,e)},getURLRegexMatch:function(e){var t=e.match(/^\/(.*)\/([gim]*)/);return(t=t&&t[1]?window.location.href.match(new RegExp(t[1],t[2])):window.location.href.match(new RegExp(e)))&&t[1]?t[1]:""},getAdobeMarketingCloudIDJson:function(e){var t="";try{var n=Visitor.getInstance(e).getMarketingCloudVisitorID();t=JSON.stringify({marketingCloudVisitorID:n})}catch(e){QSI.dbg.e(e)}return t},getJavaScriptValue:function(e){var t="";try{if("object"==typeof e&&(e=e.toString()),QSI.runOptions&&QSI.runOptions.jsExpressionValues){var n=QSI.strToVal(e,QSI.runOptions.jsExpressionValues);if(n)return"object"==typeof n&&(n=JSON.stringify(n)),n}if(QSI.global.enableJSSanitization||void 0===QSI.doEvalJS)return QSI.config&&!0===QSI.config.enableSecureVariables?t=QSI.strToVal(e):QSI.dbg.e("Error: JavaScript Evaluation has been disabled. Attempted to execute: '"+e+"'"),t;t=QSI.doEvalJS(e)}catch(e){QSI.dbg.e(e)}return t},getHistory:function(){return QSI.history.get()},getTimeOnSite:function(){return QSI.history.getTimeOnSite()},getCurrentPage:function(){var e;try{e=decodeURI(this.getWindowLocation())}catch(t){e=this.getWindowLocation()}return e},getWindowLocation:function(){return window.location.href},getReferer:function(){return QSI.history.getPageReferrer()},getSiteReferer:function(){return QSI.history.getSiteReferrer()},getSearchTerm:function(){return QSI.history.getSearch()},getPageCount:function(){return QSI.history.getPageCount().unique},getTotalPageCount:function(){return QSI.history.getPageCount().total},getPercentagePageViewed:function(){return QSI.history.getPageCount().unique},getSiteInterceptID:function(){return this.siid},getCreativeID:function(){if(QSI.global.intercepts[this.siid]&&QSI.global.intercepts[this.siid].CreativeID)return QSI.global.intercepts[this.siid].CreativeID},getEventTrackingData:function(e){return QSI.EventTracker.get(e)},getSiteCatalystValue:function(e){for(var t,n=e.split("."),i=QSI.adobeVar.split("."),r=window,o=0;o<i.length;o++)r=r[i[o]];if(0===e.indexOf("event")&&r){var a=e+"=",s=r.events;if(s)for(var u=s.split(","),c=0;c<u.length;c++){var d=u[c],l=d.indexOf(a);if(~l)return d.substring(l+a.length,d.length)}}c=0;for(var p=n.length;c<p;c++){if(r)r=r[(t=n[c]).charAt(0).toLowerCase()+t.slice(1)]||r[n[c]]}return r||""},getUserAgent:function(){return navigator.userAgent},getBrowser:function(){return QSI.Browser.name},getBrowserVersion:function(){return QSI.Browser.version},getOperatingSystem:function(){return QSI.OS.name},getDeviceType:function(){return QSI.Browser.isMobile?"Mobile":"Desktop"},getEmbeddedData:function(e,t){t=t||[],QSI.ed[e]&&(t=t.concat(QSI.ed[e])),this.siid=e;const n=this.generateDynamicEmbeddedData(t);return window.QSI.SR&&window.QSI.SR.getId()&&(n.Q_SR_ID=window.QSI.SR.getId(),n.Q_SR_PlaybackUrl=window.QSI.SR.getPlaybackUrl()),n},getUrlWithEmbeddedData:function(e){var t=e.url,n=e.targetType,i=e.requestId,r=e.interceptId,o=e.ed,a=e.creativeType,s=e.usingPopUnderTarget;if(!t)return"";var u=[QSI.util.creativeTypes.POP_UNDER],c=QSI.util.usePostToStart(n)&&!QSI.util.arrIncludes(u,a)&&!s;const d=this.getEmbeddedData(r,o);var l=d.Q_TS_ID&&d.Q_TS_RS&&d.Q_EE_ANON?{Q_TS_ID:d.Q_TS_ID,Q_TS_RS:d.Q_TS_RS,Q_EE_ANON:d.Q_EE_ANON}:{},p=c?l:d;return this.mergeEmbeddedDataIntoURL(t,p,i,r,c)},getEmbeddedDataAsArray:function(e,t,n,i){var r=[],o=this.getEmbeddedData(e,n);for(var a in i&&(this.surveyOpenED[e]=o),o){n={key:a,value:o[a]};r.push(n)}var s=this.generateQTouchpoint(t,e);if(s){n={key:"Q_Touchpoint",value:s};r.push(n)}return r},mergeEmbeddedDataIntoURL:function(e,t,n,i,r){var o,a;if(-1!==e.indexOf("?")){var s=e.split("?");o=s[0],a=this.getQueryParamsObjectFromString(s[1])}else o=e,a={};for(var u in t)a[u]=t[u];if(!r){var c=this.generateQTouchpoint(n,i);c&&(a.Q_Touchpoint=c)}var d=QSI.util.buildQueryString(a);return d?o+"?"+d:o},generateQTouchpoint:function(e,t){var n;if(e&&window.QSI.Request[e]&&t&&window.QSI.Request[e].Intercepts[t]){var i=window.QSI.Request[e].Intercepts[t].Targeting;i.ContactID&&i.DistributionID&&i.DirectoryID&&(n=i.DistributionID+"_"+i.ContactID+"_"+i.DirectoryID)}return n},getQueryParamsObjectFromString:function(e){var t={};if(!e)return t;for(var n=e.split("&"),i=0;i<n.length;i++){var r=n[i].split("=");t[r[0]]=r[1]}return t},getEmbeddedDataAsQueryStringParam:function(e,t){var n=this.getEmbeddedData(e,t);return 0===Object.keys(n).length?"":QSI.util.buildQueryString(n)},generateDynamicEmbeddedData:function(e){QSI.dbg.log("SI-4479 generateDynamicEmbeddedData start of function.  QSI.ed: "+JSON.stringify(QSI.ed)+"  ed: "+JSON.stringify(e));var t={};"string"==typeof e&&(e=e.split(""));for(var n=0,i=e.length;n<i;n++)try{var r=e[n];if(r.type&&r.name){var o="";switch(r.type){case"StaticVal":o=r.value;break;case"HTML":o=this.getHTMLFromDOM(r.value);break;case"Cookie":o=this.getCookieVal(r.value);break;case"QueryParam":o=this.getURLParameter(r.value);break;case"URLRegex":o=this.getURLRegexMatch(r.value);break;case"JavaScriptVal":o=this.getJavaScriptValue(r.value);break;case"SiteCatalyst":o=this.getSiteCatalystValue(r.value);break;case"EventTracking":o=this.getEventTrackingData(r.value);break;case"AdobeOrgID":o=this.getAdobeMarketingCloudIDJson(r.value);break;case"GoogleDataLayer":o=this.getDataLayerVal(r.value);break;default:this["get"+r.type]?o=this["get"+r.type](r.value):0===r.value.indexOf("JSON.stringify")&&-1!==r.value.indexOf("marketingCloudVisitorID")?(QSI.dbg.log("SI-4479 generateDynamicEmbeddedData Attempting default case getJavaScriptValue fix. QSI.ed: "+JSON.stringify(QSI.ed)+"  set: "+JSON.stringify(r)),o=this.getJavaScriptValue(r.value)):(QSI.dbg.log("SI-4479 generateDynamicEmbeddedData Final else case in default QSI.ed: "+JSON.stringify(QSI.ed)+"  set: "+JSON.stringify(r)),o=r.value)}"XMDContactED"!==r.type&&(o=String(o),t[r.name]=o)}}catch(e){QSI.dbg.e(e)}return t},surveyOpenED:{}})},function(e,t,n){"use strict";n.r(t),n.d(t,"LoadingAnimationElement",(function(){return i}));var i=function(){function e(){this.requestAnimationInstance=null,this.animationDiv=null,this.animationBuilt=!1,this.animationStartTimestamp=null,this.animationRotation=0}return e.prototype.buildAnimation=function(e,t,n){var i=e/5*2;this.animationDiv=window.QSI.util.build("div",{style:{width:e+"px",height:e+"px",zIndex:(window.QSI.global.currentZIndex++).toString(),position:"absolute",top:t+"px",left:n+"px"}},[]);for(var r=0;r<8;r++){var o="rotate("+45*r+"deg) translate(0, -"+i+"px)",a=window.QSI.util.build("div",{style:{position:"absolute",top:e/2-e/6+"px",left:e/2-e/20+"px",width:e/10+"px",height:e/3+"px",background:"#000",transform:o,"-ms-transform":o,"-moz-transform":o,"-webkit-transform":o,opacity:(.12*(r+1)).toString()}},[]);this.animationDiv.appendChild(a)}return this.animationBuilt=!0,this.animationDiv},e.prototype.startAnimation=function(e){var t=this,n=window.QSI.dbg;if(this.animationBuilt){var i=function(r){try{null===t.animationStartTimestamp&&(t.animationStartTimestamp=r);var o="rotate("+t.animationRotation+"deg)";window.QSI.util.setStyle(t.animationDiv,{transform:o,"-ms-transform":o,"-moz-transform":o,"-webkit-transform":o});var a=r-t.animationStartTimestamp;t.animationRotation=Math.floor(a/e)%8*45}catch(e){n.e(e)}t.requestAnimationInstance=window.requestAnimationFrame(i)};this.requestAnimationInstance=window.requestAnimationFrame(i)}},e.prototype.stopAnimation=function(){window.cancelAnimationFrame(this.requestAnimationInstance),window.QSI.util.remove(this.animationDiv)},e}();window.QSI.LoadingAnimationElement=i},function(e,t){QSI.PipedText={locators:[],setLocators:function(e){this.locators=[];for(var t=0,n=e.length;t<n;t++){var i=e[t],r={locator:i[0],prefix:i[1],type:i[2],expression:i[3]};this.locators.push(r)}},evaluateLocators:function(e,t){if(0===this.locators.length)return"";for(var n=0,i=this.locators.length;n<i;n++){var r=this.locators[n],o=r.type,a=new RegExp("\\"+this.escapeRegex(r.locator));if("SI"===r.prefix)switch(o){case"J":var s=document.createElement("textarea");s.innerHTML=r.expression;var u=s.value;e=e.replace(a,QSI.EmbeddedData.getJavaScriptValue(u));break;case"H":e=e.replace(a,QSI.EmbeddedData.getHTMLFromDOM(r.expression));break;case"U":e=e.replace(a,QSI.EmbeddedData.getURLParameter(r.expression));break;case"C":e=e.replace(a,QSI.EmbeddedData.getCookieVal(r.expression));break;case"W":t&&(e=e.replace(a,QSI.util.buildWidget(r.expression,t)))}else e=e.replace(a,"")}return e},escapeRegex:function(e){var t=new RegExp("[.*+?|()\\[\\]{}\\\\]","g");return e.replace(t,"\\$&")}}},function(e,t){QSI.BuildElementModule={buildElement:function(e){var t;if(e.unitsOfMeasurement||(e.unitsOfMeasurement={},e.unitsOfMeasurement.width="px",e.unitsOfMeasurement.height="px"),!e.type||!this["build"+e.type+"Element"])return null;(t=this["build"+e.type+"Element"](e)).tabIndex="0",QSI.util.observe(t,"keydown",QSI.util.clickOnKeyDown),e&&e.events&&QSI.util.processElementEvents(e.events,t,this);var n=QSI.util.createArrayFromIterable(t.getElementsByTagName("img"));if(!1===this.shouldPreloadImages)for(var i=0,r=n.length;i<r;i++){var o=n[i];o.setAttribute("data-src",o.src),o.src=""}if(e.addQClickListener){var a=this;t.onclick=function(){a.click()}}var s=QSI.util.createArrayFromIterable(t.getElementsByTagName("iframe")),u=n.concat(s);return t.loadingDeferred=this.combineElementLoadEvents(u),t},combineElementLoadEvents:function(e){var t=QSI.util.Deferred(),n=e.length;function i(e){try{var i=e.target||e.srcElement,r=!1;this.options&&(r=QSI.util.usePostToStart(this.options.targetURLType)&&this.options.type!==QSI.util.creativeTypes.POP_UNDER&&"IFRAME"===i.tagName),(i.getAttribute("src")||!i.getAttribute("data-src")||r)&&--n<=0&&t.resolve()}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}}for(var r=0,o=e.length;r<o;r++){var a=e[r];!a.complete||a.notComplete?QSI.util.observe(a,"load",i.bind(this)):n--}return 0===n&&t.resolve(),t.promise()},buildGenericElement:function(e){return this.buildBaseElement(e)},buildPopOverElement:function(e){return this.buildBaseElement(e)},buildTextElement:function(e){return this.buildBaseElement(e)},buildImageElement:function(e){return this.buildBaseElement(e)},buildTargetElement:function(e){var t=this.buildBaseElement(e);return this.addTargetElementFunctionality(t,e),t},addTargetElementFunctionality:function(e,t){e.style.cursor="pointer",e.setAttribute("data-type","target");var n=this;this.addEmbeddedDataToGlobalInstance(t),new QSI.Target(e,this.getTargetHelper(t&&t.embeddedData||[]),this.actionOptions,this).complete().then((function(){n.closeOnTargetClick?n.closeOnTargetClick():n.close()}))},addEmbeddedDataToGlobalInstance:function(e){if(QSI&&QSI.ed&&e&&e.embeddedData)for(var t=0;t<e.embeddedData.length;t++)QSI.ed[this.id].push(e.embeddedData[t])},buildSpanElement:function(e){this.position=e.positionAnchors.positionY;var t=this.buildBaseElement(e);return t.style.width="100%",t},buildTargetSpanElement:function(e){this.position=e.positionAnchors.positionY;var t=this.buildBaseElement(e);t.style.width="100%",t.style.cursor="pointer",this.addEmbeddedDataToGlobalInstance(e);var n=this;return new QSI.Target(t,this.getTargetHelper(e.embeddedData||[]),this.actionOptions).complete().then((function(){n.closeOnTargetClick?n.closeOnTargetClick():n.close()})),t},buildEmbeddedTargetElement:function(e){this.hasIframe=!0;var t=e.embeddedData||[],n=this.getTarget(t);this.options&&"Survey"===this.options.targetURLType&&(n=QSI.util.addScreenCaptureParameterToTarget(n));var i=e.style.width,r=e.style.height;"%"===e.unitsOfMeasurement.width&&(i=100),"%"===e.unitsOfMeasurement.height&&(r=100);var o={"data-src":n,width:"100%",height:"100%",style:{},frameBorder:0,name:"survey-iframe-"+this.id};window.QSI.global.featureFlags["DX.StatsAccuracy"]&&(this.dataName="embedded-iframe-"+this.id,o["data-name"]=this.dataName),e.accessibilityTitle&&(o.title=e.accessibilityTitle),QSI.global.isHostedJS()&&!QSI.util.isIE(9)&&(o.sandbox="allow-scripts allow-same-origin allow-popups");var a=QSI.util.build("iframe",o);this.getEmbeddedTargets().push(a);var s=QSI.util.build("div",{className:"scrollable",style:{width:i+e.unitsOfMeasurement.width,height:r+e.unitsOfMeasurement.height,overflow:"none"}},[a]);return QSI.Browser.isMobile&&QSI.util.setStyle(s,{overflow:"auto"}),this.displayed.then((function(){QSI.util.setStyle(s,{webkitTransform:"translateZ(0)"})})),e.content=s,this.buildBaseElement(e)},buildCloseButtonElement:function(e){var t=this.buildBaseElement(e);return this.fixTransparentWithOpacity(t),this.addCloseButtonElementFunctionality(t),t},addCloseButtonElementFunctionality:function(e){e.style.cursor="pointer";var t=this;e.onclick=function(){t.onCloseClick()}},fixTransparentWithOpacity:function(e){var t,n,i;if(QSI.util.isIE(7)||QSI.util.isIE(8))for(i=(t=e.getElementsByTagName("img")).length,n=0;n<i;n++)t[n].style.filter="progid:DXImageTransform.Microsoft.gradient(startColorstr=#00FFFFFF, endColorstr=#00FFFFFF);"},buildBaseElement:function(e){var t,n,i=e.style,r=e.unitsOfMeasurement,o=e.position,a={style:this.getElementStyle(i,o,r)},s=this.getYPosition(o),u=this.getXPosition(o);return this.setPositionStyles(a,e),e.style.backgroundImage&&(a.style.backgroundImage="url("+e.style.backgroundImage+")"),100!==e.style.opacity&&(a.style.opacity=e.style.opacity/100,a.style.filter="alpha(opacity = "+e.style.opacity+")"),t=QSI.util.build("div",a,[this.getElementContents(e)]),e.dropShadow&&this.appendDropShadow(e,t),n=QSI.util.getDimensions(t),t.bc={x:u+n.width,y:s+n.height},t},getElementContents:function(e){var t=e.style,n=e.unitsOfMeasurement,i=this.getContentDims(t.width,t.height,n),r=QSI.util.build("div",{style:{position:"absolute",top:0,left:0,width:i.width,height:i.height,overflow:"hidden",display:t.display}});if(e.content)if("string"==typeof e.content){var o=e.content;QSI.global.enableJSSanitization&&void 0!==QSI.DOMPurify&&(o=QSI.DOMPurify.sanitize(o)),r.innerHTML=o}else"object"==typeof e.content&&r.appendChild(e.content);return r},getContentDims:function(e,t,n){var i=e,r=t;return"%"===n.width&&(i=100),"%"===n.height&&(r=100),{width:i+n.width,height:r+n.height}},appendDropShadow:function(e,t){var n=e.style,i=n.borderWidth,r=e.unitsOfMeasurement;isNaN(i)&&(i=0);var o=this.convertPercentStylesToPixels(n,r),a=Math.floor(1*o.width+2*i),s=Math.floor(1*o.height+2*i);t.insertBefore(this.buildDropShadow(a,s,i),t.childNodes[0])},buildDropShadow:function(e,t,n){var i,r,o=Math.ceil(-.1*t)-n,a=Math.ceil(-.1*e)-n;return r={width:(i=Math.floor(1.2*e))+"px",maxWidth:i+"px",height:Math.floor(1.2*t)+"px",top:o+"px",left:a+"px",position:"absolute"},QSI.util.build("img",{src:QSI.global.imagePath+"/siteintercept/popup_shadow_transparent.png",style:r,alt:""})},onCloseClick:function(){this.close()},buildIFrame:function(e,t){var n={style:{border:"none",position:"absolute",top:0,left:0,filter:"progid:DXImageTransform.Microsoft.Alpha(style=0,opacity=0)"},width:e+"px",height:t+"px",frameBorder:"0",src:QSI.global.imagePath+"/blank.html"};return QSI.global.isHostedJS()&&!QSI.util.isIE(9)&&(n.sandbox="allow-scripts allow-same-origin allow-popups"),QSI.util.build("iframe",n)},setPositionStyles:function(e,t){t.positionAnchors||(t.positionAnchors={positionX:"left",positionY:"top"}),this.shouldAnchor&&("left"===t.positionAnchors.positionX?e.style.left=t.position.left+"px":"right"===t.positionAnchors.positionX?e.style.right=t.position.right+"px":"center"===t.positionAnchors.positionX&&this.centerHorizontally(e,t),"top"===t.positionAnchors.positionY?e.style.top=t.position.top+"px":"bottom"===t.positionAnchors.positionY?e.style.bottom=t.position.bottom+"px":"center"===t.positionAnchors.positionY&&this.centerVertically(e,t))},centerHorizontally:function(e,t){this.centerDim(e,t.style.width,"width","left",t.unitsOfMeasurement)},centerVertically:function(e,t){this.centerDim(e,t.style.height,"height","top",t.unitsOfMeasurement)},centerDim:function(e,t,n,i,r){var o=QSI.util.getPageSize(),a=i.charAt(0).toUpperCase()+i.slice(1);"%"===r[n]&&(t=QSI.util.convertPercentToPixel(t,o[n])),e.style[i]="50%",e.style["margin"+a]="-"+Math.ceil(t/2)+"px"},convertPercentStylesToPixels:function(e,t){(e=JSON.parse(JSON.stringify(e))).width=parseInt(e.width,10),e.height=parseInt(e.height,10);var n=QSI.util.getPageSize();return"%"===t.width&&e.width&&(e.width=QSI.util.convertPercentToPixel(e.width,n.width)),"%"===t.height&&e.height&&(e.height=QSI.util.convertPercentToPixel(e.height,n.height)),e},getYPosition:function(e){return 1*e.top},getXPosition:function(e){return 1*e.left},getElementStyle:function(e,t,n){return{position:"absolute",zIndex:e.zIndex,width:e.width+n.width,height:e.height+n.height,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth+"px",borderColor:e.borderColor,borderStyle:"solid",borderRadius:e.borderRadius+"px",display:e.display}},initializeIframes:function(){var e,t,n,i,r=this.getEmbeddedTargets();for(t=r.length,e=0;e<t;e++){if(n=r[e],window.QSI.global.featureFlags["DX.StatsAccuracy"]&&n.getAttribute("data-name")){if("Slider"!==this.options.type||!this.options.displayOptions.hasVisibleControl){QSI.util.detectClickInIframeListener(this)}}else{var o=this;n.onload=function(){o.click()}}if(i=n.getAttribute("data-src")){var a=!1;if(this.options&&(a=QSI.util.usePostToStart(this.options.targetURLType)&&this.options.type!==QSI.util.creativeTypes.POP_UNDER),a){var s=QSI.EmbeddedData.getEmbeddedDataAsArray(this.id,this.requestId);QSI.WindowUtils.postToIframe("survey-iframe-"+this.id,i,s)}else n.src=i}}},getEmbeddedTargets:function(){return this.embeddedTargets||(this.embeddedTargets=[]),this.embeddedTargets}}},function(e,t){QSI.BuildResponsiveElementModule={HTML_ELEMENT_TYPES:{CONTAINER:"div",HEADLINE:"h1",TEXT:"div",BUTTON:"button",IMAGE:"img"},PARENT_CONTAINER_CLASS:"QSIWebResponsive",SHADOWBOX_CLASS:"QSIWebResponsiveShadowBox",ACTION_TYPES:{TARGET:"open-target",DISMISS:"dismiss-intercept"},buildParentContainer:function(e){return this.buildHTMLElement({elementType:this.HTML_ELEMENT_TYPES.CONTAINER,content:e,className:this.PARENT_CONTAINER_CLASS})},buildHTMLElement:function(e){var t=e||{};t.content&&!Array.isArray(t.content)&&(t.content=[t.content]),t.className||(t.className="");var n=QSI.util.build(t.elementType,{className:t.className,style:t.elementStyle},t.content);return t.tabbable&&(n.tabIndex=0),t.id&&(n.id=t.id),t.src&&(n.src=t.src),t.altText&&(n.alt=t.altText),t.ariaLabel&&n.setAttribute("aria-label",t.ariaLabel),n},buildShadowBox:function(){return this.buildHTMLElement({elementType:this.HTML_ELEMENT_TYPES.CONTAINER,className:this.SHADOWBOX_CLASS,elementStyle:{position:"fixed",backgroundColor:"#000000",left:"0",top:"0",width:"100%",height:"100%",opacity:"0",zIndex:QSI.global.currentZIndex}})},addButtonFunctionality:function(e,t,n){e===this.ACTION_TYPES.TARGET?this.addTargetElementFunctionality(t,n):e===this.ACTION_TYPES.DISMISS&&this.addCloseButtonElementFunctionality(t,n)},addTargetElementFunctionality:function(e,t){e.style.cursor="pointer",e.setAttribute("data-type","target"),new QSI.Target(e,this.getTargetHelper(t),t.actionOptions,t).complete().then((function(){if(t.closeOnTargetClick)t.closeOnTargetClick();else{var e=!1;t.actionOptions.targetEmbedded&&(e=!0),t.close(e)}}))},getTargetHelper:function(e){var t=this;return function(){return t.getTargetURL(e)}},getTargetURL:function(e){var t=e.options.targetURL,n=e.options.targetURLType,i=e.options.actionOptions.targetPopUnder;return QSI.EmbeddedData.getUrlWithEmbeddedData({url:t,targetType:n,requestId:e.options.requestId,interceptId:e.id,usingPopUnderTarget:i})},addCloseButtonElementFunctionality:function(e,t){e.style.cursor="pointer",e.onclick=function(){t.close()}},appendStylesToDocument:function(e,t){const n=QSI.util.build("style",{type:"text/css"},e);return document.body.appendChild(n),t&&document.body.removeChild(t),n}}},function(e,t){QSI.BuildResponsiveEmbeddedTarget=QSI.util.Class({initialize:function(e,t){this.url=e,this.targetURLType=t,"Survey"===t&&(this.url=QSI.util.addScreenCaptureParameterToTarget(e))},build:function(e,t){if(!this.url)return null;e=e||{};var n=this.buildIframe(e.iframeOptions),i=this.buildContainer(e.containerOptions,[n]),r=document.createElement("div");r.id="QSIEmbeddedTarget-invisible-div",Object.assign(r.style,{width:"1px",height:"0px",padding:0,overflow:"hidden",position:"fixed",top:"1px",left:"1px"}),e.inCreative?this.closeButton=t.querySelectorAll("button")[0]:this.closeButton=t;var o=this;return r.onfocus=function(){o.closeButton.focus()},r.setAttribute("tabindex",0),r.setAttribute("aria-hidden",!0),i.appendChild(r),QSI.reg[e.iframeOptions.interceptId]&&(QSI.reg[e.iframeOptions.interceptId].embeddedWindows||(QSI.reg[e.iframeOptions.interceptId].embeddedWindows=[]),QSI.reg[e.iframeOptions.interceptId].embeddedWindows.push({targetIframe:n,container:i})),i},buildContainer:function(e,t){var n=(e=e||{}).className||"",i={position:"relative",overflow:"hidden",margin:"auto"};return e.style&&(i=this.concatProperties(e.style,i)),QSI.util.build("div",{className:n,style:i},t)},buildIframe:function(e){var t=(e=e||{}).className||"",n={width:"100%",height:"100%",border:"none",position:"absolute",top:"0",left:"0"};e.style&&(n=this.concatProperties(e.style,n));var i=QSI.util.build("iframe",{className:t,style:n});return i.setAttribute("data-interceptid",e.interceptId),i.setAttribute("name","survey-iframe-"+e.interceptId),e.dataName&&window.QSI.global.featureFlags["DX.StatsAccuracy"]&&i.setAttribute("data-name",e.dataName),QSI.util.usePostToStart(this.targetURLType)||(i.src=this.url),i.tabIndex=0,e.accessibilityTitle&&(i.title=e.accessibilityTitle),i},concatProperties:function(e,t){for(var n=Object.keys(e),i=0;i<n.length;i++)t[n[i]]=e[n[i]];return t}})},function(e,t){QSI.Animation&&!window.QTest||(QSI.Animation={supportedProperties:{top:"px",left:"px",right:"px",bottom:"px",width:"px",height:"px",opacity:0},tweens:[],animateStyle:function(e,t,n,i){n=0===n?1:n,this.setUpAnimationFrames();var r=this.getProperties(t,e);return this.startAnimating(r,n||1500,e,i||"easeto")},getProperties:function(e,t){var n={},i=this;return e.from&&(n=e.from,e=e.to),QSI.util.forOwn(e,(function(r,o){o in i.supportedProperties&&(e[o]=e[o],void 0!==n[o]?t.style[o]=n[o]+i.supportedProperties[o]:n[o]=parseFloat(QSI.util.removePx(t.style[o])))})),{start:n,end:e}},startAnimating:function(e,t,n,i){var r=this,o=QSI.util.Deferred(),a=new this.Tween(e.start).to(e.end,t).onUpdate((function(){var e=this;QSI.util.forOwn(this,(function(t,i){r.setElementStyle(n,i,e[i])}))})).onComplete((function(){o.resolve()}));return i&&(i=this.getTransitionFunction(i),a.transition(i)),a.start(),this.animating(),o.promise()},getTransitionFunction:function(e){return QSI.Animation.Transitions[e]?QSI.Animation.Transitions[e]:e},setElementStyle:function(e,t,n){e.style[t]=n+this.supportedProperties[t],"opacity"===t&&(e.style.filter="alpha(opacity = "+100*n+")")},animating:function(){try{QSI.Animation.tweens.length>0&&(QSI.Animation.update(),window.qsiRequestAnimationFrame(QSI.Animation.animating))}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}},setUpAnimationFrames:function(){if(!window.qsiRequestAnimationFrame){for(var e=["ms","moz","webkit","o"],t=0;t<e.length&&!window.qsiRequestAnimationFrame;++t)window.qsiRequestAnimationFrame=window[e[t]+"RequestAnimationFrame"];window.qsiRequestAnimationFrame||(window.qsiRequestAnimationFrame=this.animationFrameHelper())}},animationFrameHelper:function(){var e=0;return function(t){var n=QSI.util.getDateNow(),i=Math.max(0,20-(n-e)),r=window.setTimeout((function(){try{t(n+i)}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}}),i);return e=n+i,r}},getAll:function(){return this.tweens},removeAll:function(){this.tweens=[]},addTween:function(e){this.tweens.push(e)},update:function(e){var t=0,n=this.tweens.length;for(e=void 0!==e?e:QSI.util.getDateNow();t<n;)this.tweens[t].update(e)?t++:(this.tweens.splice(t,1),n--)},Tween:function(e){var t=e,n={},i={},r=1e3,o=0,a=null,s=QSI.Animation.Transitions.easeto,u=null,c=null;this.to=function(e,t){return null!==t&&(r=t),i=e,this},this.start=function(e){return QSI.Animation.addTween(this),Date&&(a=void 0!==e?e:QSI.util.getDateNow()),a+=o,QSI.util.forOwn(i,(function(e,r){if(null!==t[r]){if(i[r]instanceof Array){if(0===i[r].length)return;i[r]=[t[r]].concat(i[r])}n[r]=t[r]}})),this},this.delay=function(e){return o=e,this},this.transition=function(e){return s=e,this},this.onUpdate=function(e){return u=e,this},this.onComplete=function(e){return c=e,this},this.update=function(e){if(e<a)return!0;var o=(e-a)/r,d=s(o=o>1?1:o);for(var l in n)if(Object.prototype.hasOwnProperty.call(n,l)){var p=n[l],f=i[l];t[l]=p+(f-p)*d}return null!==u&&u.call(t,d),1!==o||(null!==c&&c.call(t),!1)}}},QSI.Animation.Transitions={staticly:function(){return 1},linear:function(e){return e},inquad:function(e){return e*e},easeto:function(e){return(e/=.5)<1?.5*Math.pow(e,4):-.5*((e-=2)*Math.pow(e,3)-2)},sinoidal:function(e){return-Math.cos(e*Math.PI)/2+.5},reverse:function(e){return 1-e},bounce:function(e){return e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375},elastic:function(e){return-1*Math.pow(4,-8*e)*Math.sin((6*e-1)*(2*Math.PI)/2)+1},swingFromTo:function(e){var t=1.70158;return(e/=.5)<1?e*e*((1+(t*=1.525))*e-t)*.5:.5*((e-=2)*e*((1+(t*=1.525))*e+t)+2)},swingFrom:function(e){var t=1.70158;return e*e*((t+1)*e-t)},swingTo:function(e){var t=1.70158;return(e-=1)*e*((t+1)*e+t)+1},bouncePast:function(e){return e<1/2.75?7.5625*e*e:e<2/2.75?2-(7.5625*(e-=1.5/2.75)*e+.75):e<2.5/2.75?2-(7.5625*(e-=2.25/2.75)*e+.9375):2-(7.5625*(e-=2.625/2.75)*e+.984375)},easeFromTo:function(e){return(e/=.5)<1?.5*Math.pow(e,4):-.5*((e-=2)*Math.pow(e,3)-2)},easeFrom:function(e){return Math.pow(e,4)}})},function(e,t,n){"use strict";n.r(t);var i=n(2);window.QSI.localStorage=i},function(e,t,n){"use strict";n.r(t),n.d(t,"ResolveTimeZone",(function(){return i}));var i=function(){this.prepare=function(){return"&Q_TZ="+-(new Date).getTimezoneOffset()/60}};window.QSI.ResolveTimeZone=new i},function(e,t,n){"use strict";n.r(t),n.d(t,"ResolveTimeOnSite",(function(){return i}));var i=function(){this.prepare=function(){return"&Q_TOS="+window.QSI.history.getTimeOnSite()}};window.QSI.ResolveTimeOnSite=new i},function(e,t,n){"use strict";n.r(t),n.d(t,"ResolveSiteCatalyst",(function(){return r}));var i=n(0),r=function(){var e=this;this.prepare=function(t){var n=[];return t&&(n=(Array.isArray(t)?t:[t]).map((function(t){return t+"*|*"+e.getValue(t)}))),"&Q_ASC="+encodeURIComponent(n.join("*,*"))},this.getValue=function(t){e.adobeVar=e.rootName;var n=window;return e.adobeVar.split(".").forEach((function(e){if(!n[e])return"";n=n[e]})),t.split(".").forEach((function(e){n&&(n=n[e])})),n?Object(i.isObjLiteral)(n)?JSON.stringify(n):n:""}};window.QSI.ResolveSiteCatalyst=new r},function(e,t,n){"use strict";n.r(t),n.d(t,"ResolveSearchTerm",(function(){return i}));var i=function(){this.prepare=function(){var e=window.QSI.history.getSearch();return"&Q_ST="+encodeURIComponent(e)}};window.QSI.ResolveSearchTerm=new i},function(e,t,n){"use strict";n.r(t),n.d(t,"ResolveResolution",(function(){return i}));var i=function(){this.prepare=function(){var e=window.QSI.util.getPageSize(),t="";return t+="&Q_VPDIMS="+e.width+"|"+e.height,t+="&Q_SDIMS="+screen.width+"|"+screen.height}};window.QSI.ResolveResolution=new i},function(e,t,n){"use strict";n.r(t),n.d(t,"ResolveReferrer",(function(){return i}));var i=function(){this.prepare=function(){var e=window.QSI.history;return"&Q_REFER="+encodeURIComponent(e.getPageReferrer())+"&Q_SREFER="+encodeURIComponent(e.getSiteReferrer())}};window.QSI.ResolveReferrer=new i},function(e,t,n){"use strict";n.r(t),n.d(t,"ResolvePageCount",(function(){return i}));var i=function(){this.prepare=function(){var e="&Q_RPC=",t=window.QSI.history.getPageCount();return e+=t.total+"|"+t.unique}};window.QSI.ResolvePageCount=new i},function(e,t,n){"use strict";n.r(t),n.d(t,"ResolveJavaScript",(function(){return r}));var i=n(0),r=function(){this.prepare=function(e){var t=[];if(e){var n=window.QSI.EmbeddedData;t=Object.keys(e).map((function(t){var r=n.getJavaScriptValue(e[t]);return Object(i.isObjLiteral)(r)&&(r=JSON.stringify(r)),t+"*:*"+r}))}return"&Q_RJS="+encodeURIComponent(t.join("*|*"))}};window.QSI.ResolveJavaScript=new r},function(e,t,n){"use strict";n.r(t),n.d(t,"ResolveIntercept",(function(){return i}));var i=function(){this.prepare=function(){var e=null;try{e=JSON.parse(window.localStorage.getItem("Q_INTER"))}catch(e){return"&Q_INTER="}var t=[];return null!==e&&(t=Object.keys(e).map((function(t){return t+"*:*"+e[t]}))),"&Q_INTER="+escape(t.join("*|*"))}};window.QSI.ResolveIntercept=new i},function(e,t,n){"use strict";n.r(t),n.d(t,"ResolveHTML",(function(){return i}));var i=function(){this.prepare=function(e){var t=[];if(e){var n=window.QSI.EmbeddedData;t=e.map((function(e){return e+"*:*"+escape(n.getHTMLFromDOM(e))}))}return"&Q_RHTML="+escape(t.join("*|*"))}};window.QSI.ResolveHTML=new i},function(e,t,n){"use strict";n.r(t),n.d(t,"ResolveHistory",(function(){return i}));var i=function(){this.prepare=function(){var e=window.QSI.history;return"&Q_HIST="+escape(e.get())}};window.QSI.ResolveHistory=new i},function(e,t,n){"use strict";n.r(t),n.d(t,"ResolveEventTracking",(function(){return i}));var i=function(){this.prepare=function(e){var t=[];if(e){var n=window.QSI.EventTracker;t=e.map((function(e){return e+"*:*"+escape(n.get(e).toString())}))}return"&Q_RET="+escape(t.join("*|*"))}};window.QSI.ResolveEventTracking=new i},function(e,t,n){"use strict";n.r(t),n.d(t,"ResolveCookie",(function(){return i}));var i=function(){this.prepare=function(e){var t=[];if(e){var n=window.QSI.cookie;t=e.map((function(e){return e+"*:*"+(n.get(e)||"")}))}return"&Q_COOK="+escape(t.join("*|*"))}};window.QSI.ResolveCookie=new i},function(e,t,n){"use strict";n.r(t),n.d(t,"ResolveActionSet",(function(){return i}));var i=function(){this.prepare=function(e){var t=[];if(e){var n=window.QSI.history;t=Object.keys(e).map((function(t){var i=n.getActionSetHistory(t,!e[t]);return t+"|"+e[t]+"*:*"+escape(i)}))}return"&Q_RAS="+escape(t.join("*|*"))}};window.QSI.ResolveActionSet=new i},function(e,t){QSI.strToVal=function(e,t){try{for(var n,i=e.split(/[\'"\[\]]/).filter((function(e){return""!=e})),r=i[0].split("."),o=i.slice(1,i.length),a=r.concat(o),s=0;s<a.length;s++)if(0===a[s].indexOf(".")){var u=a[s].split(".");u=u.filter((function(e){return""!=e})),a.splice(s,1,u[0]),s++;for(var c=1;c<u.length;c++)a.splice(s,0,u[c]),s++}return null!=t?n=t:(n=window,QSI.config&&QSI.config.variableRoot&&(n=QSI.config.variableRoot)),a.forEach((function(e){n=n[e]})),n}catch(e){return void QSI.dbg.e(e)}}},function(e,t,n){"use strict";n.r(t);var i={createArrayFromIterable:function(e){for(var t=e.length||0,n=new Array(t);t--;)n[t]=e[t];return n},sendHttpRequest:function(e){var t=new XMLHttpRequest;for(var n in t.open(e.type,e.url,!0),e.timeout&&(t.timeout=e.timeout,e.timeoutCallback&&(t.ontimeout=function(){try{e.timeoutCallback.apply(this,arguments)}catch(e){void 0!==window.QSI&&window.QSI.dbg&&window.QSI.dbg.e&&window.QSI.dbg.e(e)}})),e.header)Object.prototype.hasOwnProperty.call(e.header,n)&&t.setRequestHeader(n,e.header[n]);e.includeCookies&&(t.withCredentials=!0,t.setRequestHeader("Access-Control-Allow-Credentials","true")),t.onreadystatechange=function(){try{if(4===t.readyState)if(200===t.status){if(!e.successCallback)return;e.successCallback(t)}else if(e.errorCallback)return void e.errorCallback(t)}catch(e){void 0!==window.QSI&&window.QSI.dbg&&window.QSI.dbg.e&&window.QSI.dbg.e(e)}},t.send(e.data)},shouldPreventRepeatedDisplay:function(e,t){return!(!t||!t.noshow||0==t.noshow||null===window.QSI.cookie.get("QSI_"+e+"_intercept"))},Creative:function(e){var t=window.QSI.util.Class.apply(this,arguments);return t.prototype.globalInitialize=function(e){var t;if(this.displayed=window.QSI.util.Deferred(),this.willShow=window.QSI.util.Deferred(),this.cookiesEnabled=window.QSI.util.Deferred(),this.preventRepeatedDisplay=window.QSI.util.Deferred(),this.localStorageEnabled=window.QSI.util.Deferred(),this.options=e||{},this.requestId=this.options.requestId,this.id=this.options.id,this.type=this.options.type,this.displayOptions=this.options.displayOptions||{},this.displayOptions.displayed=this.displayed.promise(),this.interceptDisplayOptions=this.options.interceptDisplayOptions||{},this.actionOptions=this.options.actionOptions||{},this.actionOptions.actionSetJavaScriptBeforeDisplay&&this.shouldShow()&&(t=this.actionOptions.actionSetJavaScriptBeforeDisplay,this.evalJS(t)),this.actionOptions.actionSetJavaScript){var n=this;t=this.actionOptions.actionSetJavaScript,this.displayed.done((function(){n.evalJS(t)}))}this.actionOptions.actionSetEvents&&window.QSI.util.processElementEvents(this.actionOptions.actionSetEvents,null,this),this.getType()!==window.QSI.util.creativeTypes.POP_UNDER&&this.killPopUnder()},t.prototype.evalJS=function(e){try{window.QSI.global.enableJSSanitization||void 0===window.QSI.doEvalJS||0==window.QSI.config.customJavaScriptPermission?window.QSI.dbg.e("Error: JavaScript Evaluation has been disabled. Attempted to execute: '"+e+"'"):window.QSI.doEvalJS(e)}catch(e){window.QSI.dbg.c("Error During Eval JavaScript "+e)}},t.prototype.getType=function(){return this.type},t.prototype.getTarget=function(e){var t=this.options.targetURL,n=this.options.targetURLType,i=this.type,r=this.options.actionOptions.targetPopUnder;return window.QSI.EmbeddedData.getUrlWithEmbeddedData({url:t,targetType:n,requestId:this.requestId,interceptId:this.id,ed:e,creativeType:i,usingPopUnderTarget:r})},t.prototype.getTargetHelper=function(e){var t=this;return function(){return t.getTarget(e)}},t.prototype.resetStyles=function(){if(this.options.resetStyle){var e=window.QSI.util.build("style",{type:"text/css"});if(e.styleSheet){document.getElementsByTagName("head")[0].appendChild(e),e.styleSheet.cssText=this.options.resetStyle}else{document.body.appendChild(e);var t=document.createTextNode(this.options.resetStyle);e.appendChild(t)}window.QSI.util.addToStyleElements(e)}},t.prototype.killPopUnder=function(){try{var e="QSIPopUnder_"+this.id;if(window.QSI.cookie.get(e)){var t=window.QSI.util.legacyOpenWin("",e);t&&t.w&&(t.w.popunderDead=!0),t.close(),window.QSI.cookie.erase(e)}}catch(e){}},t.prototype.shouldShow=function(){var e=!0;return this.interceptDisplayOptions.hideOnCookiesDisabled&&(window.QSI.cookie.areCookiesEnabled()?this.cookiesEnabled.resolve():(this.cookiesEnabled.reject(),e=!1)),this.interceptDisplayOptions.hideOnLocalStorageDisabled&&(window.QSI.localStorage.isLocalStorageEnabled()?this.localStorageEnabled.resolve():(this.localStorageEnabled.reject(),e=!1)),this.shouldPreventRepeatedDisplay()?(this.preventRepeatedDisplay.resolve(),e=!1):this.preventRepeatedDisplay.reject(),e?(this.willShow.resolve(),!0):(this.willShow.reject(),!1)},t.prototype.shouldPreventRepeatedDisplay=function(){return!!window.QSI.util.shouldPreventRepeatedDisplay(this.id,this.interceptDisplayOptions)||(window.QSI.cookie.erase("QSI_"+this.id+"_intercept"),!1)},t.prototype.setPreventRepeatedDisplayCookie=function(){if(this.interceptDisplayOptions&&this.interceptDisplayOptions.noshow&&0!=this.interceptDisplayOptions.noshow&&null===window.QSI.cookie.get("QSI_"+this.id+"_intercept"))try{window.QSI.cookie.set("QSI_"+this.id+"_intercept","true",{daysToExpire:this.interceptDisplayOptions.noshow,force:!0,domain:this.interceptDisplayOptions.cookieDomain})}catch(e){window.QSI.dbg.e(e)}},t.prototype.impress=function(){window.QSI.util.sendStat(this.options.impressionURL,this.id,this.requestId);try{var e=(new Date).getTime()/1e3,t=JSON.parse(window.localStorage.getItem("Q_INTER"));null===t&&(t={}),t[this.id]=e,window.localStorage.setItem("Q_INTER",JSON.stringify(t)),window.QSI.dbg&&window.QSI.dbg.safeConsole&&window.QSI.dbg.safeConsole("log","Logging impression for creative "+this.id)}catch(e){window.QSI.dbg.e(e)}this.setPreventRepeatedDisplayCookie()},t.prototype.click=function(){window.QSI.util.sendStat(this.options.clickURL,this.id,this.requestId),window.QSI.dbg&&window.QSI.dbg.safeConsole&&window.QSI.dbg.safeConsole("log","Logging click for creative "+this.id)},t.prototype.close||(t.prototype.close=function(){}),t.prototype.remove||(t.prototype.remove=t.prototype.close),t},removePx:function(e){var t=(e=e||"").indexOf("px");return t>0&&(e=e.substr(0,t)),e},getDimensions:function(e){var t=!0;e.parentNode&&e.parentNode.tagName||(document.body.appendChild(e),t=!1);var n=e.style.display||this.getComputedStyle(e).display;if("none"!==n&&null!==n){var i={width:e.offsetWidth,height:e.offsetHeight};return t||e.parentNode.removeChild(e),i}var r=e.style,o=r.visibility,a=r.position,s=r.display;r.visibility="hidden",r.position="absolute",r.display="block";var u=e.clientWidth,c=e.clientHeight;return r.display=s,r.position=a,r.visibility=o,t||e.parentNode.removeChild(e),{width:u,height:c}},convertPercentToPixel:function(e,t){return Math.round(e/100*t)},convertPixelToPercent:function(e,t){return Math.round(e/t*100)},cumulativeOffset:function(e){var t,n={top:0,left:0},i=e&&e.ownerDocument;if(i)return t=i.documentElement,void 0!==e.getBoundingClientRect&&(n=e.getBoundingClientRect()),{top:n.top+(window.pageYOffset||t.scrollTop)-(t.clientTop||0),left:n.left+(window.pageXOffset||t.scrollLeft)-(t.clientLeft||0)}},getTimeout:function(e){var t=window.QSI.util.Deferred();return e=1e3*parseFloat(e),window.setTimeout((function(){try{t.resolve()}catch(e){void 0!==window.QSI&&window.QSI.dbg&&window.QSI.dbg.e&&window.QSI.dbg.e(e)}}),e),t.promise()},getComputedStyle:function(e){return e.currentStyle||window.getComputedStyle(e,null)},getWindowSize:function(e){var t=e||window,n=this.getPageSize(e);return{width:t.outerWidth||n.width,height:t.outerHeight||n.height}},getScreenRes:function(){return{height:window.screen.height,width:window.screen.width}},getBrowserViewHeight:function(e){var t=e||window,n=t.document.documentElement;return"Chrome"===window.QSI.Browser.name&&window.QSI.Browser.isMobile&&"Android"===window.QSI.OS.name?t.outerHeight:window.QSI.Browser.isMobile&&n.clientWidth/t.innerWidth>1?n.clientHeight:t.innerHeight},getScrollOffsets:function(){var e=window,t=e.document.documentElement;return{left:"pageXOffset"in e?e.pageXOffset:t.scrollLeft,top:"pageYOffset"in e?e.pageYOffset:t.scrollTop}},hasVerticalScrollbar:function(){var e=document.documentElement;return"Internet Explorer"===window.QSI.Browser.name&&window.QSI.Browser.version<8||("Internet Explorer"===window.QSI.Browser.name&&window.QSI.Browser.version<9?e.offsetWidth-e.scrollWidth>6:e.clientWidth<window.innerWidth)},hasHorizontalScrollbar:function(){var e=document.documentElement;return"Internet Explorer"===window.QSI.Browser.name&&window.QSI.Browser.version<8||("Internet Explorer"===window.QSI.Browser.name&&window.QSI.Browser.version<9?e.offsetHeight-e.scrollHeight>6:e.clientHeight<window.innerHeight)},pageMode:function(){return"CSS1Compat"===document.compatMode?"Standards":"Quirks"},isFixed:function(){return!("Internet Explorer"===window.QSI.Browser.name&&"Standards"!==this.pageMode())},legacyOpenWin:function(e,t,n){var i=[];return n=n||{},window.QSI.util.forOwn(n,(function(e,t){i.push(t+"="+n[t])})),i=i.join(","),window.open(e,t,i)},sendStat:function(e,t,n){var i,r={type:"POST",url:e+"&r="+(new Date).getTime(),header:{"Content-type":"application/x-www-form-urlencoded"}};try{i=window.QSI.Request[n].Intercepts[t].Targeting}catch(e){return}var o="BrandID="+window.QSI.global.brandID+"&BrandDC="+this.getBrandDC(),a=window.QSI.config.externalReference,s=this.getZoneID(t,n);(s&&(o+="&ZoneID="+s),a)&&(o+="&ExtRef="+encodeURIComponent(a));i.DirectoryID&&(o+="&DirectoryID="+i.DirectoryID),i.SurveyID&&(o+="&SurveyID="+i.SurveyID),i.DistributionID&&(o+="&DistributionID="+i.DistributionID),i.ContactID&&(o+="&ContactID="+i.ContactID),r.data=o,window.QSI.util.sendHttpRequest(r)},getZoneID:function(e,t){var n=window.QSI.Request;try{return n[t].Intercepts[e].Creative.ZoneID}catch(e){return null}},getQueryParam:function(e,t){t=t.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var n=new RegExp("[\\?&]"+t+"=([^&#]*)").exec(e);return null===n?"":n[1]},safeJSONParse:function(e){try{return JSON.parse(e)}catch(e){return null}},addScreenCaptureParameterToTarget:function(e){return-1!==e.indexOf("?")?e+="&":e+="?",e+"Q_CanScreenCapture=1"},capFirst:function(e){return e.charAt(0).toUpperCase()+e.slice(1)},hasReachedScrollPosition:function(e){return this.getScrollOffsets().top+this.getPageSize().height>=document.body.scrollHeight*(e/100)},removeAllByQuery:function(e,t){var n=e.querySelectorAll(t);Array.prototype.forEach.call(n,(function(e){window.QSI.util.remove(e)}))},buildWidget:function(e,t){var n="build"+e+"Widget";return this[n]?this[n](t):""},buildCCDWidget:function(e){if((e=e||{}).close&&parseInt(e.close,10)>0){var t=e.close,n=this.getTempID(),i=this.build("span",{id:n},t+""),r=this.build("span",{},[i]),o=this,a=function(){var i=function(){try{if(t<=0)return;var e=o.$(n);e&&(e.textContent=--t+""),setTimeout(i,1e3)}catch(e){void 0!==window.QSI&&window.QSI.dbg&&window.QSI.dbg.e&&window.QSI.dbg.e(e)}};e.delay&&e.delay>0?setTimeout((function(){try{setTimeout(i,1e3)}catch(e){void 0!==window.QSI&&window.QSI.dbg&&window.QSI.dbg.e&&window.QSI.dbg.e(e)}}),1e3*e.delay):setTimeout(i,1e3)};return e.displayed?e.displayed.done((function(){a()})):a(),this.getElementHTML(r)}return""},positionFixed:function(e,t,n){e.style.position="absolute";var i,r=0,o=this.getPageSize().height,a=document.body.scrollHeight,s=window.QSI.util.getScrollOffsets().top;s>0&&("auto"===n?(e.style.top=s+parseInt(t,10)+"px",e.style.bottom=n):"auto"===t&&(e.style.bottom=s+o-parseInt(t,10)+"px",e.style.top=t)),"string"==typeof t&&t.indexOf("px")>-1&&(t=parseInt(t,10)),"string"==typeof n&&n.indexOf("px")>-1&&(n=parseInt(n,10));var u=function(){try{clearTimeout(i),i=setTimeout((function(){try{var i=window.QSI.util.getScrollOffsets().top;if(i<0||i+o>a)return;var s,c=i+t,d=c-r,l=d>=0?20:-20;Math.abs(d)<10&&(l=d),r=c,"auto"===n?s=setInterval((function(){try{var t=parseInt(e.style.top,10)+l;e.style.top=t+"px",e.style.bottom=n,l>0?t>=c&&(clearInterval(s),window.QSI.util.observe(window,"scroll",u)):t<=c&&(clearInterval(s),window.QSI.util.observe(window,"scroll",u)),window.QSI.util.observe(window,"scroll",u)}catch(e){void 0!==window.QSI&&window.QSI.dbg&&window.QSI.dbg.e&&window.QSI.dbg.e(e)}}),15):"auto"===t&&(s=setInterval((function(){try{var r=parseInt(e.style.bottom,10)+l;e.style.bottom=i+o-n+"px",e.style.top=t,e.style.bottom=r+"px",l>0?r>=c&&(clearInterval(s),window.QSI.util.observe(window,"scroll",u)):r<=c&&(clearInterval(s),window.QSI.util.observe(window,"scroll",u)),window.QSI.util.observe(window,"scroll",u)}catch(e){void 0!==window.QSI&&window.QSI.dbg&&window.QSI.dbg.e&&window.QSI.dbg.e(e)}}),15))}catch(e){void 0!==window.QSI&&window.QSI.dbg&&window.QSI.dbg.e&&window.QSI.dbg.e(e)}}),60)}catch(e){void 0!==window.QSI&&window.QSI.dbg&&window.QSI.dbg.e&&window.QSI.dbg.e(e)}};window.QSI.util.observe(window,"scroll",u)},each:function(e,t){var n=e.length;if(n)for(var i=0;i<n;i++)t(e[i],i)},isObjectEmpty:function(e){for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t))return!1;return!0},filter:function(e,t){try{if(e.filter&&this.isFunction(e.filter))return e.filter(t)}catch(e){}var n=e.length,i=[];if(n)for(var r=0;r<n;r++)t(e[r])&&i.push(e[r]);return i},Deferred:function(){var e={},t="pending",n=[],i=[],r=[],o=[],a={state:function(){return t},then:function(e,t){return this.done(e).fail(t),this},done:function(e){if("pending"===t)r.push(e);else if("resolved"===t)try{e.apply(this,n)}catch(e){void 0!==window.QSI&&window.QSI.dbg&&window.QSI.dbg.e&&window.QSI.dbg.e(e)}return this},fail:function(e){if("pending"===t)o.push(e);else if("rejected"===t)try{e.apply(this,i)}catch(e){void 0!==window.QSI&&window.QSI.dbg&&window.QSI.dbg.e&&window.QSI.dbg.e(e)}return this},promise:function(){return a}};return window.QSI.util.forOwn(a,(function(t,n){e[n]=a[n]})),e.resolve=function(){if("pending"===t){t="resolved";var e=window.QSI.util.createArrayFromArguments(arguments);n=e;var i=this;window.QSI.util.each(r,(function(t){try{t.apply(i,e)}catch(e){void 0!==window.QSI&&window.QSI.dbg&&window.QSI.dbg.e&&window.QSI.dbg.e(e)}}))}},e.reject=function(){if("pending"===t){t="rejected";var e=window.QSI.util.createArrayFromArguments(arguments);i=e;var n=this;window.QSI.util.each(o,(function(t){try{t.apply(n,e)}catch(e){void 0!==window.QSI&&window.QSI.dbg&&window.QSI.dbg.e&&window.QSI.dbg.e(e)}}))}},e},when:function(e){var t=window.QSI.util.createArrayFromArguments(arguments),n=t.length,i=n,r=1===i?e:window.QSI.util.Deferred(),o=function(e,t){return function(n){t[e]=arguments.length>1?n:window.QSI.util.createArrayFromArguments(arguments),--i||r.resolve(t)}};if(n>1)for(var a=0;a<n;a++)t[a]&&t[a].promise?t[a].promise().done(o(a,t)).fail(r.reject):i--;return i<1&&r.resolve(t),r.promise()},moveToBackground:function(){("Firefox"===window.QSI.Browser.name||"Internet Explorer"===window.QSI.Browser.name&&window.QSI.Browser.version>=11)&&window.open("","_self","").focus()},handleMailTo:function(e,t,n,i){this.addClickImg(t,n,i,(function(){try{window.location.href=e}catch(e){void 0!==window.QSI&&window.QSI.dbg&&window.QSI.dbg.e&&window.QSI.dbg.e(e)}}))},addClickImg:function(e,t,n,i){var r=new Date,o=window.QSI.baseURL+"?Q_Click=1&Q_CID="+n+"&Q_SIID="+e+"&Q_ASID="+t+"&T="+r.getTime()+"&Q_LOC="+encodeURIComponent(window.location.href);this.addImage(o,i)},addImage:function(e,t){var n=window.QSI.util.build("img",{src:e,alt:""});window.QSI.util.isIE()&&window.QSI.util.setStyle(n,{display:"none"}),t&&window.QSI.util.observe(n,"load",t),document.body.appendChild(n)},processLocators:function(e,t){for(var n=0,i=e.length;n<i;n++){var r=e[n];r.locators&&(window.QSI.PipedText.setLocators(r.locators),r.content=window.QSI.PipedText.evaluateLocators(r.content,t))}},getDocDimension:function(e){var t=document,n=t.documentElement;return Math.max(t.body["scroll"+e],n["scroll"+e],t.body["offset"+e],n["offset"+e],n["client"+e])},getDocWidth:function(){return this.getDocDimension("Width")},getDocHeight:function(){return this.getDocDimension("Height")},getScroll:function(){var e=this.getScrollOffsets();return{width:this.getDocWidth(),height:this.getDocHeight(),left:e.left,top:e.top}},fireGoogleEventBeacon:function(e,t,n){e.search(/^UA-\d+-\d{1,2}$/)<0?window.QSI.dbg.c("Google Anylytics Account number is incorrect "+e):window.GoogleAnalyticsObject?this.googleEventAnyalytics(window.GoogleAnalyticsObject,e,t,n):this.googleEventGA(e,t,n)},googleEventGA:function(e,t,n){var i="SITracker"+this.getTempID(),r=window._gaq||[];r.push([i+"._setAccount",e]),r.push([i+"._trackEvent",t,n])},googleEventAnyalytics:function(e,t,n,i){var r="SITracker"+this.getTempID();window[e]("create",t,{name:r}),window[e](r+".send","event",n,i)},evalJS:function(e){return function(){try{window.QSI.global.enableJSSanitization||void 0===window.QSI.doEvalJS?window.QSI.dbg.e("Error: JavaScript Evaluation has been disabled. Attempted to execute: '"+e+"'"):window.QSI.doEvalJS(e)}catch(e){window.QSI.dbg.c("Error During Eval JavaScript "+e)}}},processElementEvents:function(e,t,n){if(!(!e||e.length<=0||e[0].length<=0)){for(var i=new window.QSI.ActionModule(n),r=e[0],o=0,a=r.length;o<a;o++)i.add(r[o]);i.addToElement(t)}},clickOnKeyDown:function(e){try{32!==e.which&&13!==e.which||e.target.click()}catch(e){void 0!==window.QSI&&window.QSI.dbg&&window.QSI.dbg.e&&window.QSI.dbg.e(e)}},stableSort:function(e,t){t=t||function(e,t){return e<t?-1:e>t?1:0};var n=e.map((function(e,t){return[e,t]}));n.sort((function(e,n){var i=t(e[0],n[0]);return 0!==i?i:e[1]-n[1]}));for(var i=0;i<e.length;i++)e[i]=n[i][0];return e},isLegacyCreative:function(e){if(e){if(-1!==["PopOver","Slider","InfoBar","UserDefinedHTML","PopUnder","FeedbackLink","Link","HTTPRedirect","PopUp","Relay"].indexOf(e))return!0}return!1}},r=n(0),o=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var i,r,o=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(i=o.next()).done;)a.push(i.value)}catch(e){r={error:e}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return a},a=function(e,t){for(var n=0,i=t.length,r=e.length;n<i;n++,r++)e[r]=t[n];return e};var s=function(e){var t=this;this.creative=e,this.actions={click:[],mouseout:[],mouseover:[],displayed:[]},this.add=function(e){t.actions[e.triggeringEvent]&&t.actions[e.triggeringEvent].push(t.buildAction(e))},this.buildAction=function(e){return{run:function(){switch(e.actionType){case"AddCookie":t.runAddCookieAction(e);break;case"RemoveCookie":t.runRemoveCookieAction(e);break;case"GoogleEvent":t.runGoogleEventAction(e);break;case"JavaScript":t.runJavaScriptAction(e)}}}},this.runAddCookieAction=function(e){window.QSI.cookie.set(e.cookieName,e.cookieValue,{domain:t.creative.interceptDisplayOptions.cookieDomain,daysToExpire:0})},this.runRemoveCookieAction=function(e){window.QSI.cookie.erase(e.cookieName,t.creative.interceptDisplayOptions.cookieDomain)},this.runGoogleEventAction=function(e){window.QSI.util.fireGoogleEventBeacon(e.accountNumber,e.actionCategory,e.actionName)},this.runJavaScriptAction=function(e){window.QSI.util.evalJS(e.javaScriptString)()},this.addToElement=function(e){var n=function(e){var n=window.QSI.dbg,i=!1;return function(){try{i||(i=!0,t.runAction(e))}catch(e){n.e(e)}}};for(var i in t.actions)t.actions[i].length&&("displayed"===i?t.creative.displayed.done(n(t.actions[i])):Object(r.observe)(e,i,n(t.actions[i])))},this.runAction=function(e){for(var t=0;t<e.length;t++)e[t].run()}},u=function(){return(u=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)},c="377px",d="276px",l="173px",p="339px",f=function(e,t,n,i){var o=this;this.el=e,this.urlCallback=t,this.options=n,this.parent=i,this.styleElement=null,this.openTarget=function(){var e=o.urlCallback(),t=o.options,n=window.QSI,i=n.dbg,a=n.windowHandler,s=n.BuildResponsiveEmbeddedTarget,u=n.BuildResponsiveElementModule,c=n.EmbeddedTarget,d=n.WebResponsiveDialog,l=n.global;if(e&&0!==e.indexOf("&"))if(t.targetReplaceContents&&o.parent.getType()!==r.creativeTypes.USER_DEFINED_HTML&&(t.targetNewWindow=!0,t.targetReplaceContents=!1),o.parent.click(),t.targetNewWindow)try{var p=Object(r.getPageSize)(),f=t.targetFullScreen?screen.availWidth||screen.width||p.width:t.targetWidth,g=t.targetFullScreen?screen.availHeight||screen.height||p.height:t.targetHeight,w="targetwindow_"+(new Date).valueOf().toString(),h=Object(r.openWin)(e,w,{status:"yes",scrollbars:"yes",resizable:"yes",width:f,height:g},o.parent.id,o.parent.requestId,o.parent.options.targetURLType);if(void 0!==o.parent&&null!==o.parent){var S=void 0;void 0!==o.parent.options&&null!==o.parent.options&&(S=o.parent.options.targetURLOrigin),a.addOptInIDAndWindowName(o.parent.id,w,S||"*"),a.addWindowHandle(w,h)}h&&(h.onpagehide=function(){if(window.opener&&window.opener.sessionStorage){var e=window.opener.sessionStorage,t=e.getItem("QSI_OptInIDsAndWindowNames");if(t){try{t=JSON.parse(t)}catch(e){return void i.e(e)}for(var n in t)t[n]===window.name&&delete t[n];e.setItem("QSI_OptInIDsAndWindowNames",JSON.stringify(t))}}})}catch(e){i.e(e)}else if(t.targetEmbedded){var I=o.parent.options?o.parent.options.targetURLType:null;if("WebResponsiveDialog"!==o.parent.options.type){if(new c(e,t,o.parent),window.QSI.util.usePostToStart(I)){var v=window.QSI.EmbeddedData.getEmbeddedDataAsArray(o.parent.options.id,o.parent.options.requestId),m=window.QSI.WindowUtils,E=window.QSI.util.addScreenCaptureParameterToTarget(e);m.postToIframe("survey-iframe-"+o.parent.options.id,E,v)}}else{var y=new s(e,I),b={containerOptions:{className:"QSIEmbeddedTargetContainer",style:{border:"3px solid rgb(51, 51, 51)",borderRadius:"3px",boxShadow:"rgba(0, 0, 0, 0.7) 0px 0px 10px 0px",overflow:"visible",position:"fixed",zIndex:l.currentZIndex.toString(),top:"50%",left:"50%",transform:"translate(-50%, -50%)"}},iframeOptions:{interceptId:o.parent.options.id},inCreative:!1};Object(r.stopScrolling)(),o.styleElement=u.appendStylesToDocument(o.getSimpleEmbeddedTargetStyleSheet());var T=o.buildResponsiveEmbeddedTargetCloseButton(),Q=y.build(b,T);window.QSI.targetWindows.push(Q.firstChild),T.onclick=function(){Object(r.closeResponsiveEmbeddedTarget)(Q,o.parent.previouslyFocussedElement)},Q.insertBefore(T,Q.firstChild),d.addAccessibilityFunctionality(Q,{Type:"fade"},o.parent.previouslyFocussedElement),o.options.autoCloseTarget&&Object(r.observe)(window,"message",(function(e){var t=window.QSI.Orchestrator;try{if(!t||!t.isMessageEventOriginAllowed||!t.isMessageEventOriginAllowed(e.origin))return;"closeQSIWindow"!==e.data&&"endOfSurvey"!==e.data||Object(r.closeResponsiveEmbeddedTarget)(Q,this.parent.previouslyFocussedElement)}catch(e){window.QSI.dbg.e(e)}})),document.body.appendChild(Q),window.QSI.util.usePostToStart(I)&&(v=window.QSI.EmbeddedData.getEmbeddedDataAsArray(o.parent.options.id,o.parent.options.requestId),m=window.QSI.WindowUtils,E=window.QSI.util.addScreenCaptureParameterToTarget(e),m.postToIframe("survey-iframe-"+o.parent.options.id,E,v)),T.focus()}}else if(t.targetPopUnder){var N=window.QSI,A=N.AssetManager,O=N.global,R=N.LatencyLog;A.promiseLoadScript("PopUnder",O.clientVersion,N,R).then((function(){new(0,window.QSI.PopUnderTarget)(e,t,o.parent)}))}else t.targetReplaceContents?o.handleReplaceCreative():t.targetNewTab?Object(r.openWin)(e,"",{},o.parent.id,o.parent.requestId,o.parent.options.targetURLType):Object(r.openWin)(e,"_self",{},o.parent.id,o.parent.requestId,o.parent.options.targetURLType)},this.buildResponsiveEmbeddedTargetCloseButton=function(){var e=window.QSI.global,t=window.QSI.LocalizationModule,n=Object(r.build)("img",{src:e.imagePath+"/siteintercept/bwc_close.png",style:{height:"29px",width:"29px",zIndex:(e.currentZIndex++).toString()},alt:t.getLocalizedString("Close")});return Object(r.build)("button",{style:{padding:"0",border:"0",background:"none",cursor:"pointer",position:"absolute",top:"-15px",right:"-15px",zIndex:(e.currentZIndex++).toString()},tabindex:"0","aria-label":t.getLocalizedString("Close")},[n])},this.getSimpleEmbeddedTargetStyleSheet=function(e){void 0===e&&(e="60vh");var t=window.QSI.Browser,n=e,i=e,r=e,o=e;"60vh"!==e&&(parseInt(e)<parseInt(c)&&(n=c),parseInt(e)<parseInt(d)&&(i=d),parseInt(e)<parseInt(l)&&(r=l),parseInt(e)<parseInt(p)&&(o=p));var a="      @media only screen and (max-width: 768px) {          .QSIEmbeddedTargetContainer{              width: 90%;              height: "+n+";              max-height: 90vh;          }      }     @media only screen and (min-width: 769px) {          .QSIEmbeddedTargetContainer{              width: 80%;              max-width: 750px;              height: "+n+";              max-height: 90vh;          }      }    ",s="      @media only screen and (max-device-width: 767px) and (orientation: portrait) {          .QSIEmbeddedTargetContainer{              width: 90%;              height: "+i+";              max-height: 90dvh;          }      }      @media only screen and (min-device-width: 768px) and (orientation: portrait) {          .QSIEmbeddedTargetContainer{              width: 80%;              max-width: 750px;              height: "+o+";              max-height: 90dvh;          }      }      @media only screen and (max-device-width: 926px) and (orientation: landscape) {        .QSIEmbeddedTargetContainer{            width: 70%;            height: "+r+";              max-height: 90dvh;          }      }      @media only screen and (min-device-width: 927px) and (orientation: landscape) {        .QSIEmbeddedTargetContainer{            width: 70%;            height: "+o+";            max-height: 90dvh;        }      }    ";return t.isMobile?s:a},this.surveyHeightEventListener=function(e){if(o.options&&o.options.targetEmbedded&&o.options.resizeForEmbeddedTargets)try{var t=window.QSI.util.validatePostMessage(e);if(!t||"JFE"!==t.from||"SI"!==t.to)return;if("sendingSurveyHeight"===t.event&&"string"==typeof t.value&&t.value.match(/^\d+(\.\d+)?(px)$/)){var n=(parseInt(t.value)+6).toString()+"px";window.QSI.BuildResponsiveElementModule.appendStylesToDocument(o.getSimpleEmbeddedTargetStyleSheet(n),o.styleElement),window.removeEventListener("message",o.surveyHeightEventListener)}}catch(e){window.removeEventListener("message",o.surveyHeightEventListener),void 0!==window.QSI&&window.QSI.dbg&&window.QSI.dbg.e&&window.QSI.dbg.e(e)}},this.handleReplaceCreative=function(){var e,t,n=window.QSI,i=n.WindowUtils,a=n.EmbeddedData,s=n.util,c=o.parent.node;o.options.sameSizeAsCreative?(e=o.parent.options.size.width,t=o.parent.options.size.height):(e=o.options.targetWidth,t=o.options.targetHeight);var d={width:e+"px",height:t+"px",display:"block",border:"none",outline:"none",zIndex:"2000000000"},l=o.parent.options,p=l.targetURLType,f=l.id,g=l.requestId,w=s.usePostToStart(p),h=f+"-replaced-content-target-iframe",S=o.urlCallback(),I=u({name:h},!w&&{src:S}),v=Object(r.build)("iframe",I);if(Object(r.setStyle)(v,d),o.parent.node=v,o.parent.options.size={width:e,height:t},c.parentNode.replaceChild(v,c),o.parent.displayOptions.customPosition&&o.parent.position(),w){var m=a.getEmbeddedDataAsArray(f,g);i.postToIframe(h,S,m)}},this.complete=function(){return o.targetOpenedPromise};var a=window.QSI.dbg;this.targetOpenedPromise=new Promise((function(e,t){Object(r.observe)(o.el,"click",(function(){try{o.urlCallback&&(o.openTarget(),e())}catch(e){a.e(e),t(e)}}))})),window.addEventListener("message",this.surveyHeightEventListener)},g=n(3);n.d(t,"UtilityTools",(function(){return h}));var w=function(){return(w=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)},h=w(w(w({},r),i),{Class:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];function n(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=window.QSI.dbg;try{this.initialize&&this.initialize.apply(this,a([],o(e)))}catch(e){n.e(e)}}function i(e){Object(r.forOwn)(e,(function(t,i){n.prototype[i]=e[i]}))}return e.forEach((function(e){i(e)})),n}});window.QSI.util=h,window.QSI.ActionModule=s,window.QSI.Target=f,window.QSI.WindowUtils=g},function(e,t,n){"use strict";var i;n.r(t),function(e){e.LEAF_NUMBER_NONE="LeafNumberNoneValueNode",e.LEAF_NUMBER="LeafNumberValueNode",e.LEAF_STRING_NONE="LeafStringNoneValueNode",e.LEAF_STRING="LeafStringValueNode",e.LEAF_STRING_LIST="LeafStringListValueNode"}(i||(i={}));var r,o,a,s,u,c,d=function(){this.Type=i.LEAF_NUMBER_NONE,this.Value=0},l=function(e){this.Type=i.LEAF_NUMBER,this.Value=e},p=function(){this.Type=i.LEAF_STRING_NONE,this.Value=""},f=function(e){this.Type=i.LEAF_STRING,this.Value=e},g=function(e){this.Type=i.LEAF_STRING_LIST,this.Value=e};!function(e){e.AND="AND",e.OR="OR"}(r||(r={})),function(e){e.EQ="EQ",e.NEQ="NEQ",e.GT="GT",e.GTE="GTE",e.LT="LT",e.LTE="LTE",e.IS_EMPTY="IS_EMPTY",e.NOT_EMPTY="NOT_EMPTY",e.CONTAINS="CONTAINS",e.NOT_CONTAINS="NOT_CONTAINS",e.STARTS_WITH="STARTS_WITH",e.REGEX="REGEX",e.HAS_BEEN_TAKEN="HAS_BEEN_TAKEN",e.HAS_NOT_BEEN_TAKEN="HAS_NOT_BEEN_TAKEN",e.HAS_BEEN_SEEN="HAS_BEEN_SEEN",e.HAS_NOT_BEEN_SEEN="HAS_NOT_BEEN_SEEN",e.SAMPLING="SAMPLING"}(o||(o={})),function(e){e.VALUE_NODE="ValueNode",e.COMPARATOR_NODE="ComparatorNode",e.DEBUG_COMPARATOR_NODE="DebugComparatorNode",e.CONJUNCTION_NODE="ConjunctionNode",e.LOGIC_NODE="LogicNode",e.FAILURE_NODE="FailureNode"}(a||(a={})),function(e){e.ACTION_SET_LOGIC="ActionSetLogic",e.ADOBE_ANALYTICS="AdobeAnalyticsVariable",e.COOKIE="Cookie",e.EVENT_TRACKING="EventTracking",e.HTML_ON_SITE="HtmlOnSite",e.SITE_HISTORY="SiteHistory",e.INTERCEPT_LOGIC="InterceptLogic",e.INTERCEPT_LOGIC_ANY="InterceptLogicAny",e.INTERCEPT_LOGIC_NONE="InterceptLogicNone",e.JAVASCRIPT_EXPRESSION="JavaScriptExpression",e.PAGE_COUNT="PageCount",e.REFERRER="Referrer",e.RESOLUTION="Resolution",e.SEARCH_TERM="SearchTerm",e.TIME_ON_SITE="TimeOnSite",e.DATE_TIME_LOGIC="DateTime",e.BROWSER="Browser",e.DEVICE_TYPE="DeviceType",e.SAMPLING="Sampling",e.CURRENT_URL="CurrentUrl",e.QUERY_PARAM="QueryParameter",e.LOCATION="Location",e.IP_ADDRESS="IpAddress",e.USER_AGENT="UserAgent",e.QUALTRICS_SURVEY="QualtricsSurvey",e.SEGMENTATION="Segmentation",e.GOOGLE_DATA_LAYER="GoogleDataLayer",e.RAGE_CLICK="RageClick",e.MOUSE_THRASH="MouseThrash",e.ERROR_CLICK="ErrorClick",e.DEAD_CLICK="DeadClick"}(s||(s={})),function(e){e.ACTION_SET_LOGIC="ActionSetLogic",e.ADOBE_ANALYTICS="AdobeAnalyticsVariable",e.COOKIE="Cookie",e.EVENT_TRACKING="EventTracking",e.HTML_ON_SITE="HtmlOnSite",e.SITE_HISTORY="SiteHistory",e.INTERCEPT_LOGIC="InterceptLogic",e.INTERCEPT_LOGIC_ANY="InterceptLogicAny",e.INTERCEPT_LOGIC_NONE="InterceptLogicNone",e.JAVASCRIPT_EXPRESSION="JavaScriptExpression",e.PAGE_COUNT="PageCount",e.REFERRER="Referrer",e.RESOLUTION="Resolution",e.SEARCH_TERM="SearchTerm",e.TIME_ON_SITE="TimeOnSite",e.DATE_TIME_LOGIC="DateTime",e.GOOGLE_DATA_LAYER="GoogleDataLayer",e.RAGE_CLICK="RageClick",e.MOUSE_THRASH="MouseThrash",e.ERROR_CLICK="ErrorClick",e.DEAD_CLICK="DeadClick"}(u||(u={})),function(e){e.ANY="Any",e.FIRST="First",e.LAST="Last",e.ONLY="Only"}(c||(c={}));var w,h={EQ:"is equal to",NEQ:"is not equal to",GT:"is greater than",GTE:"is greater than or equal to",LT:"is less than",LTE:"is less than or equal to",IS_EMPTY:"is empty",NOT_EMPTY:"is not empty",CONTAINS:"contains",NOT_CONTAINS:"does not contain",STARTS_WITH:"starts with",REGEX:"matches regex",HAS_BEEN_TAKEN:"has been taken",HAS_NOT_BEEN_TAKEN:"has not been taken",HAS_BEEN_SEEN:"has been seen",HAS_NOT_BEEN_SEEN:"has not been seen",ENDS_WITH:"ends with",IN_CIDR_RANGE:"is in CIDR range",NOT_IN_CIDR_RANGE:"is not in CIDR range",SAMPLING:""},S={Mon:"Monday",Tue:"Tuesday",Wed:"Wednesday",Thu:"Thursday",Fri:"Friday",Sat:"Saturday",Sun:"Sunday"},I={ScreenHeight:"Screen resolution height",ScreenWidth:"Screen resolution width",BrowserHeight:"Browser size height",BrowserWidth:"Browser size width"},v={EQ:"is",NEQ:"is not",GT:"is after",GTE:"is or is after",LT:"is before",LTE:"is or is before",IS_EMPTY:"",NOT_EMPTY:"",CONTAINS:"",NOT_CONTAINS:"",STARTS_WITH:"",REGEX:"",HAS_BEEN_TAKEN:"",HAS_NOT_BEEN_TAKEN:"",HAS_BEEN_SEEN:"",HAS_NOT_BEEN_SEEN:"",SAMPLING:""},m={ActionSetLogic:"Action Set",AdobeAnalyticsVariable:"Adobe Analytics variable",Cookie:"Cookie",EventTracking:"Event tracking",HtmlOnSite:"HTML on site",SiteHistory:"Site history",InterceptLogic:"Intercept",InterceptLogicAny:"Any intercept",InterceptLogicNone:"No intercept",JavaScriptExpression:"JavaScript expression",PageCount:"Page count",Referrer:"Referrer",Resolution:"Screen resolution",SearchTerm:"Search term",TimeOnSite:"Time on site",DateTime:"",Browser:"Browser",DeviceType:"Device type",CurrentUrl:"Current URL",QueryParameter:"Current URL query parameter",IpAddress:"IP address",Location:"Location",UserAgent:"User agent",QualtricsSurvey:"Qualtrics Survey",Sampling:"Sampling",Segmentation:"XM Directory segment",GoogleDataLayer:"Google Data Layer Variable",RageClick:"Rage click count",MouseThrash:"Mouse thrash count",ErrorClick:"Error click count",DeadClick:"Dead click count"},E=function(){function e(t){this.dataLayer=t,!w&&t.push&&(w=t.push,t.push=e.qualtricsPush)}return e.prototype.get=function(e,t){void 0===t&&(t=function(){return null});try{var n=window.QSI.config.gtmContainerID;if(!n)throw new Error("Data layer value retrieval failed because of missing Google Tag Manager container id");if(!window.google_tag_manager[n])throw new Error("Google Tag Manager container with id '"+n+"' does not exist");return window.google_tag_manager[n].dataLayer.get(e)}catch(e){return t()}},e.qualtricsPush=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=[].slice.call(e,0),i=w.apply(void 0,n);if(window.QSI){var r=window.QSI.InterceptReevaluator,o=window.QSI.DataLayerHelper;if(r.isActive()&&o)try{r.debouncedEvaluate()}catch(e){var a=window.QSI.dbg;a.e(e)}}return i},e}();function y(){return JSON.parse(window.localStorage.getItem("Q_INTER"))}function b(e){return t=e.Expression,window.QSI.DataLayerHelper.get(t)||"";var t}var T=n(2),Q=n(0),N=n(1),A=n.n(N);n.d(t,"ClientSideTargeting",(function(){return D}));var O=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],i=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},R=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var i,r,o=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(i=o.next()).done;)a.push(i.value)}catch(e){r={error:e}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return a},_=function(e,t){for(var n=0,i=t.length,r=e.length;n<i;n++,r++)e[r]=t[n];return e},D=function(){var e=this;this.enabled=!1,this.interceptID=void 0,this.setEnabled=function(t){e.enabled=t},this.isEnabled=function(){return e.enabled},this.evaluateIntercept=function(t,n,i){var r,o;A.a.PE=Number.MAX_VALUE,A.a.NE=-Number.MAX_VALUE,e.interceptID=t.InterceptID;var a={Expressions:[],ActionSets:{},State:"Published",Evaluated:!1,Valid:!1,Name:t.Name,Active:t.Active,Sampled:!0,SampleRate:100,cookiesEnabled:!0,localStorageEnabled:!0,preventRepeatedDisplay:!1,contactFrequencyPassed:null},s=window.QSI.dbg;if(!window.QSI.config.editing&&!t.Active)return a.Valid=!0,{interceptEntry:null,interceptDebugInfo:i?a:null};var u,c,d,l=!0;if(!e.shouldShow(t,a)&&(l=!1,!i))return{interceptEntry:null,interceptDebugInfo:i?a:null};try{if("boolean"!=typeof(u=e.evaluateLogicTree(t.LogicTree,a))&&(u="true"===u.Value),a.Valid=!!u,!i&&!u)return{interceptEntry:null,interceptDebugInfo:null}}catch(e){return s.e(e),{interceptEntry:null,interceptDebugInfo:null}}try{try{for(var p=O(t.ActionSets),f=p.next();!f.done;f=p.next()){var g=f.value;if(a.ActionSets[g.ActionSetID]={Expressions:[],Label:g.Label||null,Creative:null,Displayed:!1,Valid:!0,Evaluated:!1,Sampled:!0,SampleRate:100},!c&&u){var w=e.evaluateLogicTree(g.LogicTree,a.ActionSets[g.ActionSetID]);if("boolean"!=typeof w&&(w="true"===w.Value),i&&(w=w&&a.ActionSets[g.ActionSetID].Sampled),w){if(c=g,a.ActionSets[g.ActionSetID].Displayed=!0,a.ActionSets[g.ActionSetID].Evaluated=!0,a.ActionSets[g.ActionSetID].Creative={Name:g.Creative.Name,State:"Published"},!i)break}else a.ActionSets[g.ActionSetID].Evaluated=!0,a.ActionSets[g.ActionSetID].Valid=!1,a.ActionSets[g.ActionSetID].Sampled||(a.ActionSets[g.ActionSetID].Valid=!0,a.ActionSets[g.ActionSetID].Creative={Name:g.Creative.Name,State:"Published"})}}}catch(e){r={error:e}}finally{try{f&&!f.done&&(o=p.return)&&o.call(p)}finally{if(r)throw r.error}}}catch(e){return s.e(e),{interceptEntry:null,interceptDebugInfo:null}}var h=!0;if(i&&(h=a.Sampled),c&&h){if(t.ContactFrequencyRulesEnabled&&(n.FirstDCFInterceptPassed||!c.ContactFrequencyPassed))return a.contactFrequencyPassed=!1,{interceptEntry:null,interceptDebugInfo:i?a:null};a.contactFrequencyPassed=!0,d={Decision:{ActionSetID:c.ActionSetID,Creative:c.Creative,Target:c.Target},InterceptID:t.InterceptID,InterceptRevision:t.InterceptRevision,ContactID:t.ContactID,DirectoryID:t.DirectoryID,DistributionID:t.DistributionID,SurveyID:c.SurveyID},t.ContactFrequencyRulesEnabled&&c.ContactFrequencyPassed&&(n.FirstDCFInterceptPassed=!0),c&&c.PopUnderTarget&&(d.Decision.PopUnderTarget=c.PopUnderTarget)}return{interceptEntry:l&&d?d:null,interceptDebugInfo:i?a:null}},this.getLargestTimeIntervalFromSeconds=function(e,t){var n=+e;return n%86400==0&&t?n/86400+" day"+(86400===n?"":"s"):n%3600==0?n/3600+" hour"+(3600===n?"":"s"):n%60==0?n/60+" minute"+(60===n?"":"s"):n+" seconds"},this.getExpressionString=function(t,n,i){var r=t.Left,o=t.Right,a=n||!i?"If":i,u=a.charAt(0)+a.substring(1).toLowerCase();switch(r.LogicType){case s.ACTION_SET_LOGIC:var c=t.Left;return u+" "+m[r.LogicType]+" "+c.ActionSetID+" "+h[t.Comparator]+" "+(c.InCurrentSession?"in the current session":"ever");case s.INTERCEPT_LOGIC:var d=t.Left;return u+" "+m[r.LogicType]+" "+d.InterceptIDs[0]+" in this zone "+h[t.Comparator]+" in the last "+e.getLargestTimeIntervalFromSeconds(o.Value,!0);case s.INTERCEPT_LOGIC_ANY:case s.INTERCEPT_LOGIC_NONE:return u+" "+m[r.LogicType]+" in this zone "+h[t.Comparator]+" in the last "+e.getLargestTimeIntervalFromSeconds(o.Value,!0);case s.PAGE_COUNT:return u+" "+("Total"===r.Expression?"Total pages visited":"Unique pages visited")+" "+h[t.Comparator]+(o?" "+o.Value:"");case s.REFERRER:return u+" "+r.Expression+" Referrer "+h[t.Comparator]+(o?" "+o.Value:"");case s.RESOLUTION:return u+" "+I[r.Expression]+" "+h[t.Comparator]+" "+o.Value+"px";case s.TIME_ON_SITE:return u+" "+("Spent"===r.Expression?"Time spent on site":"Time focused on site")+" "+h[t.Comparator]+" "+e.getLargestTimeIntervalFromSeconds(o.Value,!1);case s.DATE_TIME_LOGIC:return u+" "+r.Expression+" "+v[t.Comparator]+" "+("Day"===r.Expression?S[o.Value]:o.Value);case s.ADOBE_ANALYTICS:case s.COOKIE:case s.EVENT_TRACKING:case s.HTML_ON_SITE:case s.JAVASCRIPT_EXPRESSION:case s.GOOGLE_DATA_LAYER:case s.RAGE_CLICK:case s.MOUSE_THRASH:case s.ERROR_CLICK:case s.DEAD_CLICK:return u+" "+m[r.LogicType]+" "+r.Expression+" "+h[t.Comparator]+(o?" "+o.Value:"");case s.SITE_HISTORY:return u+" "+m[r.LogicType]+" "+r.Expression.toLowerCase()+" visited URL "+h[t.Comparator]+(o?" "+o.Value:"");case s.CURRENT_URL:case s.IP_ADDRESS:case s.BROWSER:case s.DEVICE_TYPE:case s.USER_AGENT:case s.SEARCH_TERM:case s.LOCATION:case s.SEGMENTATION:return u+" "+m[r.LogicType]+" "+h[t.Comparator]+(o?" "+o.Value:"");case s.QUERY_PARAM:return u+" "+m[r.LogicType]+" "+r.Expression.toLowerCase()+" "+h[t.Comparator]+(o?" "+o.Value:"");case s.QUALTRICS_SURVEY:return u+" "+m[r.LogicType]+" "+r.Expression+" "+h[t.Comparator]}return""},this.evaluateLogicTree=function(t,n){return e.evaluateLogicTreeHelper(t,n,[])},this.shouldEvaluateComparatorNode=function(t){return t.Comparator!=o.SAMPLING&&(null===t.Result||e.isClientEvaluatedLogicType(t))},this.isClientEvaluatedLogicType=function(e){var t=e.Left;return Object.values(u).includes(t.LogicType)},this.evaluateLogicTreeHelper=function(t,n,i,r){switch(t.Type){case a.COMPARATOR_NODE:var u=t;return e.evaluateComparatorNode(u,n,i);case a.DEBUG_COMPARATOR_NODE:u=t,e.shouldEvaluateComparatorNode(u)&&(u.Result=e.evaluateComparatorNode(u,n,i));var c=u.Left;if("Sampling"===c.LogicType)return n.Sampled=u.Result,n.SampleRate=Number(c.Expression),!0;var d=e.getExpressionString(u,0===n.Expressions.length,i.shift());return Object(Q.strIncludes)(d,m[s.DEVICE_TYPE])&&Object(Q.strIncludes)(d,h[o.NEQ])&&Object(Q.strIncludes)(d,"all")?(i=[],n.Expressions=[]):n.Expressions.push({Description:d,Result:u.Result}),u.Result;case a.CONJUNCTION_NODE:var l=t;return e.evaluateConjunctionNode(l,n,i);case a.LOGIC_NODE:var p=t;return e.evaluateLogicNode(p,r);case a.VALUE_NODE:var f=t;return e.evaluateValueNode(f);case a.FAILURE_NODE:throw t.Value}return!1},this.evaluateComparatorNode=function(t,n,i){var r=e.evaluateLogicTreeHelper(t.Left,n,i,t.Comparator),a=t.Right?e.evaluateLogicTreeHelper(t.Right,n,i,t.Comparator):void 0,u=t.Left;if(u.LogicType===s.DATE_TIME_LOGIC&&"Date"===u.Expression&&(a=new f(new Date(a.Value).getTime().toString())),u.LogicType===s.SITE_HISTORY){var d=r.Value;if(!Array.isArray(d))return!1;var l=new p;switch(u.Expression){case c.ONLY:1===d.length&&(l=new f(d[0]));break;case c.FIRST:d.length>0&&(l=new f(d[0]));break;case c.LAST:d.length>1&&(l=new f(d[d.length-2]));break;case c.ANY:l=new g(d)}return e.compare(l,a,t.Comparator)}return u.LogicType===s.INTERCEPT_LOGIC_NONE&&t.Comparator===o.HAS_BEEN_SEEN?e.compare(r,a,o.HAS_NOT_BEEN_SEEN):e.compare(r,a,t.Comparator)},this.compare=function(e,t,n){var r,a,s=e.Type,u=t?t.Type:void 0;switch(n){case o.EQ:return c(e,t);case o.NEQ:return!c(e,t);case o.GT:return d(e,t);case o.GTE:return p(e,t);case o.LT:return r=e,a=t,!(s===i.LEAF_STRING_NONE&&u===i.LEAF_STRING||s===i.LEAF_STRING&&u===i.LEAF_STRING_NONE||s===i.LEAF_STRING_NONE&&u===i.LEAF_NUMBER||s===i.LEAF_NUMBER&&u===i.LEAF_STRING_NONE||s===i.LEAF_NUMBER&&u===i.LEAF_STRING||s===i.LEAF_STRING&&u===i.LEAF_NUMBER||p(r,a));case o.LTE:return function(e,t){return!(s===i.LEAF_STRING_NONE&&u===i.LEAF_STRING||s===i.LEAF_STRING&&u===i.LEAF_STRING_NONE||s===i.LEAF_STRING_NONE&&u===i.LEAF_NUMBER||s===i.LEAF_NUMBER&&u===i.LEAF_STRING_NONE||s===i.LEAF_NUMBER&&u===i.LEAF_STRING||s===i.LEAF_STRING&&u===i.LEAF_NUMBER||d(e,t))}(e,t);case o.IS_EMPTY:return!f(e);case o.NOT_EMPTY:return f(e);case o.CONTAINS:return g(e,t);case o.NOT_CONTAINS:return!g(e,t);case o.STARTS_WITH:return function(e,t){return s===i.LEAF_STRING&&u===i.LEAF_STRING?e.Value.startsWith(t.Value):s===i.LEAF_NUMBER&&u===i.LEAF_STRING?String(e.Value).startsWith(t.Value):s===i.LEAF_STRING&&u===i.LEAF_NUMBER?e.Value.startsWith(String(t.Value)):s===i.LEAF_NUMBER&&u===i.LEAF_NUMBER?String(e.Value).startsWith(String(t.Value)):s===i.LEAF_STRING_NONE&&u===i.LEAF_STRING_NONE}(e,t);case o.REGEX:return function(e,t){function n(e,t){var n=t;return t.match(/^\/.+\/$/)&&(n=t.substring(1,t.length-1)),!!e.match(new RegExp(n))}return s===i.LEAF_STRING&&u===i.LEAF_STRING?n(e.Value,t.Value):s===i.LEAF_NUMBER&&u===i.LEAF_STRING?n(String(e.Value),t.Value):s===i.LEAF_STRING&&u===i.LEAF_NUMBER?n(e.Value,String(t.Value)):s===i.LEAF_NUMBER&&u===i.LEAF_NUMBER?n(String(e.Value),String(t.Value)):s===i.LEAF_STRING_LIST&&u===i.LEAF_STRING?e.Value.some((function(e){return n(e,t.Value)})):s===i.LEAF_STRING_LIST&&u===i.LEAF_NUMBER&&e.Value.some((function(e){return n(e,String(t.Value))}))}(e,t);case o.HAS_BEEN_TAKEN:return w(e);case o.HAS_NOT_BEEN_TAKEN:return!w(e);case o.HAS_BEEN_SEEN:return h(e,t);case o.HAS_NOT_BEEN_SEEN:return!h(e,t)}function c(e,t){if(s===i.LEAF_STRING&&u===i.LEAF_STRING)return e.Value===t.Value;if(s===i.LEAF_NUMBER&&u===i.LEAF_STRING)return String(e.Value)===t.Value;if(s===i.LEAF_STRING&&u===i.LEAF_NUMBER)return e.Value===String(t.Value);if(s===i.LEAF_NUMBER&&u===i.LEAF_NUMBER)return e.Value.eq(t.Value);if(s===i.LEAF_STRING_LIST&&u===i.LEAF_STRING){var n=e;return Object(Q.arrIncludes)(n.Value,t.Value)}return s===i.LEAF_STRING_LIST&&u===i.LEAF_NUMBER?(n=e,Object(Q.arrIncludes)(n.Value,String(t.Value))):s===i.LEAF_STRING_NONE&&u===i.LEAF_STRING_NONE}function d(e,t){return s===i.LEAF_STRING&&u===i.LEAF_STRING?e.Value>t.Value:s===i.LEAF_NUMBER&&u===i.LEAF_NUMBER&&e.Value.gt(t.Value)}function p(e,t){return s===i.LEAF_STRING&&u===i.LEAF_STRING?e.Value>=t.Value:s===i.LEAF_NUMBER&&u===i.LEAF_NUMBER&&e.Value.gte(t.Value)}function f(e){return 0!==String(e.Value).trim().length}function g(e,t){function n(e,t){return Object(Q.strIncludes)(e.toLowerCase(),t.toLowerCase())}return s===i.LEAF_STRING&&u===i.LEAF_STRING?n(e.Value,t.Value):s===i.LEAF_NUMBER&&u===i.LEAF_STRING?n(String(e.Value),t.Value):s===i.LEAF_STRING&&u===i.LEAF_NUMBER?n(e.Value,String(t.Value)):s===i.LEAF_NUMBER&&u===i.LEAF_NUMBER?n(String(e.Value),String(t.Value)):s===i.LEAF_STRING_LIST&&u===i.LEAF_STRING?e.Value.some((function(e){return n(e,t.Value)})):s===i.LEAF_STRING_LIST&&u===i.LEAF_NUMBER?e.Value.some((function(e){return n(e,String(t.Value))})):s===i.LEAF_STRING_NONE&&u===i.LEAF_STRING_NONE}function w(e){try{return!A()(e.Value).eq(0)}catch(e){window.QSI.dbg.e(e)}}function h(e,t){var n=new l(function(e){return Date.now()/1e3-e}(t.Value));return s===i.LEAF_NUMBER&&u===i.LEAF_NUMBER?e.Value>=n.Value:(s===i.LEAF_NUMBER_NONE&&i.LEAF_NUMBER,!1)}},this.evaluateConjunctionNode=function(t,n,i){var o=t.Operator,a=e.evaluateLogicTreeHelper(t.Left,n,i);i.push(o);var s=e.evaluateLogicTreeHelper(t.Right,n,i);switch(o){case r.AND:return a&&s;case r.OR:return a||s}},this.evaluateLogicNode=function(t,n){var i,r,o,a=t;switch(t.LogicType){case s.ACTION_SET_LOGIC:var u=t;return e.convertToLeafValueNode((o=u,window.QSI.history.getActionSetHistory(o.ActionSetID,!o.InCurrentSession)));case s.ADOBE_ANALYTICS:return e.convertToLeafValueNode(function(e){return window.QSI.EmbeddedData.getSiteCatalystValue(e.Expression)}(a));case s.COOKIE:var c=function(e){return window.QSI.cookie.get(e.Expression)}(a);return null===c?new p:e.convertToLeafValueNode(c);case s.EVENT_TRACKING:return e.convertToLeafValueNode(function(e){return window.QSI.EventTracker.get(e.Expression)}(a));case s.HTML_ON_SITE:return e.convertToLeafValueNode(function(e){return window.QSI.EmbeddedData.getHTMLFromDOM(e.Expression)}(a));case s.INTERCEPT_LOGIC:if(u=t,!(h=y()))return new d;var w=h[u.InterceptIDs[0]];return new l(A()(w));case s.INTERCEPT_LOGIC_ANY:case s.INTERCEPT_LOGIC_NONE:var h;if(u=t,!(h=y()))return new d;var S=Object.keys(h);try{for(var I=O(S),v=I.next();!v.done;v=I.next()){var m=v.value;Object(Q.arrIncludes)(u.InterceptIDs,m)||delete h[m]}}catch(e){i={error:e}}finally{try{v&&!v.done&&(r=I.return)&&r.call(I)}finally{if(i)throw i.error}}var T=Object.values(h);return new l(Math.max.apply(Math,_([],R(T))));case s.DATE_TIME_LOGIC:return new f(function(e){var t=new Date(Date.now());switch(e.Expression){case"Date":var n=t.getMonth()+1,i=t.getDate(),r=t.getFullYear();return new Date([r,(n<10?"0":"")+n,(i<10?"0":"")+i].join("-")).getTime().toString();case"Day":return["Sun","Mon","Tue","Wed","Thu","Fri","Sat"][t.getDay()];case"Time":var o=t.getHours(),a=t.getMinutes();return(o<10?"0":"")+o+":"+(a<10?"0":"")+a}}(a));case s.JAVASCRIPT_EXPRESSION:return e.convertToLeafValueNode(function(e){return window.QSI.EmbeddedData.getJavaScriptValue(e.Expression)}(a),n);case s.SITE_HISTORY:return new g(window.QSI.history.get().split("|").filter((function(e){return""!==e})).map((function(e){return e.replace(new RegExp("~[0-9]+$"),"")})));case s.PAGE_COUNT:return e.convertToLeafValueNode(function(e){var t=window.QSI.history.getPageCount();switch(e.Expression){case"Total":return t.total;case"Unique":return t.unique}}(a));case s.REFERRER:return e.convertToLeafValueNode(function(e){var t=window.QSI.history;switch(e.Expression){case"Site":return t.getSiteReferrer();case"Page":return t.getPageReferrer()}}(a));case s.RESOLUTION:return e.convertToLeafValueNode(function(e){var t=window.QSI.util,n=t.getPageSize(),i=t.getScreenRes();switch(e.Expression){case"ScreenHeight":return i.height;case"ScreenWidth":return i.width;case"BrowserHeight":return n.height;case"BrowserWidth":return n.width}}(a));case s.SEARCH_TERM:return e.convertToLeafValueNode(window.QSI.history.getSearch());case s.TIME_ON_SITE:return e.convertToLeafValueNode(function(e){var t=window.QSI.history.getTimeOnSite().split("|");switch(e.Expression){case"Focused":return Number(t[1]);case"Spent":return Number(t[0])}}(a));case s.GOOGLE_DATA_LAYER:return function(){var e=window.QSI.DataLayerHelper;if(null==e){var t=window.QSI.config.gtmContainerID;if(!t)throw new Error("Google Tag Manager container ID was not provided and is needed for the Qualtrics data layer integration");if(!window.google_tag_manager||!window.google_tag_manager[t])throw new Error("Google Tag Manager container '"+t+"' does not exist");var n=window.google_tag_manager[t].dataLayer.name;if(!n)throw new Error("Google Tag Manager container '"+t+"' does not have a data layer name");var i=window[n];e=new E(i),window.QSI.DataLayerHelper=e}window.QSI.InterceptReevaluator.isActive()||window.QSI.InterceptReevaluator.activate()}(),e.convertToLeafValueNode(b(a));case s.RAGE_CLICK:case s.MOUSE_THRASH:case s.ERROR_CLICK:case s.DEAD_CLICK:return e.handleFrustrationLogicNode(a)}},this.handleFrustrationLogicNode=function(t){window.QSI.foundFrustrationLogic=!0;var n=new p;return window.QSI.global.featureFlags["DX.RealTimeTriggeredIntercepts"]?(window.QSI.InterceptReevaluator.addInterceptWithFrustrationSignalLogic(e.interceptID),!1===window.QSI.InterceptReevaluator.isActive()&&window.QSI.SR&&window.QSI.SR.isOn()&&window.QSI.InterceptReevaluator.activate(),window.QSI.InterceptReevaluator.isActive()&&window.QSI.SR&&window.QSI.SR.isOn()?e.convertToLeafValueNode(function(e){if(window.QSI&&window.QSI.SR&&window.QSI.SR.signalCounts)return window.QSI.SR.getSignalCount(e.Expression)}(t)):n):n},this.evaluateValueNode=function(t){return e.convertToLeafValueNode(t.Value)},this.convertToLeafValueNode=function(t,n){if("string"==typeof t&&""===t||Array.isArray(t)&&0===t.length)return new p;if(Object(Q.isObjLiteral)(t)||null===t||"boolean"==typeof t)return new f(JSON.stringify(t));try{isNaN(Number(t))||String(t)===String(Number(t))||(a=window.QSI.dbg).log('Intercept" '+e.interceptID+" is targeting against a large number: "+t);var i=A()(t),r=n===o.REGEX&&String(t)!==String(Number(t));if(!isNaN(Number(i))&&!r)return new l(i)}catch(e){var a=window.QSI.dbg;e&&e.message&&!Object(Q.strIncludes)(e.message,"[big.js] Invalid number")&&a.e(e)}var s=String(t);return new f(s)},this.getShouldShow=function(e){var t=window.QSI.cookie,n=!0,i=!0,r=!1;return e.HideOnCookiesDisabled&&!t.areCookiesEnabled()&&(n=!1),e.HideOnLocalStorageDisabled&&!Object(T.isLocalStorageEnabled)()&&(i=!1),e.PreventRepeatedDisplay&&("0"!==e.PreventRepeatedDisplay&&null!==t.get("QSI_"+e.InterceptID+"_intercept")?r=!0:t.erase("QSI_"+e.InterceptID+"_intercept")),{CookiesEnabled:n,LocalStorageEnabled:i,PreventRepeatedDisplay:r}},this.shouldShow=function(t,n){var i=e.getShouldShow(t);return n.cookiesEnabled=i.CookiesEnabled,n.localStorageEnabled=i.LocalStorageEnabled,n.preventRepeatedDisplay=i.PreventRepeatedDisplay,i.CookiesEnabled&&i.LocalStorageEnabled&&!i.PreventRepeatedDisplay}};window.QSI.ClientSideTargeting=new D},function(e,t,n){"use strict";n.r(t);var i=function(e,t){this.payload=t,this.type=e};n.d(t,"addPopunderEmbeddedDataHandler",(function(){return r})),n.d(t,"setEmbeddedData",(function(){return a})),n.d(t,"updatePopunderEDCallback",(function(){return s})),n.d(t,"persistEDToSurvey",(function(){return u}));var r=function(e){var t=window.QSI,n=t.util,i=t.windowHandler,r=t.dbg;n.observe(window,"beforeunload",e,!0);try{i.setupWindowHandles()}catch(e){r.e(e)}},o=function(e,t){if("string"==typeof e&&"string"==typeof t){var n=window.QSI.windowHandler,r=n.getWin(t);if(r){var o=new i("setTargetUrlInPlaceholderWindow",e),a=JSON.stringify(o),s=n.getWindowOrigin(window);r.postMessage(a,s)}}},a=function(e,t,n,r){if(void 0!==e&&void 0!==t&&"string"==typeof n&&"string"==typeof r){var o=window.QSI,a=o.windowHandler,s=o.dbg,u=a.getWin(n);if(u){var c=new i("setEmbeddedData",{key:e,value:t}),d=JSON.stringify(c);if(/targetwindow/.test(n))u.postMessage(d,r);else try{var l=u.document.getElementById("PopUnderTargetFrame");if(l&&l.contentWindow)l.contentWindow.postMessage(d,r)}catch(e){s.e(e)}}}},s=function(){var e=window.QSI.dbg;try{var t=window.QSI.windowHandler;t.removeClosedWindowHandles();var n=t.getOptInIDsAndWindowNames()||{},i=t.getOptInIDsAndTargetOrigins()||{};for(var r in n)if(Object.prototype.hasOwnProperty.call(n,r)){var s=n[r],u=i[r]||"*",c=window.QSI.EmbeddedData,d=c.getEmbeddedData(r);if(!d||window.QSI.util.isObjectEmpty(d))continue;if(window.QSI.reg[r]&&/placeholderWindow/.test(s)){var l=window.QSI.reg[r].getTarget();return void o(l,s)}var p=c.surveyOpenED[r];for(var f in d)!Object.prototype.hasOwnProperty.call(d,f)||null!=p&&p[f]==d[f]||a(f,d[f],s,u)}}catch(t){e.e(t)}},u=function(e,t,n){var r=window.QSI,o=r.dbg,a=r.global,s=new i("setEmbeddedData",{key:t,value:n});try{e instanceof HTMLIFrameElement?e.contentWindow.postMessage(s,a.brandBaseUrl||"/"):e.postMessage(s,a.brandBaseUrl||"/")}catch(e){o.e(e)}}}]);
} catch(e) {
  if (typeof QSI !== 'undefined' && QSI.dbg && QSI.dbg.e) {
    QSI.dbg.e(e);
  }
}