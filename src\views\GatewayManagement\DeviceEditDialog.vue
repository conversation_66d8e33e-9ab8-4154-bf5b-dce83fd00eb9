<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import type { FormInstance } from 'element-plus';
import type { GatewayDevice } from '../../types/gateway';

// 属性
const props = defineProps<{
  visible: boolean;
  device?: GatewayDevice | null;
}>();

// 事件
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'submit', device: any): void;
}>();

// 表单引用
const formRef = ref<FormInstance>();
const loading = ref(false);

// 表单数据
const form = reactive<{
  name: string;
  ip: string;
  port: number;
  modelName: string;
  manufacturer: string;
  firmwareVersion: string;
  location: string;
  description: string;
}>({
  name: '',
  ip: '',
  port: 1883,
  modelName: '',
  manufacturer: '',
  firmwareVersion: '',
  location: '',
  description: '',
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
  ip: [
    { required: true, message: '请输入IP地址', trigger: 'blur' },
    { pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, message: 'IP地址格式不正确', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口范围为1-65535', trigger: 'blur' }
  ],
  modelName: [{ required: true, message: '请输入设备型号', trigger: 'blur' }],
  manufacturer: [{ required: true, message: '请输入制造商', trigger: 'blur' }],
};

// 计算属性 - 是否为编辑模式
const isEditing = computed(() => !!props.device?.id);

// 计算属性 - 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 监听设备数据变化
watch(() => props.device, (device) => {
  if (device) {
    // 编辑模式 - 填充表单数据
    form.name = device.name;
    form.ip = device.ip;
    form.port = device.port;
    form.modelName = device.modelName || '';
    form.manufacturer = device.manufacturer || '';
    form.firmwareVersion = device.firmwareVersion || '';
    form.location = device.location || '';
    form.description = device.description || '';
  } else {
    // 添加模式 - 重置表单
    resetForm();
  }
}, { immediate: true });

// 重置表单
function resetForm() {
  form.name = '';
  form.ip = '';
  form.port = 1883;
  form.modelName = '';
  form.manufacturer = '';
  form.firmwareVersion = '';
  form.location = '';
  form.description = '';
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return;
  
  try {
    loading.value = true;
    
    await formRef.value.validate();
    
    // 创建提交数据
    const submitData = {
      name: form.name,
      ip: form.ip,
      port: form.port,
      modelName: form.modelName,
      manufacturer: form.manufacturer,
      firmwareVersion: form.firmwareVersion,
      location: form.location,
      description: form.description,
    };
    
    // 如果是编辑模式，添加ID
    if (isEditing.value && props.device?.id) {
      Object.assign(submitData, { id: props.device.id });
    }
    
    // 提交
    emit('submit', submitData);
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <el-dialog
    :title="isEditing ? '编辑设备' : '添加设备'"
    v-model="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-position="top"
      label-width="100px"
    >
      <el-form-item label="设备名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入设备名称" />
      </el-form-item>
      
      <el-form-item label="IP地址" prop="ip">
        <el-input v-model="form.ip" placeholder="请输入IP地址" />
      </el-form-item>
      
      <el-form-item label="端口" prop="port">
        <el-input-number
          v-model="form.port"
          :min="1"
          :max="65535"
          :controls="false"
          placeholder="请输入端口号"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="设备型号" prop="modelName">
        <el-input v-model="form.modelName" placeholder="请输入设备型号" />
      </el-form-item>
      
      <el-form-item label="制造商" prop="manufacturer">
        <el-input v-model="form.manufacturer" placeholder="请输入制造商" />
      </el-form-item>
      
      <el-form-item label="固件版本" prop="firmwareVersion">
        <el-input v-model="form.firmwareVersion" placeholder="请输入固件版本" />
      </el-form-item>
      
      <el-form-item label="设备位置" prop="location">
        <el-input v-model="form.location" placeholder="请输入设备位置" />
      </el-form-item>
      
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          rows="3"
          placeholder="请输入设备描述"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.protocol-note {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style> 