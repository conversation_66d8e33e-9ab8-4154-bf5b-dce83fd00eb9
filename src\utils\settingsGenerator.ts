import type { 
  SystemSettings, 
  User, 
  Role, 
  Permission, 
  SystemLog, 
  SystemInfo 
} from '@/types'
import dayjs from 'dayjs'

/**
 * 设置数据生成器
 */
export class SettingsGenerator {
  private users: User[] = []
  private roles: Role[] = []
  private permissions: Permission[] = []
  private systemLogs: SystemLog[] = []

  constructor() {
    this.initializePermissions()
    this.initializeRoles()
    this.initializeUsers()
    this.generateSystemLogs()
  }

  /**
   * 获取默认系统设置
   */
  getDefaultSettings(): SystemSettings {
    return {
      general: {
        systemName: '智慧水务管理平台',
        companyName: '智慧水务科技有限公司',
        contactEmail: '<EMAIL>',
        contactPhone: '+86-************',
        timezone: 'Asia/Shanghai',
        dateFormat: 'YYYY-MM-DD',
        timeFormat: '24h',
        currency: 'CNY',
        dataRetentionDays: 365,
        autoRefreshInterval: 30,
        enableDebugMode: false
      },
      notification: {
        email: {
          enabled: true,
          smtpServer: 'smtp.example.com',
          smtpPort: 587,
          smtpSecurity: 'tls',
          username: '<EMAIL>',
          password: '********',
          fromAddress: '<EMAIL>',
          fromName: '智慧水务平台',
          recipients: ['<EMAIL>', '<EMAIL>']
        },
        sms: {
          enabled: false,
          provider: 'aliyun',
          apiKey: '',
          apiSecret: '',
          defaultRecipients: ['+86-138****1234', '+86-139****5678']
        },
        push: {
          enabled: true,
          webPush: true,
          mobile: false,
          desktop: true
        },
        sound: {
          enabled: true,
          volume: 80,
          criticalAlertSound: 'critical.mp3',
          warningAlertSound: 'warning.mp3',
          infoAlertSound: 'info.mp3'
        }
      },
      security: {
        passwordPolicy: {
          minLength: 8,
          requireUppercase: true,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: true,
          passwordExpiry: 90,
          preventReuse: 5
        },
        sessionTimeout: 480, // 8小时
        maxLoginAttempts: 5,
        lockoutDuration: 30, // 30分钟
        twoFactorAuth: false,
        ipWhitelist: ['***********/24', '10.0.0.0/8'],
        auditLog: true,
        encryptionEnabled: true
      },
      backup: {
        autoBackup: true,
        backupInterval: 'daily',
        backupTime: '02:00',
        retentionPeriod: 30,
        backupLocation: '/backup',
        includeFiles: true,
        includeDatabase: true,
        includeSettings: true,
        compression: true,
        encryption: true
      },
      theme: {
        mode: 'light',
        primaryColor: '#409EFF',
        accentColor: '#67C23A',
        fontSize: 'medium',
        compactMode: false,
        animations: true,
        customCss: ''
      },
      language: {
        locale: 'zh-CN',
        dateLocale: 'zh-CN',
        numberFormat: 'zh-CN',
        rtlSupport: false
      }
    }
  }

  /**
   * 初始化权限
   */
  private initializePermissions() {
    this.permissions = [
      // 系统管理
      { id: 'system.view', name: '查看系统信息', description: '查看系统状态和信息', category: '系统管理', resource: 'system', actions: ['read'] },
      { id: 'system.config', name: '系统配置', description: '修改系统配置', category: '系统管理', resource: 'system', actions: ['read', 'write'] },
      { id: 'system.backup', name: '备份管理', description: '执行备份和恢复操作', category: '系统管理', resource: 'system', actions: ['backup', 'restore'] },
      
      // 用户管理
      { id: 'user.view', name: '查看用户', description: '查看用户列表和信息', category: '用户管理', resource: 'user', actions: ['read'] },
      { id: 'user.create', name: '创建用户', description: '创建新用户账户', category: '用户管理', resource: 'user', actions: ['create'] },
      { id: 'user.edit', name: '编辑用户', description: '修改用户信息', category: '用户管理', resource: 'user', actions: ['update'] },
      { id: 'user.delete', name: '删除用户', description: '删除用户账户', category: '用户管理', resource: 'user', actions: ['delete'] },
      
      // 设备管理
      { id: 'device.view', name: '查看设备', description: '查看设备状态和信息', category: '设备管理', resource: 'device', actions: ['read'] },
      { id: 'device.control', name: '设备控制', description: '控制设备运行', category: '设备管理', resource: 'device', actions: ['control'] },
      { id: 'device.config', name: '设备配置', description: '配置设备参数', category: '设备管理', resource: 'device', actions: ['config'] },
      
      // 预警管理
      { id: 'warning.view', name: '查看预警', description: '查看预警信息', category: '预警管理', resource: 'warning', actions: ['read'] },
      { id: 'warning.handle', name: '处理预警', description: '处理和解决预警', category: '预警管理', resource: 'warning', actions: ['handle'] },
      { id: 'warning.config', name: '预警配置', description: '配置预警规则', category: '预警管理', resource: 'warning', actions: ['config'] },
      
      // 数据分析
      { id: 'analytics.view', name: '查看分析', description: '查看数据分析报告', category: '数据分析', resource: 'analytics', actions: ['read'] },
      { id: 'analytics.export', name: '导出数据', description: '导出分析数据', category: '数据分析', resource: 'analytics', actions: ['export'] }
    ]
  }

  /**
   * 初始化角色
   */
  private initializeRoles() {
    this.roles = [
      {
        id: 'admin',
        name: '系统管理员',
        description: '拥有所有权限的超级管理员',
        permissions: this.permissions.map(p => p.id),
        isSystem: true,
        createdAt: dayjs().subtract(30, 'day').toISOString(),
        updatedAt: dayjs().toISOString()
      },
      {
        id: 'operator',
        name: '操作员',
        description: '负责日常设备操作和监控',
        permissions: [
          'device.view', 'device.control', 'warning.view', 'warning.handle',
          'analytics.view', 'system.view'
        ],
        isSystem: true,
        createdAt: dayjs().subtract(25, 'day').toISOString(),
        updatedAt: dayjs().subtract(5, 'day').toISOString()
      },
      {
        id: 'maintenance',
        name: '维护人员',
        description: '负责设备维护和故障处理',
        permissions: [
          'device.view', 'device.config', 'warning.view', 'warning.handle',
          'warning.config', 'analytics.view'
        ],
        isSystem: true,
        createdAt: dayjs().subtract(20, 'day').toISOString(),
        updatedAt: dayjs().subtract(3, 'day').toISOString()
      },
      {
        id: 'viewer',
        name: '查看者',
        description: '只能查看数据，无操作权限',
        permissions: [
          'device.view', 'warning.view', 'analytics.view', 'system.view'
        ],
        isSystem: true,
        createdAt: dayjs().subtract(15, 'day').toISOString(),
        updatedAt: dayjs().subtract(1, 'day').toISOString()
      }
    ]
  }

  /**
   * 初始化用户
   */
  private initializeUsers() {
    this.users = [
      {
        id: 'user_001',
        username: 'admin',
        email: '<EMAIL>',
        fullName: '系统管理员',
        role: 'admin',
        department: '信息技术部',
        phone: '+86-138-0000-0001',
        avatar: '/avatars/admin.jpg',
        status: 'active',
        lastLogin: dayjs().subtract(2, 'hour').toISOString(),
        createdAt: dayjs().subtract(30, 'day').toISOString(),
        updatedAt: dayjs().toISOString(),
        permissions: this.roles.find(r => r.id === 'admin')?.permissions || []
      },
      {
        id: 'user_002',
        username: 'operator01',
        email: '<EMAIL>',
        fullName: '张操作员',
        role: 'operator',
        department: '运行部',
        phone: '+86-138-0000-0002',
        avatar: '/avatars/operator01.jpg',
        status: 'active',
        lastLogin: dayjs().subtract(1, 'hour').toISOString(),
        createdAt: dayjs().subtract(25, 'day').toISOString(),
        updatedAt: dayjs().subtract(1, 'day').toISOString(),
        permissions: this.roles.find(r => r.id === 'operator')?.permissions || []
      },
      {
        id: 'user_003',
        username: 'maintenance01',
        email: '<EMAIL>',
        fullName: '李维护员',
        role: 'maintenance',
        department: '维护部',
        phone: '+86-138-0000-0003',
        avatar: '/avatars/maintenance01.jpg',
        status: 'active',
        lastLogin: dayjs().subtract(4, 'hour').toISOString(),
        createdAt: dayjs().subtract(20, 'day').toISOString(),
        updatedAt: dayjs().subtract(2, 'day').toISOString(),
        permissions: this.roles.find(r => r.id === 'maintenance')?.permissions || []
      },
      {
        id: 'user_004',
        username: 'viewer01',
        email: '<EMAIL>',
        fullName: '王查看员',
        role: 'viewer',
        department: '质量部',
        phone: '+86-138-0000-0004',
        avatar: '/avatars/viewer01.jpg',
        status: 'active',
        lastLogin: dayjs().subtract(6, 'hour').toISOString(),
        createdAt: dayjs().subtract(15, 'day').toISOString(),
        updatedAt: dayjs().subtract(3, 'day').toISOString(),
        permissions: this.roles.find(r => r.id === 'viewer')?.permissions || []
      },
      {
        id: 'user_005',
        username: 'operator02',
        email: '<EMAIL>',
        fullName: '赵操作员',
        role: 'operator',
        department: '运行部',
        phone: '+86-138-0000-0005',
        avatar: '/avatars/operator02.jpg',
        status: 'inactive',
        lastLogin: dayjs().subtract(2, 'day').toISOString(),
        createdAt: dayjs().subtract(10, 'day').toISOString(),
        updatedAt: dayjs().subtract(1, 'day').toISOString(),
        permissions: this.roles.find(r => r.id === 'operator')?.permissions || []
      }
    ]
  }

  /**
   * 生成系统日志
   */
  private generateSystemLogs() {
    const logCategories = ['system', 'user', 'device', 'warning', 'security']
    const logLevels: Array<'debug' | 'info' | 'warn' | 'error' | 'fatal'> = ['debug', 'info', 'warn', 'error', 'fatal']
    
    for (let i = 0; i < 100; i++) {
      const timestamp = dayjs().subtract(Math.random() * 7, 'day')
      const level = logLevels[Math.floor(Math.random() * logLevels.length)]
      const category = logCategories[Math.floor(Math.random() * logCategories.length)]
      const user = this.users[Math.floor(Math.random() * this.users.length)]
      
      this.systemLogs.push({
        id: `log_${timestamp.valueOf()}_${i}`,
        timestamp: timestamp.toISOString(),
        level,
        category,
        message: this.generateLogMessage(category, level),
        details: {
          category,
          level,
          source: 'system'
        },
        userId: Math.random() > 0.3 ? user.id : undefined,
        ip: `192.168.1.${Math.floor(Math.random() * 254) + 1}`,
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      })
    }
    
    // 按时间倒序排列
    this.systemLogs.sort((a, b) => dayjs(b.timestamp).valueOf() - dayjs(a.timestamp).valueOf())
  }

  /**
   * 生成日志消息
   */
  private generateLogMessage(category: string, level: string): string {
    const messages = {
      system: {
        info: ['系统启动成功', '配置更新完成', '定时任务执行', '数据备份完成'],
        warn: ['磁盘空间不足', '内存使用率过高', '网络连接不稳定', '服务响应缓慢'],
        error: ['数据库连接失败', '文件读取错误', '服务启动失败', '配置文件损坏'],
        fatal: ['系统崩溃', '数据丢失', '安全漏洞', '服务不可用']
      },
      user: {
        info: ['用户登录成功', '用户注销', '密码修改', '权限更新'],
        warn: ['登录失败次数过多', '密码即将过期', '异常登录地点', '权限不足'],
        error: ['用户认证失败', '账户被锁定', '权限验证错误', '用户数据异常']
      },
      device: {
        info: ['设备状态更新', '参数配置成功', '设备连接正常', '数据采集完成'],
        warn: ['设备响应缓慢', '参数超出范围', '连接不稳定', '数据异常'],
        error: ['设备连接失败', '控制指令失败', '数据采集错误', '设备故障']
      },
      warning: {
        info: ['预警规则更新', '预警处理完成', '规则配置成功', '通知发送成功'],
        warn: ['预警数量增加', '处理时间过长', '规则冲突', '通知发送失败'],
        error: ['预警系统故障', '规则执行错误', '通知系统异常', '数据处理失败']
      },
      security: {
        info: ['安全检查通过', '权限验证成功', '审计日志记录', '安全策略更新'],
        warn: ['可疑登录行为', '权限异常使用', '安全策略违规', 'IP地址异常'],
        error: ['安全验证失败', '未授权访问', '数据泄露风险', '安全漏洞发现'],
        fatal: ['系统被攻击', '数据被篡改', '权限被提升', '安全防护失效']
      }
    }
    
    const categoryMessages = messages[category as keyof typeof messages]
    if (!categoryMessages) return '未知日志消息'
    
    const levelMessages = categoryMessages[level as keyof typeof categoryMessages] || categoryMessages.info
    return levelMessages[Math.floor(Math.random() * levelMessages.length)]
  }

  /**
   * 获取系统信息
   */
  getSystemInfo(): SystemInfo {
    return {
      version: '2.1.0',
      buildDate: '2024-01-15',
      environment: 'production',
      uptime: Math.floor(Math.random() * 30) + 1, // 1-30天
      cpuUsage: Math.random() * 100,
      memoryUsage: Math.random() * 100,
      diskUsage: Math.random() * 100,
      networkStatus: 'connected',
      databaseStatus: 'healthy',
      lastBackup: dayjs().subtract(1, 'day').toISOString(),
      activeUsers: this.users.filter(u => u.status === 'active').length,
      totalDevices: 12,
      totalWarnings: 45
    }
  }

  // Getter方法
  getUsers(): User[] { return this.users }
  getRoles(): Role[] { return this.roles }
  getPermissions(): Permission[] { return this.permissions }
  getSystemLogs(): SystemLog[] { return this.systemLogs }
}

// 导出单例实例
export const settingsGenerator = new SettingsGenerator()
