<template>
  <div class="intelligent-optimization">
    <!-- 参数配置面板 -->
    <el-collapse v-model="activeCollapse" class="config-collapse">
      <el-collapse-item title="水泵参数配置" name="config">
        <PumpParameterInput @parameters-updated="onParametersUpdated" />
      </el-collapse-item>
    </el-collapse>

    <!-- 算法比较面板 -->
    <el-card v-if="showComparison" class="comparison-panel">
      <template #header>
        <div class="card-header">
          <el-icon><DataAnalysis /></el-icon>
          <span>算法性能比较</span>
          <el-button size="small" @click="showComparison = false">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </template>

      <AlgorithmComparison :comparison-data="pumpStore.algorithmComparison" />
    </el-card>

    <el-card class="control-panel">
      <template #header>
        <div class="card-header">
          <el-icon><MagicStick /></el-icon>
          <span>智能寻优控制面板</span>
          <div class="header-actions">
            <el-button size="small" @click="compareAlgorithms" :loading="loading">
              <el-icon><DataAnalysis /></el-icon>
              算法比较
            </el-button>
            <el-dropdown @command="handleAlgorithmChange">
              <el-button size="small">
                当前算法: {{ algorithmNames[pumpStore.fittingConfig.algorithm.type] }}
                <el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="least-squares">最小二乘法</el-dropdown-item>
                  <el-dropdown-item command="polynomial">多项式拟合</el-dropdown-item>
                  <el-dropdown-item command="neural-network">神经网络</el-dropdown-item>
                  <el-dropdown-item command="spline">样条插值</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="目标流量 (m³/h)">
            <el-input-number
              v-model="inputParams.Q"
              :min="0"
              :max="2000"
              :step="10"
              @change="handleInputChange"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="目标扬程 (m)">
            <el-input-number
              v-model="inputParams.H"
              :min="0"
              :max="60"
              :step="0.1"
              :precision="2"
              @change="handleInputChange"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="操作">
            <el-button
              type="primary"
              @click="calculateOptimization"
              :loading="loading"
              :disabled="!canOptimize"
            >
              <el-icon><MagicStick /></el-icon>
              开始寻优
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 拟合质量指标 -->
      <div v-if="fittingResults.QH" class="fitting-quality">
        <el-divider content-position="left">拟合质量评估</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="quality-item">
              <h4>Q-H曲线</h4>
              <el-progress :percentage="Math.round(fittingResults.QH.r2Score * 100)" />
              <div class="metrics">
                <span>R² = {{ fittingResults.QH.r2Score.toFixed(4) }}</span>
                <span>MSE = {{ fittingResults.QH.mse.toFixed(4) }}</span>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="quality-item">
              <h4>Q-η曲线</h4>
              <el-progress :percentage="Math.round((fittingResults.QETA?.r2Score || 0) * 100)" />
              <div class="metrics">
                <span>R² = {{ fittingResults.QETA?.r2Score?.toFixed(4) || '0.0000' }}</span>
                <span>MSE = {{ fittingResults.QETA?.mse?.toFixed(4) || '0.0000' }}</span>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="quality-item">
              <h4>Q-P曲线</h4>
              <el-progress :percentage="Math.round((fittingResults.QP?.r2Score || 0) * 100)" />
              <div class="metrics">
                <span>R² = {{ fittingResults.QP?.r2Score?.toFixed(4) || '0.0000' }}</span>
                <span>MSE = {{ fittingResults.QP?.mse?.toFixed(4) || '0.0000' }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 优化结果显示 -->
      <div v-if="optimizationResult" class="optimization-result">
        <el-divider content-position="left">智能寻优结果</el-divider>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="最优频率" :value="optimizationResult.frequency" suffix="Hz" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="预期效率" :value="optimizationResult.efficiency" suffix="%" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="预期功率" :value="optimizationResult.power" suffix="kW" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="节能率" :value="energySavingRate" suffix="%" />
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 图表显示区域 -->
    <el-card class="chart-container">
      <template #header>
        <div class="card-header">
          <el-icon><TrendCharts /></el-icon>
          <span>水泵特性曲线</span>
          <div class="chart-controls">
            <el-radio-group v-model="chartType" size="small">
<el-radio-button value="all">全部曲线</el-radio-button>
<el-radio-button value="qh">Q-H曲线</el-radio-button>
<el-radio-button value="qeta">Q-η曲线</el-radio-button>
<el-radio-button value="qp">Q-P曲线</el-radio-button>
</el-radio-group>
          </div>
        </div>
      </template>
      
      <div ref="chartRef" class="chart" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, nextTick } from 'vue'
import { MagicStick, TrendCharts, DataAnalysis, Close, ArrowDown } from '@element-plus/icons-vue'
import { usePumpStore } from '@/stores/pump'
import { initChart, updateChart } from './chart-config'
import PumpParameterInput from '@/components/PumpParameterInput.vue'
import AlgorithmComparison from './AlgorithmComparison.vue'
import type { OptimizationResult, PumpParameters, PumpData, CurveFittingConfig, AlgorithmType } from '@/types'
import { ElMessage } from 'element-plus'

// 响应式数据
const chartRef = ref<HTMLElement>()
const loading = ref(false)
const chartType = ref('all')
const activeCollapse = ref(['config'])
const showComparison = ref(false)
const inputParams = ref({
  Q: 800,
  H: 35
})

// 算法名称映射
const algorithmNames = {
  'least-squares': '最小二乘法',
  'polynomial': '多项式拟合',
  'neural-network': '神经网络',
  'spline': '样条插值'
}

// 使用 Pinia store
const pumpStore = usePumpStore()

// 计算属性
const optimizationResult = computed(() => pumpStore.optimizationResult)
const fittingResults = computed(() => pumpStore.fittingResults)
const canOptimize = computed(() => inputParams.value.Q > 0 && inputParams.value.H > 0)

// 节能率计算
const energySavingRate = computed(() => {
  if (!optimizationResult.value) return 0
  const basePower = 100 // 基准功率
  const optimizedPower = optimizationResult.value.power
  return Math.max(0, ((basePower - optimizedPower) / basePower * 100))
})

// 图表实例
let chartInstance: any = null

// 方法
const onParametersUpdated = (params: PumpParameters, data: PumpData, config: CurveFittingConfig) => {
  pumpStore.updatePumpParameters(params, data, config)
  ElMessage.success('水泵参数已更新，正在重新拟合曲线...')
  updateChartData()
}

const handleAlgorithmChange = async (algorithm: AlgorithmType) => {
  loading.value = true
  try {
    pumpStore.fittingConfig.algorithm.type = algorithm
    await pumpStore.fitCurves(algorithm)
    updateChartData()
    ElMessage.success(`已切换到${algorithmNames[algorithm]}`)
  } catch (error) {
    ElMessage.error('算法切换失败')
  } finally {
    loading.value = false
  }
}

const compareAlgorithms = async () => {
  loading.value = true
  try {
    await pumpStore.compareAlgorithms()
    showComparison.value = true
    ElMessage.success('算法比较完成')
  } catch (error) {
    ElMessage.error('算法比较失败')
  } finally {
    loading.value = false
  }
}

const handleInputChange = () => {
  pumpStore.updateInput(inputParams.value)
  updateChartData()
}

const calculateOptimization = async () => {
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟计算时间
    pumpStore.calculateOptimization(inputParams.value.Q, inputParams.value.H)
    updateChartData()
  } finally {
    loading.value = false
  }
}

const updateChartData = () => {
  if (chartInstance) {
    const curveData = pumpStore.getCurveData
    if (curveData) {
      updateChart(chartInstance, curveData, chartType.value, inputParams.value, optimizationResult.value)
    }
  }
}

// 监听图表类型变化
watch(chartType, () => {
  updateChartData()
})

// 生命周期
onMounted(async () => {
  // 初始化数据
  pumpStore.initCurveParams()
  
  await nextTick()
  
  // 初始化图表
  if (chartRef.value) {
    chartInstance = initChart(chartRef.value)
    updateChartData()
  }
})
</script>

<style lang="scss" scoped>
.intelligent-optimization {
  .config-collapse {
    margin-bottom: 20px;

    :deep(.el-collapse-item__header) {
      background: var(--el-fill-color-light);
      font-weight: 600;
    }
  }

  .comparison-panel {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      > div:first-child {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
      }
    }
  }

  .control-panel {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      > div:first-child {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
      }

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }

    .fitting-quality {
      margin-top: 20px;
      padding: 20px;
      background: var(--el-bg-color-page);
      border-radius: 8px;

      .quality-item {
        text-align: center;

        h4 {
          margin-bottom: 12px;
          color: var(--el-text-color-primary);
        }

        .metrics {
          margin-top: 8px;
          display: flex;
          justify-content: space-around;
          font-size: 12px;
          color: var(--el-text-color-regular);

          span {
            padding: 2px 6px;
            background: var(--el-fill-color-light);
            border-radius: 4px;
          }
        }
      }
    }

    .optimization-result {
      margin-top: 20px;
      padding: 20px;
      background: var(--el-bg-color-page);
      border-radius: 8px;
    }
  }

  .chart-container {
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      > div:first-child {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
      }
    }

    .chart {
      height: 600px;
      width: 100%;
    }
  }
}

:deep(.el-statistic__content) {
  font-size: 24px;
  font-weight: bold;
}

:deep(.el-statistic__title) {
  font-size: 14px;
  color: var(--el-text-color-regular);
}
</style>
