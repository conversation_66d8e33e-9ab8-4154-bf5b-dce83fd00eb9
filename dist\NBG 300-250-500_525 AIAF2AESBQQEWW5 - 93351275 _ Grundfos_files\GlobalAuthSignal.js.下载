var GlobalAuthSignal = new function () {

	this.startGlobalAuthentication = startGlobalAuthentication;
	this.globalLogout = globalLogout;
	this.globalLogin = globalLogin;
	this.isGloballyLoggedIn = isGloballyLoggedIn;
	this.getGlobalAuthSignalEnv = getGlobalAuthSignalEnv;
	this.getGlobalAuthSignalValue = getGlobalAuthSignalValue;

	var timerId;
	var config = false;
	var env = "PROD";
	var topbarVersion = "3.1.0";
	var cookieName = resolveCookieName();
	const FORCE_LOGOUT_KEY = "logouttam";
	const doNotForceLogoutLinks = [
		'https://app.grundfos.com/extranet',
		'https://tapp.grundfos.com/extranet',
		'https://sapp.grundfos.com/extranet',
		'https://extranet.grundfos.com/',
		'https://textranet.grundfos.com/',
		'https://sextranet.grundfos.com/',
		'https://app.grundfos.com/ordering',
		'https://tapp.grundfos.com/ordering',
		'https://sapp.grundfos.com/ordering',
	];

	console.log("Version: " + topbarVersion);


	function isGloballyLoggedIn(opts) {
		config = loadOptions(opts);
		cookieName = resolveCookieName();
		return checkGlobalLoginState();
	}

	function forceBackgroundLogout() {
		if (sessionStorage.getItem(FORCE_LOGOUT_KEY) != null) {
			sessionStorage.removeItem(FORCE_LOGOUT_KEY);
			window.location = "https://auth.grundfos.com/login/logout?redirTo=" + window.location.href;
		}
	}

	function startGlobalAuthentication(opts) {
		forceBackgroundLogout();
		// <div id="globalTopBar"> is the tag that shows the topbar dropdown
		// so if this does not exist, page is not using the dropdown, hence do not need to recheck
		var isGlobalTopBarExist = document.getElementById('globalTopBar');
		if (typeof (isGlobalTopBarExist) === 'undefined' || isGlobalTopBarExist == null) {
			return;
		}

		config = loadOptions(opts);
		cookieName = resolveCookieName();

		// if cookie happens to be not set, this will create one. Currently for eComm only since this issue is only found here.
		initializeEcommerceCookieValue(cookieName, config);

		if (getCookieValue(cookieName) === "0" && config.isLoggedIn()) {
			//Signal login success
			document.cookie = cookieName + "=1;" + getCookieDomain();
		} else if (getCookieValue(cookieName) === "2" && !config.isLoggedIn()) {
			//Signal logout success
			document.cookie = cookieName + "=3;" + getCookieDomain();
		}
		if (config.enableTriggerOfOtherLoginButtons()) {
			prepareTriggerOfOtherLoginButtons();
		}

		checkGlobalAuthState();

		// repeat with the interval of 4 seconds
		timerId = window.setInterval(checkGlobalAuthState, 3000);

	}

	function initializeEcommerceCookieValue(cookieName, config) {
		// if cookie happens to be not set, this will create one. Currently, for eComm only since this issue is only found here.
		function isCookieMissing(cookieValue) {
			return cookieValue === "" || cookieValue === "undefined" || cookieValue == null;
		}
		// if user has logged out previously, cookie Value is set to 3, and needed to be updated if it is logged in
		function isLoggedInAndCookieSetTo3(cookieValue) {
			return cookieValue === "3" && config.isLoggedIn();
		}

		if (config.appId.includes("eCommerce")) {
			var cookieValue = getCookieValue(cookieName);
			if (isCookieMissing(cookieValue) || isLoggedInAndCookieSetTo3(cookieValue)) {
				var cookieVal = cookieName + "=1;" + getCookieDomain();
				console.log(cookieVal)
				document.cookie = cookieVal;
			}
		}
	}

	function loadOptions(opts) {
		//Default values
		var options = {
			isLoggedIn: function () {
				return false;
			},
			logoutFunction: function (signoutUrl) {
				//Default behaviour - applications could override
				window.location.href =
					(signoutUrl && typeof signoutUrl === "string") ? signoutUrl : "/session/signout?redirectUrl=" + window.location.pathname;
			},
			//Default behaviour - applications could override this is the same as we have today in topbar
			loginFunction: function (signinUrl) {
				window.location.href =
					(signinUrl && typeof signinUrl === "string") ? signinUrl : "/session/signin?redirectUrl=" + window.location.pathname;
			},
			env: "PROD",
			enableTriggerOfOtherLoginButtons: function () {
				return true;
			},
			forceLogout: true
		};

		if (typeof opts === 'object') {
			if (typeof opts.isLoggedIn === "function") {
				options.isLoggedIn = opts.isLoggedIn;
			}
			if (typeof opts.getProfileInfo === "function") {
				options.getProfileInfo = opts.getProfileInfo;
			}
			if (typeof opts.logoutFunction === "function") {
				options.logoutFunction = opts.logoutFunction;
			}
			if (typeof opts.loginFunction === "function") {
				options.loginFunction = opts.loginFunction;
			}
			if (typeof opts.getProfileInfo === "function") {
				options.getProfileInfo = opts.getProfileInfo;
			}
			if (typeof opts.enableTriggerOfOtherLoginButtons === "function") {
				options.enableTriggerOfOtherLoginButtons = opts.enableTriggerOfOtherLoginButtons;
			}
		}

		if(opts.appId != null && opts.appId !== ""){
			options.appId = opts.appId;
		}
		for (const url of doNotForceLogoutLinks) {
			if (window.location.href.startsWith(url)) {
				options.forceLogout = false;
				break;
			}
		}

		return options;
	}

	function resolveCookieName() {
		return "GlobalAuthSignal";
	}

	function globalLogin() {
		//Stop timer
		clearInterval(timerId);

		//Signal login initiated
		document.cookie = cookieName + "=0;" + getCookieDomain();
		config.loginFunction();
	}

	function getCookieDomain() {
		//return "domain=.grundfos.com;path=/";
		var domain = window.location.hostname; // so we do not include any ports
		var domainExtension = "com";
		var temp = domain.substr(domain.lastIndexOf(".") + 1);
		var allowedExtensions = "|cn|";
		if (allowedExtensions.indexOf("|" + temp + "|") > -1) {
			domainExtension = temp;
		}

		return "domain=.grundfos." + domainExtension + ";path=/";
	}

	function globalLogout() {
		//Stop timer
		clearInterval(timerId);

		//Signal logout initated
		document.cookie = cookieName + "=2;" + getCookieDomain();

		//Will refresh again the page to
		if (config.forceLogout) sessionStorage.setItem(FORCE_LOGOUT_KEY, "1");

		config.logoutFunction();
	}

	function checkGlobalAuthState() {
		if (checkGlobalLoginState()) {
			//Login state detected - login application
			clearInterval(timerId);
			config.loginFunction();
		} else if (checkGlobalLogoutState()) {
			//Logout state detected - logout application
			clearInterval(timerId);
			config.logoutFunction();
		}
	}

	function checkGlobalLoginState() {
		return getCookieValue(cookieName) === "1" && !config.isLoggedIn();
	}

	function checkGlobalLogoutState() {
		return getCookieValue(cookieName) === "3" && config.isLoggedIn();
	}

	function getCookieValue(cname) {
		var name = cname + "=";
		var decodedCookie = decodeURIComponent(document.cookie);
		var ca = decodedCookie.split(';');
		for (var i = 0; i < ca.length; i++) {
			var c = ca[i];
			while (c.charAt(0) == ' ') {
				c = c.substring(1);
			}
			if (c.indexOf(name) == 0) {
				return c.substring(name.length, c.length);
			}
		}
		return "";
	}

	function getGlobalAuthSignalValue() {
		return getCookieValue(cookieName);
	}

	function getGlobalAuthSignalEnv() {
		return env;
	}

	function prepareTriggerOfOtherLoginButtons() {
		window.addEventListener('load', function () {
			// This will attach all anchor.href with this URL to triggering GlobalAuthSignal
			var loginB2CUrl = "https://login.grundfos.com/grundfosauth.onmicrosoft.com/";
			var loginB2CUrl2 = "https://grundfosauth.b2clogin.com/grundfosauth.onmicrosoft.com/";
			var loginClassicUrl = "https://auth.grundfos.com/login/";

			var anchors = document.getElementsByTagName("a");
			for (var i = 0; i < anchors.length; i++) {
				var url = anchors[i].href;
				if (url.indexOf(loginB2CUrl) !== -1 ||
					url.indexOf(loginB2CUrl2) !== -1 ||
					url.indexOf(loginClassicUrl) !== -1) {
					anchors[i].href = "#";
					if (url.indexOf("&showSignup=1") !== -1) {
						anchors[i].addEventListener("click", function () {
							GlobalTopBar.createAccount('https:\/\/auth.grundfos.com\/login\/register.jsp?lang=ENU&redirTo=' + window.location.href);
							return false;
						});
					} else {
						anchors[i].addEventListener("click", function () {
							GlobalAuthSignal.globalLogin();
							return false;
						}, false);
					}
				}
			}
		})


		/*$('a[href^="'+loginB2CUrl+'"], a[href^="'+loginB2CUrl2+'"], a[href^="'+loginClassicUrl+'"]')
			.on('click', function(e){
				e.preventDefault();
				// remove the href first, then redirect
				$(this).attr('href', '');
				GlobalAuthSignal.globalLogin();
			});*/
	}
};

