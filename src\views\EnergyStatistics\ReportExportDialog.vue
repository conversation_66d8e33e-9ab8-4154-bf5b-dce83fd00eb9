<template>
  <el-dialog
    v-model="visible"
    title="导出能耗报表"
    width="600px"
    :before-close="handleClose"
  >
    <el-form :model="config" label-width="120px" :rules="rules" ref="formRef">
      <el-form-item label="报表周期" prop="period">
        <el-select v-model="config.period" placeholder="请选择统计周期">
          <el-option label="日报表" value="day" />
          <el-option label="周报表" value="week" />
          <el-option label="月报表" value="month" />
          <el-option label="季度报表" value="quarter" />
          <el-option label="年度报表" value="year" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="时间范围" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="导出格式" prop="format">
        <el-radio-group v-model="config.format">
          <el-radio value="pdf">
            <el-icon><Document /></el-icon>
            PDF报告
          </el-radio>
          <el-radio value="excel">
            <el-icon><Grid /></el-icon>
            Excel表格
          </el-radio>
          <el-radio value="csv">
            <el-icon><Tickets /></el-icon>
            CSV数据
          </el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="报表内容">
        <el-checkbox-group v-model="contentOptions">
          <el-checkbox label="charts">包含图表</el-checkbox>
          <el-checkbox label="comparison">包含对比分析</el-checkbox>
          <el-checkbox label="trends">包含趋势分析</el-checkbox>
          <el-checkbox label="suggestions">包含优化建议</el-checkbox>
          <el-checkbox label="details">包含详细数据</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      
      <el-form-item label="报表模板">
        <el-select v-model="selectedTemplate" placeholder="选择报表模板">
          <el-option
            v-for="template in templates"
            :key="template.id"
            :label="template.name"
            :value="template.id"
          >
            <div class="template-option">
              <span>{{ template.name }}</span>
              <el-tag size="small" :type="template.type">{{ template.description }}</el-tag>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="邮件发送">
        <el-switch v-model="emailEnabled" />
        <div v-if="emailEnabled" class="email-config">
          <el-input
            v-model="emailAddress"
            placeholder="请输入邮箱地址"
            style="margin-top: 8px"
          >
            <template #prepend>收件人</template>
          </el-input>
          <el-input
            v-model="emailSubject"
            placeholder="邮件主题"
            style="margin-top: 8px"
          >
            <template #prepend>主题</template>
          </el-input>
        </div>
      </el-form-item>
    </el-form>
    
    <!-- 预览区域 -->
    <div class="preview-section">
      <h4>报表预览</h4>
      <div class="preview-content">
        <div class="preview-header">
          <h3>{{ previewTitle }}</h3>
          <p>{{ previewSubtitle }}</p>
        </div>
        <div class="preview-summary">
          <el-row :gutter="16">
            <el-col :span="6" v-for="item in previewData" :key="item.label">
              <div class="preview-item">
                <div class="preview-value">{{ item.value }}</div>
                <div class="preview-label">{{ item.label }}</div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="preview-note">
          <el-icon><InfoFilled /></el-icon>
          <span>这是报表的简化预览，实际报表将包含更详细的数据和图表</span>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleExport" :loading="exporting">
          <el-icon><Download /></el-icon>
          {{ exporting ? '导出中...' : '导出报表' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { 
  Document, 
  Grid, 
  Tickets, 
  Download, 
  InfoFilled 
} from '@element-plus/icons-vue'
import type { ReportConfig, StatisticsPeriod } from '@/types'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'export', config: ReportConfig & { 
    emailEnabled: boolean
    emailAddress: string
    emailSubject: string
    template: string
    contentOptions: string[]
  }): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref()
const exporting = ref(false)
const emailEnabled = ref(false)
const emailAddress = ref('')
const emailSubject = ref('能耗统计报表')
const selectedTemplate = ref('standard')
const contentOptions = ref(['charts', 'comparison', 'trends'])

const config = ref<ReportConfig>({
  period: 'month',
  startDate: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
  endDate: dayjs().format('YYYY-MM-DD'),
  includeCharts: true,
  includeComparison: true,
  includeTrends: true,
  format: 'pdf'
})

const dateRange = ref<[string, string]>([
  config.value.startDate,
  config.value.endDate
])

// 报表模板
const templates = [
  {
    id: 'standard',
    name: '标准报表',
    description: '常规',
    type: 'primary'
  },
  {
    id: 'executive',
    name: '管理层报表',
    description: '简洁',
    type: 'success'
  },
  {
    id: 'technical',
    name: '技术报表',
    description: '详细',
    type: 'warning'
  },
  {
    id: 'environmental',
    name: '环保报表',
    description: '专业',
    type: 'info'
  }
]

// 表单验证规则
const rules = {
  period: [{ required: true, message: '请选择报表周期', trigger: 'change' }],
  dateRange: [{ required: true, message: '请选择时间范围', trigger: 'change' }],
  format: [{ required: true, message: '请选择导出格式', trigger: 'change' }]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const previewTitle = computed(() => {
  const periodNames = {
    day: '日',
    week: '周',
    month: '月',
    quarter: '季度',
    year: '年度'
  }
  return `能耗统计${periodNames[config.value.period]}报表`
})

const previewSubtitle = computed(() => {
  return `统计周期: ${config.value.startDate} 至 ${config.value.endDate}`
})

const previewData = computed(() => [
  { label: '总能耗', value: '2,456 kWh' },
  { label: '总成本', value: '¥1,965' },
  { label: '平均效率', value: '78.5%' },
  { label: '碳排放', value: '1,465 kg' }
])

// 方法
const handleClose = () => {
  visible.value = false
}

const handleExport = async () => {
  const valid = await formRef.value?.validate().catch(() => false)
  if (!valid) return
  
  if (emailEnabled.value && !emailAddress.value) {
    ElMessage.warning('请输入邮箱地址')
    return
  }
  
  exporting.value = true
  
  try {
    // 更新配置
    config.value.includeCharts = contentOptions.value.includes('charts')
    config.value.includeComparison = contentOptions.value.includes('comparison')
    config.value.includeTrends = contentOptions.value.includes('trends')
    
    const exportConfig = {
      ...config.value,
      emailEnabled: emailEnabled.value,
      emailAddress: emailAddress.value,
      emailSubject: emailSubject.value,
      template: selectedTemplate.value,
      contentOptions: contentOptions.value
    }
    
    emit('export', exportConfig)
  } finally {
    exporting.value = false
  }
}

// 监听日期范围变化
watch(dateRange, (newRange) => {
  if (newRange) {
    config.value.startDate = newRange[0]
    config.value.endDate = newRange[1]
  }
})

// 监听周期变化，自动调整日期范围
watch(() => config.value.period, (newPeriod) => {
  const end = dayjs()
  let start: dayjs.Dayjs
  
  switch (newPeriod) {
    case 'day':
      start = end.subtract(7, 'day')
      break
    case 'week':
      start = end.subtract(4, 'week')
      break
    case 'month':
      start = end.subtract(6, 'month')
      break
    case 'quarter':
      start = end.subtract(4, 'quarter' as any)
      break
    case 'year':
      start = end.subtract(3, 'year')
      break
    default:
      start = end.subtract(1, 'month')
  }
  
  config.value.startDate = start.format('YYYY-MM-DD')
  config.value.endDate = end.format('YYYY-MM-DD')
  dateRange.value = [config.value.startDate, config.value.endDate]
  
  // 更新邮件主题
  const periodNames = {
    day: '日',
    week: '周', 
    month: '月',
    quarter: '季度',
    year: '年度'
  }
  emailSubject.value = `能耗统计${periodNames[newPeriod]}报表 - ${dayjs().format('YYYY-MM-DD')}`
})
</script>

<style lang="scss" scoped>
.template-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.email-config {
  width: 100%;
}

.preview-section {
  margin-top: 20px;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  
  h4 {
    margin: 0 0 16px 0;
    color: var(--el-text-color-primary);
  }
  
  .preview-content {
    .preview-header {
      text-align: center;
      margin-bottom: 20px;
      
      h3 {
        margin: 0 0 8px 0;
        color: var(--el-text-color-primary);
      }
      
      p {
        margin: 0;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }
    }
    
    .preview-summary {
      margin-bottom: 16px;
      
      .preview-item {
        text-align: center;
        padding: 12px;
        background: var(--el-bg-color);
        border-radius: 6px;
        
        .preview-value {
          font-size: 18px;
          font-weight: bold;
          color: var(--el-color-primary);
          margin-bottom: 4px;
        }
        
        .preview-label {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }
    }
    
    .preview-note {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background: var(--el-color-info-light-9);
      border-radius: 4px;
      font-size: 12px;
      color: var(--el-color-info);
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  .el-radio {
    margin-right: 0;
    
    .el-radio__label {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}
</style>
