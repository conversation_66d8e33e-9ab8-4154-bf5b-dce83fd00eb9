(function(c){"object"===typeof module&&module.exports?module.exports=c():(window.Granite=window.Granite||{}).Sling=c()})(function(){return{SELECTOR_INFINITY:".infinity",CHARSET:"_charset_",STATUS:":status",STATUS_BROWSER:"browser",OPERATION:":operation",OPERATION_DELETE:"delete",OPERATION_MOVE:"move",DELETE_SUFFIX:"@Delete",TYPEHINT_SUFFIX:"@TypeHint",COPY_SUFFIX:"@CopyFrom",MOVE_SUFFIX:"@MoveFrom",ORDER:":order",REPLACE:":replace",DESTINATION:":dest",SAVE_PARAM_PREFIX:":saveParamPrefix",IGNORE_PARAM:":ignore",
REQUEST_LOGIN_PARAM:"sling:authRequestLogin",LOGIN_URL:"/system/sling/login.html",LOGOUT_URL:"/system/sling/logout.html"}});
(function(c){"object"===typeof module&&module.exports?module.exports=c():(window.Granite=window.Granite||{}).Util=c()})(function(){return{patchText:function(c,h){if(h)if("[object Array]"!==Object.prototype.toString.call(h))c=c.replace("{0}",h);else for(var e=0;e<h.length;e++)c=c.replace("{"+e+"}",h[e]);return c},getTopWindow:function(){var c=window;if(this.iFrameTopWindow)return this.iFrameTopWindow;try{for(;c.parent&&c!==c.parent&&c.parent.location.href;)c=c.parent}catch(h){}return c},setIFrameMode:function(c){this.iFrameTopWindow=
c||window},applyDefaults:function(){for(var c,h=arguments[0]||{},e=1;e<arguments.length;e++){c=arguments[e];for(var m in c){var g=c[m];c.hasOwnProperty(m)&&void 0!==g&&(h[m]=null===g||"object"!==typeof g||g instanceof Array?g instanceof Array?g.slice(0):g:this.applyDefaults(h[m],g))}}return h},getKeyCode:function(c){return c.keyCode?c.keyCode:c.which}}});
(function(c){"object"===typeof module&&module.exports?module.exports=c(require("@granite/util"),require("jquery")):window.Granite.HTTP=c(Granite.Util,jQuery)})(function(c,h){return function(){var e=null,m=/^(?:http|https):\/\/[^/]+(\/.*)\/(?:etc\.clientlibs|etc(\/.*)*\/clientlibs|libs(\/.*)*\/clientlibs|apps(\/.*)*\/clientlibs|etc\/designs).*\.js(\?.*)?$/,g=/[^\w-.~%:/?[\]@!$&'()*+,;=]/,p=/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,n=!1,d={getSchemeAndAuthority:function(a){if(!a)return"";
a=p.exec(a);return null===a?"":[a[1],a[3]].join("")},getContextPath:function(){null===e&&(e=d.detectContextPath());return e},detectContextPath:function(){try{if(window.CQURLInfo)e=CQURLInfo.contextPath||"";else{for(var a=document.getElementsByTagName("script"),b=0;b<a.length;b++){var f=m.exec(a[b].src);if(f)return e=f[1]}e=""}}catch(l){}return e},externalize:function(a){try{0===a.indexOf("/")&&d.getContextPath()&&0!==a.indexOf(d.getContextPath()+"/")&&(a=d.getContextPath()+a)}catch(b){}return a},
internalize:function(a,b){if("/"===a.charAt(0))return e===a?"":e&&0===a.indexOf(e+"/")?a.substring(e.length):a;b||(b=document);b=d.getSchemeAndAuthority(b.location.href);var f=d.getSchemeAndAuthority(a);return b===f?a.substring(f.length+(e?e.length:0)):a},getPath:function(a){if(a)a=d.removeParameters(a),a=d.removeAnchor(a);else{if(window.CQURLInfo&&CQURLInfo.requestPath)return CQURLInfo.requestPath;a=window.location.pathname}a=d.internalize(a);var b=a.indexOf(".",a.lastIndexOf("/"));-1!==b&&(a=a.substring(0,
b));return a},removeAnchor:function(a){var b=a.indexOf("#");return 0<=b?a.substring(0,b):a},removeParameters:function(a){var b=a.indexOf("?");return 0<=b?a.substring(0,b):a},encodePathOfURI:function(a){for(var b=["?","#"],f=[a],l,k=0,q=b.length;k<q;k++)if(l=b[k],0<=a.indexOf(l)){f=a.split(l);break}g.test(f[0])&&(f[0]=d.encodePath(f[0]));return f.join(l)},encodePath:function(a){a=encodeURI(a);a=a.replace(/%5B/g,"[").replace(/%5D/g,"]");a=a.replace(/\?/g,"%3F");return a=a.replace(/#/g,"%23")},handleLoginRedirect:function(){if(!n){n=
!0;alert(Granite.I18n.get("Your request could not be completed because you have been signed out."));var a=c.getTopWindow().document.location;a.href=d.externalize("/")+"?resource\x3d"+encodeURIComponent(a.pathname+a.search+a.hash)}},getXhrHook:function(a,b,f){return window.G_XHR_HOOK&&"function"===typeof G_XHR_HOOK?(a={url:a,method:b||"GET"},f&&(a.params=f),G_XHR_HOOK(a)):null},eval:function(a){"object"!==typeof a&&(a=h.ajax({url:a,type:"get",async:!1}));try{return JSON.parse(a.body?a.body:a.responseText)}catch(b){}return null}};
return d}()});
(function(c){"object"===typeof module&&module.exports?module.exports=c(require("@granite/http")):window.Granite.I18n=c(window.Granite.HTTP)})(function(c){return function(){var h={},e="/libs/cq/i18n/dict.",m=".json",g=void 0,p=!1,n=null,d={},a=!1;d.LOCALE_DEFAULT="en";d.PSEUDO_LANGUAGE="zz";d.PSEUDO_PATTERN_KEY="_pseudoPattern_";d.init=function(b){b=b||{};this.setLocale(b.locale);this.setUrlPrefix(b.urlPrefix);this.setUrlSuffix(b.urlSuffix)};d.setLocale=function(b){b&&(g=b)};d.getLocale=function(){"function"===
typeof g&&(g=g());return g||document.documentElement.lang||d.LOCALE_DEFAULT};d.setUrlPrefix=function(b){b&&(e=b,a=!0)};d.setUrlSuffix=function(b){b&&(m=b,a=!0)};d.getDictionary=function(b){b=b||d.getLocale();if(!h[b]){p=0===b.indexOf(d.PSEUDO_LANGUAGE);try{var f=new XMLHttpRequest,l=f.open,k=c.externalize;var q=b;if(a)var r=e+q+m;else{var t,u=document.querySelector("html");u&&(t=u.getAttribute("data-i18n-dictionary-src"));r=t?t.replace("{locale}",encodeURIComponent(q)).replace("{+locale}",q):e+q+
m}l.call(f,"GET",k.call(c,r),!1);f.send();h[b]=JSON.parse(f.responseText)}catch(v){}h[b]||(h[b]={})}return h[b]};d.get=function(b,f,l){var k;var q=d.getDictionary();var r=p?d.PSEUDO_PATTERN_KEY:l?b+" (("+l+"))":b;q&&(k=q[r]);k||(k=b);p&&(k=k.replace("{string}",b).replace("{comment}",l?l:""));b=k;if(f)if(Array.isArray(f))for(l=0;l<f.length;l++)b=b.replace("{"+l+"}",f[l]);else b=b.replace("{0}",f);return b};d.getVar=function(b,f){return b?d.get(b,null,f):null};d.getLanguages=function(){if(!n)try{var b=
c.externalize("/libs/wcm/core/resources/languages.overlay.infinity.json"),f=new XMLHttpRequest;f.open("GET",b,!1);f.send();var l=JSON.parse(f.responseText);Object.keys(l).forEach(function(k){k=l[k];k.language&&(k.title=d.getVar(k.language));k.title&&k.country&&"*"!==k.country&&(k.title+=" ("+d.getVar(k.country)+")")});n=l}catch(k){n={}}return n};d.parseLocale=function(b){if(!b)return null;var f=b.indexOf("_");0>f&&(f=b.indexOf("-"));if(0>f){var l=b;f=null}else l=b.substring(0,f),f=b.substring(f+1);
return{code:b,language:l,country:f}};return d}()});
(function(c){"object"===typeof module&&module.exports?module.exports=c():(window.Granite=window.Granite||{}).TouchIndicator=c()})(function(){var c={},h=[];return{debugWithMouse:!1,init:function(){var e=this,m=function(g){e.update(g.touches);return!0};document.addEventListener("touchstart",m);document.addEventListener("touchmove",m);document.addEventListener("touchend",m);this.debugWithMouse&&document.addEventListener("mousemove",function(g){g.identifer="fake";e.update([g]);return!0})},update:function(e){for(var m=
{},g=0;g<e.length;g++){var p=e[g],n=p.identifier,d=c[n];d||(d=h.pop(),d||(d=document.createElement("div"),d.style.visibility="hidden",d.style.position="absolute",d.style.width="30px",d.style.height="30px",d.style.borderRadius="20px",d.style.border="5px solid orange",d.style.userSelect="none",d.style.opacity="0.5",d.style.zIndex="2000",d.style.pointerEvents="none",document.body.appendChild(d)));m[n]=d;d.style.left=p.pageX-20+"px";d.style.top=p.pageY-20+"px";d.style.visibility="visible"}for(n in c)c.hasOwnProperty(n)&&
!m[n]&&(d=c[n],d.style.visibility="hidden",h.push(d));c=m}}});
(function(c){"object"===typeof module&&module.exports?module.exports=c():(window.Granite=window.Granite||{}).OptOutUtil=c()})(function(c){return function(){var h={},e=[],m=[];h.init=function(g){g?(e=g.cookieNames||[],m=g.whitelistCookieNames||[]):(e=[],m=[])};h.getCookieNames=function(){return e};h.getWhitelistCookieNames=function(){return m};h.isOptedOut=function(){for(var g=document.cookie.split(";"),p=0;p<g.length;p++){var n=g[p].split("\x3d")[0];n=String.prototype.trim?n.trim():n.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,
"");if(0<=h.getCookieNames().indexOf(n))return!0}return!1};h.maySetCookie=function(g){return!(h.isOptedOut()&&-1===h.getWhitelistCookieNames().indexOf(g))};return h}()});
(function(c){"object"===typeof module&&module.exports?module.exports=c():(window.Granite=window.Granite||{}).Toggles=c()})(function(){var c=null;return{isEnabled:function(h){var e;(e=c)||(e=new XMLHttpRequest,e.open("GET",Granite.HTTP.externalize("/etc.clientlibs/toggles.json"),!1),e.send(null),e=200===e.status?JSON.parse(e.responseText):null);c=e;return(c||{enabled:[]}).enabled.includes(h)}}});Granite.OptOutUtil.init(window.GraniteOptOutConfig);Granite.HTTP.detectContextPath();