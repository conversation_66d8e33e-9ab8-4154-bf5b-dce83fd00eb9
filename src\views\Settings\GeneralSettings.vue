<template>
  <div class="general-settings">
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><Setting /></el-icon>
          <span>通用设置</span>
        </div>
      </template>
      
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="setting-section">
              <template #header>
                <span>基本信息</span>
              </template>
              
              <el-form-item label="系统名称" prop="systemName">
                <el-input v-model="form.systemName" placeholder="请输入系统名称" />
              </el-form-item>
              
              <el-form-item label="公司名称" prop="companyName">
                <el-input v-model="form.companyName" placeholder="请输入公司名称" />
              </el-form-item>
              
              <el-form-item label="联系邮箱" prop="contactEmail">
                <el-input v-model="form.contactEmail" placeholder="请输入联系邮箱" />
              </el-form-item>
              
              <el-form-item label="联系电话" prop="contactPhone">
                <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
              </el-form-item>
            </el-card>
          </el-col>
          
          <el-col :span="12">
            <el-card class="setting-section">
              <template #header>
                <span>区域设置</span>
              </template>
              
              <el-form-item label="时区" prop="timezone">
                <el-select v-model="form.timezone" placeholder="选择时区">
                  <el-option label="北京时间 (UTC+8)" value="Asia/Shanghai" />
                  <el-option label="东京时间 (UTC+9)" value="Asia/Tokyo" />
                  <el-option label="纽约时间 (UTC-5)" value="America/New_York" />
                  <el-option label="伦敦时间 (UTC+0)" value="Europe/London" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="日期格式" prop="dateFormat">
                <el-select v-model="form.dateFormat" placeholder="选择日期格式">
                  <el-option label="YYYY-MM-DD" value="YYYY-MM-DD" />
                  <el-option label="DD/MM/YYYY" value="DD/MM/YYYY" />
                  <el-option label="MM/DD/YYYY" value="MM/DD/YYYY" />
                  <el-option label="YYYY年MM月DD日" value="YYYY年MM月DD日" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="时间格式" prop="timeFormat">
                <el-radio-group v-model="form.timeFormat">
                  <el-radio value="24h">24小时制</el-radio>
                  <el-radio value="12h">12小时制</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="货币单位" prop="currency">
                <el-select v-model="form.currency" placeholder="选择货币单位">
                  <el-option label="人民币 (CNY)" value="CNY" />
                  <el-option label="美元 (USD)" value="USD" />
                  <el-option label="欧元 (EUR)" value="EUR" />
                  <el-option label="日元 (JPY)" value="JPY" />
                </el-select>
              </el-form-item>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="setting-section">
              <template #header>
                <span>数据设置</span>
              </template>
              
              <el-form-item label="数据保留天数" prop="dataRetentionDays">
                <el-input-number 
                  v-model="form.dataRetentionDays" 
                  :min="30" 
                  :max="3650"
                  placeholder="数据保留天数"
                />
                <div class="form-tip">系统将自动清理超过指定天数的历史数据</div>
              </el-form-item>
              
              <el-form-item label="自动刷新间隔" prop="autoRefreshInterval">
                <el-input-number 
                  v-model="form.autoRefreshInterval" 
                  :min="10" 
                  :max="300"
                  placeholder="刷新间隔(秒)"
                />
                <div class="form-tip">页面数据自动刷新的时间间隔</div>
              </el-form-item>
            </el-card>
          </el-col>
          
          <el-col :span="12">
            <el-card class="setting-section">
              <template #header>
                <span>系统选项</span>
              </template>
              
              <el-form-item label="调试模式">
                <el-switch 
                  v-model="form.enableDebugMode"
                  active-text="开启"
                  inactive-text="关闭"
                />
                <div class="form-tip">开启后将显示详细的调试信息</div>
              </el-form-item>
              
              <el-form-item label="主题设置">
                <el-radio-group v-model="themeMode">
                  <el-radio value="light">浅色主题</el-radio>
                  <el-radio value="dark">深色主题</el-radio>
                  <el-radio value="auto">跟随系统</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="语言设置">
                <el-select v-model="language" placeholder="选择语言">
                  <el-option label="简体中文" value="zh-CN" />
                  <el-option label="繁體中文" value="zh-TW" />
                  <el-option label="English" value="en-US" />
                  <el-option label="日本語" value="ja-JP" />
                </el-select>
              </el-form-item>
            </el-card>
          </el-col>
        </el-row>
        
        <div class="form-actions">
          <el-button @click="resetForm">重置</el-button>
          <el-button type="primary" @click="saveSettings" :loading="settingsStore.loading">
            保存设置
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { Setting } from '@element-plus/icons-vue'
import { useSettingsStore } from '@/stores/settings'
import { ElMessage } from 'element-plus'

const settingsStore = useSettingsStore()
const formRef = ref()

// 表单数据
const form = reactive({
  systemName: settingsStore.settings.general.systemName,
  companyName: settingsStore.settings.general.companyName,
  contactEmail: settingsStore.settings.general.contactEmail,
  contactPhone: settingsStore.settings.general.contactPhone,
  timezone: settingsStore.settings.general.timezone,
  dateFormat: settingsStore.settings.general.dateFormat,
  timeFormat: settingsStore.settings.general.timeFormat,
  currency: settingsStore.settings.general.currency,
  dataRetentionDays: settingsStore.settings.general.dataRetentionDays,
  autoRefreshInterval: settingsStore.settings.general.autoRefreshInterval,
  enableDebugMode: settingsStore.settings.general.enableDebugMode
})

// 主题和语言设置
const themeMode = ref(settingsStore.settings.theme.mode)
const language = ref(settingsStore.settings.language.locale)

// 表单验证规则
const rules = {
  systemName: [
    { required: true, message: '请输入系统名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  companyName: [
    { required: true, message: '请输入公司名称', trigger: 'blur' }
  ],
  contactEmail: [
    { required: true, message: '请输入联系邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  timezone: [
    { required: true, message: '请选择时区', trigger: 'change' }
  ],
  dateFormat: [
    { required: true, message: '请选择日期格式', trigger: 'change' }
  ],
  timeFormat: [
    { required: true, message: '请选择时间格式', trigger: 'change' }
  ],
  currency: [
    { required: true, message: '请选择货币单位', trigger: 'change' }
  ]
}

// 方法
const saveSettings = async () => {
  const valid = await formRef.value?.validate().catch(() => false)
  if (!valid) return
  
  try {
    // 保存通用设置
    await settingsStore.updateSettings('general', form)
    
    // 保存主题设置
    await settingsStore.updateSettings('theme', { 
      ...settingsStore.settings.theme,
      mode: themeMode.value 
    })
    
    // 保存语言设置
    await settingsStore.updateSettings('language', { 
      ...settingsStore.settings.language,
      locale: language.value 
    })
    
    ElMessage.success('设置保存成功')
  } catch (error) {
    ElMessage.error('设置保存失败')
  }
}

const resetForm = () => {
  Object.assign(form, settingsStore.settings.general)
  themeMode.value = settingsStore.settings.theme.mode
  language.value = settingsStore.settings.language.locale
  ElMessage.info('已重置为默认设置')
}
</script>

<style lang="scss" scoped>
.general-settings {
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
  }
  
  .setting-section {
    margin-bottom: 20px;
    
    :deep(.el-card__header) {
      padding: 12px 20px;
      background-color: var(--el-bg-color-page);
      font-weight: 600;
    }
    
    :deep(.el-card__body) {
      padding: 20px;
    }
  }
  
  .form-tip {
    font-size: 12px;
    color: var(--el-text-color-regular);
    margin-top: 4px;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--el-border-color-light);
  }
}
</style>
