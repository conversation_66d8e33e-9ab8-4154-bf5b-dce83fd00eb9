@use './variables.scss' as *;
@use './mixins.scss' as *;

// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-darker);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}

// 工具类
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

// 间距工具类
.m-0 { margin: 0; }
.m-1 { margin: $spacing-xs; }
.m-2 { margin: $spacing-sm; }
.m-3 { margin: $spacing-md; }
.m-4 { margin: $spacing-lg; }
.m-5 { margin: $spacing-xl; }

.p-0 { padding: 0; }
.p-1 { padding: $spacing-xs; }
.p-2 { padding: $spacing-sm; }
.p-3 { padding: $spacing-md; }
.p-4 { padding: $spacing-lg; }
.p-5 { padding: $spacing-xl; }

// 响应式显示/隐藏工具类
.mobile-hidden {
  @include respond-to(mobile) {
    display: none !important;
  }
}

.tablet-hidden {
  @include respond-to(tablet) {
    display: none !important;
  }
}

.desktop-hidden {
  @include min-width(md) {
    display: none !important;
  }
}

.mobile-only {
  display: none !important;

  @include respond-to(mobile) {
    display: block !important;
  }
}

.tablet-only {
  display: none !important;

  @include respond-to(tablet) {
    display: block !important;
  }
}

.desktop-only {
  display: none !important;

  @include min-width(md) {
    display: block !important;
  }
}

// 响应式弹性布局工具类
.flex-mobile-column {
  @include respond-to(mobile) {
    flex-direction: column !important;
  }
}

.flex-mobile-wrap {
  @include respond-to(mobile) {
    flex-wrap: wrap !important;
  }
}

// 响应式间距工具类
.m-mobile-xs {
  @include respond-to(mobile) {
    margin: $spacing-mobile-xs !important;
  }
}

.m-mobile-sm {
  @include respond-to(mobile) {
    margin: $spacing-mobile-sm !important;
  }
}

.m-mobile-md {
  @include respond-to(mobile) {
    margin: $spacing-mobile-md !important;
  }
}

.m-mobile-lg {
  @include respond-to(mobile) {
    margin: $spacing-mobile-lg !important;
  }
}

.m-mobile-xl {
  @include respond-to(mobile) {
    margin: $spacing-mobile-xl !important;
  }
}

.p-mobile-xs {
  @include respond-to(mobile) {
    padding: $spacing-mobile-xs !important;
  }
}

.p-mobile-sm {
  @include respond-to(mobile) {
    padding: $spacing-mobile-sm !important;
  }
}

.p-mobile-md {
  @include respond-to(mobile) {
    padding: $spacing-mobile-md !important;
  }
}

.p-mobile-lg {
  @include respond-to(mobile) {
    padding: $spacing-mobile-lg !important;
  }
}

.p-mobile-xl {
  @include respond-to(mobile) {
    padding: $spacing-mobile-xl !important;
  }
}

// 响应式文本对齐
.text-mobile-center {
  @include respond-to(mobile) {
    text-align: center !important;
  }
}

.text-mobile-left {
  @include respond-to(mobile) {
    text-align: left !important;
  }
}

// 响应式宽度
.w-mobile-full {
  @include respond-to(mobile) {
    width: 100% !important;
  }
}

.w-mobile-auto {
  @include respond-to(mobile) {
    width: auto !important;
  }
}

// 触摸友好的交互元素
.touch-friendly {
  @include touch-friendly;
}

// 移动端优化的滚动容器
.mobile-scroll {
  @include respond-to(mobile) {
    -webkit-overflow-scrolling: touch;
    overflow-x: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity $transition-medium;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform $transition-medium;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

// Element Plus 样式覆盖
.el-card {
  border-radius: $border-radius-md;
  box-shadow: $box-shadow-light;
  
  .el-card__header {
    border-bottom: 1px solid var(--el-border-color-lighter);
    padding: $spacing-md $spacing-lg;
  }
  
  .el-card__body {
    padding: $spacing-lg;
  }
}

.el-button {
  border-radius: $border-radius-sm;
  transition: all $transition-fast;
}

.el-input {
  .el-input__wrapper {
    border-radius: $border-radius-sm;
  }
}

// 暗色主题适配
.dark {
  color-scheme: dark;
}
