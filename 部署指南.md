# 智能供水平台 - 部署指南

## 📋 目录
- [快速开始](#快速开始)
- [环境要求](#环境要求)
- [部署方式](#部署方式)
- [配置说明](#配置说明)
- [故障排除](#故障排除)

## 🚀 快速开始

### 方式一：一键部署（推荐）

**Windows用户：**
```bash
# 双击运行
deploy.bat
```

**Linux/macOS用户：**
```bash
# 给脚本执行权限并运行
chmod +x deploy.sh
./deploy.sh
```

### 方式二：手动部署

1. **安装依赖**
```bash
pnpm install
```

2. **构建项目**
```bash
pnpm run build
```

3. **启动服务器**
```bash
go run server.go
```

## 🔧 环境要求

### 基础环境
- **Node.js**: >= 16.0.0
- **pnpm**: >= 7.0.0 (推荐) 或 npm >= 8.0.0
- **Go**: >= 1.19 (用于服务器)

### 可选环境
- **Docker**: >= 20.0.0 (容器化部署)
- **Docker Compose**: >= 2.0.0

## 🐳 部署方式

### 1. 本地开发部署

适用于开发和测试环境：

```bash
# 开发模式
pnpm run dev

# 生产模式
pnpm run build
go run server.go
```

### 2. Docker容器部署

适用于生产环境，提供更好的隔离性和可移植性：

```bash
# 构建镜像
docker build -t water-platform .

# 运行容器
docker run -d -p 8080:8080 --name water-platform water-platform

# 或使用docker-compose
docker-compose up -d
```

### 3. 云服务器部署

1. **上传代码到服务器**
```bash
# 使用git克隆
git clone <your-repo-url>
cd water-platform

# 或使用scp上传
scp -r ./water-platform user@server:/path/to/deploy/
```

2. **服务器端部署**
```bash
# 运行部署脚本
./deploy.sh

# 或手动执行
pnpm install
pnpm run build
nohup go run server.go > server.log 2>&1 &
```

## ⚙️ 配置说明

### 服务器配置

服务器默认配置在 `server.go` 中：

```go
type ServerConfig struct {
    Port     string  // 默认: "8080"
    DistDir  string  // 默认: "./dist"
    Host     string  // 默认: "0.0.0.0"
    AutoOpen bool    // 默认: true
}
```

### 环境变量

可以通过环境变量覆盖默认配置：

```bash
export PORT=3000           # 服务端口
export DIST_DIR=./build    # 静态文件目录
export AUTO_OPEN=false     # 是否自动打开浏览器
```

### Docker环境变量

在 `docker-compose.yml` 中配置：

```yaml
environment:
  - PORT=8080
  - TZ=Asia/Shanghai
```

## 🔍 故障排除

### 常见问题

#### 1. 端口被占用
```
⚠️ 端口 8080 已被占用，正在寻找可用端口...
✅ 找到可用端口: 8081
```
**解决方案**: 程序会自动寻找可用端口，或手动指定端口：
```bash
PORT=3000 go run server.go
```

#### 2. dist目录不存在
```
❌ 错误: ./dist 目录不存在
💡 请先运行以下命令进行打包:
   pnpm install
   pnpm run build
```
**解决方案**: 按提示运行构建命令

#### 3. Node.js未安装
```
❌ 错误: 未检测到Node.js
💡 请先安装Node.js: https://nodejs.org/
```
**解决方案**: 安装Node.js后重新运行

#### 4. Go环境未安装
```
❌ 错误: 未检测到Go环境
💡 请先安装Go: https://golang.org/dl/
```
**解决方案**: 
- 安装Go环境，或
- 使用Python替代: `python -m http.server 8080 --directory dist`

### 性能优化

#### 1. 启用Gzip压缩
在生产环境中，建议在反向代理（如Nginx）中启用Gzip压缩：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:8080;
        gzip on;
        gzip_types text/css application/javascript application/json;
    }
}
```

#### 2. 静态资源缓存
配置浏览器缓存策略：

```nginx
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 监控和日志

#### 1. 查看运行日志
```bash
# 实时查看日志
tail -f server.log

# 查看错误日志
grep "ERROR" server.log
```

#### 2. 系统监控
```bash
# 查看进程状态
ps aux | grep server

# 查看端口占用
netstat -tlnp | grep :8080

# 查看系统资源
top -p $(pgrep -f "go run server.go")
```

## 📞 技术支持

如果遇到问题，请检查：

1. **环境要求**: 确保所有依赖都已正确安装
2. **网络连接**: 确保能够访问npm/pnpm仓库
3. **权限问题**: 确保有足够的文件读写权限
4. **防火墙设置**: 确保端口未被防火墙阻止

更多问题请查看项目文档或提交Issue。

---

## 🎯 部署检查清单

- [ ] Node.js环境已安装
- [ ] pnpm/npm已安装
- [ ] Go环境已安装（可选）
- [ ] 项目依赖已安装
- [ ] 项目已成功构建
- [ ] 服务器已启动
- [ ] 浏览器能正常访问
- [ ] 所有功能正常工作

**部署完成！🎉**