<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑预警规则' : '添加预警规则'"
    width="600px"
    :before-close="handleClose"
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <el-form-item label="规则名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入规则名称" />
      </el-form-item>
      
      <el-form-item label="描述" prop="description">
        <el-input 
          v-model="form.description" 
          type="textarea" 
          :rows="2"
          placeholder="请输入规则描述"
        />
      </el-form-item>
      
      <el-form-item label="预警类别" prop="category">
        <el-select v-model="form.category" placeholder="选择预警类别">
          <el-option label="设备故障" value="equipment_fault" />
          <el-option label="性能异常" value="performance_anomaly" />
          <el-option label="维护到期" value="maintenance_due" />
          <el-option label="能耗异常" value="energy_consumption" />
          <el-option label="效率下降" value="efficiency_drop" />
          <el-option label="振动过高" value="vibration_high" />
          <el-option label="温度异常" value="temperature_abnormal" />
          <el-option label="压力异常" value="pressure_abnormal" />
          <el-option label="流量异常" value="flow_abnormal" />
          <el-option label="系统错误" value="system_error" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="严重程度" prop="severity">
        <el-select v-model="form.severity" placeholder="选择严重程度">
          <el-option label="严重" value="critical" />
          <el-option label="高" value="high" />
          <el-option label="中" value="medium" />
          <el-option label="低" value="low" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="触发条件">
        <div class="conditions-editor">
          <div v-for="(condition, index) in form.conditions" :key="index" class="condition-row">
            <el-select v-model="condition.parameter" placeholder="参数">
              <el-option label="温度" value="temperature" />
              <el-option label="振动" value="vibration" />
              <el-option label="压力" value="pressure" />
              <el-option label="流量" value="flow" />
              <el-option label="功率" value="power" />
              <el-option label="效率" value="efficiency" />
            </el-select>
            
            <el-select v-model="condition.operator" placeholder="操作符">
              <el-option label="大于" value="gt" />
              <el-option label="小于" value="lt" />
              <el-option label="等于" value="eq" />
              <el-option label="大于等于" value="gte" />
              <el-option label="小于等于" value="lte" />
              <el-option label="介于" value="between" />
            </el-select>
            
            <el-input-number 
              v-model="condition.value" 
              placeholder="阈值"
              :precision="2"
            />
            
            <el-input-number 
              v-model="condition.duration" 
              placeholder="持续时间(分钟)"
              :min="1"
            />
            
            <el-button 
              type="danger" 
              size="small" 
              @click="removeCondition(index)"
              :disabled="form.conditions.length <= 1"
            >
              删除
            </el-button>
          </div>
          
          <el-button type="primary" size="small" @click="addCondition">
            添加条件
          </el-button>
        </div>
      </el-form-item>
      
      <el-form-item label="响应动作">
        <el-checkbox-group v-model="form.actions">
          <el-checkbox label="notify">发送通知</el-checkbox>
          <el-checkbox label="log">记录日志</el-checkbox>
          <el-checkbox label="email">邮件告警</el-checkbox>
          <el-checkbox label="sms">短信告警</el-checkbox>
          <el-checkbox label="reduce_load">降低负荷</el-checkbox>
          <el-checkbox label="emergency_stop">紧急停机</el-checkbox>
          <el-checkbox label="schedule_maintenance">安排维护</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      
      <el-form-item label="冷却时间" prop="cooldownMinutes">
        <el-input-number 
          v-model="form.cooldownMinutes" 
          :min="1"
          :max="1440"
          placeholder="分钟"
        />
        <span class="form-tip">防止重复告警的时间间隔</span>
      </el-form-item>
      
      <el-form-item label="启用状态">
        <el-switch v-model="form.enabled" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { WarningRule, WarningCondition } from '@/types'

interface Props {
  modelValue: boolean
  rule: WarningRule | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'save', rule: WarningRule): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref()
const saving = ref(false)

const form = ref({
  id: '',
  name: '',
  description: '',
  category: 'equipment_fault' as any,
  severity: 'medium' as any,
  enabled: true,
  conditions: [{
    parameter: 'temperature',
    operator: 'gt' as any,
    value: 80,
    duration: 5
  }] as WarningCondition[],
  actions: ['notify', 'log'],
  cooldownMinutes: 30,
  createdAt: '',
  updatedAt: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入规则描述', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择预警类别', trigger: 'change' }
  ],
  severity: [
    { required: true, message: '请选择严重程度', trigger: 'change' }
  ],
  cooldownMinutes: [
    { required: true, message: '请输入冷却时间', trigger: 'blur' },
    { type: 'number', min: 1, max: 1440, message: '冷却时间应在 1-1440 分钟之间', trigger: 'blur' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.rule)

// 方法
const addCondition = () => {
  form.value.conditions.push({
    parameter: 'temperature',
    operator: 'gt',
    value: 80,
    duration: 5
  })
}

const removeCondition = (index: number) => {
  if (form.value.conditions.length > 1) {
    form.value.conditions.splice(index, 1)
  }
}

const handleSave = async () => {
  const valid = await formRef.value?.validate().catch(() => false)
  if (!valid) return
  
  saving.value = true
  
  try {
    const ruleData: WarningRule = {
      ...form.value,
      id: form.value.id || `rule_${Date.now()}`,
      createdAt: form.value.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    emit('save', ruleData)
  } finally {
    saving.value = false
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  form.value = {
    id: '',
    name: '',
    description: '',
    category: 'equipment_fault',
    severity: 'medium',
    enabled: true,
    conditions: [{
      parameter: 'temperature',
      operator: 'gt',
      value: 80,
      duration: 5
    }],
    actions: ['notify', 'log'],
    cooldownMinutes: 30,
    createdAt: '',
    updatedAt: ''
  }
}

// 监听规则变化
watch(() => props.rule, (newRule) => {
  if (newRule) {
    form.value = { ...newRule }
  } else {
    resetForm()
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.conditions-editor {
  .condition-row {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    
    .el-select, .el-input-number {
      flex: 1;
    }
  }
}

.form-tip {
  margin-left: 8px;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
