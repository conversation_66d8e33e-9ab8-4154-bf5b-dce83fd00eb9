echo 'Terminal capability test'
git config user.name
git config user.email
echo 'Terminal capability test'
git config user.name
git config user.email
echo 'Terminal capability test'
pnpm add @modelcontextprotocol/server-filesystem @modelcontextprotocol/server-git @modelcontextprotocol/server-fetch
pnpm add @modelcontextprotocol/server-filesystem @modelcontextprotocol/server-git @modelcontextprotocol/server-memory
pnpm add @modelcontextprotocol/server-filesystem @modelcontextprotocol/server-sequential-thinking @modelcontextprotocol/server-brave-search
pnpm add @modelcontextprotocol/sdk @modelcontextprotocol/inspector
node test-mcp.js
npx @modelcontextprotocol/inspector
echo 'Terminal capability test'
