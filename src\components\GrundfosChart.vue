<template>
  <div class="grundfos-chart">
    <!-- 图表控制栏 -->
    <div class="grundfos-chart__controls">
      <button 
        class="grundfos-chart__control-btn"
        @click="toggleFullscreen"
        title="全屏查看"
      >
        <span class="grundfos-chart__icon grundfos-chart__icon--fullscreen"></span>
      </button>
      <button 
        class="grundfos-chart__control-btn"
        @click="downloadChart"
        title="下载图表"
      >
        <span class="grundfos-chart__icon grundfos-chart__icon--download"></span>
      </button>
    </div>
    
    <!-- 主图表区域 -->
    <div class="grundfos-chart__container" ref="chartContainer">
      <canvas 
        ref="chartCanvas"
        class="grundfos-chart__canvas"
        :width="chartWidth"
        :height="chartHeight"
        @mousemove="handleMouseMove"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
        @click="handleClick"
        @wheel="handleWheel"
        @dblclick="handleDoubleClick"
      ></canvas>
      
      <!-- 数据提示框 -->
      <div 
        v-if="tooltip.visible"
        class="grundfos-chart__tooltip"
        :style="tooltipStyle"
      >
        <div class="grundfos-chart__tooltip-content">
          <div class="grundfos-chart__tooltip-item">
            <span class="grundfos-chart__tooltip-label">流量:</span>
            <span class="grundfos-chart__tooltip-value">{{ tooltip.data.flow }} m³/h</span>
          </div>
          <div class="grundfos-chart__tooltip-item">
            <span class="grundfos-chart__tooltip-label">扬程:</span>
            <span class="grundfos-chart__tooltip-value">{{ tooltip.data.head }} m</span>
          </div>
          <div class="grundfos-chart__tooltip-item">
            <span class="grundfos-chart__tooltip-label">效率:</span>
            <span class="grundfos-chart__tooltip-value">{{ tooltip.data.efficiency }}%</span>
          </div>
        </div>
      </div>
      
      <!-- 缩放信息 -->
      <div 
        v-if="zoom.scale !== 1"
        class="grundfos-chart__zoom-info"
      >
        <span>缩放: {{ Math.round(zoom.scale * 100) }}%</span>
        <span>双击重置</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { 
  GRUNDFOS_CURVE_DATA, 
  GRUNDFOS_COLORS, 
  calculatePumpParameters,
  type PumpCurveData 
} from '@/utils/grundfosDataProcessor'

interface Props {
  width?: number
  height?: number
  showTooltip?: boolean
  enableZoom?: boolean
  operatingPoint?: { flow: number; head: number }
}

const props = withDefaults(defineProps<Props>(), {
  width: 1200,
  height: 800,
  showTooltip: true,
  enableZoom: true,
  operatingPoint: () => ({ flow: 750, head: 32.8 })
})

const emit = defineEmits<{
  operatingPointChange: [point: { flow: number; head: number }]
  chartClick: [event: MouseEvent, data: PumpCurveData]
}>()

// 响应式数据
const chartContainer = ref<HTMLDivElement>()
const chartCanvas = ref<HTMLCanvasElement>()

const chartWidth = computed(() => props.width)
const chartHeight = computed(() => props.height)

// 鼠标交互状态
const mouse = reactive({
  x: 0,
  y: 0,
  isOver: false
})

// 提示框状态
const tooltip = reactive({
  visible: false,
  x: 0,
  y: 0,
  data: {
    flow: 0,
    head: 0,
    efficiency: 0,
    power: 0,
    npsh: 0
  }
})

// 缩放状态
const zoom = reactive({
  scale: 1,
  offsetX: 0,
  offsetY: 0,
  minScale: 0.5,
  maxScale: 3
})

// 计算提示框样式
const tooltipStyle = computed(() => ({
  left: `${tooltip.x + 10}px`,
  top: `${tooltip.y - 10}px`,
  transform: tooltip.x > chartWidth.value - 200 ? 'translateX(-100%)' : 'none'
}))

// 图表绘制区域
const chartArea = {
  x: 100,
  y: 100,
  width: 1000,
  height: 600
}

// 鼠标事件处理
const handleMouseMove = (event: MouseEvent) => {
  const rect = chartCanvas.value?.getBoundingClientRect()
  if (!rect) return
  
  mouse.x = event.clientX - rect.left
  mouse.y = event.clientY - rect.top
  
  updateTooltip()
  redrawChart()
}

const handleMouseEnter = () => {
  mouse.isOver = true
  if (chartCanvas.value) {
    chartCanvas.value.style.cursor = 'crosshair'
  }
}

const handleMouseLeave = () => {
  mouse.isOver = false
  tooltip.visible = false
  if (chartCanvas.value) {
    chartCanvas.value.style.cursor = 'default'
  }
  redrawChart()
}

const handleClick = (event: MouseEvent) => {
  if (tooltip.visible) {
    const newPoint = { flow: tooltip.data.flow, head: tooltip.data.head }
    emit('operatingPointChange', newPoint)
    emit('chartClick', event, tooltip.data)
  }
}

const handleWheel = (event: WheelEvent) => {
  if (!props.enableZoom) return
  
  event.preventDefault()
  
  const rect = chartCanvas.value?.getBoundingClientRect()
  if (!rect) return
  
  const mouseX = event.clientX - rect.left
  const mouseY = event.clientY - rect.top
  
  const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1
  const newScale = Math.max(zoom.minScale, Math.min(zoom.maxScale, zoom.scale * zoomFactor))
  
  if (newScale !== zoom.scale) {
    const scaleChange = newScale / zoom.scale
    
    zoom.offsetX = mouseX - (mouseX - zoom.offsetX) * scaleChange
    zoom.offsetY = mouseY - (mouseY - zoom.offsetY) * scaleChange
    zoom.scale = newScale
    
    redrawChart()
  }
}

const handleDoubleClick = () => {
  if (zoom.scale !== 1) {
    animateZoomReset()
  }
}

// 更新提示框
const updateTooltip = () => {
  if (!props.showTooltip || !mouse.isOver) {
    tooltip.visible = false
    return
  }
  
  // 检查鼠标是否在图表区域内
  if (mouse.x >= chartArea.x && mouse.x <= chartArea.x + chartArea.width &&
      mouse.y >= chartArea.y && mouse.y <= chartArea.y + chartArea.height) {
    
    // 计算对应的流量值
    const flow = ((mouse.x - chartArea.x) / chartArea.width) * 900
    const calculatedData = calculatePumpParameters(flow)
    
    tooltip.visible = true
    tooltip.x = mouse.x
    tooltip.y = mouse.y
    tooltip.data = {
      flow: Math.round(calculatedData.flow),
      head: Math.round(calculatedData.head * 10) / 10,
      efficiency: Math.round(calculatedData.efficiency * 10) / 10,
      power: Math.round(calculatedData.power * 10) / 10,
      npsh: Math.round(calculatedData.npsh * 10) / 10
    }
  } else {
    tooltip.visible = false
  }
}

// 绘制图表
const drawChart = () => {
  const canvas = chartCanvas.value
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')
  if (!ctx) return
  
  // 清空画布
  ctx.clearRect(0, 0, chartWidth.value, chartHeight.value)
  
  // 应用缩放变换
  ctx.save()
  ctx.translate(zoom.offsetX, zoom.offsetY)
  ctx.scale(zoom.scale, zoom.scale)
  
  // 绘制背景
  ctx.fillStyle = GRUNDFOS_COLORS.background
  ctx.fillRect(0, 0, chartWidth.value, chartHeight.value)
  
  // 绘制网格
  drawGrid(ctx)
  
  // 绘制坐标轴
  drawAxes(ctx)
  
  // 绘制曲线
  drawCurves(ctx)
  
  // 绘制工作点
  drawOperatingPoint(ctx)

  // 绘制图表标题和技术信息
  drawChartTitle(ctx)

  // 绘制鼠标交互
  if (mouse.isOver) {
    drawMouseInteraction(ctx)
  }

  ctx.restore()
}

// 绘制专业网格 - 完全模拟格兰富
const drawGrid = (ctx: CanvasRenderingContext2D) => {
  // 主网格线
  ctx.strokeStyle = '#d0d0d0'
  ctx.lineWidth = 1

  // 垂直主网格线 (每100 m³/h)
  for (let i = 0; i <= 9; i++) {
    const x = chartArea.x + (i / 9) * chartArea.width
    ctx.beginPath()
    ctx.moveTo(x, chartArea.y)
    ctx.lineTo(x, chartArea.y + chartArea.height)
    ctx.stroke()
  }

  // 水平主网格线 (每10m扬程)
  for (let i = 0; i <= 5; i++) {
    const y = chartArea.y + chartArea.height - (i / 5) * chartArea.height
    ctx.beginPath()
    ctx.moveTo(chartArea.x, y)
    ctx.lineTo(chartArea.x + chartArea.width, y)
    ctx.stroke()
  }

  // 次网格线
  ctx.strokeStyle = '#e8e8e8'
  ctx.lineWidth = 0.5

  // 垂直次网格线 (每20 m³/h)
  for (let i = 0; i <= 45; i++) {
    const x = chartArea.x + (i / 45) * chartArea.width
    ctx.beginPath()
    ctx.moveTo(x, chartArea.y)
    ctx.lineTo(x, chartArea.y + chartArea.height)
    ctx.stroke()
  }

  // 水平次网格线 (每2m扬程)
  for (let i = 0; i <= 25; i++) {
    const y = chartArea.y + chartArea.height - (i / 25) * chartArea.height
    ctx.beginPath()
    ctx.moveTo(chartArea.x, y)
    ctx.lineTo(chartArea.x + chartArea.width, y)
    ctx.stroke()
  }
}

// 绘制专业坐标轴 - 格兰富标准
const drawAxes = (ctx: CanvasRenderingContext2D) => {
  ctx.strokeStyle = '#333333'
  ctx.lineWidth = 2

  // X轴
  ctx.beginPath()
  ctx.moveTo(chartArea.x, chartArea.y + chartArea.height)
  ctx.lineTo(chartArea.x + chartArea.width, chartArea.y + chartArea.height)
  ctx.stroke()

  // Y轴（左侧 - 扬程）
  ctx.beginPath()
  ctx.moveTo(chartArea.x, chartArea.y)
  ctx.lineTo(chartArea.x, chartArea.y + chartArea.height)
  ctx.stroke()

  // Y轴（右侧 - 效率）
  ctx.beginPath()
  ctx.moveTo(chartArea.x + chartArea.width, chartArea.y)
  ctx.lineTo(chartArea.x + chartArea.width, chartArea.y + chartArea.height)
  ctx.stroke()

  // 绘制刻度标签
  drawAxisLabels(ctx)
}

// 绘制坐标轴标签 - 专业格式
const drawAxisLabels = (ctx: CanvasRenderingContext2D) => {
  ctx.fillStyle = '#333333'
  ctx.font = '12px Arial'

  // X轴标签（流量 m³/h）
  ctx.textAlign = 'center'
  for (let i = 0; i <= 9; i++) {
    const flow = i * 100
    const x = chartArea.x + (i / 9) * chartArea.width
    ctx.fillText(flow.toString(), x, chartArea.y + chartArea.height + 20)
  }

  // X轴标题
  ctx.font = 'bold 14px Arial'
  ctx.fillStyle = '#1f5582'
  ctx.fillText('Flow rate Q [m³/h]', chartArea.x + chartArea.width / 2, chartArea.y + chartArea.height + 45)

  // Y轴标签（扬程 m）
  ctx.font = '12px Arial'
  ctx.fillStyle = '#333333'
  ctx.textAlign = 'right'
  for (let i = 0; i <= 5; i++) {
    const head = i * 10
    const y = chartArea.y + chartArea.height - (i / 5) * chartArea.height
    ctx.fillText(head.toString(), chartArea.x - 10, y + 4)
  }

  // Y轴标题（左侧 - 扬程）
  ctx.save()
  ctx.translate(chartArea.x - 50, chartArea.y + chartArea.height / 2)
  ctx.rotate(-Math.PI / 2)
  ctx.font = 'bold 14px Arial'
  ctx.fillStyle = '#1f5582'
  ctx.textAlign = 'center'
  ctx.fillText('Head H [m]', 0, 0)
  ctx.restore()

  // Y轴标签（效率 %）
  ctx.font = '12px Arial'
  ctx.fillStyle = '#333333'
  ctx.textAlign = 'left'
  for (let i = 0; i <= 5; i++) {
    const efficiency = i * 20
    const y = chartArea.y + chartArea.height - (i / 5) * chartArea.height
    ctx.fillText(efficiency.toString(), chartArea.x + chartArea.width + 10, y + 4)
  }

  // Y轴标题（右侧 - 效率）
  ctx.save()
  ctx.translate(chartArea.x + chartArea.width + 50, chartArea.y + chartArea.height / 2)
  ctx.rotate(Math.PI / 2)
  ctx.font = 'bold 14px Arial'
  ctx.fillStyle = '#ff8c00'
  ctx.textAlign = 'center'
  ctx.fillText('Efficiency η [%]', 0, 0)
  ctx.restore()
}

// 绘制专业曲线 - 使用平滑贝塞尔曲线
const drawCurves = (ctx: CanvasRenderingContext2D) => {
  // 绘制扬程曲线（蓝色）
  drawSmoothHeadCurve(ctx)

  // 绘制效率曲线（橙色）
  drawSmoothEfficiencyCurve(ctx)

  // 绘制曲线标签
  drawCurveLabels(ctx)
}

// 绘制平滑扬程曲线
const drawSmoothHeadCurve = (ctx: CanvasRenderingContext2D) => {
  ctx.strokeStyle = '#1f5582'
  ctx.lineWidth = 3
  ctx.lineCap = 'round'
  ctx.lineJoin = 'round'

  const headData = GRUNDFOS_CURVE_DATA.headCurve
  const points = headData.map(point => ({
    x: chartArea.x + (point.flow / 900) * chartArea.width,
    y: chartArea.y + chartArea.height - (point.head / 50) * chartArea.height
  }))

  ctx.beginPath()
  ctx.moveTo(points[0].x, points[0].y)

  // 使用二次贝塞尔曲线创建平滑曲线
  for (let i = 1; i < points.length - 1; i++) {
    const cp1x = (points[i].x + points[i - 1].x) / 2
    const cp1y = (points[i].y + points[i - 1].y) / 2
    const cp2x = (points[i].x + points[i + 1].x) / 2
    const cp2y = (points[i].y + points[i + 1].y) / 2

    ctx.quadraticCurveTo(points[i].x, points[i].y, cp2x, cp2y)
  }

  // 最后一段
  ctx.quadraticCurveTo(
    points[points.length - 1].x,
    points[points.length - 1].y,
    points[points.length - 1].x,
    points[points.length - 1].y
  )

  ctx.stroke()
}

// 绘制平滑效率曲线
const drawSmoothEfficiencyCurve = (ctx: CanvasRenderingContext2D) => {
  ctx.strokeStyle = '#ff8c00'
  ctx.lineWidth = 3
  ctx.lineCap = 'round'
  ctx.lineJoin = 'round'

  const efficiencyData = GRUNDFOS_CURVE_DATA.efficiencyCurve
  const points = efficiencyData.map(point => ({
    x: chartArea.x + (point.flow / 900) * chartArea.width,
    y: chartArea.y + chartArea.height - (point.efficiency / 100) * chartArea.height
  }))

  ctx.beginPath()
  ctx.moveTo(points[0].x, points[0].y)

  // 使用二次贝塞尔曲线创建平滑的抛物线效率曲线
  for (let i = 1; i < points.length - 1; i++) {
    const cp1x = (points[i].x + points[i - 1].x) / 2
    const cp1y = (points[i].y + points[i - 1].y) / 2
    const cp2x = (points[i].x + points[i + 1].x) / 2
    const cp2y = (points[i].y + points[i + 1].y) / 2

    ctx.quadraticCurveTo(points[i].x, points[i].y, cp2x, cp2y)
  }

  // 最后一段
  ctx.quadraticCurveTo(
    points[points.length - 1].x,
    points[points.length - 1].y,
    points[points.length - 1].x,
    points[points.length - 1].y
  )

  ctx.stroke()
}

// 绘制曲线标签
const drawCurveLabels = (ctx: CanvasRenderingContext2D) => {
  ctx.font = 'bold 14px Arial'

  // 扬程曲线标签
  ctx.fillStyle = '#1f5582'
  ctx.textAlign = 'left'
  ctx.fillText('H [m]', chartArea.x + 50, chartArea.y + 30)

  // 效率曲线标签
  ctx.fillStyle = '#ff8c00'
  ctx.textAlign = 'right'
  ctx.fillText('η [%]', chartArea.x + chartArea.width - 50, chartArea.y + 30)

  // BEP标记
  const bepX = chartArea.x + (600 / 900) * chartArea.width
  const bepY = chartArea.y + chartArea.height - (82 / 100) * chartArea.height

  ctx.fillStyle = '#00aa00'
  ctx.beginPath()
  ctx.arc(bepX, bepY, 6, 0, 2 * Math.PI)
  ctx.fill()

  ctx.font = '12px Arial'
  ctx.textAlign = 'center'
  ctx.fillText('BEP', bepX, bepY - 12)
}

// 绘制图表标题和技术信息
const drawChartTitle = (ctx: CanvasRenderingContext2D) => {
  // 主标题
  ctx.font = 'bold 18px Arial'
  ctx.fillStyle = '#1f5582'
  ctx.textAlign = 'center'
  ctx.fillText('Performance Curves', chartArea.x + chartArea.width / 2, chartArea.y - 60)

  // 副标题 - 产品信息
  ctx.font = 'bold 16px Arial'
  ctx.fillStyle = '#333333'
  ctx.fillText('NBG 300-250-500/525', chartArea.x + chartArea.width / 2, chartArea.y - 40)

  // 技术参数
  ctx.font = '12px Arial'
  ctx.fillStyle = '#666666'
  ctx.fillText('Impeller diameter: 525 mm | Speed: 2950 rpm | 50 Hz', chartArea.x + chartArea.width / 2, chartArea.y - 20)

  // 格兰富Logo区域（文字版）
  ctx.font = 'bold 14px Arial'
  ctx.fillStyle = '#1f5582'
  ctx.textAlign = 'left'
  ctx.fillText('GRUNDFOS', chartArea.x, chartArea.y - 60)

  // 产品编号
  ctx.font = '10px Arial'
  ctx.fillStyle = '#999999'
  ctx.textAlign = 'right'
  ctx.fillText('Product No: 93351275', chartArea.x + chartArea.width, chartArea.y - 60)
  ctx.fillText('Type: AIAF2AESBQQEWW5', chartArea.x + chartArea.width, chartArea.y - 45)

  // 图例
  drawLegend(ctx)
}

// 绘制图例
const drawLegend = (ctx: CanvasRenderingContext2D) => {
  const legendX = chartArea.x + chartArea.width - 200
  const legendY = chartArea.y + 50

  // 图例背景
  ctx.fillStyle = 'rgba(255, 255, 255, 0.9)'
  ctx.fillRect(legendX - 10, legendY - 10, 180, 80)
  ctx.strokeStyle = '#ddd'
  ctx.lineWidth = 1
  ctx.strokeRect(legendX - 10, legendY - 10, 180, 80)

  // 扬程曲线图例
  ctx.strokeStyle = '#1f5582'
  ctx.lineWidth = 3
  ctx.beginPath()
  ctx.moveTo(legendX, legendY)
  ctx.lineTo(legendX + 30, legendY)
  ctx.stroke()

  ctx.fillStyle = '#1f5582'
  ctx.font = '12px Arial'
  ctx.textAlign = 'left'
  ctx.fillText('Head H [m]', legendX + 40, legendY + 4)

  // 效率曲线图例
  ctx.strokeStyle = '#ff8c00'
  ctx.lineWidth = 3
  ctx.beginPath()
  ctx.moveTo(legendX, legendY + 20)
  ctx.lineTo(legendX + 30, legendY + 20)
  ctx.stroke()

  ctx.fillStyle = '#ff8c00'
  ctx.fillText('Efficiency η [%]', legendX + 40, legendY + 24)

  // BEP点图例
  ctx.fillStyle = '#00aa00'
  ctx.beginPath()
  ctx.arc(legendX + 15, legendY + 40, 4, 0, 2 * Math.PI)
  ctx.fill()

  ctx.fillStyle = '#00aa00'
  ctx.fillText('Best Efficiency Point', legendX + 40, legendY + 44)

  // 工作点图例
  ctx.fillStyle = '#ff0000'
  ctx.beginPath()
  ctx.arc(legendX + 15, legendY + 60, 4, 0, 2 * Math.PI)
  ctx.fill()

  ctx.fillStyle = '#ff0000'
  ctx.fillText('Operating Point', legendX + 40, legendY + 64)
}

// 绘制工作点
const drawOperatingPoint = (ctx: CanvasRenderingContext2D) => {
  const x = chartArea.x + (props.operatingPoint.flow / 900) * chartArea.width
  const y = chartArea.y + chartArea.height - (props.operatingPoint.head / 50) * chartArea.height
  
  // 工作点圆圈
  ctx.fillStyle = '#ff0000'
  ctx.beginPath()
  ctx.arc(x, y, 8, 0, 2 * Math.PI)
  ctx.fill()
  
  // 工作点标签
  ctx.fillStyle = '#ff0000'
  ctx.font = '12px Arial'
  ctx.textAlign = 'center'
  ctx.fillText('工作点', x, y - 15)
}

// 绘制鼠标交互
const drawMouseInteraction = (ctx: CanvasRenderingContext2D) => {
  if (!tooltip.visible) return
  
  ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)'
  ctx.lineWidth = 1
  ctx.setLineDash([3, 3])
  
  // 垂直线
  ctx.beginPath()
  ctx.moveTo(mouse.x, chartArea.y)
  ctx.lineTo(mouse.x, chartArea.y + chartArea.height)
  ctx.stroke()
  
  // 水平线
  ctx.beginPath()
  ctx.moveTo(chartArea.x, mouse.y)
  ctx.lineTo(chartArea.x + chartArea.width, mouse.y)
  ctx.stroke()
  
  ctx.setLineDash([])
}

// 重绘图表
const redrawChart = () => {
  drawChart()
}

// 缩放重置动画
const animateZoomReset = () => {
  const startScale = zoom.scale
  const startOffsetX = zoom.offsetX
  const startOffsetY = zoom.offsetY
  
  const duration = 500
  const startTime = Date.now()
  
  const animate = () => {
    const elapsed = Date.now() - startTime
    const progress = Math.min(elapsed / duration, 1)
    
    const easeProgress = 1 - Math.pow(1 - progress, 3)
    
    zoom.scale = startScale + (1 - startScale) * easeProgress
    zoom.offsetX = startOffsetX + (0 - startOffsetX) * easeProgress
    zoom.offsetY = startOffsetY + (0 - startOffsetY) * easeProgress
    
    redrawChart()
    
    if (progress < 1) {
      requestAnimationFrame(animate)
    }
  }
  
  animate()
}

// 控制功能
const toggleFullscreen = () => {
  if (chartContainer.value) {
    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      chartContainer.value.requestFullscreen()
    }
  }
}

const downloadChart = () => {
  const canvas = chartCanvas.value
  if (!canvas) return
  
  const link = document.createElement('a')
  link.download = 'grundfos-pump-curve.png'
  link.href = canvas.toDataURL()
  link.click()
}

// 初始化
onMounted(async () => {
  await nextTick()
  drawChart()
})

// 暴露方法
defineExpose({
  redrawChart,
  resetZoom: animateZoomReset,
  downloadChart
})
</script>

<style lang="scss" scoped>
.grundfos-chart {
  position: relative;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  &__controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    display: flex;
    gap: 8px;
  }
  
  &__control-btn {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    
    &:hover {
      background: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }
  
  &__icon {
    width: 16px;
    height: 16px;
    background-size: contain;
    
    &--fullscreen {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z'/%3E%3C/svg%3E");
    }
    
    &--download {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z'/%3E%3C/svg%3E");
    }
  }
  
  &__container {
    position: relative;
  }
  
  &__canvas {
    display: block;
    border: 1px solid #e9ecef;
    border-radius: 4px;
  }
  
  &__tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;
    z-index: 20;
    min-width: 120px;
  }
  
  &__tooltip-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  &__tooltip-label {
    margin-right: 8px;
  }
  
  &__tooltip-value {
    font-weight: 600;
  }
  
  &__zoom-info {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    z-index: 10;
    
    span {
      display: block;
    }
  }
}
</style>
