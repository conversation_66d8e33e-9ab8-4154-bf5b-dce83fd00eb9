<template>
  <div class="algorithm-metrics">
    <h3 class="section-title">拟合质量评估</h3>
    
    <div class="algorithm-selector">
      <div class="algorithm-options">
        <el-radio-group v-model="compareMode">
          <el-radio :value="false">单算法评估</el-radio>
          <el-radio :value="true">算法比较</el-radio>
        </el-radio-group>
      </div>
      
      <div v-if="compareMode" class="algorithm-compare">
        <el-select 
          v-model="compareAlgorithm" 
          placeholder="选择比较算法"
          style="width: 160px;"
        >
          <el-option 
            v-for="alg in availableAlgorithms" 
            :key="alg.value" 
            :label="alg.label" 
            :value="alg.value"
            :disabled="alg.value === selectedAlgorithm"
          />
        </el-select>
        <el-button 
          type="primary" 
          size="small" 
          @click="runComparison"
          :loading="comparing"
        >
          运行比较
        </el-button>
      </div>
    </div>
    
    <div class="metrics-container">
      <div class="metric-card" :data-quality="getQualityLevel(metrics.rSquared)">
        <div class="metric-header">
          <div class="metric-icon">
            <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="2" fill="none">
              <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
            </svg>
          </div>
          <div class="metric-title">R² 决定系数</div>
        </div>
        <div class="metric-value">{{ formatValue(metrics.rSquared) }}</div>
        <div class="metric-quality-bar">
          <div class="quality-indicator" :style="{width: `${metrics.rSquared * 100}%`}"></div>
        </div>
        <div class="metric-description">
          衡量模型解释数据变异性的能力，值越接近1越好
        </div>
      </div>
      
      <div class="metric-card" :data-quality="getQualityLevelInverse(metrics.mse)">
        <div class="metric-header">
          <div class="metric-icon">
            <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="2" fill="none">
              <path d="M2 20h.01"></path>
              <path d="M7 20v-4"></path>
              <path d="M12 20v-8"></path>
              <path d="M17 20v-6"></path>
              <path d="M22 20v-2"></path>
              <path d="M22 4l-10 10-4-4-6 6"></path>
            </svg>
          </div>
          <div class="metric-title">均方误差</div>
        </div>
        <div class="metric-value">{{ formatValue(metrics.mse) }}</div>
        <div class="metric-quality-bar inverse">
          <div 
            class="quality-indicator" 
            :style="{width: `${100 - Math.min(metrics.mse * 800, 100)}%`}"
          ></div>
        </div>
        <div class="metric-description">
          预测值与实际值差异的均方，值越小越好
        </div>
      </div>
      
      <div class="metric-card" :data-quality="getSpeedLevel(metrics.computeTime)">
        <div class="metric-header">
          <div class="metric-icon">
            <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="2" fill="none">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
          </div>
          <div class="metric-title">计算时间</div>
        </div>
        <div class="metric-value">{{ metrics.computeTime }}<span class="unit">ms</span></div>
        <div class="metric-quality-bar inverse">
          <div 
            class="quality-indicator" 
            :style="{width: `${100 - Math.min(metrics.computeTime / 2, 100)}%`}"
          ></div>
        </div>
        <div class="metric-description">
          算法运行耗时，影响实时性能
        </div>
      </div>
    </div>
    
    <div v-if="compareMode && comparisonResult" class="comparison-result">
      <h4>算法比较结果</h4>
      <div class="comparison-table">
        <table>
          <thead>
            <tr>
              <th>指标</th>
              <th>{{ getAlgorithmLabel(selectedAlgorithm) }}</th>
              <th>{{ getAlgorithmLabel(compareAlgorithm) }}</th>
              <th>差异</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>R² 决定系数</td>
              <td>{{ formatValue(metrics.rSquared) }}</td>
              <td>{{ formatValue(comparisonResult.rSquared) }}</td>
              <td :class="getCompareClass(metrics.rSquared, comparisonResult.rSquared, true)">
                {{ formatDiff(metrics.rSquared, comparisonResult.rSquared, true) }}
              </td>
            </tr>
            <tr>
              <td>均方误差</td>
              <td>{{ formatValue(metrics.mse) }}</td>
              <td>{{ formatValue(comparisonResult.mse) }}</td>
              <td :class="getCompareClass(metrics.mse, comparisonResult.mse, false)">
                {{ formatDiff(metrics.mse, comparisonResult.mse, false) }}
              </td>
            </tr>
            <tr>
              <td>计算时间</td>
              <td>{{ metrics.computeTime }}ms</td>
              <td>{{ comparisonResult.computeTime }}ms</td>
              <td :class="getCompareClass(metrics.computeTime, comparisonResult.computeTime, false)">
                {{ formatDiff(metrics.computeTime, comparisonResult.computeTime, false) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="comparison-summary">
        <p v-if="getOverallBetter() === 'current'">
          当前算法 <strong>{{ getAlgorithmLabel(selectedAlgorithm) }}</strong> 整体表现更好
        </p>
        <p v-else-if="getOverallBetter() === 'compare'">
          比较算法 <strong>{{ getAlgorithmLabel(compareAlgorithm) }}</strong> 整体表现更好
        </p>
        <p v-else>
          两种算法各有优势，可根据具体需求选择
        </p>
      </div>
    </div>
    
    <div class="metrics-analysis">
      <div class="analysis-header">
        <div class="analysis-icon">
          <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="2" fill="none">
            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
            <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
            <line x1="12" y1="22.08" x2="12" y2="12"></line>
          </svg>
        </div>
        <div class="analysis-title">拟合效果分析</div>
      </div>
      <div class="analysis-content">
        <p v-if="metrics.rSquared > 0.95">
          当前算法拟合效果<strong>极佳</strong>，模型能够精确地描述数据特征。
        </p>
        <p v-else-if="metrics.rSquared > 0.85">
          当前算法拟合效果<strong>良好</strong>，模型能够较好地描述数据特征。
        </p>
        <p v-else-if="metrics.rSquared > 0.7">
          当前算法拟合效果<strong>一般</strong>，可以考虑调整参数或尝试其他算法。
        </p>
        <p v-else>
          当前算法拟合效果<strong>较差</strong>，建议调整参数或更换更适合的算法。
        </p>
        
        <p v-if="metrics.computeTime < 50">
          计算速度<strong>极快</strong>，适合实时应用场景。
        </p>
        <p v-else-if="metrics.computeTime < 100">
          计算速度<strong>较快</strong>，满足大多数应用需求。
        </p>
        <p v-else-if="metrics.computeTime < 200">
          计算速度<strong>一般</strong>，可能会影响实时性能。
        </p>
        <p v-else>
          计算速度<strong>较慢</strong>，不建议用于需要实时响应的场景。
        </p>
        
        <p v-if="selectedAlgorithm === 'polynomial'">
          多项式拟合算法对于平滑曲线和水泵的QH曲线通常有较好的表现，特别适合小型数据集。
          可以通过调整多项式阶数来控制曲线的灵活性，阶数越高拟合越精确但过拟合风险也越大。
        </p>
        <p v-else-if="selectedAlgorithm === 'spline'">
          样条插值在保持数据点精确性的同时能提供平滑的曲线，特别适合有多个转折点的水泵性能曲线。
          它能在不同区间使用不同的多项式函数，较好地捕捉水泵在不同流量区间的表现。
        </p>
        <p v-else-if="selectedAlgorithm === 'neural'">
          神经网络算法具有强大的非线性拟合能力，适合复杂的水泵性能曲线，但计算开销较大，
          且需要足够多的数据点才能发挥优势。
        </p>
        <p v-else-if="selectedAlgorithm === 'bezier'">
          贝塞尔曲线提供了一种直观的方式来控制曲线形状，适合需要手动调整曲线形态的场景，
          但在精确拟合方面可能不如其他算法。
        </p>
        <p v-else-if="selectedAlgorithm === 'linear'">
          线性拟合算法计算速度最快，但只能处理线性关系，对于水泵性能曲线这种非线性关系，
          通常需要分段处理才能获得合理结果。
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { MetricsType, AlgorithmParamsType } from './types'

const props = defineProps<{
  selectedAlgorithm?: string;
  dataPoints?: any[];
  algorithmParams?: AlgorithmParamsType;
  metrics?: MetricsType;
}>()

// 算法比较模式
const compareMode = ref(false)
const compareAlgorithm = ref('')
const comparing = ref(false)
const comparisonResult = ref<MetricsType | null>(null)

// 可用的算法列表
const availableAlgorithms = [
  { value: 'polynomial', label: '多项式拟合' },
  { value: 'spline', label: '样条插值' },
  { value: 'neural', label: '神经网络' },
  { value: 'bezier', label: '贝塞尔曲线' },
  { value: 'linear', label: '线性拟合' }
]

// 获取算法显示名称
const getAlgorithmLabel = (value: string | undefined): string => {
  if (!value) return '未知算法'
  const alg = availableAlgorithms.find(a => a.value === value)
  return alg ? alg.label : value
}

// 计算指标，可以基于传入的算法、数据点和参数来生成
const metrics = computed<MetricsType>(() => {
  if (props.metrics) {
    return props.metrics;
  }
  
  // 如果没有传入metrics，则基于算法和数据点模拟生成一些指标
  let rSquared = 0.85;
  let mse = 0.03;
  let mae = 0.15;
  let computeTime = 80;
  
  if (props.selectedAlgorithm && props.dataPoints) {
    switch (props.selectedAlgorithm) {
      case 'linear':
        rSquared = 0.85 + Math.random() * 0.05;
        mse = 0.04 + Math.random() * 0.02;
        computeTime = 30 + Math.random() * 20;
        break;
      case 'polynomial':
        rSquared = 0.93 + Math.random() * 0.04;
        mse = 0.02 + Math.random() * 0.01;
        computeTime = 50 + Math.random() * 30;
        break;
      case 'spline':
        rSquared = 0.96 + Math.random() * 0.03;
        mse = 0.01 + Math.random() * 0.01;
        computeTime = 70 + Math.random() * 40;
        break;
      case 'neural':
        rSquared = 0.97 + Math.random() * 0.02;
        mse = 0.005 + Math.random() * 0.01;
        computeTime = 120 + Math.random() * 80;
        break;
      case 'bezier':
        rSquared = 0.88 + Math.random() * 0.04;
        mse = 0.03 + Math.random() * 0.02;
        computeTime = 40 + Math.random() * 25;
        break;
    }
    
    // 数据点越多，计算时间越长
    if (props.dataPoints.length > 10) {
      computeTime *= 1.2;
    }
    
    // 如果是多项式拟合，考虑阶数的影响
    if (props.selectedAlgorithm === 'polynomial' && props.algorithmParams?.polynomial) {
      const degree = props.algorithmParams.polynomial.degree;
      if (degree > 3) {
        rSquared += (degree - 3) * 0.01; // 阶数越高，R²越高
        computeTime += (degree - 3) * 15; // 阶数越高，计算时间越长
      }
    }
  }
  
  mae = mse * 2.5 + Math.random() * 0.05;
  
  return {
    rSquared,
    mse,
    mae,
    computeTime
  };
})

// 运行算法比较
const runComparison = () => {
  if (!compareAlgorithm.value) return;
  
  comparing.value = true;
  
  setTimeout(() => {
    // 模拟算法比较结果
    const baseMetrics = {
      rSquared: 0.9,
      mse: 0.025,
      mae: 0.08,
      computeTime: 70
    };
    
    switch (compareAlgorithm.value) {
      case 'linear':
        baseMetrics.rSquared = 0.83 + Math.random() * 0.05;
        baseMetrics.mse = 0.045 + Math.random() * 0.02;
        baseMetrics.computeTime = 25 + Math.random() * 15;
        break;
      case 'polynomial':
        baseMetrics.rSquared = 0.91 + Math.random() * 0.04;
        baseMetrics.mse = 0.025 + Math.random() * 0.01;
        baseMetrics.computeTime = 55 + Math.random() * 25;
        break;
      case 'spline':
        baseMetrics.rSquared = 0.94 + Math.random() * 0.03;
        baseMetrics.mse = 0.015 + Math.random() * 0.01;
        baseMetrics.computeTime = 65 + Math.random() * 35;
        break;
      case 'neural':
        baseMetrics.rSquared = 0.96 + Math.random() * 0.02;
        baseMetrics.mse = 0.008 + Math.random() * 0.007;
        baseMetrics.computeTime = 130 + Math.random() * 70;
        break;
      case 'bezier':
        baseMetrics.rSquared = 0.86 + Math.random() * 0.04;
        baseMetrics.mse = 0.035 + Math.random() * 0.015;
        baseMetrics.computeTime = 35 + Math.random() * 20;
        break;
    }
    
    baseMetrics.mae = baseMetrics.mse * 2.5 + Math.random() * 0.05;
    
    comparisonResult.value = baseMetrics;
    comparing.value = false;
  }, 800); // 模拟计算延迟
};

// 格式化数值，保留4位小数
const formatValue = (value: number): string => {
  return Number(value).toFixed(4)
}

// 格式化比较差异
const formatDiff = (current: number, compare: number, higherIsBetter: boolean): string => {
  const diff = current - compare;
  const percentDiff = Math.abs(diff) / ((current + compare) / 2) * 100;
  
  if (Math.abs(percentDiff) < 1) {
    return '相似';
  }
  
  const sign = diff > 0 ? '+' : '-';
  const isImproved = (higherIsBetter && diff > 0) || (!higherIsBetter && diff < 0);
  
  return `${isImproved ? '优' : '劣'} ${sign}${percentDiff.toFixed(1)}%`;
}

// 获取比较结果的CSS类
const getCompareClass = (current: number, compare: number, higherIsBetter: boolean): string => {
  const diff = current - compare;
  const isImproved = (higherIsBetter && diff > 0) || (!higherIsBetter && diff < 0);
  
  if (Math.abs(diff) < 0.01) {
    return 'neutral';
  }
  
  return isImproved ? 'better' : 'worse';
}

// 获取整体哪个算法更好
const getOverallBetter = (): 'current' | 'compare' | 'neutral' => {
  if (!comparisonResult.value) return 'neutral';
  
  let currentScore = 0;
  let compareScore = 0;
  
  // R²越高越好
  if (metrics.value.rSquared > comparisonResult.value.rSquared + 0.02) {
    currentScore += 1;
  } else if (comparisonResult.value.rSquared > metrics.value.rSquared + 0.02) {
    compareScore += 1;
  }
  
  // MSE越低越好
  if (metrics.value.mse < comparisonResult.value.mse * 0.8) {
    currentScore += 1;
  } else if (comparisonResult.value.mse < metrics.value.mse * 0.8) {
    compareScore += 1;
  }
  
  // 计算时间越短越好
  if (metrics.value.computeTime < comparisonResult.value.computeTime * 0.7) {
    currentScore += 1;
  } else if (comparisonResult.value.computeTime < metrics.value.computeTime * 0.7) {
    compareScore += 1;
  }
  
  if (currentScore > compareScore) {
    return 'current';
  } else if (compareScore > currentScore) {
    return 'compare';
  } else {
    return 'neutral';
  }
}

// 获取R²的质量等级
const getQualityLevel = (value: number): string => {
  if (value > 0.95) return 'excellent'
  if (value > 0.85) return 'good'
  if (value > 0.7) return 'average'
  return 'poor'
}

// 获取MSE的质量等级（反向，值越小越好）
const getQualityLevelInverse = (value: number): string => {
  if (value < 0.01) return 'excellent'
  if (value < 0.05) return 'good'
  if (value < 0.1) return 'average'
  return 'poor'
}

// 获取计算时间的质量等级（反向，值越小越好）
const getSpeedLevel = (value: number): string => {
  if (value < 50) return 'excellent'
  if (value < 100) return 'good'
  if (value < 200) return 'average'
  return 'poor'
}
</script>

<style lang="scss" scoped>
.algorithm-metrics {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--el-color-primary-dark-2);
  position: relative;
  padding-left: 0.75rem;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0.25rem;
    bottom: 0.25rem;
    width: 4px;
    background-color: var(--el-color-primary);
    border-radius: 2px;
  }
}

.algorithm-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 0.75rem 1rem;
  background-color: var(--el-fill-color-light);
  border-radius: 0.5rem;
  
  .algorithm-compare {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }
}

.metrics-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.metric-card {
  background-color: var(--el-bg-color);
  border-radius: 0.5rem;
  padding: 1.25rem;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border-left: 4px solid transparent;
  
  &[data-quality="excellent"] {
    border-left-color: #67c23a;
  }
  
  &[data-quality="good"] {
    border-left-color: #e6a23c;
  }
  
  &[data-quality="average"] {
    border-left-color: #f56c6c;
  }
  
  &[data-quality="poor"] {
    border-left-color: #909399;
  }
  
  .metric-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    
    .metric-icon {
      color: #909399;
      margin-right: 0.5rem;
      
      svg {
        vertical-align: middle;
      }
    }
    
    .metric-title {
      font-weight: 600;
      color: #303133;
    }
  }
  
  .metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #303133;
    margin-bottom: 0.75rem;
    
    .unit {
      font-size: 1rem;
      font-weight: 400;
      color: #909399;
      margin-left: 0.25rem;
    }
  }
  
  .metric-quality-bar {
    height: 6px;
    background-color: var(--el-fill-color-lighter);
    border-radius: 3px;
    margin-bottom: 0.75rem;
    overflow: hidden;
    
    .quality-indicator {
      height: 100%;
      background-color: var(--el-color-primary);
      border-radius: 3px;
    }
    
    &.inverse .quality-indicator {
      background-color: var(--el-color-success);
    }
  }
  
  .metric-description {
    font-size: 0.875rem;
    color: #606266;
  }
}

.comparison-result {
  background-color: var(--el-bg-color);
  border-radius: 0.5rem;
  padding: 1.25rem;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
  
  h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 1rem;
    color: #303133;
  }
  
  .comparison-table {
    margin-bottom: 1rem;
    overflow-x: auto;
    
    table {
      width: 100%;
      border-collapse: collapse;
      
      th, td {
        padding: 0.75rem;
        text-align: left;
        border-bottom: 1px solid var(--el-border-color-lighter);
      }
      
      th {
        font-weight: 600;
        color: #303133;
        background-color: var(--el-fill-color-light);
      }
      
      td.better {
        color: var(--el-color-success);
        font-weight: 600;
      }
      
      td.worse {
        color: var(--el-color-danger);
        font-weight: 600;
      }
      
      td.neutral {
        color: var(--el-color-info);
      }
    }
  }
  
  .comparison-summary {
    font-size: 1rem;
    color: #303133;
    
    p {
      margin: 0.5rem 0;
    }
    
    strong {
      color: var(--el-color-primary);
    }
  }
}

.metrics-analysis {
  background-color: var(--el-bg-color);
  border-radius: 0.5rem;
  padding: 1.25rem;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  
  .analysis-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    
    .analysis-icon {
      color: var(--el-color-primary);
      margin-right: 0.5rem;
    }
    
    .analysis-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .analysis-content {
    p {
      margin: 0.75rem 0;
      color: #606266;
      
      strong {
        font-weight: 600;
        color: #303133;
      }
      
      &:first-child {
        margin-top: 0;
      }
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style> 