import { ref, computed, onMounted, onUnmounted } from 'vue'
import { debounce } from '@/utils/mobile-optimization'

// 断点定义
export const breakpoints = {
  xs: 480,
  sm: 768,
  md: 992,
  lg: 1200,
  xl: 1920
} as const

export type Breakpoint = keyof typeof breakpoints

// 响应式组合函数
export function useResponsive() {
  const windowWidth = ref(0)
  const windowHeight = ref(0)

  // 更新窗口尺寸
  const updateSize = () => {
    windowWidth.value = window.innerWidth
    windowHeight.value = window.innerHeight
  }

  // 防抖的resize处理函数
  const debouncedResize = debounce(updateSize, 100)

  // 当前断点
  const currentBreakpoint = computed<Breakpoint>(() => {
    const width = windowWidth.value
    if (width < breakpoints.xs) return 'xs'
    if (width < breakpoints.sm) return 'sm'
    if (width < breakpoints.md) return 'md'
    if (width < breakpoints.lg) return 'lg'
    return 'xl'
  })

  // 设备类型判断
  const isMobile = computed(() => windowWidth.value < breakpoints.sm)
  const isTablet = computed(() => 
    windowWidth.value >= breakpoints.sm && windowWidth.value < breakpoints.md
  )
  const isDesktop = computed(() => windowWidth.value >= breakpoints.md)

  // 屏幕方向
  const isLandscape = computed(() => windowWidth.value > windowHeight.value)
  const isPortrait = computed(() => windowWidth.value <= windowHeight.value)

  // 断点匹配函数
  const matches = (breakpoint: Breakpoint) => {
    return computed(() => windowWidth.value >= breakpoints[breakpoint])
  }

  // 范围匹配函数
  const between = (min: Breakpoint, max: Breakpoint) => {
    return computed(() => 
      windowWidth.value >= breakpoints[min] && windowWidth.value < breakpoints[max]
    )
  }

  // 小于指定断点
  const smaller = (breakpoint: Breakpoint) => {
    return computed(() => windowWidth.value < breakpoints[breakpoint])
  }

  // 大于指定断点
  const greater = (breakpoint: Breakpoint) => {
    return computed(() => windowWidth.value >= breakpoints[breakpoint])
  }

  onMounted(() => {
    updateSize()
    window.addEventListener('resize', debouncedResize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', debouncedResize)
  })

  return {
    // 窗口尺寸
    windowWidth: readonly(windowWidth),
    windowHeight: readonly(windowHeight),
    
    // 当前断点
    currentBreakpoint,
    
    // 设备类型
    isMobile,
    isTablet,
    isDesktop,
    
    // 屏幕方向
    isLandscape,
    isPortrait,
    
    // 断点匹配函数
    matches,
    between,
    smaller,
    greater,
    
    // 工具函数
    updateSize
  }
}

// 触摸设备检测
export function useTouchDevice() {
  const isTouchDevice = ref(false)
  const isIOS = ref(false)
  const isAndroid = ref(false)

  const detectDevice = () => {
    isTouchDevice.value = 'ontouchstart' in window || navigator.maxTouchPoints > 0
    isIOS.value = /iPad|iPhone|iPod/.test(navigator.userAgent)
    isAndroid.value = /Android/.test(navigator.userAgent)
  }

  onMounted(() => {
    detectDevice()
  })

  return {
    isTouchDevice: readonly(isTouchDevice),
    isIOS: readonly(isIOS),
    isAndroid: readonly(isAndroid)
  }
}

// 网络状态检测
export function useNetworkStatus() {
  const isOnline = ref(navigator.onLine)
  const connectionType = ref<string>('')
  const effectiveType = ref<string>('')

  const updateNetworkStatus = () => {
    isOnline.value = navigator.onLine
    
    // 检测网络连接类型
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection
    
    if (connection) {
      connectionType.value = connection.type || ''
      effectiveType.value = connection.effectiveType || ''
    }
  }

  const handleOnline = () => {
    isOnline.value = true
    updateNetworkStatus()
  }

  const handleOffline = () => {
    isOnline.value = false
  }

  onMounted(() => {
    updateNetworkStatus()
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)
    
    // 监听网络变化
    const connection = (navigator as any).connection
    if (connection) {
      connection.addEventListener('change', updateNetworkStatus)
    }
  })

  onUnmounted(() => {
    window.removeEventListener('online', handleOnline)
    window.removeEventListener('offline', handleOffline)
    
    const connection = (navigator as any).connection
    if (connection) {
      connection.removeEventListener('change', updateNetworkStatus)
    }
  })

  return {
    isOnline: readonly(isOnline),
    connectionType: readonly(connectionType),
    effectiveType: readonly(effectiveType),
    isSlowConnection: computed(() => 
      effectiveType.value === 'slow-2g' || effectiveType.value === '2g'
    )
  }
}

// 电池状态检测
export function useBatteryStatus() {
  const batteryLevel = ref(1)
  const isCharging = ref(false)
  const chargingTime = ref(0)
  const dischargingTime = ref(Infinity)

  const updateBatteryStatus = (battery: any) => {
    batteryLevel.value = battery.level
    isCharging.value = battery.charging
    chargingTime.value = battery.chargingTime
    dischargingTime.value = battery.dischargingTime
  }

  onMounted(async () => {
    if ('getBattery' in navigator) {
      try {
        const battery = await (navigator as any).getBattery()
        updateBatteryStatus(battery)
        
        battery.addEventListener('chargingchange', () => updateBatteryStatus(battery))
        battery.addEventListener('levelchange', () => updateBatteryStatus(battery))
        battery.addEventListener('chargingtimechange', () => updateBatteryStatus(battery))
        battery.addEventListener('dischargingtimechange', () => updateBatteryStatus(battery))
      } catch (error) {
        console.warn('Battery API not supported:', error)
      }
    }
  })

  return {
    batteryLevel: readonly(batteryLevel),
    isCharging: readonly(isCharging),
    chargingTime: readonly(chargingTime),
    dischargingTime: readonly(dischargingTime),
    isLowBattery: computed(() => batteryLevel.value < 0.2 && !isCharging.value)
  }
}

// 性能监控
export function usePerformanceMonitor() {
  const memoryUsage = ref({ used: 0, total: 0, limit: 0 })
  const fps = ref(60)
  const isSlowDevice = ref(false)

  let frameCount = 0
  let lastTime = performance.now()
  let animationId: number

  const measureFPS = () => {
    frameCount++
    const currentTime = performance.now()
    
    if (currentTime - lastTime >= 1000) {
      fps.value = Math.round((frameCount * 1000) / (currentTime - lastTime))
      frameCount = 0
      lastTime = currentTime
      
      // 判断是否为低性能设备
      isSlowDevice.value = fps.value < 30
    }
    
    animationId = requestAnimationFrame(measureFPS)
  }

  const updateMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      memoryUsage.value = {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
      }
    }
  }

  onMounted(() => {
    measureFPS()
    updateMemoryUsage()
    
    // 定期更新内存使用情况
    const memoryInterval = setInterval(updateMemoryUsage, 5000)
    
    onUnmounted(() => {
      cancelAnimationFrame(animationId)
      clearInterval(memoryInterval)
    })
  })

  return {
    fps: readonly(fps),
    memoryUsage: readonly(memoryUsage),
    isSlowDevice: readonly(isSlowDevice)
  }
}

// 组合所有响应式功能
export function useResponsiveFeatures() {
  const responsive = useResponsive()
  const touch = useTouchDevice()
  const network = useNetworkStatus()
  const battery = useBatteryStatus()
  const performance = usePerformanceMonitor()

  // 综合判断是否需要性能优化
  const needsOptimization = computed(() => {
    return responsive.isMobile.value || 
           network.isSlowConnection.value || 
           battery.isLowBattery.value || 
           performance.isSlowDevice.value
  })

  return {
    ...responsive,
    ...touch,
    ...network,
    ...battery,
    ...performance,
    needsOptimization
  }
}
