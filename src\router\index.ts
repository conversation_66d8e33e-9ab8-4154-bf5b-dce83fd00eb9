import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/views/Layout/index.vue'),
    redirect: '/pump-curve',
    children: [
      {
        path: '/pump-curve',
        name: 'PumpCurve',
        component: () => import('@/views/PumpCurve/index.vue'),
        meta: {
          title: '水泵曲线',
          icon: 'DataLine'
        }
      },
      {
        path: '/real-time-energy',
        name: 'RealTimeEnergy',
        component: () => import('@/views/RealTimeEnergy/index.vue'),
        meta: {
          title: '实时能耗',
          icon: 'Lightning'
        }
      },
      {
        path: '/energy-statistics',
        name: 'EnergyStatistics',
        component: () => import('@/views/EnergyStatistics/index.vue'),
        meta: {
          title: '能耗统计',
          icon: 'DataAnalysis'
        }
      },
      {
        path: '/intelligent-optimization',
        name: 'IntelligentOptimization',
        component: () => import('@/views/IntelligentOptimization/index.vue'),
        meta: {
          title: '智能寻优',
          icon: 'MagicStick'
        }
      },
      {
        path: '/warning',
        name: 'Warning',
        component: () => import('@/views/Warning/index.vue'),
        meta: {
          title: '故障预警',
          icon: 'Warning'
        }
      },
      {
        path: '/settings',
        name: 'Settings',
        component: () => import('@/views/Settings/index.vue'),
        meta: {
          title: '系统设置',
          icon: 'Setting'
        }
      },
      {
        path: '/grundfos-exact-copy',
        name: 'GrundfosExactCopy',
        component: () => import('@/views/GrundfosExactCopy/index.vue'),
        meta: {
          title: '高精度曲线',
          icon: 'TrendCharts'
        }
      },
      {
        path: '/gateway-management',
        name: 'GatewayManagement',
        component: () => import('@/views/GatewayManagement/index.vue'),
        meta: {
          title: '变频器网关管理',
          icon: 'Connection'
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
