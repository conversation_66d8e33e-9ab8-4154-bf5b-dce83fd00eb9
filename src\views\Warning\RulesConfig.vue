<template>
  <div class="rules-config">
    <el-card>
      <template #header>
        <div class="rules-header">
          <span>预警规则配置</span>
          <div class="header-actions">
            <el-button size="small" type="primary" @click="showAddRule">
              <el-icon><Plus /></el-icon>
              添加规则
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 规则列表 -->
      <el-table :data="rules" stripe v-loading="warningStore.loading">
        <el-table-column prop="name" label="规则名称" width="150" />
        
        <el-table-column prop="description" label="描述" min-width="200" />
        
        <el-table-column prop="category" label="类别" width="120">
          <template #default="{ row }">
            <el-tag :type="getCategoryType(row.category)" size="small">
              {{ getCategoryText(row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="severity" label="严重程度" width="100">
          <template #default="{ row }">
            <el-tag :type="getSeverityType(row.severity)" size="small">
              {{ getSeverityText(row.severity) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="enabled" label="状态" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.enabled"
              @change="toggleRule(row.id)"
            />
          </template>
        </el-table-column>
        
        <el-table-column prop="conditions" label="触发条件" min-width="200">
          <template #default="{ row }">
            <div class="conditions">
              <div v-for="condition in row.conditions" :key="condition.parameter" class="condition-item">
                {{ condition.parameter }} {{ getOperatorText(condition.operator) }} {{ condition.value }}
                <span v-if="condition.duration">(持续{{ condition.duration }}分钟)</span>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="cooldownMinutes" label="冷却时间" width="100">
          <template #default="{ row }">
            {{ row.cooldownMinutes }}分钟
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button size="small" @click="editRule(row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteRule(row.id)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 规则编辑对话框 -->
    <RuleEditDialog 
      v-model="showRuleDialog"
      :rule="selectedRule"
      @save="saveRule"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { useWarningStore } from '@/stores/warning'
import RuleEditDialog from './RuleEditDialog.vue'
import type { WarningRule, WarningCategory, WarningSeverity } from '@/types'
import { ElMessage, ElMessageBox } from 'element-plus'

const warningStore = useWarningStore()

// 响应式数据
const showRuleDialog = ref(false)
const selectedRule = ref<WarningRule | null>(null)

// 计算属性
const rules = computed(() => warningStore.rules)

// 方法
const getCategoryType = (category: WarningCategory) => {
  const types = {
    equipment_fault: 'danger',
    performance_anomaly: 'warning',
    maintenance_due: 'info',
    energy_consumption: 'primary',
    efficiency_drop: 'warning',
    vibration_high: 'danger',
    temperature_abnormal: 'danger',
    pressure_abnormal: 'warning',
    flow_abnormal: 'warning',
    system_error: 'danger'
  }
  return types[category] || 'info'
}

const getCategoryText = (category: WarningCategory) => {
  const texts = {
    equipment_fault: '设备故障',
    performance_anomaly: '性能异常',
    maintenance_due: '维护到期',
    energy_consumption: '能耗异常',
    efficiency_drop: '效率下降',
    vibration_high: '振动过高',
    temperature_abnormal: '温度异常',
    pressure_abnormal: '压力异常',
    flow_abnormal: '流量异常',
    system_error: '系统错误'
  }
  return texts[category] || category
}

const getSeverityType = (severity: WarningSeverity) => {
  const types = {
    critical: 'danger',
    high: 'warning',
    medium: 'primary',
    low: 'info'
  }
  return types[severity] || 'info'
}

const getSeverityText = (severity: WarningSeverity) => {
  const texts = {
    critical: '严重',
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[severity] || severity
}

const getOperatorText = (operator: string) => {
  const texts = {
    gt: '>',
    lt: '<',
    eq: '=',
    gte: '>=',
    lte: '<=',
    between: '介于'
  }
  return (texts as Record<string, string>)[operator] || operator
}

const showAddRule = () => {
  selectedRule.value = null
  showRuleDialog.value = true
}

const editRule = (rule: WarningRule) => {
  selectedRule.value = rule
  showRuleDialog.value = true
}

const saveRule = (rule: WarningRule) => {
  if (selectedRule.value) {
    // 更新规则
    warningStore.updateWarningRule(rule.id, rule)
    ElMessage.success('规则更新成功')
  } else {
    // 添加新规则
    warningStore.addWarningRule(rule)
    ElMessage.success('规则添加成功')
  }
  showRuleDialog.value = false
}

const toggleRule = (ruleId: string) => {
  const enabled = warningStore.toggleWarningRule(ruleId)
  ElMessage.success(`规则已${enabled ? '启用' : '禁用'}`)
}

const deleteRule = async (ruleId: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个预警规则吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const success = warningStore.deleteWarningRule(ruleId)
    if (success) {
      ElMessage.success('规则删除成功')
    } else {
      ElMessage.error('规则删除失败')
    }
  } catch {
    // 用户取消
  }
}
</script>

<style lang="scss" scoped>
.rules-config {
  .rules-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
  }
  
  .conditions {
    .condition-item {
      font-size: 12px;
      color: var(--el-text-color-regular);
      margin-bottom: 4px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
