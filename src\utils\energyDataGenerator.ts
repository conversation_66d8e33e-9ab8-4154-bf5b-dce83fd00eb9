import type { EnergyData, EnergyStatistics, EnergyTrend, StatisticsPeriod, EnergyAlert } from '@/types'
import dayjs from 'dayjs'

/**
 * 能耗数据生成器
 */
export class EnergyDataGenerator {
  private baseConsumption = 85 // 基础功率 kW
  private efficiencyBase = 78 // 基础效率 %
  private costPerKwh = 0.8 // 电价 元/kWh
  
  /**
   * 生成指定时间范围的能耗数据
   */
  generateEnergyData(startDate: string, endDate: string, intervalMinutes = 15): EnergyData[] {
    const data: EnergyData[] = []
    const start = dayjs(startDate)
    const end = dayjs(endDate)
    let current = start
    
    while (current.isBefore(end) || current.isSame(end)) {
      const hour = current.hour()
      const dayOfWeek = current.day()
      
      // 模拟不同时间段的负荷变化
      const timeMultiplier = this.getTimeMultiplier(hour, dayOfWeek)
      const seasonMultiplier = this.getSeasonMultiplier(current.month())
      const randomFactor = 0.9 + Math.random() * 0.2 // 0.9-1.1的随机因子
      
      const power = this.baseConsumption * timeMultiplier * seasonMultiplier * randomFactor
      const efficiency = Math.min(95, Math.max(60, 
        this.efficiencyBase + (Math.random() - 0.5) * 20 + this.getEfficiencyBonus(power)
      ))
      
      // 根据功率和效率计算其他参数
      const flow = (power * efficiency / 100) * 12 // 简化的流量计算
      const head = 40 + (Math.random() - 0.5) * 10 // 扬程变化
      const frequency = 45 + (power / this.baseConsumption) * 10 // 频率变化
      const temperature = 25 + (Math.random() - 0.5) * 15 // 温度变化
      const vibration = 1 + Math.random() * 2 // 振动
      
      const energy = power * (intervalMinutes / 60) // kWh
      const cost = energy * this.costPerKwh
      
      data.push({
        timestamp: current.toISOString(),
        power: Math.round(power * 100) / 100,
        energy: Math.round(energy * 1000) / 1000,
        efficiency: Math.round(efficiency * 10) / 10,
        cost: Math.round(cost * 100) / 100,
        flow: Math.round(flow * 10) / 10,
        head: Math.round(head * 10) / 10,
        frequency: Math.round(frequency * 10) / 10,
        temperature: Math.round(temperature * 10) / 10,
        vibration: Math.round(vibration * 100) / 100
      })
      
      current = current.add(intervalMinutes, 'minute')
    }
    
    return data
  }
  
  /**
   * 获取时间段负荷系数
   */
  private getTimeMultiplier(hour: number, dayOfWeek: number): number {
    // 工作日和周末的不同模式
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6
    
    if (isWeekend) {
      // 周末模式：相对平稳
      if (hour >= 8 && hour <= 18) return 0.7
      if (hour >= 19 && hour <= 22) return 0.8
      return 0.5
    } else {
      // 工作日模式：明显的峰谷差
      if (hour >= 6 && hour <= 8) return 0.9 // 早高峰
      if (hour >= 9 && hour <= 17) return 1.0 // 工作时间
      if (hour >= 18 && hour <= 21) return 1.1 // 晚高峰
      if (hour >= 22 || hour <= 5) return 0.6 // 夜间
      return 0.8
    }
  }
  
  /**
   * 获取季节系数
   */
  private getSeasonMultiplier(month: number): number {
    // 夏季和冬季用电量较高
    if (month >= 5 && month <= 8) return 1.2 // 夏季
    if (month >= 11 || month <= 2) return 1.1 // 冬季
    return 1.0 // 春秋季
  }
  
  /**
   * 获取效率加成
   */
  private getEfficiencyBonus(power: number): number {
    const loadRate = power / this.baseConsumption
    // 在额定负荷附近效率最高
    if (loadRate >= 0.7 && loadRate <= 0.9) return 5
    if (loadRate >= 0.5 && loadRate <= 1.2) return 2
    return -3
  }
  
  /**
   * 计算统计数据
   */
  calculateStatistics(data: EnergyData[], period: StatisticsPeriod): EnergyStatistics {
    if (data.length === 0) {
      throw new Error('数据为空，无法计算统计信息')
    }
    
    const totalEnergy = data.reduce((sum, item) => sum + item.energy, 0)
    const totalCost = data.reduce((sum, item) => sum + item.cost, 0)
    const totalFlow = data.reduce((sum, item) => sum + item.flow, 0) * (15 / 60) // 转换为实际流量
    
    const powers = data.map(item => item.power)
    const efficiencies = data.map(item => item.efficiency)
    
    const avgPower = powers.reduce((sum, val) => sum + val, 0) / powers.length
    const maxPower = Math.max(...powers)
    const minPower = Math.min(...powers)
    
    const avgEfficiency = efficiencies.reduce((sum, val) => sum + val, 0) / efficiencies.length
    const maxEfficiency = Math.max(...efficiencies)
    const minEfficiency = Math.min(...efficiencies)
    
    const runningHours = data.length * 0.25 // 15分钟间隔
    const energyPerUnit = totalFlow > 0 ? totalEnergy / totalFlow : 0
    const costPerUnit = totalFlow > 0 ? totalCost / totalFlow : 0
    const carbonEmission = totalEnergy * 0.5968 // 中国电网平均碳排放因子
    const peakDemand = maxPower
    const loadFactor = avgPower / maxPower
    
    return {
      period,
      startDate: data[0].timestamp,
      endDate: data[data.length - 1].timestamp,
      totalEnergy: Math.round(totalEnergy * 100) / 100,
      totalCost: Math.round(totalCost * 100) / 100,
      avgPower: Math.round(avgPower * 100) / 100,
      maxPower: Math.round(maxPower * 100) / 100,
      minPower: Math.round(minPower * 100) / 100,
      avgEfficiency: Math.round(avgEfficiency * 10) / 10,
      maxEfficiency: Math.round(maxEfficiency * 10) / 10,
      minEfficiency: Math.round(minEfficiency * 10) / 10,
      runningHours: Math.round(runningHours * 10) / 10,
      energyPerUnit: Math.round(energyPerUnit * 1000) / 1000,
      costPerUnit: Math.round(costPerUnit * 1000) / 1000,
      carbonEmission: Math.round(carbonEmission * 100) / 100,
      peakDemand: Math.round(peakDemand * 100) / 100,
      loadFactor: Math.round(loadFactor * 1000) / 1000
    }
  }
  
  /**
   * 生成趋势数据
   */
  generateTrendData(period: StatisticsPeriod, count: number): EnergyTrend[] {
    const trends: EnergyTrend[] = []
    const now = dayjs()
    
    for (let i = count - 1; i >= 0; i--) {
      let date: dayjs.Dayjs
      
      switch (period) {
        case 'day':
          date = now.subtract(i, 'day')
          break
        case 'week':
          date = now.subtract(i, 'week')
          break
        case 'month':
          date = now.subtract(i, 'month')
          break
        case 'quarter':
          date = now.subtract(i * 3, 'month')
          break
        case 'year':
          date = now.subtract(i, 'year')
          break
        default:
          date = now.subtract(i, 'day')
      }
      
      // 生成该时间段的模拟数据
      const baseEnergy = 2000 + Math.random() * 500
      const seasonFactor = this.getSeasonMultiplier(date.month())
      const trendFactor = 1 + (Math.random() - 0.5) * 0.1 // 小幅波动
      
      const energy = baseEnergy * seasonFactor * trendFactor
      const cost = energy * this.costPerKwh
      const efficiency = 75 + Math.random() * 10
      const power = energy / (24 * (period === 'day' ? 1 : period === 'week' ? 7 : 30))
      
      trends.push({
        date: date.format('YYYY-MM-DD'),
        energy: Math.round(energy * 100) / 100,
        cost: Math.round(cost * 100) / 100,
        efficiency: Math.round(efficiency * 10) / 10,
        power: Math.round(power * 100) / 100
      })
    }
    
    return trends
  }
  
  /**
   * 生成能耗预警
   */
  generateAlerts(data: EnergyData[]): EnergyAlert[] {
    const alerts: EnergyAlert[] = []
    
    if (data.length === 0) return alerts
    
    const avgPower = data.reduce((sum, item) => sum + item.power, 0) / data.length
    const avgEfficiency = data.reduce((sum, item) => sum + item.efficiency, 0) / data.length
    const totalCost = data.reduce((sum, item) => sum + item.cost, 0)
    
    // 高能耗预警
    if (avgPower > this.baseConsumption * 1.2) {
      alerts.push({
        id: `alert_${Date.now()}_1`,
        type: 'high_consumption',
        level: 'warning',
        title: '能耗偏高',
        message: `当前平均功率 ${avgPower.toFixed(1)} kW 超过正常范围`,
        value: avgPower,
        threshold: this.baseConsumption * 1.2,
        timestamp: new Date().toISOString(),
        resolved: false
      })
    }
    
    // 低效率预警
    if (avgEfficiency < 70) {
      alerts.push({
        id: `alert_${Date.now()}_2`,
        type: 'low_efficiency',
        level: 'critical',
        title: '效率偏低',
        message: `当前平均效率 ${avgEfficiency.toFixed(1)}% 低于正常水平`,
        value: avgEfficiency,
        threshold: 70,
        timestamp: new Date().toISOString(),
        resolved: false
      })
    }
    
    // 成本超标预警
    const dailyCostThreshold = 1500
    if (totalCost > dailyCostThreshold) {
      alerts.push({
        id: `alert_${Date.now()}_3`,
        type: 'cost_overrun',
        level: 'warning',
        title: '成本超标',
        message: `当前总成本 ${totalCost.toFixed(2)} 元超过预算`,
        value: totalCost,
        threshold: dailyCostThreshold,
        timestamp: new Date().toISOString(),
        resolved: false
      })
    }
    
    return alerts
  }
}

// 导出单例实例
export const energyDataGenerator = new EnergyDataGenerator()
