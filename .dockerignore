# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# 构建输出
dist/
build/

# 开发工具
.vscode/
.idea/
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
logs/
*.log

# 临时文件
tmp/
temp/

# 测试覆盖率
coverage/

# 部署脚本（不需要在容器中）
deploy.bat
deploy.sh
启动服务器.bat
打包部署.bat

# 文档
README*.md
docs/

# Go相关
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work