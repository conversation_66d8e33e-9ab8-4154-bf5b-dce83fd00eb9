version: '3.8'

services:
  water-platform:
    build: .
    container_name: water-platform-server
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - TZ=Asia/Shanghai
    restart: unless-stopped
    volumes:
      - ./logs:/root/logs
    networks:
      - water-network

networks:
  water-network:
    driver: bridge

# 可选：添加数据库服务
# services:
#   water-platform:
#     # ... 上面的配置
#     depends_on:
#       - mysql
#       - redis
#   
#   mysql:
#     image: mysql:8.0
#     container_name: water-platform-mysql
#     environment:
#       MYSQL_ROOT_PASSWORD: your_password
#       MYSQL_DATABASE: water_platform
#     ports:
#       - "3306:3306"
#     volumes:
#       - mysql_data:/var/lib/mysql
#     networks:
#       - water-network
#   
#   redis:
#     image: redis:7-alpine
#     container_name: water-platform-redis
#     ports:
#       - "6379:6379"
#     networks:
#       - water-network
# 
# volumes:
#   mysql_data: