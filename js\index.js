// var list = document.querySelector(".sidenav");

// list.addEventListener('click', function(ev) {
    
//   if (ev.target.tagName === 'SPAN') {
//     ev.target.classList.toggle('checked');
//     console.log(list)
//   }
// }, false);

// var my



// $(document).ready(function(){
//     $("span").bind('click',function(){
//         console.log(!$(this))
//         $(this).css("background", "#818181")
//     })
// })

