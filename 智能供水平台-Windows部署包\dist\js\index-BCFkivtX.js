var k=(C,d,a)=>new Promise((m,c)=>{var y=t=>{try{s(a.next(t))}catch(o){c(o)}},f=t=>{try{s(a.throw(t))}catch(o){c(o)}},s=t=>t.done?m(t.value):Promise.resolve(t.value).then(y,f);s((a=a.apply(C,d)).next())});import{_ as F}from"./_plugin-vue_export-helper-C6jw_E77.js";/* empty css                        *//* empty css                  *//* empty css               *//* empty css                */import{d as L,r as g,U as V,O as b,P as D,b as A,e as l,w as r,F as S,k as B,V as M,f as i,E as T,h as N,W,X as z,n as R,v as p,S as U,L as _,o as w,Y as $,N as E,x as v,Z as G}from"./index-8zz4iTME.js";import{i as O,L as P}from"./index-_zIlD_l3.js";const X={class:"real-time-energy"},Y={class:"data-item"},Z={class:"icon"},j={class:"content"},q={class:"title"},H={class:"value"},J={class:"unit"},K={class:"card-header"},Q={class:"controls"},tt=L({__name:"index",setup(C){const d=g(),a=g("1h"),m=g([{title:"当前功率",value:"85.6",unit:"kW",color:"#409EFF",icon:"Lightning",trend:"up",trendIcon:"ArrowUp",trendValue:"2.3"},{title:"当前效率",value:"78.2",unit:"%",color:"#67C23A",icon:"TrendCharts",trend:"up",trendIcon:"ArrowUp",trendValue:"1.8"},{title:"累计能耗",value:"1,245.8",unit:"kWh",color:"#E6A23C",icon:"DataAnalysis",trend:"down",trendIcon:"ArrowDown",trendValue:"0.5"},{title:"运行成本",value:"892.3",unit:"元",color:"#F56C6C",icon:"Money",trend:"down",trendIcon:"ArrowDown",trendValue:"1.2"}]);let c=null;const y=s=>{const t=[],o=new Date;for(let e=s*60;e>=0;e-=5){const h=new Date(o.getTime()-e*60*1e3),x=80+Math.random()*20,u=70+Math.random()*15;t.push({time:h.toLocaleTimeString(),power:x.toFixed(1),efficiency:u.toFixed(1)})}return t},f=()=>{if(!c)return;const s=parseInt(a.value),t=y(s),o={title:{text:"实时能耗监控",left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"cross"}},legend:{data:["功率","效率"],top:30},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:t.map(e=>e.time),axisLabel:{rotate:45}},yAxis:[{type:"value",name:"功率 (kW)",position:"left",axisLabel:{formatter:"{value} kW"}},{type:"value",name:"效率 (%)",position:"right",axisLabel:{formatter:"{value}%"}}],series:[{name:"功率",type:"line",yAxisIndex:0,data:t.map(e=>e.power),smooth:!0,lineStyle:{color:"#409EFF"},areaStyle:{color:new P(0,0,0,1,[{offset:0,color:"rgba(64, 158, 255, 0.3)"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}])}},{name:"效率",type:"line",yAxisIndex:1,data:t.map(e=>e.efficiency),smooth:!0,lineStyle:{color:"#67C23A"}}]};c.setOption(o)};return V(a,f),b(()=>k(this,null,function*(){yield D(),d.value&&(c=O(d.value),f()),setInterval(()=>{m.value.forEach(s=>{const t=(Math.random()-.5)*2,o=parseFloat(s.value.replace(",","")),e=Math.max(0,o+t);s.value=e.toLocaleString("zh-CN",{maximumFractionDigits:1})})},3e3)})),(s,t)=>{const o=T,e=U,h=$,x=M,u=R,I=z;return _(),A("div",X,[l(x,{gutter:20},{default:r(()=>[(_(!0),A(S,null,B(m.value,n=>(_(),w(h,{span:6,key:n.title},{default:r(()=>[l(e,{class:"data-card"},{default:r(()=>[i("div",Y,[i("div",Z,[l(o,{size:32,color:n.color},{default:r(()=>[(_(),w(E(n.icon)))]),_:2},1032,["color"])]),i("div",j,[i("div",q,v(n.title),1),i("div",H,[p(v(n.value)+" ",1),i("span",J,v(n.unit),1)]),i("div",{class:G(["trend",n.trend])},[l(o,null,{default:r(()=>[(_(),w(E(n.trendIcon)))]),_:2},1024),p(" "+v(n.trendValue)+"% ",1)],2)])])]),_:2},1024)]),_:2},1024))),128))]),_:1}),l(e,{class:"chart-card"},{header:r(()=>[i("div",K,[l(o,null,{default:r(()=>[l(N(W))]),_:1}),t[6]||(t[6]=i("span",null,"实时能耗趋势",-1)),i("div",Q,[l(I,{size:"small"},{default:r(()=>[l(u,{type:a.value==="1h"?"primary":"",onClick:t[0]||(t[0]=n=>a.value="1h")},{default:r(()=>t[3]||(t[3]=[p("1小时")])),_:1,__:[3]},8,["type"]),l(u,{type:a.value==="6h"?"primary":"",onClick:t[1]||(t[1]=n=>a.value="6h")},{default:r(()=>t[4]||(t[4]=[p("6小时")])),_:1,__:[4]},8,["type"]),l(u,{type:a.value==="24h"?"primary":"",onClick:t[2]||(t[2]=n=>a.value="24h")},{default:r(()=>t[5]||(t[5]=[p("24小时")])),_:1,__:[5]},8,["type"])]),_:1})])])]),default:r(()=>[i("div",{ref_key:"chartRef",ref:d,class:"chart"},null,512)]),_:1})])}}}),ct=F(tt,[["__scopeId","data-v-da92d44f"]]);export{ct as default};
