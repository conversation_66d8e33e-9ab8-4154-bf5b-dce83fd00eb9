import{n as l,i as _,w as c,a as v,t as f,V as p,s as K,b as M,c as o,h as Q,L as x,G as E,g as S,d as it,f as at,C as nt,e as Bs,j as W,D as Ds,A as $s,I as qs,P as gt,k as xs,r as T,l as ws,m as zs,E as yt,o as Ct,p as St,q as kt,u as Tt,v as $t,x as xt,M as wt,y as Lt,z as Et,B as It,F as At,H as Ft,J as Ot,T as Vs,K as Us,N as js,O as Gs,Q as Ws,R as Qs,S as Ks,U as Ys,W as Zs,X as Js,Y as Xs}from"/etc.clientlibs/settings/wcm/design/aembase/base/resources/chunks/upperCase.60da3e62.js";import{l as a,v as Mt,m as ti,s as Ls,i as ei,Q as rt,f as si,M as ii,V as ai}from"/etc.clientlibs/settings/wcm/design/aembase/base/resources/chunks/vendor.2f836b19.js";const ni={name:"ElmBtnAction",inheritAttrs:!1};var ri=function(){var t=this,e=t._self._c;return e("elm-btn",t._g(t._b({staticClass:"elm-btn--action"},"elm-btn",t.$attrs,!1),t.$listeners))},oi=[],li=l(ni,ri,oi,!1,null,null,null,null);const Pt=li.exports,ci={name:"ElmBtnClose",inheritAttrs:!1};var di=function(){var t=this,e=t._self._c;return e("elm-btn",t._g(t._b({staticClass:"elm-btn--close"},"elm-btn",t.$attrs,!1),t.$listeners))},ui=[],pi=l(ci,di,ui,!1,null,null,null,null);const Rt=pi.exports,bt={mixins:[_],props:{mobileOnly:{type:Boolean,default:!1}},computed:{isActive(){return!this.mobileOnly||this.isMobile}}},mi={name:"CmpAccordion",mixins:[bt,c],props:{mobileOnly:{type:Boolean,default:!1}}};var hi=function(){var t=this,e=t._self._c;return e("div",[t._t("default",null,{isActive:t.isActive})],2)},_i=[],fi=l(mi,hi,_i,!1,null,null,null,null);const Nt=fi.exports;function vt(s){const t=new Uint8Array(s);return window.crypto.getRandomValues(t),Array.from(t).map(e=>e.toString(16).padStart(2,"0")).join("")}const{mapMutations:gi}=p,bi="Expand element",vi=4,yi={name:"CmpAccordionItem",mixins:[v,c],props:{id:{type:String,required:!0},trackingId:{type:String,default:null},isActive:{type:Boolean,default:!1},isOpen:{type:Boolean,default:!1}},data(){return{inTransition:!1,isVisible:this.isOpen}},computed:{containerAttrs(){return this.isActive?{class:"cmp-accordion-item__container",style:{height:this.height,opacity:this.opacity}}:{}},contentAttrs(){return this.isActive?{"aria-hidden":this.hidden,class:"cmp-accordion-item__content",id:this.componentId,style:{display:this.display}}:{}},headerAttrs(){return this.isActive?{"aria-controls":this.componentId,"aria-expanded":this.expanded,class:"cmp-accordion-item__header",type:"button"}:{}},height(){let s="auto";return this.isEditMode||(this.inTransition?s=`${this.contentHeight}px`:this.isVisible||(s="0px")),s},opacity(){let s=1;return!this.isEditMode&&!this.isVisible&&(s=0),s},componentId(){return`${this.baseId}-${this.randomId}`},baseId(){return a.kebabCase(`${this.id}-content`)},contentHeight(){return this.isVisible?this.$refs.content.offsetHeight:0},randomId(){return vt(vi).toString("hex")},expanded(){return this.isEditMode||this.isVisible?"true":"false"},display(){return this.isEditMode||this.inTransition||this.isVisible?"block":"none"},hidden(){return this.isEditMode?!1:!this.isVisible}},watch:{isOpen(s){this.isVisible=s}},mounted(){this.$refs.container&&this.$refs.container.addEventListener("transitionend",this.endTransition,!1)},beforeDestroy(){this.$refs.container.removeEventListener("transitionend",this.endTransition,!1)},methods:{...gi("base",["resetViewportSize"]),endTransition(s){s.target===this.$refs.container&&(this.inTransition=!1,this.isVisible&&this.resetViewportSize())},toggle(){!this.isEditMode&&this.isActive&&(this.inTransition=!0,this.$nextTick().then(()=>{this.isVisible=!this.isVisible,this.forceLayout(),this.isVisible&&f({page:{elementName:this.trackingId?this.trackingId:this.id}},bi)}))},forceLayout(){this.$refs.container&&this.$refs.container.offsetHeight}}};var Ci=function(){var t=this,e=t._self._c;return e("div",[e(t.isActive?"button":"div",t._b({tag:"component",on:{click:t.toggle}},"component",t.headerAttrs,!1),[t._t("header")],2),e("div",t._b({ref:"container"},"div",t.containerAttrs,!1),[e("div",t._b({ref:"content"},"div",t.contentAttrs,!1),[t._t("content")],2)])],1)},Si=[],ki=l(yi,Ci,Si,!1,null,null,null,null);const Ht=ki.exports,{mapState:Ti}=p,$i=192,xi={name:"CmpActionButtons",mixins:[c],props:{labels:{type:Object,default:()=>({})}},data(){return{isExpanded:!1,tooltipPosition:"top"}},computed:{...Ti("base",["windowScroll"]),hasHiddenContent(){return!!this.$scopedSlots.hidden}},mounted(){this.setSticky(this.windowScroll),this.$watch("windowScroll",this.setSticky)},methods:{toggle(){this.isExpanded=!this.isExpanded},setSticky({y:s}){parseInt(s,10)>=$i?(this.$el.classList.add("cmp-action-buttons--sticky"),this.tooltipPosition="bottom"):(this.$el.classList.remove("cmp-action-buttons--sticky"),this.tooltipPosition="top")}}};var wi=function(){var t=this,e=t._self._c;return e("div",{attrs:{"aria-expanded":t.isExpanded}},[t.hasHiddenContent&&!t.isExpanded?e("button",{staticClass:"elm-action-button elm-action-button--subtle cmp-action-buttons__action cmp-action-buttons__action--toggle",attrs:{type:"button"},on:{click:function(i){return i.preventDefault(),t.toggle.apply(null,arguments)}}},[e("span",{staticClass:"elm-action-button__text"},[t._v(t._s(t.labels.toggle))])]):t._e(),t.hasHiddenContent&&t.isExpanded?t._t("hidden",null,{tooltipPosition:t.tooltipPosition}):t._e(),t._t("visible",null,{tooltipPosition:t.tooltipPosition})],2)},Li=[],Ei=l(xi,wi,Li,!1,null,null,null,null);const Bt=Ei.exports,Ii={name:"CmpAnchorNav",mixins:[c],props:{labels:{type:Object,default:()=>({})},scrollOffset:{type:Number,default:0},resetWatch:{type:[Number,String,Boolean,Object,Array],default:null}},data(){return{anchors:[]}},mounted(){this.setAnchors(),this.resetWatch!==null&&this.$watch("resetWatch",this.setAnchors)},methods:{setAnchors(){this.anchors=this.$children.reduce((s,t)=>{const{id:e,label:i}=t._props;return i&&e?s.concat({id:e,label:i}):s},[])},scrollToContent(s,t){K(t,!0,{top:this.scrollOffset})}}};var Ai=function(){var t=this,e=t._self._c;return e("section",[t.anchors.length>1?e("nav",{staticClass:"cmp-anchor-nav__nav",attrs:{"aria-label":t.labels.nav}},[e("div",{staticClass:"cmp-anchor-nav__nav-inner"},[t.labels.heading?e("h3",{staticClass:"cmp-anchor-nav__heading"},[t._v(" "+t._s(t.labels.heading)+" ")]):t._e(),e("div",{staticClass:"cmp-anchor-nav__nav-bar"},[e("cmp-horizontal-scroll",{staticClass:"cmp-anchor-nav__list cmp-horizontal-scroll",attrs:{labels:t.labels,"resize-watch":t.anchors}},t._l(t.anchors,function(i){return e("div",{key:i.id,staticClass:"cmp-anchor-nav__list-item"},[e("a",{staticClass:"cmp-anchor-nav__nav-link elm-tab-button",attrs:{href:`#${i.id}`,title:i.label},on:{click:function(n){return n.preventDefault(),t.scrollToContent(n,`#${i.id}`)}}},[t._v(" "+t._s(i.label)+" ")])])}),0)],1)])]):t._e(),t._t("default")],2)},Fi=[],Oi=l(Ii,Ai,Fi,!1,null,null,null,null);const Dt=Oi.exports,Mi={name:"CmpAnchorNavItem",mixins:[c],props:{id:{type:String,required:!0},label:{type:String,required:!0}}};var Pi=function(){var t=this,e=t._self._c;return e("div",{attrs:{id:t.id}},[t._t("default")],2)},Ri=[],Ni=l(Mi,Pi,Ri,!1,null,null,null,null);const qt=Ni.exports,{mapMutations:Hi}=p,Bi=60,Di={name:"CmpCampaignDeckHeroBackgroundColor",props:{theme:{type:String,default:""},isAssetEnabled:{type:Boolean,default:!1},isEyebrowEnabled:{type:Boolean,default:!1},isRteEnabled:{type:Boolean,default:!1},isButtonsEnabled:{type:Boolean,default:!1},isSearchFieldEnabled:{type:Boolean,default:!1},assetPosition:{type:String,default:"right"},isHero:{type:Boolean,default:!1},headlineText:{type:String,default:""},headlineColor:{type:String,default:""}},computed:{isAssetRightAligned(){return this.assetPosition==="right"},alignmentClass(){return this.isAssetEnabled?this.isAssetRightAligned?"cmp-campaign-deck-background-color__container--asset-right":"cmp-campaign-deck-background-color__container--asset-left":""},searchFieldClass(){return!this.isEyebrowEnabled&&!this.isRteEnabled&&!this.isButtonsEnabled&&!this.headlineText?"cmp-campaign-deck-background-color__content-search-field--no-margin":"cmp-campaign-deck-background-color__content-search-field"},isDarkTheme(){return this.theme==="b-theme--dark-blue"},isLongHeadline(){return this.headlineText.length>Bi}},beforeMount(){this.updateOldTheme(),this.isHero&&this.setBreadcrumbToFloating(),this.isDarkTheme&&this.setBreadcrumbToDarkTheme()},methods:{...Hi("base",["setBreadcrumbToFloating","setBreadcrumbToDarkTheme"]),updateOldTheme(){this.theme&&!this.theme.startsWith("b-theme--")&&(this.theme=`b-theme--${this.theme}`)}}};var qi=function(){var t=this,e=t._self._c;return e("section",{class:["b-theme",`${t.theme}`,"b-deck b-deck--full-width b-deck--no_spacing","cmp-campaign-deck-background-color",!t.isAssetEnabled&&"cmp-campaign-deck-background-color--centered-content",t.isHero&&"cmp-campaign-deck-background-color--hero"]},[e("div",{class:["b-deck__inner","cmp-campaign-deck-background-color__container",t.alignmentClass]},[t.isAssetEnabled?e("div",{class:["cmp-campaign-deck-background-color__media",t.isAssetRightAligned&&"cmp-campaign-deck-background-color__media--right-aligned"]},[t._t("dynamicMedia")],2):t._e(),e("div",{staticClass:"cmp-campaign-deck-background-color__content"},[t.isEyebrowEnabled?e("div",{staticClass:"cmp-campaign-deck-background-color__content-eyebrow"},[t._t("eyebrowContent")],2):t._e(),t.$slots.authorHeadlineContent||t.headlineText?e("div",{class:["cmp-campaign-deck-background-color__content-headline",{"long-headline":t.isLongHeadline}]},[t._t("authorHeadlineContent"),t.$slots.authorHeadlineContent?t._e():e("cmp-extended-headline",{attrs:{"headline-text":t.headlineText,"headline-color":t.headlineColor,"headline-size":t.isHero?"h1":"h2"}})],2):t._e(),t.isRteEnabled?e("div",{staticClass:"cmp-campaign-deck-background-color__content-text"},[t._t("rteContent")],2):t._e(),t.isButtonsEnabled?e("div",{staticClass:"cmp-campaign-deck-background-color__content-buttons"},[t._t("buttonsContent")],2):t._e(),t.isSearchFieldEnabled?e("div",{class:[t.searchFieldClass]},[t._t("searchFieldContent")],2):t._e()])])])},zi=[],Vi=l(Di,qi,zi,!1,null,null,null,null);const zt=Vi.exports,Ui={name:"CmpCarousel",components:{VueperSlides:Mt.VueperSlides,VueperSlide:Mt.VueperSlide,VNodesRender:{name:"VNodesRender",props:{slideProp:{type:Object,default:()=>{},required:!0}},render(){const s=this.$props.slideProp||null;return s??""}}},props:{breakpoints:{type:Object,default:()=>{}},visibleSlides:{type:Number,default:1},slideMultiple:{type:[Boolean,Number],default:!1},labels:{type:Object,default:()=>({})}},data(){return{slides:[]}},mounted(){this.updateContainerTabindex()},created(){this.$slots.default&&this.$slots.default.length>0&&(this.slides=this.$slots.default.filter(s=>s.tag))},methods:{watchSlides(){this.$nextTick().then(()=>{this.updateContainerTabindex()})},updateContainerTabindex(){this.$refs.items!==void 0&&this.$refs.items.forEach(s=>{s.$el.querySelectorAll("a").forEach(e=>{s.$el.getAttribute("aria-hidden")==="true"?e.setAttribute("tabindex",-1):e.removeAttribute("tabindex")})})}}};var ji=function(){var t=this,e=t._self._c;return t.slides.length>0?e("div",{staticClass:"cmp-carousel"},[e("vueper-slides",{ref:"carousel",staticClass:"no-shadow slide-container-mode",attrs:{gap:2,"dragging-distance":70,breakpoints:t.breakpoints,arrows:!1,touchable:!0,"visible-slides":t.visibleSlides,"slide-multiple":t.slideMultiple,"fixed-height":"","prevent-y-scroll":""},on:{slide:t.watchSlides},scopedSlots:t._u([{key:"bullets",fn:function({bulletIndexes:i,goToSlide:n,previous:r,next:d,currentSlide:u}){return[e("button",{staticClass:"elm-button--tertiary elm-button--small elm-button--icon-arrow-left_outline cmp-carousel__button",attrs:{disabled:u===0,"aria-label":t.labels.previous,type:"button"},on:{click:r}}),t._l(i,function(m,g){return e("button",{key:g,staticClass:"cmp-carousel__bullet-container",attrs:{"aria-label":`Slide ${g+1}`,type:"button",role:"tab"},on:{click:function(h){return n(m)},keyup:[function(h){return!h.type.indexOf("key")&&t._k(h.keyCode,"left",37,h.key,["Left","ArrowLeft"])||"button"in h&&h.button!==0?null:r()},function(h){return!h.type.indexOf("key")&&t._k(h.keyCode,"right",39,h.key,["Right","ArrowRight"])||"button"in h&&h.button!==2?null:d()}]}},[e("div",{class:["cmp-carousel__bullet",{"cmp-carousel__bullet--active":u===m}]})])}),e("button",{staticClass:"elm-button--tertiary elm-button--small elm-button--icon-arrow-right_outline cmp-carousel__button",attrs:{disabled:u===i[i.length-1],"aria-label":t.labels.next,type:"button"},on:{click:d}})]}}],null,!1,2256881474)},t._l(t.slides,function(i,n){return e("vueper-slide",{key:n,ref:"items",refInFor:!0,scopedSlots:t._u([{key:"content",fn:function(){return[e("v-nodes-render",{attrs:{"slide-prop":i}})]},proxy:!0}],null,!0)})}),1)],1):t._e()},Gi=[],Wi=l(Ui,ji,Gi,!1,null,null,null,null);const Vt=Wi.exports,Qi="Chat button clicked",Ki={name:"CmpChatButton",props:{label:{type:String,required:!0},buttonType:{type:String,default:""}},methods:{isChatAvailable(){return typeof window.FIVN<"u"},openChat(){window.FIVN.open(),f(null,Qi)},openCookieConsent(){window.Cookiebot.renew(),window.addEventListener("CookiebotOnAccept",this.cookieBotOnAcceptListener)},cookieBotOnAcceptListener(){window.Cookiebot.consent.marketing&&(this.openChat(),window.removeEventListener("CookiebotOnAccept",this.cookieBotOnAcceptListener))}}};var Yi=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cmp-chat-button"},[e("button",{staticClass:"elm-button",class:t.buttonType,attrs:{type:"button"},on:{click:function(i){t.isChatAvailable()?t.openChat():t.openCookieConsent()}}},[t._v(" "+t._s(t.label)+" ")])])},Zi=[],Ji=l(Ki,Yi,Zi,!1,null,null,null,null);const Ut=Ji.exports,P={methods:{generateHash(s){return ti(JSON.stringify(s))}}},Xi=8e3,ta={name:"CmpClipboard",mixins:[c,P],props:{content:{type:[Array,String],required:!0},labels:{type:Object,default:()=>({})}},data(){return{isCopied:!1,CLIPBOARD_ID:"clipboard"}},computed:{contentStr(){return(a.isArray(this.content)?this.content:[this.content]).join(`
`)}},methods:{copyToClipboard(){this.$refs.textarea.select(),document.execCommand("copy"),this.isCopied=!0,setTimeout(()=>{this.isCopied=!1},Xi)}}};var ea=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cmp-clipboard"},[e("div",{staticClass:"cmp-form-text"},[e("label",{staticClass:"cmp-form-text__label",attrs:{for:t.CLIPBOARD_ID}},[t._v(" "+t._s(t.labels.text)+" ")]),e("textarea",{ref:"textarea",staticClass:"cmp-form-text__textarea cmp-clipboard__textarea",attrs:{id:t.CLIPBOARD_ID,name:t.CLIPBOARD_ID,readonly:"",rows:"10"},domProps:{value:t.contentStr}})]),e("button",{staticClass:"elm-button elm-button--icon-copy_outline cmp-clipboard__button",attrs:{disabled:t.isCopied,type:"button"},on:{click:function(i){return t.copyToClipboard()}}},[t._v(" "+t._s(t.isCopied?t.labels.copied:t.labels.copy)+" ")])])},sa=[],ia=l(ta,ea,sa,!1,null,null,null,null);const jt=ia.exports,{mapState:aa,mapMutations:na}=p,ra=27,oa={name:"CmpConfirmationDialog",mixins:[c],props:{labels:{type:Object,default:()=>({})}},data(){return{originTarget:null}},computed:{...aa("base",["confirmation"]),message(){return a.get(this.confirmation,"message")},confirmHandler(){return a.get(this.confirmation,"confirmHandler",a.noop)}},watch:{confirmation:{immediate:!0,handler(){this.confirmation?(this.originTarget=a.get(this.confirmation,"origin.target",document.querySelector("[data-site-wrapper]")),this.$nextTick().then(this.setFocus)):this.setFocus()}}},mounted(){document.body.addEventListener("keyup",this.escapeHandler,!1)},beforeDestroy(){document.body.removeEventListener("keyup",this.escapeHandler,!1)},methods:{...na("base",["setConfirmation"]),cancel(){this.setConfirmation(null)},confirm(){this.confirmHandler(),this.setConfirmation(null)},escapeHandler(s){s.keyCode===ra&&this.cancel()},setFocus(){this.confirmation?this.$refs.cancel.focus():a.isElement(this.originTarget)&&(this.originTarget.focus(),this.originTarget=null)}}};var la=function(){var t=this,e=t._self._c;return t.confirmation?e("transition",{attrs:{"aria-live":"assertive","aria-modal":"true",name:"tr-fade",role:"alertdialog",tag:"div"}},[e("div",{staticClass:"cmp-confirmation-dialog"},[e("div",{staticClass:"cmp-confirmation-dialog__body"},[e("p",{staticClass:"cmp-confirmation-dialog__text"},[t._v(t._s(t.message))])]),e("div",{staticClass:"cmp-confirmation-dialog__footer"},[e("button",{ref:"cancel",staticClass:"elm-button elm-button--ghost cmp-confirmation-dialog__footer-button",attrs:{type:"button"},on:{click:t.cancel}},[t._v(" "+t._s(t.labels.cancel)+" ")]),e("button",{staticClass:"elm-button cmp-confirmation-dialog__footer-button",attrs:{type:"button"},on:{click:t.confirm}},[t._v(" "+t._s(t.labels.confirm)+" ")])])])]):t._e()},ca=[],da=l(oa,la,ca,!1,null,null,null,null);const Gt=da.exports,ua="criticalInformationRead-",pa=30,ma={name:"CmpCriticalInformation",mixins:[c],props:{id:{type:String,required:!0},labels:{type:Object,default:()=>({})},color:{type:String,default:"blue"},message:{type:String,default:null},link:{type:String,default:null},linkText:{type:String,default:null}},data(){return{key:null,showBar:!1}},mounted(){this.key=ua+this.message.substring(0,pa),this.showBar=!a.has(window.sessionStorage,this.key)},methods:{dismiss(){this.showBar=!1,window.sessionStorage.setItem(this.key,!0)}}};var ha=function(){var t=this,e=t._self._c;return t.showBar?e("div",{class:["cmp-critical-information",`cmp-critical-information--${t.color}`],attrs:{"aria-live":"assertive",name:"info-fade",role:"alert"}},[e("div",{staticClass:"cmp-critical-information__text-container"},[e("p",{staticClass:"cmp-critical-information__text"},[t._v(t._s(t.message)+" "),e("a",{staticClass:"elm-link elm-link--active cmp-critical-information__link",attrs:{href:t.link}},[t._v(" "+t._s(t.linkText)+" ")])])]),e("div",{staticClass:"cmp-critical-information__button-container"},[e("button",{staticClass:"cmp-critical-information__button elm-square-button elm-square-button--icon-close_outline",attrs:{"aria-controls":t._f("kebabCase")(`${t.id}-dismiss`),type:"button"},on:{click:t.dismiss}},[e("span",{staticClass:"elm-square-button__text"},[t._v(t._s(t.labels.dismiss))])])])]):t._e()},_a=[],fa=l(ma,ha,_a,!1,null,null,null,null);const Wt=fa.exports,ga={name:"CmpCrosslinkCard",props:{fileReference:{type:String,default:""},headline:{type:String,default:""},text:{type:String,default:""},link:{type:String,default:""},linkLabel:{type:String,default:""},asset:{type:String,default:""},alt:{type:String,default:""}},data(){return{isHover:!1}},computed:{isDynamicMedia(){return this.asset==="dynamicMedia"},isSvg(){return this.asset==="svg"&&this.fileReference}},methods:{setHover(){this.isHover=!0},removeHover(){this.isHover=!1}}};var ba=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cmp-crosslink-card"},[e("a",{staticClass:"cmp-crosslink-card__link",attrs:{href:t.link,title:t.linkLabel},on:{mouseover:t.setHover,mouseout:t.removeHover,click:t.removeHover}},[e("div",{staticClass:"cmp-crosslink-card__content"},[t.isDynamicMedia?e("div",{staticClass:"cmp-crosslink-card__image-container"},[t._t("image")],2):t._e(),t.isSvg?e("div",{staticClass:"cmp-crosslink-card__image-container"},[t.fileReference?e("elm-img",{staticClass:"cmp-crosslink-card__image",attrs:{src:t.fileReference,alt:t.alt}}):t._e()],1):t._e()]),e("div",{staticClass:"cmp-crosslink-card__meta"},[e("div",{staticClass:"cmp-crosslink-card__heading"},[t._v(" "+t._s(t.headline)+" ")]),t._t("text"),e("div",{staticClass:"cmp-crosslink-card__cta-container"},[e("div",{class:["elm-button",t.isHover?"elm-button--hover":""]},[t._v(" "+t._s(t.linkLabel)+" ")])])],2)])])},va=[],ya=l(ga,ba,va,!1,null,null,null,null);const Qt=ya.exports,y={props:{config:{type:Object,required:!0}}};function Ca(s,t){return{props:{selectHandler:{type:Function,default:a.noop}},data(){return{selectedValues:[]}},computed:{activeSelectedValues(){return a.intersection(this.selectableValues,this.selectedValues)},allSelected(){return this.selectableValues.length?a.difference(this.selectableValues,Array.from(this.selectedValues)).length===0:!1},selectAllId(){return a.kebabCase(`${this.id}-select-all`)},selectableValues(){return this.items.map(e=>a.get(e,s))}},watch:{selectedValues(e){this.selectHandler(e)}},methods:{selectAll({target:e}){e.checked?this.selectedValues=a.uniq(this.selectedValues.concat(this.selectableValues)):this.selectedValues=[]},toggleSelectValue({value:e},i){if(t)i&&(this.selectedValues=[e]);else{const n=this.selectedValues.includes(e);!i&&n?this.selectedValues.splice(this.selectedValues.indexOf(e),1):i&&!n&&this.selectedValues.push(e)}}}}}const Sa="gpcDownloadPDF",ka={name:"CmpDocumentationResult",mixins:[c],props:{id:{type:[Number,String],required:!0},date:{type:String,required:!0},fileId:{type:Number,required:!0},publicationNumber:{type:String,required:!0},labels:{type:Object,default:()=>({})},language:{type:String,required:!0},links:{type:Array,default:()=>[]},revisions:{type:Array,default:()=>[]},selected:{type:Boolean,required:!0},revision:{type:Number,default:null},title:{type:String,required:!0},type:{type:String,required:!0},typeCode:{type:String,required:!0},selectHandler:{type:Function,required:!0},setRevisionHandler:{type:Function,required:!0},value:{type:[Number,String],required:!0}},computed:{href(){return this.revision?a.get(a.find(this.revisions,{historyid:this.revision}),"link.href"):a.get(a.find(this.links,{rel:"file"}),"href")},options(){return[{value:this.labels.currentVersion}].concat(this.revisions.map(s=>({key:s.historyid,value:s.created})))}},methods:{updateRevision({target:s}){this.setRevisionHandler(a.toNumber(s.value)||null)},track(){f({download:{title:this.title,type:this.typeCode}},Sa)}}};var Ta=function(){var t=this,e=t._self._c;return e("div",[e("span",{staticClass:"cmp-form-option cmp-documentation-result__selector"},[e("input",{staticClass:"cmp-form-option__field cmp-form-option__field--checkbox",attrs:{id:t.id,name:t.id,type:"checkbox"},domProps:{checked:t.selected,value:t.value},on:{change:t.selectHandler}}),e("label",{staticClass:"cmp-form-option__label cmp-form-option__label--checkbox cmp-documentation-result__selector-label",attrs:{for:t.id}},[t._v(" "+t._s(t.value)+" ")])]),e("cmp-accordion",{staticClass:"cmp-accordion cmp-documentation-result__content",attrs:{"mobile-only":""},scopedSlots:t._u([{key:"default",fn:function(i){return[e("cmp-accordion-item",{staticClass:"cmp-accordion-item cmp-documentation-result__reveal",attrs:{id:t._f("kebabCase")(`${t.id}-title`),"is-active":i.isActive}},[e("div",{staticClass:"cmp-documentation-result__header",attrs:{slot:"header"},slot:"header"},[e("p",{staticClass:"cmp-documentation-result__meta"},[e("span",{staticClass:"cmp-documentation-result__meta-item"},[t._v(t._s(t.publicationNumber))]),e("span",{staticClass:"cmp-documentation-result__meta-sep"},[t._v("/")]),e("span",{staticClass:"cmp-documentation-result__meta-item"},[t._v(t._s(t.fileId))]),e("span",{staticClass:"cmp-documentation-result__meta-sep"},[t._v("/")]),e("span",{staticClass:"cmp-documentation-result__meta-item cmp-documentation-result__meta-item--truncate"},[t._v(t._s(t.type))])]),e("a",{staticClass:"elm-link elm-link--active cmp-documentation-result__link",attrs:{download:t.title,href:t.href,"data-qa":"documentation-result-item"},on:{click:t.track}},[t._v(" "+t._s(t.title)+" ")])]),e("div",{attrs:{slot:"content"},slot:"content"},[e("p",{staticClass:"cmp-documentation-result__meta cmp-documentation-result__meta--sub"},[e("span",{staticClass:"cmp-documentation-result__meta-item cmp-documentation-result__meta-item--truncate"},[t._v(t._s(t.language))]),e("span",{staticClass:"cmp-documentation-result__meta-item cmp-documentation-result__meta-item--fill"},[t._v(t._s(t.date))])]),t.revisions.length?e("cmp-form-select",{staticClass:"cmp-documentation-result__version-selector",attrs:{id:t._f("kebabCase")(`${t.id}-revisions`),label:t.labels.selectVersion,options:t.options,value:t.revision,"update-handler":t.updateRevision,modifier:"underline"}}):t._e()],1)])]}}])})],1)},$a=[],xa=l(ka,Ta,$a,!1,null,null,null,null);const wa=xa.exports,La="pdf",Ea="literature",R="resourceid",Ia="gpcDownloadPDF",Aa="Multiple",Fa="ZIP",Oa={name:"CmpDocumentationResultList",components:{CmpDocumentationResult:wa},mixins:[_,y,Ca(R),c],props:{id:{type:String,required:!0},items:{type:Array,default:()=>[]},labels:{type:Object,default:()=>({})},updateHandler:{type:Function,default:a.noop},isVueButtonEnabled:{type:Boolean,default:!1}},data(){return{SELECT_ID:R}},computed:{downloadAllHref(){if(!this.selectedItems.length)return null;const s=window.location.pathname.replace(".html","");return M(`${s}.zip.json`,{...this.getDocumentList(this.selectedItems),name:this.labels.actions.download.downloadName,service:Ea})},selectedItems(){return this.activeSelectedValues.map(s=>a.find(this.items,{[R]:s}))}},methods:{downloadSelected(s){this.selectedItems.length?f({download:{title:Aa,type:Fa}},Ia):s.preventDefault()},selectItem(s){return({target:t})=>{this.toggleSelectValue({value:s[R]},t.checked)}},getDocumentHref(s){return s.revision?a.get(a.find(s.revisions,{historyid:s.revision}),"link.href"):a.get(a.find(s.links,{rel:"file"}),"href")},getDocumentList(){return this.selectedItems.reduce((s,t)=>{const{fileext:e}=t,i=e||La;return Object.assign({},s,{[i]:s[i]?s[i].concat(this.getDocumentReference(t)):[this.getDocumentReference(t)]})},{})},getDocumentReference(s){return a.get(s,"fileid")},setItemRevision(s){return t=>{o.set(s,"revision",t)}}}};var Ma=function(){var t=this,e=t._self._c;return e("section",{staticClass:"cmp-result-list cmp-result-list--shadow",attrs:{id:t.id}},[e("header",{staticClass:"cmp-result-list__header cmp-result-list__header--fill"},[e("div",{staticClass:"cmp-result-list__actions"},[e("div",{staticClass:"cmp-form-option cmp-result-list__selector"},[e("input",{staticClass:"cmp-form-option__field cmp-form-option__field--checkbox",attrs:{disabled:!t.items.length,id:t.selectAllId,name:t.selectAllId,type:"checkbox",value:"all"},domProps:{checked:t.allSelected},on:{change:t.selectAll}}),e("label",{staticClass:"cmp-form-option__label cmp-form-option__label--checkbox",attrs:{for:t.selectAllId}},[e("span",{staticClass:"cmp-results-table__label"},[t._v(t._s(t.labels.selectAll))]),e("span",[t._v("("+t._s(t.activeSelectedValues.length)+")")])])]),e("div",{staticClass:"cmp-action-buttons cmp-action-buttons--inline"},[t.isVueButtonEnabled?e("cmp-overlay-button",{directives:[{name:"tooltip",rawName:"v-tooltip:top",value:t.labels.actions.clipboard.copy,expression:"labels.actions.clipboard.copy",arg:"top"}],staticClass:"cmp-action-buttons__action",attrs:{id:t._f("kebabCase")(`documentation-action-copy-${t.id}`),disabled:!t.selectedItems.length,"component-props":{content:t.selectedItems.map(t.getDocumentHref),labels:t.labels.actions.clipboard},heading:t.labels.documents,label:"",component:"CmpClipboard",icon:"copy_outline",variant:"link","is-vue-button-enabled":""}}):e("cmp-overlay-button",{directives:[{name:"tooltip",rawName:"v-tooltip:top",value:t.labels.actions.clipboard.copy,expression:"labels.actions.clipboard.copy",arg:"top"}],staticClass:"elm-action-button elm-action-button--subtle elm-action-button--copy_outline cmp-action-buttons__action",attrs:{id:t._f("kebabCase")(`documentation-action-copy-${t.id}`),disabled:!t.selectedItems.length,"component-props":{content:t.selectedItems.map(t.getDocumentHref),labels:t.labels.actions.clipboard},label:t.labels.actions.clipboard.copy,heading:t.labels.documents,component:"CmpClipboard","hide-label":""}}),e("elm-action-button-saved-documents-add",{staticClass:"elm-action-button elm-action-button--subtle elm-action-button--save_outline cmp-action-buttons__action",attrs:{id:t._f("kebabCase")(`documentation-action-add-to-saved-documents-${t.id}`),labels:t.labels.actions.save,"selected-items":t.selectedItems}})],1)])]),e("div",{staticClass:"cmp-result-list__body"},t._l(t.items,function(i){return e("cmp-documentation-result",{key:i[t.SELECT_ID],staticClass:"cmp-documentation-result",attrs:{id:i[t.SELECT_ID],"file-id":i.fileid,"publication-number":i.publicationnumber,date:i.publisheddate,labels:t.labels,language:i.languagetext,links:i.links,revision:i.revision,revisions:i.revisions,selected:t.selectedValues.includes(i[t.SELECT_ID]),"set-revision-handler":t.setItemRevision(i),title:i.title,type:i.documenttypetext,"type-code":i.documenttypecode,"select-handler":t.selectItem(i),value:i[t.SELECT_ID]}})}),1)])},Pa=[],Ra=l(Oa,Ma,Pa,!1,null,null,null,null);const Kt=Ra.exports,Na={name:"CmpDynamicMediaHotspots",data(){return{isReadyToShowHotspots:!1,isPositionAbsolute:!1,absoluteTop:0,absoluteHeight:0,imageElement:null}},mounted(){this.initializeImageElement(),this.waitForImageToLoad()},methods:{initializeImageElement(){const s=this.$el.previousElementSibling;s&&(this.imageElement=s.querySelector("img.elm-img__asset"))},waitForImageToLoad(){this.imageElement&&(this.imageElement.onload=(function(){this.imageElement.offsetTop>0&&(this.setPositionAbsoluteValues(),window.addEventListener("resize",this.setPositionAbsoluteValues)),this.isReadyToShowHotspots=!0}).bind(this))},setPositionAbsoluteValues(){this.isPositionAbsolute=!0,this.absoluteTop=this.imageElement.offsetTop,this.absoluteHeight=this.imageElement.clientHeight}}};var Ha=function(){var t=this,e=t._self._c;return t.isReadyToShowHotspots?e("div",{staticClass:"cmp-dynamic-media-hotspots",class:{"cmp-dynamic-media-hotspots--absolute":t.isPositionAbsolute},style:{top:t.isPositionAbsolute&&t.absoluteTop+"px",height:t.isPositionAbsolute&&t.absoluteHeight+"px"}},[t._t("default")],2):t._e()},Ba=[],Da=l(Na,Ha,Ba,!1,null,null,null,null);const Yt=Da.exports,{mapMutations:qa}=p,za={name:"CmpDynamicMediaHotspot",mixins:[_],props:{hotspot:{type:Object,required:!0},alwaysOnTooltip:{type:Boolean,required:!0},hotspotColor:{type:String,default:"white"}},data(){return{isHotspotActive:!1}},computed:{isAssetTooltip(){return this.hotspot.asset==="toolTip"},isAssetRichTooltip(){return this.hotspot.asset==="richTooltip"},isAssetExperienceFragment(){return this.hotspot.asset==="experienceFragment"},isHotspotLink(){return this.hotspot.link!=null&&this.hotspot.link!==""},isTooltipEnabled(){return!(this.isAssetExperienceFragment&&!this.hotspot.experienceFragmentEnableTooltip)},isTooltipClickable(){return!this.isAssetRichTooltip}},methods:{...qa("base",["setOverlayComponent"]),hotspotClicked(){this.isTooltipEnabled||this.runHotspotAction()},mouseOverActive(){if(!this.isTooltipEnabled){this.isHotspotActive=!1;return}this.isHotspotActive=!0},mouseleave(){this.isHotspotActive=!1},tooltipClicked(){this.runHotspotAction()},runHotspotAction(){if(this.isAssetExperienceFragment){this.setOverlayComponent({component:"CmpOverlayExperienceFragment",fullScreen:this.isMobile,props:{width:this.hotspot.width,overlaySize:this.hotspot.size},showToggle:!0,defaultSlot:this.$slots.default});return}window.location.assign(this.hotspot.link)}}};var Va=function(){var t=this,e=t._self._c;return e("div",{class:["cmp-dynamic-media-hotspot",`cmp-dynamic-media-hotspot--${t.hotspotColor}`,{"cmp-dynamic-media-hotspot--active":t.isHotspotActive,"cmp-dynamic-media-hotspot--always-on":t.alwaysOnTooltip}],style:{left:t.hotspot.positionX===""?"0":t.hotspot.positionX+"%",top:t.hotspot.positionY===""?"0":t.hotspot.positionY+"%"},on:{click:t.hotspotClicked,mouseover:t.mouseOverActive,mouseleave:t.mouseleave}},[t.isTooltipEnabled?e("cmp-dynamic-media-hotspot-tooltip",{attrs:{"always-on-tooltip":t.alwaysOnTooltip,visible:t.isHotspotActive,"is-tooltip-clickable":t.isTooltipClickable},on:{"tooltip-clicked":t.tooltipClicked}},[t.isAssetTooltip?e("cmp-dynamic-media-hotspot-tooltip-simple",{attrs:{text:t.hotspot.text,"is-hotspot-link":t.isHotspotLink}}):t._e(),t.isAssetRichTooltip?e("cmp-dynamic-media-hotspot-tooltip-rich",{attrs:{headline:t.hotspot.richTooltipHeader,text:t.hotspot.richTooltipText,"is-link-enabled":t.hotspot.richTooltipEnableLink,link:t.hotspot.richTooltipLink,"link-text":t.hotspot.richTooltipLinkText,"is-button-enabled":t.hotspot.richTooltipEnableButton,"button-link":t.hotspot.richTooltipButtonLink,"button-text":t.hotspot.richTooltipButtonText}}):t._e(),t.isAssetExperienceFragment?e("cmp-dynamic-media-hotspot-tooltip-simple",{attrs:{text:t.hotspot.experienceFragmentTooltipText,"is-hotspot-link":t.isHotspotLink}}):t._e()],1):t._e()],1)},Ua=[],ja=l(za,Va,Ua,!1,null,null,null,null);const Zt=ja.exports,Ga={name:"CmpDynamicMediaHotspotTooltip",props:{alwaysOnTooltip:{type:Boolean,required:!0},visible:{type:Boolean,required:!0},isTooltipClickable:{type:Boolean,required:!0}},data(){return{hotspot:null,componentWrapper:null,isTooltipTop:!1,isTooltipRight:!1,isTooltipBottom:!1,isTooltipLeft:!1}},computed:{tooltipWidth(){return this.$refs.tooltip.clientWidth},tooltipHeight(){return this.$refs.tooltip.clientHeight},halfOfHotspotClientHeight(){return this.hotspot.clientHeight/2},halfOfHotspotClientWidth(){return this.hotspot.clientWidth/2}},mounted(){this.hotspot=this.$refs.tooltip.parentElement,this.componentWrapper=this.$refs.tooltip.closest(".cmp-dynamic-media-with-hotspots"),this.initTooltipPlacement(),window.addEventListener("resize",this.initTooltipPlacement)},destroyed(){window.removeEventListener("resize",this.initTooltipPlacement)},methods:{initTooltipPlacement(){if(this.resetTooltipPosition(),this.hotspot.offsetTop-this.halfOfHotspotClientHeight-this.tooltipHeight<0){this.isTooltipBottom=!0;return}if(this.hotspot.offsetTop+this.halfOfHotspotClientHeight+this.tooltipHeight>this.componentWrapper.clientHeight){this.isTooltipTop=!0;return}if(this.hotspot.offsetLeft-this.halfOfHotspotClientWidth-this.tooltipWidth<0){this.isTooltipRight=!0;return}if(this.hotspot.offsetLeft+this.halfOfHotspotClientWidth+this.tooltipWidth>this.componentWrapper.clientWidth){this.isTooltipLeft=!0;return}this.isTooltipBottom=!0},resetTooltipPosition(){this.isTooltipTop=!1,this.isTooltipRight=!1,this.isTooltipBottom=!1,this.isTooltipLeft=!1},tooltipClicked(){this.isTooltipClickable&&this.$emit("tooltip-clicked")}}};var Wa=function(){var t=this,e=t._self._c;return e("div",{ref:"tooltip",class:["cmp-dynamic-media-hotspot-tooltip",{"cmp-dynamic-media-hotspot-tooltip--visible":t.visible,"cmp-dynamic-media-hotspot-tooltip--always-on":t.alwaysOnTooltip,"cmp-dynamic-media-hotspot-tooltip--top":t.isTooltipTop,"cmp-dynamic-media-hotspot-tooltip--right":t.isTooltipRight,"cmp-dynamic-media-hotspot-tooltip--bottom":t.isTooltipBottom,"cmp-dynamic-media-hotspot-tooltip--left":t.isTooltipLeft,"cmp-dynamic-media-hotspot-tooltip--clickable":t.isTooltipClickable}],on:{click:t.tooltipClicked}},[e("div",{staticClass:"cmp-dynamic-media-hotspot-tooltip__arrow"}),t._t("default")],2)},Qa=[],Ka=l(Ga,Wa,Qa,!1,null,null,null,null);const Jt=Ka.exports,Ya={name:"CmpDynamicMediaHotspotTooltipSimple",props:{text:{type:String,default:""},isHotspotLink:{type:Boolean,default:!1}}};var Za=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cmp-dynamic-media-hotspot-tooltip-simple__content"},[e("span",{staticClass:"cmp-dynamic-media-hotspot-tooltip-simple__text"},[t._v(t._s(t.text))]),t.isHotspotLink?e("span",{staticClass:"cmp-dynamic-media-hotspot-tooltip-simple__icon"}):t._e()])},Ja=[],Xa=l(Ya,Za,Ja,!1,null,null,null,null);const Xt=Xa.exports,tn={name:"CmpDynamicMediaHotspotTooltipRich",props:{headline:{type:String,default:""},text:{type:String,default:""},isLinkEnabled:{type:Boolean,default:!1},link:{type:String,default:""},linkText:{type:String,default:""},isButtonEnabled:{type:Boolean,default:!1},buttonLink:{type:String,default:""},buttonText:{type:String,default:""}},computed:{isLinkVisible(){return this.isLinkEnabled&&this.link&&this.linkText},isButtonVisible(){return this.isButtonEnabled&&this.buttonLink&&this.buttonText}}};var en=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cmp-dynamic-media-hotspot-tooltip-rich__content"},[e("span",{staticClass:"cmp-dynamic-media-hotspot-tooltip-rich__headline"},[t._v(t._s(t.headline))]),e("span",{staticClass:"cmp-dynamic-media-hotspot-tooltip-rich__text"},[t._v(t._s(t.text))]),t.isLinkVisible||t.isButtonVisible?e("div",{staticClass:"cmp-dynamic-media-hotspot-tooltip-rich__actions"},[t.isLinkVisible?e("a",{staticClass:"cmp-dynamic-media-hotspot-tooltip-rich__link elm-link",attrs:{href:t.link}},[t._v(" "+t._s(t.linkText)+" ")]):t._e(),t.isButtonVisible?e("a",{staticClass:"cmp-dynamic-media-hotspot-tooltip-rich__button elm-button elm-button--small",attrs:{href:t.buttonLink}},[t._v(" "+t._s(t.buttonText)+" ")]):t._e()]):t._e()])},sn=[],an=l(tn,en,sn,!1,null,null,null,null);const te=an.exports,F="data",O="error",nn="loading",Es="message";function L(s,t=null){if(!rn(s)||!on(t))return ee(null,{message:"Invalid state."});const e=a.get(s,F,null),i=a.get(s,O,null),n=a.get(t,F,null),r=a.get(t,O,null);return ee(e||n,i||(e?null:r),!(e||i))}function ee(s,t,e=!1){return Object.assign({loading:e},s&&{data:s},t&&{error:t})}function rn(s){return s===null?!0:a.isPlainObject(s)?!Q([F,O])(s)&&(a.has(s,F)||a.get(s,`${O}.${Es}`)):!1}function on(s){return s===null?!0:a.isPlainObject(s)?a.has(s,F)||a.get(s,`${O}.${Es}`)||a.has(s,nn):!1}const ln={name:"CmpSAPSearchField",props:{labels:{type:Object,default:()=>({})},query:{type:String,default:""},apiUrl:{type:String,default:""},action:{type:String,default:""}},data(){return{LOADING:x,isLoading:!1}},computed:{message(){const s=".?",t=new RegExp(`[^${s}]+[${s}]`,"gm");return this.labels.prompt.match(t)}},methods:{search(){this.isLoading=!0,E(`${this.apiUrl}?productnumber=${this.query}`).then(s=>{s.data?window.location.href=`${this.action}.gotoproduct.json?pumpsystemid=${s.data}`:window.location.href=`${this.action}?query=${this.query}`})},closeModal(){const s=a.get(this.$parent,"close");s&&s()}}};var cn=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cmp-search-field__sap-search-field cmp-overlay-form"},[e("div",{staticClass:"cmp-overlay-form__body"},[e("div",{class:["cmp-search-field__sap-search-field",t.isLoading&&t.LOADING]},[e("div",{directives:[{name:"show",rawName:"v-show",value:!t.isLoading,expression:"!isLoading"}]},[e("h3",{staticClass:"cmp-search-field__sap-placeholder"},[t._v(t._s(t.labels.placeholder)+" "+t._s(t.query))]),t._l(t.message,function(i,n){return e("span",{key:n,staticClass:"cmp-search-field__sap-search-field--message"},[t._v(" "+t._s(i)),n<t.message.length-1?e("br"):t._e()])})],2)]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isLoading,expression:"isLoading"}]},[e("h3",{staticClass:"cmp-search-field__search"},[t._v(t._s(t.labels.searching))])]),e("div",{staticClass:"cmp-search-field__footer"},[e("button",{staticClass:"elm-button elm-button--ghost cmp-search-field__footer-button",attrs:{type:"button"},on:{click:function(i){return t.closeModal()}}},[e("span",{staticClass:"elm-button__text"},[t._v(" "+t._s(t.labels.cancel)+" ")])]),t.isLoading?t._e():e("button",{staticClass:"elm-button cmp-search-field__confirm cmp-search-field__footer-button",on:{click:function(i){return t.search()}}},[t._v(" "+t._s(t.labels.confirm)+" ")])])])])},dn=[],un=l(ln,cn,dn,!1,null,null,null,null);const se=un.exports,{mapMutations:pn}=p,mn=500,hn=1,_n=6,fn=3,gn=27,bn="product-selection",Y="assisted search",vn="auto_suggestion_used",yn="typeahead",Cn="",Sn=`query suggest(
	$term: String!
	$site: SitesEnum!
	$sizeSuggestions: Int
	$sizeProducts: Int
	) {
	suggest(
		term: $term
		site: $site
		sizeSuggestions: $sizeSuggestions
		sizeProducts: $sizeProducts
	) {
		terms
		products {
			productName
		    thumbnail
		    url
		}
	}
}`,kn={name:"CmpElasticSearchField",components:{CmpSAPSearchField:se},mixins:[_,y,c],props:{action:{type:String,required:!0},autoSuggest:{type:Boolean,default:!1},autoFocus:{type:Boolean,default:!1},config:{type:Object,default:()=>({})},heading:{type:String,default:""},headingTag:{type:String,default:"h2"},id:{type:String,required:!0},labels:{type:Object,default:()=>({})},method:{type:String,required:!0},name:{type:String,required:!0},value:{type:String,default:null},type:{type:String,required:!0},useOverlay:{type:Boolean,default:!1},inputHandler:{type:Function,default:a.noop},toggleHandler:{type:Function,default:a.noop},enableTouchPointMenu:{type:Boolean,default:!1},defaultCategory:{type:String,default:"all-results"},isWithoutHeadline:{type:Boolean,default:!1}},data(){const s=[{value:"all-results",text:this.labels.categories.all},{value:"products",text:this.labels.categories.products},{value:"documentation",text:this.labels.categories.documentation},{value:"support",text:this.labels.categories.support},{value:"other",text:this.labels.categories.other}];function t(n){return s.find(r=>r.value===n)}let e=t(this.defaultCategory);const i=S(window.location.search).tab;return i?e=t(i)||e:this.isGpcDomain()&&(e=t("products")),{isFocused:!1,query:this.value||S(window.location.search).query||null,suggestions:null,products:null,categories:s,selectedCategory:e,dropdownFocus:!1,headingLength:0}},computed:{suggestionsList(){const s=this.suggestions;return a.isArray(s)?s:[]},suggestedProduct(){const s=this.products;return a.isArray(s)?s:[]},checkSite(){return!a.get(window,"grundfos.locale.country")||!a.get(window,"grundfos.locale.language")},frequency(){return a.get(window,"grundfos.settings.user.frequency")||null}},created(){this.cancelRequest=a.noop,this.autoFocus||document.body.addEventListener("click",this.closeSuggestionList,!1),document.body.addEventListener("keyup",this.escapeSuggestionList,!1)},mounted(){!this.useOverlay&&this.autoSuggest&&this.$watch("query",a.throttle(this.getInputSuggestions,mn)),this.autoFocus&&this.$refs.input.focus(),this.heading&&(this.headingLength=this.$refs.heading.textContent.length)},beforeDestroy(){this.autoFocus||document.body.removeEventListener("click",this.closeSuggestionList,!1),document.body.removeEventListener("keyup",this.escapeSuggestionList,!1)},methods:{...pn("base",["setOverlayComponent"]),clearFocus(){this.isFocused=!1},focusSuggestion(s){if(!this.suggestionsList[s]){this.$refs.input.focus();return}const t=a.first(this.$refs[`suggestion-${s}`]);t&&t.focus()},getInputSuggestions(s){this.isFocused&&this.getSuggestions(s)},getSuggestions(s){if(s.length<=hn)return this.setSuggestions(null),Promise.resolve();const t=it();this.cancelRequest(),this.cancelRequest=t.cancel,this.suggestions=L(null,this.suggestions);const e=`${a.get(window,"grundfos.locale.country").toUpperCase()}_${a.get(window,"grundfos.locale.language").toUpperCase()}`,i=this.config.sizeSuggestions!==null&&this.config.sizeSuggestions!==void 0&&this.config.sizeSuggestions!==0?this.config.sizeSuggestions:_n,n=this.config.sizeProducts!==null&&this.config.sizeProducts!==void 0&&this.config.sizeProducts!==0?this.config.sizeProducts:fn,r={query:Sn,variables:{term:s,site:this.checkSite?"WWW_EN":e,sizeSuggestions:i,sizeProducts:n}};return this.fetch(r,t).then(d=>d.json()).then(this.setSuggestions).catch(this.setSuggestions)},fetch(s,t){return!!window.MSInputMethodContext&&!!document.documentMode?this.getIeSearchResponse(s,t):this.getSearchResponse(s,t)},getSearchResponse(s,t){return fetch(this.config.apiUrl,{method:"POST",mode:"cors",credentials:"same-origin",headers:{"Content-Type":"application/json"},redirect:"follow",referrer:document.location.href.split("?")[0],referrerPolicy:"unsafe-url",body:JSON.stringify(s),cancelToken:t.token})},getIeSearchResponse(s,t){return window.fetch(this.config.apiUrl,{method:"POST",mode:"cors",credentials:"same-origin",headers:{"Content-Type":"application/json"},redirect:"follow",referrer:document.location.href.split("?")[0],referrerPolicy:"unsafe-url",body:JSON.stringify(s),cancelToken:t.token})},getSuggestionText(s){const t=new RegExp(`(${this.query})`,"gi");return s.replace(t,"<strong>$1</strong>")},getTrackingData(s={}){return{search:Object.assign({term:this.query,type:this.type},s)}},searchProduct(s){const t=`${a.get(window,"grundfos.locale.country").toUpperCase()}_${a.get(window,"grundfos.locale.language").toUpperCase()}`,e={query:`query search( $q: String, $site: SitesEnum!, $limit: Int) {
					search(q: $q, site: $site, limit: $limit, filters: {field: TYPE, value: ["PRODUCTS"]}){
						count,
						items {
							uuid,
							metadata{
								baseData {
									url
								}
							}
						}
					}
				}`,variables:{q:s,site:this.checkSite?"WWW_EN":t,limit:2}};return window.fetch(this.config.apiUrl,{method:"POST",mode:"cors",credentials:"same-origin",headers:{"Content-Type":"application/json"},redirect:"follow",referrer:document.location.href.split("?")[0],referrerPolicy:"unsafe-url",body:JSON.stringify(e)}).then(i=>i.json()).catch(i=>i)},isQueryValid(s){return s.length>6&&!!s.trim().match(/^\S+$/gm)},hasValidProduct(s){const i=this.isQueryValid(this.query);return!!(a.has(s,"data.search.items")&&s.data.search.count===1&&s.data.search.items[0].metadata.baseData.url.length>1&&s.data.search.items[0].metadata.baseData.url.match(/\/products\//g)!=null&&i)},handleSubmit(){this.searchProduct(this.query).then(s=>{a.has(s,"errors")&&this.$el.submit(),this.hasValidProduct(s)?this.$nextTick().then(()=>{window.location.assign(s.data.search.items[0].metadata.baseData.url)}):this.isValidSAPProductNumber(s)?this.openSAPSearchModal():this.$nextTick().then(()=>{this.$el.submit()})}).catch(()=>{this.$el.submit()})},selectSuggestion(s){this.setQuery(s),this.handleSubmit()},setFocus(s){if(s.preventDefault(),this.useOverlay){const t=this.$el.getBoundingClientRect(),e=!this.isMobile&&{x:t.left+t.width/2,y:t.top+t.height/2};this.setOverlayComponent({autoFocus:!1,component:"CmpElasticSearchField",fullScreen:!0,props:{action:this.action,autoSuggest:this.autoSuggest,autoFocus:!0,config:this.config,heading:this.heading,id:"search-overlay-query",inputHandler:this.updateQuery,labels:this.labels,method:this.method,name:this.name,type:this.type,value:this.query},origin:e,showToggle:"desktop",transparent:!0})}else this.isFocused=!0},setQuery(s){this.inputHandler({target:{value:s}}),this.query=s},setSuggestions(s){const t=a.get(s,"data.suggest.terms"),e=a.get(s,"data.suggest.products"),i=[],n=[];t&&t.forEach(r=>{i.push(r)}),e&&e.forEach(r=>{n.push(r)}),this.suggestions=i,this.products=n},updateQuery(s){this.inputHandler(s),this.query=s.target.value},closeSuggestionList(){this.suggestionsList.length&&(this.clearFocus(),this.suggestions=null,this.products=null)},escapeSuggestionList(s){s.keyCode===gn&&this.closeSuggestionList()},openSAPSearchModal(){this.setOverlayComponent({component:se,props:{labels:this.labels.sap,query:this.query,apiUrl:this.config.sapSearchApiUrl,action:this.action}})},isValidSAPProductNumber(s){const t=this.query.trim();return this.isQueryValid(t)&&t.match(/^[a-zA-Z0-9]+$/gm)&&s.data.search.count===0},isGpcDomain(){return window.location.href.toLowerCase().includes(bn)},manualSearch(){f(this.getTrackingData({assisted_search:Cn}),Y),this.handleSubmit()},termSuggestionSearch(s){f(this.getTrackingData({assisted_search:yn}),Y),this.selectSuggestion(s)},productSuggestionSearch(){f(this.getTrackingData({assisted_search:vn}),Y)}}};var Tn=function(){var t=this,e=t._self._c;return e("form",{class:["cmp-search-field",t.useOverlay&&"has-overlay"],attrs:{action:t.action,method:t.method,autocomplete:"off",role:"search"},on:{click:t.toggleHandler,submit:function(i){return i.preventDefault(),t.manualSearch.apply(null,arguments)}}},[e("div",{staticClass:"cmp-search-field__inner",on:{click:function(i){i.stopPropagation()}}},[!t.isWithoutHeadline&&t.heading?e(t.headingTag,{ref:"heading",tag:"component",class:["cmp-search-field__heading","cmp-title","cmp-title--1__extd",t.headingLength>60&&"cmp-search-field__heading--long"]},[t._v(" "+t._s(t.heading)+" ")]):t._e(),e("fieldset",{staticClass:"cmp-search-field__fieldset",class:{"cmp-search-field__fieldset--focus":t.isFocused}},[e("legend",{staticClass:"cmp-search-field__legend"},[t._v(t._s(t.labels.legend))]),e("div",{staticClass:"cmp-search-field__category-container"},[e("div",{staticClass:"cmp-search-field__category-overlay",class:{"cmp-search-field__category-overlay--focus":t.dropdownFocus}},[e("span",{staticClass:"cmp-search-field__category-overlay-text"},[t._v(t._s(t.selectedCategory.text))])]),e("select",{directives:[{name:"model",rawName:"v-model",value:t.selectedCategory,expression:"selectedCategory"}],staticClass:"cmp-search-field__category-select",attrs:{"aria-label":t.labels.categoryLabel},on:{focus:function(i){t.dropdownFocus=!0},blur:function(i){t.dropdownFocus=!1},change:function(i){var n=Array.prototype.filter.call(i.target.options,function(r){return r.selected}).map(function(r){var d="_value"in r?r._value:r.value;return d});t.selectedCategory=i.target.multiple?n:n[0]}}},t._l(t.categories,function(i,n){return e("option",{key:n,attrs:{"aria-label":i.text},domProps:{value:{value:i.value,text:i.text}}},[t._v(" "+t._s(i.text)+" ")])}),0)]),e("div",{staticClass:"cmp-search-field__field"},[e("label",{staticClass:"cmp-search-field__label",attrs:{for:t.id}},[t._v(" "+t._s(t.labels.label)+" ")]),e("input",{ref:"input",staticClass:"cmp-search-field__input",attrs:{id:t.id,name:t.name,placeholder:t.labels.placeholder,type:"search"},domProps:{value:t.query},on:{focus:t.setFocus,blur:t.clearFocus,input:t.updateQuery,keydown:function(i){if(!i.type.indexOf("key")&&t._k(i.keyCode,"down",40,i.key,["Down","ArrowDown"]))return null;i.preventDefault()},keyup:function(i){if(!i.type.indexOf("key")&&t._k(i.keyCode,"down",40,i.key,["Down","ArrowDown"]))return null;t.suggestionsList.length&&t.focusSuggestion(0)}}}),e("input",{attrs:{id:t._f("kebabCase")(`${t.id}-type`),name:"search-type",type:"hidden"},domProps:{value:t.type}}),e("input",{attrs:{id:t._f("kebabCase")(`${t.id}-tab`),name:"tab",type:"hidden"},domProps:{value:t.selectedCategory.value}}),e("input",{attrs:{id:t._f("kebabCase")(`${t.id}-category`),name:"category",type:"hidden"},domProps:{value:t.selectedCategory.value}}),t.enableTouchPointMenu?e("input",{attrs:{id:t._f("kebabCase")(`${t.id}-freq`),name:"frequency",type:"hidden"},domProps:{value:t.frequency}}):t._e(),e("button",{staticClass:"elm-button elm-button--icon-search_outline elm-button--small",attrs:{type:"submit"}},[e("span",{staticClass:"elm-square-button__text"},[t._v(t._s(t.labels.search))])]),e("div",{class:{"cmp-search-field__suggestions-container":t.suggestionsList.length||t.suggestedProduct.length}},[t.suggestionsList.length?e("ul",{staticClass:"cmp-search-field__suggestions-list cmp-search-field__suggestions-list-terms",attrs:{"aria-live":"polite"}},t._l(t.suggestionsList,function(i,n){return e("li",{key:i,ref:`suggestion-${n}`,refInFor:!0,staticClass:"cmp-search-field__suggestions-list-item",attrs:{role:"button",tabindex:"0"},domProps:{innerHTML:t._s(t.getSuggestionText(i))},on:{click:function(r){return t.termSuggestionSearch(i)},focus:function(r){return t.setQuery(i)},keyup:[function(r){return!r.type.indexOf("key")&&t._k(r.keyCode,"enter",13,r.key,"Enter")?null:t.termSuggestionSearch(i)},function(r){return!r.type.indexOf("key")&&t._k(r.keyCode,"up",38,r.key,["Up","ArrowUp"])?null:t.focusSuggestion(n-1)},function(r){if(!r.type.indexOf("key")&&t._k(r.keyCode,"down",40,r.key,["Down","ArrowDown"]))return null;n<t.suggestionsList.length-1&&t.focusSuggestion(n+1)}],keydown:[function(r){if(!r.type.indexOf("key")&&t._k(r.keyCode,"up",38,r.key,["Up","ArrowUp"]))return null;r.preventDefault()},function(r){if(!r.type.indexOf("key")&&t._k(r.keyCode,"down",40,r.key,["Down","ArrowDown"]))return null;r.preventDefault()}]}})}),0):t._e(),t.suggestedProduct.length?e("span",{staticClass:"cmp-search-field__suggested-label",domProps:{innerHTML:t._s(t.labels.suggestedProduct)}}):t._e(),t.suggestedProduct.length?e("ul",{staticClass:"cmp-search-field__suggestions-list cmp-search-field__suggestions-list-products",attrs:{"aria-live":"polite"}},t._l(t.suggestedProduct,function(i,n){return e("li",{key:i.productName,ref:`suggested-product-${n}`,refInFor:!0,staticClass:"cmp-search-field__suggestions-list-item",attrs:{tabindex:"0"},on:{focus:function(r){return t.setQuery(i.productName)}}},[e("a",{staticClass:"cmp-search-field__suggestions-list-product-link",attrs:{href:i.url,title:i.productName},on:{click:t.productSuggestionSearch}},[e("div",{staticClass:"cmp-search-field__product cmp-search-field__product--landscape"},[e("div",{staticClass:"cmp-search-field__product-item"},[e("div",{staticClass:"cmp-search-field__product-content"},[e("div",{staticClass:"cmp-search-field__product-image"},[e("elm-img",{staticClass:"elm-img elm-img--4-3",attrs:{alt:i.productName,"use-dpr":!1,src:{src:i.thumbnail,width:140,height:124},srcset:[{src:i.thumbnail,width:140,height:124},{src:i.thumbnail,width:400,height:300},{src:i.thumbnail,width:600,height:450},{src:i.thumbnail,width:768,height:576}],"src-format":"w={width}&h={height}","fill-mode":"cover"}})],1)]),e("div",{staticClass:"cmp-search-field__product-meta"},[e("div",{staticClass:"cmp-search-field__product-label",domProps:{innerHTML:t._s(i.productName)}})])])])])])}),0):t._e()])])])],1)])},$n=[],xn=l(kn,Tn,$n,!1,null,null,null,null);const ie=xn.exports,wn={name:"CmpExample",mixins:[c],props:{labels:{type:Object,default:()=>({})}}};var Ln=function(){var t=this,e=t._self._c;return e("p",[t._v(t._s(t.labels.text))])},En=[],In=l(wn,Ln,En,!1,null,null,null,null);const ae=In.exports,An={name:"CmpEmbedded",props:{children:{type:[Array,Function,String],required:!0},toggleHandler:{type:Function,default:a.noop}},render(s){return s("div",a.isFunction(this.children)?this.children({toggleHandler:this.toggleHandler}):this.children)}},Fn=null,On=null;var Mn=l(An,Fn,On,!1,null,null,null,null);const ne=Mn.exports,{mapMutations:Pn}=p,Rn={name:"CmpEmbeddedOverlayButton",mixins:[v,c],props:{buttonClass:{type:String,default:"elm-button"},buttonTextClass:{type:String,default:null},fullScreen:{type:Boolean,default:!1},heading:{type:String,default:null},label:{type:String,required:!0},hideLabel:{type:Boolean,default:!1},useSlideIn:{type:Boolean,default:!1},closeHandler:{type:Function,default:a.noop},toggleHandler:{type:Function,default:a.noop}},computed:{defaultButtonTextClass(){return a.trim([`${a.first(this.buttonClass.split(" "))}__text`,this.hideLabel?"h-hidden":""].join(" "))}},methods:{...Pn("base",["setOverlayComponent"]),setOverlay(s){const t=this.$slots.default;this.setOverlayComponent({component:"CmpEmbedded",fullScreen:this.fullScreen,heading:this.heading,props:{children:t},origin:{target:s.target,x:s.clientX,y:s.clientY},showToggle:this.showToggle,useSlideIn:this.useSlideIn,closeHandler:this.closeHandler})}}};var Nn=function(){var t=this,e=t._self._c;return e("div",[t.isEditMode?t._e():e("button",{class:t.buttonClass,attrs:{"aria-controls":"overlay",type:"button"},on:{click:t.setOverlay}},[e("span",{class:t.buttonTextClass||t.defaultButtonTextClass},[t._v(t._s(t.label))])]),t.isEditMode?t._t("default"):t._e()],2)},Hn=[],Bn=l(Rn,Nn,Hn,!1,null,null,null,null);const re=Bn.exports;function Is(s,t){return s.map(e=>{const i=e;return i.label.toLowerCase()==="local"&&i.filtervalues.forEach(n=>{const r=n;r.basevalue!=="false"?r.displayvalue=t.localCountry:r.displayvalue=t.international}),i})}const Dn={name:"CmpFacetsListCheckbox",mixins:[c],props:{basevalue:{type:[Number,String],required:!0},controlsId:{type:String,required:!0},count:{type:Number,default:0},displayvalue:{type:String,required:!0},id:{type:String,required:!0},updateHandler:{type:Function,required:!0},selected:{type:Boolean,required:!0}}};var qn=function(){var t=this,e=t._self._c;return e("div",[e("input",{staticClass:"cmp-form-option__field cmp-form-option__field--checkbox",attrs:{"aria-controls":t.controlsId,id:t.id,name:t.id,type:"checkbox"},domProps:{checked:t.selected,value:t.basevalue},on:{change:t.updateHandler}}),e("label",{staticClass:"cmp-form-option__label cmp-form-option__label--checkbox",attrs:{for:t.id,"data-qa":`facet-filter-${t.displayvalue.toLowerCase()}`},domProps:{innerHTML:t._s(`${t.displayvalue} (${t.count})`)}})])},zn=[],Vn=l(Dn,qn,zn,!1,null,null,null,null);const As=Vn.exports,Un=27,jn={name:"CmpFacetsOverlay",components:{CmpFacetsListCheckbox:As},mixins:[c],props:{container:{type:Node,required:!0},controlsId:{type:String,required:!0},id:{type:String,required:!0},labels:{type:Object,default:()=>({})},naturalFacetOrdering:{type:Boolean,default:!1},filtervalues:{type:Array,required:!0},toggleHandler:{type:Function,default:a.noop},updateHandler:{type:Function,default:a.noop}},computed:{firstColumnValues(){return this.sortedValues.filter(this.filterColumnIndex(0))},secondColumnValues(){return this.sortedValues.filter(this.filterColumnIndex(1))},thirdColumnValues(){return this.sortedValues.filter(this.filterColumnIndex(2))},fourthColumnValues(){return this.sortedValues.filter(this.filterColumnIndex(3))},sortedValues(){return this.naturalFacetOrdering?this.filtervalues:a.orderBy(this.filtervalues,["count","displayvalue"],["desc","desc"])}},mounted(){document.body.addEventListener("keyup",this.escapeHandler,!1),this.container.addEventListener("click",this.updateHandler,!1)},beforeDestroy(){document.body.removeEventListener("keyup",this.escapeHandler,!1),this.container.removeEventListener("click",this.updateHandler,!1)},methods:{close(){this.updateHandler(),this.toggleHandler()},selectFiltervalue(s){return({target:t})=>{s.selected=t.checked}},filterColumnIndex(s){const t=this.filtervalues.length/4;return(e,i)=>i>=s*t&&i<t*(s+1)},escapeHandler(s){s.keyCode===Un&&this.updateHandler()}}};var Gn=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cmp-facets-overlay"},[e("button",{staticClass:"elm-button cmp-overlay__toggle elm-button--close cmp-facets-overlay--close",attrs:{type:"button"},on:{click:t.close}},[e("span",{staticClass:"elm-square-button__text"},[t._v(t._s(t.labels.close))])]),e("div",{staticClass:"cmp-facets__body"},[e("div",{staticClass:"b-layout-grid"},[e("div",{staticClass:"b-layout-grid__group"},[e("div",{staticClass:"b-layout-grid__item b-layout-grid__item--3"},[e("div",{staticClass:"cmp-facets__list"},t._l(t.firstColumnValues,function(i){return e("cmp-facets-list-checkbox",t._b({key:t._f("kebabCase")(i.basevalue),staticClass:"cmp-form-option cmp-facets__list-item",attrs:{"controls-id":t.controlsId,id:t._f("kebabCase")(`${t.id}-${i.basevalue}`),"update-handler":t.selectFiltervalue(i)}},"cmp-facets-list-checkbox",i,!1))}),1)]),e("div",{staticClass:"b-layout-grid__item b-layout-grid__item--3"},[e("div",{staticClass:"cmp-facets__list"},t._l(t.secondColumnValues,function(i){return e("cmp-facets-list-checkbox",t._b({key:t._f("kebabCase")(i.basevalue),staticClass:"cmp-form-option cmp-facets__list-item",attrs:{"controls-id":t.controlsId,id:t._f("kebabCase")(`${t.id}-${i.basevalue}`),"update-handler":t.selectFiltervalue(i)}},"cmp-facets-list-checkbox",i,!1))}),1)]),e("div",{staticClass:"b-layout-grid__item b-layout-grid__item--3"},[e("div",{staticClass:"cmp-facets__list"},t._l(t.thirdColumnValues,function(i){return e("cmp-facets-list-checkbox",t._b({key:t._f("kebabCase")(i.basevalue),staticClass:"cmp-form-option cmp-facets__list-item",attrs:{"controls-id":t.controlsId,id:t._f("kebabCase")(`${t.id}-${i.basevalue}`),"update-handler":t.selectFiltervalue(i)}},"cmp-facets-list-checkbox",i,!1))}),1)]),e("div",{staticClass:"b-layout-grid__item b-layout-grid__item--3"},[e("div",{staticClass:"cmp-facets__list"},t._l(t.fourthColumnValues,function(i){return e("cmp-facets-list-checkbox",t._b({key:t._f("kebabCase")(i.basevalue),staticClass:"cmp-form-option cmp-facets__list-item",attrs:{"controls-id":t.controlsId,id:t._f("kebabCase")(`${t.id}-${i.basevalue}`),"update-handler":t.selectFiltervalue(i)}},"cmp-facets-list-checkbox",i,!1))}),1)])])])]),e("footer",{staticClass:"cmp-facets__footer"},[e("button",{staticClass:"elm-button cmp-facets__footer-button",attrs:{type:"button"},on:{click:t.close}},[t._v(" "+t._s(t.labels.close)+" ")])])])},Wn=[],Qn=l(jn,Gn,Wn,!1,null,null,null,null);const ot=Qn.exports,Kn={name:"CmpFacetsListSelect",mixins:[c],props:{label:{type:String,required:!0},options:{type:Array,required:!0},controlsId:{type:String,required:!0},id:{type:String,required:!0},updateHandler:{type:Function,required:!0}}};var Yn=function(){var t=this,e=t._self._c;return e("div",[e("select",{staticClass:"cmp-form-options__field cmp-form-options__field--drop-down",attrs:{"aria-controls":t.controlsId,id:t.id,name:t.id},on:{change:t.updateHandler}},t._l(t.options,function(i){return e("option",{key:i.value,domProps:{value:i.basevalue,selected:i.selected}},[t._v(t._s(i.displayvalue))])}),0)])},Zn=[],Jn=l(Kn,Yn,Zn,!1,null,null,null,null);const Xn=Jn.exports,{mapMutations:tr}=p;function Z(s){return s.selected}const er={name:"CmpFacetsList",components:{CmpFacetsListCheckbox:As,CmpFacetsListSelect:Xn},mixins:[c],props:{controlsId:{type:String,required:!0},filtervalues:{type:Array,required:!0},label:{type:String,required:!0},labels:{type:Object,default:()=>({})},minFiltervaluesLength:{type:Number,default:5},maxFiltervaluesLength:{type:Number,default:10},naturalFacetOrdering:{type:Boolean,default:!1},text:{type:String,required:!0},updateHandler:{type:Function,default:a.noop},useOverlay:{type:Boolean,default:!1},uniqueListId:{type:String,default:"list"}},data(){return{isVisible:!1}},computed:{selectedFiltervalueCount(){return this.filtervalues.filter(Z).length},isToggleVisible(){const s=!this.filtervalues.slice(this.minFiltervaluesLength).every(Z);return this.useOverlay?s&&this.filtervalues.length<=this.maxFiltervaluesLength:s},isShowAllToggleVisible(){const s=!this.filtervalues.slice(this.minFiltervaluesLength).every(Z);return this.useOverlay?s&&this.filtervalues.length>this.maxFiltervaluesLength:!1},sortedValues(){return this.naturalFacetOrdering?this.filtervalues:a.orderBy(this.filtervalues,["count","displayvalue"],["desc","desc"])}},methods:{...tr("base",["setOverlayComponent"]),getItemVisibility(s,t){const e=s.selected||t<this.minFiltervaluesLength;return this.useOverlay?e||t<this.maxFiltervaluesLength&&this.isVisible:e||this.isVisible},selectFiltervalue(s){return({target:t})=>{s.options?s.options.forEach(e=>{e.selected=e.basevalue.toString()===t.value}):s.selected=t.checked,this.updateHandler()}},showAll(s){this.setOverlayComponent({component:ot,heading:this.text,props:{controlsId:this.controlsId,filtervalues:this.filtervalues,id:`extended-${this.id}`,labels:this.labels,naturalFacetOrdering:this.naturalFacetOrdering,updateHandler:this.updateHandler},origin:{target:s.target,x:s.clientX,y:s.clientY},showToggle:!1})},toggle(){this.isVisible=!this.isVisible},getComponentType(s){return s.options?"CmpFacetsListSelect":"CmpFacetsListCheckbox"},getComponentId(s,t){return a.kebabCase(`${this.controlsId}-${this.label}-${this.getBaseKey(s)}-${this.uniqueListId}-${t}`)},getComponentKey(s,t){return`${a.kebabCase(this.getBaseKey(s))}-${t}`},getBaseKey(s){return s.options?s.label:s.basevalue}}};var sr=function(){var t=this,e=t._self._c;return e("div",[t._l(t.sortedValues,function(i,n){return t.getItemVisibility(i,n)?e(t.getComponentType(i),t._b({key:t.getComponentKey(i,n),tag:"component",staticClass:"cmp-form-option cmp-facets__list-item",attrs:{"controls-id":t.controlsId,id:t.getComponentId(i,n),"update-handler":t.selectFiltervalue(i)}},"component",i,!1)):t._e()}),t.isToggleVisible?e("button",{class:["elm-link",t.isVisible&&"elm-link__show-less",!t.isVisible&&"elm-link__show-more"],attrs:{type:"button"},on:{click:t.toggle}},[t._v(" "+t._s(t.isVisible?t.labels.showLess:t.labels.showMore)+" ")]):t._e(),t.isShowAllToggleVisible?e("button",{staticClass:"elm-link",attrs:{type:"button"},on:{click:t.showAll}},[t._v(" "+t._s(t.labels.showAll)+" ")]):t._e()],2)},ir=[],ar=l(er,sr,ir,!1,null,null,null,null);const nr=ar.exports,rr=1e3,or=4,lr={name:"CmpFacets",components:{CmpFacetsList:nr},mixins:[c,_],props:{autoUpdate:{type:Boolean,default:!1},collapseFacets:{type:Boolean,default:!1},controlsId:{type:String,required:!0},items:{type:Array,default:()=>[]},labels:{type:Object,default:()=>({})},naturalFacetOrdering:{type:Boolean,default:!1},resetHandler:{type:Function,default:a.noop},toggleHandler:{type:Function,default:a.noop},updateHandler:{type:Function,default:a.noop},useOverlay:{type:Boolean,default:!1}},computed:{selectionChanged(){return this.initialFacetsState!==this.getFacetsState()},mappedItems(){return Is(this.items,this.labels)},uniqueListId(){return vt(or).toString("hex")}},created(){this.debouncedUpdate=a.debounce(this.update,rr),this.initialFacetsState=this.getFacetsState()},mounted(){this.update(),this.removeUrlFilters()},methods:{close(){this.update(),this.toggleHandler()},reset(){this.resetHandler(),this.toggleHandler()},update(){this.updateHandler(JSON.parse(this.getFacetsState()))},selectFacets(s){s.forEach(t=>{const e=this.items.find(i=>i.label===t.field);e&&(e.filtervalues=e.filtervalues.map(i=>t.value.includes(i.basevalue)?{...i,selected:!0}:i))})},getFacetsState(){const s=S(window.location.search),t=this.getFilters(s);return this.selectFacets(t),JSON.stringify(this.items)},getFilters(s){const t=[];let e=0;for(;a.get(s,`filters[${e}]`);)t.push(a.get(s,`filters[${e}]`)),e++;return t},removeUrlFilters(){const s=new URL(window.location.href),t=new URLSearchParams(s.search);[...t.keys()].filter(e=>decodeURIComponent(e).startsWith("filters")).forEach(e=>t.delete(e)),s.search=t.toString(),window.history.replaceState({},"",`${s.pathname}${s.search}`)}}};var cr=function(){var t=this,e=t._self._c;return e("section",{staticClass:"cmp-facets",attrs:{"data-qa":"facets"}},[t.isMobile?e("div",{staticClass:"cmp-facets__toolbar"},[e("button",{ref:"toggle",staticClass:"elm-button cmp-overlay__toggle elm-button--close",attrs:{type:"button"},on:{click:function(i){return i.stopPropagation(),t.close.apply(null,arguments)}}},[e("span",{staticClass:"elm-square-button__text"},[t._v(t._s(t.labels.close))])])]):t._e(),e("header",{staticClass:"cmp-facets__header"},[t.labels.heading?e("h3",{staticClass:"cmp-facets__heading"},[t._v(" "+t._s(t.labels.heading)+" ")]):t._e(),e("button",{staticClass:"elm-link elm-link--block",attrs:{disabled:!t.selectionChanged,type:"button","data-qa":"facets-reset"},on:{click:t.reset}},[t._v(" "+t._s(t.labels.reset)+" ")])]),e("div",{staticClass:"cmp-facets__body"},[e("div",{staticClass:"cmp-facets__wrapper"},[e("cmp-accordion",{staticClass:"cmp-accordion cmp-facets__accordion",scopedSlots:t._u([{key:"default",fn:function(i){return t._l(t.mappedItems,function(n){return e("cmp-accordion-item",{key:n.label,staticClass:"cmp-accordion-item cmp-facets__group",attrs:{id:n.label,"is-active":i.isActive,"is-open":t.useOverlay&&!t.collapseFacets,"data-qa":`facet-group-${n.label.toLowerCase()}`}},[e("h3",{staticClass:"cmp-accordion-item__heading cmp-facets__group-heading",attrs:{slot:"header"},slot:"header"},[t._v(" "+t._s(n.text)+" ")]),e("div",{attrs:{slot:"content"},slot:"content"},[e("cmp-facets-list",t._b({staticClass:"cmp-facets__list",attrs:{"controls-id":t.controlsId,labels:t.labels,"natural-facet-ordering":t.naturalFacetOrdering,"use-overlay":t.useOverlay,"update-handler":t.autoUpdate?t.debouncedUpdate:()=>{},"unique-list-id":t.uniqueListId}},"cmp-facets-list",n,!1))],1)])})}}])})],1)]),t.useOverlay?t._e():e("footer",{staticClass:"cmp-facets__footer cmp-facets__footer--centered"},[e("button",{staticClass:"elm-button cmp-facets__footer-button",attrs:{type:"button"},on:{click:t.close}},[t._v(" "+t._s(t.labels.close)+" ")])])])},dr=[],ur=l(lr,cr,dr,!1,null,null,null,null);const lt=ur.exports,{mapMutations:pr}=p,mr={name:"CmpFacetsContainer",mixins:[_,c],props:{collapseFacets:{type:Boolean,default:!1},controlsId:{type:String,required:!0},items:{type:Array,default:()=>[]},isLoading:{type:Boolean,default:!1},labels:{type:Object,default:()=>({})},naturalFacetOrdering:{type:Boolean,default:!1},resetHandler:{type:Function,default:a.noop},updateHandler:{type:Function,default:a.noop},useOverlay:{type:Boolean,default:!1}},data(){return{LOADING:x}},computed:{selectedItemCount(){return this.items.reduce((s,t)=>s+a.get(t,"filtervalues",[]).filter(({selected:e})=>e).length,0)}},methods:{...pr("base",["setOverlayComponent"]),showFilters(s){this.setOverlayComponent({component:lt,fullScreen:this.isMobile,props:{controlsId:this.controlsId,items:at(this.items),labels:this.labels,resetHandler:this.resetHandler,updateHandler:this.updateHandler},origin:{target:s.target,x:s.clientX,y:s.clientY},showToggle:!1,transparent:!0})}}};var hr=function(){var t=this,e=t._self._c;return e("div",[t.useOverlay||t.isMobile?e("button",{staticClass:"elm-button elm-button--ghost",attrs:{type:"button"},on:{click:t.showFilters}},[e("span",{staticClass:"elm-button__text"},[t._v(t._s(t.labels.heading)+" ("+t._s(t.selectedItemCount)+")")])]):e("cmp-facets",{class:["cmp-facets",t.isLoading&&t.LOADING],attrs:{"auto-update":!0,"collapse-facets":t.collapseFacets,"controls-id":t.controlsId,items:t.items,labels:t.labels,"natural-facet-ordering":t.naturalFacetOrdering,"reset-handler":t.resetHandler,"update-handler":t.updateHandler,"use-overlay":""}})],1)},_r=[],fr=l(mr,hr,_r,!1,null,null,null,null);const oe=fr.exports,gr={name:"CmpFaqResultList",mixins:[P,c],props:{id:{type:String,required:!0},items:{type:Array,default:()=>[]},labels:{type:Object,default:()=>({})}}};var br=function(){var t=this,e=t._self._c;return e("section",{staticClass:"cmp-result-list",attrs:{id:t.id}},[e("div",{staticClass:"cmp-result-list__body"},[e("cmp-accordion",{staticClass:"cmp-accordion",attrs:{itemscope:"",itemtype:"https://schema.org/ItemList"},scopedSlots:t._u([{key:"default",fn:function(i){return t._l(t.items,function(n,r){return e("cmp-accordion-item",{key:t.generateHash(n),staticClass:"cmp-accordion-item",attrs:{id:`accordion-item-${r}`,"is-active":i.isActive,"tracking-id":"faq-overview-page",itemprop:"itemListElement",itemscope:"",itemtype:"https://schema.org/ListItem"}},[e("h3",{staticClass:"cmp-accordion-item__heading",attrs:{slot:"header"},slot:"header"},[t._v(" "+t._s(n.faqQuestion||n.title)+" ")]),e("div",{attrs:{slot:"content"},slot:"content"},[n.faqAnswer?e("div",{domProps:{innerHTML:t._s(n.faqAnswer)}}):t._e(),n.desc&&n.pageType!=="faq"?e("p",[t._v(t._s(n.desc))]):t._e(),n.canonicalUrl?e("a",{staticClass:"elm-link elm-link--block",attrs:{href:n.canonicalUrl}},[t._v(" "+t._s(n.title)+" ")]):t._e(),e("meta",{attrs:{content:n.canonicalUrl,itemprop:"url"}}),e("meta",{attrs:{content:r,itemprop:"position"}})])])})}}])})],1)])},vr=[],yr=l(gr,br,vr,!1,null,null,null,null);const le=yr.exports,Cr="form event",Sr="sent",kr={name:"CmpForm",props:{action:{type:String,required:!0},method:{type:String,required:!0},resourceName:{type:String,required:!0}},methods:{submit(){this.$el.checkValidity()&&f({form:{event:Sr,name:this.resourceName}},Cr)}}};var Tr=function(){var t=this,e=t._self._c;return e("form",{attrs:{action:t.action,method:t.method,autocomplete:"off"},on:{submit:t.submit}},[t._t("default")],2)},$r=[],xr=l(kr,Tr,$r,!1,null,null,null,null);const ce=xr.exports,b={props:{autoComplete:{type:String,default:null},autoFocus:{type:Boolean,default:!1},compact:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},errorMessage:{type:[Boolean,String],default:null},id:{type:String,required:!0},label:{type:String,required:!0},maxlength:{type:[Number,String],default:null},modifier:{type:[Array,String],default:null},name:{type:String,default:null},pattern:{type:String,default:null},postfix:{type:String,default:null},required:{type:Boolean,default:!1},updateHandler:{type:Function,default:null},value:{type:[Number,String],default:null},labelTooltip:{type:String,default:null}},computed:{hasError(){return!!this.errorMessage}},mounted(){this.autoFocus&&this.$refs.input.focus()},methods:{emitInput(s){a.isFunction(this.updateHandler)&&this.updateHandler(s),this.$emit("input",s.target.value)},getModifierClasses(s){return this.modifier?(a.isArray(this.modifier)?this.modifier:[this.modifier]).map(e=>`${s}--${e}`):[]}}},wr={name:"CmpFormCheckbox",mixins:[b,c],props:{value:{type:Boolean,default:!1}},methods:{emitInput(s){a.isFunction(this.updateHandler)&&this.updateHandler(s),this.$emit("input",s.target.checked)}}};var Lr=function(){var t=this,e=t._self._c;return e("div",{class:["cmp-form-options","cmp-form-options--checkbox",t.hasError&&"cmp-form-options--error"].concat(t.getModifierClasses("cmp-form-options"))},[e("div",{staticClass:"cmp-form-option"},[e("input",{ref:"input",staticClass:"cmp-form-option__field cmp-form-option__field--checkbox",attrs:{"aria-invalid":t.hasError,disabled:t.disabled,id:t.id,name:t.name||t.id,required:t.required,type:"checkbox"},domProps:{checked:t.value,value:t.value},on:{change:t.emitInput}}),e("label",{class:["cmp-form-option__label","cmp-form-option__label--checkbox",t.required&&"cmp-form-option__label--required"],attrs:{for:t.id}},[t._v(" "+t._s(t.label)+" ")]),t.hasError?e("p",{staticClass:"cmp-form-options__error-block",attrs:{role:"alert"}},[t._v(" "+t._s(t.errorMessage)+" ")]):t._e()])])},Er=[],Ir=l(wr,Lr,Er,!1,null,null,null,null);const de=Ir.exports,Ar={name:"CmpFormCheckboxGroup",mixins:[b,c],props:{inline:{type:Boolean,default:!1},label:{type:String,default:null},options:{type:Array,default:()=>[]},value:{type:Array,required:!0}},methods:{emitInput(s){a.isFunction(this.updateHandler)&&this.updateHandler(s);const{checked:t,value:e}=s.target,i=t?a.uniq(at(this.value).concat(e)):a.without(at(this.value),e);this.$emit("input",i)}}};var Fr=function(){var t=this,e=t._self._c;return e("fieldset",{class:["cmp-form-options","cmp-form-options--checkbox",t.hasError&&"cmp-form-options--error"].concat(t.getModifierClasses("cmp-form-options"))},[t.label?e("legend",{class:["cmp-form-options__legend",t.required&&"cmp-form-options__legend--required",t.compact&&"h-hidden"]},[t._v(" "+t._s(t.label)+" ")]):t._e(),e("div",{class:["cmp-form-options__group",t.inline&&"cmp-form-options__group--inline"]},t._l(t.options,function(i,n){return e("div",{key:i.key,staticClass:"cmp-form-option"},[e("input",{ref:n===0&&"input",refInFor:!0,staticClass:"cmp-form-option__field cmp-form-option__field--checkbox",attrs:{"aria-invalid":t.hasError,id:t._f("kebabCase")(`${t.id}-${i.key}`),name:t.name||t.id,required:t.required,type:"checkbox"},domProps:{checked:t.value.includes(i.key),value:i.key},on:{change:t.emitInput}}),e("label",{staticClass:"cmp-form-option__label cmp-form-option__label--checkbox",attrs:{for:t._f("kebabCase")(`${t.id}-${i.key}`)}},[t._v(" "+t._s(i.value)+" ")])])}),0),t.hasError?e("p",{staticClass:"cmp-form-options__error-block",attrs:{role:"alert"}},[t._v(" "+t._s(t.errorMessage)+" ")]):t._e()])},Or=[],Mr=l(Ar,Fr,Or,!1,null,null,null,null);const ue=Mr.exports,Pr={name:"CmpFormDate",mixins:[b,c]};var Rr=function(){var t=this,e=t._self._c;return e("div",{class:["cmp-form-text",t.hasError&&"cmp-form-text--error"].concat(t.getModifierClasses("cmp-form-text"))},[e("label",{class:["cmp-form-text__label",t.required&&"cmp-form-text__label--required",t.compact&&"h-hidden"],attrs:{for:t.id}},[t._v(" "+t._s(t.label)+" ")]),e("div",{staticClass:"cmp-form-text__input-container"},[e("input",{ref:"input",staticClass:"cmp-form-text__text",attrs:{"aria-invalid":t.hasError,disabled:t.disabled,id:t.id,name:t.name||t.id,required:t.required,pattern:"\\d{4}-\\d{2}-\\d{2}",type:"date"},domProps:{value:t.value},on:{input:t.emitInput}}),t.postfix?e("span",{staticClass:"cmp-form-text__postfix"},[t._v(" "+t._s(t.postfix)+" ")]):t._e()]),e("p",{staticClass:"cmp-form-text__help-block cmp-form-text__help-block--fallback"},[t._v("YYYY-MM-DD")]),t.hasError?e("p",{staticClass:"cmp-form-text__error-block",attrs:{role:"alert"}},[t._v(" "+t._s(t.errorMessage)+" ")]):t._e()])},Nr=[],Hr=l(Pr,Rr,Nr,!1,null,null,null,null);const pe=Hr.exports,Br={name:"CmpFormEmail",mixins:[b,c],props:{placeholder:{type:String,default:null}}};var Dr=function(){var t=this,e=t._self._c;return e("div",{class:["cmp-form-text",t.hasError&&"cmp-form-text--error"].concat(t.getModifierClasses("cmp-form-text"))},[e("label",{class:["cmp-form-text__label",t.required&&"cmp-form-text__label--required",t.compact&&"h-hidden"],attrs:{for:t.id}},[t._v(" "+t._s(t.label)+" ")]),e("div",{staticClass:"cmp-form-text__input-container"},[e("input",{ref:"input",staticClass:"cmp-form-text__text",attrs:{"aria-invalid":t.hasError,disabled:t.disabled,id:t.id,maxlength:t.maxlength,name:t.name||t.id,placeholder:t.placeholder,required:t.required,type:"email"},domProps:{value:t.value},on:{input:t.emitInput}}),t.postfix?e("span",{staticClass:"cmp-form-text__postfix"},[t._v(" "+t._s(t.postfix)+" ")]):t._e()]),t.hasError?e("p",{staticClass:"cmp-form-text__error-block",attrs:{role:"alert"}},[t._v(" "+t._s(t.errorMessage)+" ")]):t._e()])},qr=[],zr=l(Br,Dr,qr,!1,null,null,null,null);const me=zr.exports,Vr={name:"CmpFormFile",mixins:[b,c],props:{updateHandler:{type:Function,required:!0}},emitInput(s){this.updateHandler(s)},computed:{buttonLabel(){let s;return this.value&&(this.value.includes("/")?s=a.last(this.value.split("/")):this.value.includes("\\")&&(s=a.last(this.value.split("\\")))),s||this.label}},methods:{upload(){this.$refs.input.click()}}};var Ur=function(){var t=this,e=t._self._c;return e("div",{class:["cmp-form-text",t.hasError&&"cmp-form-text--error"].concat(t.getModifierClasses("cmp-form-text"))},[e("label",{class:["cmp-form-text__label",t.required&&"cmp-form-text__label--required",t.compact&&"h-hidden"],attrs:{for:t.id}},[t._v(" "+t._s(t.label)+" ")]),e("div",{staticClass:"cmp-form-text__input-container"},[e("input",{ref:"input",staticClass:"h-hidden",attrs:{"aria-invalid":t.hasError,disabled:t.disabled,id:t.id,name:t.name||t.id,required:t.required,type:"file"},on:{input:t.emitInput}}),e("button",{staticClass:"elm-button elm-button--small elm-button--icon-upload_outline cmp-form-text__file-button",attrs:{type:"button"},on:{click:t.upload}},[t._v(" "+t._s(t.buttonLabel)+" ")]),t.postfix?e("span",{staticClass:"cmp-form-text__postfix"},[t._v(" "+t._s(t.postfix)+" ")]):t._e()]),t.hasError?e("p",{staticClass:"cmp-form-text__error-block",attrs:{role:"alert"}},[t._v(" "+t._s(t.errorMessage)+" ")]):t._e()])},jr=[],Gr=l(Vr,Ur,jr,!1,null,null,null,null);const he=Gr.exports,Wr={name:"CmpFormNumber",mixins:[b,c],props:{int:{type:Boolean,default:!1},max:{type:Number,default:null},min:{type:Number,default:null},step:{type:[Number,String],default:"any"},placeholder:{type:String,default:null}}};var Qr=function(){var t=this,e=t._self._c;return e("div",{class:["cmp-form-text",t.hasError&&"cmp-form-text--error"].concat(t.getModifierClasses("cmp-form-text"))},[e("label",{directives:[{name:"tooltip",rawName:"v-tooltip:top",value:t.labelTooltip,expression:"labelTooltip",arg:"top"}],class:["cmp-form-text__label",t.required&&"cmp-form-text__label--required",t.compact&&"h-hidden"],attrs:{for:t.id}},[t._v(" "+t._s(t.label)+" ")]),e("div",{staticClass:"cmp-form-text__input-container"},[e("input",{ref:"input",staticClass:"cmp-form-text__text",attrs:{"aria-invalid":t.hasError,disabled:t.disabled,id:t.id,inputmode:t.int?"numeric":"decimal",name:t.name||t.id,max:t.max,maxlength:t.maxlength,min:t.min,placeholder:t.placeholder,required:t.required,step:t.int?1:t.step,type:"number"},domProps:{value:t.value},on:{input:t.emitInput}}),t.postfix?e("span",{staticClass:"cmp-form-text__postfix"},[t._v(" "+t._s(t.postfix)+" ")]):t._e()]),t.hasError?e("p",{staticClass:"cmp-form-text__error-block",attrs:{role:"alert"}},[t._v(" "+t._s(t.errorMessage)+" ")]):t._e()])},Kr=[],Yr=l(Wr,Qr,Kr,!1,null,null,null,null);const _e=Yr.exports,Zr={name:"CmpFormRadioGroup",mixins:[b,c],props:{inline:{type:Boolean,default:!1},label:{type:String,default:null},options:{type:Array,default:()=>[]}}};var Jr=function(){var t=this,e=t._self._c;return e("div",{class:["cmp-form-options","cmp-form-options--radio",t.hasError&&"cmp-form-options--error"].concat(t.getModifierClasses("cmp-form-options"))},[t.label?e("span",{class:["cmp-form-options__legend",t.required&&"cmp-form-options__legend--required",t.compact&&"h-hidden"]},[t._v(" "+t._s(t.label)+" ")]):t._e(),e("div",{class:["cmp-form-options__group",t.inline&&"cmp-form-options__group--inline"]},t._l(t.options,function(i,n){return e("div",{key:i.key,staticClass:"cmp-form-option"},[e("input",{ref:n===0&&"input",refInFor:!0,staticClass:"cmp-form-option__field cmp-form-option__field--radio",attrs:{"aria-invalid":t.hasError,id:t._f("kebabCase")(`${t.id}-${i.key}`),name:t.name||t.id,required:t.required,type:"radio"},domProps:{checked:t.value===i.key,value:i.key},on:{change:t.emitInput}}),e("label",{staticClass:"cmp-form-option__label cmp-form-option__label--radio",attrs:{for:t._f("kebabCase")(`${t.id}-${i.key}`)}},[t._v(" "+t._s(i.value)+" ")])])}),0),t.hasError?e("p",{staticClass:"cmp-form-options__error-block",attrs:{role:"alert"}},[t._v(" "+t._s(t.errorMessage)+" ")]):t._e()])},Xr=[],to=l(Zr,Jr,Xr,!1,null,null,null,null);const ct=to.exports,eo={name:"CmpFormSearch",mixins:[b,c],props:{placeholder:{type:String,default:null},text:{type:String,required:!0},searchHandler:{type:Function,default:a.noop}},methods:{emitSearchInput(){const s=this.$refs.input;a.isFunction(this.updateHandler)&&this.updateHandler({target:s}),this.$emit("input",s.value)}}};var so=function(){var t=this,e=t._self._c;return e("div",{class:["cmp-form-text"].concat(t.getModifierClasses("cmp-form-text"))},[e("label",{class:["cmp-form-text__label",t.compact&&"h-hidden"],attrs:{for:t.id}},[t._v(" "+t._s(t.label)+" ")]),e("div",{staticClass:"cmp-form-text__input-container"},[e("input",{ref:"input",staticClass:"cmp-form-text__text",attrs:{disabled:t.disabled,id:t.id,name:t.name||t.id,placeholder:t.placeholder,required:t.required,type:"search"},domProps:{value:t.value},on:{search:function(i){return i.preventDefault(),t.emitInput.apply(null,arguments)}}}),e("button",{staticClass:"elm-square-button elm-square-button--icon-search_outline cmp-form-text__search-button",attrs:{type:"button"},on:{click:t.emitSearchInput}},[t._v(" "+t._s(t.text)+" ")])])])},io=[],ao=l(eo,so,io,!1,null,null,null,null);const fe=ao.exports,no={name:"CmpFormSelect",mixins:[b,c],props:{options:{type:Array,default:()=>[]},value:{type:[Boolean,Number,String],default:null}}};var ro=function(){var t=this,e=t._self._c;return e("div",{class:["cmp-form-options","cmp-form-options--drop-down",t.hasError&&"cmp-form-options--error"].concat(t.getModifierClasses("cmp-form-options"))},[e("label",{class:["cmp-form-options__label",t.required&&"cmp-form-options__label--required",t.compact&&"h-hidden"],attrs:{for:t.id}},[t._v(" "+t._s(t.label)+" ")]),e("div",{staticClass:"cmp-form-options__input-container"},[e("select",{ref:"input",staticClass:"cmp-form-options__field cmp-form-options__field--drop-down",attrs:{"aria-invalid":t.hasError,disabled:t.disabled||!t.options.length,id:t.id,name:t.name||t.id,required:t.required,"data-qa":`form-select-${t.name||t.id}`},domProps:{value:t.value},on:{change:t.emitInput}},t._l(t.options,function(i){return e("option",{key:i.key,domProps:{value:i.key}},[t._v(" "+t._s(i.value)+" ")])}),0),t.postfix?e("span",{staticClass:"cmp-form-options__postfix"},[t._v(" "+t._s(t.postfix)+" ")]):t._e()]),t.hasError?e("p",{staticClass:"cmp-form-options__error-block",attrs:{role:"alert"}},[t._v(" "+t._s(t.errorMessage)+" ")]):t._e()])},oo=[],lo=l(no,ro,oo,!1,null,null,null,null);const dt=lo.exports,co={name:"CmpFormText",mixins:[b,c],props:{placeholder:{type:String,default:null},type:{type:String,default:"text"}}};var uo=function(){var t=this,e=t._self._c;return e("div",{class:["cmp-form-text",t.hasError&&"cmp-form-text--error"].concat(t.getModifierClasses("cmp-form-text"))},[e("label",{class:["cmp-form-text__label",t.required&&"cmp-form-text__label--required",t.compact&&"h-hidden"],attrs:{for:t.id}},[t._v(" "+t._s(t.label)+" ")]),e("div",{staticClass:"cmp-form-text__input-container"},[e("input",{ref:"input",staticClass:"cmp-form-text__text",attrs:{autocomplete:t.autoComplete,"aria-invalid":t.hasError,disabled:t.disabled,id:t.id,maxlength:t.maxlength,name:t.name||t.id,pattern:t.pattern,placeholder:t.placeholder,required:t.required,type:t.type},domProps:{value:t.value},on:{input:t.emitInput}}),t.postfix?e("span",{staticClass:"cmp-form-text__postfix"},[t._v(" "+t._s(t.postfix)+" ")]):t._e()]),t.hasError?e("p",{staticClass:"cmp-form-text__error-block",attrs:{role:"alert"}},[t._v(" "+t._s(t.errorMessage)+" ")]):t._e()])},po=[],mo=l(co,uo,po,!1,null,null,null,null);const ut=mo.exports,ho={name:"CmpFormTextarea",mixins:[b,c],props:{placeholder:{type:String,default:null}}};var _o=function(){var t=this,e=t._self._c;return e("div",{class:["cmp-form-text",t.hasError&&"cmp-form-text--error"].concat(t.getModifierClasses("cmp-form-text"))},[e("label",{class:["cmp-form-text__label",t.required&&"cmp-form-text__label--required",t.compact&&"h-hidden"],attrs:{for:t.id}},[t._v(" "+t._s(t.label)+" ")]),e("div",{staticClass:"cmp-form-text__input-container"},[e("textarea",{ref:"input",staticClass:"cmp-form-text__textarea",attrs:{"aria-invalid":t.hasError,disabled:t.disabled,id:t.id,maxlength:t.maxlength,name:t.name||t.id,placeholder:t.placeholder,required:t.required,type:"text"},domProps:{value:t.value},on:{input:t.emitInput}}),t._v(" "),t.postfix?e("span",{staticClass:"cmp-form-text__postfix"},[t._v(" "+t._s(t.postfix)+" ")]):t._e()]),t.hasError?e("p",{staticClass:"cmp-form-text__error-block",attrs:{role:"alert"}},[t._v(" "+t._s(t.errorMessage)+" ")]):t._e()])},fo=[],go=l(ho,_o,fo,!1,null,null,null,null);const ge=go.exports,{mapState:bo}=p,C="[data-nav]",vo="[data-sub-nav]",$="[data-sub-child-nav]",yo="[data-sub-child-item]",be="[data-sub-child-nav-item]",N=9,J="0ms",H="cmp-header-nav__sub-child-nav-link--hidden",Co=500,ve=350,So={name:"CmpHeaderNavItems",components:{CmpShowAllLink:nt,simplebar:Ls},mixins:[_,c,Bs],props:{currentPage:{type:String,required:!1,default:null},subNavCollapseDelay:{type:String,default:J},labels:{type:Object,default:()=>({})},menuItems:{type:String,required:!1,default:null},isAuthorMode:{type:Boolean,default:!1}},data(){return{menus:JSON.parse(this.menuItems),selectedFirstLevelNavElement:null,THIRD_LEVEL_MENU_ITEM_MAX_LIMIT:N,preventOpening:!1,NAV_TRANSITION_TIME:ve,isNavTransitioning:!1}},computed:{...bo("base",["windowScroll","viewportSize"])},mounted(){this.initNavigation(this.$el),this.setActiveMenu(),this.$watch("windowScroll",this.windowScrolled)},methods:{initNavigation(s){this.setNoSubNavCollapseDelay(s),this.nextChosenElement=s},hasChildren(s){return s.length>0},isActive(s){return this.currentPage===s},setActiveMenu(){const s=document.querySelector(".cmp-header-nav__sub-child-nav-link--active");if(s!==null){const t=s.closest($);t.firstChild.classList.add("cmp-header-nav__sub-nav-link--active"),this.expandNav(t),this.setActiveNav()}this.setActiveNav()},setActiveNav(){const s=document.querySelector(".cmp-header-nav__sub-nav-link--active");if(s!==null){const t=s.closest(C);t.classList.add("cmp-header-nav__list-item--active"),this.isMobile&&this.expandNav(t)}},navItemClickHandler(s){this.setActiveMenu();const t=Array.from(this.$el.querySelectorAll(C)),e=Array.from(this.$el.querySelectorAll($)),i=s.target.closest(C);if(!this.isMobile&&this.selectedFirstLevelNavElement===i){this.transitionNav(),this.resetNav(t),this.resetNav(e),this.selectedFirstLevelNavElement=null;return}this.resetNav(t),this.resetNav(e),this.selectedFirstLevelNavElement||this.transitionNav(),this.expandNav(i),this.selectedFirstLevelNavElement=i;const n=document.querySelectorAll(".cmp-header-nav__child-nav");this.isMobile?this.resetNav(n):this.handleSelectedChildNavItems(s,n)},handleSelectedChildNavItems(s,t){this.selectedSubNavElement=s.currentTarget.nextElementSibling,this.selectedSubChildNavElement=Array.from(this.selectedSubNavElement.querySelectorAll(yo)),this.isTablet&&this.resetNav(t),this.selectedSubChildNavElement.forEach(e=>{const i=e.closest($),n=Array.from(i.querySelectorAll(be)),r=n.slice(N);n.forEach((d,u)=>{d.getAttribute("href")===this.currentPage&&(this.expandNav(i),d.scrollIntoView({behavior:"smooth",block:"end"}),u<=N?r.forEach(g=>{g.classList.add(H)}):(r.forEach(g=>{g.classList.remove(H)}),i.querySelector("[data-toggle-view-all]").setAttribute("data-toggle-view-all",!0),i.querySelector("[data-toggle-view-all]").classList.add("cmp-header-nav__sub-child-view-all--hidden")))})})},subNavContainerClickHandler(s){const t=s.target.closest(C);!this.isMobile&&this.isTablet&&this.expandNav(t)},subNavItemActionHandler(s){if(this.isMobile){const t=s.target.closest(C);this.expandNav(t)}else{this.isTablet&&this.subNavItemClickHandler(s);const t=s.currentTarget.nextElementSibling,e=Array.from(t.querySelectorAll(be)),i=t.querySelector(".cmp-header-nav__sub-child-view-all");this.toggleSubChildNavLinks(i,e)}},toggleSubChildNavLinks(s,t){s!==null&&s.dataset.toggleViewAll===""?t.slice(N).forEach(i=>{i.classList.add(H)}):t.forEach(e=>{e.classList.remove(H)})},subNavItemClickHandler(s){const t=s.target.closest(C),e=s.target.closest($);this.expandNav(t),this.expandNav(e)},subNavItemClickMobileHandler(s){this.isMobile&&(this.subNavItemClickHandler(s),W(0,0,!0,this.$el.parentElement))},expandNav(s){this.preventOpening||(this.selectedNavElement=s||null,this.selectedNavElement&&this.selectedNavElement.setAttribute("aria-expanded",!0))},collapseNav(s){this.selectedNavElement=s,this.selectedNavElement&&(this.selectedNavElement.setAttribute("aria-expanded",!1),this.selectedNavElement=null)},resetNav(s){s.forEach(t=>{this.collapseNav(t)})},expandSubNavItem(s){if(!this.isMobile){const t=s.target.closest(C);this.expandNav(t);const e=s.target.closest($);this.expandNav(e),this.selectedNavEl=s.target.closest(vo),this.subNavEls=Array.from(this.selectedNavEl.querySelectorAll($)),this.resetNav(this.subNavEls),this.expandNav(s.currentTarget),s.currentTarget.setAttribute("style","max-width: 100%;")}},collapseSubNavItem(s){if(!this.isMobile){const t=s.target.closest(C);this.expandNav(t),this.collapseNav(s.currentTarget),s.currentTarget.removeAttribute("style")}},addNavTransitionDelay(){this.setSubNavCollapseDelay()},removeNavTransitionDelay(){this.setSubNavCollapseDelay(this.noDelay?J:this.subNavCollapseDelay)},setSubNavCollapseDelay(s=J){this.subChildContainer=Array.from(this.$el.querySelectorAll(".cmp-header-nav__sub-nav-container")),this.subChildContainer.forEach(t=>{t.setAttribute("style",`transition-delay: ${s}`)})},setNoSubNavCollapseDelay(s){this.noDelay=s!==this.nextChosenElement},overlayEventListener(s){if(!this.isMobile){const t=s.target.closest(C);this.transitionNav(),this.collapseNav(t),this.selectedFirstLevelNavElement=null}},subNavBackButton(s){if(this.isMobile){const t=s.target.closest(C)||null;this.collapseNav(t),W(0,0,!0,this.$el)}},subChildNavBackButton(s){if(this.isMobile){const t=s.target.closest($);this.collapseNav(t),W(0,0,!0,this.$el)}},shouldDisplayFeatureSpot(s){return s&&s.headline&&s.description&&s.link&&s.linkText&&s.image},windowScrolled(){const s=document.querySelector(`${C}[aria-expanded="true"]`);s&&(this.transitionNav(),this.collapseNav(s),this.selectedFirstLevelNavElement=null,this.preventOpening=!0,setTimeout(()=>{this.preventOpening=!1},Co))},transitionNav(){this.isNavTransitioning=!0,setTimeout(()=>{this.isNavTransitioning=!1},ve)}}};var ko=function(){var t=this,e=t._self._c;return e("div",[e("div",{class:["cmp-header-nav__list",t.isNavTransitioning&&"cmp-header-nav__list--transition"],style:`--nav-transition-time: ${t.NAV_TRANSITION_TIME}ms`},t._l(t.menus,function(i,n){return e("div",{key:t._f("kebabCase")(`${i.name}-${n}`),class:["cmp-header-nav__list-item",t.isActive(i.url)&&"cmp-header-nav__list-item--active"],attrs:{"data-nav":""}},[e("a",{class:["cmp-header-nav__link",t.hasChildren(i.children)&&"cmp-header-nav__link--toggle"],attrs:{href:t.hasChildren(i.children)?"javascript:void(0)":i.url,"data-toggle-nav":t.hasChildren(i.children)},on:{click:t.navItemClickHandler}},[e("span",{domProps:{innerHTML:t._s(i.name)}})]),t.hasChildren(i.children)?e("div",{staticClass:"cmp-header-nav__sub-nav",attrs:{"data-sub-nav":i.name},on:{click:function(r){return r.stopPropagation(),t.subNavContainerClickHandler.apply(null,arguments)}}},[t.isMobile?e("div",[e("button",{staticClass:"cmp-header-nav__sub-nav-link cmp-header-nav__sub-nav-link--toggle",attrs:{type:"button","data-toggle-sub-nav":""},on:{click:function(r){return r.preventDefault(),t.subNavBackButton.apply(null,arguments)}}},[t._v(" "+t._s(t.labels.backLabel)+" ")]),e("a",{staticClass:"cmp-header-nav__sub-nav-link-heading",attrs:{href:i.url,itemprop:"url"}},[e("span",{attrs:{itemprop:"name"},domProps:{innerHTML:t._s(i.name)}})])]):t._e(),e("div",{staticClass:"cmp-header-nav__sub-nav-shadow"},[e("div",{staticClass:"cmp-header-nav__sub-nav-container",on:{mouseenter:t.addNavTransitionDelay,mouseout:t.removeNavTransitionDelay}},[t.isMobile?t._e():e("a",{staticClass:"cmp-header-nav__sub-nav-link cmp-header-nav__sub-nav-link--first",attrs:{href:i.url,itemprop:"url"}},[e("span",{attrs:{itemprop:"name"},domProps:{innerHTML:t._s(i.name)}})]),e("div",{staticClass:"cmp-header-nav__sub-nav-wrapper"},[e("div",{staticClass:"cmp-header-nav__sub-nav-links-wrapper"},t._l(i.children,function(r,d){return e("div",{key:t._f("kebabCase")(`${r.name}-${d}`),staticClass:"cmp-header-nav__child-nav",attrs:{"data-sub-child-nav":r.name},on:{mouseover:t.expandSubNavItem,mouseleave:t.collapseSubNavItem}},[e("a",{class:["cmp-header-nav__sub-nav-link",t.hasChildren(r.children)?"cmp-header-nav__sub-nav-link--indicator":"",t.isActive(r.url)&&"cmp-header-nav__sub-nav-link--active"],attrs:{href:t.hasChildren(r.children)&&(t.isMobile||t.isTablet)?"javascript:void(0)":r.url,"data-toggle-child-nav":"true",itemprop:"url"},on:{click:t.subNavItemClickMobileHandler,mouseover:t.subNavItemActionHandler,mousedown:t.subNavItemActionHandler,mouseup:t.subNavItemActionHandler}},[e("span",{attrs:{itemprop:"name"},domProps:{innerHTML:t._s(r.name)}})]),e("div",{staticClass:"cmp-header-nav__sub-child-item cmp-header-nav__sub-child-item--less",attrs:{"data-sub-child-item":"","aria-expanded":"true"}},[e("simplebar",{staticClass:"cmp-header-nav__sub-child-item-container",attrs:{"data-simplebar-auto-hide":"false"}},[t.isMobile?e("div",[e("button",{staticClass:"cmp-header-nav__sub-child-nav-link cmp-header-nav__sub-child-nav-link--toggle",attrs:{type:"button","data-toggle-sub-child-nav":""},on:{click:function(u){return u.preventDefault(),t.subChildNavBackButton.apply(null,arguments)}}},[t._v(" "+t._s(t.labels.backLabel)+" ")]),e("a",{staticClass:"cmp-header-nav__sub-child-nav-link cmp-header-nav__sub-child-nav-link--mobile-header cmp-header-nav__sub-child-nav-link--selected",attrs:{href:r.url,itemprop:"url"}},[e("span",{staticClass:"cmp-title cmp-title--2 cmp-header-nav__sub-nav-link-heading",domProps:{innerHTML:t._s(r.name)}})])]):t._e(),t._l(r.children,function(u,m){return e("div",{key:t._f("kebabCase")(`${u.name}-${m}`),staticClass:"cmp-header-nav__sub-child-nav-item"},[e("a",{class:["cmp-header-nav__sub-child-nav-link",t.isActive(u.url)&&"cmp-header-nav__sub-child-nav-link--active"],attrs:{href:u.url,itemprop:"url","data-sub-child-nav-item":""}},[e("span",{attrs:{itemprop:"name"},domProps:{innerHTML:t._s(u.name)}})])])}),e("cmp-show-all-link",{attrs:{labels:t.labels,item:r,limit:t.THIRD_LEVEL_MENU_ITEM_MAX_LIMIT}})],2)],1)])}),0),t.shouldDisplayFeatureSpot(i.featureSpot)?e("div",{staticClass:"cmp-header-nav__spot"},[e("div",{staticClass:"cmp-header-nav__spot-divider"}),e("div",{staticClass:"cmp-header-nav__spot-content"},[e("elm-img",{staticClass:"elm-img",attrs:{alt:i.featureSpot.imageAltText,"fill-mode":"cover"}},[e("elm-dynamic-media",[e("div",{staticClass:"s7dm-dynamic-media",attrs:{id:t._f("kebabCase")(`${i.name}-${i.featureSpot.image.id}`),"data-asset-path":i.featureSpot.image.assetPath,"data-asset-name":i.featureSpot.image.assetName,"data-viewer-path":i.featureSpot.image.viewerPath,"data-imageserver":i.featureSpot.image.imageServer,"data-contenturl":i.featureSpot.image.contentUrl,"data-asset-type":i.featureSpot.image.assetType,"data-wcmdisabled":"","data-dms7":"","data-mode":"smartcrop","data-linktarget":"_self"}})])],1),e("h3",{staticClass:"cmp-header-nav__spot-headline"},[t._v(t._s(i.featureSpot.headline))]),e("p",{staticClass:"cmp-header-nav__spot-description"},[t._v(t._s(i.featureSpot.description))]),e("a",{staticClass:"elm-button elm-button--medium",attrs:{href:i.featureSpot.link}},[t._v(" "+t._s(i.featureSpot.linkText)+" ")])],1)]):t._e()])])])]):t._e(),!t.isAuthorMode&&!t.isMobile&&t.hasChildren(i.children)?e("div",{staticClass:"cmp-header-nav__overlay cmp-overlay cmp-overlay--full-screen cmp-overlay--transparent",attrs:{"data-overlay":""},on:{mouseover:t.overlayEventListener}}):t._e()])}),0)])},To=[],$o=l(So,ko,To,!1,null,null,null,null);const ye=$o.exports;function xo(s){return t=>t?s.includes(t):!0}const wo={data(){return{dragOrigin:{},dragPos:{},dragDelta:{},DRAGGED:Ds}},computed:{isDragged(){return Q(["x","y"])(this.dragPos)}},methods:{startDrag(s){const{clientX:t,clientY:e}=a.get(s,"touches[0]",s);this.dragOrigin={x:t,y:e}},drag(s){const{clientX:t,clientY:e}=a.get(s,"touches[0]",s);Q(["x","y"])(this.dragOrigin)&&(this.dragEvt=s,this.dragPos={x:t-this.dragOrigin.x,y:e-this.dragOrigin.y})},endDrag(){this.dragEvt=null,this.dragOrigin={},this.dragPos={}}}},Fs={mounted(){window.addEventListener("load",()=>{this.$nextTick().then(()=>{a.isFunction(this.windowLoad)&&this.windowLoad()})},{once:!0,useCapture:!1})}},{mapState:Lo}=p,Os="left",Eo="center",Io=[Eo,Os],Ce=20,Ao=.1,Se=2,Fo={name:"CmpHorizontalScroll",mixins:[_,wo,Fs,c],props:{align:{type:String,default:"left",validator:xo(Io)},controlOffset:{type:[Number,String],default:null},labels:{type:Object,default:()=>({})},resizeWatch:{type:[Number,String,Boolean,Object,Array],default:null},shiftFactor:{type:[Number,String],default:.85},useOffset:{type:Boolean,default:!1},useInertia:{type:Boolean,default:!0},useForcedResize:{type:Boolean,default:!1},contentPosInView:{type:Number,default:0}},data(){return{ACTIVE:$s,isActive:!1,offset:0,containerWidth:0,contentOrigin:0,contentWidth:0,contentPos:0,contentDelta:0,maxPos:null}},computed:{...Lo("base",["viewportSize","mainSize"]),controlStyles(){let s=this.controlOffset;return a.isNumber(s)?s=`${this.controlOffset}px`:s||(s="50%"),{top:s}},idlePos(){return this.align===Os?this.offset:(this.containerWidth-this.contentWidth)/2}},watch:{contentPosInView(s){this.contentPos=s},dragOrigin({x:s}){this.useInertia&&!s&&this.$nextTick().then(this.simulateInertia)},dragPos({x:s}){if(!s){this.contentOrigin=this.contentPos;return}this.dragEvt.cancelable&&(s<Ce*-1||s>Ce)&&this.dragEvt.preventDefault(),this.contentDelta=this.contentPos-this.contentOrigin-s,this.contentPos=this.getPos(this.contentOrigin+s)},viewportSize({width:s},{width:t}){(this.useForcedResize||s!==t)&&this.$nextTick().then(()=>{requestAnimationFrame(this.resetSize)})}},mounted(){this.resetSize(),this.$el.addEventListener("touchstart",this.startDrag,!1),this.$el.addEventListener("touchmove",this.drag,{passive:!1,useCapture:!1}),this.$el.addEventListener("touchend",this.endDrag,!1),this.resizeWatch!==null?this.$watch("resizeWatch",()=>{this.$nextTick().then(()=>{requestAnimationFrame(this.resetSize)})}):(this.resizeObserver=new ResizeObserver(()=>{requestAnimationFrame(this.resetSize)}),this.resizeObserver.observe(this.$refs.content))},beforeDestroy(){this.$el.removeEventListener("touchstart",this.startDrag,!1),this.$el.removeEventListener("touchmove",this.drag,!1),this.$el.removeEventListener("touchend",this.endDrag,!1),this.resizeObserver&&this.resizeObserver.disconnect()},methods:{windowLoad(){this.resetSize()},getPos(s){return s>this.offset?this.offset:this.scrollWidth+s<this.containerWidth?this.maxPos:s},getShiftFactor(){if(a.isNumber(this.shiftFactor))return this.shiftFactor;if(a.isString(this.shiftFactor)){const s=this.$el.querySelector(this.shiftFactor).offsetWidth;return s?s/this.containerWidth:1}return 1},jump(s){this.contentPos=this.getPos(this.contentPos+s*(this.containerWidth*this.getShiftFactor()))},resetSize(){if(!(this.$el.offsetParent===null||!this.$refs.content)){if(this.containerWidth=this.$el.offsetWidth,this.contentWidth=this.$refs.content.offsetWidth,this.scrollWidth=this.$refs.content.scrollWidth,this.maxPos=this.containerWidth-this.scrollWidth,this.useOffset){const s=(this.mainSize.width-this.containerWidth)/2;this.offset=s<0?s*-1:0}this.contentPos=this.offset,this.contentOrigin=this.contentPos,this.isActive=this.contentWidth>=this.containerWidth-this.offset}},simulateInertia(){const s=a.floor(this.contentDelta*Ao,Se);if(this.dragOrigin.x||s===0){this.contentOrigin=this.contentPos,this.contentDelta=0;return}this.contentDelta=a.floor(this.contentDelta-s,Se),this.contentPos=this.getPos(this.contentPos-this.contentDelta),requestAnimationFrame(this.simulateInertia)}}};var Oo=function(){var t=this,e=t._self._c;return e("div",{class:[t.isActive&&t.ACTIVE,(t.isDragged||t.contentDelta)&&t.DRAGGED]},[t.isActive?e("button",{staticClass:"cmp-horizontal-scroll__dir-button cmp-horizontal-scroll__dir-button--start",style:t.controlStyles,attrs:{disabled:t.contentPos===t.offset,type:"button"},on:{click:function(i){return t.jump(1)}}},[t._v(" "+t._s(t.labels.scrollLess)+" ")]):t._e(),e("div",{ref:"content",staticClass:"cmp-horizontal-scroll__content",style:{transform:`translate(${t.isActive?t.contentPos:t.idlePos}px, 0)`}},[t._t("default")],2),t.isActive?e("button",{staticClass:"cmp-horizontal-scroll__dir-button cmp-horizontal-scroll__dir-button--end",style:t.controlStyles,attrs:{disabled:t.contentPos===t.maxPos,type:"button"},on:{click:function(i){return t.jump(-1)}}},[t._v(" "+t._s(t.labels.scrollMore)+" ")]):t._e()])},Mo=[],Po=l(Fo,Oo,Mo,!1,null,null,null,null);const pt=Po.exports,Ro={name:"CmpCardCarousel",props:{cardSize:{type:String,default:"small"},labels:{type:Object,default:()=>({})}},data(){return{cardSizes:{large:{slideNumber:2,breakPoints:{767:{visibleSlides:1,slideMultiple:1}}},portrait:{slideNumber:3,breakPoints:{767:{visibleSlides:2,slideMultiple:2},480:{visibleSlides:1,slideMultiple:1}}},small:{slideNumber:4,breakPoints:{1179:{visibleSlides:3,slideMultiple:3},767:{visibleSlides:2,slideMultiple:2},480:{visibleSlides:1,slideMultiple:1}}}}}},computed:{cardBreakPoint(){return this.cardSizes[this.cardSize].breakPoints},slideNumber(){return this.cardSizes[this.cardSize].slideNumber}}};var No=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cmp-card-carousel"},[e("cmp-carousel",{attrs:{breakpoints:t.cardBreakPoint,"visible-slides":t.slideNumber,"slide-multiple":t.slideNumber,labels:t.labels}},[t._t("default")],2)],1)},Ho=[],Bo=l(Ro,No,Ho,!1,null,null,null,null);const ke=Bo.exports,Do={name:"CmpIframe",props:{title:{type:String,required:!0},url:{type:String,required:!0},width:{type:String,default:"100%"}},data(){return{height:0,isIframeLoading:!0}},beforeDestroy(){this.$refs.iframe.iFrameResizer.removeListeners()},methods:{iframeLoaded(){this.isIframeLoading=!1,ei({checkOrigin:!1,scrolling:!1},this.$refs.iframe),this.height=this.$refs.iframe.contentWindow.document.documentElement.scrollHeight}}};var qo=function(){var t=this,e=t._self._c;return e("div",[t.isIframeLoading?e("div",{staticClass:"cmp-iframe--loading"}):t._e(),e("iframe",{ref:"iframe",staticClass:"cmp-iframe",attrs:{src:t.url,title:t.title,height:`${t.height}px`,width:t.width},on:{load:t.iframeLoaded}})])},zo=[],Vo=l(Do,qo,zo,!1,null,null,null,null);const Te=Vo.exports,Uo={name:"CmpInstallersFinderButton",props:{variant:{type:String,default:""},isVueButtonEnabled:{type:Boolean,default:!1},label:{type:String,required:!0},labels:{type:Object,default:()=>({})}},data(){return{isLoading:!1,isErrorShown:!1}},methods:{async buttonClicked(){this.isLoading=!0,this.isErrorShown=!1;let s;try{s=await this.getGeoLocation(),window.open(`https://www.google.com/maps/search/${this.labels.mapsSearchTerm}/@${s.coords.latitude},${s.coords.longitude},9z`,"_blank")}catch{this.isErrorShown=!0}this.isLoading=!1},getGeoLocation(){return navigator.geolocation?new Promise((s,t)=>{navigator.geolocation.getCurrentPosition(s,t,{enableHighAccuracy:!0})}):Promise.reject()}}};var jo=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cmp-installers-finder"},[t.isVueButtonEnabled?e("elm-btn",{attrs:{variant:t.variant,"is-loading":t.isLoading,label:t.label},on:{click:t.buttonClicked}}):e("button",{class:["elm-button",t.variant,t.isLoading&&"is-loading"],on:{click:t.buttonClicked}},[e("span",{staticClass:"elm-button__text"},[t._v(t._s(t.label))])]),t.isErrorShown?e("div",{staticClass:"cmp-installers-finder__error-text"},[e("span",[t._v(t._s(t.labels.errorMessage))])]):t._e()],1)},Go=[],Wo=l(Uo,jo,Go,!1,null,null,null,null);const $e=Wo.exports,Qo={name:"CmpLocationCard",mixins:[_],props:{id:{type:String,required:!0},location:{type:Object,default:()=>({})},labels:{type:Object,default:()=>({})},isActive:{type:Boolean,default:!1},isOpen:{type:Boolean,default:!1},selectedMarker:{type:String,default:null},locationSelectHandler:{type:Function,default:a.noop},closeOtherCards:{type:Function,default:a.noop},enableSidePanelScroll:{type:Function,default:a.noop}},data(){return{isVisible:this.isOpen}},computed:{containerAttrs(){let s="auto",t=1;return this.isActive?(s=`${this.isVisible?"auto":"0"}`,t=this.isVisible?1:0):this.isVisible||(s="0px",t=0),this.isActive?{class:"cmp-location-card-item__container",style:{height:s,opacity:t}}:{}},contentAttrs(){return this.isActive?{"aria-hidden":!this.isVisible,class:"cmp-location-card-item__content",id:a.kebabCase(`${this.id}-content`),style:{display:this.isVisible?"block":"none"}}:{}},headerAttrs(){return this.isActive?{"aria-controls":a.kebabCase(`${this.id}-content`),"aria-expanded":this.isVisible?"true":"false",class:"cmp-location-card-item__header",type:"button"}:{}}},watch:{location(s){this.isVisible=s.isOpen},isOpen(s){this.isVisible=s},isVisible(s){s||this.$refs.container.parentElement.parentElement.removeEventListener("scroll",this.closeWhenOut)}},methods:{filteredLocationDetails(s){return[{type:"website",hrefType:"https://"},{type:"email",hrefType:"mailto:"},{type:"phone",hrefType:"tel:"},{type:"fax",hrefType:"fax:"},{type:"geocodeAddress",hrefType:"https://maps.google.com/?q="},{type:"products",hasContent:!0}].filter(e=>s[e.type]&&s[e.type].length>0)},toggle(){if(this.isActive){const s=!this.isVisible;this.closeOtherCards(this.location),s&&this.isMobile&&(this.$refs.container.scrollIntoView({behavior:"smooth",block:"end",inline:"center"}),this.$refs.container.parentElement.parentElement.addEventListener("scroll",this.closeWhenOut)),this.$nextTick().then(()=>{this.isVisible=s,this.forceLayout()}).then(()=>{this.enableSidePanelScroll()})}},closeWhenOut(s){const{offsetLeft:t}=this.$refs.container.parentElement,{scrollLeft:e}=s.target,{offsetWidth:i}=this.$refs.container;(e<t-window.outerWidth||t+i<e||e===0&&i<t)&&(this.isVisible=!1)},forceLayout(){this.$refs.container&&this.$refs.container.offsetHeight}}};var Ko=function(){var t=this,e=t._self._c;return e("div",[e(t.isActive?"button":"div",t._b({tag:"component",on:{click:t.toggle}},"component",t.headerAttrs,!1),[e("div",{class:`cmp-location-card-item__distance--${t.selectedMarker}`},[e("span",[t._v(t._s(t.location.distance))])]),e("h3",{staticClass:"cmp-location-card-item__heading",on:{click:function(i){return t.locationSelectHandler(t.location.position)}}},[t._v(" "+t._s(t.location.name)+" ")])]),e("div",t._b({ref:"container"},"div",t.containerAttrs,!1),[e("div",t._b({},"div",t.contentAttrs,!1),[e("ul",{staticClass:"cmp-location-card-item__info",attrs:{itemscope:"",itemtype:"http://schema.org/ItemList"}},t._l(t.filteredLocationDetails(t.location),function(i){return e("li",{key:i.type,attrs:{itemprop:"item",itemtype:"http://schema.org/ListItem"}},[e("div",{class:`cmp-location-card-item__list-item cmp-location-card-item__list-item-icon--${i.type}`},[i.hrefType?e("div",[i.type!="geocodeAddress"?e("span",[e("a",{staticClass:"elm-link",attrs:{href:i.hrefType+t.location[i.type],target:"_blank",itemprop:"url"},on:{click:function(n){return t.trackLocationEvent(t.index)}}},[t._v(" "+t._s(t.location[i.type])+" ")])]):e("span",{attrs:{itemscope:"",itemtype:"http://schema.org/PostalAddress"}},[e("span",{attrs:{itemprop:"streetAddress"}},[t._v(t._s(t.location.addressLine1))]),e("span",{staticClass:"cmp-where-to-buy-results__location-address"},[e("span",{attrs:{itemprop:"postalCode"}},[t._v(t._s(t.location.zipcode))]),e("span",{attrs:{itemprop:"addressLocality"}},[t._v(t._s(t.location.city))])]),e("span",{attrs:{itemprop:"addressRegion"}},[t._v(t._s(t.location.state))]),e("span",{attrs:{itemprop:"addressCountry"}},[t._v(t._s(t.location.country))]),e("a",{staticClass:"elm-link",attrs:{href:i.hrefType+t.location[i.type],target:"_blank",itemprop:"url"},on:{click:function(n){return t.trackLocationEvent(t.index)}}},[t._v(" "+t._s(t.labels.getDirections)+" ")])])]):e("div",[t._v(" "+t._s(t.location[i.type])+" ")])])])}),0)])])],1)},Yo=[],Zo=l(Qo,Ko,Yo,!1,null,null,null,null);const xe=Zo.exports,{mapState:Jo,mapMutations:Xo}=p,tl={name:"CmpNotificationBar",mixins:[c],props:{labels:{type:Object,default:()=>({})}},computed:{...Jo("base",["notification"]),message(){return a.get(this.notification,"message")},component(){return a.get(this.notification,"component")},componentProps(){return a.get(this.notification,"props")},type(){return a.get(this.notification,"type",qs)}},methods:{...Xo("base",["setNotification"]),dismiss(){this.setNotification(null)}}};var el=function(){var t=this,e=t._self._c;return t.notification?e("transition",{attrs:{"aria-live":"assertive",name:"tr-fade",role:"alert",tag:"div"}},[e("div",{class:["cmp-notification-bar",`cmp-notification-bar--${t.type}`]},[e("div",{staticClass:"cmp-notification-bar__text-container"},[t.component?e(t.component,t._b({tag:"component",staticClass:"cmp-notification-bar__component",attrs:{"toggle-handler":t.dismiss}},"component",t.componentProps,!1)):e("p",{staticClass:"cmp-notification-bar__text"},[t._v(t._s(t.message))])],1),e("div",{staticClass:"cmp-notification-bar__button-container"},[e("button",{staticClass:"cmp-notification-bar__button elm-square-button elm-square-button--icon-close_outline",attrs:{type:"button"},on:{click:t.dismiss}},[e("span",{staticClass:"elm-square-button__text"},[t._v(t._s(t.labels.dismiss))])])])])]):t._e()},sl=[],il=l(tl,el,sl,!1,null,null,null,null);const we=il.exports,{mapMutations:al}=p,nl={name:"CmpOverlayButton",mixins:[c],props:{component:{type:String,required:!0},componentProps:{type:Object,default:()=>({})},fullScreen:{type:Boolean,default:!1},heading:{type:String,default:null},label:{type:String,required:!0},hideLabel:{type:Boolean,default:!1},showToggle:{type:Boolean,default:!0},textClass:{type:String,default:null},useSlideIn:{type:Boolean,default:!1},closeHandler:{type:Function,default:a.noop},toggleHandler:{type:Function,default:a.noop},variant:{type:String,default:""},size:{type:String,default:""},icon:{type:String,default:""},isVueButtonEnabled:{type:Boolean,default:!1}},data(){return{defaultTextClass:null}},mounted(){this.defaultTextClass=a.trim([`${a.first(Array.from(this.$el.classList))}__text`,this.hideLabel?"h-hidden":""].join(" "))},methods:{...al("base",["setOverlayComponent"]),setOverlay(s){this.setOverlayComponent({component:this.component,fullScreen:this.fullScreen,heading:this.heading,props:JSON.parse(JSON.stringify(this.componentProps)),origin:{target:s.target,x:s.clientX,y:s.clientY},showToggle:this.showToggle,useSlideIn:this.useSlideIn,closeHandler:this.closeHandler,defaultSlot:this.$slots.default}),this.toggleHandler()}}};var rl=function(){var t=this,e=t._self._c;return t.isVueButtonEnabled?e("elm-btn",{attrs:{label:t.label,variant:t.variant,size:t.size,icon:t.icon,"aria-controls":"overlay",type:"button"},on:{click:t.setOverlay}}):e("button",{attrs:{"aria-controls":"overlay",type:"button"},on:{click:t.setOverlay}},[e("span",{class:t.textClass||t.defaultTextClass},[t._v(t._s(t.label))])])},ol=[],ll=l(nl,rl,ol,!1,null,null,null,null);const Le=ll.exports,cl={name:"CmpOverlayCountrySelector",mixins:[c],props:{labels:{type:Object,default:()=>({})},links:{type:Object,default:()=>({})},linkClickedHandler:{type:Function,default:()=>{}}},data(){return{corporateScreen:"/etc.clientlibs/settings/wcm/design/aembase/head/resources/img/grundfos/corporate-screen.png"}}};var dl=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cmp-overlay-country-selector"},[e("div",{staticClass:"cmp-overlay-country-selector__content cmp-overlay-country-selector__content--white"},[e("div",{staticClass:"cmp-overlay-country-selector__section"},[e("h2",{staticClass:"cmp-overlay-country-selector-section__headline"},[t._v(t._s(t.labels.siteSpecificHeadline))]),e("div",{staticClass:"cmp-overlay-country-selector-section__subheading"},[t._v(t._s(t.labels.siteSpecificSubHeading))]),t._m(0),e("p",{staticClass:"cmp-overlay-country-selector-section__text"},[t._v(t._s(t.labels.siteSpecific))]),e("div",{staticClass:"cmp-overlay-country-selector-section__action"},[e("a",{staticClass:"elm-button",attrs:{href:t.links.siteSpecific,type:"button"},on:{click:function(i){return t.linkClickedHandler()}}},[t._v(" "+t._s(t.labels.explore)+" ")])])])]),e("div",{staticClass:"cmp-overlay-country-selector__content cmp-overlay-country-selector__content--blue"},[e("div",{staticClass:"cmp-overlay-country-selector__section"},[e("h2",{staticClass:"cmp-overlay-country-selector-section__headline"},[t._v(t._s(t.labels.corporate))]),e("div",{staticClass:"cmp-overlay-country-selector-section__subheading"},[t._v(t._s(t.labels.corporateSubHeading))]),e("div",{staticClass:"cmp-overlay-country-selector-section__coporate-screen"},[e("img",{attrs:{src:t.corporateScreen,alt:"coporate screen"}})]),e("p",{staticClass:"cmp-overlay-country-selector-section__text"},[t._v(" "+t._s(t.labels.corporateText)+" ")]),e("div",{staticClass:"cmp-overlay-country-selector-section__action"},[e("a",{staticClass:"elm-button elm-button--light",attrs:{href:t.links.corporate,type:"button"},on:{click:function(i){return t.linkClickedHandler()}}},[t._v(" "+t._s(t.labels.continue)+" ")])])])])])},ul=[function(){var s=this,t=s._self._c;return t("div",{staticClass:"cmp-overlay-country-selector-section__icons"},[t("div",{staticClass:"cmp-overlay-country-selector-section__icon cmp-overlay-country-selector-section__icon--user"}),t("div",{staticClass:"cmp-overlay-country-selector-section__icon cmp-overlay-country-selector-section__icon--pump"}),t("div",{staticClass:"cmp-overlay-country-selector-section__icon cmp-overlay-country-selector-section__icon--training"})])}],pl=l(cl,dl,ul,!1,null,null,null,null);const Ee=pl.exports,ml={name:"CmpOverlayHTML",mixins:[c],props:{html:{type:String,required:!0}}};var hl=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cmp-overlay-html",domProps:{innerHTML:t._s(t.html)}})},_l=[],fl=l(ml,hl,_l,!1,null,null,null,null);const Ie=fl.exports,gl="fit-to-window",bl="full-size",vl="custom-width",yl=new Set([gl,bl,vl]),Cl={name:"CmpOverlayExperienceFragment",mixins:[c,_],props:{width:{type:String,default:""},overlaySize:{type:String,required:!0,validator:s=>yl.has(s)},toggleHandler:{type:Function,default:a.noop}},computed:{frameWidth(){return!this.isMobile&&this.overlaySize==="custom-width"&&this.width?`${this.width}px`:"100%"}}};var Sl=function(){var t=this,e=t._self._c;return e("div",{class:["cmp-overlay-experience-fragment",`cmp-overlay-experience-fragment--${t.overlaySize}`]},[e("div",{staticClass:"cmp-overlay-experience-fragment__container"},[t._t("default")],2)])},kl=[],Tl=l(Cl,Sl,kl,!1,null,null,null,null);const Ae=Tl.exports,{mapState:$l}=p,xl="news",wl={name:"CmpPageRepositoryResult",mixins:[c],props:{desc:{type:String,required:!0},imageSrc:{type:String,required:!0},contentType:{type:String,required:!0},pageType:{type:String,required:!0},title:{type:String,required:!0},publishedDate:{type:String,required:!0},canonicalUrl:{type:String,required:!0},showDate:{type:Boolean,default:!1}},computed:{...$l("base",["useListView"]),tag(){return this.contentType!==""?this.getContentType():this.pageType!==""?this.getPageType():""},publisherUrl(){return`${window.location.protocol}//${window.location.host}`}},methods:{showDateInfo(){return this.showDate||this.pageType===xl},getContentType(){const s=this.contentType.toLowerCase();let t=this.contentType;return s==="ecademy-learning-track"?t=this.labels.learningTrack:s==="ecademy-course"?t=this.labels.course:s==="ecademy-audiotrack"&&(t=this.labels.audiotrack),t},getPageType(){const s=this.pageType.toLowerCase();let t=this.pageType;return s==="pagetype.ecademy-course"?t=this.labels.course:s==="pagetype.ecademy-learning-track"||s==="ecademy-learning-track"?t=this.labels.learningTrack:s==="ecademy-course"?t=this.labels.course:s==="ecademy-audiotrack"||s==="pagetype.ecademy-audiotrack"?t=this.labels.audiotrack:s==="classroom-training"&&(t=this.labels.classroomTraining),t}}};var Ll=function(){var t=this,e=t._self._c;return e("div",{class:["cmp-content-card",{"cmp-content-card--portrait cmp-content-card--portrait-compressed":!t.useListView,"cmp-content-card--landscape":t.useListView}],attrs:{itemprop:"item",itemscope:"",itemtype:"https://schema.org/Article"}},[e("a",{staticClass:"cmp-content-card__link",attrs:{href:t.canonicalUrl,title:t.title,itemprop:"mainEntityOfPage"}},[e("div",{staticClass:"cmp-content-card__content"},[e("div",{staticClass:"cmp-content-card__image"},[e("elm-img",{staticClass:"elm-img",attrs:{"use-dpr":!1,src:{src:t.imageSrc,width:400,height:300},srcset:[{src:t.imageSrc,width:200,height:266},{src:t.imageSrc,width:400,height:300}],"fill-mode":"cover"}})],1)]),e("div",{staticClass:"cmp-content-card__meta"},[e("ul",{staticClass:"cmp-content-card__tag-list cmp-tag-list"},[e("li",{staticClass:"cmp-tag-list__item"},[e("p",{staticClass:"elm-tag"},[t._v(" "+t._s(t.tag)+" ")])])]),e("div",{staticClass:"cmp-content-card__info"},[e("div",{staticClass:"cmp-content-card__text"},[e("h3",{staticClass:"cmp-content-card__heading cmp-content-card__heading--small",attrs:{itemprop:"name headline"}},[t._v(" "+t._s(t.title)+" ")]),e("p",{directives:[{name:"truncate",rawName:"v-truncate:80",value:{text:t.desc,isExcessWord:!0},expression:"{ text: desc, isExcessWord: true }",arg:"80"}],staticClass:"cmp-content-card__description",attrs:{itemprop:"description"}}),t.showDateInfo()?e("p",{staticClass:"cmp-content-card__date"},[t._v(" "+t._s(t.publishedDate)+" ")]):t._e()])])])]),t._m(0),e("meta",{attrs:{content:t.imageSrc,itemprop:"image"}}),e("meta",{attrs:{content:t.publishedDate,itemprop:"datePublished"}}),e("meta",{attrs:{content:t.publishedDate,itemprop:"dateModified"}}),e("meta",{attrs:{content:t.canonicalUrl,itemprop:"url"}}),e("div",{attrs:{itemprop:"publisher",itemscope:"",itemtype:"https://schema.org/Organization"}},[e("meta",{attrs:{itemprop:"name",content:"GRUNDFOS"}}),e("meta",{attrs:{content:t.publisherUrl,itemprop:"url"}}),t._m(1)])])},El=[function(){var s=this,t=s._self._c;return t("div",{attrs:{itemprop:"author",itemscope:"",itemtype:"https://schema.org/Organization"}},[t("meta",{attrs:{itemprop:"name",content:"GRUNDFOS"}})])},function(){var s=this,t=s._self._c;return t("div",{attrs:{itemprop:"logo",itemscope:"",itemtype:"https://schema.org/ImageObject"}},[t("meta",{attrs:{itemprop:"url",content:"/etc.clientlibs/settings/wcm/design/aembase/head/resources/img/grundfos/logo.png"}})])}],Il=l(wl,Ll,El,!1,null,null,null,null);const Al=Il.exports,{mapState:Fl}=p,Ol={name:"CmpPageRepositoryResultList",components:{CmpPageRepositoryResult:Al},mixins:[_,P,c],props:{id:{type:String,required:!0},items:{type:Array,default:()=>[]},labels:{type:Object,default:()=>({})}},computed:{...Fl("base",["useListView"])}};var Ml=function(){var t=this,e=t._self._c;return e("section",{staticClass:"cmp-result-list",attrs:{id:t.id}},[e("div",{staticClass:"cmp-result-list__offset-actions"},[e("elm-list-view-selector",{directives:[{name:"show",rawName:"v-show",value:!t.isMobile,expression:"!isMobile"}],attrs:{id:t._f("kebabCase")(`${t.id}-list-selector`),labels:t.labels}})],1),e("div",{staticClass:"cmp-result-list__body cmp-result-list__body--transparent b-layout-grid b-layout-grid--spaced"},[e("div",{class:["b-layout-grid__group",t.useListView?"":"b-layout-grid-group--spaced"],attrs:{itemscope:"",itemtype:"https://schema.org/ItemList"}},t._l(t.items,function(i,n){return e("div",{key:t.generateHash(i),class:["b-layout-grid__item b-layout-grid__item--12",!t.useListView&&"b-layout-grid__item--large-4"],attrs:{itemprop:"itemListElement",itemscope:"",itemtype:"https://schema.org/ListItem"}},[e("cmp-page-repository-result",t._b({staticClass:"cmp-page-repository-result"},"cmp-page-repository-result",i,!1)),e("meta",{attrs:{content:n,itemprop:"position"}})],1)}),0)])])},Pl=[],Rl=l(Ol,Ml,Pl,!1,null,null,null,null);const Fe=Rl.exports,{mapState:Nl}=p,Hl="news",Bl={name:"CmpPageRepositoryHorizontalResult",mixins:[c],props:{desc:{type:String,required:!0},imageSrc:{type:String,required:!0},contentType:{type:String,required:!0},pageType:{type:String,required:!0},title:{type:String,required:!0},publishedDate:{type:String,required:!0},canonicalUrl:{type:String,required:!0},showDate:{type:Boolean,default:!1}},computed:{...Nl("base",["useListView"]),tag(){return this.contentType!==""?this.contentType:this.pageType},publisherUrl(){return`${window.location.protocol}//${window.location.host}`}},methods:{showDateInfo(){return this.showDate||this.pageType===Hl}}};var Dl=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cmp-content-card cmp-content-card--portrait cmp-content-card--portrait-normal"},[e("a",{staticClass:"cmp-content-card__link",attrs:{href:t.canonicalUrl,title:t.title}},[e("div",{staticClass:"cmp-content-card__content"},[e("div",{staticClass:"cmp-content-card__image"},[e("elm-img",{staticClass:"elm-img",attrs:{"use-dpr":!1,src:{src:t.imageSrc,width:400,height:300},srcset:[{src:t.imageSrc,width:200,height:266},{src:t.imageSrc,width:400,height:300}],"fill-mode":"cover"}})],1)]),e("div",{staticClass:"cmp-content-card__meta"},[e("ul",{staticClass:"cmp-content-card__tag-list cmp-tag-list"},[e("li",{staticClass:"cmp-tag-list__item"},[e("p",{staticClass:"elm-tag"},[t._v(" "+t._s(t.tag)+" ")])])]),e("div",{staticClass:"cmp-content-card__info"},[e("div",{staticClass:"cmp-content-card__text"},[e("h3",{staticClass:"cmp-content-card__heading",attrs:{itemprop:"name headline"}},[t._v(" "+t._s(t.title)+" ")]),e("p",{directives:[{name:"truncate",rawName:"v-truncate:80",value:{text:t.desc,isExcessWord:!0},expression:"{ text: desc, isExcessWord: true }",arg:"80"}],staticClass:"cmp-content-card__description",attrs:{itemprop:"description"}}),t.showDateInfo()?e("p",{staticClass:"cmp-content-card__date"},[t._v(" "+t._s(t.publishedDate)+" ")]):t._e()])])])])])},ql=[],zl=l(Bl,Dl,ql,!1,null,null,null,null);const Vl=zl.exports,{mapState:Ul}=p,jl={name:"CmpPageRepositoryHorizontalResultList",components:{CmpPageRepositoryHorizontalResult:Vl},mixins:[_,P,c],props:{id:{type:String,required:!0},items:{type:Array,default:()=>[]},labels:{type:Object,default:()=>({})}},computed:{...Ul("base",["useListView"])}};var Gl=function(){var t=this,e=t._self._c;return e("section",{staticClass:"b-deck b-deck--full-width cmp-cards-highlight-deck",attrs:{id:t.id}},[e("div",{staticClass:"cmp-cards-highlight-deck__body"},[e("cmp-horizontal-scroll",{staticClass:"cmp-horizontal-scroll cmp-horizontal-scroll--large cmp-cards-highlight-deck__list",attrs:{labels:{scrollLess:t.labels.scrollLess,scrollMore:t.labels.scrollMore},"shift-factor":".cmp-cards-highlight-deck__list-item","use-forced-resize":"","use-offset":""}},t._l(t.items,function(i){return e("div",{key:t.generateHash(i),staticClass:"cmp-cards-highlight-deck__list-item"},[e("cmp-page-repository-horizontal-result",t._b({},"cmp-page-repository-horizontal-result",i,!1))],1)}),0)],1)])},Wl=[],Ql=l(jl,Gl,Wl,!1,null,null,null,null);const Oe=Ql.exports,Kl=5,Yl=7,Zl={name:"CmpPagination",mixins:[_,c],props:{controlsId:{type:String,required:!0},currentPage:{type:Number,default:1},labels:{type:Object,default:()=>({})},maxPage:{type:Number,default:1},pageUrl:{type:String,default:null},updateHandler:{type:Function,default:a.noop}},computed:{pages(){if(this.maxPage<=this.visiblePageCount)return a.range(this.maxPage).map((n,r)=>r+1);const s=this.visiblePageCount-2,t=Math.floor(s/2);let e;this.currentPage-t>1?this.currentPage+t>=this.maxPage?e=this.maxPage-s:e=this.currentPage-t:e=2;const i=a.range(s).map((n,r)=>e+r);return[1].concat(i).concat(this.maxPage)},truncStart(){return this.pages[1]>2},truncEnd(){return this.pages[this.pages.length-2]+1<this.pages[this.pages.length-1]},visiblePageCount(){return this.isMobile?Kl:Yl}},methods:{getButtonComponent(s){return this.pageUrl?s?"span":"a":"button"},getButtonAttrs(s,t){const e=this.getButtonComponent(t)==="button";return t?{class:this.pageUrl&&"elm-link--disabled",disabled:e}:{"aria-controls":this.controlsId,href:!e&&M(this.pageUrl,{pageNumber:s}),type:e&&"button"}}}};var Jl=function(){var t=this,e=t._self._c;return e("nav",{attrs:{"aria-label":"pagination navigation"}},[e(t.currentPage===1?"span":"a",t._b({tag:"component",staticClass:"elm-link elm-link--block cmp-pagination__button cmp-pagination__button--previous",on:{click:function(i){i.preventDefault(),t.currentPage!==1&&t.updateHandler(t.currentPage-1)}}},"component",t.getButtonAttrs(t.currentPage-1,t.currentPage===1),!1),[e("span",{staticClass:"cmp-pagination__button-text"},[t._v(" "+t._s(t.labels.previous)+" ")])]),e("ol",{class:["cmp-pagination__page-list",{"cmp-pagination__page-list--trunc-start":t.truncStart,"cmp-pagination__page-list--trunc-end":t.truncEnd}]},t._l(t.pages,function(i){return e("li",{key:i,staticClass:"cmp-pagination__page-list-item"},[e(t.getButtonComponent(i===t.currentPage),t._b({tag:"component",staticClass:"elm-link elm-link--block cmp-pagination__page-list-button",on:{click:function(n){n.preventDefault(),i!==t.currentPage&&t.updateHandler(i)}}},"component",t.getButtonAttrs(i,i===t.currentPage),!1),[t._v(" "+t._s(i)+" ")])],1)}),0),e(t.getButtonComponent(t.currentPage===t.maxPage),t._b({tag:"component",staticClass:"elm-link elm-link--block cmp-pagination__button cmp-pagination__button--next",on:{click:function(i){i.preventDefault(),t.currentPage!==t.maxPage&&t.updateHandler(t.currentPage+1)}}},"component",t.getButtonAttrs(t.currentPage+1,t.currentPage===t.maxPage),!1),[e("span",{staticClass:"cmp-pagination__button-text"},[t._v(" "+t._s(t.labels.next)+" ")])])],1)},Xl=[],tc=l(Zl,Jl,Xl,!1,null,null,null,null);const Me=tc.exports,{mapMutations:ec}=p,sc="Expand element",ic=4,ac={name:"CmpReveal",mixins:[bt,v,c],props:{id:{type:String,required:!0},trackingId:{type:String,default:null},labels:{type:Object,default:()=>({})},tooltipContent:{type:String,default:null}},data(){return{inTransition:!1,isVisible:!1}},computed:{containerAttrs(){return this.isActive?{class:"cmp-reveal__container",style:{height:this.height,opacity:this.opacity}}:{}},height(){let s="auto";return this.isEditMode||(this.inTransition?s=`${this.contentHeight}px`:this.isVisible||(s="0px")),s},opacity(){let s=1;return!this.isEditMode&&!this.isVisible&&(s=0),s},contentAttrs(){return this.isActive?{"aria-hidden":this.hidden,class:"cmp-reveal__content",id:this.componentId,style:{display:this.display}}:{}},headerAttrs(){return this.isActive?{"aria-controls":this.componentId,"aria-expanded":this.expanded,class:"cmp-reveal__toggle",type:"button"}:{}},componentId(){return`${this.baseId}-${this.randomId}`},baseId(){return a.kebabCase(`${this.id}-content`)},toggleLabel(){return this.isEditMode||this.isVisible?this.labels.hide:this.labels.show},showTooltip(){return!this.isVisible&&this.tooltipContent},contentHeight(){return this.isVisible?this.$refs.content.offsetHeight:0},randomId(){return vt(ic).toString("hex")},expanded(){return this.isEditMode||this.isVisible?"true":"false"},display(){return this.isEditMode||this.inTransition||this.isVisible?"block":"none"},hidden(){return this.isEditMode?!1:!this.isVisible}},mounted(){this.$refs.container.addEventListener("transitionend",this.endTransition,!1)},beforeDestroy(){this.$refs.container.removeEventListener("transitionend",this.endTransition,!1)},methods:{...ec("base",["resetViewportSize"]),endTransition(s){s.target===this.$refs.container&&(this.inTransition=!1,this.isVisible&&this.resetViewportSize())},toggle(){!this.isEditMode&&this.isActive&&(this.inTransition=!0,this.$nextTick().then(()=>{this.isVisible=!this.isVisible,this.forceBrowserScrollPosition(),this.isVisible&&f({page:{elementName:this.trackingId?this.trackingId:this.id}},sc)}))},forceBrowserScrollPosition(){if(this.$refs.container&&!this.isVisible){const i=this.$refs.container.getBoundingClientRect().top+window.scrollY-window.innerHeight/2;window.scrollTo(0,i)}}}};var nc=function(){var t=this,e=t._self._c;return e("div",[e("div",t._b({ref:"container"},"div",t.containerAttrs,!1),[e("div",t._b({ref:"content"},"div",t.contentAttrs,!1),[t._t("default")],2)]),t.isActive?e("button",{staticClass:"cmp-reveal__toggle elm-link",attrs:{"aria-controls":t.componentId,"aria-expanded":t.expanded,type:"button"},on:{click:function(i){return i.preventDefault(),t.toggle.apply(null,arguments)}}},[t.showTooltip?[e("span",{directives:[{name:"tooltip",rawName:"v-tooltip:right",value:t.tooltipContent,expression:"tooltipContent",arg:"right"}]},[t._v(t._s(t.labels.show))])]:[t._v(" "+t._s(t.toggleLabel)+" ")]],2):t._e()])},rc=[],oc=l(ac,nc,rc,!1,null,null,null,null);const Pe=oc.exports,lc="generic",cc={name:"CmpSearchResultCard",mixins:[c],props:{alert:{type:String,required:!0},contentType:{type:String,default:null},desc:{type:String,required:!0},discontinued:{type:String,default:"false"},imageSrc:{type:String,default:null},labels:{type:Object,default:()=>({})},pageType:{type:String,required:!0},productNumber:{type:String,default:null},title:{type:String,default:""},url:{type:String,required:!0},isVariant:{type:Boolean,default:!0},variantProperties:{type:Object,default:()=>({})}},data(){return{GENERIC:lc}},computed:{productTitle(){return this.title.concat(this.productNumber?` - ${this.productNumber}`:"")},isDiscontinued(){return this.discontinued.toLowerCase()==="true"},tag(){return this.contentType||this.pageType},variantProps(){if(!this.variantProperties)return{};const s=2,t={},e=Object.values({...this.variantProperties});return e.forEach((i,n)=>{n%s===1&&i!==""&&(t[e[n-1]]=i)}),t}}};var dc=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cmp-search-result-card"},[e("div",{staticClass:"cmp-search-result-card__info"},[t.tag?e("ul",{staticClass:"cmp-tag-list"},[e("li",{staticClass:"cmp-tag-list__item"},[e("p",{staticClass:"elm-tag"},[t._v(t._s(t.tag))])])]):t._e(),e("div",{staticClass:"cmp-search-result-card__text-container"},[e("a",{staticClass:"cmp-search-result-card__link track-search-results",attrs:{href:t.url,title:t.title}},[e("h3",{staticClass:"cmp-search-result-card__heading cmp-search-result-card__heading--inline",domProps:{innerHTML:t._s(t.productTitle)}}),t.isDiscontinued?e("span",{staticClass:"cmp-alert cmp-search-result-card__alert"},[t._v(" "+t._s(t.alert)+" ")]):t._e(),t.isVariant?e("div",{staticClass:"cmp-search-result-card__data"},t._l(t.variantProps,function(i,n){return i?e("div",{key:n,staticClass:"cmp-search-result-card__data-list"},[e("span",{staticClass:"cmp-search-result-card__data-term"},[t._v(t._s(n))]),e("span",{staticClass:"cmp-search-result-card__data-description"},[t._v(t._s(i))])]):t._e()}),0):e("p",{staticClass:"cmp-search-result-card__description"},[t._v(t._s(t._f("truncate")(t.desc,160)))])])])]),t.imageSrc?e("a",{staticClass:"cmp-search-result-card__image track-search-results",attrs:{href:t.url,title:t.title}},[e("elm-img",{staticClass:"elm-img elm-img--fill",attrs:{src:t.imageSrc,"fill-mode":t.pageType===t.GENERIC?"cover":"contain"}})],1):t._e()])},uc=[],pc=l(cc,dc,uc,!1,null,null,null,null);const mc=pc.exports,hc="generic",_c={name:"CmpNewSearchResultCard",mixins:[c],props:{alert:{type:String,required:!0},contentType:{type:String,default:null},desc:{type:String,required:!1,default:null},discontinued:{type:Boolean,default:!1},imageSrc:{type:String,default:null},labels:{type:Object,default:()=>({})},pageType:{type:String,required:!0},productName:{type:String,default:null},productNumber:{type:String,default:null},productNumberLabel:{type:String,default:null},title:{type:String,default:""},url:{type:String,required:!0},isVariant:{type:Boolean,default:!0},variantProperties:{type:Object,default:()=>({})}},data(){return{GENERIC:hc}},computed:{productTitle(){return this.title.concat(this.productNumber?` - ${this.productNumber}`:"")},tag(){return this.contentType||this.pageType},variantProps(){if(!this.variantProperties)return{};const s=2,t={},e=Object.values({...this.variantProperties});return e.forEach((i,n)=>{n%s===1&&i!==""&&(t[e[n-1]]=i)}),t}},methods:{navigationHandler(s){const e=s.target.closest("a").href;this.$emit("navigation-handler",e)}}};var fc=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cmp-search-result-card",attrs:{"data-qa":"search-result-card"}},[e("div",{staticClass:"cmp-search-result-card__info"},[t.tag?e("ul",{staticClass:"cmp-tag-list"},[e("li",{staticClass:"cmp-tag-list__item"},[e("div",{staticClass:"elm-tag"},[t._v(t._s(t.tag))])])]):t._e(),e("div",{staticClass:"cmp-search-result-card__text-container"},[e("a",{staticClass:"cmp-search-result-card__link track-search-results",attrs:{href:t.url,title:t.title},on:{click:function(i){return i.preventDefault(),t.navigationHandler.apply(null,arguments)}}},[t.isVariant?t._e():e("h3",{staticClass:"cmp-search-result-card__heading cmp-search-result-card__heading--inline",domProps:{innerHTML:t._s(t.productTitle)}}),t.isVariant?e("div",[e("h3",{staticClass:"cmp-search-result-card__heading cmp-search-result-card__heading--inline"},[t._v(" "+t._s(t.productName)+" ")]),e("div",{staticClass:"cmp-search-result-card__sub-heading cmp-search-result-card__data"},[e("div",{staticClass:"cmp-search-result-card__data-list"},[e("span",{staticClass:"cmp-search-result-card__data-term"},[t._v(" "+t._s(t.productNumberLabel)+" ")]),e("span",{staticClass:"cmp-search-result-card__data-description"},[t._v(" "+t._s(t.productNumber)+" ")])])])]):t._e(),t.discontinued?e("span",{staticClass:"cmp-alert cmp-search-result-card__alert"},[t._v(" "+t._s(t.alert)+" ")]):t._e(),t.isVariant?e("div",{staticClass:"cmp-search-result-card__data"},t._l(t.variantProps,function(i,n){return i?e("div",{key:n,staticClass:"cmp-search-result-card__data-list"},[e("span",{staticClass:"cmp-search-result-card__data-term"},[t._v(t._s(n))]),e("span",{staticClass:"cmp-search-result-card__data-description"},[t._v(t._s(i))])]):t._e()}),0):e("p",{staticClass:"cmp-search-result-card__description"},[t._v(t._s(t._f("truncate")(t.desc,160)))])])])]),t.imageSrc?e("a",{staticClass:"cmp-search-result-card__image track-search-results",attrs:{href:t.url,title:t.title,"data-qa":"search-result-card-image"},on:{click:function(i){return i.preventDefault(),t.navigationHandler.apply(null,arguments)}}},[e("elm-img",{staticClass:"elm-img elm-img--fill",attrs:{src:t.imageSrc,"fill-mode":t.pageType===t.GENERIC?"cover":"contain"}})],1):t._e()])},gc=[],bc=l(_c,fc,gc,!1,null,null,null,null);const vc=bc.exports,yc={name:"CmpSearchUniformResultList",components:{CmpSearchResultCard:mc,CmpNewSearchResultCard:vc},mixins:[P,c],props:{items:{type:Array,default:()=>[]},labels:{type:Object,default:()=>({})},isElasticSearch:{type:Boolean,default:!1}}};var Cc=function(){var t=this,e=t._self._c;return e("section",{staticClass:"cmp-result-list"},[e("div",{staticClass:"cmp-result-list__body cmp-result-list__body--transparent b-layout-grid b-layout-grid--spaced"},[e("div",{staticClass:"b-layout-grid__group"},t._l(t.items,function(i,n){return e("div",{key:t.generateHash(i),staticClass:"b-layout-grid__item b-layout-grid__item--12"},[t.isElasticSearch?e("div",[e("cmp-new-search-result-card",t._b({attrs:{alert:t.labels.discontinued,"product-number-label":t.labels.productNo},on:{"navigation-handler":r=>t.$emit("navigation-handler",r,n+1)}},"cmp-new-search-result-card",i,!1))],1):e("div",[e("cmp-search-result-card",t._b({attrs:{alert:t.labels.discontinued}},"cmp-search-result-card",i,!1))],1)])}),0)])])},Sc=[],kc=l(yc,Cc,Sc,!1,null,null,null,null);const Re=kc.exports,Tc={name:"CmpSizingText",mixins:[c],props:{value:{type:String,required:!0}}};var $c=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cmp-sizing-basic__field--align-bottom cmp-sizing-basic__field--33w"},[e("p",{staticClass:"cmp-sizing-basic__text"},[t._v(" "+t._s(t.value)+" ")])])},xc=[],wc=l(Tc,$c,xc,!1,null,null,null,null);const Lc=wc.exports,Ec={name:"CmpSizingNumberUnit",mixins:[b,c],props:{max:{type:Number,default:null},min:{type:Number,default:null},placeholder:{type:String,default:null},step:{type:[Number,String],default:"any"},unitOptions:{type:Array,default:()=>[]},unitValue:{type:String,default:null}},methods:{update(s){a.isFunction(this.updateHandler)&&this.updateHandler(s)}}};var Ic=function(){var t=this,e=t._self._c;return e("div",{class:["cmp-form-text",t.modifier&&`cmp-form-text--${t.modifier}`,t.hasError&&"cmp-form-text--error"]},[e("label",{staticClass:"cmp-form-text__label",attrs:{for:t.id}},[t._v(" "+t._s(t.label)+" ")]),e("div",{staticClass:"cmp-form-text__group"},[e("input",{staticClass:"cmp-form-text__text",attrs:{"aria-invalid":t.hasError,"data-unit":t.unitValue,disabled:t.disabled,id:t.id,max:t.max,min:t.min,name:t.id,placeholder:t.placeholder,step:t.step,inputmode:"decimal",type:"number"},domProps:{value:t.value},on:{blur:t.update}}),t.unitOptions&&t.unitOptions.length>1?e("div",{class:["cmp-form-options","cmp-form-options--drop-down",t.modifier&&`cmp-form-options--${t.modifier}`,"cmp-form-text__unit"]},[e("select",{staticClass:"cmp-form-options__field cmp-form-options__field--drop-down",attrs:{"aria-label":t.id,"data-value":t.value||"",disabled:t.disabled,id:t._f("kebabCase")(`${t.id}-unit`),name:t._f("kebabCase")(`${t.id}-unit`)},on:{change:t.update}},t._l(t.unitOptions,function(i){return e("option",{key:i.key,domProps:{selected:t.unitValue===i.key,value:i.key}},[t._v(" "+t._s(i.value)+" ")])}),0)]):t.unitOptions[0].value?e("span",{staticClass:"cmp-form-text__postfix"},[t._v(" "+t._s(t.unitOptions[0].value)+" ")]):t._e()]),t.hasError?e("p",{staticClass:"cmp-form-text__error-block",attrs:{role:"alert"}},[t._v(" "+t._s(t.errorMessage)+" ")]):t._e()])},Ac=[],Fc=l(Ec,Ic,Ac,!1,null,null,null,null);const Oc=Fc.exports,Mc={name:"CmpSizingCheckbox",mixins:[b,c],props:{value:{type:[Number,String],default:null}},computed:{isChecked(){return typeof this.value=="string"?this.value==="true"?!0:this.value==="false"?!1:!!this.value:this.value}},methods:{emitInput(s){s.target.setAttribute("value",s.target.checked),a.isFunction(this.updateHandler)&&this.updateHandler(s)}}};var Pc=function(){var t=this,e=t._self._c;return e("div",{class:["cmp-form-options","cmp-form-options--checkbox",t.hasError&&"cmp-form-options--error"].concat(t.getModifierClasses("cmp-form-options"))},[e("div",{staticClass:"cmp-form-option"},[e("input",{ref:"input",staticClass:"cmp-form-option__field cmp-form-option__field--checkbox",attrs:{id:t.id,"aria-invalid":t.hasError,disabled:t.disabled,name:t.name||t.id,type:"checkbox"},domProps:{checked:t.isChecked,value:t.value},on:{change:t.emitInput}}),e("label",{class:["cmp-form-option__label","cmp-form-option__label--checkbox",t.required&&"cmp-form-option__label--required"],attrs:{for:t.id}},[t._v(" "+t._s(t.label)+" ")]),t.hasError?e("p",{staticClass:"cmp-form-options__error-block",attrs:{role:"alert"}},[t._v(" "+t._s(t.errorMessage)+" ")]):t._e()])])},Rc=[],Nc=l(Mc,Pc,Rc,!1,null,null,null,null);const Hc=Nc.exports,Bc=1,Dc=2,qc=3,Ne=4,zc=6,Vc={name:"CmpSizingField",components:{CmpSizingNumberUnit:Oc,CmpSizingText:Lc,CmpFormSelect:dt,CmpFormRadioGroup:ct,CmpSizingCheckbox:Hc,CmpFormText:ut},mixins:[c],props:{baseunit:{type:String,default:""},inputModifier:{type:String,default:null},inline:{type:Boolean,default:!0},label:{type:String,required:!0},mandatory:{type:Boolean,default:!1},options:{type:Array,default:()=>[]},qcId:{type:String,required:!0},rangeentries:{type:Array,default:()=>[]},readonly:{type:Boolean,default:!1},selectedoptionkey:{type:String,default:null},selectedunitoptionkey:{type:String,default:null},text:{type:String,required:!0},type:{type:Number,required:!0},unitoptions:{type:Array,default:()=>[]},value:{type:[Number,String],default:null},updateHandler:{type:Function,default:a.noop}},data(){return{errorMessage:null,newValue:null}},computed:{component(){switch(this.type){case qc:return this.optionsComponent();case Ne:return a.get(this.unitoptions,"length")?"CmpSizingNumberUnit":"CmpFormNumber";case Bc:return this.readonly?"CmpSizingText":a.get(this.unitoptions,"length")?"CmpSizingNumberUnit":"CmpFormText";case zc:return"CmpFormRadioGroup";case Dc:return"CmpSizingCheckbox";default:return null}},currentValue(){return this.newValue!==null?this.newValue:this.selectedoptionkey||this.value},range(){if(!a.isEmpty(this.rangeentries)||this.type!==Ne||!this.selectedunitoptionkey)return null;const{min:s,max:t}=a.find(this.rangeentries,{relatedunitoptionkey:this.selectedunitoptionkey})||{};return{min:s,max:t}}},watch:{qcId(){this.errorMessage=null,this.newValue=null}},methods:{update({target:s}){const{dataset:t,selectedIndex:e,options:i,value:n,validationMessage:r}=s,d=i?i[e].value:n,u=a.has(t,"value"),m=a.has(t,"unit");if(!u){if(this.newValue=n,!s.checkValidity()){this.errorMessage=r;return}this.errorMessage=null}let g;!u&&!m?g=d:u?g=t.value:g=d;let h;!u&&!m?h="":m?h=t.unit:h=d,this.updateHandler({baseunit:this.baseunit,label:this.label,conunitkey:h,convalue:g})},optionsComponent(){return this.options.length>2||a.find(this.options,a.matches({key:"",value:""}))?"CmpFormSelect":"CmpFormRadioGroup"}}};var Uc=function(){var t=this,e=t._self._c;return e(t.component,t._b({tag:"component",attrs:{disabled:t.readonly,"error-message":t.errorMessage,id:t._f("kebabCase")(`qc-${t.label}`),inline:t.inline,label:t.text,modifier:t.inputModifier,options:t.options,"unit-options":t.unitoptions,"unit-value":t.selectedunitoptionkey,"update-handler":t.update,value:t.currentValue}},"component",t.range,!1))},jc=[],Gc=l(Vc,Uc,jc,!1,null,null,null,null);const mt=Gc.exports,Wc="Sizing parameters updated",Qc="RESTSelectHowToSize",Kc="RESTPumpDesign",Yc="RESTProductFamily",Zc="RESTProductGroup",Jc="UxArea",Xc="UxApplication",td={name:"CmpSizingBasic",components:{CmpSizingField:mt},mixins:[c],props:{buttonClass:{type:String,default:"elm-button--icon-arrow-right_outline"},content:{type:Object,default:()=>({})},hideButton:{type:Boolean,default:!1},labels:{type:Object,default:()=>({})},buttonHandler:{type:Function,default:a.noop},updateHandler:{type:Function,default:a.noop}},data(){return{LOADING:x}},computed:{criteria(){return a.get(this.content,"data.criteria",[])},criteriaFieldModifier(){return`${Math.floor(100/this.criteria.length)}w`},startSizingVisible(){return!this.hideButton&&!this.disableButton},disableButton(){return a.get(this.content,"data")?this.criteria.map(this.validateField).includes(!1):!0},hasActions(){return!!this.$slots.actions},isLoading(){return a.get(this.content,"loading")},journey(){return a.get(this.content,"data.UxJourney")},qcId(){return a.get(this.content,"data.id")}},methods:{track(){f({userJourney:{journey:"sizing",subjourney:a.get(this.journey,"selectedoptionkey"),stepType:"Sizing deck",data:{type:a.get(a.find(this.criteria,{label:Qc}),"selectedoptionkey"),application:a.get(a.find(this.criteria,{label:Xc}),"selectedoptionkey"),applicationArea:a.get(a.find(this.criteria,{label:Jc}),"selectedoptionkey"),pumpDesign:a.get(a.find(this.criteria,{label:Kc}),"selectedoptionkey"),range:a.get(a.find(this.criteria,{label:Yc}),"selectedoptionkey"),product:a.get(a.find(this.criteria,{label:Zc}),"selectedoptionkey")}}},Wc)},validateField(s){if(!a.isPlainObject(s))return!0;const{readonly:t,selectedoptionkey:e,value:i}=s;return t||!!e||!!i}}};var ed=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"cmp-sizing-basic__header"},[t._t("header")],2),e("div",{staticClass:"cmp-sizing-basic__fieldset"},[e("legend",{staticClass:"cmp-sizing-basic__legend"},[t._v(t._s(t.labels.legend))]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.criteria,expression:"criteria"}],class:["cmp-sizing-basic__fields",t.hideButton&&"cmp-sizing-basic__fields--no-button"]},[e("div",{staticClass:"cmp-sizing-basic__field-group cmp-sizing-basic__field-group--criteria"},t._l(t.criteria,function(i){return e("cmp-sizing-field",t._b({key:i.label,class:["cmp-sizing-basic__field",`cmp-sizing-basic__field--${t.criteriaFieldModifier}`],attrs:{"qc-id":t.qcId,"update-handler":t.updateHandler,"input-modifier":"underline"}},"cmp-sizing-field",i,!1))}),1),t.startSizingVisible?e("button",{class:["elm-button","elm-button--small","cmp-sizing-basic__button",t.buttonClass,t.isLoading&&t.LOADING],attrs:{disabled:t.disableButton,type:"button"},on:{click:t.buttonHandler}},[e("span",{staticClass:"elm-square-button__text"},[t._v(t._s(t.labels.start))])]):t._e()]),e("div",{class:["cmp-sizing-actions",t.hasActions&&"cmp-sizing-actions--align-right"]},[e("div",{directives:[{name:"show",rawName:"v-show",value:t.criteria,expression:"criteria"}],staticClass:"cmp-sizing-actions__group cmp-sizing-basic__journey-selector"},[t.journey?e("cmp-sizing-field",t._b({attrs:{"qc-id":t.qcId,type:6,"update-handler":t.updateHandler,inline:""}},"cmp-sizing-field",t.journey,!1)):t._e()],1),t.hasActions?e("div",{staticClass:"cmp-sizing-actions__group"},[t._t("actions")],2):t._e(),e("div",{staticClass:"cmp-sizing-actions__group"},[t.criteria&&t.startSizingVisible?e("button",{class:["elm-button","cmp-sizing-basic__mobile-button",t.isLoading&&t.LOADING],attrs:{disabled:t.disableButton,type:"submit"},on:{click:t.buttonHandler}},[e("span",{staticClass:"elm-button__text"},[t._v(t._s(t.labels.start))])]):t._e()])])]),e("div",{directives:[{name:"show",rawName:"v-show",value:!t.criteria&&t.isLoading,expression:"!criteria && isLoading"}],staticClass:"cmp-sizing-basic__loader"},[t._v(" "+t._s(t.labels.loading)+" ")])])},sd=[],id=l(td,ed,sd,!1,null,null,null,null);const ht=id.exports,ad=250;function nd(s="update"){return{data(){return{batchUpdates:[]}},created(){if(!a.isFunction(this[s]))throw new Error(`"${s}" method required for use with the "withBatchUpdate" mixin was not found on this instance.`);this.batchUpdate=a.debounce(t=>{this[s](t).then(this.clearBatchUpdates).catch(this.clearBatchUpdates)},ad)},watch:{batchUpdates(t){t.length&&this.batchUpdate(t)}},methods:{queueUpdate(t){this.batchUpdates.push(t)},clearBatchUpdates(){this.batchUpdates=[]}}}}const rd="UxHero",X="UxJourney",I="Advanced",od="Standard",{mapState:ld}=p,cd=["baseunit","label","mandatory","options","rangeentries","readonly","selectedoptionkey","selectedunitoptionkey","text","type","unitoptions","value"],dd={name:"CmpSizingHero",components:{CmpSizingBasic:ht},mixins:[v,nd("updateContent"),y,c],props:{labels:{type:Object,default:()=>({})},sizingPageUrl:{type:String,required:!0},advancedSizingPageUrl:{type:String,default:""}},data(){return{content:null,shouldGoToAdvancedSizing:!1}},computed:{...ld("base/user",["isLoggedIn"]),qcId(){return a.get(this.content,"data.id")},uxJourney(){return a.get(this.content,"data.UxJourney",{})},enabledAdvancedOption(){return this.isLoggedIn||!!this.config.enableAnonymousAccess}},watch:{uxJourney(s){s.selectedoptionkey===I&&this.enabledAdvancedOption&&this.shouldGoToAdvancedSizing&&(this.shouldGoToAdvancedSizing=!1,this.startSizing())}},created(){this.cancelRequest=a.noop},mounted(){if(!this.isEditMode)this.getContent();else{const s=this.$watch("isEditMode",t=>{t||(s(),this.getContent())})}},methods:{startSizing(){const t=this.uxJourney.selectedoptionkey===I&&this.config.enableAdvancedSizingPage?this.advancedSizingPageUrl:this.sizingPageUrl,e={sQcid:this.qcId};window.location.href=M(t,e)},getContent(){const s=it();return this.cancelRequest(),this.cancelRequest=s.cancel,this.setContent(null),E(this.config.apiUrl,{params:{compact:!0},cancelToken:s.token}).then(this.mapContent).then(this.setContent).catch(this.setContent).then(this.switchToStandardSizing)},setContent(s){this.content=L(s,this.content)},mapContent(s){const t=a.get(s,"data");if(!t)return s;const{id:e,groups:i}=t,n=a.get(a.find(i,{label:rd}),"questions",[]).reduce((g,h)=>Object.assign({},g,{[h.label]:a.pick(h,cd)}),{}),{Flow:r,Head:d,UxJourney:u,...m}=n;return Object.assign({},s,{data:{criteria:Object.values(m),Flow:r,Head:d,id:e,UxJourney:u}})},updateContent(s){const t=it();return this.cancelRequest(),this.cancelRequest=t.cancel,this.setContent(null),gt(this.config.apiUrl,rt.stringify({body:JSON.stringify(s)},{encode:!0}),{params:{compact:!0,qcid:this.qcId},cancelToken:t.token}).then(this.mapContent).then(this.setContent).catch(this.setContent).then(this.track)},update(s){this.queueUpdate(s)},goToAdvancedSizing(){if(this.shouldGoToAdvancedSizing=!0,this.uxJourney.options)this.update({baseunit:"",label:X,conunitkey:"",convalue:I});else{const s={baseunit:"",label:"SizeBy",conunitkey:"",convalue:"PumpFamily"};this.update(s);const t={baseunit:"",label:X,conunitkey:"",convalue:I};this.update(t)}},switchToStandardSizing(){a.get(this.uxJourney,"selectedoptionkey","")===I&&this.update({baseunit:"",label:X,conunitkey:"",convalue:od})}}};var ud=function(){var t=this,e=t._self._c;return e("form",{attrs:{autocomplete:"off"},on:{submit:function(i){i.preventDefault()}}},[e("cmp-sizing-basic",{staticClass:"cmp-sizing-basic",attrs:{content:t.content,labels:t.labels,"button-handler":t.startSizing,"update-handler":t.update}},[e("template",{slot:"header"},[t._t("intro")],2),e("template",{slot:"actions"},[t._t("actions",null,{enabledAdvancedOption:t.enabledAdvancedOption,goToAdvancedSizing:t.goToAdvancedSizing})],2)],2)],1)},pd=[],md=l(dd,ud,pd,!1,null,null,null,null);const He=md.exports,hd={name:"CmpScrollButton",mixins:[c],props:{disabled:{type:Boolean,default:!1},label:{type:String,required:!0},offset:{type:Object,default:()=>({left:0,top:0})},scrollElement:{type:String,required:!0}},methods:{scrollToElement(){K(this.scrollElement,!0,this.offset)}}};var _d=function(){var t=this,e=t._self._c;return e("button",{attrs:{disabled:t.disabled,type:"button"},on:{click:t.scrollToElement}},[e("span",{staticClass:"elm-button__text"},[t._v(t._s(t.label))])])},fd=[],gd=l(hd,_d,fd,!1,null,null,null,null);const Be=gd.exports,bd={name:"CmpSubmitButton",mixins:[c],props:{disabled:{type:Boolean,default:!1},isLoading:{type:Boolean,default:!1},label:{type:String,required:!0}},data(){return{LOADING:x}}};var vd=function(){var t=this,e=t._self._c;return e("button",t._g({class:["elm-button","elm-button--positive",t.isLoading&&t.LOADING],attrs:{disabled:t.disabled||t.isLoading,type:"submit"}},t.$listeners),[e("span",{staticClass:"elm-button__text"},[t._v(t._s(t.label))])])},yd=[],Cd=l(bd,vd,yd,!1,null,null,null,null);const De=Cd.exports,{mapMutations:Sd}=p,kd={name:"CmpTab",mixins:[v,c],props:{caption:{type:String,default:null},currentTabId:{type:String,default:null},disabled:{type:Boolean,default:!1},id:{type:String,required:!0},isActive:{type:Boolean,default:!1},label:{type:String,required:!0},subCaption:{type:String,default:null},captionClass:{type:String,default:null},useQueryOnWindowLoad:{type:Boolean,default:!1}},computed:{isExpanded(){return this.id===this.currentTabId},attrs(){return this.isActive?{"aria-expanded":this.isEditMode||this.isExpanded?"true":"false"}:{}}},watch:{isExpanded(s){this.$nextTick().then(()=>{this.$el.dispatchEvent(xs(s?"TabExpanded":"TabCollapsed")),s&&this.resetViewportSize()})}},methods:{...Sd("base",["resetViewportSize"])}};var Td=function(){var t=this,e=t._self._c;return e("div",t._b({attrs:{id:t.id}},"div",t.attrs,!1),[t._t("default",null,{isExpanded:t.isExpanded})],2)},$d=[],xd=l(kd,Td,$d,!1,null,null,null,null);const qe=xd.exports,{mapState:wd}=p,tt="all",Ld=2,Ed="Tab change",Id={name:"CmpTabs",components:{CmpHorizontalScroll:pt},mixins:[v,bt,Fs,c],props:{activeTabId:{type:String,default:null},caption:{type:String,default:null},defaultTabId:{type:String,default:null},disabled:{type:Boolean,default:!1},heading:{type:String,default:null},theme:{type:String,default:""},labels:{type:Object,default:()=>({})},resetWatch:{type:[Number,String,Boolean,Object,Array],default:null},showAll:{type:Boolean,default:!1},sticky:{type:Boolean,default:!1},stickyHeading:{type:Boolean,default:!1},centered:{type:Boolean,default:!1},useQuery:{type:Boolean,default:!1},toggleHandler:{type:Function,default:a.noop}},data(){return{SHOW_ALL:tt,currentTabId:null,isStuck:!1,prevTabId:null,navOffset:0,barHeight:0,contentPosInView:0,tabs:[],loadedTabs:[]}},computed:{...wd("base",["navIsExpanded","windowScroll","viewportSize"]),attrs(){return this.isStuck?{style:{"padding-top":`${this.navOffset}px`}}:{}},initialTabId(){return this.showAll?tt:this.defaultTabId},headingIsVisible(){return this.heading?this.stickyHeading?this.isStuck:!0:!1}},watch:{activeTabId(){this.currentTabId!==this.activeTabId&&this.setTab(this.activeTabId)}},mounted(){setTimeout(()=>{this.setTabs()},200),this.sticky&&(this.setSticky(this.windowScroll),this.$watch("windowScroll",this.setSticky),this.$watch("viewportSize",this.setNavOffset),this.$watch("viewportSize",this.setBarHeight)),this.resetWatch!==null&&this.$watch("resetWatch",this.setTabs),!!window.MSInputMethodContext&&!!document.documentMode&&setTimeout(()=>{this.setBarHeight(),this.setScroll()},200)},methods:{windowLoad(){if(this.sticky&&(this.setNavOffset(),this.setBarHeight()),this.useQuery){const s=this.getHashValue(),t=this.getQueryValue();this.validateTabId(t)&&(s||this.tabUseQueryOnWindowLoad(t))&&this.setScroll()}this.setTabInViewport()},setTabInViewport(){const s=Math.abs(this.$children[0].maxPos),t=Math.abs(this.$children[0].contentPos),i=document.querySelector(`[aria-controls = ${this.currentTabId} ]`).parentElement.getBoundingClientRect(),n=this.viewportSize.width,r=(n-(i.right-i.left))/Ld,d=i.right-n+r+t;d<0?this.contentPosInView=0:d>0&&d<s?this.contentPosInView=-d:d>s&&(this.contentPosInView=-s)},getDimensions(s){const t=document.body.getBoundingClientRect(),e=s.getBoundingClientRect();return{top:e.top-t.top,bottom:e.top-t.top+e.height}},getHashValue(){const{location:{hash:s}}=window;return s.split("#")[1]||null},getHashEl(){return this.getHashValue()?document.getElementById(this.getHashValue()):null},getQueryValue(){return a.get(S(window.location.search),"tab",null)},setBarHeight(){this.$el.offsetParent!==null&&(this.barHeight=this.$refs.nav.getBoundingClientRect().height)},setSticky({y:s}){if(this.$el.offsetParent===null)return;const t=this.getDimensions(this.$el);this.isStuck=!this.isEditMode&&!this.navIsExpanded?s>=t.top&&s<=t.bottom-this.navOffset:!1},setNavOffset(){if(this.$el.offsetParent===null)return;if(this.isEditMode){this.navOffset=0;return}const s=this.stickyHeading?this.$refs.bar:this.$refs.nav;this.navOffset=s?s.getBoundingClientRect().height:0},setScroll(){this.$el.offsetParent!==null&&this.$nextTick().then(()=>{const s=this.getHashEl(),t=document.getElementById(this.currentTabId),e=s&&t&&t.contains(s)?this.getDimensions(s).top-this.barHeight:this.getDimensions(this.$el).top;W(0,e,!0)})},setTab(s){const t=decodeURI(s),e=this.validateTabId(t);!this.isEditMode&&e&&(this.prevTabId=this.currentTabId,this.currentTabId=t,this.toggleHandler(this.currentTabId,this.prevTabId),this.isActive&&this.useQuery&&(T({tab:this.currentTabId}),this.setScroll()),f({page:{tabName:s}},Ed),this.setTabInViewport(),this.addToLoadedTabs(this.currentTabId))},setTabs(){this.tabs=this.$children.reduce((i,n)=>{const{caption:r,disabled:d,id:u,label:m,subCaption:g,useQueryOnWindowLoad:h,captionClass:Hs}=n._props||{};return m&&u?i.concat({caption:r,disabled:d,id:u,label:m,subCaption:g,useQueryOnWindowLoad:h,captionClass:Hs}):i},[]);const s=this.initialTabId||a.get(this.tabs,"[0].id"),t=this.validateTabId(s)?s:null,e=this.validateTabId(this.activeTabId)?this.activeTabId:null;if(this.useQuery){const i=this.getQueryValue(),n=i&&this.validateTabId(i);this.currentTabId=n?i:t,this.isEditMode||T({tab:this.currentTabId})}else this.currentTabId=e||t;this.toggleHandler(this.currentTabId),this.addToLoadedTabs(this.currentTabId)},toggleTab(s,t=!1){const e=a.find(this.tabs,{id:s});e&&(e.disabled=t)},validateTabId(s){const t=s===tt&&this.showAll;if(s===null||t)return!0;const e=a.find(this.tabs,{id:s});return!!(e&&!e.disabled)},tabUseQueryOnWindowLoad(s){const t=a.find(this.tabs,{id:s});return t&&t.useQueryOnWindowLoad},addToLoadedTabs(s){this.loadedTabs.includes(s)||this.loadedTabs.push(this.currentTabId)},removeTabById(s){const t=a.find(this.tabs,{id:s});t&&this.tabs.splice(this.tabs.indexOf(t),1)}}};var Ad=function(){var t=this,e=t._self._c;return e("section",t._b({},"section",t.attrs,!1),[t.isActive?e("nav",{ref:"nav",class:["cmp-tabs__nav","cmp-tabs__nav--border",{"cmp-tabs__nav--sticky":t.sticky,"cmp-tabs__nav--centered":t.centered,"cmp-tabs__nav--sticky-heading":t.stickyHeading,"is-stuck":t.isStuck},`b-theme ${t.theme}`],attrs:{role:"tablist"}},[e("div",{staticClass:"cmp-tabs__nav-inner"},[t.headingIsVisible?e("h2",{staticClass:"cmp-tabs__heading"},[t._v(" "+t._s(t.heading)+" "),t.caption?e("span",{staticClass:"cmp-tabs__caption"},[t._v(" "+t._s(t.caption)+" ")]):t._e()]):t._e(),e("div",{ref:"bar",staticClass:"cmp-tabs__nav-bar"},[e("cmp-horizontal-scroll",{key:"scroll",ref:"bar",staticClass:"cmp-tabs__list cmp-horizontal-scroll",attrs:{"content-pos-in-view":t.contentPosInView,labels:t.labels,"resize-watch":t.tabs}},[t.showAll?e("div",{staticClass:"cmp-tabs__list-item"},[e("button",{staticClass:"elm-tab-button",attrs:{"aria-selected":!t.isEditMode&&t.SHOW_ALL===t.currentTabId,type:"button",role:"tab"},on:{click:function(i){return i.preventDefault(),t.setTab(t.SHOW_ALL)}}},[t._v(" "+t._s(t.labels.all)+" ")])]):t._e(),t._l(t.tabs,function(i){return e("div",{key:i.id,staticClass:"cmp-tabs__list-item"},[e("button",{staticClass:"elm-tab-button",attrs:{"data-qa":`cmp-tab-${i.id}`,"aria-controls":i.id,"aria-selected":!t.isEditMode&&i.id===t.currentTabId,disabled:i.disabled||t.disabled,type:"button",role:"tab"},on:{click:function(n){return n.preventDefault(),t.setTab(i.id)}}},[t._v(" "+t._s(i.label)+" "),i.caption?e("span",{class:["cmp-tabs__caption",i.captionClass]},[t._v(" "+t._s(i.caption)+" ")]):t._e(),i.subCaption?e("span",{staticClass:"cmp-tabs__sub-caption"},[t._v(" "+t._s(i.subCaption)+" ")]):t._e()])])})],2)],1)])]):t._e(),t._t("default",null,{currentTabId:t.currentTabId,isActive:t.currentTabId===t.SHOW_ALL?!1:t.isActive,toggleHandler:t.toggleTab,barHeight:t.barHeight,loadedTabs:t.loadedTabs,isStuck:t.isStuck})],2)},Fd=[],Od=l(Id,Ad,Fd,!1,null,null,null,null);const ze=Od.exports,Md={name:"CmpTabToggleTest",props:{tabId:{type:String,required:!0},toggleHandler:{type:Function,required:!0}},mounted(){setTimeout(()=>{this.toggleHandler(this.tabId,!1)},3e3)},render(){return null}},Pd=null,Rd=null;var Nd=l(Md,Pd,Rd,!1,null,null,null,null);const Ve=Nd.exports,{mapMutations:Hd}=p,Bd={name:"CmpTabSelector",mixins:[c,v],props:{align:{type:String,default:void 0},controlsId:{type:String,required:!0},labels:{type:Object,default:()=>({})},items:{type:Array,required:!0,validator:si.every(Q(["id","label"]))},value:{type:String,default:null},toggleHandler:{type:Function,default:a.noop},useQuery:{type:Boolean,default:!1}},data(){return{currentTabId:null,prevTabId:null}},computed:{isExpanded(){return this.value},attrs(){return this.isActive?{"aria-expanded":this.isEditMode||this.isExpanded?"true":"false"}:{}}},watch:{isExpanded(s){this.$nextTick().then(()=>{this.$el.dispatchEvent(xs(s?"TabExpanded":"TabCollapsed")),s&&this.resetViewportSize()})}},methods:{...Hd("base",["resetViewportSize"]),setValue(s){this.selectedTabId=decodeURI(s.id),this.useQuery&&(this.prevTabId=this.currentTabId,this.currentTabId=this.selectedTabId,this.isEditMode||T({tab:this.selectedTabId.replace(/ /g,"-")})),this.toggleHandler(s)}}};var Dd=function(){var t=this,e=t._self._c;return e("section",[e("nav",{ref:"nav",staticClass:"cmp-tabs__nav",attrs:{role:"tablist"}},[e("div",{staticClass:"cmp-tabs__nav-inner"},[e("div",{ref:"bar",staticClass:"cmp-tabs__nav-bar"},[e("cmp-horizontal-scroll",{key:"scroll",ref:"bar",staticClass:"cmp-tabs__list cmp-horizontal-scroll",attrs:{align:t.align,labels:t.labels}},t._l(t.items,function(i){return e("div",{key:i.id,staticClass:"cmp-tabs__list-item"},[e("button",{staticClass:"elm-tab-button",attrs:{"aria-controls":t.controlsId,"aria-selected":!t.isEditMode&&i.id===t.value,disabled:i.disabled,type:"button",role:"tab"},on:{click:function(n){return n.preventDefault(),t.setValue(i)}}},[t._v(" "+t._s(i.label)+" ")])])}),0)],1)])])])},qd=[],zd=l(Bd,Dd,qd,!1,null,null,null,null);const Ue=zd.exports,Vd={name:"CmpTaxonomyApplicationCard",props:{url:{type:String,required:!0},thumbnail:{type:String,default:""},title:{type:String,required:!0},size:{type:Number,default:0},config:{type:Object,default:()=>({})},labels:{type:Object,default:()=>({})}}};var Ud=function(){var t=this,e=t._self._c;return e("div",{class:t.config.horizontalScroll?"cmp-cards-highlight-deck__list-item":"b-layout-grid__item b-layout-grid__item--12 b-layout-grid__item--medium-6 b-layout-grid__item--large-4"},[e("div",{staticClass:"cmp-application-card cmp-application-card--application"},[e("a",{staticClass:"cmp-application-card__link",attrs:{href:t.url,title:t.title}},[e("div",{staticClass:"cmp-application-card__content"},[e("div",{staticClass:"cmp-application-card__image"},[t.config.dynamicMedia?e("elm-img",{staticClass:"elm-img elm-img--16-9",attrs:{alt:t.config.altText,"fill-mode":"cover"}},[e("elm-dynamic-media",[e("div",{staticClass:"s7dm-dynamic-media",attrs:{id:t.config.id,"data-asset-path":t.config.assetPath,"data-asset-name":t.config.assetName,"data-asset-type":t.config.assetType,"data-viewer-path":t.config.viewerPath,"data-imageserver":t.config.imageServer,"data-contenturl":t.config.contentUrl,"data-alt":t.config.altText,"data-wcmdisabled":"","data-dms7":"","data-mode":"","data-linktarget":"_self"}})])],1):e("elm-img",{staticClass:"elm-img elm-img--16-9",attrs:{src:t.thumbnail,alt:t.title,"fill-mode":"cover"}})],1)]),e("div",{staticClass:"cmp-application-card__meta"},[e("div",{staticClass:"cmp-application-card__info"},[e("h3",{staticClass:"cmp-application-card__heading"},[t._v(t._s(t.title))]),e("p",{staticClass:"cmp-application-card__pre-heading"})])])])])])},jd=[],Gd=l(Vd,Ud,jd,!1,null,null,null,null);const je=Gd.exports,Wd=["small","portrait"],Qd=`{
    items {
      uuid
      metadata {
        baseData {
          title
          description
          url
          thumbnail
          pageType
        }
        ... on ProductCenterMetadata {
          features
          technicalData
          typecode
          products
          pageData {
            language
            country
            site
            range
            tags
            productName
            application
            applicationArea
            categoryArea
          }
        }
        ... on ProductVariantMetadata {
          sizable
          source
        }
        ... on CMSMetadata {
          contentType
          continent
        }
      }
    }
    count
  }
}`,Kd=`query taxonomy(
  $site: SitesEnum!
  $taxonomyDeckType: String!
  $tagsFromHostPage: [FilterItem]
  $tagsFromTaxonomyQuery: [FilterItem]
  $allTagsFromHostPage: [String]
	$excludedPaths: [String]
  $limit: Int
  $offset: Int
  $sort: [SortOptionsEnum]
) {
  results: taxonomy(
    site: $site
    taxonomyDeckType: $taxonomyDeckType
    tagsFromHostPage: $tagsFromHostPage
    tagsFromTaxonomyQuery: $tagsFromTaxonomyQuery
    allTagsFromHostPage: $allTagsFromHostPage
		excludedPaths: $excludedPaths
    limit: $limit
    offset: $offset
    sort: $sort
  ) ${Qd}`,Yd={name:"CmpTaxonomyCards",mixins:[y],props:{cardComponent:{type:String,required:!0},limit:{type:Number,default:10},filters:{type:Object,default:()=>({})},tags:{type:String,default:null},labels:{type:Object,default:()=>({})},contentType:{type:String,default:null},sortBy:{type:String,default:""},isStandardPage:{type:Boolean,default:!1},isFaqPage:{type:Boolean,default:!1},horizontalScroll:{type:Boolean,default:!1}},data(){return{items:[],isError:!1,isDoneLoading:!1}},computed:{isGenericQuery(){return this.contentType==="generic"},site(){const s=a.get(window,"grundfos.locale.country").toUpperCase(),t=a.get(window,"grundfos.locale.language").toUpperCase();return`${s==="LANGUAGE-MASTERS"?"WWW":s}_${t}`},postBody(){return this.postBodyTaxonomyBackend},postBodyTaxonomyBackend(){const s={query:Kd,variables:{site:this.site,taxonomyDeckType:this.contentType,tagsFromHostPage:this.getFilterList("tagsFromHostPage"),tagsFromTaxonomyQuery:this.getFilterList("tagsFromTaxonomyQuery"),excludedPaths:[window.location.origin+window.location.pathname],limit:this.limit,sort:this.sortedBy}};return this.isGenericQuery&&(s.variables.allTagsFromHostPage=this.allTagsFromHostPage),s},allTagsFromHostPage(){return this.filters&&this.filters.allTagsFromHostPage?this.filters.allTagsFromHostPage:[]},sortedBy(){return this.sortBy&&this.sortBy==="0"?"PUBLISHED_ASC":this.sortBy&&this.sortBy==="1"?"PUBLISHED_DESC":this.sortBy&&this.sortBy==="title"?"TITLE_ASC":"PUBLISHED_ASC"},className(){return this.isFaqPage||this.horizontalScroll?"":this.isSpacingNeeded?"b-layout-grid__group b-layout-grid-group--spaced":"b-layout-grid__group"},isSpacingNeeded(){return Wd.includes(this.config.cardSize)}},mounted(){this.fetch().then(s=>s.json()).then(this.setContent).catch(this.setContent)},methods:{fetch(){return!!window.MSInputMethodContext&&!!document.documentMode?this.getIeSearchResponse():this.getSearchResponse()},getSearchResponse(){return fetch(this.config.apiUrl,{method:"POST",mode:"cors",credentials:"same-origin",headers:{"Content-Type":"application/json"},redirect:"follow",referrer:document.location.href.split("?")[0],referrerPolicy:"unsafe-url",body:JSON.stringify(this.postBody)})},getIeSearchResponse(){return window.fetch(this.config.apiUrl,{method:"POST",mode:"cors",credentials:"same-origin",headers:{"Content-Type":"application/json"},redirect:"follow",referrer:document.location.href.split("?")[0],referrerPolicy:"unsafe-url",body:JSON.stringify(this.postBody)})},setContent(s){const t=[];if(Object.prototype.hasOwnProperty.call(s,"errors")){this.isError=!0,this.items=t,this.isDoneLoading=!0;return}a.get(s,"data.results.items").forEach(e=>{const i=a.get(e,"metadata.baseData");i.features=a.get(e,"metadata.features"),i.technicalData=a.get(e,"metadata.technicalData"),i.sizable=a.get(e,"metadata.sizable"),i.pageContentType=a.get(e,"metadata.contentType"),i.id=a.get(e,"uuid"),t.push(i)}),t.length===0&&this.hideParentContainer(),this.items=t,this.isDoneLoading=!0},hideParentContainer(){const s=this.$refs.outerContainer.closest(".b-theme.b-deck.b-deck--full-width.cmp-cards-highlight-deck");s&&s.classList.add("hidden")},fieldTypeAddRegEx(s){return s.replace(/-/g,"_")},getFilterList(s){if(!this.isGenericQuery)return this.getFlatTagsFilter(s);if(this.filters&&this.filters[s]){const t=this.filters[s],e=[];return Object.entries(t).forEach(([i,n])=>{e.push({field:this.fieldTypeAddRegEx(i).toUpperCase(),value:n})}),e}return[]},getFlatTagsFilter(s){const t=[];if(this.filters&&this.filters[s]){const e=this.filters[s];Object.values(e).forEach(i=>{i.forEach(n=>{t.push(n)})})}return t.length?[{field:"TAGS",value:t}]:[]}}};var Zd=function(){var t=this,e=t._self._c;return e("div",{ref:"outerContainer",class:t.className},[t.horizontalScroll?t._e():t._l(t.items,function(i,n){return e(t.cardComponent,t._b({key:i.title,tag:"component",attrs:{count:n,labels:t.labels,config:t.config}},"component",i,!1))}),t.horizontalScroll&&t.isDoneLoading?e("cmp-card-carousel",{staticClass:"cmp-horizontal-scroll cmp-horizontal-scroll--large cmp-cards-highlight-deck__list",attrs:{labels:t.labels}},t._l(t.items,function(i,n){return e(t.cardComponent,t._b({key:i.title,tag:"component",attrs:{count:n,labels:t.labels,config:t.config}},"component",i,!1))}),1):t._e(),t.isError?e("p",{staticClass:"cmp-cards-highlight-deck__error-block"},[t._v(" "+t._s(t.labels.errorMessage)+" ")]):t._e()],2)},Jd=[],Xd=l(Yd,Zd,Jd,!1,null,null,null,null);const Ge=Xd.exports,tu={name:"CmpTaxonomyCategoryCard",props:{url:{type:String,required:!0},thumbnail:{type:String,default:""},title:{type:String,required:!0},config:{type:Object,default:()=>({})}}};var eu=function(){var t=this,e=t._self._c;return e("div",{class:t.config.horizontalScroll?"cmp-cards-highlight-deck__list-item":"b-layout-grid__item b-layout-grid__item--6 b-layout-grid__item--medium-4 b-layout-grid__item--large-3"},[e("div",{staticClass:"cmp-category-card cmp-category-card--category"},[e("a",{staticClass:"cmp-category-card__link",attrs:{href:t.url}},[e("div",{staticClass:"cmp-category-card__content"},[t.thumbnail?e("div",{staticClass:"cmp-application-card__image"},[t.config.dynamicMedia?e("elm-img",{staticClass:"elm-img elm-img--16-9",attrs:{alt:t.config.altText,"fill-mode":"cover"}},[e("elm-dynamic-media",[e("div",{staticClass:"s7dm-dynamic-media",attrs:{id:t.config.id,"data-asset-path":t.config.assetPath,"data-asset-name":t.config.assetName,"data-asset-type":t.config.assetType,"data-viewer-path":t.config.viewerPath,"data-imageserver":t.config.imageServer,"data-contenturl":t.config.contentUrl,"data-alt":t.config.altText,"data-wcmdisabled":"","data-dms7":"","data-mode":"","data-linktarget":"_self"}})])],1):e("elm-img",{staticClass:"elm-img elm-img--16-9",attrs:{src:t.thumbnail,alt:t.title,"fill-mode":"cover"}})],1):t._e()]),e("div",{staticClass:"cmp-category-card__meta"},[e("div",{staticClass:"cmp-category-card__info"},[e("h3",{staticClass:"cmp-category-card__heading"},[t._v(" "+t._s(t.title)+" ")])])])])])])},su=[],iu=l(tu,eu,su,!1,null,null,null,null);const We=iu.exports,au=2,nu={name:"CmpTaxonomyContentCard",props:{url:{type:String,required:!0},thumbnail:{type:String,default:""},title:{type:String,required:!0},description:{type:String,default:""},count:{type:Number,default:0},config:{type:Object,default:()=>({})},pageType:{type:String,default:""},pageContentType:{type:String,default:null},labels:{type:Object,default:()=>({})}},data(){return{pageContentTypeTranslation:""}},computed:{isLarge(){return this.config.cardSize==="largeAndSmall"&&this.isLargeCard()},cardSize(){let{cardSize:s}=this.config;return this.config.cardSize==="largeAndSmall"&&(s=this.isLargeCard()?"large":"small"),s},classes(){const s=["b-layout-grid__item","b-layout-grid__item--12"];return this.config.horizontalScroll?s.push("cmp-cards-highlight-deck__list-item"):this.config.cardSize==="portrait"?(s.push("b-layout-grid__item--medium-6"),s.push("b-layout-grid__item--large-4")):this.config.cardSize==="small"?s.push("b-layout-grid__item--large-3"):this.config.cardSize==="largeAndSmall"?this.isLargeCard()?s.push("b-layout-grid__item--large-6"):s.push("b-layout-grid__item--large-3"):s.push("b-layout-grid__item--large-6"),s},classNames(){const s=["cmp-content-card"];return s.push(`cmp-content-card--${this.cardSize}`),s.push(`cmp-content-card--${this.cardSize}-normal`),s},isContentType(){return this.pageContentType?"contentType":"pageType"}},mounted(){this.getPageContentTypeTranslation()},methods:{getPageContentTypeTranslation(){const s={field:this.isContentType,value:this.isContentType==="contentType"?this.pageContentType:this.pageType};E(`${this.config.currentPagePath}.taxonomyresultpagetype.json`,{params:s}).then(t=>{this.pageContentTypeTranslation=a.get(t,"data")}).catch(t=>t)},isLargeCard(){return this.count<au}}};var ru=function(){var t=this,e=t._self._c;return e("div",{class:t.classes},[e("div",{class:t.classNames},[e("a",{staticClass:"cmp-content-card__link",attrs:{href:t.url}},[e("div",{staticClass:"cmp-content-card__content"},[e("div",{staticClass:"cmp-content-card__image"},[e("elm-img",{staticClass:"elm-img elm-img--16-9 is-active",attrs:{alt:t.title,src:{src:t.thumbnail,height:t.cardSize==="small"?220:440},"src-format":"hei={height}","fill-mode":"cover"}})],1)]),e("div",{staticClass:"cmp-content-card__meta"},[e("ul",{staticClass:"cmp-content-card__tag-list cmp-tag-list"},[e("li",{staticClass:"cmp-tag-list__item"},[e("p",{staticClass:"elm-tag"},[t._v(" "+t._s(t.pageContentTypeTranslation)+" ")])])]),e("div",{staticClass:"cmp-content-card__info"},[e("div",{staticClass:"cmp-content-card__text"},[t.config.date&&t.config.showDate?e("p",{staticClass:"cmp-content-card__date"},[t._v(" "+t._s(t.config.date)+" ")]):t._e(),e("h3",{staticClass:"cmp-content-card__heading"},[t._v(" "+t._s(t.title)+" ")]),t.isLarge?t._e():e("p",{directives:[{name:"truncate",rawName:"v-truncate:80",value:{isExcessWord:!0},expression:"{ isExcessWord: true }",arg:"80"}],staticClass:"cmp-content-card__description"},[t._v(" "+t._s(t.description)+" ")])])]),e("div",{staticClass:"cmp-content-card__button"})])])])])},ou=[],lu=l(nu,ru,ou,!1,null,null,null,null);const Qe=lu.exports,cu={name:"CmpTaxonomyFaqCard",mixins:[y],props:{url:{type:String,required:!0},title:{type:String,required:!0},description:{type:String,default:""},isActive:{type:Boolean,default:!0},id:{type:String,default:""}}};var du=function(){var t=this,e=t._self._c;return e("cmp-accordion-item",{staticClass:"cmp-accordion-item",attrs:{id:`accordion-item-${t.id}`,"is-active":t.isActive,"tracking-id":"faq-page"}},[e("h3",{staticClass:"cmp-accordion-item__heading",attrs:{slot:"header"},slot:"header"},[t._v(" "+t._s(t.title)+" ")]),e("div",{attrs:{slot:"content"},slot:"content"},[e("div",{staticClass:"cmp-text h-vsb--large"},[e("p",[t._v(t._s(t.description))])]),e("a",{staticClass:"elm-link elm-link--block",attrs:{href:t.url}},[t._v(" "+t._s(t.title)+" ")])])])},uu=[],pu=l(cu,du,uu,!1,null,null,null,null);const Ke=pu.exports,mu={name:"CmpTaxonomyProductCard",props:{title:{type:String,required:!0},description:{type:String,default:""},url:{type:String,required:!0},thumbnail:{type:String,default:""},features:{type:String,default:""},technicalData:{type:String,default:""},sizable:{type:Boolean,default:!1},typecode:{type:String,default:""},config:{type:Object,default:()=>({})},labels:{type:Object,default:()=>({})}},computed:{hasFeatures(){return this.config.type==="product"&&!!this.features&&this.features.length>0},featuresArray(){return this.features?this.features.split("::"):[]},hasTechnicalData(){return this.config.type==="range"&&!!this.technicalData&&this.technicalData.length>0},sizingUrl(){return`${this.config.currentPagePath}.gotosizing.json?typecode=${this.typecode}&resourcetype=${this.config.type}`}}};var hu=function(){var t=this,e=t._self._c;return e("div",{class:t.config.horizontalScroll?"cmp-cards-highlight-deck__list-item":"b-layout-grid__item b-layout-grid__item--12 b-layout-grid__item--medium-6 b-layout-grid__item--large-4"},[e("div",{class:["cmp-catalogue-card",`cmp-catalogue-card--${t.config.type}`,`${t.config.wide?"cmp-catalogue-card--range-wide":""}`]},[e("a",{staticClass:"cmp-catalogue-card__link",attrs:{href:t.url,title:t.title}},[e("div",{staticClass:"cmp-catalogue-card__content"},[e("div",{staticClass:"cmp-catalogue-card__image cmp-catalogue-card__image--inset"},[e("div",{attrs:{"data-component-root":""}},[e("elm-img",{staticClass:"elm-img elm-img--4-3",attrs:{alt:t.title,src:{src:t.thumbnail,width:200,height:150},srcset:[{src:t.thumbnail,width:200,height:150},{src:t.thumbnail,width:400,height:300},{src:t.thumbnail,width:600,height:450},{src:t.thumbnail,width:768,height:576}],"src-format":"w={width}&h={height}","fill-mode":"contain"}})],1)])]),e("div",{staticClass:"cmp-catalogue-card__meta"},[e("div",{staticClass:"cmp-catalogue-card__info"},[e("h3",{staticClass:"cmp-catalogue-card__heading"},[t._v(t._s(t.title))]),e("p",{staticClass:"cmp-catalogue-card__description noindex",attrs:{id:"taxonony-product-card-description"}},[t._v(" "+t._s(t.description)+" ")])]),t.hasTechnicalData?e("div",{staticClass:"cmp-catalogue-card__spec"},[e("table",{staticClass:"cmp-catalogue-card__spec-table",attrs:{"aria-describedby":"taxonony-product-card-description"}},[e("tr",{staticClass:"cmp-catalogue-card__spec-row"},[e("th",{staticClass:"cmp-catalogue-card__spec-cell cmp-catalogue-card__spec-cell--key"},[t._v(" "+t._s(t.label)+" ")]),e("td",{staticClass:"cmp-catalogue-card__spec-cell cmp-catalogue-card__spec-cell--value"},[t._v(" "+t._s(t.technicalData[t.label])+" ")])])])]):t._e(),t.hasFeatures?e("div",{staticClass:"cmp-catalogue-card__usp"},[e("ul",{staticClass:"cmp-catalogue-card__usp-list"},t._l(t.featuresArray,function(i,n){return n<3?e("li",{key:i,staticClass:"cmp-catalogue-card__usp-list-item"},[t._v(" "+t._s(i)+" ")]):t._e()}),0)]):t._e()])]),e("div",{staticClass:"cmp-catalogue-card__actions"},[e("div",{staticClass:"cmp-catalogue-card__action-item"},[e("a",{staticClass:"cmp-catalogue-card__action-link elm-link elm-link--block",attrs:{href:t.url}},[t._v(" "+t._s(t.labels.explore)+" ")])]),t.sizable?e("div",{staticClass:"cmp-catalogue-card__action-item"},[e("a",{staticClass:"cmp-catalogue-card__action-link elm-link elm-link--block",attrs:{href:t.sizingUrl}},[t._v(" "+t._s(t.config.type=="product"?t.labels.sizeproduct:t.labels.sizerange)+" ")])]):t._e()])])])},_u=[],fu=l(mu,hu,_u,!1,null,null,null,null);const Ye=fu.exports,gu={name:"CmpTaxonomyRangeCard",props:{title:{type:String,required:!0},description:{type:String,default:""},url:{type:String,required:!0},thumbnail:{type:String,default:""},className:{type:String,default:""},features:{type:String,default:""},technicalData:{type:String,default:""},sizable:{type:Boolean,default:!1},typecode:{type:String,default:""},config:{type:Object,default:()=>({})},labels:{type:Object,default:()=>({})}},computed:{hasFeatures(){return this.config.type==="product"&&!!this.features&&this.features.length>0},featuresArray(){return this.features?this.features.split("::"):[]},hasTechnicalData(){return this.config.type==="range"&&!!this.technicalData&&this.technicalData.length>0},sizingUrl(){return`${this.config.currentPagePath}.gotosizing.json?typecode=${this.typecode}&resourcetype=${this.config.type}`}}};var bu=function(){var t=this,e=t._self._c;return e("div",{class:t.config.horizontalScroll?"cmp-cards-highlight-deck__list-item":"b-layout-grid__item b-layout-grid__item--12 b-layout-grid__item--medium-6 b-layout-grid__item--large-4"},[e("div",{class:["cmp-catalogue-card",`cmp-catalogue-card--${t.config.type}`,`${t.config.wide?"cmp-catalogue-card--range-wide":""}`]},[e("a",{staticClass:"cmp-catalogue-card__link",attrs:{href:t.url,title:t.title}},[e("div",{staticClass:"cmp-catalogue-card__content"},[e("div",{staticClass:"cmp-catalogue-card__image cmp-catalogue-card__image--inset"},[e("div",{attrs:{"data-component-root":""}},[e("elm-img",{staticClass:"elm-img elm-img--4-3",attrs:{alt:t.title,src:{src:t.thumbnail,width:200,height:150},srcset:[{src:t.thumbnail,width:200,height:150},{src:t.thumbnail,width:400,height:300},{src:t.thumbnail,width:600,height:450},{src:t.thumbnail,width:768,height:576}],"src-format":"w={width}&h={height}","fill-mode":"contain"}})],1)])]),e("div",{staticClass:"cmp-catalogue-card__meta"},[e("div",{staticClass:"cmp-catalogue-card__info"},[e("h3",{staticClass:"cmp-catalogue-card__heading"},[t._v(t._s(t.title))]),e("p",{staticClass:"cmp-catalogue-card__description noindex",attrs:{id:"taxonomy-range-card-description"}},[t._v(" "+t._s(t.description)+" ")])]),t.hasTechnicalData?e("div",{staticClass:"cmp-catalogue-card__spec"},[e("table",{staticClass:"cmp-catalogue-card__spec-table",attrs:{"aria-describedby":"taxonomy-range-card-description"}},[e("tr",{staticClass:"cmp-catalogue-card__spec-row"},[e("th",{staticClass:"cmp-catalogue-card__spec-cell cmp-catalogue-card__spec-cell&#45;&#45;key"},[t._v(" "+t._s(t.label)+" ")]),e("td",{staticClass:"cmp-catalogue-card__spec-cell cmp-catalogue-card__spec-cell&#45;&#45;value"},[t._v(" "+t._s(t.technicalData[t.label])+" ")])])])]):t._e(),t.hasFeatures?e("div",{staticClass:"cmp-catalogue-card__usp"},t._l(t.featuresArray,function(i,n){return e("ul",{key:i,staticClass:"cmp-catalogue-card__usp-list"},[n<3?e("li",{staticClass:"cmp-catalogue-card__usp-list-item"},[t._v(" "+t._s(i)+" ")]):t._e()])}),0):t._e()])]),e("div",{staticClass:"cmp-catalogue-card__actions"},[e("div",{staticClass:"cmp-catalogue-card__action-item"},[e("a",{staticClass:"cmp-catalogue-card__action-link elm-link elm-link--block",attrs:{href:t.url}},[t._v(" "+t._s(t.labels.explore)+" ")])]),t.sizable?e("div",{staticClass:"cmp-catalogue-card__action-item"},[e("a",{staticClass:"cmp-catalogue-card__action-link elm-link elm-link--block",attrs:{href:t.sizingUrl}},[t._v(" "+t._s(t.config.type=="product"?t.labels.sizeproduct:t.labels.sizerange)+" ")])]):t._e()])])])},vu=[],yu=l(gu,bu,vu,!1,null,null,null,null);const Ze=yu.exports,Cu="AIzaSyDXzvGkqX5y1kiyw7bXd0AHHhe4E_pZWcg",Je="gmapsCallback";let Xe=!!window.google,Ms,Ps;const ts=new Promise((s,t)=>{Ms=s,Ps=t});function Su(){if(Xe)return ts;const s=a.get(window,"grundfos.locale.language"),t=a.get(window,"grundfos.locale.country");Xe=!0,window[Je]=()=>Ms(window.google);const e=document.createElement("script");return e.async=!0,e.defer=!0,e.src=`https://maps.googleapis.com/maps/api/js?key=${Cu}&callback=${Je}&language=${s}&region=${t}&libraries=geometry`,e.onerror=Ps,document.querySelector("head").appendChild(e),ts}const k={userMarker:"/etc.clientlibs/settings/wcm/design/aembase/head/resources/img/icons/user-marker-default.svg",userMarkerHover:"/etc.clientlibs/settings/wcm/design/aembase/head/resources/img/icons/user-marker-hover.svg",grundfosMarker:"/etc.clientlibs/settings/wcm/design/aembase/head/resources/img/icons/grundfos-marker-default.svg",grundfosMarkerHover:"/etc.clientlibs/settings/wcm/design/aembase/head/resources/img/icons/grundfos-marker-hover.svg",genericMarker:"/etc.clientlibs/settings/wcm/design/aembase/head/resources/img/icons/generic-marker-default.svg",genericMarkerHover:"/etc.clientlibs/settings/wcm/design/aembase/head/resources/img/icons/generic-marker-hover.svg"},es=20,ss=15,ku=270,Tu=["uk","gb","us"],$u="grundfos",xu=800;let is;const wu={name:"CmpGoogleMap",mixins:[_,y,c],props:{allLocations:{type:Array,default:null},defaultZoom:{type:String,default:null},labels:{type:Object,default:()=>({})},locationType:{type:String,default:null},mapStyles:{type:[Object,String],default:null},mapHeight:{type:String,default:null},selectedMarker:{type:String,default:null},isGeoCode:{type:Boolean,default:!1},closeCard:{type:Function,default:a.noop}},data(){return{bounds:null,filteredLocations:null,google:null,isSearchResultsEmpty:!1,locations:null,map:null,markerClusterer:null,markers:null,userGeoLocation:null,gmapsApiError:null,searchResult:null,level:0}},computed:{geocoder(){return this.getMapObjectItem("Geocoder")},infoWindow(){return this.getMapObjectItem("InfoWindow")},computedLocations(){return`${this.locationType}|${this.allLocations}`},useMiles(){const s=a.get(window,"grundfos.locale.country");return Tu.includes(s)},isGrundfosMarker(){return this.selectedMarker===$u}},watch:{computedLocations(){this.filterLocations(),this.map&&this.setBoundsAndMarkers()},isGeoCode(){this.runGeoCode()}},mounted(){Su().then(s=>{this.google=s,this.renderMap(),this.setMapHeight(),this.getGeoLocation().then(t=>{this.setGeoLocation(t)}),this.filterLocations(),this.setBoundsAndMarkers(),this.google.maps.event.addListener(this.map,"idle",this.boundsChangeHandler),this.loadControls(),this.getGeoLocationPermission()}).catch(s=>{this.gmapsApiError=s})},beforeDestroy(){this.google.maps.event.clearListeners(this.map,"idle",this.boundsChangeHandler),this.markers.forEach(s=>{this.google.maps.event.clearInstanceListeners(s)})},methods:{filterLocations(){if(this.allLocations&&this.locationType){const s=this.allLocations.filter(t=>{if(t.type.toLowerCase()===this.locationType.toLowerCase()){const e=t;return this.userGeoLocation&&(e.distance=this.getDistance(t)),e}return null});this.locations=s.sort((t,e)=>parseInt(t.distance,10)-parseInt(e.distance,10)),this.filteredLocations=this.locations}},getDistance(s){const t=this.google.maps.geometry.spherical.computeDistanceBetween(new this.google.maps.LatLng(s.position),new this.google.maps.LatLng(this.userGeoLocation)),e=`${(t/1e3).toFixed(0)} km`,i=`${(t/1609.344).toFixed(0)} mi`;return this.useMiles?i:e},getMapObjectItem(s,t,e){return this.google?new this.google.maps[s](t,e):null},addMarker(s){return this.getMapObjectItem("Marker",s)},renderMap(){const s=this.mapStyles?JSON.parse(this.mapStyles):null;this.map=this.getMapObjectItem("Map",this.$refs.map,{disableDefaultUI:!0,styles:s})},runGeoCode(){this.userGeoLocation&&this.geocoder.geocode({location:this.userGeoLocation},(s,t)=>{if(t!=="OK"||!s[0])throw new Error(t);const{location:e}=s[0].geometry,i=this.addMarker({position:e,map:this.map,icon:k.userMarker});i.addListener("mouseover",()=>{i.setIcon(k.userMarkerHover)}),i.addListener("mouseout",()=>{i.setIcon(k.userMarker)}),this.map.setCenter(e)})},getGeoLocation(){return navigator.geolocation?new Promise((s,t)=>(navigator.geolocation.getCurrentPosition(s,t),Promise.reject())):null},setGeoLocation(s){this.userGeoLocation={lat:s.coords.latitude,lng:s.coords.longitude}},getGeoLocationPermission(){return navigator.permissions?navigator.permissions.query({name:"geolocation"}).then(s=>{const t=s;this.checkStatusAndFilterLocations(t.state),t.onchange=()=>{this.checkStatusAndFilterLocations(t.state)}}):null},checkStatusAndFilterLocations(s){s==="granted"&&this.getGeoLocation().then(t=>{this.setGeoLocation(t),this.filterLocations()})},setMapHeight(){this.mapHeight&&(this.$refs.map.style.height=`${this.mapHeight}px`)},updateQuery(s){clearTimeout(is),is=setTimeout(()=>{const{value:t}=s.target;if(!t)this.searchResult=null,this.isSearchResultsEmpty=!1,this.filteredLocations=this.locations,this.setBoundsAndMarkers();else{const e=this.searchLocations(t);e.length!==0?(this.isSearchResultsEmpty=!1,this.searchResult=e,this.fitBounds(e),this.setBoundsAndMarkers()):(this.isSearchResultsEmpty=!0,this.findLocality(t))}},xu)},searchLocations(s){return this.locations.filter(t=>{const{name:e,company:i,city:n}=t;let r=!1;return[e,i,n].forEach(d=>{a.deburr(d.toLowerCase()).includes(a.deburr(s.toLowerCase()))&&(r=!0)}),r})},fitBounds(s){const t=new this.google.maps.LatLngBounds;s.forEach(e=>{t.extend(e.position)}),this.map.fitBounds(t)},findLocality(s){E(this.config.geocodeUrl,{params:{country:a.get(window,"grundfos.locale.country"),locality:s}}).then(this.zoomLocality)},zoomLocality(s){const t=a.get(s,"data.geometry.location");t&&(this.map.setCenter(t),this.map.setZoom(ss),this.level=ss)},locationSelectHandler(s){this.map.setZoom(es),this.map.setCenter(s)},markerClickHandler(s,t){this.map.setZoom(es),this.map.setCenter(s.getPosition()),this.isSearchResultsEmpty=!1,this.filteredLocations=this.filteredLocations.map((e,i)=>{const n=e;return n.isActive=!1,i===t&&(n.isActive=!0),n})},setFilteredLocations(s){this.filteredLocations=s,this.isSearchResultsEmpty=this.filteredLocations==null||!this.filteredLocations.length,this.zoomOutToResults()},zoomOutToResults(){this.level&&this.isSearchResultsEmpty?(this.level=this.level-1,this.map.setZoom(this.level)):this.level=0},setBoundsAndMarkers(){this.setBounds(),this.resetMarkers(),this.preventMaxZoomOut(),this.setFitBounds(),this.map.panToBounds(this.bounds),this.google.maps.event.addListenerOnce(this.map,"tilesloaded",()=>{this.resetMarkers()})},setBounds(){this.bounds=this.getMapObjectItem("LatLngBounds",null)},setMarkers(){return this.searchResult?this.getResultMarker(this.searchResult):this.getResultMarker(this.locations)},getResultMarker(s){const t=this.isGrundfosMarker?k.grundfosMarker:k.genericMarker,e=this.isGrundfosMarker,i=s?s.map((n,r)=>{const d=this.addMarker({position:n.position,map:this.map,icon:t});d.addListener("mouseover",()=>{d.setIcon(e?k.grundfosMarkerHover:k.genericMarkerHover)}),d.addListener("mouseout",()=>{d.setIcon(e?k.grundfosMarker:k.genericMarker)}),d.addListener("click",()=>this.markerClickHandler(d,r),!1);const u=this.getMapObjectItem("LatLng",d.position.lat(),d.position.lng());return this.bounds.extend(u),d}):null;this.markers=i},setIcon(s){return this.getMapObjectItem("Marker",s)},preventMaxZoomOut(){if(this.bounds.getNorthEast().equals(this.bounds.getSouthWest())){const s=t=>this.getMapObjectItem("LatLng",this.bounds.getNorthEast().lat()+(t?.01:-.01),this.bounds.getNorthEast().lng()+(t?.01:-.01));this.bounds.extend(s(!0)),this.bounds.extend(s()),this.map.setCenter(this.locations.position)}},groupMarkers(){this.markerClusterer=new ii(this.map,this.markers,{clusterClass:"cmp-google-map__clustericon",styles:[{textColor:"white",height:40,width:40}]})},boundsChangeHandler(){this.searchResult=null;const s=this.map.getBounds(),t=this.locations?this.locations.reduce((e,i)=>{const n=i.position.lat,r=i.position.lng;return this.markers.find(d=>{const u=d.getPosition();return u.lng()===r&&u.lat()===n&&s.contains(u)?(e.push(i),!0):!1}),e},[]):null;this.setFilteredLocations(t)},resetMarkers(){this.markerClusterer&&this.markerClusterer.clearMarkers(),this.setMarkers(),this.groupMarkers()},setFitBounds(){this.map.fitBounds(this.bounds,{left:this.isMobile?0:ku})},loadControls(){this.map.controls[this.google.maps.ControlPosition.RIGHT_BOTTOM].push(this.$refs.zoomControls)},zoomIn(){this.map.setZoom(this.map.getZoom()+1)},zoomOut(){this.map.setZoom(this.map.getZoom()-1)}}};var Lu=function(){var t=this,e=t._self._c;return e("section",{staticClass:"cmp-google-map"},[e("div",{ref:"map",staticClass:"cmp-google-map-container",on:{click:t.closeCard}},[t.gmapsApiError?e("div",{staticClass:"cmp-alert"},[e("div",{staticClass:"cmp-text cmp-alert__message"},[e("p",[t._v(t._s(t.gmapsApiError))])])]):t._e(),t.isMobile?t._e():e("div",{ref:"zoomControls"},[e("div",{staticClass:"cmp-google-map__controls"},[e("button",{staticClass:"elm-link cmp-google-map__btn cmp-google-map__btn--geolocate",attrs:{type:"button"},on:{click:t.runGeoCode}})]),e("div",{staticClass:"cmp-google-map__controls"},[e("button",{staticClass:"elm-link cmp-google-map__btn cmp-google-map__btn--zoom-in",attrs:{type:"button"},on:{click:t.zoomIn}},[t._v(" "+t._s(t.labels.zoomIn)+" ")]),e("button",{staticClass:"elm-link cmp-google-map__btn cmp-google-map__btn--zoom-out",attrs:{type:"button"},on:{click:t.zoomOut}},[t._v(" "+t._s(t.labels.zoomOut)+" ")])])])]),t.gmapsApiError?t._e():t._t("sidebar",null,{locations:t.filteredLocations,updateQuery:t.updateQuery,isSearchResultsEmpty:t.isSearchResultsEmpty,locationSelectHandler:t.locationSelectHandler})],2)},Eu=[],Iu=l(wu,Lu,Eu,!1,null,null,null,null);const as=Iu.exports,ns=20,Au=12,Fu=270,Ou=["uk","gb","us"],Mu="grundfos",Pu={name:"CmpMap",mixins:[_,y,c],props:{allLocations:{type:Array,default:null},defaultZoom:{type:String,default:"1"},labels:{type:Object,default:()=>({})},locationType:{type:String,default:null},mapStyles:{type:[Object,String],default:null},mapHeight:{type:String,default:"100%"},mapProvider:{type:String,default:null},selectedMarker:{type:String,default:null}},data(){return{map:null,userGeoLocation:null,locations:null,filteredLocations:null,latLngBounds:null,searchResult:null,markers:null,markerClusterGroup:null,isSearchResultsEmpty:!1}},computed:{computedLocations(){return`${this.locationType}|${this.allLocations}`},useMiles(){const s=a.get(window,"grundfos.locale.country");return Ou.includes(s)},isGrundfosMarker(){return this.selectedMarker===Mu}},watch:{computedLocations(){this.init()}},mounted(){this.renderMap(),this.getGeoLocation().then(this.setGeoLocation),this.getGeoLocationPermission(),this.allLocations&&this.init()},beforeDestroy(){this.map.remove()},methods:{renderMap(){if(this.setDefaultIcon(),this.$refs.map.style.height=`${this.mapHeight}px`,this.map=window.L.map("map").setView([0,0],this.defaultZoom),this.mapProvider){const s=JSON.parse(this.mapProvider);window.L.tileLayer(s.url,s.options).addTo(this.map)}},setDefaultIcon(){window.L.Marker.prototype.options.icon=window.L.divIcon(this.isGrundfosMarker?{className:"cmp-map__icon--grundfos",iconSize:[46,46],iconAnchor:[23,46]}:{className:"cmp-map__icon",iconSize:[40,40],iconAnchor:[20,40]})},getGeoLocationPermission(){navigator.permissions&&navigator.permissions.query({name:"geolocation"}).then(s=>{const t=s;this.checkStatusAndGetGeoLocation(t.state),t.onchange=()=>{this.checkStatusAndGetGeoLocation(t.state)}})},checkStatusAndGetGeoLocation(s){s==="granted"&&this.getGeoLocation().then(this.setGeoLocation)},getGeoLocation(){return navigator.geolocation?new Promise((s,t)=>{navigator.geolocation.getCurrentPosition(s,t)}):null},setGeoLocation(s){this.userGeoLocation={lat:s.coords.latitude,lng:s.coords.longitude}},init(){this.filterLocations(),this.latLngBounds=window.L.latLngBounds(),this.setBoundsAndMarkers(),this.map.on("moveend",this.boundsChangeHandler),this.loadControls()},filterLocations(){this.allLocations&&this.locationType&&(this.locations=this.allLocations.filter(s=>{if(s.type.toLowerCase()===this.locationType.toLowerCase()){const t=s;return this.userGeoLocation&&(t.distance=this.getDistance(s)),t}return null}),this.locations.sort((s,t)=>parseInt(s.distance,10)-parseInt(t.distance,10)),this.filteredLocations=this.locations)},getDistance(s){const t=window.L.circleMarker([s.position.lat,s.position.lng]),e=window.L.circleMarker([this.userGeoLocation.lat,this.userGeoLocation.lng]),i=t.getLatLng().distanceTo(e.getLatLng()).toFixed(0),n=`${(i/1e3).toFixed(0)} km`,r=`${(i/1609.344).toFixed(0)} mi`;return this.useMiles?r:n},setBoundsAndMarkers(){this.getResultMarkers(this.searchResult?this.searchResult:this.locations),this.groupMarkers(),this.setFitBounds()},getResultMarkers(s){s&&(this.markers=s.map(t=>{const e=window.L.marker(t.position);return e.on("click",this.markerClickHandler),this.latLngBounds.extend(e.getLatLng()),e}))},markerClickHandler(s){const t=s.sourceTarget;this.map.setView(t.getLatLng(),ns),this.isSearchResultsEmpty=!1},groupMarkers(){this.markerClusterGroup?this.markerClusterGroup.clearLayers():(this.markerClusterGroup=window.L.markerClusterGroup({showCoverageOnHover:!1,iconCreateFunction:this.createGroupIcon}),this.map.addLayer(this.markerClusterGroup)),this.markers&&this.markerClusterGroup.addLayers(this.markers)},createGroupIcon(s){return window.L.divIcon({className:"cmp-map__clustericon",iconSize:[30,30],html:`<div class="cmp-map__clustericon--div">${s.getChildCount()}</div>`})},boundsChangeHandler(){this.getResultMarkers(this.locations),this.groupMarkers();const s=this.map.getBounds();this.filteredLocations=this.locations?this.locations.reduce((t,e)=>(this.markers.find(i=>{const n=i.getLatLng();return n.lng===e.position.lng&&n.lat===e.position.lat&&s.contains(n)?(t.push(e),!0):!1}),t),[]):null,this.isSearchResultsEmpty=this.filteredLocations==null||!this.filteredLocations.length},loadControls(){this.map.zoomControl.remove();const{zoomControls:s}=this.$refs;window.L.Control.GeoLocate=window.L.Control.extend({onAdd(){return s},onRemove(){}}),window.L.control.geoLocate=t=>new window.L.Control.GeoLocate(t),window.L.control.geoLocate({position:"bottomright"}).addTo(this.map)},runGeoCode(){this.userGeoLocation&&(window.L.marker(this.userGeoLocation,{icon:window.L.divIcon({className:"cmp-map__icon--user",iconSize:[46,46],iconAnchor:[23,46]})}).addTo(this.map),this.latLngBounds.extend(this.userGeoLocation),this.setFitBounds())},setFitBounds(){this.latLngBounds.isValid()&&this.map.fitBounds(this.latLngBounds,{paddingTopLeft:this.isMobile?[0,0]:[Fu,0]})},updateQuery(s){const{value:t}=s.target;if(!t)this.searchResult=null,this.isSearchResultsEmpty=!1,this.filteredLocations=this.locations,this.setBoundsAndMarkers();else{const e=this.searchLocations(t);e.length!==0?(this.isSearchResultsEmpty=!1,this.searchResult=e,this.latLngBounds=window.L.latLngBounds(),this.getResultMarkers(this.searchResult),this.markerClusterGroup.clearLayers(),this.markerClusterGroup.addLayers(this.markers),this.setFitBounds()):(this.isSearchResultsEmpty=!0,this.findLocality(t))}},searchLocations(s){return this.locations.filter(t=>{const{name:e,company:i,city:n}=t;let r=!1;return[e,i,n].forEach(d=>{d.toLowerCase().includes(s.toLowerCase())&&(r=!0)}),r})},findLocality(s){E(this.config.geocodeUrl,{params:{country:a.get(window,"grundfos.locale.country"),locality:s}}).then(this.zoomLocality)},zoomLocality(s){const t=a.get(s,"data.geometry.location");t&&this.map.setView([t.lat,t.lng],Au)},locationSelectHandler(s){this.map.setView(s,ns)},zoomIn(){this.map.setZoom(this.map.getZoom()+1)},zoomOut(){this.map.setZoom(this.map.getZoom()-1)}}};var Ru=function(){var t=this,e=t._self._c;return e("section",{staticClass:"cmp-google-map"},[e("div",{staticClass:"cmp-google-map-container"},[e("div",{ref:"map",staticStyle:{height:"100%"},attrs:{id:"map"}}),e("div",{ref:"zoomControls"},[e("div",{staticClass:"cmp-google-map__controls cmp-google-map__controls"},[e("button",{staticClass:"elm-link cmp-google-map__btn cmp-google-map__btn--geolocate",attrs:{type:"button"},on:{click:t.runGeoCode}})]),e("div",{staticClass:"cmp-google-map__controls cmp-google-map__controls"},[e("button",{staticClass:"elm-link cmp-google-map__btn cmp-google-map__btn--zoom-in",attrs:{type:"button"},on:{click:t.zoomIn}},[t._v(" "+t._s(t.labels.zoomIn)+" ")]),e("button",{staticClass:"elm-link cmp-google-map__btn cmp-google-map__btn--zoom-out",attrs:{type:"button"},on:{click:t.zoomOut}},[t._v(" "+t._s(t.labels.zoomOut)+" ")])])])]),t._t("sidebar",null,{locations:t.filteredLocations,updateQuery:t.updateQuery,isSearchResultsEmpty:t.isSearchResultsEmpty,locationSelectHandler:t.locationSelectHandler})],2)},Nu=[],Hu=l(Pu,Ru,Nu,!1,null,null,null,null);const rs=Hu.exports,Bu={name:"CmpWhereToBuyUpload",mixins:[y,c],props:{labels:{type:Object,default:()=>({})}},data(){return{file:null,successMsg:!1,errorMsg:!1,errorEmptyMsg:!1,fieldForm:!0}},computed:{errorMessage(){return this.errorMsg||this.errorEmptyMsg?this.errorMsg?this.labels.errorMsg:this.labels.errorEmptyMsg:null}},methods:{upload(){const{files:s}=this.$refs.input.$refs.input,{0:t}=s;this.file=t,this.errorMsg=!1,this.file&&(this.errorEmptyMsg=!1,this.submit())},submit(s){if(this.successMsg)this.reset();else{const t={headers:{"Content-Type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}};if(this.successMsg=!1,this.errorMsg=!1,this.errorEmptyMsg=!1,!this.file){s.preventDefault(),this.errorEmptyMsg=!0;return}gt(this.config.apiUrl,this.file,t).then(()=>{this.successMsg=!0,this.fieldForm=!1}).catch(()=>{this.errorMsg=!0})}},download(){window.location.href=this.config.apiUrl},reset(){this.file="",this.fieldForm=!0,this.successMsg=!1}}};var Du=function(){var t=this,e=t._self._c;return e("form",{staticClass:"cmp-where-to-buy-upload",on:{submit:function(i){return i.preventDefault(),t.download.apply(null,arguments)}}},[e("cmp-form-file",{ref:"input",attrs:{id:"file",label:t.labels.fileUpload,"has-error":t.errorMsg||t.errorEmptyMsg,"button-label":t.successMsg?t.labels.reset:t.labels.upload,"error-message":t.errorMessage,"update-handler":t.upload}}),t.successMsg?e("p",[t._v(" "+t._s(t.labels.successMsg))]):t._e(),e("label",{staticClass:"cmp-form-text__label",attrs:{for:"download"}},[t._v(t._s(t.labels.fileDownload))]),e("button",{staticClass:"elm-button elm-button--small elm-button--icon-download_outline cmp-form-text__file-button",attrs:{id:"download",type:"submit"}},[e("span",{staticClass:"elm-button__text"},[t._v(t._s(t.labels.download))])])],1)},qu=[],zu=l(Bu,Du,qu,!1,null,null,null,null);const os=zu.exports,{mapMutations:Vu}=p,Uu={name:"CmpVideoOverlay",mixins:[v,c],props:{buttonClass:{type:String,default:"elm-round-button elm-round-button--align-middle elm-round-button--medium elm-round-button--icon-play_filled"},buttonTextClass:{type:String,default:null},label:{type:String,default:"play button"},fullScreen:{type:Boolean,default:!1},heading:{type:String,default:null},hideLabel:{type:Boolean,default:!1}},computed:{defaultButtonTextClass(){return a.trim([`${a.first(this.buttonClass.split(" "))}__text`,this.hideLabel?"h-hidden":""].join(" "))}},methods:{...Vu("base",["setOverlayComponent"]),setOverlay(){const s=this.$slots.default;this.setOverlayComponent({component:"CmpEmbedded",fullScreen:this.fullScreen,heading:this.heading,props:{children:s},transparent:!0})}}};var ju=function(){var t=this,e=t._self._c;return e("div",[t.isEditMode?t._e():e("button",{class:t.buttonClass,attrs:{"aria-label":t.label,"aria-controls":"overlay","data-qa":"video-control-button",type:"button"},on:{click:t.setOverlay}},[e("span",{class:t.buttonTextClass||t.defaultButtonTextClass},[t._v(t._s(t.label))])]),t.isEditMode?t._t("default"):t._e()],2)},Gu=[],Wu=l(Uu,ju,Gu,!1,null,null,null,null);const ls=Wu.exports,{mapActions:Qu,mapGetters:Ku,mapState:Yu}=p,Zu="gpcAddToSavedItems",Ju={name:"ElmActionButtonSavedDocumentsAdd",mixins:[c],props:{id:{type:String,required:!0},labels:{type:Object,default:()=>({})},selectedItems:{type:Array,required:!0}},computed:{...Yu("base/user",["isLoggedIn"]),...Ku("productCenter/savedItems/products",["isLoading"]),hasSavedDocuments(){return!!a.get(this.$store,"state.productCenter.savedItems.documents",!1)}},methods:{...Qu("productCenter/savedItems/documents",{addDocumentsToSavedItems:"addDocuments"}),addDocuments(){this.addDocumentsToSavedItems(this.selectedItems.map(({fileid:s,description:t,documenttypecode:e,revision:i,title:n})=>({fileid:s,description:t,documenttype:e,historyid:i||0,title:n}))),f(null,Zu)}}};var Xu=function(){var t=this,e=t._self._c;return t.isLoggedIn&&t.hasSavedDocuments?e("button",{directives:[{name:"tooltip",rawName:"v-tooltip:top",value:t.labels.text,expression:"labels.text",arg:"top"}],attrs:{id:t.id,disabled:!t.selectedItems.length||t.isLoading,type:"button"},on:{click:function(i){return i.preventDefault(),t.addDocuments.apply(null,arguments)}}},[e("span",{staticClass:"elm-action-button__text"},[t._v(t._s(t.labels.text))])]):t._e()},tp=[],ep=l(Ju,Xu,tp,!1,null,null,null,null);const cs=ep.exports,sp={name:"ElmDynamicMedia",mixins:[v],props:{mediaTracker:{type:Function,default:ws}},mounted(){try{zs.init(this.$el,this.mediaTracker)}catch(s){this.errorHandler(s)}},methods:{errorHandler(s){console.error(s),console.warn("Unable to initialise DynamicMedia.")}},render(){return this.$slots.default[0]}},ip=null,ap=null;var np=l(sp,ip,ap,!1,null,null,null,null);const ds=np.exports,{mapState:rp}=p,B="auto",D="100%",Rs="cover",Ns="contain",op=new Set([null,Ns,Rs]),us="img-loading",ps="eager",lp={name:"ElmImg",mixins:[v,c],props:{alt:{type:String,default:""},fillMode:{type:String,default:null,validator:s=>op.has(s)},imageLoadErrorHandler:{type:Function,default:a.noop},src:{type:[String,Object],default:null},srcset:{type:[String,Array],default:null},srcFormat:{type:String,default:null},useDpr:{type:Boolean,default:!0},x:{type:[Number,String],default:.5},y:{type:[Number,String],default:.5}},data(){return{ACTIVE:$s,LOADING:x,isLoading:!1,currentSrc:null,imgStyles:null,imgLoading:"lazy"}},computed:{...rp("base",["viewportSize"]),isActive(){return!this.isEditMode&&!!this.fillMode},isEmbedded(){return!!this.$slots.default},baseSrc(){return this.isEmbedded?null:a.isPlainObject(this.src)?this.getSrcObj(this.src).src:this.src},srcList(){return this.isEmbedded?[]:a.isArray(this.srcset)?a.sortBy(this.srcset.map(this.getSrcObj),["width"]):a.isString(this.srcset)?a.sortBy(this.srcset.split(",").map(s=>{const[t,e]=a.trim(a.toString(s)).split(" ");return{src:t,width:a.toNumber(a.toString(e).replace(/\D+/g,""))}}),["width"]):[]}},mounted(){const s=this.getRefImg();s&&this.addImageListener(s),this.setImgLoading(),this.isEmbedded?(this.mutationObserver=new MutationObserver(t=>{t.forEach(e=>{Array.from(e.addedNodes).forEach(this.addImageListener),Array.from(e.removedNodes).forEach(this.removeImageListener)})}),this.mutationObserver.observe(this.$refs.hidden,{subtree:!0,childList:!0,characterData:!0})):this.$nextTick().then(this.setCurrentSrc),this.isEmbedded||(this.$watch("src",this.setCurrentSrc),this.$watch("srcset",this.setCurrentSrc)),this.$watch("viewportSize",this.isEmbedded?this.setFill:this.setCurrentSrc)},beforeDestroy(){this.mutationObserver&&this.mutationObserver.disconnect()},methods:{getRefImg(){return this.isEmbedded?this.$refs.hidden.querySelector("img"):this.$refs.img},getSrcObj(s){const{src:t,width:e,height:i}=s;if(!this.srcFormat||!t)return s;const n=t.split("?")[0],r=rt.parse(this.srcFormat.replace(" ","").replace("?","").replace("{width}",e).replace("{height}",i));return{src:`${n}?${rt.stringify(Object.assign({},S(t),r),{encode:!1})}`,width:e}},getTransform(s,t,e){const i=(s-t)/s/2,n=.5-i,r=.5+i;return n>e?n:r<e?r:e},getContainStyles(s,t){const e="translate(-50%, -50%)",i=s>t?D:B,n=s>t?B:D;return{height:i,transform:e,width:n}},getCoverStyles(s,t,e,i){let n,r,d;if(s>t){n=B,r=D;const u=s/t*e;d=`translate(-50%, ${this.getTransform(u,e,this.y)*-100}%)`}else{n=D,r=B;const u=t/s*i;d=`translate(${this.getTransform(u,i,this.x)*-100}%, -50%)`}return{height:n,transform:d,width:r}},setCurrentSrc(){if(this.$el.offsetParent===null||!this.getRefImg())return;const t=this.useDpr?window.devicePixelRatio:1,e=this.$el.getBoundingClientRect().width*t,i=this.srcList.reduce((n,r,d)=>n.width?n:r.width>=e||d===this.srcList.length-1&&e>r.width?r:n,{src:this.baseSrc,width:0}).src;i!==this.currentSrc?(this.imgStyles={opacity:0},this.currentSrc=i):this.setFill()},syncCurrentSrc(s){this.currentSrc=a.get(s,"target.src",null),this.isLoading=!1},setFill(){if(this.$el.offsetParent===null)return;const s=this.getRefImg();if(!s)return;if(!this.isActive){this.imgStyles={opacity:1};return}const{naturalWidth:t,naturalHeight:e}=s,{width:i,height:n}=this.$el.getBoundingClientRect(),r=i/n,d=t/e;let u;switch(this.fillMode){case Ns:u=this.getContainStyles(r,d);break;case Rs:u=this.getCoverStyles(r,d,n,i);break;default:u={};break}this.imgStyles=Object.assign({opacity:1},u)},addImageListener(s){const t=s.tagName==="IMG"?s:s.querySelector("img");t&&(this.isLoading=!0,t.addEventListener("load",this.syncCurrentSrc),t.complete&&this.setFill(),t.addEventListener("error",this.errorHandler))},errorHandler(){this.isLoading=!1,this.imageLoadErrorHandler()},removeImageListener(s){const t=s.tagName==="IMG"?s:s.querySelector("img");t&&(t.removeEventListener("load",this.syncCurrentSrc),t.removeEventListener("error",this.errorHandler))},setImgLoading(){const t=this.getRefImg()||this.$refs.img;if(!t)return;const e=t.closest(`[${us}]`);e!==null&&e.getAttribute(us)===ps&&(this.imgLoading=ps)}}};var cp=function(){var t=this,e=t._self._c;return e("div",{class:[t.isActive&&t.ACTIVE,t.isLoading&&t.LOADING]},[e("img",{ref:"img",staticClass:"elm-img__asset",style:t.imgStyles,attrs:{alt:t.alt,src:t.currentSrc,loading:t.imgLoading},on:{load:t.setFill}}),t.isEmbedded?e("div",{ref:"hidden",staticClass:"elm-img__hidden"},[t._t("default")],2):t._e()])},dp=[],up=l(lp,cp,dp,!1,null,null,null,null);const ms=up.exports,{mapState:pp,mapMutations:mp}=p,hp={name:"ElmListViewSelector",mixins:[c],props:{id:{type:String,required:!0},labels:{type:Object,default:()=>({})}},computed:{...pp("base",["useListView"])},methods:{...mp("base",["setUseListView"]),toggleView({target:{value:s}}){this.setUseListView(s==="true")}}};var _p=function(){var t=this,e=t._self._c;return e("fieldset",{staticClass:"elm-list-view-selector"},[e("div",{staticClass:"elm-list-view-selector__display"},[e("legend",{staticClass:"elm-list-view-selector__legend"},[t._v(" "+t._s(t.labels.view)+" ")]),e("div",{staticClass:"elm-list-view-selector__option"},[e("input",{staticClass:"elm-list-view-selector__option-field",attrs:{id:t._f("kebabCase")(`${t.id}-grid`),name:t.id,type:"radio"},domProps:{checked:!t.useListView,value:!1},on:{change:t.toggleView}}),e("label",{staticClass:"elm-list-view-selector__option-label elm-list-view-selector__option--icon-grid-view_outline",attrs:{for:t._f("kebabCase")(`${t.id}-grid`)}},[t._v(" "+t._s(t.labels.grid)+" ")])]),e("div",{staticClass:"elm-list-view-selector__option"},[e("input",{staticClass:"elm-list-view-selector__option-field",attrs:{id:t._f("kebabCase")(`${t.id}-list`),name:t.id,type:"radio"},domProps:{checked:t.useListView,value:!0},on:{change:t.toggleView}}),e("label",{staticClass:"elm-list-view-selector__option-label elm-list-view-selector__option--icon-list-view_outline",attrs:{for:t._f("kebabCase")(`${t.id}-list`)}},[t._v(" "+t._s(t.labels.list)+" ")])])])])},fp=[],gp=l(hp,_p,fp,!1,null,null,null,null);const hs=gp.exports,bp="https://www.youtube.com/iframe_api";let q;function vp(){return q||(q=new Promise((s,t)=>{try{const e=document.createElement("script");e.src=bp;const i=document.getElementsByTagName("script")[0];i.parentNode.insertBefore(e,i),window.onYouTubeIframeAPIReady=()=>{s(window.YT)}}catch(e){t(e)}}),q)}const yp=1e3,_s=[.25,.5,.75],Cp={name:"ElmYoutube",mixins:[v,c],props:{autoplay:{type:Boolean,default:!1},poster:{type:Boolean,default:!1},height:{type:[Number,String],default:"100%"},language:{type:String,required:!0},videoId:{type:String,required:!0},title:{type:String,required:!1,default:""},width:{type:[Number,String],default:"100%"},duration:{type:String,default:""}},data(){return{player:null,hasStarted:!1,id:null,milestones:[].concat(_s),playerLastCurrentTime:null,previousPlayerState:null,recentlyTrackedSkip:!1}},mounted(){this.id=`youtube-${this._uid}`,this.$nextTick().then(vp).then(this.initPlayer)},beforeDestroy(){this.player&&this.player.destroy(),this.mediaTracker&&this.hasStarted&&this.mediaTracker.trackSessionEnd(),this.playbackTracker&&clearInterval(this.playbackTracker)},methods:{initPlayer(s){this.player=new s.Player(this.id,{height:this.height,videoId:this.videoId,title:`video: ${this.title}`,width:this.width,host:"https://www.youtube-nocookie.com",playerVars:{autoplay:this.autoplayEnabled()?1:0,cc_lang_pref:this.language,hl:this.language,modestbranding:1,rel:0,mute:this.mutingEnabled()?1:0},events:{onStateChange:this.trackMediaEvent(s)}}),this.mediaTracker=ws({id:this.videoId,title:`https://www.youtube.com/watch?v=${this.videoId}`,type:"YouTube",autoplay:this.autoplay.toString(),loop:(!1).toString(),videoLengthSec:this.getTimeInSeconds().toString()})},autoplayEnabled(){return!this.isEditMode&&this.autoplay},mutingEnabled(){return this.autoplayEnabled()&&!this.poster},trackMediaEvent(s){return({data:t})=>{if(this.playerLastCurrentTime!==null&&!this.recentlyTrackedSkip&&Math.abs(this.player.getCurrentTime()-this.playerLastCurrentTime-1)>2&&(this.mediaTracker.trackSkip(),this.recentlyTrackedSkip=!0,setTimeout(()=>{this.recentlyTrackedSkip=!1},1e3)),!this.recentlyTrackedSkip)switch(t){case s.PlayerState.ENDED:this.videoEnded();break;case s.PlayerState.PLAYING:this.videoPlaying(s);break;case s.PlayerState.PAUSED:this.videoPaused(s);break}this.previousPlayerState=t}},trackPlayback(){if(!this.player)return;this.playerLastCurrentTime=this.player.getCurrentTime();const s=this.player.getCurrentTime()/this.player.getDuration();for(let t=0;t<this.milestones.length;t++){const e=this.milestones[t];if(s>=e){this.mediaTracker.trackEvent(`${e*100}% played`),this.milestones.splice(t,1);break}}},videoEnded(){clearInterval(this.playbackTracker),this.mediaTracker.trackComplete(),this.milestones=[].concat(_s)},videoPlaying(s){clearInterval(this.playbackTracker),this.hasStarted?(this.isPaused||this.previousPlayerState!==s.PlayerState.BUFFERING)&&(this.mediaTracker.trackPlay(),this.isPaused=!1):(this.mediaTracker.trackSessionStart(),this.hasStarted=!0),this.playbackTracker=setInterval(this.trackPlayback,yp)},videoPaused(s){clearInterval(this.playbackTracker),this.previousPlayerState!==s.PlayerState.BUFFERING&&(this.isPaused=!0,this.mediaTracker.trackPause())},getTimeInSeconds(){return this.isTimeSet(this.duration)?this.getTotalSeconds(this.duration):""},getTotalSeconds(s){const[t,e,i]=s.split(":").map(Number);return t*3600+e*60+i},isTimeSet(s){return typeof s=="string"&&s.length>0}}};var Sp=function(){var t=this,e=t._self._c;return e("div",{attrs:{id:t.id}})},kp=[],Tp=l(Cp,Sp,kp,!1,null,null,null,null);const fs=Tp.exports,A="asc",_t="desc",{mapState:$p}=p,xp={name:"ModResultList",mixins:[y,c],props:{id:{type:String,required:!0},fullWidth:{type:Boolean,default:!1},fullWidthWithLeftSideFilter:{type:Boolean,default:!1},labels:{type:Object,default:()=>({})},naturalFacetOrdering:{type:Boolean,default:!1},resultsComponent:{type:String,required:!0},scrollOffset:{type:Number,default:0},useQuery:{type:[Array,Boolean],default:!1},sessionStorageKey:{type:String,default:null},resultCountHandler:{type:Function,default:a.noop},pollingMethod:{type:Function,required:!1,default:a.noop},qcId:{type:String,required:!1,default:null},usePolling:{type:Boolean,default:!1,required:!1},isVueButtonEnabled:{type:Boolean,default:!1}},data(){return{LOADING:x,content:null}},computed:{...$p("base",["mainSize"]),apiUrl(){return this.config.apiUrl},currentRequestState(){return{filteritems:this.compactSelectedFacets(this.facets),sortId:a.get(this.results,"sortId"),sortOrder:a.get(this.results,"sortOrder"),pageNumber:a.get(this.pagination,"currentPage"),resultsId:this.id}},hasFacets(){return!!(this.facets&&this.facets.length)},hasResults(){return!!a.get(this.results,"items.length")},isLoading(){return a.get(this.content,"loading")},facets(){return a.get(this.content,"data.filteritems")},results(){return a.get(this.content,"data.results")},pagination(){return a.get(this.content,"data.pagination")},resultsId(){return a.kebabCase(`${this.id}-results`)},pageUrl(){return M(window.location.href,this.currentRequestState)},isFullWidthLeftSideFilterWithFacets(){return this.fullWidthWithLeftSideFilter&&this.hasFacets},isFullWidthLeftSideFilterWithoutFacets(){return this.fullWidthWithLeftSideFilter&&!this.hasFacets},isAnyFullWidth(){return this.fullWidth||this.fullWidthWithLeftSideFilter},minWidthOfAnyFullWidth(){let s=null;return(this.isFullWidthLeftSideFilterWithoutFacets||this.fullWidth)&&(s=this.mainSize.width),this.isFullWidthLeftSideFilterWithFacets&&(s=this.mainSize.width-376),s?`min-width: ${s}px`:null}},watch:{pagination({total:s}){this.resultCountHandler(s||0)},apiUrl(){this.getContent(this.facets)}},mounted(){const{resultsId:s,...t}=S(window.location.search);if(a.isEmpty(this.initialState)){const{filteritems:e,pageNumber:i,sortId:n,sortOrder:r}=Object.assign({},this.useQuery&&s===this.id&&Object.assign(this.getAdditionalQueryParams(),a.pick(t,["filteritems","pageNumber","sortId","sortOrder"]))),d=this.sessionStorageKey?a.get(window.sessionStorage,this.sessionStorageKey):!1;if(d){const u=JSON.parse(d);this.getContent(u.filteritems,u.pageNumber,u.sortId,u.sortOrder)}else this.getContent(this.expandSelectedFacets(e),i,n,r)}},methods:{compactSelectedFacets(s=[]){return s.reduce((t,{label:e,filtervalues:i})=>{const n=i.filter(({selected:r})=>!!r).map(({basevalue:r})=>r);return Object.assign(t,n.length&&{[e]:n})},{})},expandSelectedFacets(s={}){const t=e=>({basevalue:e,selected:!0});return Object.entries(s).reduce((e,[i,n])=>e.concat({label:i,filtervalues:a.isArray(n)?n.map(t):[t(n)]}),[])},syncQueryParams(){T(this.currentRequestState)},getAdditionalQueryParams(){return a.pick(S(window.location.search),a.isArray(this.useQuery)?this.useQuery:[])},getResult(s){return this.usePolling?this.pollingMethod(s,this.qcId,this.apiUrl):gt(this.apiUrl,s)},getContent(s,t,e,i){this.setContent(null);const n=Object.assign({},this.useQuery?this.getAdditionalQueryParams():{},{filteritems:s||[],pageNumber:t||1,sortId:e||a.get(this.results,"sortId"),sortOrder:i||a.get(this.results,"sortOrder")||_t,total:a.get(this.pagination,"total")},this.config.params);return this.sessionStorageKey&&window.sessionStorage.setItem(this.sessionStorageKey,JSON.stringify(n)),this.getResult(n).then(this.setContent).catch(this.setContent).then(this.useQuery?this.syncQueryParams:a.noop)},setContent(s){this.content=L(s,this.content)},setFacets(...s){return this.getContent(...s).then(this.setScroll)},setPage(s){return this.getContent(this.facets,s).then(this.setScroll)},setScroll(){K(this.$el,!0,{top:this.scrollOffset})},setSort(s){let t;return s===a.get(this.results,"sortId")&&(t=a.get(this.results,"sortOrder")===A?_t:A),this.getContent(this.facets,1,s,t).then(this.setScroll)}}};var wp=function(){var t=this,e=t._self._c;return e("section",{class:t.isLoading&&!t.hasResults&&t.LOADING,attrs:{"aria-live":"polite"}},[e("div",{staticClass:"b-layout-grid"},[e("div",{class:[t.isFullWidthLeftSideFilterWithFacets&&"mod-result-list--full-width-with-left-side-filter"]},[e("div",{class:[!t.isFullWidthLeftSideFilterWithFacets&&"b-layout-grid__group",t.isFullWidthLeftSideFilterWithFacets&&"mod-result-list--full-width-with-left-side-filter-inner"]},[t.hasFacets?e("aside",{class:["b-layout-grid__item",!t.fullWidthWithLeftSideFilter&&"b-layout-grid__item--12",!t.isAnyFullWidth&&"b-layout-grid__item--large-4",t.fullWidthWithLeftSideFilter&&"mod-result-list__aside--full-width-with-left-side-filter","b-layout-grid__item--xlarge-3"]},[e("cmp-facets-container",{staticClass:"cmp-facets-container",attrs:{"controls-id":t.resultsId,items:t.facets,"is-loading":t.isLoading,labels:t.labels.facets,"natural-facet-ordering":t.naturalFacetOrdering,"reset-handler":t.setFacets,"update-handler":t.setFacets,"use-overlay":t.fullWidth}})],1):t._e(),t.hasResults?e("div",{class:["b-layout-grid__item",!t.fullWidthWithLeftSideFilter&&"b-layout-grid__item--12",!t.isAnyFullWidth&&t.hasFacets&&"b-layout-grid__item--large-8",t.isFullWidthLeftSideFilterWithFacets&&"mod-result-list__results-grid--full-width-with-left-side-filter",t.hasFacets&&"b-layout-grid__item--xlarge-9"]},[e("div",{class:["mod-result-list__results",(t.fullWidth||t.isFullWidthLeftSideFilterWithoutFacets)&&"mod-result-list__results--full-width"]},[e("div",{staticClass:"mod-result-list__results-inner",style:t.minWidthOfAnyFullWidth},[e(t.resultsComponent,t._b({tag:"component",class:t.isLoading&&t.LOADING,attrs:{config:t.config,id:t.resultsId,labels:t.labels.results,total:t.pagination&&t.pagination.total,"update-handler":t.setSort,"is-vue-button-enabled":t.isVueButtonEnabled}},"component",t.results,!1))],1)]),t.pagination&&t.hasResults&&!t.config.hidePagination?e("cmp-pagination",t._b({staticClass:"cmp-pagination",attrs:{"controls-id":t.resultsId,id:t._f("kebabCase")(`${t.id}-pagination`),labels:t.labels.pagination,"page-url":t.pageUrl,"update-handler":t.setPage}},"cmp-pagination",t.pagination,!1)):t._e()],1):t.isLoading?t._e():e("div",{class:["b-layout-grid__item","b-layout-grid__item--12",!t.fullWidth&&t.hasFacets&&"b-layout-grid__item--large-8","b-layout-grid__item--xlarge-9"]},[e("div",{staticClass:"cmp-alert"},[e("p",{staticClass:"cmp-alert__message"},[t._v(t._s(t.labels.results.noResults))])])])])])])])},Lp=[],Ep=l(xp,wp,Lp,!1,null,null,null,null);const gs=Ep.exports;function bs(s){const t=[];return s.forEach(e=>{const i=e.filtervalues.reduce((n,r)=>(r.selected===!0&&n.push(`${e.text.toLowerCase()}|${r.displayvalue.toLowerCase()}`),n),[]);t.push(...i)}),t.join(",")}const{mapState:Ip}=p,Ap=`query search(
	$q: String
	$site: SitesEnum!
	$limit: Int
	$offset: Int
	$sort: [SortOptionsEnum]
	$facets: [Facet]
	$filters: [FilterItem]
	$mustMatchAll: Boolean
) {
	results: search(
		q: $q
		site: $site
		limit: $limit
		offset: $offset
		sort: $sort
		facets: $facets
		filters: $filters
		mustMatchAll: $mustMatchAll
	) {
		items {
			uuid
			metadata {
				baseData {
					title
					desc: description
					url
					canonicalUrl: url
					imageSrc: thumbnail
					category
					pageType
				}
				... on CMSMetadata {
					contentType
					pageData {
						processingData {
							published
						}
					}
				}
				... on ProductVariantMetadata {
					productName
					productNumber
					frequency
					discontinued
					codepipe
					matpumph
					phase
					p2kw
					ieetaexp
					ieeffclas
					pipecon
					pipepres
					shaftseal
					u
					ambmaxc
					ambminc
					codemat
					codepump
					eta100
					flownomin
					headnomm
					i
					liqumxc
					matim
					mei
					model
					motprotec
					motortype
					npump
					pipecono
					pmaxbar
					cableplug
					f
					n
					impelnom
					startform
					ipclass
					pipeconi
					expumps1
					insulatio
					dualfreq
					approval
					pipestd
					particle
					imaxa
					matdoshe
					matgask
					lviNumber
					nrfNumber
					vvsNumber
					rskNumber
				}
			}
		}
		filteritems: facets {
			label: field
			text: field
			filtervalues: items {
				basevalue: value
				displayvalue: value
				count
			}
		}
		count
	}
}`,Fp="faq-list",Op="horizontal-scrolling",Mp="DISCONTINUED",et="FREQUENCY",Pp="PAGE_CONTENT_TYPE",Rp=1e3,Np={name:"ModSearchResultList",mixins:[y,c],props:{id:{type:String,required:!0},collapseFacets:{type:Boolean,default:!1},fullWidth:{type:Boolean,default:!1},isElasticSearch:{type:Boolean,default:!1},labels:{type:Object,default:()=>({})},naturalFacetOrdering:{type:Boolean,default:!1},useConfigFacetOrder:{type:Boolean,default:!1},resultsComponent:{type:String,required:!0},displayAs:{type:String,required:!1,default:null},sortBy:{type:String,required:!1,default:null},scrollOffset:{type:Number,default:0},useQuery:{type:[Array,Boolean],default:!1},sessionStorageKey:{type:String,default:null},resultCountHandler:{type:Function,default:a.noop},tabId:{type:String,default:null},currentTabId:{type:String,default:null},enableTouchPointMenu:{type:Boolean,default:!1}},data(){return{LOADING:x,content:null,variables:{q:"",site:"DE_DE",filters:[],facets:this.config.facets}}},computed:{...Ip("base",["mainSize"]),apiUrl(){return this.useElasticSearch()?this.config.apiUrl:`${window.location.pathname.replace(".html","")}.repository.json`},currentRequestState(){return{filteritems:this.compactSelectedFacets(this.facets),sortId:a.get(this.results,"sortId"),sortOrder:a.get(this.results,"sortOrder"),pageNumber:a.get(this.pagination,"currentPage"),resultsId:this.id}},hasFacets(){return this.isHorizontalScrolling()?!1:!!(this.filteredFacets&&this.filteredFacets.length)},hasResults(){return!!a.get(this.results,"items.length")},displayPagination(){return!this.isHorizontalScrolling()},isLoading(){return a.get(this.content,"loading")},facets(){return a.get(this.content,"data.filteritems")},filteredFacets(){return this.facets?this.facets.filter(s=>s.filtervalues.length!==0):[]},results(){return a.get(this.content,"data.results")},pagination(){return a.get(this.content,"data.pagination")},resultsComponentName(){return this.displayAs?this.isFaqList()?"CmpFaqResultList":this.isHorizontalScrolling()?"CmpPageRepositoryHorizontalResultList":"CmpPageRepositoryResultList":this.resultsComponent},resultsId(){return a.kebabCase(`${this.id}-results`)},pageUrl(){return M(window.location.href,this.currentRequestState)}},watch:{pagination({total:s}){this.resultCountHandler(s||0)},apiUrl(){this.getContent(this.facets)}},mounted(){const{resultsId:s,...t}=S(window.location.search);if(this.setVariables(t),a.isEmpty(this.initialState)){const{filteritems:e,pageNumber:i,sortId:n,sortOrder:r}=Object.assign({},this.useQuery&&s===this.id&&Object.assign(this.getAdditionalQueryParams(),a.pick(t,["filteritems","pageNumber","sortId","sortOrder"]))),d=this.sessionStorageKey?a.get(window.sessionStorage,this.sessionStorageKey):!1;if(d){const u=JSON.parse(d);this.getContent(u.filteritems,u.pageNumber,u.sortId,u.sortOrder).then(this.trackSearch)}else this.getContent(this.expandSelectedFacets(e),i,n,r).then(this.trackSearch)}},methods:{useElasticSearch(){return this.config.useElasticSearch!=="false"},trackSearch(){if(this.tabId===this.currentTabId){const s=S(window.location.search),t=this.sortFacets(this.filteredFacets),e=Is(t,this.labels.facets);f({search:{term:s.query,results:this.results.count,type:s["search-type"],filtersApplied:bs(e),category:s.category}},"internal search")}},compactSelectedFacets(s=[]){return s.reduce((t,{label:e,filtervalues:i})=>{const n=i.filter(({selected:r})=>!!r).map(({basevalue:r})=>r);return Object.assign(t,n.length&&{[e]:n})},{})},expandSelectedFacets(s={}){const t=e=>({basevalue:e,selected:!0});return Object.entries(s).reduce((e,[i,n])=>e.concat({label:i,filtervalues:a.isArray(n)?n.map(t):[t(n)]}),[])},setVariables(s){this.variables={q:a.get(s,"query",null),site:`${a.get(window,"grundfos.locale.country").toUpperCase()}_${a.get(window,"grundfos.locale.language").toUpperCase()}`,limit:this.config.limit,offset:0,sort:this.getSorting(),facets:this.config.facets,mustMatchAll:this.getMustMatchAll()}},getAdditionalQueryParams(){return a.pick(S(window.location.search),a.isArray(this.useQuery)?this.useQuery:[])},getContent(s,t){this.setContent(null),this.setFilters(s),t!==void 0?this.variables.offset=this.config.limit*(t-1):this.variables.offset=0;const e={query:Ap,variables:this.variables};return this.sessionStorageKey&&window.sessionStorage.setItem(this.sessionStorageKey,JSON.stringify(e)),this.getResult(e).then(i=>i.json()).then(this.setContent).catch(this.setContent).then(this.useQuery&&this.tabId===this.currentTabId?this.syncQueryParams:a.noop)},setFilters(s){this.variables.filters=[],this.setPageTypeFilter(),this.setTypeFilter(),s&&s.forEach(t=>{const e={field:t.label,value:[]};t.filtervalues.forEach(i=>{i.selected&&e.value.push(i.basevalue)}),e.value.length&&this.variables.filters.push(e)}),this.enableTouchPointMenu&&this.setTouchpointSettingToFilter()},setPageTypeFilter(){const s=a.get(this.config,"params.pageType");a.isEmpty(s)||this.variables.filters.push({field:"PAGE_TYPE",value:s.split("|")})},setTypeFilter(){const s=a.get(this.config,"params.type");a.isEmpty(s)||this.variables.filters.push({field:"TYPE",value:s})},getResult(s){let t=fetch;return window.MSInputMethodContext&&document.documentMode&&(t=window.fetch),this.useElasticSearch()?t(this.apiUrl,this.getRequestObject(s,{})):this.getCSRFToken().then(e=>t(this.apiUrl,this.getRequestObject(s,{"CSRF-Token":e})))},setContent(s){this.content=L(s,this.content);const t=a.get(s,"data.results.items");t&&(this.transformResult(t),this.setFilterItems(s),this.setPagination(s))},transformResult(s){const t=[];s.forEach(e=>{const i=a.get(e,"metadata"),n=a.get(e,"metadata.baseData"),r=a.get(e,"metadata.pageData.processingData.published"),d=this.labels.pageType||{},u=this.labels.contentType||{};if(n){const m=n;m.isVariant=m.pageType==="product-variant",m.pageType=d[m.pageType]||m.pageType,m.contentType=u[i.contentType]||"",m.publishedDate=r?this.getFormattedDate(r):"",m.tags="",m.productName=i.productName,m.productNumber=i.productNumber,m.discontinued=i.discontinued,m.variantProperties=this.getVariantProperties(i),t.push(m)}}),a.set(this.content,"data.results.items",t)},getVariantProperties(s){const t={},e=this.labels.variantInfo||{};if(!a.isEmpty(s.frequency)){const i=s.frequency.join().replace(",","/");s.frequency=`${i} ${this.labels.units.hz}`}return Object.entries(s).forEach(([i,n])=>{this.shouldTranslate(i)&&typeof n=="string"&&(t[`${i}Description`]=e[i]||i,t[`${i}Value`]=n)}),t},setFilterItems(s){const t=a.get(s,"data.results.filteritems");for(let e=0;e<t.length;e++){const i=t[e];i.text=this.labels.facets[i.label]||i.text,i.filtervalues=this.getCuratedFilterValues(i)}a.set(this.content,"data.filteritems",t)},getCuratedFilterValues(s){const t=[],e=this.variables.filters.find(n=>n.field===s.label),i=this.labels.contentType||{};for(let n=0;n<s.filtervalues.length;n++){const r=s.filtervalues[n];r.selected=!!(e&&e.value.includes(r.basevalue)),this.isDiscontinuedFilter(s)?r.basevalue==="true"&&(r.displayvalue=this.labels.facets.showDiscontinuedProducts,t.push(r)):this.isFrequencyFilter(s)?(r.displayvalue=`${r.displayvalue} ${this.labels.units.hz}`,t.push(r)):(this.isContentTypeFilter(s)&&(r.displayvalue=i[r.basevalue]||r.displayvalue),t.push(r))}return t},isDiscontinuedFilter(s){return s.label===Mp},isFrequencyFilter(s){return s.label===et},isContentTypeFilter(s){return s.label===Pp},setPagination(s){const t=a.get(s,"data.results.count");a.set(this.content,"data.pagination",{currentPage:Math.floor(this.variables.offset/this.config.limit)+1,maxPage:Math.min(Rp,Math.ceil(t/this.config.limit)),total:t})},syncQueryParams(){this.isHorizontalScrolling()||T(this.currentRequestState)},setFacets(...s){return this.getContent(...s).then(()=>{this.trackSelectedFacets(...s)}).then(this.setScroll)},trackSelectedFacets(s){f({search:{...window.dataLayer.search,results:this.results.count,filtersApplied:bs(s)}},"internal search filtered")},setPage(s){return this.getContent(this.facets,s).then(this.setScroll)},setSort(s){let t;return s===a.get(this.results,"sortId")&&(t=a.get(this.results,"sortOrder")===A?_t:A),this.getContent(this.facets,1,s,t).then(this.setScroll)},setScroll(){K(this.$el,!0,{top:this.scrollOffset})},isFaqList(){return this.displayAs&&this.displayAs===Fp},isHorizontalScrolling(){return this.displayAs&&this.displayAs===Op},getSorting(){return this.sortBy?this.sortBy:this.config.sort},getMustMatchAll(){return typeof this.config.mustMatchAll>"u"?!1:this.config.mustMatchAll===!0||this.config.mustMatchAll==="true"},getFormattedDate(s){const t=new Date(parseInt(s,10));return(this.config.dateFormat?this.config.dateFormat:"dd-MM-yyyy").replace("dd",String(t.getDate()).padStart(2,"0")).replace("MM",String(t.getMonth()+1).padStart(2,"0")).replace("yyyy",t.getFullYear())},sortFacets(s){return this.useConfigFacetOrder?this.orderFacetsByFacetConfig(s):a.orderBy(s,["label"],A)},orderFacetsByFacetConfig(s){const t=this.config.facets.map(e=>e.field);if(this.enableTouchPointMenu){const e=s.filter(i=>i.label!==et);return a.orderBy(e,i=>t.indexOf(i.label))}return a.orderBy(s,e=>t.indexOf(e.label))},getCSRFToken(){return window.fetch("/libs/granite/csrf/token.json").then(s=>s.json()).then(s=>s.token).catch(null)},getRequestObject(s,t){let e={"Content-Type":"application/json"};return e={...e,...t},{method:"POST",mode:"cors",credentials:"same-origin",headers:e,redirect:"follow",referrer:document.location.href.split("?")[0],referrerPolicy:"unsafe-url",body:JSON.stringify(s)}},shouldTranslate(s){return s!=="productName"&&s!=="productNumber"&&s!=="discontinued"},navigationHandler(s,t){f({search:{...window.dataLayer.search,position:t,page:a.get(this.pagination,"currentPage")}},"internal search clicked"),setTimeout(()=>{window.location.assign(s)},250)},setTouchpointSettingToFilter(){const s=a.get(window,"grundfos.settings.user");if(s){const t={field:et,value:[s.frequency]};this.variables.filters.push(t)}}}};var Hp=function(){var t=this,e=t._self._c;return e("section",{class:t.isLoading&&!t.hasResults&&t.LOADING,attrs:{"aria-live":"polite"}},[e("div",{staticClass:"b-layout-grid"},[e("div",{staticClass:"b-layout-grid__group"},[t.hasFacets?e("aside",{class:["b-layout-grid__item","b-layout-grid__item--12",!t.fullWidth&&"b-layout-grid__item--large-4","b-layout-grid__item--xlarge-3"]},[e("cmp-facets-container",{staticClass:"cmp-facets-container",attrs:{"collapse-facets":t.collapseFacets,"controls-id":t.resultsId,items:t.sortFacets(t.filteredFacets),"is-loading":t.isLoading,labels:t.labels.facets,"natural-facet-ordering":t.naturalFacetOrdering,"reset-handler":t.setFacets,"update-handler":t.setFacets,"use-overlay":t.fullWidth}})],1):t._e(),t.hasResults?e("div",{class:["b-layout-grid__item","b-layout-grid__item--12",!t.fullWidth&&t.hasFacets&&"b-layout-grid__item--large-8",t.hasFacets&&"b-layout-grid__item--xlarge-9"]},[e("div",{class:["mod-result-list__results",t.fullWidth&&"mod-result-list__results--full-width"]},[e("div",{staticClass:"mod-result-list__results-inner",style:t.fullWidth&&{"min-width":`${t.mainSize.width}px`}},[e(t.resultsComponentName,t._b({tag:"component",class:t.isLoading&&t.LOADING,attrs:{config:t.config,id:t.resultsId,labels:t.labels.results,total:t.pagination&&t.pagination.total,"update-handler":t.setSort,"is-elastic-search":t.isElasticSearch},on:{"navigation-handler":t.navigationHandler}},"component",t.results,!1))],1)]),t.pagination&&t.hasResults&&t.displayPagination?e("cmp-pagination",t._b({staticClass:"cmp-pagination",attrs:{"controls-id":t.resultsId,id:t._f("kebabCase")(`${t.id}-pagination`),labels:t.labels.pagination,"page-url":t.pageUrl,"update-handler":t.setPage}},"cmp-pagination",t.pagination,!1)):t._e()],1):t.isLoading?t._e():e("div",{class:["b-layout-grid__item","b-layout-grid__item--12",!t.fullWidth&&t.hasFacets&&"b-layout-grid__item--large-8","b-layout-grid__item--xlarge-9"]},[e("div",{staticClass:"cmp-alert"},[e("p",{staticClass:"cmp-alert__message"},[t._v(t._s(t.labels.results.noResults))])])])])])])},Bp=[],Dp=l(Np,Hp,Bp,!1,null,null,null,null);const vs=Dp.exports,z="allResultCount",V="productResultCount",U="documentationResultCount",j="supportResultCount",G="otherResultCount",qp={name:"ProviderSearchResults",mixins:[c],data(){return{[z]:0,[V]:0,[U]:0,[j]:0,[G]:0}},methods:{setResultCount(s,t){this.setState({[s]:t})}},render(){return this.$scopedSlots.default({[z]:this[z],[V]:this[V],[U]:this[U],[j]:this[j],[G]:this[G],setAllResultCount:a.partial(this.setResultCount,z),setProductResultCount:a.partial(this.setResultCount,V),setDocumentationResultCount:a.partial(this.setResultCount,U),setSupportResultCount:a.partial(this.setResultCount,j),setOtherResultCount:a.partial(this.setResultCount,G)})}},zp=null,Vp=null;var Up=l(qp,zp,Vp,!1,null,null,null,null);const ys=Up.exports,{mapMutations:jp}=p;function Gp(s,t){return{props:{initialState:{type:Object,default:()=>({})}},mixins:[y],methods:{...jp(s,["setConfig","setState"])},beforeCreate(){this.$store.registerModule(s.split("/"),t)},created(){this.setConfig(this.config),this.setState(this.initialState)}}}const Wp="Track location details",ft=10;let w=ft;const Qp={name:"CmpWhereToBuyMapSidePanel",components:{simplebar:Ls},mixins:[_,c],props:{locations:{type:[Array,null],default:null},labels:{type:Object,default:()=>({})},updateQuery:{type:Function,default:a.noop},isSearchResultsEmpty:{type:Boolean,default:!1},locationSelectHandler:{type:Function,default:a.noop},selectedMarker:{type:String,default:null},runGeoCode:{type:Function,default:a.noop},closeCards:{type:Number,default:0}},data(){return{shownLocations:this.locations.slice(0,w),lastCardState:!1}},watch:{locations(){w=ft,this.locations.forEach(s=>{const t=s;t.isOpen=!1}),this.shownLocations=this.locations.slice(0,w),this.locations&&this.locations.length===1&&(this.trackLocationEvent(this.locations[0]),this.locations[0].isOpen=!0),this.$nextTick().then(()=>{this.enableSidePanelScroll()})},closeCards(){this.closeOtherCards({})}},methods:{trackLocationEvent(s){f({whereToBuy:{website:s.website,email:s.email,phone:s.phone,fax:s.fax,geocodeAddress:`https://maps.google.com/?q=${encodeURIComponent(s.geocodeAddress).trim()}`,products:s.products}},Wp)},hasMore(){return this.locations.length>w},showMore(){w+=ft,this.shownLocations=this.locations.slice(0,w),this.$nextTick().then(()=>{this.enableSidePanelScroll()})},closeOtherCards(s){this.shownLocations.forEach((t,e,i)=>{const n=t;n.isOpen=t===s,i.splice(e,1,n)})},enableSidePanelScroll(){if(!this.isMobile){const t=this.$root.$children.find(e=>e.$options.name==="ModWhereToBuy").$children.find(e=>e.$options.name==="CmpGoogleMap");if(t){const e=t.$el.offsetHeight,i=this.$refs.sidePanel.$refs.contentElement.offsetHeight,n=this.$refs.inputSearch,r=getComputedStyle(n);i+n.offsetHeight+parseFloat(r.marginTop)+parseFloat(r.marginBottom)<e?this.$el.classList.add("cmp-where-to-buy-side-panel__auto-height"):this.$el.classList.remove("cmp-where-to-buy-side-panel__auto-height")}}}}};var Kp=function(){var t=this,e=t._self._c;return e("section",{staticClass:"cmp-where-to-buy-side-panel"},[e("div",{ref:"inputSearch",staticClass:"cmp-where-to-buy-side-panel__input-container"},[e("input",{staticClass:"cmp-where-to-buy-side-panel__input cmp-form-text__text",attrs:{placeholder:t.labels.search,autocomplete:"off","data-cmp-hook-form-text":"input",name:"location-search",type:"search"},on:{input:t.updateQuery}}),t.isMobile?e("button",{staticClass:"elm-link cmp-where-to-buy__btn cmp-where-to-buy__btn--geolocate",attrs:{type:"button"},on:{click:t.runGeoCode}}):t._e()]),t.isMobile?t._e():e("simplebar",{ref:"sidePanel",staticClass:"cmp-where-to-buy-results",attrs:{"data-simplebar-auto-hide":"false"}},[t.isSearchResultsEmpty?e("div",{staticClass:"cmp-alert cmp-where-to-buy-results__empty"},[e("p",{staticClass:"cmp-alert__message"},[t._v(t._s(t.labels.noResults))])]):e("div",{staticClass:"cmp-location-card",attrs:{itemscope:"",itemtype:"https://schema.org/ItemList"}},[t._l(t.shownLocations,function(i,n){return e("cmp-location-card",{key:n,staticClass:"cmp-location-card-item",attrs:{id:`location-item-${n}`,location:i,labels:t.labels,"is-active":!0,"is-open":i.isOpen,"location-select-handler":t.locationSelectHandler,"selected-marker":t.selectedMarker,"close-other-cards":t.closeOtherCards,"enable-side-panel-scroll":t.enableSidePanelScroll,itemscope:"",itemtype:"https://schema.org/ListItem"}})}),t.hasMore()?e("div",{staticClass:"cmp-location-card-item__load-more"},[e("button",{staticClass:"elm-button",attrs:{type:"button"},on:{click:t.showMore}},[t._v(" "+t._s(t.labels.showMore)+" ")])]):t._e()],2)]),t.isMobile?e("div",{staticClass:"cmp-where-to-buy-results"},[t.isSearchResultsEmpty?e("div",{staticClass:"cmp-alert cmp-where-to-buy-results__empty"},[e("p",{staticClass:"cmp-alert__message"},[t._v(t._s(t.labels.noResults))])]):e("div",{staticClass:"cmp-location-card",attrs:{itemscope:"",itemtype:"https://schema.org/ItemList"}},[t._l(t.shownLocations,function(i,n){return e("cmp-location-card",{key:n,staticClass:"cmp-location-card-item",attrs:{id:`location-item-${n}`,location:i,labels:t.labels,"is-active":!0,"is-open":i.isOpen,"location-select-handler":t.locationSelectHandler,"selected-marker":t.selectedMarker,"close-other-cards":t.closeOtherCards,itemscope:"",itemtype:"https://schema.org/ListItem"}})}),t.hasMore()?e("div",{staticClass:"cmp-location-card-item__load-more"},[e("button",{staticClass:"elm-button",attrs:{type:"button"},on:{click:t.showMore}},[t._v(" "+t._s(t.labels.showMore)+" ")])]):e("div",[e("span",[t._v(" ")])])],2)]):t._e()],1)},Yp=[],Zp=l(Qp,Kp,Yp,!1,null,null,null,null);const Jp=Zp.exports;function Xp(s){return a.merge({mutations:{setConfig(t,e){t.config=e},setState(t,e){Object.assign(t,a.omit(e,["config"]))}}},s)}const st="base/wheretobuy",tm=Xp({namespaced:!0,state:{config:{},allLocations:null},actions:{getLocations({commit:s,state:t}){return E(t.config.apiUrl,t.config).then(e=>{s("setLocations",L(e).data)}).catch(e=>{s("setLocations",L(e,t.locations))})}},mutations:{setLocations(s,t){s.allLocations=t}}}),{mapActions:em,mapState:sm}=p,im=6,am={name:"ModWhereToBuy",components:{CmpWhereToBuySidePanel:Jp},mixins:[y,Gp(st,tm),v],props:{defaultZoom:{type:String,default:null},labels:{type:Object,default:()=>({})},tabs:{type:Object,default:()=>({})},contactUs1Hide:{type:Boolean,default:!1},contactUs2Hide:{type:Boolean,default:!1},locationType:{type:String,default:null},mapStyles:{type:[Object,String],default:null},mapHeight:{type:String,default:null},mapProvider:{type:String,default:null},useQuery:{type:Boolean,default:!1}},data(){return{tabItems:[],currentTab:null,contactUs1id:null,contactUs2id:null,isGeoCode:!1,closeCards:0}},computed:{...sm(st,["allLocations"]),currentTabId(){return a.get(this.currentTab,"id")},markerIcon(){return a.get(this.currentTab,"marker")},isContactUs1Selected(){return this.currentTabId===this.contactUs1id},isContactUs2Selected(){return this.currentTabId===this.contactUs2id},isLeftColumnVisible(){return a.get(this.currentTab,"leftColumnVisible")},isGoogleMap(){return!this.mapProvider||this.mapProvider==="google"}},watch:{allLocations(){this.setTab()}},mounted(){this.getLocations()},methods:{...em(st,["getLocations"]),getLocationTypes(){const s=this.allLocations?this.allLocations.reduce((t,e)=>{const{type:i}=e;return i&&i.length>0&&!t.includes(i)&&t.push(i),t},[]):null;for(let t=1;t<=im;t++){const e=this.tabs[`tab${t}show`]==="true",i=this.tabs[`tab${t}type`],n=this.tabs[`tab${t}ShowLeftColumn`]==="true",r=this.tabs[`tab${t}marker`];e&&s&&s.includes(i)&&this.tabItems.push({id:i,label:this.labels[`tab${t}title`],leftColumnVisible:n,marker:r})}this.contactUs1Hide||(this.contactUs1id=this.tabUrlAddRegEx(this.labels.contactUs1),this.tabItems.push({id:this.contactUs1id,label:this.labels.contactUs1})),this.contactUs2Hide||(this.contactUs2id=this.labels.contactUs1===this.labels.contactUs2?`${this.tabUrlAddRegEx(this.labels.contactUs2)}-`:this.tabUrlAddRegEx(this.labels.contactUs2),this.tabItems.push({id:this.contactUs2id,label:this.labels.contactUs2}))},getQueryValue(){return a.get(S(window.location.search),"tab",null)},setTab(){this.getLocationTypes();const s=a.get(this.tabItems,"[0]"),t=this.validateTabId(s)?s:null;if(this.useQuery&&!this.isEditMode){const e=this.getQueryValue();if(e===null)this.currentTab=s,T({tab:this.tabUrlAddRegEx(this.currentTab.id)});else{const i=e&&this.validateTabId(e),n=i?e:s;i?(this.currentTab=this.tabItems.find(r=>r.id===n),T({tab:this.tabUrlAddRegEx(this.currentTab.id)})):(this.currentTab=s,T({tab:this.tabUrlAddRegEx(this.currentTab.id)}))}}else this.currentTab=t;this.tabToggleHandler(this.currentTab)},tabToggleHandler(s){this.currentTab===s?this.resetCurrentTab(s):this.currentTab=s},resetCurrentTab(s){this.currentTab=null,this.$nextTick().then(()=>{this.currentTab=s})},tabUrlAddRegEx(s){return s.replace(/ /g,"-")},validateTabId(s){if(s===null)return!0;const t=a.find(this.tabItems,{id:s});return t&&!t.disabled},runGeoCode(){this.isGeoCode=!0},closeCard(){this.closeCards=this.closeCards+1}}};var nm=function(){var t=this,e=t._self._c;return e("section",[t.tabItems&&!t.locationType?e("cmp-tab-selector",{staticClass:"mod-where-to-buy-tabs",attrs:{items:t.tabItems,"toggle-handler":t.tabToggleHandler,value:t.currentTabId,"use-query":t.useQuery,"controls-id":"cmp-where-to-buy"}}):t._e(),t.isEditMode?e("div",[t._v("Contact us 1")]):t._e(),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isContactUs1Selected||t.isEditMode,expression:"isContactUs1Selected || isEditMode"}]},[t._t("contact-us-1")],2),t.isEditMode?e("div",[t._v("Contact us 2")]):t._e(),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isContactUs2Selected||t.isEditMode,expression:"isContactUs2Selected || isEditMode"}]},[t._t("contact-us-2")],2),!t.isContactUs1Selected&&!t.isContactUs2Selected||t.isEditMode?t._t("default",function(){return[t.isGoogleMap?e("cmp-google-map",{attrs:{config:t.config,labels:t.labels,"all-locations":t.allLocations,"default-zoom":t.defaultZoom,"map-styles":t.mapStyles,"location-type":t.locationType||t.currentTabId,"map-height":t.mapHeight,"selected-marker":t.markerIcon,"is-geo-code":t.isGeoCode,"close-card":t.closeCard},scopedSlots:t._u([{key:"sidebar",fn:function(i){return[t.isLeftColumnVisible?e("cmp-where-to-buy-side-panel",{attrs:{locations:i.locations,labels:t.labels,"update-query":i.updateQuery,"is-search-results-empty":i.isSearchResultsEmpty,"location-select-handler":i.locationSelectHandler,"selected-marker":t.markerIcon,"run-geo-code":t.runGeoCode,"close-cards":t.closeCards}}):t._e()]}}],null,!1,442232114)}):t._e(),t.isGoogleMap?t._e():e("cmp-map",{attrs:{config:t.config,labels:t.labels,"all-locations":t.allLocations,"default-zoom":t.defaultZoom,"map-styles":t.mapStyles,"location-type":t.locationType||t.currentTabId,"map-height":t.mapHeight,"map-provider":t.mapProvider,"selected-marker":t.markerIcon},scopedSlots:t._u([{key:"sidebar",fn:function(i){return[t.isLeftColumnVisible?e("cmp-where-to-buy-side-panel",{attrs:{locations:i.locations,labels:t.labels,"update-query":i.updateQuery,"is-search-results-empty":i.isSearchResultsEmpty,"location-select-handler":i.locationSelectHandler,"selected-marker":t.markerIcon,"run-geo-code":t.runGeoCode,"close-cards":t.closeCards}}):t._e()]}}],null,!1,442232114)})]}):t._e()],2)},rm=[],om=l(am,nm,rm,!1,null,null,null,null);const Cs=om.exports,{mapMutations:lm}=p,cm={name:"CmpSearchHero",props:{size:{type:String,default:""}},beforeMount(){this.setBreadcrumbToFloating(),this.setBreadcrumbToDarkTheme()},methods:{...lm("base",["setBreadcrumbToFloating","setBreadcrumbToDarkTheme"])}};var dm=function(){var t=this,e=t._self._c;return e("div",{class:["cmp-search-hero",t.size&&`cmp-search-hero--${t.size}`]},[t._t("dynamicMediaLayout")],2)},um=[],pm=l(cm,dm,um,!1,null,null,null,null);const Ss=pm.exports,mm=60,hm={name:"CmpCampaignDeck",mixins:[_],props:{deckTheme:{type:String,default:"dark-blue"},hasBlurryBox:{type:Boolean,default:!1},isRightAligned:{type:Boolean,default:!1},isAuthorMode:{type:Boolean,default:!1},headlineText:{type:String,default:""},headlineColor:{type:String,default:""}},data(){return{blurryBackgroundImage:"",dynamicMediaLayoutClassObject:{}}},computed:{extendedFontSize(){return this.headlineText.length>=mm?"cmp-campaign-deck--extended-font-size":"cmp-campaign-deck--extended-font-clamp"},determineHeadlineSize(){return"1"}},watch:{isMobile(s){this.setBackgroundImage(s)}},mounted(){this.hasBlurryBox&&this.observeImageTag()},beforeMount(){this.createDMLayoutClassObject()},methods:{observeImageTag(){const s=this.$refs.CmpDynamicMediaLayoutRef.$refs.dynamicMediaContainerRef,t={attributes:!0,subtree:!0},e=new MutationObserver(i=>{const n=i.filter(r=>r.type==="attributes"&&r.attributeName==="src");n.length&&(this.blurryBackgroundImage=`url("${n[0].target.src}")`,this.isMobile&&this.setBackgroundImage(this.isMobile),e.disconnect())});e.observe(s,t)},setBackgroundImage(s){s&&this.blurryBackgroundImage.length?this.$refs.campaignDeckCustomContent.style.backgroundImage=this.blurryBackgroundImage:this.$refs.campaignDeckCustomContent.style.backgroundImage=""},getCleanTheme(){return this.deckTheme.replace("b-theme--","")},createDMLayoutClassObject(){const s={componentContainer:["b-theme",`b-theme--${this.getCleanTheme()}`,"b-full-width"]};this.dynamicMediaLayoutClassObject={...s}}}};var _m=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cmp-campaign-deck"},[e("CmpDynamicMediaLayout",{ref:"CmpDynamicMediaLayoutRef",attrs:{"is-author-mode":t.isAuthorMode,"additional-classes":t.dynamicMediaLayoutClassObject,"left-align":!t.isRightAligned},scopedSlots:t._u([{key:"dynamicMedia",fn:function(){return[t._t("dynamicMedia")]},proxy:!0},{key:"customContent",fn:function(){return[e("div",{ref:"campaignDeckCustomContent",class:["cmp-dynamic-media-layout__box-content","elm-flow--spacing",t.hasBlurryBox?"":`cmp-dynamic-media-layout--${t.getCleanTheme()}-box`,t.hasBlurryBox&&!t.isMobile?"cmp-dynamic-media-layout--blurred-box":"",t.hasBlurryBox&&t.isMobile?"cmp-campaign-deck--mobile-background":""]},[e("cmp-extended-headline",{attrs:{"headline-text":t.headlineText,"headline-color":t.headlineColor,"heading-class-size":t.determineHeadlineSize,"custom-classes":[t.extendedFontSize],"headline-size":"h2"}}),t._t("rteContent"),t._t("legacyParsys"),e("div",{staticClass:"cmp-dynamic-media-layout__buttons"},[t._t("buttonOne"),t._t("buttonTwo"),t._t("videoOverlay")],2)],2)]},proxy:!0},{key:"editModeContent",fn:function(){return[e("div",{staticClass:"b-layout-grid__group b-layout-grid__group--around"},[e("div",{staticClass:"b-layout-grid__item b-layout-grid__item--6"},[t._t("headlineContent"),t._t("rteContent"),t._t("buttonOne"),t._t("buttonTwo")],2),e("div",{staticClass:"b-layout-grid__item b-layout-grid__item--6"},[t._t("videoOverlay")],2),e("div",{staticClass:"b-layout-grid__item b-layout-grid__item--6"},[t._t("legacyParsys")],2)])]},proxy:!0}],null,!0)})],1)},fm=[],gm=l(hm,_m,fm,!1,null,null,null,null);const ks=gm.exports,{mapMutations:bm}=p,vm={name:"CmpArticleHeader",mixins:[],props:{assetSize:{type:String,default:""},tag:{type:String,default:""},theme:{type:String,default:""},title:{type:String,default:""}},computed:{isAssetSizeLarge(){return this.assetSize==="large"},isDarkTheme(){return this.theme==="b-theme--dark-blue"}},beforeMount(){this.setBreadcrumbToFloating(),this.isDarkTheme&&this.setBreadcrumbToDarkTheme()},methods:{...bm("base",["setBreadcrumbToFloating","setBreadcrumbToDarkTheme"])}};var ym=function(){var t=this,e=t._self._c;return e("header",{class:[`b-theme ${t.theme}`,"b-deck b-deck--full-width b-deck--no_spacing","cmp-article-header"]},[e("div",{staticClass:"cmp-article-header__inner"},[e("div",{staticClass:"cmp-article-header__content"},[t.tag?e("span",{staticClass:"elm-tag elm-tag--transparent-light"},[t._v(" "+t._s(t.tag)+" ")]):t._e(),e("cmp-extended-headline",{attrs:{"headline-text":t.title}}),t.$slots.articleDate?[t._t("articleDate")]:t._e()],2),t.$slots.dynamicMedia?e("div",{class:["cmp-article-header__media",t.isAssetSizeLarge&&"cmp-article-header__media--large"]},[t._t("dynamicMedia")],2):t._e()])])},Cm=[],Sm=l(vm,ym,Cm,!1,null,null,null,null);const Ts=Sm.exports;document.documentElement.classList.remove("no-js");o.component(yt.name,yt);o.component(Pt.name,Pt);o.component(Rt.name,Rt);o.component(Nt.name,Nt);o.component(Ht.name,Ht);o.component(Bt.name,Bt);o.component(Dt.name,Dt);o.component(qt.name,qt);o.component(Ct.name,Ct);o.component(zt.name,zt);o.component(Vt.name,Vt);o.component(Ut.name,Ut);o.component(jt.name,jt);o.component(Gt.name,Gt);o.component(St.name,St);o.component(kt.name,kt);o.component(Wt.name,Wt);o.component(Qt.name,Qt);o.component(Kt.name,Kt);o.component(Yt.name,Yt);o.component(Zt.name,Zt);o.component(Jt.name,Jt);o.component(Xt.name,Xt);o.component(te.name,te);o.component(ie.name,ie);o.component(ne.name,ne);o.component(re.name,re);o.component(ae.name,ae);o.component(lt.name,lt);o.component(oe.name,oe);o.component(ot.name,ot);o.component(le.name,le);o.component(ce.name,ce);o.component(de.name,de);o.component(ue.name,ue);o.component(pe.name,pe);o.component(me.name,me);o.component(he.name,he);o.component(_e.name,_e);o.component(ct.name,ct);o.component(fe.name,fe);o.component(dt.name,dt);o.component(ut.name,ut);o.component(ge.name,ge);o.component(Tt.name,Tt);o.component(ye.name,ye);o.component(pt.name,pt);o.component(ke.name,ke);o.component(Te.name,Te);o.component($e.name,$e);o.component(xe.name,xe);o.component(we.name,we);o.component($t.name,$t);o.component(Le.name,Le);o.component(Ee.name,Ee);o.component(Ie.name,Ie);o.component(Ae.name,Ae);o.component(Fe.name,Fe);o.component(Oe.name,Oe);o.component(Me.name,Me);o.component(Pe.name,Pe);o.component(Re.name,Re);o.component(ht.name,ht);o.component(mt.name,mt);o.component(He.name,He);o.component(Be.name,Be);o.component(De.name,De);o.component(qe.name,qe);o.component(ze.name,ze);o.component(Ve.name,Ve);o.component(Ue.name,Ue);o.component(je.name,je);o.component(Ge.name,Ge);o.component(We.name,We);o.component(Qe.name,Qe);o.component(Ke.name,Ke);o.component(Ye.name,Ye);o.component(Ze.name,Ze);o.component(as.name,as);o.component(rs.name,rs);o.component(os.name,os);o.component(xt.name,xt);o.component(ls.name,ls);o.component(cs.name,cs);o.component(ds.name,ds);o.component(ms.name,ms);o.component(hs.name,hs);o.component(fs.name,fs);o.component(wt.name,wt);o.component(gs.name,gs);o.component(vs.name,vs);o.component(ys.name,ys);o.component(Cs.name,Cs);o.component(nt.name,nt);o.component(Lt.name,Lt);o.component(Et.name,Et);o.component(It.name,It);o.component(Ss.name,Ss);o.component(ks.name,ks);o.component(At.name,At);o.component(Ts.name,Ts);o.component(Ft.name,Ft);o.component(Ot.name,Ot);o.directive("tooltip",Vs);o.directive("truncate",Us);o.use(ai);o.filter("kebabCase",js);o.filter("maxNum",Gs);o.filter("relativeDate",Ws);o.filter("translate",Qs);o.filter("truncate",Ks);o.filter("upperCase",Ys);o.config.ignoredElements=[/^cq/,"video-js"];const km=Js();km.init();const Tm=Xs();document.addEventListener("DOMContentLoaded",()=>{Tm.init()});window.grundfos.keepAlive.isKeepAliveEnabled&&Zs(window.grundfos.keepAlive.url,18e4);
