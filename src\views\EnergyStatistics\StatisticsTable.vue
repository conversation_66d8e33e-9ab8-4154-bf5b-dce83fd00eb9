<template>
  <div class="statistics-table">
    <el-table :data="tableData" border stripe>
      <el-table-column prop="metric" label="统计指标" width="150" fixed="left">
        <template #default="{ row }">
          <div class="metric-cell">
            <el-icon :color="row.color">
              <component :is="row.icon" />
            </el-icon>
            <span>{{ row.metric }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="current" label="当前周期" width="120" align="right">
        <template #default="{ row }">
          <span class="value-cell">{{ row.current }}</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="previous" label="上一周期" width="120" align="right">
        <template #default="{ row }">
          <span class="value-cell">{{ row.previous || '-' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="change" label="变化" width="100" align="center">
        <template #default="{ row }">
          <div v-if="row.change !== null" class="change-cell" :class="row.changeClass">
            <el-icon>
              <component :is="row.changeIcon" />
            </el-icon>
            <span>{{ Math.abs(row.change) }}%</span>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="unit" label="单位" width="80" align="center" />
      
      <el-table-column prop="description" label="说明" min-width="200">
        <template #default="{ row }">
          <el-tooltip :content="row.tooltip" placement="top">
            <span class="description-cell">{{ row.description }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      
      <el-table-column label="趋势" width="120" align="center">
        <template #default="{ row }">
          <div class="trend-cell">
            <el-progress
              :percentage="row.trendPercentage"
              :color="row.trendColor"
              :stroke-width="8"
              :show-text="false"
            />
            <span class="trend-text">{{ row.trendText }}</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 汇总信息 -->
    <div class="summary-section">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-statistic title="总体评分" :value="overallScore" suffix="/100">
            <template #prefix>
              <el-icon color="#409EFF"><TrendCharts /></el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="8">
          <el-statistic title="节能潜力" :value="energySavingPotential" suffix="%">
            <template #prefix>
              <el-icon color="#67C23A"><Lightning /></el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="8">
          <el-statistic title="成本优化空间" :value="costOptimizationSpace" suffix="元">
            <template #prefix>
              <el-icon color="#E6A23C"><Money /></el-icon>
            </template>
          </el-statistic>
        </el-col>
      </el-row>
    </div>
    
    <!-- 建议和分析 -->
    <div class="analysis-section">
      <h4>智能分析建议</h4>
      <el-timeline>
        <el-timeline-item
          v-for="suggestion in suggestions"
          :key="suggestion.id"
          :type="suggestion.type"
          :icon="suggestion.icon"
        >
          <div class="suggestion-content">
            <h5>{{ suggestion.title }}</h5>
            <p>{{ suggestion.description }}</p>
            <div class="suggestion-impact">
              <el-tag size="small" :type="suggestion.impactType">
                预期影响: {{ suggestion.impact }}
              </el-tag>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  TrendCharts, 
  Lightning, 
  Money, 
  ArrowUp, 
  ArrowDown, 
  Minus,
  DataAnalysis,
  Setting
} from '@element-plus/icons-vue'
import type { EnergyStatistics, EnergyComparison } from '@/types'

interface Props {
  currentStats: EnergyStatistics | null
  previousStats: EnergyStatistics | null
  comparison: EnergyComparison | null
}

const props = defineProps<Props>()

// 计算属性
const tableData = computed(() => {
  if (!props.currentStats) return []
  
  const current = props.currentStats
  const previous = props.previousStats
  const comparison = props.comparison
  
  return [
    {
      metric: '总能耗',
      current: `${current.totalEnergy.toLocaleString()}`,
      previous: previous ? `${previous.totalEnergy.toLocaleString()}` : null,
      change: comparison?.changePercent.totalEnergy || null,
      changeClass: getChangeClass(comparison?.changePercent.totalEnergy, true),
      changeIcon: getChangeIcon(comparison?.changePercent.totalEnergy),
      unit: 'kWh',
      description: '统计周期内的总电能消耗',
      tooltip: '包括所有设备的电能消耗总和，是衡量能源使用效率的重要指标',
      icon: 'Lightning',
      color: '#409EFF',
      trendPercentage: Math.min(100, (current.totalEnergy / 3000) * 100),
      trendColor: '#409EFF',
      trendText: '正常'
    },
    {
      metric: '总成本',
      current: `${current.totalCost.toLocaleString()}`,
      previous: previous ? `${previous.totalCost.toLocaleString()}` : null,
      change: comparison?.changePercent.totalCost || null,
      changeClass: getChangeClass(comparison?.changePercent.totalCost, true),
      changeIcon: getChangeIcon(comparison?.changePercent.totalCost),
      unit: '元',
      description: '统计周期内的总运行成本',
      tooltip: '包括电费、维护费等所有运行成本',
      icon: 'Money',
      color: '#E6A23C',
      trendPercentage: Math.min(100, (current.totalCost / 2500) * 100),
      trendColor: '#E6A23C',
      trendText: '可控'
    },
    {
      metric: '平均效率',
      current: `${current.avgEfficiency.toFixed(1)}`,
      previous: previous ? `${previous.avgEfficiency.toFixed(1)}` : null,
      change: comparison?.changePercent.avgEfficiency || null,
      changeClass: getChangeClass(comparison?.changePercent.avgEfficiency, false),
      changeIcon: getChangeIcon(comparison?.changePercent.avgEfficiency),
      unit: '%',
      description: '设备运行的平均效率',
      tooltip: '效率越高表示能源利用越充分，是优化的重要目标',
      icon: 'TrendCharts',
      color: '#67C23A',
      trendPercentage: current.avgEfficiency,
      trendColor: current.avgEfficiency > 80 ? '#67C23A' : current.avgEfficiency > 70 ? '#E6A23C' : '#F56C6C',
      trendText: current.avgEfficiency > 80 ? '优秀' : current.avgEfficiency > 70 ? '良好' : '需改进'
    },
    {
      metric: '单位能耗',
      current: `${current.energyPerUnit.toFixed(3)}`,
      previous: previous ? `${previous.energyPerUnit.toFixed(3)}` : null,
      change: comparison?.changePercent.energyPerUnit || null,
      changeClass: getChangeClass(comparison?.changePercent.energyPerUnit, true),
      changeIcon: getChangeIcon(comparison?.changePercent.energyPerUnit),
      unit: 'kWh/m³',
      description: '单位流量的能耗',
      tooltip: '反映系统的能效水平，数值越低越好',
      icon: 'DataAnalysis',
      color: '#909399',
      trendPercentage: Math.max(0, 100 - (current.energyPerUnit * 100)),
      trendColor: '#909399',
      trendText: '监控中'
    },
    {
      metric: '碳排放',
      current: `${current.carbonEmission.toFixed(1)}`,
      previous: previous ? `${previous.carbonEmission.toFixed(1)}` : null,
      change: null,
      changeClass: '',
      changeIcon: 'Minus',
      unit: 'kg CO2',
      description: '统计周期内的碳排放量',
      tooltip: '基于电网碳排放因子计算的间接碳排放',
      icon: 'Setting',
      color: '#F56C6C',
      trendPercentage: Math.min(100, (current.carbonEmission / 2000) * 100),
      trendColor: '#F56C6C',
      trendText: '关注'
    },
    {
      metric: '负荷因子',
      current: `${current.loadFactor.toFixed(3)}`,
      previous: previous ? `${previous.loadFactor.toFixed(3)}` : null,
      change: null,
      changeClass: '',
      changeIcon: 'Minus',
      unit: '-',
      description: '平均负荷与峰值负荷的比值',
      tooltip: '反映负荷的平稳程度，数值越接近1越好',
      icon: 'TrendCharts',
      color: '#909399',
      trendPercentage: current.loadFactor * 100,
      trendColor: current.loadFactor > 0.8 ? '#67C23A' : current.loadFactor > 0.6 ? '#E6A23C' : '#F56C6C',
      trendText: current.loadFactor > 0.8 ? '优秀' : current.loadFactor > 0.6 ? '良好' : '需优化'
    }
  ]
})

const overallScore = computed(() => {
  if (!props.currentStats) return 0
  
  const stats = props.currentStats
  let score = 0
  
  // 效率评分 (40%)
  const efficiencyScore = Math.min(100, (stats.avgEfficiency / 90) * 100)
  score += efficiencyScore * 0.4
  
  // 负荷因子评分 (30%)
  const loadFactorScore = stats.loadFactor * 100
  score += loadFactorScore * 0.3
  
  // 单位能耗评分 (30%)
  const energyPerUnitScore = Math.max(0, 100 - (stats.energyPerUnit * 50))
  score += energyPerUnitScore * 0.3
  
  return Math.round(score)
})

const energySavingPotential = computed(() => {
  if (!props.currentStats) return 0
  
  const currentEfficiency = props.currentStats.avgEfficiency
  const maxEfficiency = 90 // 理论最大效率
  
  return Math.round(((maxEfficiency - currentEfficiency) / maxEfficiency) * 100)
})

const costOptimizationSpace = computed(() => {
  if (!props.currentStats) return 0
  
  const currentCost = props.currentStats.totalCost
  const optimizationRate = energySavingPotential.value / 100
  
  return Math.round(currentCost * optimizationRate)
})

const suggestions = computed(() => {
  if (!props.currentStats) return []
  
  const stats = props.currentStats
  const suggestions = []
  
  if (stats.avgEfficiency < 75) {
    suggestions.push({
      id: 1,
      title: '提升运行效率',
      description: '当前平均效率偏低，建议检查设备状态，优化运行参数',
      impact: '可提升效率5-10%',
      type: 'warning',
      impactType: 'warning',
      icon: TrendCharts
    })
  }
  
  if (stats.loadFactor < 0.7) {
    suggestions.push({
      id: 2,
      title: '优化负荷分配',
      description: '负荷因子较低，建议平衡各时段用电负荷',
      impact: '可降低峰值需量10-15%',
      type: 'primary',
      impactType: 'primary',
      icon: DataAnalysis
    })
  }
  
  if (stats.energyPerUnit > 0.02) {
    suggestions.push({
      id: 3,
      title: '降低单位能耗',
      description: '单位能耗偏高，建议采用变频调速等节能技术',
      impact: '可节约能耗15-20%',
      type: 'success',
      impactType: 'success',
      icon: Lightning
    })
  }
  
  return suggestions
})

// 辅助函数
function getChangeClass(change: number | null | undefined, isNegativeBetter: boolean): string {
  if (change === null || change === undefined) return ''
  
  if (isNegativeBetter) {
    return change > 0 ? 'change-negative' : change < 0 ? 'change-positive' : 'change-neutral'
  } else {
    return change > 0 ? 'change-positive' : change < 0 ? 'change-negative' : 'change-neutral'
  }
}

function getChangeIcon(change: number | null | undefined): string {
  if (change === null || change === undefined) return 'Minus'
  return change > 0 ? 'ArrowUp' : change < 0 ? 'ArrowDown' : 'Minus'
}
</script>

<style lang="scss" scoped>
.statistics-table {
  .metric-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
  }
  
  .value-cell {
    font-family: 'Monaco', 'Menlo', monospace;
    font-weight: 600;
  }
  
  .change-cell {
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 600;
    
    &.change-positive {
      color: #67C23A;
    }
    
    &.change-negative {
      color: #F56C6C;
    }
    
    &.change-neutral {
      color: var(--el-text-color-regular);
    }
  }
  
  .description-cell {
    cursor: help;
  }
  
  .trend-cell {
    .trend-text {
      display: block;
      font-size: 12px;
      margin-top: 4px;
      text-align: center;
    }
  }
  
  .summary-section {
    margin: 20px 0;
    padding: 20px;
    background: var(--el-bg-color-page);
    border-radius: 8px;
  }
  
  .analysis-section {
    margin-top: 20px;
    
    h4 {
      margin-bottom: 16px;
      color: var(--el-text-color-primary);
    }
    
    .suggestion-content {
      h5 {
        margin: 0 0 8px 0;
        color: var(--el-text-color-primary);
      }
      
      p {
        margin: 0 0 8px 0;
        color: var(--el-text-color-regular);
        line-height: 1.5;
      }
      
      .suggestion-impact {
        margin-top: 8px;
      }
    }
  }
}
</style>
