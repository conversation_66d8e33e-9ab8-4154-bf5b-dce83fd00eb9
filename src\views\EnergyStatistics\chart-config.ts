import * as echarts from 'echarts'
import type { EnergyData, EnergyTrend, EnergyStatistics } from '@/types'
import dayjs from 'dayjs'

interface ChartRefs {
  trendChart?: HTMLElement
  powerChart?: HTMLElement
  heatmapChart?: HTMLElement
  costChart?: HTMLElement
}

interface ChartData {
  trendData: EnergyTrend[]
  energyData: EnergyData[]
  statistics: EnergyStatistics
  trendType: string
}

export function initEnergyCharts(refs: ChartRefs, data: ChartData) {
  const instances: any = {}

  // 趋势图
  if (refs.trendChart) {
    // 检查是否已经初始化，如果是则销毁
    const existingInstance = echarts.getInstanceByDom(refs.trendChart)
    if (existingInstance) {
      existingInstance.dispose()
    }
    instances.trendChart = echarts.init(refs.trendChart)
    updateTrendChart(instances.trendChart, data.trendData, data.trendType)
  }

  // 功率分布图
  if (refs.powerChart) {
    const existingInstance = echarts.getInstanceByDom(refs.powerChart)
    if (existingInstance) {
      existingInstance.dispose()
    }
    instances.powerChart = echarts.init(refs.powerChart)
    updatePowerChart(instances.powerChart, data.energyData)
  }

  // 效率热力图
  if (refs.heatmapChart) {
    const existingInstance = echarts.getInstanceByDom(refs.heatmapChart)
    if (existingInstance) {
      existingInstance.dispose()
    }
    instances.heatmapChart = echarts.init(refs.heatmapChart)
    updateHeatmapChart(instances.heatmapChart, data.energyData)
  }

  // 成本构成图
  if (refs.costChart) {
    const existingInstance = echarts.getInstanceByDom(refs.costChart)
    if (existingInstance) {
      existingInstance.dispose()
    }
    instances.costChart = echarts.init(refs.costChart)
    updateCostChart(instances.costChart, data.statistics)
  }

  return instances
}

function updateTrendChart(chart: echarts.ECharts, trendData: EnergyTrend[], type: string) {
  const dates = trendData.map(item => dayjs(item.date).format('MM-DD'))
  
  let seriesData: number[]
  let yAxisName: string
  let color: string
  
  switch (type) {
    case 'energy':
      seriesData = trendData.map(item => item.energy)
      yAxisName = '能耗 (kWh)'
      color = '#409EFF'
      break
    case 'cost':
      seriesData = trendData.map(item => item.cost)
      yAxisName = '成本 (元)'
      color = '#E6A23C'
      break
    case 'efficiency':
      seriesData = trendData.map(item => item.efficiency)
      yAxisName = '效率 (%)'
      color = '#67C23A'
      break
    default:
      seriesData = trendData.map(item => item.energy)
      yAxisName = '能耗 (kWh)'
      color = '#409EFF'
  }
  
  const option = {
    title: {
      text: `${type === 'energy' ? '能耗' : type === 'cost' ? '成本' : '效率'}趋势`,
      left: 'center',
      textStyle: { fontSize: 16 }
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        const param = params[0]
        return `${param.name}<br/>${param.seriesName}: ${param.value}${type === 'efficiency' ? '%' : type === 'cost' ? '元' : 'kWh'}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: yAxisName,
      nameLocation: 'middle',
      nameGap: 50
    },
    series: [{
      name: yAxisName,
      type: 'line',
      data: seriesData,
      smooth: true,
      lineStyle: {
        color: color,
        width: 3
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: color + '40' },
          { offset: 1, color: color + '10' }
        ])
      },
      symbol: 'circle',
      symbolSize: 6
    }]
  }
  
  chart.setOption(option)
}

function updatePowerChart(chart: echarts.ECharts, energyData: EnergyData[]) {
  // 按功率区间统计分布
  const powerRanges = [
    { min: 0, max: 50, label: '0-50kW' },
    { min: 50, max: 70, label: '50-70kW' },
    { min: 70, max: 90, label: '70-90kW' },
    { min: 90, max: 110, label: '90-110kW' },
    { min: 110, max: 130, label: '110-130kW' },
    { min: 130, max: 999, label: '>130kW' }
  ]
  
  const distribution = powerRanges.map(range => {
    const count = energyData.filter(item => 
      item.power >= range.min && item.power < range.max
    ).length
    return {
      name: range.label,
      value: count
    }
  })
  
  const option = {
    title: {
      text: '功率分布',
      left: 'center',
      textStyle: { fontSize: 16 }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle'
    },
    series: [{
      name: '功率分布',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['60%', '50%'],
      data: distribution,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        formatter: '{b}\n{d}%'
      }
    }]
  }
  
  chart.setOption(option)
}

function updateHeatmapChart(chart: echarts.ECharts, energyData: EnergyData[]) {
  // 生成24小时x7天的效率热力图数据
  const hours = Array.from({ length: 24 }, (_, i) => i)
  const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  
  const heatmapData: any[] = []
  
  // 按小时和星期统计平均效率
  for (let day = 0; day < 7; day++) {
    for (let hour = 0; hour < 24; hour++) {
      const filteredData = energyData.filter(item => {
        const date = dayjs(item.timestamp)
        return date.day() === day && date.hour() === hour
      })
      
      const avgEfficiency = filteredData.length > 0 
        ? filteredData.reduce((sum, item) => sum + item.efficiency, 0) / filteredData.length
        : 0
      
      heatmapData.push([hour, day, Math.round(avgEfficiency * 10) / 10])
    }
  }
  
  const option = {
    title: {
      text: '效率热力图',
      left: 'center',
      textStyle: { fontSize: 16 }
    },
    tooltip: {
      position: 'top',
      formatter: function(params: any) {
        return `${days[params.value[1]]} ${params.value[0]}:00<br/>效率: ${params.value[2]}%`
      }
    },
    grid: {
      height: '60%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: hours.map(h => h + ':00'),
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: days,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 60,
      max: 90,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '5%',
      inRange: {
        color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
      }
    },
    series: [{
      name: '效率',
      type: 'heatmap',
      data: heatmapData,
      label: {
        show: false
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  
  chart.setOption(option)
}

function updateCostChart(chart: echarts.ECharts, statistics: EnergyStatistics) {
  // 成本构成分析
  const totalCost = statistics.totalCost
  const energyCost = totalCost * 0.85 // 电费占85%
  const maintenanceCost = totalCost * 0.10 // 维护费占10%
  const otherCost = totalCost * 0.05 // 其他费用占5%
  
  const costData = [
    { name: '电费', value: energyCost, itemStyle: { color: '#409EFF' } },
    { name: '维护费', value: maintenanceCost, itemStyle: { color: '#67C23A' } },
    { name: '其他费用', value: otherCost, itemStyle: { color: '#E6A23C' } }
  ]
  
  const option = {
    title: {
      text: '成本构成',
      left: 'center',
      textStyle: { fontSize: 16 }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle'
    },
    series: [{
      name: '成本构成',
      type: 'pie',
      radius: '60%',
      center: ['60%', '50%'],
      data: costData,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        formatter: '{b}\n¥{c}\n({d}%)'
      }
    }]
  }
  
  chart.setOption(option)
}
