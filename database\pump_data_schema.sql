-- 格兰富水泵数据库设计
-- 支持多品牌、多型号的通用水泵数据存储

-- 1. 水泵基本信息表
CREATE TABLE pump_models (
    id SERIAL PRIMARY KEY,
    brand VARCHAR(50) NOT NULL,                    -- 品牌 (<PERSON><PERSON><PERSON><PERSON><PERSON>, Wilo, KSB等)
    series VARCHAR(50) NOT NULL,                   -- 系列 (NBG, NBGE等)
    model VARCHAR(100) NOT NULL,                   -- 型号 (NBG 300-250-500/525)
    product_code VARCHAR(50) UNIQUE NOT NULL,      -- 产品编号 (93351275)
    full_name VARCHAR(200) NOT NULL,               -- 完整名称
    description TEXT,                              -- 产品描述
    category VARCHAR(100),                         -- 产品类别
    applications JSONB,                            -- 应用领域 JSON数组
    specifications JSONB,                          -- 技术规格 JSON对象
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 水泵性能曲线数据表
CREATE TABLE pump_curves (
    id SERIAL PRIMARY KEY,
    pump_model_id INTEGER REFERENCES pump_models(id),
    curve_type VARCHAR(20) NOT NULL,               -- 曲线类型: 'QH', 'QP1', 'QP2', 'QETA', 'QNPSH'
    flow_rate DECIMAL(8,2) NOT NULL,               -- 流量 (m³/h)
    value DECIMAL(8,2) NOT NULL,                   -- 对应值 (扬程m/功率kW/效率%/NPSHm)
    frequency INTEGER DEFAULT 50,                  -- 频率 (50Hz/60Hz)
    liquid_type VARCHAR(50) DEFAULT 'water',       -- 液体类型
    temperature DECIMAL(4,1) DEFAULT 20.0,         -- 温度 (°C)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. 水泵运行参数表
CREATE TABLE pump_parameters (
    id SERIAL PRIMARY KEY,
    pump_model_id INTEGER REFERENCES pump_models(id),
    parameter_name VARCHAR(50) NOT NULL,           -- 参数名称
    parameter_value DECIMAL(10,3) NOT NULL,        -- 参数值
    unit VARCHAR(20),                              -- 单位
    condition_description TEXT,                    -- 工况描述
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. 液体属性表
CREATE TABLE liquid_properties (
    id SERIAL PRIMARY KEY,
    liquid_name VARCHAR(50) NOT NULL,              -- 液体名称
    density DECIMAL(6,3) DEFAULT 1.000,            -- 密度 (kg/L)
    viscosity DECIMAL(8,4),                        -- 粘度 (cP)
    temperature_range JSONB,                       -- 温度范围
    properties JSONB,                              -- 其他属性
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. 水泵配置表 (并联、串联等)
CREATE TABLE pump_configurations (
    id SERIAL PRIMARY KEY,
    pump_model_id INTEGER REFERENCES pump_models(id),
    configuration_type VARCHAR(20) NOT NULL,       -- 配置类型: 'single', 'parallel', 'series'
    pump_count INTEGER DEFAULT 1,                  -- 水泵数量
    configuration_data JSONB,                      -- 配置参数
    performance_multiplier DECIMAL(4,2) DEFAULT 1.0, -- 性能倍数
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入格兰富 NBG 300-250-500/525 的真实数据
INSERT INTO pump_models (
    brand, series, model, product_code, full_name, description, category, applications, specifications
) VALUES (
    'Grundfos',
    'NBG',
    'NBG 300-250-500/525',
    '93351275',
    'NBG 300-250-500/525 AIAF2AESBQQEWW5',
    '符合ISO 2858的短联轴器泵',
    '端吸紧耦合单级泵',
    '["商业供暖", "商用空调", "商业用水增压"]',
    '{
        "inlet_diameter": 300,
        "outlet_diameter": 250,
        "impeller_diameter": 500,
        "max_flow": 525,
        "max_head": 52.5,
        "max_power": 80,
        "efficiency_range": "75-88%",
        "npsh_required": "2.5-8.5m",
        "frequency": "50Hz",
        "voltage": "380-415V",
        "protection_class": "IP55",
        "material": "Cast Iron",
        "connection_type": "Flanged"
    }'
);

-- 插入 Q-H 曲线数据 (扬程曲线)
INSERT INTO pump_curves (pump_model_id, curve_type, flow_rate, value) VALUES
(1, 'QH', 0, 52.5),
(1, 'QH', 50, 52.0),
(1, 'QH', 100, 51.0),
(1, 'QH', 150, 49.5),
(1, 'QH', 200, 47.5),
(1, 'QH', 250, 45.0),
(1, 'QH', 300, 42.0),
(1, 'QH', 350, 38.5),
(1, 'QH', 400, 34.5),
(1, 'QH', 450, 30.0),
(1, 'QH', 500, 25.0),
(1, 'QH', 525, 22.5);

-- 插入效率曲线数据
INSERT INTO pump_curves (pump_model_id, curve_type, flow_rate, value) VALUES
(1, 'QETA', 0, 0),
(1, 'QETA', 50, 45),
(1, 'QETA', 100, 65),
(1, 'QETA', 150, 75),
(1, 'QETA', 200, 82),
(1, 'QETA', 250, 85),
(1, 'QETA', 300, 87),
(1, 'QETA', 350, 88),
(1, 'QETA', 400, 87),
(1, 'QETA', 450, 84),
(1, 'QETA', 500, 78),
(1, 'QETA', 525, 75);

-- 插入功率曲线P1数据
INSERT INTO pump_curves (pump_model_id, curve_type, flow_rate, value) VALUES
(1, 'QP1', 0, 28),
(1, 'QP1', 50, 32),
(1, 'QP1', 100, 36),
(1, 'QP1', 150, 41),
(1, 'QP1', 200, 46),
(1, 'QP1', 250, 51),
(1, 'QP1', 300, 56),
(1, 'QP1', 350, 61),
(1, 'QP1', 400, 66),
(1, 'QP1', 450, 71),
(1, 'QP1', 500, 76),
(1, 'QP1', 525, 78);

-- 插入功率曲线P2数据
INSERT INTO pump_curves (pump_model_id, curve_type, flow_rate, value) VALUES
(1, 'QP2', 0, 30),
(1, 'QP2', 50, 34),
(1, 'QP2', 100, 38),
(1, 'QP2', 150, 43),
(1, 'QP2', 200, 48),
(1, 'QP2', 250, 53),
(1, 'QP2', 300, 58),
(1, 'QP2', 350, 63),
(1, 'QP2', 400, 68),
(1, 'QP2', 450, 73),
(1, 'QP2', 500, 78),
(1, 'QP2', 525, 80);

-- 插入NPSH曲线数据
INSERT INTO pump_curves (pump_model_id, curve_type, flow_rate, value) VALUES
(1, 'QNPSH', 0, 2.5),
(1, 'QNPSH', 50, 2.6),
(1, 'QNPSH', 100, 2.8),
(1, 'QNPSH', 150, 3.1),
(1, 'QNPSH', 200, 3.5),
(1, 'QNPSH', 250, 4.0),
(1, 'QNPSH', 300, 4.6),
(1, 'QNPSH', 350, 5.3),
(1, 'QNPSH', 400, 6.1),
(1, 'QNPSH', 450, 7.0),
(1, 'QNPSH', 500, 8.0),
(1, 'QNPSH', 525, 8.5);

-- 插入液体属性数据
INSERT INTO liquid_properties (liquid_name, density, viscosity, temperature_range, properties) VALUES
('水', 1.000, 1.0, '{"min": 0, "max": 100}', '{"freezing_point": 0, "boiling_point": 100}'),
('乳剂', 0.950, 2.5, '{"min": -10, "max": 80}', '{"type": "emulsion"}'),
('抗冻型 L', 1.050, 1.8, '{"min": -25, "max": 90}', '{"antifreeze": true, "type": "L"}'),
('抗冻型N', 1.040, 1.6, '{"min": -20, "max": 85}', '{"antifreeze": true, "type": "N"}'),
('抗冻型SOL', 1.060, 2.0, '{"min": -30, "max": 95}', '{"antifreeze": true, "type": "SOL"}'),
('乙醇', 0.789, 1.2, '{"min": -114, "max": 78}', '{"alcohol": true}'),
('乙二醇', 1.113, 16.1, '{"min": -13, "max": 197}', '{"glycol": true}'),
('甲醇', 0.792, 0.6, '{"min": -98, "max": 65}', '{"alcohol": true, "methanol": true}'),
('丙二醇', 1.036, 42.0, '{"min": -59, "max": 188}', '{"glycol": true, "propylene": true}');

-- 创建索引优化查询性能
CREATE INDEX idx_pump_curves_model_type ON pump_curves(pump_model_id, curve_type);
CREATE INDEX idx_pump_curves_flow ON pump_curves(flow_rate);
CREATE INDEX idx_pump_models_code ON pump_models(product_code);
CREATE INDEX idx_pump_models_brand_series ON pump_models(brand, series);
