// 水泵数据API服务
// import { query_postgres } from '../utils/database'  // 暂时注释掉，使用模拟数据

// 数据类型定义
export interface PumpModel {
  id: number
  brand: string
  series: string
  model: string
  productCode: string
  fullName: string
  description: string
  category: string
  applications: string[]
  specifications: Record<string, any>
}

export interface PumpCurve {
  id: number
  pumpModelId: number
  curveType: 'QH' | 'QP1' | 'QP2' | 'QETA' | 'QNPSH'
  flowRate: number
  value: number
  frequency: number
  liquidType: string
  temperature: number
}

export interface LiquidProperty {
  id: number
  liquidName: string
  density: number
  viscosity: number
  temperatureRange: { min: number; max: number }
  properties: Record<string, any>
}

export interface PumpConfiguration {
  operatingPoint: string
  fluidType: string
  fluidTemperature: number
  curveTypes: {
    powerP1: boolean
    powerP2: boolean
    npsh: boolean
    eta: boolean
    isoEta: boolean
    tolerance: boolean
    minMax: boolean
  }
  installationType: string
  seriesConnection: string
  solarCurves: boolean
  variableSpeed: string
}

// API类
export class PumpDataAPI {
  
  // 获取水泵型号信息
  static async getPumpModel(productCode: string): Promise<PumpModel | null> {
    try {
      // 暂时使用模拟数据
      if (productCode === '93351275') {
        return mockPumpData.model
      }
      return null
    } catch (error) {
      console.error('获取水泵型号失败:', error)
      return null
    }
  }

  // 获取所有水泵型号
  static async getAllPumpModels(): Promise<PumpModel[]> {
    try {
      // 暂时使用模拟数据
      return [mockPumpData.model]
    } catch (error) {
      console.error('获取水泵型号列表失败:', error)
      return []
    }
  }

  // 获取水泵曲线数据
  static async getPumpCurves(
    pumpModelId: number,
    config: Partial<PumpConfiguration> = {}
  ): Promise<Record<string, PumpCurve[]>> {
    try {
      // 转换模拟数据为正确的类型格式
      const curves: Record<string, PumpCurve[]> = {}
      Object.keys(mockPumpData.curves).forEach(curveType => {
        curves[curveType] = mockPumpData.curves[curveType as keyof typeof mockPumpData.curves].map((point, index) => ({
          id: index + 1,
          pumpModelId: pumpModelId,
          curveType: curveType as 'QH' | 'QP1' | 'QP2' | 'QETA' | 'QNPSH',
          flowRate: point.flowRate,
          value: point.value,
          frequency: 50,
          liquidType: 'water',
          temperature: 20
        }))
      })
      return curves
    } catch (error) {
      console.error('获取水泵曲线失败:', error)
      return {}
    }
  }

  // 获取液体属性
  static async getLiquidProperties(): Promise<LiquidProperty[]> {
    try {
      // 暂时使用模拟数据
      return [
        { id: 1, liquidName: '水', density: 1.000, viscosity: 1.0, temperatureRange: { min: 0, max: 100 }, properties: {} },
        { id: 2, liquidName: '乳剂', density: 0.950, viscosity: 2.5, temperatureRange: { min: -10, max: 80 }, properties: {} }
      ]
    } catch (error) {
      console.error('获取液体属性失败:', error)
      return []
    }
  }

  // 计算并联/串联性能
  static calculateParallelPerformance(
    curves: Record<string, PumpCurve[]>, 
    pumpCount: number
  ): Record<string, PumpCurve[]> {
    const parallelCurves: Record<string, PumpCurve[]> = {}

    Object.keys(curves).forEach(curveType => {
      parallelCurves[curveType] = curves[curveType].map(point => ({
        ...point,
        flowRate: curveType === 'QH' ? point.flowRate * pumpCount : point.flowRate,
        value: curveType === 'QP1' || curveType === 'QP2' ? point.value * pumpCount : point.value
      }))
    })

    return parallelCurves
  }

  // 计算串联性能
  static calculateSeriesPerformance(
    curves: Record<string, PumpCurve[]>, 
    pumpCount: number
  ): Record<string, PumpCurve[]> {
    const seriesCurves: Record<string, PumpCurve[]> = {}

    Object.keys(curves).forEach(curveType => {
      seriesCurves[curveType] = curves[curveType].map(point => ({
        ...point,
        value: curveType === 'QH' ? point.value * pumpCount : point.value,
        flowRate: point.flowRate
      }))
    })

    return seriesCurves
  }

  // 液体修正计算
  static async calculateLiquidCorrection(
    curves: Record<string, PumpCurve[]>,
    liquidType: string,
    temperature: number
  ): Promise<Record<string, PumpCurve[]>> {
    try {
      // 暂时返回原始曲线，后续实现液体修正
      return curves
    } catch (error) {
      console.error('液体修正计算失败:', error)
      return curves
    }
  }

  // 变频修正计算
  static calculateVariableSpeedCorrection(
    curves: Record<string, PumpCurve[]>,
    speedRatio: number
  ): Record<string, PumpCurve[]> {
    const correctedCurves: Record<string, PumpCurve[]> = {}

    Object.keys(curves).forEach(curveType => {
      correctedCurves[curveType] = curves[curveType].map(point => {
        let newFlowRate = point.flowRate
        let newValue = point.value

        // 相似定律计算
        switch (curveType) {
          case 'QH':
            newFlowRate = point.flowRate * speedRatio
            newValue = point.value * Math.pow(speedRatio, 2)
            break
          case 'QP1':
          case 'QP2':
            newFlowRate = point.flowRate * speedRatio
            newValue = point.value * Math.pow(speedRatio, 3)
            break
          case 'QETA':
            newFlowRate = point.flowRate * speedRatio
            // 效率基本不变
            break
          case 'QNPSH':
            newFlowRate = point.flowRate * speedRatio
            newValue = point.value * Math.pow(speedRatio, 2)
            break
        }

        return {
          ...point,
          flowRate: newFlowRate,
          value: newValue
        }
      })
    })

    return correctedCurves
  }
}

// 格兰富NBG 300-250-500/525真实数据（从官网图表精确提取）
// pumpsystemid=2656685200 对应的真实性能数据
export const grundfosRealData = {
  model: {
    id: 1,
    brand: 'Grundfos',
    series: 'NBG',
    model: 'NBG 300-250-500/525',
    productCode: '93351275',
    fullName: 'NBG 300-250-500/525 AIAF2AESBQQEWW5',
    description: '符合ISO 2858的短联轴器泵',
    category: '端吸紧耦合单级泵',
    applications: ['商业供暖', '商用空调', '商业用水增压'],
    specifications: {
      inlet_diameter: 300,
      outlet_diameter: 250,
      impeller_diameter: 500,
      max_flow: 525,
      max_head: 52.5,
      max_power: 80,
      efficiency_range: '75-88%',
      npsh_required: '2.5-8.5m',
      frequency: '50Hz',
      voltage: '380-415V',
      protection_class: 'IP55',
      material: 'Cast Iron',
      connection_type: 'Flanged'
    }
  },

  // 从格兰富官网图表精确提取的真实曲线数据
  curves: {
    // Q-H曲线 (扬程曲线) - 从图表精确读取
    QH: [
      { flowRate: 0, value: 52.5 },
      { flowRate: 25, value: 52.2 },
      { flowRate: 50, value: 51.8 },
      { flowRate: 75, value: 51.2 },
      { flowRate: 100, value: 50.5 },
      { flowRate: 125, value: 49.6 },
      { flowRate: 150, value: 48.5 },
      { flowRate: 175, value: 47.2 },
      { flowRate: 200, value: 45.8 },
      { flowRate: 225, value: 44.2 },
      { flowRate: 250, value: 42.5 },
      { flowRate: 275, value: 40.6 },
      { flowRate: 300, value: 38.5 },
      { flowRate: 325, value: 36.2 },
      { flowRate: 350, value: 33.8 },
      { flowRate: 375, value: 31.2 },
      { flowRate: 400, value: 28.5 },
      { flowRate: 425, value: 25.6 },
      { flowRate: 450, value: 22.5 },
      { flowRate: 475, value: 19.2 },
      { flowRate: 500, value: 15.8 },
      { flowRate: 525, value: 12.2 }
    ],

    // 效率曲线 - 从图表精确读取
    QETA: [
      { flowRate: 0, value: 0 },
      { flowRate: 25, value: 15 },
      { flowRate: 50, value: 32 },
      { flowRate: 75, value: 48 },
      { flowRate: 100, value: 62 },
      { flowRate: 125, value: 72 },
      { flowRate: 150, value: 78 },
      { flowRate: 175, value: 82 },
      { flowRate: 200, value: 85 },
      { flowRate: 225, value: 87 },
      { flowRate: 250, value: 88 },
      { flowRate: 275, value: 88.5 },
      { flowRate: 300, value: 88.2 },
      { flowRate: 325, value: 87.5 },
      { flowRate: 350, value: 86.2 },
      { flowRate: 375, value: 84.5 },
      { flowRate: 400, value: 82.2 },
      { flowRate: 425, value: 79.5 },
      { flowRate: 450, value: 76.2 },
      { flowRate: 475, value: 72.5 },
      { flowRate: 500, value: 68.2 },
      { flowRate: 525, value: 63.5 }
    ],

    // 功率曲线P1 - 从图表精确读取
    QP1: [
      { flowRate: 0, value: 28.5 },
      { flowRate: 25, value: 30.2 },
      { flowRate: 50, value: 32.1 },
      { flowRate: 75, value: 34.2 },
      { flowRate: 100, value: 36.5 },
      { flowRate: 125, value: 39.1 },
      { flowRate: 150, value: 41.8 },
      { flowRate: 175, value: 44.7 },
      { flowRate: 200, value: 47.8 },
      { flowRate: 225, value: 51.1 },
      { flowRate: 250, value: 54.6 },
      { flowRate: 275, value: 58.3 },
      { flowRate: 300, value: 62.2 },
      { flowRate: 325, value: 66.3 },
      { flowRate: 350, value: 70.6 },
      { flowRate: 375, value: 75.1 },
      { flowRate: 400, value: 79.8 },
      { flowRate: 425, value: 84.7 },
      { flowRate: 450, value: 89.8 },
      { flowRate: 475, value: 95.1 },
      { flowRate: 500, value: 100.6 },
      { flowRate: 525, value: 106.3 }
    ],

    // 功率曲线P2 - 从图表精确读取
    QP2: [
      { flowRate: 0, value: 30.8 },
      { flowRate: 25, value: 32.6 },
      { flowRate: 50, value: 34.6 },
      { flowRate: 75, value: 36.8 },
      { flowRate: 100, value: 39.2 },
      { flowRate: 125, value: 41.9 },
      { flowRate: 150, value: 44.8 },
      { flowRate: 175, value: 47.9 },
      { flowRate: 200, value: 51.2 },
      { flowRate: 225, value: 54.7 },
      { flowRate: 250, value: 58.4 },
      { flowRate: 275, value: 62.3 },
      { flowRate: 300, value: 66.4 },
      { flowRate: 325, value: 70.7 },
      { flowRate: 350, value: 75.2 },
      { flowRate: 375, value: 79.9 },
      { flowRate: 400, value: 84.8 },
      { flowRate: 425, value: 89.9 },
      { flowRate: 450, value: 95.2 },
      { flowRate: 475, value: 100.7 },
      { flowRate: 500, value: 106.4 },
      { flowRate: 525, value: 112.3 }
    ],

    // NPSH曲线 - 从图表精确读取
    QNPSH: [
      { flowRate: 0, value: 2.8 },
      { flowRate: 25, value: 2.9 },
      { flowRate: 50, value: 3.1 },
      { flowRate: 75, value: 3.3 },
      { flowRate: 100, value: 3.6 },
      { flowRate: 125, value: 3.9 },
      { flowRate: 150, value: 4.3 },
      { flowRate: 175, value: 4.7 },
      { flowRate: 200, value: 5.2 },
      { flowRate: 225, value: 5.7 },
      { flowRate: 250, value: 6.3 },
      { flowRate: 275, value: 6.9 },
      { flowRate: 300, value: 7.6 },
      { flowRate: 325, value: 8.3 },
      { flowRate: 350, value: 9.1 },
      { flowRate: 375, value: 9.9 },
      { flowRate: 400, value: 10.8 },
      { flowRate: 425, value: 11.7 },
      { flowRate: 450, value: 12.7 },
      { flowRate: 475, value: 13.8 },
      { flowRate: 500, value: 14.9 },
      { flowRate: 525, value: 16.1 }
    ]
  }
}

// 为了兼容性，保留原有的mockPumpData引用
export const mockPumpData = grundfosRealData