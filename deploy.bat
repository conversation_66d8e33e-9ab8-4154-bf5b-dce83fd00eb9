@echo off
chcp 65001 >nul
title 智能供水平台 - 一键部署

echo.
echo ========================================
echo    智能供水平台 - 一键部署脚本
echo ========================================
echo.

:: 检查Node.js是否安装
echo [1/5] 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未检测到Node.js
    echo 💡 请先安装Node.js: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js环境检查通过

:: 检查pnpm是否安装
echo.
echo [2/5] 检查pnpm包管理器...
pnpm --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 未检测到pnpm，正在安装...
    npm install -g pnpm
    if errorlevel 1 (
        echo ❌ pnpm安装失败
        pause
        exit /b 1
    )
)
echo ✅ pnpm检查通过

:: 安装依赖
echo.
echo [3/5] 安装项目依赖...
echo 💡 这可能需要几分钟时间，请耐心等待...
pnpm install
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)
echo ✅ 依赖安装完成

:: 构建项目
echo.
echo [4/5] 构建生产版本...
pnpm run build
if errorlevel 1 (
    echo ❌ 项目构建失败
    pause
    exit /b 1
)
echo ✅ 项目构建完成

:: 检查Go环境
echo.
echo [5/5] 检查Go环境并启动服务器...
go version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未检测到Go环境
    echo 💡 请先安装Go: https://golang.org/dl/
    echo 💡 或者直接使用Python启动: python -m http.server 8080 --directory dist
    pause
    exit /b 1
)

echo ✅ Go环境检查通过
echo.
echo ========================================
echo    🚀 正在启动智能供水平台服务器...
echo ========================================
echo.

:: 启动Go服务器
go run server.go

pause