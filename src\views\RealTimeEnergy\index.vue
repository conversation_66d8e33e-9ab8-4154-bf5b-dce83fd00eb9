<template>
  <div class="real-time-energy">
    <el-row :gutter="20">
      <!-- 实时数据卡片 -->
      <el-col :span="6" v-for="item in realTimeData" :key="item.title">
        <el-card class="data-card">
          <div class="data-item">
            <div class="icon">
              <el-icon :size="32" :color="item.color">
                <component :is="item.icon" />
              </el-icon>
            </div>
            <div class="content">
              <div class="title">{{ item.title }}</div>
              <div class="value">{{ item.value }} <span class="unit">{{ item.unit }}</span></div>
              <div class="trend" :class="item.trend">
                <el-icon><component :is="item.trendIcon" /></el-icon>
                {{ item.trendValue }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 能耗趋势图 -->
    <el-card class="chart-card">
      <template #header>
        <div class="card-header">
          <el-icon><Lightning /></el-icon>
          <span>实时能耗趋势</span>
          <div class="controls">
            <el-button-group size="small">
              <el-button :type="timeRange === '1h' ? 'primary' : ''" @click="timeRange = '1h'">1小时</el-button>
              <el-button :type="timeRange === '6h' ? 'primary' : ''" @click="timeRange = '6h'">6小时</el-button>
              <el-button :type="timeRange === '24h' ? 'primary' : ''" @click="timeRange = '24h'">24小时</el-button>
            </el-button-group>
          </div>
        </div>
      </template>
      
      <div ref="chartRef" class="chart" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch } from 'vue'
import { Lightning, TrendCharts, ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

const chartRef = ref<HTMLElement>()
const timeRange = ref('1h')

// 模拟实时数据
const realTimeData = ref([
  {
    title: '当前功率',
    value: '85.6',
    unit: 'kW',
    color: '#409EFF',
    icon: 'Lightning',
    trend: 'up',
    trendIcon: 'ArrowUp',
    trendValue: '2.3'
  },
  {
    title: '当前效率',
    value: '78.2',
    unit: '%',
    color: '#67C23A',
    icon: 'TrendCharts',
    trend: 'up',
    trendIcon: 'ArrowUp',
    trendValue: '1.8'
  },
  {
    title: '累计能耗',
    value: '1,245.8',
    unit: 'kWh',
    color: '#E6A23C',
    icon: 'DataAnalysis',
    trend: 'down',
    trendIcon: 'ArrowDown',
    trendValue: '0.5'
  },
  {
    title: '运行成本',
    value: '892.3',
    unit: '元',
    color: '#F56C6C',
    icon: 'Money',
    trend: 'down',
    trendIcon: 'ArrowDown',
    trendValue: '1.2'
  }
])

let chartInstance: any = null

// 生成模拟数据
const generateData = (hours: number) => {
  const data = []
  const now = new Date()
  
  for (let i = hours * 60; i >= 0; i -= 5) {
    const time = new Date(now.getTime() - i * 60 * 1000)
    const power = 80 + Math.random() * 20
    const efficiency = 70 + Math.random() * 15
    
    data.push({
      time: time.toLocaleTimeString(),
      power: power.toFixed(1),
      efficiency: efficiency.toFixed(1)
    })
  }
  
  return data
}

const updateChart = () => {
  if (!chartInstance) return
  
  const hours = parseInt(timeRange.value)
  const data = generateData(hours)
  
  const option = {
    title: {
      text: '实时能耗监控',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['功率', '效率'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.time),
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '功率 (kW)',
        position: 'left',
        axisLabel: {
          formatter: '{value} kW'
        }
      },
      {
        type: 'value',
        name: '效率 (%)',
        position: 'right',
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '功率',
        type: 'line',
        yAxisIndex: 0,
        data: data.map(item => item.power),
        smooth: true,
        lineStyle: {
          color: '#409EFF'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
          ])
        }
      },
      {
        name: '效率',
        type: 'line',
        yAxisIndex: 1,
        data: data.map(item => item.efficiency),
        smooth: true,
        lineStyle: {
          color: '#67C23A'
        }
      }
    ]
  }
  
  chartInstance.setOption(option)
}

watch(timeRange, updateChart)

onMounted(async () => {
  await nextTick()
  
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value)
    updateChart()
  }
  
  // 模拟实时数据更新
  setInterval(() => {
    realTimeData.value.forEach(item => {
      const change = (Math.random() - 0.5) * 2
      const currentValue = parseFloat(item.value.replace(',', ''))
      const newValue = Math.max(0, currentValue + change)
      item.value = newValue.toLocaleString('zh-CN', { maximumFractionDigits: 1 })
    })
  }, 3000)
})
</script>

<style lang="scss" scoped>
.real-time-energy {
  .data-card {
    margin-bottom: 20px;
    
    .data-item {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .content {
        flex: 1;
        
        .title {
          font-size: 14px;
          color: var(--el-text-color-regular);
          margin-bottom: 8px;
        }
        
        .value {
          font-size: 24px;
          font-weight: bold;
          color: var(--el-text-color-primary);
          
          .unit {
            font-size: 14px;
            font-weight: normal;
            color: var(--el-text-color-regular);
          }
        }
        
        .trend {
          font-size: 12px;
          display: flex;
          align-items: center;
          gap: 4px;
          margin-top: 4px;
          
          &.up {
            color: #67C23A;
          }
          
          &.down {
            color: #F56C6C;
          }
        }
      }
    }
  }
  
  .chart-card {
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      > div:first-child {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
      }
    }
    
    .chart {
      height: 400px;
      width: 100%;
    }
  }
}
</style>
