// 最小二乘拟合多项式

export function LS(x, y, m){

    if ( x.length != y.length ){
        alert("输入长度不一致！")

    }else{
        var ATA = []
        var ATy = []

        for ( var m_ = 0; m_ < 2*m+1; m_++ ){
            var sum = 0
            for ( var i = 0; i < x.length; i++ ){
                sum += x[i] ** m_
            }

            ATA.push(sum)
        }

        for ( var m_ = 0; m_ < m+1; m_++ ){
            var sum = 0
            for ( i = 0; i < x.length; i++ ){
                sum += (x[i] ** m_) * y[i]
            }

            ATy.push(sum)
        }

        // console.log("系数：", ATA)
        // console.log("偏置：", ATy)

        var a = new Array(m+1)
        for ( var i = 0; i < a.length; i++ ){
            a[i] = ATA.slice(i, (m+1)+i )
        }

        // console.log("系数矩阵", a)

        var back_x = gauss<PERSON>eidel(a, ATy, m)

    }
    return back_x

}


function gauss<PERSON><PERSON>del(a, b, m){

    var x = new Array(m+1)
    var xCopy = new Array(m+1)
    for ( var item = 0; item < x.length; item++ ){
        x[item] = 0
        xCopy[item] = 0
    }

    for ( var k = 0; k < (m-1)*1000; k++ ){
        xCopy = x

        for ( var i = 0; i < m+1; i++ ){
            var sumk1 = 0
            for ( var j = 0; j < m+1; j++ ){
                if ( j == i ){
                    continue
                }
                sumk1 += a[i][j] * x[j]
            }

            x[i] = (1-1.7)*x[i] + 1.7/a[i][i]*(b[i]-sumk1)
        }

        var sum = 0
        for ( var i = 0; i < m+1; i++ ){
            if ( x[i] - xCopy < 1e-8 ){
                sum += 1
            }
        }

        if ( sum == m+1 ){
            // console.log("迭代了： " , k, " 次")
            break
        }
    }

    // console.log("参数：", x)
    var sumfd = 0
    for (var ir=0; ir<x.length;ir++){
        sumfd += 700**(ir) * x[ir]
    }
    // console.log(sumfd)
    return x
}


// document.write(LS([1,2,3,4,6,7,8], [2,3,6,7,5,3,2], 2))
// document.write(LS([0,200,400,600,800,1000,1200], [55,54.12,54.12,53,50.54,45.85,39.58], 6))