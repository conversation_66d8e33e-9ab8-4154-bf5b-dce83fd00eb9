<script setup lang="ts">
import { ref, onMounted, nextTick, watch, onBeforeUnmount, computed } from 'vue';
import * as echarts from 'echarts';
import { useElementSize, useResizeObserver, useIntervalFn } from '@vueuse/core';
import { useGatewayStore } from '@/stores/gateway';
import type { InverterData } from '@/types/gateway';

const props = defineProps<{
  deviceId: string;
  dataPoints?: number; // 保留多少数据点
  pollInterval?: number; // 轮询间隔，单位毫秒
}>();

const chartContainer = ref<HTMLDivElement | null>(null);
const { width, height } = useElementSize(chartContainer);
const chart = ref<echarts.ECharts | null>(null);
const gatewayStore = useGatewayStore();
const isLoading = ref(true);
const error = ref<string | null>(null);

// 数据系列
const frequencyData = ref<Array<[number, number]>>([]);
const currentData = ref<Array<[number, number]>>([]);
const voltageData = ref<Array<[number, number]>>([]);
const powerData = ref<Array<[number, number]>>([]);
const temperatureData = ref<Array<[number, number]>>([]);

// 默认值
const dataPoints = computed(() => props.dataPoints || 100);
const pollInterval = computed(() => props.pollInterval || 5000);

// 最新数据
const latestData = ref<InverterData | null>(null);

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;
  
  chart.value?.dispose();
  
  chart.value = echarts.init(chartContainer.value, 'dark');
  
  const option = {
    title: {
      text: '变频器实时数据',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['频率', '电流', '电压', '功率', '温度'],
      top: '30px'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'time',
      boundaryGap: false,
      axisLabel: {
        formatter: (value: number) => {
          const date = new Date(value);
          return `${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}`;
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '频率/电流',
        position: 'left',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#5470C6'
          }
        },
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '电压/功率/温度',
        position: 'right',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#91CC75'
          }
        },
        axisLabel: {
          formatter: '{value}'
        }
      }
    ],
    series: [
      {
        name: '频率',
        type: 'line',
        yAxisIndex: 0,
        data: frequencyData.value,
        smooth: true,
        showSymbol: false,
        emphasis: {
          focus: 'series'
        }
      },
      {
        name: '电流',
        type: 'line',
        yAxisIndex: 0,
        data: currentData.value,
        smooth: true,
        showSymbol: false,
        emphasis: {
          focus: 'series'
        }
      },
      {
        name: '电压',
        type: 'line',
        yAxisIndex: 1,
        data: voltageData.value,
        smooth: true,
        showSymbol: false,
        emphasis: {
          focus: 'series'
        }
      },
      {
        name: '功率',
        type: 'line',
        yAxisIndex: 1,
        data: powerData.value,
        smooth: true,
        showSymbol: false,
        emphasis: {
          focus: 'series'
        }
      },
      {
        name: '温度',
        type: 'line',
        yAxisIndex: 1,
        data: temperatureData.value,
        smooth: true,
        showSymbol: false,
        emphasis: {
          focus: 'series'
        }
      }
    ]
  };
  
  chart.value.setOption(option);
  isLoading.value = false;
};

// 更新图表数据
const updateChartData = (data: InverterData) => {
  latestData.value = data;
  const timestamp = data.timestamp;
  
  // 添加新数据
  frequencyData.value.push([timestamp, data.frequency]);
  currentData.value.push([timestamp, data.current]);
  voltageData.value.push([timestamp, data.voltage]);
  powerData.value.push([timestamp, data.power]);
  temperatureData.value.push([timestamp, data.temperature]);
  
  // 限制数据点数量
  if (frequencyData.value.length > dataPoints.value) {
    frequencyData.value = frequencyData.value.slice(-dataPoints.value);
    currentData.value = currentData.value.slice(-dataPoints.value);
    voltageData.value = voltageData.value.slice(-dataPoints.value);
    powerData.value = powerData.value.slice(-dataPoints.value);
    temperatureData.value = temperatureData.value.slice(-dataPoints.value);
  }
  
  // 更新图表
  if (chart.value) {
    chart.value.setOption({
      series: [
        { data: frequencyData.value },
        { data: currentData.value },
        { data: voltageData.value },
        { data: powerData.value },
        { data: temperatureData.value }
      ]
    });
  }
};

// 获取实时数据
const fetchDeviceData = async () => {
  try {
    error.value = null;
    const data = await gatewayStore.getInverterLatestData(props.deviceId, 'inverter-1'); // 假设每个网关都有一个默认变频器
    if (data) {
      updateChartData(data);
    }
  } catch (err) {
    error.value = (err as Error).message;
    console.error('获取设备数据失败:', err);
  }
};

// 创建轮询函数
const { pause, resume, isActive } = useIntervalFn(fetchDeviceData, pollInterval.value);

// 监听容器尺寸变化
useResizeObserver(chartContainer, () => {
  nextTick(() => {
    chart.value?.resize();
  });
});

// 监听deviceId变化，重新获取数据
watch(() => props.deviceId, () => {
  // 清空数据
  frequencyData.value = [];
  currentData.value = [];
  voltageData.value = [];
  powerData.value = [];
  temperatureData.value = [];
  
  // 初始化图表
  initChart();
  
  // 立即获取一次数据
  fetchDeviceData();
});

onMounted(() => {
  nextTick(() => {
    initChart();
    fetchDeviceData();
    resume();
  });
});

onBeforeUnmount(() => {
  pause();
  chart.value?.dispose();
});
</script>

<template>
  <div class="real-time-chart">
    <el-card class="chart-card" v-loading="isLoading">
      <template #header>
        <div class="card-header">
          <span>实时数据监控</span>
          <div class="header-actions">
            <el-switch
              v-model="isActive"
              @change="isActive ? resume() : pause()"
              active-text="自动更新"
            />
          </div>
        </div>
      </template>
      
      <div v-if="error" class="error-message">
        <el-alert :title="error" type="error" show-icon />
      </div>
      
      <div class="chart-container" ref="chartContainer"></div>
      
      <div v-if="latestData" class="latest-data">
        <div class="data-grid">
          <div class="data-item">
            <div class="data-label">频率</div>
            <div class="data-value">{{ latestData.frequency.toFixed(2) }} Hz</div>
          </div>
          <div class="data-item">
            <div class="data-label">电流</div>
            <div class="data-value">{{ latestData.current.toFixed(2) }} A</div>
          </div>
          <div class="data-item">
            <div class="data-label">电压</div>
            <div class="data-value">{{ latestData.voltage.toFixed(2) }} V</div>
          </div>
          <div class="data-item">
            <div class="data-label">功率</div>
            <div class="data-value">{{ latestData.power.toFixed(2) }} kW</div>
          </div>
          <div class="data-item">
            <div class="data-label">温度</div>
            <div class="data-value">{{ latestData.temperature.toFixed(2) }} °C</div>
          </div>
          <div class="data-item" v-if="latestData.rotationSpeed">
            <div class="data-label">转速</div>
            <div class="data-value">{{ latestData.rotationSpeed.toFixed(2) }} rpm</div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
.real-time-chart {
  width: 100%;
  height: 100%;
  
  .chart-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    :deep(.el-card__header) {
      padding: 12px 20px;
    }
    
    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 15px;
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-actions {
      display: flex;
      gap: 15px;
    }
  }
  
  .chart-container {
    flex: 1;
    min-height: 300px;
  }
  
  .error-message {
    margin-bottom: 15px;
  }
  
  .latest-data {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--el-border-color-light);
    
    .data-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 20px;
    }
    
    .data-item {
      .data-label {
        color: var(--el-text-color-secondary);
        font-size: 14px;
        margin-bottom: 5px;
      }
      
      .data-value {
        font-size: 18px;
        font-weight: bold;
      }
    }
  }
}
</style> 