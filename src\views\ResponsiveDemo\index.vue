<template>
  <div class="responsive-demo">
    <div class="responsive-demo__header">
      <h1>响应式组件演示</h1>
      <el-tag :type="isMobile ? 'success' : 'primary'">
        {{ isMobile ? '移动端' : '桌面端' }} - {{ currentBreakpoint }}
      </el-tag>
    </div>

    <!-- 设备信息 -->
    <el-card class="responsive-demo__card">
      <template #header>
        <h3>设备信息</h3>
      </template>
      <el-descriptions :column="isMobile ? 1 : 2" border>
        <el-descriptions-item label="屏幕尺寸">
          {{ windowWidth }} × {{ windowHeight }}
        </el-descriptions-item>
        <el-descriptions-item label="设备类型">
          {{ deviceType }}
        </el-descriptions-item>
        <el-descriptions-item label="触摸设备">
          {{ isTouchDevice ? '是' : '否' }}
        </el-descriptions-item>
        <el-descriptions-item label="网络状态">
          {{ isOnline ? '在线' : '离线' }}
        </el-descriptions-item>
        <el-descriptions-item label="连接类型">
          {{ effectiveType || '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="电池电量">
          {{ Math.round(batteryLevel * 100) }}%
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 响应式图表 -->
    <el-card class="responsive-demo__card">
      <template #header>
        <h3>响应式图表</h3>
      </template>
      <ResponsiveChart
        title="示例图表"
        :options="chartOptions"
        :height="isMobile ? 300 : 400"
        :legend="chartLegend"
        @fullscreen="handleChartFullscreen"
        @download="handleChartDownload"
      />
    </el-card>

    <!-- 响应式表格 -->
    <el-card class="responsive-demo__card">
      <template #header>
        <h3>响应式表格</h3>
      </template>
      <ResponsiveTable
        :data="tableData"
        :mobile-fields="mobileFields"
        title-field="name"
        :show-mobile-search="true"
        @card-click="handleCardClick"
      >
        <!-- 桌面端表格列 -->
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="value" label="数值" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '活跃' : '非活跃' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="日期" />
        
        <!-- 移动端操作按钮 -->
        <template #mobile-actions="{ row }">
          <el-button size="small" @click="editItem(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteItem(row)">删除</el-button>
        </template>
      </ResponsiveTable>
    </el-card>

    <!-- 响应式表单 -->
    <el-card class="responsive-demo__card">
      <template #header>
        <h3>响应式表单</h3>
      </template>
      <ResponsiveForm
        v-model="formData"
        :form-fields="formFields"
        :rules="formRules"
        @submit="handleFormSubmit"
        @reset="handleFormReset"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useResponsiveFeatures } from '@/composables/useResponsive'
import ResponsiveChart from '@/components/ResponsiveChart.vue'
import ResponsiveTable from '@/components/ResponsiveTable.vue'
import ResponsiveForm from '@/components/ResponsiveForm.vue'

// 使用响应式功能
const {
  windowWidth,
  windowHeight,
  currentBreakpoint,
  isMobile,
  isTablet,
  isDesktop,
  isTouchDevice,
  isOnline,
  effectiveType,
  batteryLevel
} = useResponsiveFeatures()

// 设备类型
const deviceType = computed(() => {
  if (isMobile.value) return '移动设备'
  if (isTablet.value) return '平板设备'
  if (isDesktop.value) return '桌面设备'
  return '未知设备'
})

// 图表配置
const chartOptions = ref({
  title: {
    text: '示例数据'
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '销售额',
      type: 'line',
      data: [120, 200, 150, 80, 70, 110]
    },
    {
      name: '利润',
      type: 'bar',
      data: [20, 40, 30, 15, 10, 25]
    }
  ]
})

const chartLegend = ref([
  { name: '销售额', color: '#5470c6', visible: true },
  { name: '利润', color: '#91cc75', visible: true }
])

// 表格数据
const tableData = ref([
  { id: 1, name: '项目A', value: 100, status: 'active', date: '2024-01-01' },
  { id: 2, name: '项目B', value: 200, status: 'inactive', date: '2024-01-02' },
  { id: 3, name: '项目C', value: 150, status: 'active', date: '2024-01-03' },
  { id: 4, name: '项目D', value: 80, status: 'active', date: '2024-01-04' },
  { id: 5, name: '项目E', value: 300, status: 'inactive', date: '2024-01-05' }
])

const mobileFields = [
  { key: 'value', label: '数值' },
  { key: 'status', label: '状态', formatter: (value: string) => value === 'active' ? '活跃' : '非活跃' },
  { key: 'date', label: '日期' }
]

// 表单数据
const formData = ref({
  name: '',
  email: '',
  age: null,
  gender: '',
  city: '',
  birthday: '',
  newsletter: false,
  description: ''
})

const formFields = [
  {
    prop: 'name',
    label: '姓名',
    type: 'input',
    placeholder: '请输入姓名',
    required: true
  },
  {
    prop: 'email',
    label: '邮箱',
    type: 'input',
    placeholder: '请输入邮箱地址',
    required: true
  },
  {
    prop: 'age',
    label: '年龄',
    type: 'number',
    placeholder: '请输入年龄',
    min: 1,
    max: 120
  },
  {
    prop: 'gender',
    label: '性别',
    type: 'radio',
    options: [
      { label: '男', value: 'male' },
      { label: '女', value: 'female' }
    ]
  },
  {
    prop: 'city',
    label: '城市',
    type: 'select',
    placeholder: '请选择城市',
    options: [
      { label: '北京', value: 'beijing' },
      { label: '上海', value: 'shanghai' },
      { label: '深圳', value: 'shenzhen' },
      { label: '广州', value: 'guangzhou' }
    ]
  },
  {
    prop: 'birthday',
    label: '生日',
    type: 'date',
    placeholder: '请选择生日'
  },
  {
    prop: 'newsletter',
    label: '订阅通讯',
    type: 'switch',
    activeText: '是',
    inactiveText: '否'
  },
  {
    prop: 'description',
    label: '描述',
    type: 'textarea',
    placeholder: '请输入描述信息',
    help: '最多200个字符'
  }
]

const formRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 事件处理
const handleChartFullscreen = () => {
  ElMessage.success('图表全屏功能')
}

const handleChartDownload = () => {
  ElMessage.success('图表下载完成')
}

const handleCardClick = (row: any) => {
  ElMessage.info(`点击了卡片: ${row.name}`)
}

const editItem = (row: any) => {
  ElMessage.info(`编辑项目: ${row.name}`)
}

const deleteItem = (row: any) => {
  ElMessage.warning(`删除项目: ${row.name}`)
}

const handleFormSubmit = (data: any) => {
  ElMessage.success('表单提交成功')
  console.log('表单数据:', data)
}

const handleFormReset = () => {
  ElMessage.info('表单已重置')
}
</script>

<style lang="scss" scoped>
@use '@/styles/mixins.scss' as *;
@use '@/styles/variables.scss' as *;

.responsive-demo {
  @include responsive-spacing(padding, $spacing-lg, $spacing-mobile-md);
  
  &__header {
    @include flex-between;
    @include responsive-spacing(margin-bottom, $spacing-xl, $spacing-mobile-lg);
    
    @include respond-to(mobile) {
      @include flex-column;
      align-items: flex-start;
      gap: $spacing-mobile-md;
    }
    
    h1 {
      @include responsive-font-size(24px, 20px);
      margin: 0;
      color: var(--el-text-color-primary);
    }
  }
  
  &__card {
    @include responsive-spacing(margin-bottom, $spacing-lg, $spacing-mobile-md);
    
    &:last-child {
      margin-bottom: 0;
    }
    
    :deep(.el-card__header) {
      @include responsive-spacing(padding, $spacing-lg, $spacing-mobile-md);
      
      h3 {
        @include responsive-font-size(18px, 16px);
        margin: 0;
        color: var(--el-text-color-primary);
      }
    }
    
    :deep(.el-card__body) {
      @include responsive-spacing(padding, $spacing-lg, $spacing-mobile-md);
    }
  }
}
</style>
