<template>
  <div class="layout-container">
    <!-- 移动端遮罩层 -->
    <div
      v-if="isMobile && !collapsed"
      class="mobile-overlay"
      @click="toggleCollapse"
    ></div>

    <!-- 侧边栏 -->
    <el-aside
      :width="sidebarWidth"
      class="sidebar"
      :class="{
        'sidebar--mobile': isMobile,
        'sidebar--collapsed': collapsed
      }"
    >
      <div class="logo">
        <el-icon size="32" color="#409EFF">
          <DataLine />
        </el-icon>
        <span v-if="!collapsed || isMobile" class="logo-text">智慧水务平台</span>
      </div>

      <el-menu
        :default-active="activeMenu"
        :collapse="collapsed && !isMobile"
        :unique-opened="true"
        router
        class="sidebar-menu"
        @select="handleMenuSelect"
      >
        <el-menu-item
          v-for="route in menuRoutes"
          :key="route.path"
          :index="route.path"
        >
          <el-icon>
            <component :is="route.meta?.icon" />
          </el-icon>
          <template #title>{{ route.meta?.title }}</template>
        </el-menu-item>
      </el-menu>
    </el-aside>

    <!-- 主内容区 -->
    <el-container class="main-container">
      <!-- 顶部导航 -->
      <el-header class="header" :class="{ 'header--mobile': isMobile }">
        <div class="header-left">
          <el-button
            link
            @click="toggleCollapse"
            class="collapse-btn touch-friendly"
          >
            <el-icon :size="isMobile ? 24 : 20">
              <Operation v-if="isMobile" />
              <Expand v-else-if="collapsed" />
              <Fold v-else />
            </el-icon>
          </el-button>

          <el-breadcrumb
            separator="/"
            class="breadcrumb"
            :class="{ 'mobile-hidden': isMobile }"
          >
            <el-breadcrumb-item>智慧水务平台</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
          </el-breadcrumb>

          <!-- 移动端页面标题 -->
          <h1 v-if="isMobile" class="mobile-title">{{ currentPageTitle }}</h1>
        </div>

        <div class="header-right">
          <el-switch
            v-model="isDark"
            @change="toggleTheme"
            inline-prompt
            :active-icon="Moon"
            :inactive-icon="Sunny"
            :active-text="isMobile ? '' : '暗色'"
            :inactive-text="isMobile ? '' : '亮色'"
            size="small"
          />

          <el-dropdown trigger="click">
            <el-button link class="user-btn touch-friendly">
              <el-icon><User /></el-icon>
              <span v-if="!isMobile">管理员</span>
              <el-icon v-if="!isMobile"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>个人中心</el-dropdown-item>
                <el-dropdown-item divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主要内容 -->
      <el-main class="main-content" :class="{ 'main-content--mobile': isMobile }">
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  DataLine,
  Lightning,
  DataAnalysis,
  MagicStick,
  Warning,
  Setting,
  CopyDocument,
  Expand,
  Fold,
  Moon,
  Sunny,
  User,
  ArrowDown,
  Operation
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const collapsed = ref(false)
const isDark = ref(false)
const isMobile = ref(false)

// 检测屏幕尺寸
const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768
  // 移动端默认折叠侧边栏
  if (isMobile.value) {
    collapsed.value = true
  }
}

// 监听窗口大小变化
const handleResize = () => {
  checkScreenSize()
}

onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 菜单路由
const menuRoutes = computed(() => {
  return router.getRoutes().find(r => r.name === 'Layout')?.children || []
})

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 当前页面标题
const currentPageTitle = computed(() => {
  return route.meta?.title as string || '首页'
})

// 侧边栏宽度
const sidebarWidth = computed(() => {
  if (isMobile.value) {
    return collapsed.value ? '0px' : '280px'
  }
  return collapsed.value ? '64px' : '240px'
})

// 切换侧边栏折叠状态
const toggleCollapse = () => {
  collapsed.value = !collapsed.value
}

// 处理菜单选择（移动端选择后自动折叠）
const handleMenuSelect = () => {
  if (isMobile.value) {
    collapsed.value = true
  }
}

// 切换主题
const toggleTheme = (dark: boolean) => {
  document.documentElement.classList.toggle('dark', dark)
}
</script>

<style lang="scss" scoped>
@use '@/styles/mixins.scss' as *;
@use '@/styles/variables.scss' as *;

.layout-container {
  height: 100vh;
  display: flex;
  position: relative;
}

// 移动端遮罩层
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: $z-index-modal-backdrop;

  @include min-width(md) {
    display: none;
  }
}

.sidebar {
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color);
  transition: all 0.3s ease;
  z-index: $z-index-modal;

  // 移动端样式
  &--mobile {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    transform: translateX(-100%);

    &:not(.sidebar--collapsed) {
      transform: translateX(0);
    }
  }

  .logo {
    height: $header-height;
    @include flex-center;
    padding: 0 $spacing-lg;
    border-bottom: 1px solid var(--el-border-color);

    @include respond-to(mobile) {
      height: $header-mobile-height;
      padding: 0 $spacing-mobile-md;
    }

    .logo-text {
      @include responsive-font-size(18px, 16px);
      font-weight: bold;
      color: var(--el-color-primary);
      margin-left: $spacing-sm;

      @include respond-to(mobile) {
        margin-left: $spacing-mobile-sm;
      }
    }
  }

  .sidebar-menu {
    border: none;
    height: calc(100vh - #{$header-height});

    @include respond-to(mobile) {
      height: calc(100vh - #{$header-mobile-height});
    }
  }
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0; // 防止flex子元素溢出

  @include respond-to(mobile) {
    width: 100%;
  }
}

.header {
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color);
  @include flex-between;
  @include responsive-spacing(padding, 0 $spacing-lg, 0 $spacing-mobile-md);
  height: $header-height;

  &--mobile {
    height: $header-mobile-height;
  }

  .header-left {
    @include flex-start;
    gap: $spacing-lg;
    flex: 1;
    min-width: 0;

    @include respond-to(mobile) {
      gap: $spacing-mobile-md;
    }

    .collapse-btn {
      padding: 0;
      @include responsive-font-size(20px, 24px);
    }

    .breadcrumb {
      @include respond-to(mobile) {
        display: none;
      }
    }

    .mobile-title {
      @include responsive-font-size(18px, 16px);
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin: 0;
      @include text-ellipsis;

      @include min-width(md) {
        display: none;
      }
    }
  }

  .header-right {
    @include flex-end;
    gap: $spacing-lg;

    @include respond-to(mobile) {
      gap: $spacing-mobile-md;
    }

    .user-btn {
      @include flex-center;
      gap: $spacing-sm;

      @include respond-to(mobile) {
        gap: $spacing-mobile-sm;
      }
    }
  }
}

.main-content {
  background: var(--el-bg-color-page);
  @include responsive-spacing(padding, $spacing-lg);
  overflow: auto;
  @include custom-scrollbar;

  &--mobile {
    padding: $spacing-mobile-md;
  }
}

// 页面切换动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s ease;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);

  @include respond-to(mobile) {
    transform: translateY(20px);
  }
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);

  @include respond-to(mobile) {
    transform: translateY(-20px);
  }
}

// 触摸友好的交互元素
.touch-friendly {
  @include touch-friendly;
}
</style>
