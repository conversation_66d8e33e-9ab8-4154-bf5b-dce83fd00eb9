#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}$1${NC}"
}

print_header() {
    echo
    echo "========================================"
    echo "   智能供水平台 - 一键部署脚本"
    echo "========================================"
    echo
}

print_step() {
    print_message "[$1] $2" $BLUE
}

print_success() {
    print_message "✅ $1" $GREEN
}

print_error() {
    print_message "❌ $1" $RED
}

print_warning() {
    print_message "⚠️ $1" $YELLOW
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 主函数
main() {
    print_header
    
    # 检查Node.js
    print_step "1/5" "检查Node.js环境..."
    if ! command_exists node; then
        print_error "未检测到Node.js"
        print_message "💡 请先安装Node.js: https://nodejs.org/" $YELLOW
        exit 1
    fi
    print_success "Node.js环境检查通过"
    
    # 检查pnpm
    echo
    print_step "2/5" "检查pnpm包管理器..."
    if ! command_exists pnpm; then
        print_warning "未检测到pnpm，正在安装..."
        npm install -g pnpm
        if [ $? -ne 0 ]; then
            print_error "pnpm安装失败"
            exit 1
        fi
    fi
    print_success "pnpm检查通过"
    
    # 安装依赖
    echo
    print_step "3/5" "安装项目依赖..."
    print_message "💡 这可能需要几分钟时间，请耐心等待..." $YELLOW
    pnpm install
    if [ $? -ne 0 ]; then
        print_error "依赖安装失败"
        exit 1
    fi
    print_success "依赖安装完成"
    
    # 构建项目
    echo
    print_step "4/5" "构建生产版本..."
    pnpm run build
    if [ $? -ne 0 ]; then
        print_error "项目构建失败"
        exit 1
    fi
    print_success "项目构建完成"
    
    # 检查Go环境并启动
    echo
    print_step "5/5" "检查Go环境并启动服务器..."
    if ! command_exists go; then
        print_error "未检测到Go环境"
        print_message "💡 请先安装Go: https://golang.org/dl/" $YELLOW
        print_message "💡 或者直接使用Python启动: python3 -m http.server 8080 --directory dist" $YELLOW
        exit 1
    fi
    
    print_success "Go环境检查通过"
    echo
    echo "========================================"
    echo "   🚀 正在启动智能供水平台服务器..."
    echo "========================================"
    echo
    
    # 启动Go服务器
    go run server.go
}

# 运行主函数
main "$@"