@echo off
chcp 65001 >nul
title 智能供水平台服务器

echo.
echo ========================================
echo    智能供水平台 - Windows启动器
echo ========================================
echo.

:: 检查是否存在可执行文件
if not exist "smart-water-server.exe" (
    echo ❌ 错误：找不到 smart-water-server.exe 文件
    echo 请确保文件在当前目录下
    pause
    exit /b 1
)

:: 检查dist目录
if not exist "dist" (
    echo ❌ 错误：找不到 dist 目录
    echo 请先运行打包命令生成 dist 目录
    pause
    exit /b 1
)

echo ✅ 正在启动智能供水平台服务器...
echo.

:: 启动服务器
smart-water-server.exe

:: 如果服务器意外退出，暂停以查看错误信息
echo.
echo 服务器已停止运行
pause