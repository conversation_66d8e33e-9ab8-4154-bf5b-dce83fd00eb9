var Ae=Object.defineProperty,Ee=Object.defineProperties;var Ie=Object.getOwnPropertyDescriptors;var $e=Object.getOwnPropertySymbols;var Oe=Object.prototype.hasOwnProperty,Ue=Object.prototype.propertyIsEnumerable;var pe=(i,t,n)=>t in i?Ae(i,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):i[t]=n,ne=(i,t)=>{for(var n in t||(t={}))Oe.call(t,n)&&pe(i,n,t[n]);if($e)for(var n of $e(t))Ue.call(t,n)&&pe(i,n,t[n]);return i},_e=(i,t)=>Ee(i,Ie(t));var le=(i,t,n)=>pe(i,typeof t!="symbol"?t+"":t,n);var H=(i,t,n)=>new Promise((u,y)=>{var p=$=>{try{D(n.next($))}catch(k){y(k)}},c=$=>{try{D(n.throw($))}catch(k){y(k)}},D=$=>$.done?u($.value):Promise.resolve($.value).then(p,c);D((n=n.apply(i,t)).next())});import{_ as X}from"./_plugin-vue_export-helper-C6jw_E77.js";/* empty css               *//* empty css                *//* empty css                  *//* empty css                        *//* empty css                  */import{_ as w,$ as Fe,d as Q,b as U,e,g as B,a1 as ie,w as a,a2 as ue,a9 as te,v as f,x as b,f as l,E as K,h as V,aM as He,o as G,n as Z,aN as Ye,aO as ye,L as T,au as se,r as Y,c as q,S as ae,aP as he,V as de,Y as ce,ac as xe,ad as De,aQ as be,Q as qe,ao as Be,ap as F,U as fe,aF as Le,aG as Ge,a4 as ke,Z as J,F as oe,k as re,as as we,af as Pe,ar as je,a7 as Xe,aR as Ce,aq as Ve,N as Qe,a8 as Ze,P as ge,aS as ve,aT as Je,aa as Ke,ab as Ne,am as et,ak as tt,al as at,ax as nt,y as Se,ay as lt,O as st,X as ot,aU as rt,aV as it}from"./index-8zz4iTME.js";import{i as ee,L as ut}from"./index-_zIlD_l3.js";/* empty css                 *//* empty css                  *//* empty css                   *//* empty css                   *//* empty css                 *//* empty css                    *//* empty css                   *//* empty css                          *//* empty css                        *//* empty css                             *//* empty css                     *//* empty css                        */class dt{constructor(){le(this,"warningHistory",[]);le(this,"deviceStatuses",[]);le(this,"warningRules",[]);this.initializeDevices(),this.initializeRules(),this.generateHistoricalWarnings()}initializeDevices(){this.deviceStatuses=[{id:"pump_001",name:"主水泵#1",type:"离心泵",status:"online",lastUpdate:w().toISOString(),parameters:{temperature:45+Math.random()*10,vibration:1.2+Math.random()*.8,pressure:3.5+Math.random()*.5,flow:800+Math.random()*200,power:85+Math.random()*15,efficiency:78+Math.random()*10},health:85+Math.random()*10,nextMaintenance:w().add(15,"day").format("YYYY-MM-DD")},{id:"pump_002",name:"备用水泵#2",type:"离心泵",status:"online",lastUpdate:w().toISOString(),parameters:{temperature:42+Math.random()*8,vibration:1+Math.random()*.6,pressure:3.2+Math.random()*.4,flow:600+Math.random()*150,power:65+Math.random()*12,efficiency:82+Math.random()*8},health:92+Math.random()*5,nextMaintenance:w().add(8,"day").format("YYYY-MM-DD")},{id:"motor_001",name:"驱动电机#1",type:"异步电机",status:"online",lastUpdate:w().toISOString(),parameters:{temperature:65+Math.random()*15,vibration:.8+Math.random()*.4,pressure:0,flow:0,power:90+Math.random()*10,efficiency:94+Math.random()*3},health:88+Math.random()*8,nextMaintenance:w().add(22,"day").format("YYYY-MM-DD")},{id:"valve_001",name:"调节阀#1",type:"电动调节阀",status:"online",lastUpdate:w().toISOString(),parameters:{temperature:25+Math.random()*5,vibration:.2+Math.random()*.1,pressure:4+Math.random()*.3,flow:850+Math.random()*100,power:2+Math.random()*1,efficiency:95+Math.random()*3},health:95+Math.random()*3,nextMaintenance:w().add(30,"day").format("YYYY-MM-DD")}]}initializeRules(){this.warningRules=[{id:"rule_001",name:"高温预警",description:"设备温度超过安全阈值",category:"temperature_abnormal",severity:"high",enabled:!0,conditions:[{parameter:"temperature",operator:"gt",value:80,duration:5}],actions:["notify","log","reduce_load"],cooldownMinutes:30,createdAt:w().subtract(30,"day").toISOString(),updatedAt:w().subtract(5,"day").toISOString()},{id:"rule_002",name:"振动异常",description:"设备振动超过正常范围",category:"vibration_high",severity:"medium",enabled:!0,conditions:[{parameter:"vibration",operator:"gt",value:2.5,duration:10}],actions:["notify","log"],cooldownMinutes:60,createdAt:w().subtract(25,"day").toISOString(),updatedAt:w().subtract(3,"day").toISOString()},{id:"rule_003",name:"效率下降",description:"设备效率低于正常水平",category:"efficiency_drop",severity:"medium",enabled:!0,conditions:[{parameter:"efficiency",operator:"lt",value:70,duration:30}],actions:["notify","log","schedule_maintenance"],cooldownMinutes:120,createdAt:w().subtract(20,"day").toISOString(),updatedAt:w().subtract(1,"day").toISOString()},{id:"rule_004",name:"维护到期提醒",description:"设备维护计划到期提醒",category:"maintenance_due",severity:"low",enabled:!0,conditions:[{parameter:"days_to_maintenance",operator:"lte",value:7}],actions:["notify","schedule_maintenance"],cooldownMinutes:1440,createdAt:w().subtract(15,"day").toISOString(),updatedAt:w().toISOString()},{id:"rule_005",name:"功率异常",description:"设备功率超出正常范围",category:"performance_anomaly",severity:"high",enabled:!0,conditions:[{parameter:"power",operator:"gt",value:120,duration:15}],actions:["notify","log","emergency_stop"],cooldownMinutes:15,createdAt:w().subtract(10,"day").toISOString(),updatedAt:w().toISOString()}]}generateHistoricalWarnings(){const t=["equipment_fault","performance_anomaly","maintenance_due","energy_consumption","efficiency_drop","vibration_high","temperature_abnormal","pressure_abnormal","flow_abnormal"],n=["critical","high","medium","low"];for(let u=30;u>=0;u--){const y=w().subtract(u,"day"),p=Math.floor(Math.random()*5)+1;for(let c=0;c<p;c++){const D=t[Math.floor(Math.random()*t.length)],$=n[Math.floor(Math.random()*n.length)],k=this.deviceStatuses[Math.floor(Math.random()*this.deviceStatuses.length)],C={id:`warning_${y.format("YYYYMMDD")}_${c+1}`,type:$==="critical"?"error":$==="low"?"info":"warning",title:this.generateWarningTitle(D,k.name),message:this.generateWarningMessage(D,k.name),timestamp:y.add(Math.random()*24,"hour").toISOString(),resolved:Math.random()>.3,category:D,severity:$,source:k.id,parameters:{deviceName:k.name,value:Math.random()*100,threshold:Math.random()*50+50},actions:[{id:`action_${Date.now()}_${Math.random()}`,name:"通知维护人员",description:"发送预警通知给相关维护人员",type:"automatic",executed:!0,executedAt:y.add(5,"minute").toISOString(),result:"通知已发送"}]};C.resolved&&(C.resolvedBy="维护人员",C.resolvedAt=y.add(Math.random()*12+1,"hour").toISOString()),this.warningHistory.push(C)}}}generateWarningTitle(t,n){return{equipment_fault:`${n} 设备故障`,performance_anomaly:`${n} 性能异常`,maintenance_due:`${n} 维护到期`,energy_consumption:`${n} 能耗异常`,efficiency_drop:`${n} 效率下降`,vibration_high:`${n} 振动过高`,temperature_abnormal:`${n} 温度异常`,pressure_abnormal:`${n} 压力异常`,flow_abnormal:`${n} 流量异常`,system_error:`${n} 系统错误`}[t]||`${n} 未知预警`}generateWarningMessage(t,n){return{equipment_fault:`检测到 ${n} 出现设备故障，请立即检查设备状态`,performance_anomaly:`${n} 运行参数超出正常范围，建议进行性能检查`,maintenance_due:`${n} 计划维护时间即将到期，请安排维护工作`,energy_consumption:`${n} 能耗水平异常，可能存在效率问题`,efficiency_drop:`${n} 运行效率显著下降，建议检查设备状态`,vibration_high:`${n} 振动水平超过安全阈值，请检查机械部件`,temperature_abnormal:`${n} 运行温度异常，请检查冷却系统`,pressure_abnormal:`${n} 压力参数超出正常范围，请检查管路系统`,flow_abnormal:`${n} 流量参数异常，可能存在堵塞或泄漏`,system_error:`${n} 系统出现错误，请检查控制系统`}[t]||`${n} 出现未知问题，请及时处理`}getCurrentWarnings(){const t=w(),n=this.warningHistory.filter(u=>w(u.timestamp).isAfter(t.subtract(24,"hour")));if(Math.random()>.7){const u=this.deviceStatuses[Math.floor(Math.random()*this.deviceStatuses.length)],y=["temperature_abnormal","vibration_high","efficiency_drop"],p=y[Math.floor(Math.random()*y.length)],c={id:`warning_${Date.now()}`,type:"warning",title:this.generateWarningTitle(p,u.name),message:this.generateWarningMessage(p,u.name),timestamp:t.toISOString(),resolved:!1,category:p,severity:"medium",source:u.id,parameters:{deviceName:u.name,value:Math.random()*100,threshold:80},actions:[]};n.unshift(c)}return n.sort((u,y)=>w(y.timestamp).valueOf()-w(u.timestamp).valueOf())}getDeviceStatuses(){return this.deviceStatuses.forEach(t=>{t.lastUpdate=w().toISOString(),t.parameters.temperature+=(Math.random()-.5)*2,t.parameters.vibration+=(Math.random()-.5)*.1,t.parameters.pressure+=(Math.random()-.5)*.1,t.parameters.flow+=(Math.random()-.5)*20,t.parameters.power+=(Math.random()-.5)*5,t.parameters.efficiency+=(Math.random()-.5)*2,t.parameters.temperature=Math.max(20,Math.min(100,t.parameters.temperature)),t.parameters.vibration=Math.max(0,Math.min(5,t.parameters.vibration)),t.parameters.pressure=Math.max(0,Math.min(10,t.parameters.pressure)),t.parameters.flow=Math.max(0,Math.min(2e3,t.parameters.flow)),t.parameters.power=Math.max(0,Math.min(150,t.parameters.power)),t.parameters.efficiency=Math.max(50,Math.min(100,t.parameters.efficiency)),t.health=Math.max(60,Math.min(100,t.health+(Math.random()-.5)*2)),t.parameters.temperature>80||t.parameters.vibration>3||t.health<70?t.status="fault":t.health<80?t.status="maintenance":t.status="online"}),this.deviceStatuses}getWarningRules(){return this.warningRules}getWarningStatistics(){const t=w(),n=t.startOf("day"),u=t.startOf("week"),y=t.startOf("month"),p=this.warningHistory.filter(S=>w(S.timestamp).isAfter(n)),c=this.warningHistory.filter(S=>w(S.timestamp).isAfter(u)),D=this.warningHistory.filter(S=>w(S.timestamp).isAfter(y)),$=this.warningHistory.filter(S=>S.resolved),k=this.warningHistory.filter(S=>!S.resolved),C={equipment_fault:0,performance_anomaly:0,maintenance_due:0,energy_consumption:0,efficiency_drop:0,vibration_high:0,temperature_abnormal:0,pressure_abnormal:0,flow_abnormal:0,system_error:0},W={critical:0,high:0,medium:0,low:0};this.warningHistory.forEach(S=>{C[S.category]++,W[S.severity]++});const E=$.filter(S=>S.resolvedAt),m=E.length>0?E.reduce((S,v)=>{const s=w(v.resolvedAt).diff(w(v.timestamp),"minute");return S+s},0)/E.length:0;return{total:this.warningHistory.length,resolved:$.length,unresolved:k.length,byCategory:C,bySeverity:W,avgResolutionTime:Math.round(m),todayCount:p.length,weekCount:c.length,monthCount:D.length}}resolveWarning(t,n){const u=this.warningHistory.find(y=>y.id===t);return u&&!u.resolved?(u.resolved=!0,u.resolvedBy=n,u.resolvedAt=w().toISOString(),!0):!1}}const N=new dt,me=Fe("warning",{state:()=>({warnings:N.getCurrentWarnings(),devices:N.getDeviceStatuses(),rules:N.getWarningRules(),statistics:N.getWarningStatistics(),filters:{category:null,severity:null,resolved:null,dateRange:null},loading:!1,realTimeEnabled:!1,updateInterval:null,selectedWarning:null,showWarningDetail:!1,showRuleConfig:!1,showDeviceMonitor:!1,dataLoaded:!1}),getters:{getFilteredWarnings:i=>{let t=[...i.warnings];if(i.filters.category&&(t=t.filter(n=>n.category===i.filters.category)),i.filters.severity&&(t=t.filter(n=>n.severity===i.filters.severity)),i.filters.resolved!==null&&(t=t.filter(n=>n.resolved===i.filters.resolved)),i.filters.dateRange){const[n,u]=i.filters.dateRange;t=t.filter(y=>{const p=new Date(y.timestamp);return p>=new Date(n)&&p<=new Date(u)})}return t.sort((n,u)=>new Date(u.timestamp).getTime()-new Date(n.timestamp).getTime())},getUnresolvedCount:i=>i.warnings.filter(t=>!t.resolved).length,getCriticalCount:i=>i.warnings.filter(t=>!t.resolved&&(t.severity==="critical"||t.severity==="high")).length,getOnlineDevicesCount:i=>i.devices.filter(t=>t.status==="online").length,getFaultDevicesCount:i=>i.devices.filter(t=>t.status==="fault").length,getMaintenanceDevicesCount:i=>i.devices.filter(t=>t.status==="maintenance").length,getTodayWarningTrend:i=>{const t=new Date;t.setHours(0,0,0,0);const n=i.warnings.filter(y=>new Date(y.timestamp)>=t);return Array.from({length:24},(y,p)=>{const c=p,D=n.filter($=>new Date($.timestamp).getHours()===c).length;return{hour:c,count:D}})},getDeviceHealthDistribution:i=>{const t={excellent:0,good:0,fair:0,poor:0};return i.devices.forEach(n=>{n.health>=90?t.excellent++:n.health>=80?t.good++:n.health>=70?t.fair++:t.poor++}),t}},actions:{initializeData(){return H(this,null,function*(){console.log("Database functionality disabled, using memory data"),this.dataLoaded=!0})},loadFromDatabase(){return H(this,null,function*(){console.log("Database loading disabled")})},seedInitialData(){return H(this,null,function*(){console.log("Database seeding disabled")})},loadMemoryData(){console.log("Using memory data")},loadWarnings(){return H(this,null,function*(){this.loading=!0;try{console.log("Loading warnings from memory")}catch(i){console.error("加载预警数据失败:",i)}finally{this.loading=!1}})},resolveWarning(i,t="操作员"){return H(this,null,function*(){const n=this.warnings.find(u=>u.id===i);return n&&!n.resolved?(n.resolved=!0,n.resolvedBy=t,n.resolvedAt=new Date().toISOString(),this.statistics=N.getWarningStatistics(),!0):!1})},batchResolveWarnings(i,t="操作员"){return H(this,null,function*(){let n=0;for(const u of i)(yield this.resolveWarning(u,t))&&n++;return n})},setFilter(i,t){this.filters[i]=t},clearFilters(){this.filters={category:null,severity:null,resolved:null,dateRange:null}},showWarningDetails(i){this.selectedWarning=i,this.showWarningDetail=!0},hideWarningDetails(){this.selectedWarning=null,this.showWarningDetail=!1},startRealTimeUpdate(){this.updateInterval||(this.realTimeEnabled=!0,this.updateInterval=setInterval(()=>{this.loadWarnings()},3e4))},stopRealTimeUpdate(){this.updateInterval&&(clearInterval(this.updateInterval),this.updateInterval=null),this.realTimeEnabled=!1},addWarningRule(i){const t=_e(ne({},i),{id:`rule_${Date.now()}`,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()});return this.rules.push(t),t},updateWarningRule(i,t){const n=this.rules.find(u=>u.id===i);return n?(Object.assign(n,t,{updatedAt:new Date().toISOString()}),!0):!1},deleteWarningRule(i){const t=this.rules.findIndex(n=>n.id===i);return t!==-1?(this.rules.splice(t,1),!0):!1},toggleWarningRule(i){const t=this.rules.find(n=>n.id===i);return t?(t.enabled=!t.enabled,t.updatedAt=new Date().toISOString(),t.enabled):!1},getDeviceDetails(i){return this.devices.find(t=>t.id===i)},updateDeviceStatus(i,t){const n=this.devices.find(u=>u.id===i);return n?(n.status=t,n.lastUpdate=new Date().toISOString(),!0):!1},exportWarningReport(i){return H(this,null,function*(){return new Promise(t=>{setTimeout(()=>{t({success:!0,filename:`warning_report_${new Date().toISOString().split("T")[0]}.${i.format}`,url:"#"})},2e3)})})},resetData(){this.warnings=[],this.devices=[],this.rules=[],this.statistics={total:0,resolved:0,unresolved:0,byCategory:{equipment_fault:0,performance_anomaly:0,maintenance_due:0,energy_consumption:0,efficiency_drop:0,vibration_high:0,temperature_abnormal:0,pressure_abnormal:0,flow_abnormal:0,system_error:0},bySeverity:{critical:0,high:0,medium:0,low:0},avgResolutionTime:0,todayCount:0,weekCount:0,monthCount:0},this.clearFilters(),this.hideWarningDetails()}}});function ct(i,t){const n={};return i.trendChart&&(n.trendChart=ee(i.trendChart),mt(n.trendChart,t.trendData)),i.categoryChart&&(n.categoryChart=ee(i.categoryChart),pt(n.categoryChart,t.statistics)),i.healthChart&&(n.healthChart=ee(i.healthChart),_t(n.healthChart,t.healthDistribution)),i.resolutionChart&&(n.resolutionChart=ee(i.resolutionChart),ft(n.resolutionChart,t.statistics)),n}function mt(i,t){const n=t.map(p=>`${p.hour}:00`),u=t.map(p=>p.count),y={title:{text:"24小时预警趋势",left:"center",textStyle:{fontSize:14}},tooltip:{trigger:"axis",formatter:function(p){const c=p[0];return`${c.name}<br/>预警数量: ${c.value}条`}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:n,axisLabel:{interval:3}},yAxis:{type:"value",name:"预警数量",nameLocation:"middle",nameGap:30},series:[{name:"预警数量",type:"line",data:u,smooth:!0,lineStyle:{color:"#E6A23C",width:3},areaStyle:{color:new ut(0,0,0,1,[{offset:0,color:"#E6A23C40"},{offset:1,color:"#E6A23C10"}])},symbol:"circle",symbolSize:6}]};i.setOption(y)}function pt(i,t){const n={equipment_fault:"设备故障",performance_anomaly:"性能异常",maintenance_due:"维护到期",energy_consumption:"能耗异常",efficiency_drop:"效率下降",vibration_high:"振动过高",temperature_abnormal:"温度异常",pressure_abnormal:"压力异常",flow_abnormal:"流量异常",system_error:"系统错误"},u=Object.entries(t.byCategory).filter(([p,c])=>c>0).map(([p,c])=>({name:n[p]||p,value:c})).sort((p,c)=>c.value-p.value),y={title:{text:"预警类别分布",left:"center",textStyle:{fontSize:14}},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",top:"middle",textStyle:{fontSize:12}},series:[{name:"预警类别",type:"pie",radius:["30%","60%"],center:["65%","50%"],data:u,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},label:{formatter:`{b}
{d}%`,fontSize:10}}]};i.setOption(y)}function _t(i,t){const n=[{name:"优秀(90-100)",value:t.excellent,itemStyle:{color:"#67C23A"}},{name:"良好(80-89)",value:t.good,itemStyle:{color:"#409EFF"}},{name:"一般(70-79)",value:t.fair,itemStyle:{color:"#E6A23C"}},{name:"较差(<70)",value:t.poor,itemStyle:{color:"#F56C6C"}}],u={title:{text:"设备健康度分布",left:"center",textStyle:{fontSize:14}},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c}台 ({d}%)"},legend:{orient:"horizontal",bottom:"5%",textStyle:{fontSize:12}},series:[{name:"设备健康度",type:"pie",radius:"60%",center:["50%","45%"],data:n,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},label:{formatter:`{b}
{c}台`,fontSize:10}}]};i.setOption(u)}function ft(i,t){const n=[{name:"已处理",value:t.resolved,itemStyle:{color:"#67C23A"}},{name:"未处理",value:t.unresolved,itemStyle:{color:"#F56C6C"}}],u={title:{text:"预警处理状态",left:"center",textStyle:{fontSize:14}},tooltip:{trigger:"item",formatter:function(p){const c=t.total,D=(p.value/c*100).toFixed(1);return`${p.name}<br/>数量: ${p.value}条<br/>占比: ${D}%`}},legend:{orient:"horizontal",bottom:"5%",textStyle:{fontSize:12}},series:[{name:"处理状态",type:"pie",radius:["40%","70%"],center:["50%","45%"],data:n,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},label:{formatter:function(p){const c=t.total,D=(p.value/c*100).toFixed(1);return`${p.name}
${p.value}条
${D}%`},fontSize:10}}]},y={graphic:{type:"text",left:"center",top:"middle",style:{text:`总计
${t.total}条`,textAlign:"center",fill:"#333",fontSize:16,fontWeight:"bold"}}};Object.assign(u,y),i.setOption(u)}const gt={class:"recent-warnings-list"},vt={class:"warning-info"},yt={class:"warning-title"},ht={class:"warning-message"},bt={class:"timestamp"},wt={key:0,class:"empty-state"},Ct=Q({__name:"RecentWarningsList",props:{warnings:{}},emits:["resolve","showDetails"],setup(i,{emit:t}){const n=t,u=C=>({critical:"danger",high:"warning",medium:"primary",low:"info"})[C]||"info",y=C=>({critical:"严重",high:"高",medium:"中",low:"低"})[C]||C,p=C=>({equipment_fault:"danger",performance_anomaly:"warning",maintenance_due:"info",energy_consumption:"primary",efficiency_drop:"warning",vibration_high:"danger",temperature_abnormal:"danger",pressure_abnormal:"warning",flow_abnormal:"warning",system_error:"danger"})[C]||"info",c=C=>({equipment_fault:"设备故障",performance_anomaly:"性能异常",maintenance_due:"维护到期",energy_consumption:"能耗异常",efficiency_drop:"效率下降",vibration_high:"振动过高",temperature_abnormal:"温度异常",pressure_abnormal:"压力异常",flow_abnormal:"流量异常",system_error:"系统错误"})[C]||C,D=C=>w(C).format("MM-DD HH:mm"),$=C=>H(this,null,function*(){try{yield se.confirm("确定要处理这个预警吗？","确认处理",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),n("resolve",C)}catch(W){}}),k=C=>{n("showDetails",C)};return(C,W)=>{const E=te,m=ue,S=K,v=Z,s=ie,h=Ye;return T(),U("div",gt,[e(s,{data:C.warnings,stripe:""},{default:a(()=>[e(m,{prop:"severity",label:"级别",width:"80"},{default:a(({row:d})=>[e(E,{type:u(d.severity),size:"small"},{default:a(()=>[f(b(y(d.severity)),1)]),_:2},1032,["type"])]),_:1}),e(m,{prop:"title",label:"预警信息","min-width":"200"},{default:a(({row:d})=>[l("div",vt,[l("div",yt,b(d.title),1),l("div",ht,b(d.message),1)])]),_:1}),e(m,{prop:"category",label:"类别",width:"100"},{default:a(({row:d})=>[e(E,{type:p(d.category),size:"small"},{default:a(()=>[f(b(c(d.category)),1)]),_:2},1032,["type"])]),_:1}),e(m,{prop:"timestamp",label:"发生时间",width:"150"},{default:a(({row:d})=>[l("div",bt,[e(S,null,{default:a(()=>[e(V(He))]),_:1}),l("span",null,b(D(d.timestamp)),1)])]),_:1}),e(m,{prop:"resolved",label:"状态",width:"80"},{default:a(({row:d})=>[e(E,{type:d.resolved?"success":"danger",size:"small"},{default:a(()=>[f(b(d.resolved?"已处理":"待处理"),1)]),_:2},1032,["type"])]),_:1}),e(m,{label:"操作",width:"120"},{default:a(({row:d})=>[d.resolved?B("",!0):(T(),G(v,{key:0,size:"small",type:"primary",onClick:r=>$(d.id)},{default:a(()=>W[0]||(W[0]=[f(" 处理 ")])),_:2,__:[0]},1032,["onClick"])),e(v,{size:"small",onClick:r=>k(d)},{default:a(()=>W[1]||(W[1]=[f(" 详情 ")])),_:2,__:[1]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),C.warnings.length===0?(T(),U("div",wt,[e(h,{description:"暂无预警信息"},{default:a(()=>[e(S,{size:"64",color:"#C0C4CC"},{default:a(()=>[e(V(ye))]),_:1}),W[2]||(W[2]=l("div",{style:{"margin-top":"16px",color:"#67C23A"}}," 系统运行正常 ",-1))]),_:1,__:[2]})])):B("",!0)])}}}),St=X(Ct,[["__scopeId","data-v-1003115c"]]),Mt={class:"warnings-list"},$t={class:"list-header"},xt={class:"header-actions"},Dt={class:"filters"},kt={class:"warning-info"},Vt={class:"warning-title"},Tt={class:"warning-message"},Wt={key:0,class:"batch-actions"},Rt=Q({__name:"WarningsList",setup(i){const t=me(),n=Y([]),u=Y({severity:null,category:null,resolved:null}),y=q(()=>{let s=[...t.warnings];return u.value.severity&&(s=s.filter(h=>h.severity===u.value.severity)),u.value.category&&(s=s.filter(h=>h.category===u.value.category)),u.value.resolved!==null&&(s=s.filter(h=>h.resolved===u.value.resolved)),s.sort((h,d)=>new Date(d.timestamp).getTime()-new Date(h.timestamp).getTime())}),p=s=>({critical:"danger",high:"warning",medium:"primary",low:"info"})[s]||"info",c=s=>({critical:"严重",high:"高",medium:"中",low:"低"})[s]||s,D=s=>({equipment_fault:"danger",performance_anomaly:"warning",maintenance_due:"info",energy_consumption:"primary",efficiency_drop:"warning",vibration_high:"danger",temperature_abnormal:"danger",pressure_abnormal:"warning",flow_abnormal:"warning",system_error:"danger"})[s]||"info",$=s=>({equipment_fault:"设备故障",performance_anomaly:"性能异常",maintenance_due:"维护到期",energy_consumption:"能耗异常",efficiency_drop:"效率下降",vibration_high:"振动过高",temperature_abnormal:"温度异常",pressure_abnormal:"压力异常",flow_abnormal:"流量异常",system_error:"系统错误"})[s]||s,k=s=>w(s).format("YYYY-MM-DD HH:mm:ss"),C=s=>{n.value=s},W=()=>{u.value={severity:null,category:null,resolved:null}},E=s=>H(this,null,function*(){try{yield se.confirm("确定要处理这个预警吗？","确认处理",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),(yield t.resolveWarning(s))?F.success("预警已处理"):F.error("处理失败")}catch(h){}}),m=()=>H(this,null,function*(){try{yield se.confirm(`确定要批量处理选中的 ${n.value.length} 条预警吗？`,"确认批量处理",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const s=n.value.map(d=>d.id),h=yield t.batchResolveWarnings(s);F.success(`成功处理 ${h} 条预警`),n.value=[]}catch(s){}}),S=s=>{t.showWarningDetails(s)},v=()=>H(this,null,function*(){try{const s=yield t.exportWarningReport({format:"excel",includeResolved:!0});F.success("报告导出成功")}catch(s){F.error("报告导出失败")}});return(s,h)=>{const d=K,r=Z,o=De,g=xe,R=ce,A=de,z=ue,M=te,I=ie,L=qe,x=ae,P=be;return T(),U("div",Mt,[e(x,null,{header:a(()=>[l("div",$t,[h[4]||(h[4]=l("span",null,"预警列表",-1)),l("div",xt,[e(r,{size:"small",onClick:v},{default:a(()=>[e(d,null,{default:a(()=>[e(V(Be))]),_:1}),h[3]||(h[3]=f(" 导出报告 "))]),_:1,__:[3]})])])]),default:a(()=>[l("div",Dt,[e(A,{gutter:16},{default:a(()=>[e(R,{span:6},{default:a(()=>[e(g,{modelValue:u.value.severity,"onUpdate:modelValue":h[0]||(h[0]=_=>u.value.severity=_),placeholder:"选择严重程度",clearable:""},{default:a(()=>[e(o,{label:"严重",value:"critical"}),e(o,{label:"高",value:"high"}),e(o,{label:"中",value:"medium"}),e(o,{label:"低",value:"low"})]),_:1},8,["modelValue"])]),_:1}),e(R,{span:6},{default:a(()=>[e(g,{modelValue:u.value.category,"onUpdate:modelValue":h[1]||(h[1]=_=>u.value.category=_),placeholder:"选择预警类别",clearable:""},{default:a(()=>[e(o,{label:"设备故障",value:"equipment_fault"}),e(o,{label:"性能异常",value:"performance_anomaly"}),e(o,{label:"维护到期",value:"maintenance_due"}),e(o,{label:"能耗异常",value:"energy_consumption"}),e(o,{label:"效率下降",value:"efficiency_drop"}),e(o,{label:"振动过高",value:"vibration_high"}),e(o,{label:"温度异常",value:"temperature_abnormal"}),e(o,{label:"压力异常",value:"pressure_abnormal"}),e(o,{label:"流量异常",value:"flow_abnormal"}),e(o,{label:"系统错误",value:"system_error"})]),_:1},8,["modelValue"])]),_:1}),e(R,{span:6},{default:a(()=>[e(g,{modelValue:u.value.resolved,"onUpdate:modelValue":h[2]||(h[2]=_=>u.value.resolved=_),placeholder:"选择处理状态",clearable:""},{default:a(()=>[e(o,{label:"已处理",value:!0}),e(o,{label:"未处理",value:!1})]),_:1},8,["modelValue"])]),_:1}),e(R,{span:6},{default:a(()=>[e(r,{onClick:W},{default:a(()=>h[5]||(h[5]=[f("清除筛选")])),_:1,__:[5]})]),_:1})]),_:1})]),he((T(),G(I,{data:y.value,stripe:"",onSelectionChange:C},{default:a(()=>[e(z,{type:"selection",width:"55"}),e(z,{prop:"severity",label:"级别",width:"80"},{default:a(({row:_})=>[e(M,{type:p(_.severity),size:"small"},{default:a(()=>[f(b(c(_.severity)),1)]),_:2},1032,["type"])]),_:1}),e(z,{prop:"title",label:"预警信息","min-width":"250"},{default:a(({row:_})=>[l("div",kt,[l("div",Vt,b(_.title),1),l("div",Tt,b(_.message),1)])]),_:1}),e(z,{prop:"category",label:"类别",width:"120"},{default:a(({row:_})=>[e(M,{type:D(_.category),size:"small"},{default:a(()=>[f(b($(_.category)),1)]),_:2},1032,["type"])]),_:1}),e(z,{prop:"source",label:"设备",width:"120"}),e(z,{prop:"timestamp",label:"发生时间",width:"150"},{default:a(({row:_})=>[f(b(k(_.timestamp)),1)]),_:1}),e(z,{prop:"resolved",label:"状态",width:"100"},{default:a(({row:_})=>[e(M,{type:_.resolved?"success":"danger",size:"small"},{default:a(()=>[f(b(_.resolved?"已处理":"待处理"),1)]),_:2},1032,["type"])]),_:1}),e(z,{label:"操作",width:"150"},{default:a(({row:_})=>[_.resolved?B("",!0):(T(),G(r,{key:0,size:"small",type:"primary",onClick:j=>E(_.id)},{default:a(()=>h[6]||(h[6]=[f(" 处理 ")])),_:2,__:[6]},1032,["onClick"])),e(r,{size:"small",onClick:j=>S(_)},{default:a(()=>h[7]||(h[7]=[f(" 详情 ")])),_:2,__:[7]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[P,V(t).loading]]),n.value.length>0?(T(),U("div",Wt,[e(L,{title:`已选择 ${n.value.length} 条预警`,type:"info",closable:!1},{default:a(()=>[e(r,{size:"small",type:"primary",onClick:m},{default:a(()=>h[8]||(h[8]=[f(" 批量处理 ")])),_:1,__:[8]})]),_:1},8,["title"])])):B("",!0)]),_:1})])}}}),zt=X(Rt,[["__scopeId","data-v-8dbdf9f6"]]),At={key:0,class:"device-detail"},Et={class:"health-display"},It={class:"card-header"},Ot={class:"param-header"},Ut={class:"param-name"},Ft={class:"param-value"},Ht={class:"param-unit"},Yt={class:"param-status"},qt={class:"card-header"},Bt={class:"maintenance-record"},Lt={class:"record-meta"},Gt={class:"dialog-footer"},Pt=Q({__name:"DeviceDetailDialog",props:{modelValue:{type:Boolean},device:{}},emits:["update:modelValue"],setup(i,{emit:t}){const n=i,u=t,y=Y(),p=Y("temperature"),c=q({get:()=>n.modelValue,set:o=>u("update:modelValue",o)}),D=q(()=>{if(!n.device)return[];const o=n.device.parameters;return[{key:"temperature",name:"温度",value:o.temperature.toFixed(1),unit:"°C",icon:"Warning",color:o.temperature>80?"#F56C6C":o.temperature>70?"#E6A23C":"#67C23A",status:o.temperature>80?"danger":o.temperature>70?"warning":"normal",statusType:o.temperature>80?"danger":o.temperature>70?"warning":"success",statusText:o.temperature>80?"过高":o.temperature>70?"偏高":"正常"},{key:"vibration",name:"振动",value:o.vibration.toFixed(2),unit:"mm/s",icon:"DataLine",color:o.vibration>2.5?"#F56C6C":o.vibration>2?"#E6A23C":"#67C23A",status:o.vibration>2.5?"danger":o.vibration>2?"warning":"normal",statusType:o.vibration>2.5?"danger":o.vibration>2?"warning":"success",statusText:o.vibration>2.5?"异常":o.vibration>2?"偏高":"正常"},{key:"efficiency",name:"效率",value:o.efficiency.toFixed(1),unit:"%",icon:"DataLine",color:o.efficiency<70?"#F56C6C":o.efficiency<80?"#E6A23C":"#67C23A",status:o.efficiency<70?"danger":o.efficiency<80?"warning":"normal",statusType:o.efficiency<70?"danger":o.efficiency<80?"warning":"success",statusText:o.efficiency<70?"偏低":o.efficiency<80?"一般":"良好"},{key:"power",name:"功率",value:o.power.toFixed(1),unit:"kW",icon:"Lightning",color:"#409EFF",status:"normal",statusType:"primary",statusText:"正常"},{key:"pressure",name:"压力",value:o.pressure.toFixed(2),unit:"MPa",icon:"DataLine",color:"#909399",status:"normal",statusType:"info",statusText:"正常"},{key:"flow",name:"流量",value:o.flow.toFixed(1),unit:"m³/h",icon:"DataLine",color:"#67C23A",status:"normal",statusType:"success",statusText:"正常"}]}),$=[{id:1,date:"2024-01-15 14:30",title:"定期保养",description:"更换润滑油，检查轴承状态，清洁设备外观",technician:"张师傅",duration:"2小时",type:"primary"},{id:2,date:"2024-01-08 09:15",title:"故障维修",description:"更换损坏的密封圈，调整叶轮间隙",technician:"李师傅",duration:"4小时",type:"warning"},{id:3,date:"2024-01-01 16:45",title:"年度大修",description:"全面检修设备，更换易损件，性能测试",technician:"王师傅",duration:"8小时",type:"success"}];let k=null;const C=o=>({online:"success",fault:"danger",maintenance:"warning",offline:"info"})[o]||"info",W=o=>({online:"在线",fault:"故障",maintenance:"维护中",offline:"离线"})[o]||o,E=o=>o>=90?"#67C23A":o>=80?"#409EFF":o>=70?"#E6A23C":"#F56C6C",m=o=>{const g=w(o).diff(w(),"day");return g<=3?"maintenance-urgent":g<=7?"maintenance-warning":"maintenance-normal"},S=o=>w(o).format("YYYY-MM-DD"),v=()=>{F.success("参数已刷新")},s=()=>{F.success("维护计划已安排")},h=()=>{c.value=!1},d=()=>{y.value&&(k=ee(y.value),r())},r=()=>{if(!k||!n.device)return;const o=[],g=w();for(let A=23;A>=0;A--){const z=g.subtract(A,"hour");let M=0;switch(p.value){case"temperature":M=45+Math.random()*10;break;case"vibration":M=1.2+Math.random()*.8;break;case"efficiency":M=75+Math.random()*15;break;case"power":M=80+Math.random()*20;break}o.push([z.format("HH:mm"),M])}const R={title:{text:`${p.value==="temperature"?"温度":p.value==="vibration"?"振动":p.value==="efficiency"?"效率":"功率"}趋势`,left:"center"},tooltip:{trigger:"axis"},xAxis:{type:"category",data:o.map(A=>A[0])},yAxis:{type:"value"},series:[{data:o.map(A=>A[1]),type:"line",smooth:!0,areaStyle:{}}]};k.setOption(R)};return fe(p,r),fe(c,o=>{o&&ge(()=>{d()})}),(o,g)=>{var Me;const R=Ge,A=te,z=ke,M=Le,I=ae,L=K,x=Z,P=ce,_=de,j=je,Te=Pe,We=Ze,Re=Xe,ze=Ve;return T(),G(ze,{modelValue:c.value,"onUpdate:modelValue":g[1]||(g[1]=O=>c.value=O),title:((Me=o.device)==null?void 0:Me.name)||"设备详情",width:"800px","before-close":h},{footer:a(()=>[l("div",Gt,[e(x,{onClick:h},{default:a(()=>g[11]||(g[11]=[f("关闭")])),_:1,__:[11]}),e(x,{type:"primary",onClick:s},{default:a(()=>[e(L,null,{default:a(()=>[e(V(Ce))]),_:1}),g[12]||(g[12]=f(" 安排维护 "))]),_:1,__:[12]})])]),default:a(()=>[o.device?(T(),U("div",At,[e(I,{class:"info-card"},{header:a(()=>g[2]||(g[2]=[l("span",null,"基本信息",-1)])),default:a(()=>[e(M,{column:2,border:""},{default:a(()=>[e(R,{label:"设备名称"},{default:a(()=>[f(b(o.device.name),1)]),_:1}),e(R,{label:"设备类型"},{default:a(()=>[f(b(o.device.type),1)]),_:1}),e(R,{label:"设备ID"},{default:a(()=>[f(b(o.device.id),1)]),_:1}),e(R,{label:"运行状态"},{default:a(()=>[e(A,{type:C(o.device.status)},{default:a(()=>[f(b(W(o.device.status)),1)]),_:1},8,["type"])]),_:1}),e(R,{label:"健康度"},{default:a(()=>[l("div",Et,[e(z,{percentage:o.device.health,color:E(o.device.health),"stroke-width":12},null,8,["percentage","color"])])]),_:1}),e(R,{label:"下次维护"},{default:a(()=>[l("span",{class:J(m(o.device.nextMaintenance))},b(S(o.device.nextMaintenance)),3)]),_:1})]),_:1})]),_:1}),e(I,{class:"params-card"},{header:a(()=>[l("div",It,[g[4]||(g[4]=l("span",null,"实时参数",-1)),e(x,{size:"small",onClick:v},{default:a(()=>[e(L,null,{default:a(()=>[e(V(we))]),_:1}),g[3]||(g[3]=f(" 刷新 "))]),_:1,__:[3]})])]),default:a(()=>[e(_,{gutter:20},{default:a(()=>[(T(!0),U(oe,null,re(D.value,O=>(T(),G(P,{span:8,key:O.key},{default:a(()=>[l("div",{class:J(["param-card",O.status])},[l("div",Ot,[e(L,{size:20,color:O.color},{default:a(()=>[(T(),G(Qe(O.icon)))]),_:2},1032,["color"]),l("span",Ut,b(O.name),1)]),l("div",Ft,[f(b(O.value)+" ",1),l("span",Ht,b(O.unit),1)]),l("div",Yt,[e(A,{type:O.statusType,size:"small"},{default:a(()=>[f(b(O.statusText),1)]),_:2},1032,["type"])])],2)]),_:2},1024))),128))]),_:1})]),_:1}),e(I,{class:"chart-card"},{header:a(()=>[l("div",qt,[g[9]||(g[9]=l("span",null,"参数趋势",-1)),e(Te,{modelValue:p.value,"onUpdate:modelValue":g[0]||(g[0]=O=>p.value=O),size:"small"},{default:a(()=>[e(j,{value:"temperature"},{default:a(()=>g[5]||(g[5]=[f("温度")])),_:1,__:[5]}),e(j,{value:"vibration"},{default:a(()=>g[6]||(g[6]=[f("振动")])),_:1,__:[6]}),e(j,{value:"efficiency"},{default:a(()=>g[7]||(g[7]=[f("效率")])),_:1,__:[7]}),e(j,{value:"power"},{default:a(()=>g[8]||(g[8]=[f("功率")])),_:1,__:[8]})]),_:1},8,["modelValue"])])]),default:a(()=>[l("div",{ref_key:"chartRef",ref:y,class:"trend-chart"},null,512)]),_:1}),e(I,{class:"maintenance-card"},{header:a(()=>g[10]||(g[10]=[l("span",null,"维护记录",-1)])),default:a(()=>[e(Re,null,{default:a(()=>[(T(),U(oe,null,re($,O=>e(We,{key:O.id,timestamp:O.date,type:O.type},{default:a(()=>[l("div",Bt,[l("h4",null,b(O.title),1),l("p",null,b(O.description),1),l("div",Lt,[l("span",null,"维护人员: "+b(O.technician),1),l("span",null,"耗时: "+b(O.duration),1)])])]),_:2},1032,["timestamp","type"])),64))]),_:1})]),_:1})])):B("",!0)]),_:1},8,["modelValue","title"])}}}),jt=X(Pt,[["__scopeId","data-v-c4f57a1a"]]),Xt={class:"device-monitor"},Qt={class:"monitor-header"},Zt={class:"header-actions"},Jt={class:"device-overview"},Kt={class:"status-card online"},Nt={class:"status-icon"},ea={class:"status-info"},ta={class:"status-count"},aa={class:"status-card fault"},na={class:"status-icon"},la={class:"status-info"},sa={class:"status-count"},oa={class:"status-card maintenance"},ra={class:"status-icon"},ia={class:"status-info"},ua={class:"status-count"},da={class:"status-card offline"},ca={class:"status-icon"},ma={class:"status-info"},pa={class:"status-count"},_a={class:"device-list"},fa={class:"health-indicator"},ga={class:"health-text"},va={class:"parameters"},ya={class:"param-item"},ha={class:"param-item"},ba={class:"param-item"},wa={class:"maintenance-info"},Ca=Q({__name:"DeviceMonitor",setup(i){const t=me(),n=Y(!1),u=Y(null),y=q(()=>t.devices),p=q(()=>y.value.filter(d=>d.status==="online").length),c=q(()=>y.value.filter(d=>d.status==="fault").length),D=q(()=>y.value.filter(d=>d.status==="maintenance").length),$=q(()=>y.value.filter(d=>d.status==="offline").length),k=()=>{t.loadWarnings()},C=d=>({online:"success",fault:"danger",maintenance:"warning",offline:"info"})[d]||"info",W=d=>({online:"在线",fault:"故障",maintenance:"维护中",offline:"离线"})[d]||d,E=d=>d>=90?"#67C23A":d>=80?"#409EFF":d>=70?"#E6A23C":"#F56C6C",m=(d,r)=>{switch(d){case"temperature":return r>80?"param-danger":r>70?"param-warning":"param-normal";case"vibration":return r>2.5?"param-danger":r>2?"param-warning":"param-normal";case"efficiency":return r<70?"param-danger":r<80?"param-warning":"param-normal";default:return"param-normal"}},S=d=>{const r=w(d).diff(w(),"day");return r<=3?"maintenance-urgent":r<=7?"maintenance-warning":"maintenance-normal"},v=d=>{const r=w(d).diff(w(),"day");return r<0?"已过期":r===0?"今天":r===1?"明天":`${r}天后`},s=d=>w(d).format("MM-DD HH:mm"),h=d=>{u.value=d,n.value=!0};return(d,r)=>{const o=K,g=Z,R=ce,A=de,z=ue,M=te,I=ke,L=ie,x=ae,P=be;return T(),U("div",Xt,[e(x,null,{header:a(()=>[l("div",Qt,[r[2]||(r[2]=l("span",null,"设备监控",-1)),l("div",Zt,[e(g,{size:"small",onClick:k},{default:a(()=>[e(o,null,{default:a(()=>[e(V(we))]),_:1}),r[1]||(r[1]=f(" 刷新 "))]),_:1,__:[1]})])])]),default:a(()=>[l("div",Jt,[e(A,{gutter:20},{default:a(()=>[e(R,{span:6},{default:a(()=>[l("div",Kt,[l("div",Nt,[e(o,{size:"24"},{default:a(()=>[e(V(ye))]),_:1})]),l("div",ea,[l("div",ta,b(p.value),1),r[3]||(r[3]=l("div",{class:"status-label"},"在线设备",-1))])])]),_:1}),e(R,{span:6},{default:a(()=>[l("div",aa,[l("div",na,[e(o,{size:"24"},{default:a(()=>[e(V(ve))]),_:1})]),l("div",la,[l("div",sa,b(c.value),1),r[4]||(r[4]=l("div",{class:"status-label"},"故障设备",-1))])])]),_:1}),e(R,{span:6},{default:a(()=>[l("div",oa,[l("div",ra,[e(o,{size:"24"},{default:a(()=>[e(V(Ce))]),_:1})]),l("div",ia,[l("div",ua,b(D.value),1),r[5]||(r[5]=l("div",{class:"status-label"},"维护中",-1))])])]),_:1}),e(R,{span:6},{default:a(()=>[l("div",da,[l("div",ca,[e(o,{size:"24"},{default:a(()=>[e(V(Je))]),_:1})]),l("div",ma,[l("div",pa,b($.value),1),r[6]||(r[6]=l("div",{class:"status-label"},"离线设备",-1))])])]),_:1})]),_:1})]),l("div",_a,[he((T(),G(L,{data:y.value,stripe:""},{default:a(()=>[e(z,{prop:"name",label:"设备名称",width:"150"}),e(z,{prop:"type",label:"设备类型",width:"120"}),e(z,{prop:"status",label:"运行状态",width:"100"},{default:a(({row:_})=>[e(M,{type:C(_.status),size:"small"},{default:a(()=>[f(b(W(_.status)),1)]),_:2},1032,["type"])]),_:1}),e(z,{prop:"health",label:"健康度",width:"120"},{default:a(({row:_})=>[l("div",fa,[e(I,{percentage:_.health,color:E(_.health),"stroke-width":8,"show-text":!1},null,8,["percentage","color"]),l("span",ga,b(_.health.toFixed(0))+"%",1)])]),_:1}),e(z,{label:"关键参数"},{default:a(({row:_})=>[l("div",va,[l("div",ya,[r[7]||(r[7]=l("span",{class:"param-label"},"温度:",-1)),l("span",{class:J(["param-value",m("temperature",_.parameters.temperature)])},b(_.parameters.temperature.toFixed(1))+"°C ",3)]),l("div",ha,[r[8]||(r[8]=l("span",{class:"param-label"},"振动:",-1)),l("span",{class:J(["param-value",m("vibration",_.parameters.vibration)])},b(_.parameters.vibration.toFixed(2))+"mm/s ",3)]),l("div",ba,[r[9]||(r[9]=l("span",{class:"param-label"},"效率:",-1)),l("span",{class:J(["param-value",m("efficiency",_.parameters.efficiency)])},b(_.parameters.efficiency.toFixed(1))+"% ",3)])])]),_:1}),e(z,{prop:"nextMaintenance",label:"下次维护",width:"120"},{default:a(({row:_})=>[l("div",wa,[l("span",{class:J(S(_.nextMaintenance))},b(v(_.nextMaintenance)),3)])]),_:1}),e(z,{prop:"lastUpdate",label:"最后更新",width:"150"},{default:a(({row:_})=>[f(b(s(_.lastUpdate)),1)]),_:1}),e(z,{label:"操作",width:"120"},{default:a(({row:_})=>[e(g,{size:"small",onClick:j=>h(_)},{default:a(()=>r[10]||(r[10]=[f(" 详情 ")])),_:2,__:[10]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[P,V(t).loading]])])]),_:1}),e(jt,{modelValue:n.value,"onUpdate:modelValue":r[0]||(r[0]=_=>n.value=_),device:u.value},null,8,["modelValue","device"])])}}}),Sa=X(Ca,[["__scopeId","data-v-a57bba6d"]]),Ma={class:"conditions-editor"},$a={class:"dialog-footer"},xa=Q({__name:"RuleEditDialog",props:{modelValue:{type:Boolean},rule:{}},emits:["update:modelValue","save"],setup(i,{emit:t}){const n=i,u=t,y=Y(),p=Y(!1),c=Y({id:"",name:"",description:"",category:"equipment_fault",severity:"medium",enabled:!0,conditions:[{parameter:"temperature",operator:"gt",value:80,duration:5}],actions:["notify","log"],cooldownMinutes:30,createdAt:"",updatedAt:""}),D={name:[{required:!0,message:"请输入规则名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],description:[{required:!0,message:"请输入规则描述",trigger:"blur"}],category:[{required:!0,message:"请选择预警类别",trigger:"change"}],severity:[{required:!0,message:"请选择严重程度",trigger:"change"}],cooldownMinutes:[{required:!0,message:"请输入冷却时间",trigger:"blur"},{type:"number",min:1,max:1440,message:"冷却时间应在 1-1440 分钟之间",trigger:"blur"}]},$=q({get:()=>n.modelValue,set:v=>u("update:modelValue",v)}),k=q(()=>!!n.rule),C=()=>{c.value.conditions.push({parameter:"temperature",operator:"gt",value:80,duration:5})},W=v=>{c.value.conditions.length>1&&c.value.conditions.splice(v,1)},E=()=>H(this,null,function*(){var s;if(yield(s=y.value)==null?void 0:s.validate().catch(()=>!1)){p.value=!0;try{const h=_e(ne({},c.value),{id:c.value.id||`rule_${Date.now()}`,createdAt:c.value.createdAt||new Date().toISOString(),updatedAt:new Date().toISOString()});u("save",h)}finally{p.value=!1}}}),m=()=>{$.value=!1,S()},S=()=>{c.value={id:"",name:"",description:"",category:"equipment_fault",severity:"medium",enabled:!0,conditions:[{parameter:"temperature",operator:"gt",value:80,duration:5}],actions:["notify","log"],cooldownMinutes:30,createdAt:"",updatedAt:""}};return fe(()=>n.rule,v=>{v?c.value=ne({},v):S()},{immediate:!0}),(v,s)=>{const h=et,d=Ne,r=De,o=xe,g=nt,R=Z,A=at,z=tt,M=Se,I=Ke,L=Ve;return T(),G(L,{modelValue:$.value,"onUpdate:modelValue":s[7]||(s[7]=x=>$.value=x),title:k.value?"编辑预警规则":"添加预警规则",width:"600px","before-close":m},{footer:a(()=>[l("div",$a,[e(R,{onClick:m},{default:a(()=>s[18]||(s[18]=[f("取消")])),_:1,__:[18]}),e(R,{type:"primary",onClick:E,loading:p.value},{default:a(()=>[f(b(k.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:a(()=>[e(I,{model:c.value,rules:D,ref_key:"formRef",ref:y,"label-width":"120px"},{default:a(()=>[e(d,{label:"规则名称",prop:"name"},{default:a(()=>[e(h,{modelValue:c.value.name,"onUpdate:modelValue":s[0]||(s[0]=x=>c.value.name=x),placeholder:"请输入规则名称"},null,8,["modelValue"])]),_:1}),e(d,{label:"描述",prop:"description"},{default:a(()=>[e(h,{modelValue:c.value.description,"onUpdate:modelValue":s[1]||(s[1]=x=>c.value.description=x),type:"textarea",rows:2,placeholder:"请输入规则描述"},null,8,["modelValue"])]),_:1}),e(d,{label:"预警类别",prop:"category"},{default:a(()=>[e(o,{modelValue:c.value.category,"onUpdate:modelValue":s[2]||(s[2]=x=>c.value.category=x),placeholder:"选择预警类别"},{default:a(()=>[e(r,{label:"设备故障",value:"equipment_fault"}),e(r,{label:"性能异常",value:"performance_anomaly"}),e(r,{label:"维护到期",value:"maintenance_due"}),e(r,{label:"能耗异常",value:"energy_consumption"}),e(r,{label:"效率下降",value:"efficiency_drop"}),e(r,{label:"振动过高",value:"vibration_high"}),e(r,{label:"温度异常",value:"temperature_abnormal"}),e(r,{label:"压力异常",value:"pressure_abnormal"}),e(r,{label:"流量异常",value:"flow_abnormal"}),e(r,{label:"系统错误",value:"system_error"})]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"严重程度",prop:"severity"},{default:a(()=>[e(o,{modelValue:c.value.severity,"onUpdate:modelValue":s[3]||(s[3]=x=>c.value.severity=x),placeholder:"选择严重程度"},{default:a(()=>[e(r,{label:"严重",value:"critical"}),e(r,{label:"高",value:"high"}),e(r,{label:"中",value:"medium"}),e(r,{label:"低",value:"low"})]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"触发条件"},{default:a(()=>[l("div",Ma,[(T(!0),U(oe,null,re(c.value.conditions,(x,P)=>(T(),U("div",{key:P,class:"condition-row"},[e(o,{modelValue:x.parameter,"onUpdate:modelValue":_=>x.parameter=_,placeholder:"参数"},{default:a(()=>[e(r,{label:"温度",value:"temperature"}),e(r,{label:"振动",value:"vibration"}),e(r,{label:"压力",value:"pressure"}),e(r,{label:"流量",value:"flow"}),e(r,{label:"功率",value:"power"}),e(r,{label:"效率",value:"efficiency"})]),_:2},1032,["modelValue","onUpdate:modelValue"]),e(o,{modelValue:x.operator,"onUpdate:modelValue":_=>x.operator=_,placeholder:"操作符"},{default:a(()=>[e(r,{label:"大于",value:"gt"}),e(r,{label:"小于",value:"lt"}),e(r,{label:"等于",value:"eq"}),e(r,{label:"大于等于",value:"gte"}),e(r,{label:"小于等于",value:"lte"}),e(r,{label:"介于",value:"between"})]),_:2},1032,["modelValue","onUpdate:modelValue"]),e(g,{modelValue:x.value,"onUpdate:modelValue":_=>x.value=_,placeholder:"阈值",precision:2},null,8,["modelValue","onUpdate:modelValue"]),e(g,{modelValue:x.duration,"onUpdate:modelValue":_=>x.duration=_,placeholder:"持续时间(分钟)",min:1},null,8,["modelValue","onUpdate:modelValue"]),e(R,{type:"danger",size:"small",onClick:_=>W(P),disabled:c.value.conditions.length<=1},{default:a(()=>s[8]||(s[8]=[f(" 删除 ")])),_:2,__:[8]},1032,["onClick","disabled"])]))),128)),e(R,{type:"primary",size:"small",onClick:C},{default:a(()=>s[9]||(s[9]=[f(" 添加条件 ")])),_:1,__:[9]})])]),_:1}),e(d,{label:"响应动作"},{default:a(()=>[e(z,{modelValue:c.value.actions,"onUpdate:modelValue":s[4]||(s[4]=x=>c.value.actions=x)},{default:a(()=>[e(A,{label:"notify"},{default:a(()=>s[10]||(s[10]=[f("发送通知")])),_:1,__:[10]}),e(A,{label:"log"},{default:a(()=>s[11]||(s[11]=[f("记录日志")])),_:1,__:[11]}),e(A,{label:"email"},{default:a(()=>s[12]||(s[12]=[f("邮件告警")])),_:1,__:[12]}),e(A,{label:"sms"},{default:a(()=>s[13]||(s[13]=[f("短信告警")])),_:1,__:[13]}),e(A,{label:"reduce_load"},{default:a(()=>s[14]||(s[14]=[f("降低负荷")])),_:1,__:[14]}),e(A,{label:"emergency_stop"},{default:a(()=>s[15]||(s[15]=[f("紧急停机")])),_:1,__:[15]}),e(A,{label:"schedule_maintenance"},{default:a(()=>s[16]||(s[16]=[f("安排维护")])),_:1,__:[16]})]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"冷却时间",prop:"cooldownMinutes"},{default:a(()=>[e(g,{modelValue:c.value.cooldownMinutes,"onUpdate:modelValue":s[5]||(s[5]=x=>c.value.cooldownMinutes=x),min:1,max:1440,placeholder:"分钟"},null,8,["modelValue"]),s[17]||(s[17]=l("span",{class:"form-tip"},"防止重复告警的时间间隔",-1))]),_:1,__:[17]}),e(d,{label:"启用状态"},{default:a(()=>[e(M,{modelValue:c.value.enabled,"onUpdate:modelValue":s[6]||(s[6]=x=>c.value.enabled=x)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}}}),Da=X(xa,[["__scopeId","data-v-3a0132b0"]]),ka={class:"rules-config"},Va={class:"rules-header"},Ta={class:"header-actions"},Wa={class:"conditions"},Ra={key:0},za=Q({__name:"RulesConfig",setup(i){const t=me(),n=Y(!1),u=Y(null),y=q(()=>t.rules),p=v=>({equipment_fault:"danger",performance_anomaly:"warning",maintenance_due:"info",energy_consumption:"primary",efficiency_drop:"warning",vibration_high:"danger",temperature_abnormal:"danger",pressure_abnormal:"warning",flow_abnormal:"warning",system_error:"danger"})[v]||"info",c=v=>({equipment_fault:"设备故障",performance_anomaly:"性能异常",maintenance_due:"维护到期",energy_consumption:"能耗异常",efficiency_drop:"效率下降",vibration_high:"振动过高",temperature_abnormal:"温度异常",pressure_abnormal:"压力异常",flow_abnormal:"流量异常",system_error:"系统错误"})[v]||v,D=v=>({critical:"danger",high:"warning",medium:"primary",low:"info"})[v]||"info",$=v=>({critical:"严重",high:"高",medium:"中",low:"低"})[v]||v,k=v=>({gt:">",lt:"<",eq:"=",gte:">=",lte:"<=",between:"介于"})[v]||v,C=()=>{u.value=null,n.value=!0},W=v=>{u.value=v,n.value=!0},E=v=>{u.value?(t.updateWarningRule(v.id,v),F.success("规则更新成功")):(t.addWarningRule(v),F.success("规则添加成功")),n.value=!1},m=v=>{const s=t.toggleWarningRule(v);F.success(`规则已${s?"启用":"禁用"}`)},S=v=>H(this,null,function*(){try{yield se.confirm("确定要删除这个预警规则吗？删除后无法恢复。","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),t.deleteWarningRule(v)?F.success("规则删除成功"):F.error("规则删除失败")}catch(s){}});return(v,s)=>{const h=K,d=Z,r=ue,o=te,g=Se,R=ie,A=ae,z=be;return T(),U("div",ka,[e(A,null,{header:a(()=>[l("div",Va,[s[2]||(s[2]=l("span",null,"预警规则配置",-1)),l("div",Ta,[e(d,{size:"small",type:"primary",onClick:C},{default:a(()=>[e(h,null,{default:a(()=>[e(V(lt))]),_:1}),s[1]||(s[1]=f(" 添加规则 "))]),_:1,__:[1]})])])]),default:a(()=>[he((T(),G(R,{data:y.value,stripe:""},{default:a(()=>[e(r,{prop:"name",label:"规则名称",width:"150"}),e(r,{prop:"description",label:"描述","min-width":"200"}),e(r,{prop:"category",label:"类别",width:"120"},{default:a(({row:M})=>[e(o,{type:p(M.category),size:"small"},{default:a(()=>[f(b(c(M.category)),1)]),_:2},1032,["type"])]),_:1}),e(r,{prop:"severity",label:"严重程度",width:"100"},{default:a(({row:M})=>[e(o,{type:D(M.severity),size:"small"},{default:a(()=>[f(b($(M.severity)),1)]),_:2},1032,["type"])]),_:1}),e(r,{prop:"enabled",label:"状态",width:"80"},{default:a(({row:M})=>[e(g,{modelValue:M.enabled,"onUpdate:modelValue":I=>M.enabled=I,onChange:I=>m(M.id)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),e(r,{prop:"conditions",label:"触发条件","min-width":"200"},{default:a(({row:M})=>[l("div",Wa,[(T(!0),U(oe,null,re(M.conditions,I=>(T(),U("div",{key:I.parameter,class:"condition-item"},[f(b(I.parameter)+" "+b(k(I.operator))+" "+b(I.value)+" ",1),I.duration?(T(),U("span",Ra,"(持续"+b(I.duration)+"分钟)",1)):B("",!0)]))),128))])]),_:1}),e(r,{prop:"cooldownMinutes",label:"冷却时间",width:"100"},{default:a(({row:M})=>[f(b(M.cooldownMinutes)+"分钟 ",1)]),_:1}),e(r,{label:"操作",width:"150"},{default:a(({row:M})=>[e(d,{size:"small",onClick:I=>W(M)},{default:a(()=>s[3]||(s[3]=[f(" 编辑 ")])),_:2,__:[3]},1032,["onClick"]),e(d,{size:"small",type:"danger",onClick:I=>S(M.id)},{default:a(()=>s[4]||(s[4]=[f(" 删除 ")])),_:2,__:[4]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[z,V(t).loading]])]),_:1}),e(Da,{modelValue:n.value,"onUpdate:modelValue":s[0]||(s[0]=M=>n.value=M),rule:u.value,onSave:E},null,8,["modelValue","rule"])])}}}),Aa=X(za,[["__scopeId","data-v-8d6d344c"]]),Ea={class:"warning-system"},Ia={class:"card-header"},Oa={class:"header-actions"},Ua={key:0,class:"overview-section"},Fa={class:"kpi-cards"},Ha={class:"kpi-content"},Ya={class:"kpi-icon"},qa={class:"kpi-data"},Ba={class:"kpi-value"},La={class:"kpi-content"},Ga={class:"kpi-icon"},Pa={class:"kpi-data"},ja={class:"kpi-value"},Xa={class:"kpi-content"},Qa={class:"kpi-icon"},Za={class:"kpi-data"},Ja={class:"kpi-value"},Ka={class:"kpi-content"},Na={class:"kpi-icon"},en={class:"kpi-data"},tn={class:"kpi-value"},an={class:"section-header"},nn={key:1,class:"warnings-section"},ln={key:2,class:"devices-section"},sn={key:3,class:"rules-section"},on=Q({__name:"index",setup(i){const t=me(),n=Y("overview"),u=Y(),y=Y(),p=Y(),c=Y(),D=q(()=>t.getFilteredWarnings.slice(0,10)),$=E=>{E?(t.startRealTimeUpdate(),F.success("已开启实时监控")):(t.stopRealTimeUpdate(),F.info("已关闭实时监控"))},k=()=>{t.loadWarnings(),F.success("数据已刷新")},C=E=>H(this,null,function*(){(yield t.resolveWarning(E))?(F.success("预警已处理"),W()):F.error("处理失败")}),W=()=>{t.statistics&&ge(()=>{ct({trendChart:u.value,categoryChart:y.value,healthChart:p.value,resolutionChart:c.value},{statistics:t.statistics,trendData:t.getTodayWarningTrend,healthDistribution:t.getDeviceHealthDistribution})})};return st(()=>H(this,null,function*(){yield t.loadWarnings(),yield ge(),W()})),(E,m)=>{const S=K,v=Z,s=ot,h=Se,d=ae,r=ce,o=de;return T(),U("div",Ea,[e(d,{class:"control-panel"},{header:a(()=>[l("div",Ia,[e(S,null,{default:a(()=>[e(V(ve))]),_:1}),m[11]||(m[11]=l("span",null,"故障预警系统",-1)),l("div",Oa,[e(s,{size:"small"},{default:a(()=>[e(v,{type:n.value==="overview"?"primary":"",onClick:m[0]||(m[0]=g=>n.value="overview")},{default:a(()=>m[6]||(m[6]=[f(" 总览 ")])),_:1,__:[6]},8,["type"]),e(v,{type:n.value==="warnings"?"primary":"",onClick:m[1]||(m[1]=g=>n.value="warnings")},{default:a(()=>m[7]||(m[7]=[f(" 预警列表 ")])),_:1,__:[7]},8,["type"]),e(v,{type:n.value==="devices"?"primary":"",onClick:m[2]||(m[2]=g=>n.value="devices")},{default:a(()=>m[8]||(m[8]=[f(" 设备监控 ")])),_:1,__:[8]},8,["type"]),e(v,{type:n.value==="rules"?"primary":"",onClick:m[3]||(m[3]=g=>n.value="rules")},{default:a(()=>m[9]||(m[9]=[f(" 规则配置 ")])),_:1,__:[9]},8,["type"])]),_:1}),e(h,{modelValue:V(t).realTimeEnabled,"onUpdate:modelValue":m[4]||(m[4]=g=>V(t).realTimeEnabled=g),onChange:$,"inline-prompt":"","active-text":"实时","inactive-text":"静态",size:"small"},null,8,["modelValue"]),e(v,{size:"small",onClick:k,loading:V(t).loading},{default:a(()=>[e(S,null,{default:a(()=>[e(V(we))]),_:1}),m[10]||(m[10]=f(" 刷新 "))]),_:1,__:[10]},8,["loading"])])])]),_:1}),n.value==="overview"?(T(),U("div",Ua,[l("div",Fa,[e(o,{gutter:20},{default:a(()=>[e(r,{span:6},{default:a(()=>[e(d,{class:"kpi-card critical"},{default:a(()=>[l("div",Ha,[l("div",Ya,[e(S,{size:32,color:"#F56C6C"},{default:a(()=>[e(V(ve))]),_:1})]),l("div",qa,[l("div",Ba,b(V(t).getCriticalCount),1),m[12]||(m[12]=l("div",{class:"kpi-label"},"严重预警",-1))])])]),_:1})]),_:1}),e(r,{span:6},{default:a(()=>[e(d,{class:"kpi-card warning"},{default:a(()=>[l("div",La,[l("div",Ga,[e(S,{size:32,color:"#E6A23C"},{default:a(()=>[e(V(rt))]),_:1})]),l("div",Pa,[l("div",ja,b(V(t).getUnresolvedCount),1),m[13]||(m[13]=l("div",{class:"kpi-label"},"未处理预警",-1))])])]),_:1})]),_:1}),e(r,{span:6},{default:a(()=>[e(d,{class:"kpi-card success"},{default:a(()=>[l("div",Xa,[l("div",Qa,[e(S,{size:32,color:"#67C23A"},{default:a(()=>[e(V(ye))]),_:1})]),l("div",Za,[l("div",Ja,b(V(t).getOnlineDevicesCount),1),m[14]||(m[14]=l("div",{class:"kpi-label"},"在线设备",-1))])])]),_:1})]),_:1}),e(r,{span:6},{default:a(()=>[e(d,{class:"kpi-card info"},{default:a(()=>[l("div",Ka,[l("div",Na,[e(S,{size:32,color:"#909399"},{default:a(()=>[e(V(Ce))]),_:1})]),l("div",en,[l("div",tn,b(V(t).getMaintenanceDevicesCount),1),m[15]||(m[15]=l("div",{class:"kpi-label"},"维护中设备",-1))])])]),_:1})]),_:1})]),_:1})]),e(o,{gutter:20,class:"charts-section"},{default:a(()=>[e(r,{span:12},{default:a(()=>[e(d,null,{header:a(()=>m[16]||(m[16]=[l("span",null,"今日预警趋势",-1)])),default:a(()=>[l("div",{ref_key:"trendChartRef",ref:u,class:"chart"},null,512)]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(d,null,{header:a(()=>m[17]||(m[17]=[l("span",null,"预警类别分布",-1)])),default:a(()=>[l("div",{ref_key:"categoryChartRef",ref:y,class:"chart"},null,512)]),_:1})]),_:1})]),_:1}),e(o,{gutter:20,class:"charts-section"},{default:a(()=>[e(r,{span:12},{default:a(()=>[e(d,null,{header:a(()=>m[18]||(m[18]=[l("span",null,"设备健康度分布",-1)])),default:a(()=>[l("div",{ref_key:"healthChartRef",ref:p,class:"chart"},null,512)]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(d,null,{header:a(()=>m[19]||(m[19]=[l("span",null,"预警处理统计",-1)])),default:a(()=>[l("div",{ref_key:"resolutionChartRef",ref:c,class:"chart"},null,512)]),_:1})]),_:1})]),_:1}),e(d,{class:"recent-warnings"},{header:a(()=>[l("div",an,[m[21]||(m[21]=l("span",null,"最新预警",-1)),e(v,{size:"small",onClick:m[5]||(m[5]=g=>n.value="warnings")},{default:a(()=>[m[20]||(m[20]=f(" 查看全部 ")),e(S,null,{default:a(()=>[e(V(it))]),_:1})]),_:1,__:[20]})])]),default:a(()=>[e(St,{warnings:D.value,onResolve:C},null,8,["warnings"])]),_:1})])):B("",!0),n.value==="warnings"?(T(),U("div",nn,[e(zt)])):B("",!0),n.value==="devices"?(T(),U("div",ln,[e(Sa)])):B("",!0),n.value==="rules"?(T(),U("div",sn,[e(Aa)])):B("",!0)])}}}),Vn=X(on,[["__scopeId","data-v-f143e1d9"]]);export{Vn as default};
