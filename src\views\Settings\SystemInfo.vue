<template>
  <div class="system-info">
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><Monitor /></el-icon>
          <span>系统信息</span>
          <div class="header-actions">
            <el-button @click="refreshInfo" :loading="refreshing">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="info-section">
            <template #header>
              <span>基本信息</span>
            </template>
            
            <el-descriptions :column="1" border>
              <el-descriptions-item label="系统版本">
                <el-tag type="primary">v{{ systemInfo.version }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="构建日期">
                {{ systemInfo.buildDate }}
              </el-descriptions-item>
              <el-descriptions-item label="运行环境">
                <el-tag :type="systemInfo.environment === 'production' ? 'success' : 'warning'">
                  {{ getEnvironmentText(systemInfo.environment) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="运行时间">
                {{ formatUptime(systemInfo.uptime) }}
              </el-descriptions-item>
              <el-descriptions-item label="最后备份">
                {{ formatTime(systemInfo.lastBackup) }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card class="info-section">
            <template #header>
              <span>服务状态</span>
            </template>
            
            <div class="service-status">
              <div class="status-item">
                <div class="status-icon success">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="status-info">
                  <div class="status-title">网络连接</div>
                  <div class="status-desc">{{ systemInfo.networkStatus }}</div>
                </div>
              </div>
              
              <div class="status-item">
                <div class="status-icon success">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="status-info">
                  <div class="status-title">数据库</div>
                  <div class="status-desc">{{ systemInfo.databaseStatus }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="resource-card">
            <template #header>
              <span>CPU使用率</span>
            </template>
            
            <div class="resource-content">
              <div class="resource-chart">
                <el-progress 
                  type="circle" 
                  :percentage="systemInfo.cpuUsage" 
                  :color="getResourceColor(systemInfo.cpuUsage)"
                  :width="120"
                />
              </div>
              <div class="resource-info">
                <div class="resource-value">{{ systemInfo.cpuUsage.toFixed(1) }}%</div>
                <div class="resource-status" :class="getResourceStatus(systemInfo.cpuUsage)">
                  {{ getResourceStatusText(systemInfo.cpuUsage) }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card class="resource-card">
            <template #header>
              <span>内存使用率</span>
            </template>
            
            <div class="resource-content">
              <div class="resource-chart">
                <el-progress 
                  type="circle" 
                  :percentage="systemInfo.memoryUsage" 
                  :color="getResourceColor(systemInfo.memoryUsage)"
                  :width="120"
                />
              </div>
              <div class="resource-info">
                <div class="resource-value">{{ systemInfo.memoryUsage.toFixed(1) }}%</div>
                <div class="resource-status" :class="getResourceStatus(systemInfo.memoryUsage)">
                  {{ getResourceStatusText(systemInfo.memoryUsage) }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card class="resource-card">
            <template #header>
              <span>磁盘使用率</span>
            </template>
            
            <div class="resource-content">
              <div class="resource-chart">
                <el-progress 
                  type="circle" 
                  :percentage="systemInfo.diskUsage" 
                  :color="getResourceColor(systemInfo.diskUsage)"
                  :width="120"
                />
              </div>
              <div class="resource-info">
                <div class="resource-value">{{ systemInfo.diskUsage.toFixed(1) }}%</div>
                <div class="resource-status" :class="getResourceStatus(systemInfo.diskUsage)">
                  {{ getResourceStatusText(systemInfo.diskUsage) }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="info-section">
            <template #header>
              <span>业务统计</span>
            </template>
            
            <div class="business-stats">
              <div class="stat-item">
                <div class="stat-icon">
                  <el-icon size="24" color="#67C23A"><UserFilled /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ systemInfo.activeUsers }}</div>
                  <div class="stat-label">活跃用户</div>
                </div>
              </div>
              
              <div class="stat-item">
                <div class="stat-icon">
                  <el-icon size="24" color="#409EFF"><Monitor /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ systemInfo.totalDevices }}</div>
                  <div class="stat-label">设备总数</div>
                </div>
              </div>
              
              <div class="stat-item">
                <div class="stat-icon">
                  <el-icon size="24" color="#E6A23C"><Warning /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ systemInfo.totalWarnings }}</div>
                  <div class="stat-label">预警总数</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card class="info-section">
            <template #header>
              <span>系统操作</span>
            </template>
            
            <div class="system-actions">
              <el-button type="primary" @click="restartSystem" :loading="restarting">
                <el-icon><RefreshLeft /></el-icon>
                重启系统
              </el-button>
              
              <el-button type="warning" @click="clearCache" :loading="clearing">
                <el-icon><Delete /></el-icon>
                清理缓存
              </el-button>
              
              <el-button @click="downloadLogs">
                <el-icon><Download /></el-icon>
                下载日志
              </el-button>
              
              <el-button @click="checkUpdates" :loading="checking">
                <el-icon><Refresh /></el-icon>
                检查更新
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Monitor,
  Refresh,
  CircleCheck,
  UserFilled,
  Warning,
  RefreshLeft,
  Delete,
  Download
} from '@element-plus/icons-vue'
import { useSettingsStore } from '@/stores/settings'
import dayjs from 'dayjs'
import { ElMessage, ElMessageBox } from 'element-plus'

const settingsStore = useSettingsStore()

// 响应式数据
const refreshing = ref(false)
const restarting = ref(false)
const clearing = ref(false)
const checking = ref(false)

// 计算属性
const systemInfo = computed(() => settingsStore.systemInfo)

// 方法
const getEnvironmentText = (env: string) => {
  const texts = {
    development: '开发环境',
    testing: '测试环境',
    staging: '预发布环境',
    production: '生产环境'
  }
  return texts[env as keyof typeof texts] || env
}

const formatUptime = (days: number) => {
  if (days < 1) {
    const hours = Math.floor(days * 24)
    return `${hours} 小时`
  }
  return `${days} 天`
}

const formatTime = (timestamp: string) => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

const getResourceColor = (usage: number) => {
  if (usage < 60) return '#67C23A'
  if (usage < 80) return '#E6A23C'
  return '#F56C6C'
}

const getResourceStatus = (usage: number) => {
  if (usage < 60) return 'normal'
  if (usage < 80) return 'warning'
  return 'danger'
}

const getResourceStatusText = (usage: number) => {
  if (usage < 60) return '正常'
  if (usage < 80) return '偏高'
  return '过高'
}

const refreshInfo = async () => {
  refreshing.value = true
  try {
    settingsStore.refreshSystemInfo()
    ElMessage.success('系统信息已刷新')
  } finally {
    refreshing.value = false
  }
}

const restartSystem = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重启系统吗？这将中断所有用户的连接。',
      '确认重启',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    restarting.value = true
    await new Promise(resolve => setTimeout(resolve, 3000))
    ElMessage.success('系统重启完成')
  } catch {
    // 用户取消
  } finally {
    restarting.value = false
  }
}

const clearCache = async () => {
  clearing.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('缓存清理完成')
  } finally {
    clearing.value = false
  }
}

const downloadLogs = () => {
  ElMessage.success('开始下载系统日志')
}

const checkUpdates = async () => {
  checking.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.info('当前已是最新版本')
  } finally {
    checking.value = false
  }
}
</script>

<style lang="scss" scoped>
.system-info {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    > div:first-child {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
    }
  }
  
  .info-section {
    margin-bottom: 20px;
    
    :deep(.el-card__header) {
      padding: 12px 20px;
      background-color: var(--el-bg-color-page);
      font-weight: 600;
    }
  }
  
  .service-status {
    .status-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid var(--el-border-color-lighter);
      
      &:last-child {
        border-bottom: none;
      }
      
      .status-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        
        &.success {
          background-color: #f0f9ff;
          color: #67C23A;
        }
      }
      
      .status-info {
        .status-title {
          font-weight: 600;
          margin-bottom: 4px;
        }
        
        .status-desc {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
  
  .resource-card {
    margin-bottom: 20px;
    
    .resource-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px 0;
      
      .resource-chart {
        margin-bottom: 16px;
      }
      
      .resource-info {
        text-align: center;
        
        .resource-value {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 4px;
        }
        
        .resource-status {
          font-size: 12px;
          
          &.normal { color: #67C23A; }
          &.warning { color: #E6A23C; }
          &.danger { color: #F56C6C; }
        }
      }
    }
  }
  
  .business-stats {
    display: flex;
    justify-content: space-around;
    
    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .stat-icon {
        margin-bottom: 8px;
      }
      
      .stat-info {
        text-align: center;
        
        .stat-value {
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
  
  .system-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }
}
</style>
