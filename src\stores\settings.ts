import { defineStore } from 'pinia'
import type {
  SystemSettings,
  User,
  Role,
  Permission,
  SystemLog,
  SystemInfo,
  UserRole
} from '@/types'
import { settingsGenerator } from '@/utils/settingsGenerator'
// import {
//   settingsDAO,
//   dbInitializer,
//   dbManager
// } from '@/utils/databaseConfig'
// import {
//   userDAO,
//   roleDAO,
//   permissionDAO,
//   systemLogDAO
// } from '@/utils/dataAccess'
// import { dataBackup, autoBackup } from '@/utils/dataBackup'

export const useSettingsStore = defineStore('settings', {
  state: () => ({
    // 系统设置
    settings: settingsGenerator.getDefaultSettings() as SystemSettings,

    // 用户管理
    users: settingsGenerator.getUsers() as User[],
    currentUser: null as User | null,

    // 角色权限
    roles: settingsGenerator.getRoles() as Role[],
    permissions: settingsGenerator.getPermissions() as Permission[],

    // 系统日志
    systemLogs: settingsGenerator.getSystemLogs() as SystemLog[],

    // 系统信息
    systemInfo: settingsGenerator.getSystemInfo() as SystemInfo,

    // 数据库状态
    databaseInitialized: false,

    // 加载状态
    loading: false,

    // 对话框状态
    showUserDialog: false,
    showRoleDialog: false,
    showBackupDialog: false,

    // 选中项
    selectedUser: null as User | null,
    selectedRole: null as Role | null,

    // 筛选条件
    userFilters: {
      role: null as UserRole | null,
      status: null as User['status'] | null,
      department: null as string | null
    },

    logFilters: {
      level: null as SystemLog['level'] | null,
      category: null as string | null,
      dateRange: null as [string, string] | null
    }
  }),

  getters: {
    // 获取筛选后的用户列表
    getFilteredUsers: (state) => {
      let filtered = [...state.users]
      
      if (state.userFilters.role) {
        filtered = filtered.filter(u => u.role === state.userFilters.role)
      }
      
      if (state.userFilters.status) {
        filtered = filtered.filter(u => u.status === state.userFilters.status)
      }
      
      if (state.userFilters.department) {
        filtered = filtered.filter(u => u.department === state.userFilters.department)
      }
      
      return filtered.sort((a, b) => 
        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      )
    },
    
    // 获取筛选后的日志列表
    getFilteredLogs: (state) => {
      let filtered = [...state.systemLogs]
      
      if (state.logFilters.level) {
        filtered = filtered.filter(l => l.level === state.logFilters.level)
      }
      
      if (state.logFilters.category) {
        filtered = filtered.filter(l => l.category === state.logFilters.category)
      }
      
      if (state.logFilters.dateRange) {
        const [start, end] = state.logFilters.dateRange
        filtered = filtered.filter(l => {
          const logDate = new Date(l.timestamp)
          return logDate >= new Date(start) && logDate <= new Date(end)
        })
      }
      
      return filtered
    },
    
    // 获取活跃用户数量
    getActiveUsersCount: (state) => {
      return state.users.filter(u => u.status === 'active').length
    },
    
    // 获取各部门用户统计
    getDepartmentStats: (state) => {
      const stats: Record<string, number> = {}
      state.users.forEach(user => {
        stats[user.department] = (stats[user.department] || 0) + 1
      })
      return stats
    },
    
    // 获取角色统计
    getRoleStats: (state) => {
      const stats: Record<UserRole, number> = {
        admin: 0,
        operator: 0,
        viewer: 0,
        maintenance: 0
      }
      state.users.forEach(user => {
        stats[user.role]++
      })
      return stats
    },
    
    // 获取日志级别统计
    getLogLevelStats: (state) => {
      const stats = {
        debug: 0,
        info: 0,
        warn: 0,
        error: 0,
        fatal: 0
      }
      state.systemLogs.forEach(log => {
        stats[log.level]++
      })
      return stats
    }
  },

  actions: {
    // 初始化数据库
    async initializeDatabase() {
      // 暂时禁用数据库功能
      console.log('Database functionality temporarily disabled')
      this.databaseInitialized = false
    },

    // 暂时禁用数据库相关方法
    async loadAllData() {
      console.log('Database methods temporarily disabled')
    },

    loadMemoryData() {
      console.log('Using memory data')
    },

    async saveDefaultSettings() {
      console.log('Save default settings disabled')
    },

    async seedUsers() {
      console.log('Seed users disabled')
    },

    async seedRoles() {
      console.log('Seed roles disabled')
    },

    async seedPermissions() {
      console.log('Seed permissions disabled')
    },

    async seedSystemLogs() {
      console.log('Seed system logs disabled')
    },

    // 更新系统设置
    async updateSettings(section: keyof SystemSettings, data: any) {
      this.loading = true
      try {
        this.settings[section] = { ...this.settings[section], ...data }
        // 数据库功能暂时禁用
        return true
      } catch (error) {
        console.error('更新设置失败:', error)
        return false
      } finally {
        this.loading = false
      }
    },
    
    // 用户管理
    async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>) {
      const newUser: User = {
        ...userData,
        id: `user_${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      this.users.push(newUser)
      return newUser
    },

    async updateUser(userId: string, userData: Partial<User>) {
      const userIndex = this.users.findIndex(u => u.id === userId)
      if (userIndex !== -1) {
        this.users[userIndex] = {
          ...this.users[userIndex],
          ...userData,
          updatedAt: new Date().toISOString()
        }
        return true
      }
      return false
    },

    async deleteUser(userId: string) {
      const userIndex = this.users.findIndex(u => u.id === userId)
      if (userIndex !== -1) {
        this.users.splice(userIndex, 1)
        return true
      }
      return false
    },

    async toggleUserStatus(userId: string) {
      const user = this.users.find(u => u.id === userId)
      if (user) {
        user.status = user.status === 'active' ? 'inactive' : 'active'
        user.updatedAt = new Date().toISOString()
        return user.status
      }
      return null
    },
    
    // 角色管理
    async createRole(roleData: Omit<Role, 'id' | 'createdAt' | 'updatedAt'>) {
      const newRole: Role = {
        ...roleData,
        id: `role_${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      this.roles.push(newRole)
      return newRole
    },
    
    async updateRole(roleId: string, roleData: Partial<Role>) {
      const roleIndex = this.roles.findIndex(r => r.id === roleId)
      if (roleIndex !== -1) {
        this.roles[roleIndex] = {
          ...this.roles[roleIndex],
          ...roleData,
          updatedAt: new Date().toISOString()
        }
        return true
      }
      return false
    },
    
    async deleteRole(roleId: string) {
      const role = this.roles.find(r => r.id === roleId)
      if (role && !role.isSystem) {
        const roleIndex = this.roles.findIndex(r => r.id === roleId)
        this.roles.splice(roleIndex, 1)
        return true
      }
      return false
    },
    
    // 系统操作
    async performBackup() {
      this.loading = true
      try {
        // 模拟备份过程
        await new Promise(resolve => setTimeout(resolve, 3000))
        this.systemInfo.lastBackup = new Date().toISOString()
        return {
          success: true,
          filename: `backup_${new Date().toISOString().split('T')[0]}.zip`,
          size: '125.6 MB'
        }
      } finally {
        this.loading = false
      }
    },

    async restoreBackup(file: File) {
      this.loading = true
      try {
        // 模拟恢复过程
        await new Promise(resolve => setTimeout(resolve, 5000))
        return true
      } finally {
        this.loading = false
      }
    },

    // 启动自动备份
    startAutoBackup(intervalHours: number = 24) {
      console.log('Auto backup disabled')
    },

    // 停止自动备份
    stopAutoBackup() {
      console.log('Auto backup disabled')
    },

    // 获取自动备份列表
    getAutoBackups() {
      return []
    },

    // 恢复自动备份
    async restoreAutoBackup(backupKey: string) {
      console.log('Auto backup restore disabled')
      return false
    },
    
    async clearLogs(olderThan?: string) {
      if (olderThan) {
        const cutoffDate = new Date(olderThan)
        this.systemLogs = this.systemLogs.filter(log => 
          new Date(log.timestamp) > cutoffDate
        )
      } else {
        this.systemLogs = []
      }
      return true
    },
    
    async exportLogs(format: 'csv' | 'json' | 'txt') {
      // 模拟导出过程
      await new Promise(resolve => setTimeout(resolve, 2000))
      return {
        success: true,
        filename: `system_logs_${new Date().toISOString().split('T')[0]}.${format}`,
        url: '#'
      }
    },
    
    // 筛选器
    setUserFilter(key: keyof typeof this.userFilters, value: any) {
      this.userFilters[key] = value
    },
    
    clearUserFilters() {
      this.userFilters = {
        role: null,
        status: null,
        department: null
      }
    },
    
    setLogFilter(key: keyof typeof this.logFilters, value: any) {
      this.logFilters[key] = value
    },
    
    clearLogFilters() {
      this.logFilters = {
        level: null,
        category: null,
        dateRange: null
      }
    },
    
    // 对话框管理
    showUserEditor(user?: User) {
      this.selectedUser = user || null
      this.showUserDialog = true
    },
    
    hideUserEditor() {
      this.selectedUser = null
      this.showUserDialog = false
    },
    
    showRoleEditor(role?: Role) {
      this.selectedRole = role || null
      this.showRoleDialog = true
    },
    
    hideRoleEditor() {
      this.selectedRole = null
      this.showRoleDialog = false
    },
    
    // 刷新系统信息
    refreshSystemInfo() {
      this.systemInfo = settingsGenerator.getSystemInfo()
    },
    
    // 重置数据
    resetData() {
      this.settings = settingsGenerator.getDefaultSettings()
      this.users = settingsGenerator.getUsers()
      this.roles = settingsGenerator.getRoles()
      this.permissions = settingsGenerator.getPermissions()
      this.systemLogs = settingsGenerator.getSystemLogs()
      this.systemInfo = settingsGenerator.getSystemInfo()
    }
  }
})
