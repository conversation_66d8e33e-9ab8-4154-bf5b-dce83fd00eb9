/*!
 TRW.js version 3.22.1
 */
 (function(a) {
    var d = "%"
      , b = {
        checkPattern: function(e, f) {
            if (f.indexOf(d) == 0 && f.lastIndexOf(d) == f.length - 1) {
                f = f.substring(1, f.length - 1);
                return e.indexOf(f) != -1
            } else {
                if (f.indexOf(d) == 0) {
                    f = f.substring(1, f.length);
                    return e.lastIndexOf(f) == e.length - f.length
                } else {
                    if (f.lastIndexOf(d) == f.length - 1) {
                        f = f.substring(0, f.length - 1);
                        return e.indexOf(f) == 0
                    } else {
                        return e == f
                    }
                }
            }
        },
        compareWithWildcard: function(n, e, f) {
            var l;
            if (f === undefined || f == null) {
                l = d
            } else {
                l = f
            }
            var j = e.split(l);
            if (!this.validateParam(j[j.length - 1])) {
                j.pop()
            }
            var m = e.indexOf(l) + 1 == e.length;
            var h = e.indexOf(l) == 0;
            if (m && j[j.length - 1].lastIndexOf("/") + 1 == j[j.length - 1].length) {
                j[j.length - 1] = j[j.length - 1].substring(0, j[j.length - 1].length - 1)
            }
            for (var g = 0; g < j.length; g++) {
                if (g == 0 && !h) {
                    if (n.indexOf(j[g]) != 0) {
                        return false
                    }
                }
                if (g + 1 == j.length && !m) {
                    if (!(n.indexOf(j[g]) + j[g].length == n.length)) {
                        return false
                    }
                }
                var k = n.indexOf(j[g]);
                if (k == -1) {
                    return false
                }
                n = n.substring(k + j[g].length)
            }
            return true
        },
        validateParam: function(e) {
            return (e !== undefined && e != null && e != "null" && e != "undefined" && e != "")
        }
    }
      , c = {
        supports: function() {
            if (localStorage) {
                return true
            }
            return false
        },
        store: function(e, f) {
            localStorage[e] = f
        },
        getValue: function(e) {
            return localStorage[e]
        },
        remove: function(e) {
            localStorage.removeItem(e)
        }
    };
    a.ITLocalStorageAPI = c;
    a.InsighteraUtil = b
}
)(window);
(function() {
    if (window.AITag) {
        return
    }
    var scriptExecutionTime = new Date().getTime()
      , msgCallTime = 0
      , viewTime = 0
      , isDebug = false;
    var performanceTime = window.performance && window.performance.timing;
    function logMessage(message) {
        if (isDebug && window.console && console.log) {
            console.log("RTP message: " + message)
        }
    }
    function logMessageTime(message, currentTime, compareTo) {
        if (!currentTime) {
            currentTime = new Date().getTime()
        }
        if (compareTo) {
            logMessage(message + (currentTime - compareTo))
        } else {
            if (performanceTime) {
                logMessage(message + (currentTime - performanceTime.domLoading))
            } else {
                logMessage(message + (currentTime - scriptExecutionTime))
            }
        }
    }
    function isDefined(obj) {
        return obj !== void 0 && obj != null
    }
    function isObject(obj) {
        return isDefined(obj) && Object.prototype.toString.call(obj) == "[object Object]"
    }
    function handleDoNotTrack() {
        try {
            if (AITag.getConfig("honorDnt")) {
                var dnt = navigator.doNotTrack == 1 || navigator.msDoNotTrack == 1 || window.doNotTrack == 1;
                if (dnt) {
                    AITag.disableTag();
                    AITag.removeRTPCookies.apply(AITag)
                }
            }
        } catch (e) {
            logMessage("failed to handle do not track " + e)
        }
    }
    function handleOptOut() {
        try {
            var optOut = AITag.extractURLParameter("marketo_opt_out");
            var exp_2_year = new Date();
            var exp_30_minutes = new Date();
            var default_cookie_expiry_value = 730;
            if (!isNaN(AITag.getConfig("cookieExpiration")) && AITag.getConfig("cookieExpiration") != "" && AITag.getConfig("cookieExpiration") != 730) {
                default_cookie_expiry_value = AITag.getConfig("cookieExpiration")
            }
            exp_2_year.setTime(exp_2_year.getTime() + (default_cookie_expiry_value * 24 * 60 * 60 * 1000));
            if (optOut != null) {
                if (optOut == "true") {
                    AITag.setCookie("mkto_opt_out", true, exp_2_year);
                    AITag.removeRTPCookies.apply(AITag);
                    AITag.disableTag()
                } else {
                    AITag.deleteCookie("mkto_opt_out")
                }
            } else {
                optOut = AITag.getCookie("mkto_opt_out");
                if (optOut != null) {
                    AITag.setCookie("mkto_opt_out", true, exp_2_year);
                    AITag.removeRTPCookies.apply(AITag);
                    AITag.disableTag()
                }
            }
        } catch (e) {
            logMessage("failed to handle opt out " + e)
        }
    }
    function doPrivacyCheckup() {
        handleDoNotTrack();
        handleOptOut()
    }
    function doRobotCheck() {
        var robotsList = ["googlebot", "bingbot", "pingdom", "khte", "ktxn", "gomezagent", "ruxitsynthetic", "ktht", "dynatrace", "alertsite", "yottamonitor", "yandex", "baidu", "msnbot", "bingpreview", "soso", "exabot", "sogou", "facebookexternalhit", "feedfetcher", "slurp", "duckduckbot"];
        var userAgent = navigator.userAgent.toLowerCase();
        for (var index in robotsList) {
            if (userAgent.indexOf(robotsList[index]) > -1) {
                AITag.removeRTPCookies.apply(AITag);
                AITag.disableTag();
                consoleMessage("Disabling RTP: recognized bot agent " + robotsList[index]);
                return
            }
        }
    }
    var tagScope = "TAG"
      , scriptScope = "SCRIPT";
    var configScope = {
        enableRecommendationBar: tagScope
    };
    var tagConfig = {};
    function getTagConfig() {
        try {
            if (window.rtp) {
                var config = rtp.e;
                if (isObject(config)) {
                    tagConfig = config
                }
            }
        } catch (e) {
            logMessageTime("Error:: failed to get tag configurations")
        }
    }
    getTagConfig();
    var AITag = new function() {
        var cdnSrc = null;
        var config = {
            "bar_position": "bottom",
            "uaIndustryIndex": 2,
            "enableYoutubeApi": true,
            "gaOrganizationSlot": 2,
            "requiredCssPath": "/insightera-1.3.css",
            "sCatSgmOrgNum": 36,
            "fbEnableGroupPush": false,
            "inZoneBlockingTimeout": 0,
            "uaEnableSizePush": false,
            "useExistingjQueryUI": false,
            "fbEnableCategoryPush": false,
            "initialDelay": 50,
            "uaSizeIndex": 3,
            "campaignInjectionInterval": 50,
            "jQueryUICustomCssPath": "/jquery-ui-insightera-custom-1.9.6.css",
            "fbEnableSegmentedAudiencePush": false,
            "dialogCloseButton": "/dialog-close.png",
            "gaEnableOrganizationtPush": false,
            "gwUrlPrefix": "nld1rtp1.marketo.com/gw1",
            "link_color": "white",
            "pollingPerPage": 1,
            "startup": true,
            "sCatEnableVisitorOrganization": true,
            "trackAsset": true,
            "sCatSgmIndNum": 37,
            "uaEnableOrganizationPush": false,
            "uaEnableABMPush": false,
            "uaEnableSegmentPush": false,
            "sCatCtaEvNum": 15,
            "requestInterval": 50,
            "bar_tab_position": "right",
            "uaEnableRecommendationPush": false,
            "barIconFont": "fa-chevron",
            "fbEnableABMPush": false,
            "ignoreUrlParams": true,
            "disableClick": false,
            "youTubeApiUrl": "www.youtube.com/player_api",
            "dialogCloseOnlyOnActualClose": false,
            "sCatIndVarNum": 35,
            "sCatCtaOrgNum": 38,
            "enableFBRemarketing": true,
            "honorDnt": true,
            "font_color": "black",
            "pollingDelay": 2,
            "jQueryUIDialogPath": "/jquery-ui-insightera-custom-1.4.css",
            "enableITPMitigation": true,
            "loadJquery": false,
            "sCatEnableCampaignPush": true,
            "enableRecommendationBar": false,
            "font_family": "arial",
            "jqueryUrl": "rtp-static.marketo.com/rtp/libs/jquery/VERSION/jquery.min.js",
            "uaOrganizationIndex": 1,
            "getAllEmails": true,
            "font_size": "12px",
            "loadOnlyJQueryDialog": false,
            "uaRevenueIndex": 4,
            "discoverAsset": false,
            "cookieExpiration": 730,
            "viewBlocking": false,
            "enableTag": true,
            "jqueryUiCssExistsOnPage": false,
            "uaEnableRevenuePush": false,
            "jQueryUIVersion": "1.9.2f",
            "uaABMIndex": 5,
            "cdnSrc": "http://rtp-static.marketo.com/rtp/libs",
            "fbEnableCampaignPush": false,
            "jqueryUIUrl": "rtp-static.marketo.com/rtp/libs/jqueryui/VERSION/jquery-custom-ui.min.js",
            "captureLeads": true,
            "dialogCloseTime": 30,
            "fbEnableIndustryPush": false,
            "sCatSgmEvNum": 17,
            "sCatOrgVarNum": 36,
            "barIconOpacity": 0.4,
            "userContextAPI": false,
            "sCatEnableVisitorIndustry": true,
            "debug": false,
            "useExistingJQuery": false,
            "cdnSrcSSL": "https://rtp-static.marketo.com/rtp/libs",
            "enableSiteCatalyst": true,
            "gaEnableVisitorIndustry": false,
            "enableUAnalytics": true,
            "arrowRightPath": "/black_02.png",
            "gaIndustrySlot": 1,
            "timeoutDelay": 3,
            "disablePoll": false,
            "arrowLeftPath": "/black_01.png",
            "jQueryVersion": "1.8.3",
            "sCatCtaIndNum": 39,
            "gaEnableCampaignPush": false,
            "sCatEnableSegmentPush": true,
            "background_color": "#1A95CE",
            "googleAnalytics": true,
            "barIconColor": "#ffffff",
            "uaEnableCampaignPush": false,
            "uaEnableIndustryPush": false,
            "gaEnableSegmentPush": false
        };
        var campaignUrlData = "";
        var currentPageAllowedZones = []
          , currentPageAllowedCampaigns = []
          , allPagesAllowedCampaigs = []
          , potentialCampaignExist = false;
        var sessionReady = false
          , sessionQueue = [];
        var exp_2_year = new Date();
        var exp_30_minutes = new Date();
        var cookiesExpiryDay = config.cookieExpiration && config.cookieExpiration != 730 ? config.cookieExpiration : 730;
        exp_2_year.setTime(exp_2_year.getTime() + (cookiesExpiryDay * 24 * 60 * 60 * 1000));
        exp_30_minutes.setTime(exp_30_minutes.getTime() + (30 * 60 * 1000));
        var sessionId = null;
        var sessionExpireId = null;
        var isDialogPresented = false;
        var isWidgetOrDialogPresented = false;
        var second = 1000;
        var newVisitor = false;
        var newSession = false;
        var viewedZones = [];
        var viewedTypes = [];
        var COOKIE_TRW_SESSION_ID = "trwsa.sid";
        var COOKIE_TRW_SEARCH_TERMS = "trwsb.stu";
        var COOKIE_TRW_CLICK_PER_VISIT = "trwsb.cpv";
        var COOKIE_TRW_CREATION_DATE = "trwv.crd";
        var COOKIE_TRW_COUNT = "trwv.vc";
        var COOKIE_TRW_LAST_VISIT = "trwv.lvd";
        var COOKIE_TRW_EMAIL = "trwv.eml";
        var COOKIE_TRW_VISITOR_UID = "trwv.uid";
        var COOKIE_TRW_IE_EMAIL = "_ie_eml";
        var COOKIE_TRW_IE_CPN = "_ie_cpn";
        var COOKIE_TRW_SESSION_ID_EXPIRE = "trwsb.sid";
        var COOKIE_TRW_CURRENT_VISIT = "trwv.cvd";
        var ESP_COOKIES_ARR = [COOKIE_TRW_SESSION_ID, COOKIE_TRW_EMAIL, COOKIE_TRW_VISITOR_UID];
        var unusedCookies = [COOKIE_TRW_SEARCH_TERMS, COOKIE_TRW_CLICK_PER_VISIT, COOKIE_TRW_CREATION_DATE, COOKIE_TRW_COUNT, COOKIE_TRW_LAST_VISIT, COOKIE_TRW_IE_EMAIL, COOKIE_TRW_IE_CPN, COOKIE_TRW_CURRENT_VISIT, COOKIE_TRW_SESSION_ID_EXPIRE];
        var COOKIE_TRW_LOG = "trw1.LOG";
        var COOKIE_MARKETO_TOKEN = "_mkto_trk";
        var SESSION_ID_CLICKS_SEPERATOR = "$$$###";
        var COOKIE_VAL_SEPERATOR = ":";
        var IMPRESSION_SUFFIX = ":0";
        var CONVERSION_SUFFIX = ":1";
        var MA_TYPE_MARKETO = "1";
        var MA_TYPE_NONE = "0";
        var VISITOR_UUID = "REPLACE_VUUID";
        var SESSION_UUID = "REPLACE_SUUID";
        var VISITOR_IP = "REPLACE_IP";
        var MKTO_LP_DOMAIN = "_mktoLpDomain_";
        var MKTO_LP_SECURE = "_mktoSecureLp_";
        var POSITION_TOP = "50";
        var widgetSide = "";
        var PDF = ".pdf";
        var PPT = ".ppt";
        var PPTX = ".pptx";
        var HTML5_VIDEO_MP4 = ".mp4";
        var HTML5_VIDEO_OGG = ".ogg";
        var HTML5_VIDEO_WEBM = ".webm";
        var RTP_PARAM_INDICATION = "iesrc"
          , RCMD_PARAM_VALUE = "rcmd"
          , ASSET_ID_PARAM = "astid"
          , ASSET_TYPE_PARAM = "at";
        var ASSETS_SUFFIX_ARR = [PDF, PPT, PPTX, HTML5_VIDEO_MP4, HTML5_VIDEO_OGG, HTML5_VIDEO_WEBM];
        var videoIframeMap = {};
        var videoAPILoaded = {};
        var TARGET_VAL = ["_self", "_parent", "_top"];
        var PLAYERS = ["youtube", "wistia", "vimeo"];
        var WISTIA_API_SRC = "fast.wistia.net/static/iframe-api-v1.js";
        var VIMEO_API_SRC = "/vimeo-player.min.js";
        var playersData = {
            players: {},
            playerIds: [],
            lastCount: 0
        };
        var VIMEO_PLAYER_ID = "player_id";
        var EMAIL_REG = /^([A-Za-z0-9!#$%&’*+/=?^_`{|}~\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/;
        var BAR_CDN_PATH = "/insightera-bar-2.12.js";
        var GA_CDN_PATH = "/ga-integration-2.0.5.js";
        var rcmdHoverAssetArray = [];
        var hoverEvtInterval = null;
        var fontsCss = "netdna.bootstrapcdn.com/font-awesome/4.1.0/css/font-awesome.min.css";
        var domainLevel = null;
        var localStorageApi = ITLocalStorageAPI;
        window.iiq = window.iiq || [];
        window.ibq = window.ibq || [];
        var cpq = [];
        var rcmdq = [];
        var campaignLinks = {};
        var assetLinks = {};
        var nonjQueryCampaignQueue = [];
        var mktoExistOnClick = false;
        var eventFirstClick = false;
        var isReady = false;
        var previewMode = false;
        this.richMediaMap = {};
        var checkIESupported = function() {
            var version = getInternetExplorerVersion();
            if (version > -1 && version < 8) {
                return false
            } else {
                if (version > 7) {
                    if (document.documentMode && document.documentMode < 8) {
                        return false
                    }
                }
            }
            return true
        };
        if (!Array.prototype.indexOf) {
            Array.prototype.indexOf = function(obj) {
                for (var i = 0; i < this.length; i++) {
                    if (this[i] == obj) {
                        return i
                    }
                }
                return -1
            }
        }
        var campaignStorage = new function() {
            var STORAGENAME = "it.cs"
              , DIALOG = "dialog"
              , WIDGET = "widget"
              , SESSION_ID = "sid"
              , _this = this;
            trackObj = null,
            isCurrentSession = function(campaigns) {
                if (campaigns[SESSION_ID] !== getSessionId()) {
                    return false
                }
                return true
            }
            ;
            this.init = function() {
                trackObj = window.AITag || null
            }
            ;
            this.getStoredCampaigns = function() {
                var campaigns = null;
                if (localStorageApi.supports()) {
                    campaigns = localStorageApi.getValue(STORAGENAME);
                    if (campaigns !== undefined && campaigns != null) {
                        campaigns = JSON.parse(campaigns)
                    }
                }
                return campaigns
            }
            ;
            this.showStoredCampaigns = function() {
                if (localStorageApi.supports()) {
                    var campaigns = _this.getStoredCampaigns();
                    if (campaigns !== undefined && campaigns != null && isCurrentSession(campaigns)) {
                        for (var campaign in campaigns) {
                            if (campaigns.hasOwnProperty(campaign) && campaign != SESSION_ID) {
                                try {
                                    trackObj.msgCallback(campaigns[campaign][0], campaigns[campaign][1], false, true)
                                } catch (e) {
                                    logMessage("failed to show stored campaign. error: " + e.message)
                                }
                            }
                        }
                    } else {
                        localStorageApi.remove(STORAGENAME);
                        localStorageApi.remove(WIDGET_OPEN)
                    }
                }
            }
            ;
            this.storeCampaign = function(campaignMsg, requestId) {
                if (localStorageApi.supports()) {
                    var campaigns = this.getStoredCampaigns();
                    if (campaigns === undefined || campaigns == null) {
                        campaigns = {}
                    } else {
                        if (!isCurrentSession(campaigns)) {
                            campaigns = {}
                        }
                    }
                    campaigns[SESSION_ID] = getSessionId();
                    if (campaignMsg.targetDiv) {
                        campaigns[campaignMsg.targetDiv] = [campaignMsg, requestId]
                    } else {
                        campaigns[DIALOG] = [campaignMsg, requestId]
                    }
                    localStorageApi.store(STORAGENAME, JSON.stringify(campaigns))
                }
            }
            ;
            this.exc = function(params) {
                try {
                    var method = params.shift();
                    _this[method].apply(_this, params)
                } catch (e) {
                    console.log(e)
                }
            }
        }
        ;
        this.removeRTPCookies = function() {
            for (var i = 0; i < ESP_COOKIES_ARR.length; i++) {
                this.deleteCookie(ESP_COOKIES_ARR[i])
            }
        }
        ;
        if (!String.prototype.trim) {
            String.prototype.trim = function() {
                return this.replace(/^\s+|\s+$/g, "")
            }
        }
        function httpGet(theUrl) {
            var xmlHttp = new XMLHttpRequest();
            xmlHttp.open("GET", theUrl, false);
            xmlHttp.send(null);
            return xmlHttp.responseText
        }
        function makeCorsRequest(url, handler) {
            if (url === undefined || url == null || url == "") {
                return
            }
            if (url.indexOf("http") != 0 && url.indexOf("https") != 0) {
                url = AITag.getConfig("protocol") + "://" + url
            }
            var xhr = createCORSRequest("GET", url);
            if (!xhr) {
                consoleMessage("CORS not supported");
                return
            }
            xhr.onload = function() {
                var text = xhr.responseText;
                var response = JSON.parse(text);
                if (response.status == 200) {
                    if (typeof handler == "function") {
                        handler(response)
                    }
                } else {
                    consoleMessage(response.errorMessage)
                }
            }
            ;
            xhr.onerror = function() {}
            ;
            xhr.send()
        }
        this.activateRecommendation = function() {
            if (this.getConfig("enableRecommendationBar")) {
                var expression = this.getConfig("excludedUrlsRecommendation");
                var match = true;
                var condition = "";
                if (expression !== undefined) {
                    var url = window.location.href;
                    var host = window.location.host;
                    var path = url.substring(url.indexOf(host) + host.length);
                    condition = expression.substring(0, expression.indexOf("[") - 1).trim();
                    var excludeUrls = expression.substring(expression.indexOf("["));
                    excludeUrls = eval(excludeUrls);
                    for (var index = 0; index < excludeUrls.length; index++) {
                        match = InsighteraUtil.checkPattern(path, excludeUrls[index]);
                        if (condition == "in" && match) {
                            break
                        }
                        if (match) {
                            this.setConfig("enableRecommendationBar", false);
                            return
                        }
                    }
                }
                if (condition == "in" && !match) {
                    this.setConfig("enableRecommendationBar", false);
                    return
                }
                addDynamicScript(cdnSrc + BAR_CDN_PATH, function() {});
                logMessageTime("loaded recommendation Bar. Time since DOM loading: ", new Date().getTime())
            }
        }
        ;
        this.eventCallback = function(response) {}
        ;
        this.updateInitialConfig = function() {
            if (!window.AIConfig) {
                window.AIConfig = {}
            }
            campaignStorage.init();
            this.setDefaultConfig("protocol", (("https:" == document.location.protocol) ? "https" : "http"));
            cdnSrc = (AITag.getConfig("protocol") == "http" && getInternetExplorerVersion() == -1) ? this.getConfig("cdnSrc") : this.getConfig("cdnSrcSSL");
            var jQueryUrl = this.getConfig("jqueryUrl");
            jQueryUrl = jQueryUrl.replace("VERSION", this.getConfig("jQueryVersion"));
            var jQueryUIUrl = this.getConfig("jqueryUIUrl");
            jQueryUIUrl = jQueryUIUrl.replace("VERSION", this.getConfig("jQueryUIVersion"));
            this.setConfig("jqueryUrl", jQueryUrl);
            this.setConfig("jqueryUIUrl", jQueryUIUrl);
            this.setConfig("moderntrim2CloseButton", "/modern-trim-II-close.png");
            this.setConfig("dialogTransparentButton", "/trans_x.png");
            this.pollIntervalCounter = 0;
            var mktoTrk = this.getCookie(COOKIE_MARKETO_TOKEN);
            if (mktoTrk != null) {
                this.setDefaultConfig("ma", MA_TYPE_MARKETO)
            } else {
                this.setDefaultConfig("ma", MA_TYPE_NONE)
            }
            addDOMReadyListener(runAsyncQueue);
            if (window.rtp) {
                this.setConfig("pollingPerPage", 2)
            }
            if (isAnalyticsEnabled()) {
                try {
                    iiq.push(["setGWPrefix", AITag.getConfig("gwUrlPrefix")]);
                    iiq.push(["updateConfig", {
                        eGaIndrPush: AITag.getConfig("gaEnableVisitorIndustry"),
                        eGaOrgPush: AITag.getConfig("gaEnableOrganizationtPush"),
                        eGaSgmPush: AITag.getConfig("gaEnableSegmentPush"),
                        eGaCmpPush: AITag.getConfig("gaEnableCampaignPush"),
                        eSIndrPush: AITag.getConfig("sCatEnableVisitorIndustry"),
                        eSOrgPush: AITag.getConfig("sCatEnableVisitorOrganization"),
                        eSSgmPush: AITag.getConfig("sCatEnableSegmentPush"),
                        eSCtaPush: AITag.getConfig("sCatEnableCampaignPush"),
                        gaIndrSlot: AITag.getConfig("gaIndustrySlot"),
                        gaOrgSlot: AITag.getConfig("gaOrganizationSlot"),
                        sIndCC: AITag.getConfig("sCatIndVarNum"),
                        sOrgCC: AITag.getConfig("sCatOrgVarNum"),
                        sSgmCE: AITag.getConfig("sCatSgmEvNum"),
                        sCmpCE: AITag.getConfig("sCatCtaEvNum"),
                        eGA: AITag.getConfig("googleAnalytics"),
                        eSC: AITag.getConfig("enableSiteCatalyst"),
                        sSgmOrgTV: AITag.getConfig("sCatSgmOrgNum"),
                        sSgmIndTV: AITag.getConfig("sCatSgmIndNum"),
                        sCmpOrgTV: AITag.getConfig("sCatCtaOrgNum"),
                        sCmpIndTV: AITag.getConfig("sCatCtaIndNum"),
                        gaOrgIndex: AITag.getConfig("ugaOrgIndex"),
                        gaIndrIndex: AITag.getConfig("ugaIndrIndex"),
                        gaSgmIndex: AITag.getConfig("ugaSgmIndex"),
                        eUA: AITag.getConfig("enableUAnalytics"),
                        eUAOrgPush: AITag.getConfig("uaEnableOrganizationPush"),
                        eUAIndrPush: AITag.getConfig("uaEnableIndustryPush"),
                        eUASizePush: AITag.getConfig("uaEnableSizePush"),
                        eUARevenuePush: AITag.getConfig("uaEnableRevenuePush"),
                        eUAaBMPush: AITag.getConfig("uaEnableABMPush"),
                        eUASgmPush: AITag.getConfig("uaEnableSegmentPush"),
                        eUACtaPush: AITag.getConfig("uaEnableCampaignPush"),
                        eUARcmdPush: AITag.getConfig("uaEnableRecommendationPush"),
                        uaOrgIndex: AITag.getConfig("uaOrganizationIndex"),
                        uaIndrIndex: AITag.getConfig("uaIndustryIndex"),
                        uaSizeIndex: AITag.getConfig("uaSizeIndex"),
                        uaRevenueIndex: AITag.getConfig("uaRevenueIndex"),
                        uaABMIndex: AITag.getConfig("uaABMIndex"),
                        eFC: AITag.getConfig("enableFBRemarketing"),
                        eFCIndPush: AITag.getConfig("fbEnableIndustryPush"),
                        eFCCatPush: AITag.getConfig("fbEnableCategoryPush"),
                        eFCGroupPush: AITag.getConfig("fbEnableGroupPush"),
                        eFCAbmPush: AITag.getConfig("fbEnableABMPush"),
                        eFCSAPush: AITag.getConfig("fbEnableSegmentedAudiencePush"),
                        eFCCmpPush: AITag.getConfig("fbEnableCampaignPush"),
                        newV: AITag.isNewVisitor(),
                        newS: AITag.isNewSession(),
                        aid: AITag.getConfig("accountId")
                    }])
                } catch (e) {
                    logMessage("Error happened while initialize Integration . Error " + e)
                }
            }
        }
        ;
        var getDomain = function(domain, domainLevel) {
            if (domainLevel != null && domainLevel === parseInt(domainLevel)) {
                for (var splitedDomain = domain.split("."); splitedDomain.length > domainLevel && splitedDomain.length > 2; ) {
                    splitedDomain.shift()
                }
                return splitedDomain.join(".")
            }
            splitedDomain = /([^.]+\.[^.]{3,})$/i.exec(domain);
            return splitedDomain != null ? splitedDomain[1] : (splitedDomain = /([^.]+\.[^.]+\.[^.]{2})$/i.exec(domain),
            splitedDomain != null ? splitedDomain[1] : domain)
        };
        var getHostDomain = function(hostname, domainLevel, domainSelectorV2) {
            var splitParts = hostname.split(".")
              , partsLength = splitParts.length
              , level = 2;
            if (domainLevel != null) {
                level = domainLevel
            } else {
                if (isDefined(domainSelectorV2) && domainSelectorV2) {
                    if (splitParts[partsLength - 1] !== "com") {
                        var ipv4Regex = new RegExp("^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$");
                        if (partsLength === 4 && ipv4Regex.test(hostname)) {
                            level = 4
                        } else {
                            if (splitParts[partsLength - 1].length === 2 && partsLength > 1 && splitParts[partsLength - 2] === "co") {
                                level = 3
                            }
                        }
                    }
                } else {
                    if (splitParts[partsLength - 1].length > 2) {
                        level = 2
                    } else {
                        if (splitParts[partsLength - 1].length === 2) {
                            level = 3
                        } else {
                            return hostname
                        }
                    }
                }
            }
            while (splitParts.length > level && splitParts.length > 2) {
                splitParts.shift()
            }
            return splitParts.join(".")
        };
        this.extractURLParameter = function(name) {
            return extractParam(location.search, name)
        }
        ;
        function extractParam(url, name) {
            return decodeURIComponent((new RegExp("[?|&]" + name + "=([^&;]+?)(&|#|;|$)").exec(url) || [, ""])[1].replace(/\+/g, "%20")) || null
        }
        var SCHEME = "://"
          , WWW = "www."
          , WILDCARD = "*"
          , WILDCARD_REGEX = /\*/g
          , ALLOWED_WILDCARD = "%"
          , URL_WITHOUT_QUERY = "url"
          , WIDGET_OPEN = "widget.open"
          , URL_WITH_QUERY = "urlWithQuery";
        function containsWildcard(url) {
            return url.indexOf(WILDCARD) > -1
        }
        function getCurrentUrlAsArray() {
            var location = window.location;
            var currentURL = normalizeURLPrefix(location.host) + location.pathname;
            var urlArray = [];
            urlArray[URL_WITH_QUERY] = (InsighteraUtil.validateParam(location.search) || InsighteraUtil.validateParam(location.hash)) ? currentURL + location.search + location.hash : normalizeURLSuffix(currentURL);
            urlArray[URL_WITH_QUERY] = decodeURI(urlArray[URL_WITH_QUERY]);
            urlArray[URL_WITHOUT_QUERY] = (InsighteraUtil.validateParam(location.hash)) ? currentURL + location.hash : normalizeURLSuffix(currentURL);
            urlArray[URL_WITHOUT_QUERY] = decodeURI(urlArray[URL_WITHOUT_QUERY]);
            return urlArray
        }
        function normalizeURLPrefix(url) {
            var idx = url.indexOf(SCHEME);
            if (idx > -1 && idx < 6) {
                url = url.substring(idx + SCHEME.length)
            }
            idx = url.indexOf(WWW);
            if (idx == 0) {
                url = url.substring(WWW.length)
            }
            return url
        }
        function normalizeURLSuffix(url, includeQuery) {
            if (includeQuery !== undefined && (includeQuery == "false" || includeQuery === false)) {
                var idx = url.indexOf("?")
                  , hashIdx = url.indexOf("#");
                var hashString = "";
                if (idx > 0) {
                    if (hashIdx > -1) {
                        hashString = url.substring(hashIdx)
                    }
                    url = url.substring(0, idx);
                    url += hashString
                }
            }
            idx = url.lastIndexOf("/");
            if (idx + 1 == url.length) {
                url = url.substring(0, url.length - 1)
            }
            return url
        }
        function normalizeURL(url, includeQuery) {
            url = normalizeURLPrefix(url);
            url = normalizeURLSuffix(url, includeQuery);
            return url
        }
        function matchUrls(urlFilters, includeUrlQuery) {
            if (urlFilters === undefined || urlFilters == null || urlFilters.length == 0) {
                return true
            }
            var currentUrl = getCurrentUrlAsArray(), url;
            for (var i = 0; i < urlFilters.length; i++) {
                if (isURLMatch(currentUrl, urlFilters[i], includeUrlQuery)) {
                    return true
                }
            }
            return false
        }
        function isURLMatch(currentUrl, url, includeQuery) {
            url = normalizeURL(url, includeQuery);
            url = decodeURI(url);
            url = url.replace(WILDCARD_REGEX, ALLOWED_WILDCARD);
            return includeQuery == "true" || includeQuery === true ? InsighteraUtil.compareWithWildcard(currentUrl[URL_WITH_QUERY], url) : InsighteraUtil.compareWithWildcard(currentUrl[URL_WITHOUT_QUERY], url)
        }
        function concatArray(first, second, notAllowed) {
            for (var i = 0; i < second.length; i++) {
                if (InsighteraUtil.validateParam(second[i]) && notAllowed === undefined || notAllowed.indexOf(second[i]) == -1) {
                    first.push(second[i])
                }
            }
            return first
        }
        this.checkExcludeUrls = function() {
            var excludeUrls = this.getConfig("excludeUrls");
            if (excludeUrls !== undefined) {
                var ignoreUrlParams = this.getConfig("ignoreUrlParams");
                if (typeof ignoreUrlParams == "undefined") {
                    ignoreUrlParams = true
                }
                var url = window.location.pathname;
                if (!ignoreUrlParams) {
                    url += window.location.search
                }
                for (var index = 0; index < excludeUrls.length; index++) {
                    if (InsighteraUtil.checkPattern(url, excludeUrls[index])) {
                        return true
                    }
                }
            }
            return false
        }
        ;
        this.hex2rgb = function(hex, opacity) {
            var rgb = hex.replace("#", "").match(/(.{2})/g);
            var i = 3;
            while (i--) {
                rgb[i] = parseInt(rgb[i], 16)
            }
            if (typeof opacity == "undefined") {
                return "rgb(" + rgb.join(", ") + ")"
            }
            return "rgba(" + rgb.join(", ") + ", " + opacity + ")"
        }
        ;
        this.rgb2hex = function(rgb) {
            var hex;
            var hexString = ""
              , hexWord = "";
            var i = 0;
            if (rgb.indexOf("rgba") == -1) {
                hex = rgb.replace("rgb(", "");
                hex = hex.replace(")", "").replace(" ", "").split(",")
            } else {
                hex = rgb.replace("rgba(", "").replace(")", "").replace(" ", "").split(",")
            }
            if (hex.length == 3) {
                hexString += "#ff"
            } else {
                hexString += "#" + Math.floor(hex[3] * 255).toString(16)
            }
            while (i < 3) {
                hexWord = (hex[i] * 1).toString(16);
                hexWord = hexWord.length == 1 ? "0" + hexWord : hexWord;
                hexString += hexWord;
                i++
            }
            return hexString
        }
        ;
        this.addEvent = function(evnt, elem, func) {
            if (elem.addEventListener) {
                elem.addEventListener(evnt, func, true)
            } else {
                if (elem.attachEvent) {
                    elem.attachEvent("on" + evnt, func)
                } else {
                    elem[evnt] = func
                }
            }
        }
        ;
        function injectScript(elementTagName, location, scriptContent) {
            var script = document.createElement("script");
            script.type = "text/javascript";
            try {
                script.innerHTML = scriptContent
            } catch (e) {
                script.text = scriptContent
            }
            var element = document.getElementsByTagName(elementTagName);
            if (element && element.length > 0) {
                element = element[0];
                switch (location) {
                case "FIRST":
                    element.insertBefore(script, head.firstChild);
                    break;
                case "LAST":
                    element.appendChild(script);
                    break;
                default:
                    break
                }
            }
        }
        this.addScriptInHeader = function(scriptContent) {
            var script = document.createElement("script");
            script.type = "text/javascript";
            try {
                script.innerHTML = scriptContent
            } catch (e) {
                script.text = scriptContent
            }
            var head = document.getElementsByTagName("head")[0];
            head.insertBefore(script, head.firstChild)
        }
        ;
        this.onPlayerStateChange = function(e) {
            try {
                e = e || window.event;
                var targetElement = e.target || e.srcElement;
                var videoData = targetElement.getVideoData();
                if (videoData) {
                    var player = playersData.players.youtube[videoData.video_id];
                    if (e.data == 1 && typeof player.sendVideoAsset == "undefined") {
                        AITag.sendClick(targetElement.getVideoUrl(), null, {
                            yt: 1
                        });
                        playersData.players.youtube[videoData.video_id].sendVideoAsset = 1
                    }
                }
            } catch (e) {}
        }
        ;
        var rcmdClickObjName = "rtpRH"
          , visitorIdProp = "vi"
          , rcmdHistoryProp = "rcmdH"
          , expRcmd = 1000 * 60 * 60 * 24 * 90
          , storeRcmdClick = "storeClick"
          , cleanRCMDHistory = "cleanHistory"
          , getAsParams = "getAsParams"
          , extractAssetId = "extractAssetId"
          , getLastRCMD = "getLastRCMD";
        var RCMDStorage = {};
        RCMDStorage[cleanRCMDHistory] = function(rcmdHistory, currentTime, newAssetId) {
            var existIndex = null;
            var indexsToRemove = [];
            for (var i = 0; i < rcmdHistory.length; i++) {
                for (var assetId in rcmdHistory[i]) {
                    if (rcmdHistory[i].hasOwnProperty(assetId)) {
                        if ((rcmdHistory[i][assetId] + expRcmd) < currentTime) {
                            indexsToRemove.push(i)
                        } else {
                            if (newAssetId && assetId == newAssetId) {
                                indexsToRemove.push(i)
                            }
                        }
                    }
                }
            }
            if (indexsToRemove.length > 0) {
                for (var j = 0; j < indexsToRemove.length; j++) {
                    rcmdHistory.splice(indexsToRemove[j] - j, 1)
                }
            }
            if (rcmdHistory.length == 5) {
                rcmdHistory.splice(0, 1)
            }
            return rcmdHistory
        }
        ;
        RCMDStorage[getLastRCMD] = function(rcmdHistory, currentTime) {
            var lastRCMD = null
              , lastTime = null;
            for (var i = 0; i < rcmdHistory.length; i++) {
                for (var assetId in rcmdHistory[i]) {
                    if (rcmdHistory[i].hasOwnProperty(assetId)) {
                        if ((rcmdHistory[i][assetId] + expRcmd) > currentTime) {
                            if (lastRCMD == null || lastTime < rcmdHistory[i][assetId]) {
                                lastRCMD = assetId;
                                lastTime = rcmdHistory[i][assetId]
                            }
                        }
                    }
                }
            }
            return lastRCMD
        }
        ;
        RCMDStorage[storeRcmdClick] = function(assetId) {
            var visitorId = getVisitorId();
            var currentTime = new Date().getTime();
            if (visitorId != null) {
                var currentData = localStorageApi.getValue(rcmdClickObjName);
                if (currentData !== void 0) {
                    currentData = JSON.parse(currentData);
                    if (currentData[visitorIdProp] && currentData[visitorIdProp] == visitorId) {
                        var rcmdHistory = currentData[rcmdHistoryProp];
                        if (rcmdHistory === void 0 || rcmdHistory == null) {
                            rcmdHistory = []
                        } else {
                            rcmdHistory = this[cleanRCMDHistory](rcmdHistory, currentTime, assetId)
                        }
                    } else {
                        currentData = {};
                        rcmdHistory = []
                    }
                } else {
                    currentData = {};
                    rcmdHistory = []
                }
                var newRcmdClick = {};
                newRcmdClick[assetId] = currentTime;
                rcmdHistory.push(newRcmdClick);
                currentData[visitorIdProp] = visitorId;
                currentData[rcmdHistoryProp] = rcmdHistory;
                localStorageApi.store(rcmdClickObjName, JSON.stringify(currentData))
            }
        }
        ;
        RCMDStorage[getAsParams] = function() {
            var params = null;
            var visitorId = getVisitorId();
            var currentData = localStorageApi.getValue(rcmdClickObjName);
            if (currentData !== void 0) {
                currentData = JSON.parse(currentData);
                var currentTime = new Date().getTime();
                if (currentData[visitorIdProp] && currentData[visitorIdProp] == visitorId) {
                    rcmdHistory = this[cleanRCMDHistory](currentData[rcmdHistoryProp], currentTime, null);
                    var rcmdIds = [];
                    for (var i = 0; i < rcmdHistory.length; i++) {
                        for (var assetId in rcmdHistory[i]) {
                            if (rcmdHistory[i].hasOwnProperty(assetId)) {
                                rcmdIds.push(assetId)
                            }
                        }
                    }
                    if (rcmdIds.length > 0) {
                        params = {};
                        params.rch = rcmdIds.join();
                        var lastRCMD = this[getLastRCMD](rcmdHistory, currentTime);
                        if (lastRCMD != null) {
                            params.lcrc = lastRCMD
                        }
                    }
                }
            }
            return params
        }
        ;
        RCMDStorage[extractAssetId] = function(url) {
            return extractParam(url, ASSET_ID_PARAM)
        }
        ;
        var discoverRecommendation = function(href, discoveryCallback) {
            var paramsIndex = href.indexOf("?");
            if (paramsIndex != -1) {
                var parameters = href.substring(paramsIndex);
                if (parameters.indexOf("iesrc=rcmd") != -1) {
                    try {
                        var assetId = RCMDStorage[extractAssetId](parameters);
                        if (assetId) {
                            RCMDStorage[storeRcmdClick].call(RCMDStorage, assetId)
                        }
                    } catch (e) {}
                    AITag.sendClick(encodeURIComponent(href), discoveryCallback, {
                        cmd: 3,
                        asid: assetId
                    }, true);
                    if (AITag.richMediaMap[parameters] != undefined && !AITag.richMediaMap[parameters]) {
                        AITag.sendClick(encodeURIComponent(href), null, {
                            cmd: 4,
                            asid: assetId,
                            viewId: AITag.getViewId()
                        }, true);
                        AITag.richMediaMap[parameters] = true
                    }
                }
            }
        };
        function isRecommendation(href) {
            var paramsIndex = href.indexOf("?");
            if (paramsIndex != -1) {
                var parameters = href.substring(paramsIndex);
                if (parameters.indexOf("iesrc=rcmd") != -1) {
                    return true
                }
            }
            return false
        }
        var getPalyerId = function(iframe, count) {
            var iframeId;
            if (typeof iframe.id != "undefined" && iframe.id != null && iframe.id != "") {
                iframeId = iframe.id
            } else {
                iframe.id = "player" + count;
                iframeId = "player" + count
            }
            return iframeId
        };
        this.addPlayer = function(type, player, id, src) {
            players = playersData.players[type];
            if (players === undefined) {
                players = {}
            }
            var iplayer = {};
            iplayer.player = player;
            iplayer.src = src;
            players[id] = iplayer;
            playersData.players[type] = players
        }
        ;
        function handleVimeoPlayers() {
            var players = playersData.players.vimeo;
            for (var id in players) {
                (function(player, src) {
                    if (player.element.contentWindow != null) {
                        setTimeout(function() {
                            player.on("play", function(player, src) {
                                return function() {
                                    AITag.sendClick(src, null, {
                                        yt: 1
                                    });
                                    player.removeEvent("play")
                                }
                            }(player, src))
                        }, 500)
                    }
                }
                )(players[id].player, players[id].src)
            }
        }
        var createVimeoEventListener = function(iframe, type) {
            (function(iframe, type, src) {
                var player = new Vimeo.Player(iframe);
                AITag.addPlayer(type, player, iframe.id, src);
                try {
                    (function() {
                        handleVimeoPlayers()
                    }
                    )()
                } catch (e) {}
                player.ready().then(function() {
                    (function() {
                        handleVimeoPlayers()
                    }
                    )()
                })
            }
            )(iframe, type, iframe.src)
        };
        var bindPlayerEvent = function(type, iframe, count, src) {
            iframeId = getPalyerId(iframe, count);
            var players;
            switch (type) {
            case "youtube":
                try {
                    var url = src;
                    var queryStringIndex = url.indexOf("?");
                    if (queryStringIndex > -1) {
                        url = url.substring(0, queryStringIndex)
                    }
                    var videoIdIndex = url.lastIndexOf("/");
                    var videoId = url.substring(videoIdIndex + 1);
                    (function(type, iframeId, videoId) {
                        if (src.indexOf("?") != -1 && src.indexOf("enablejsapi=1") == -1) {
                            iframe.contentWindow.location.replace(iframe.src + "&enablejsapi=1")
                        } else {
                            if (src.indexOf("api=1") == -1) {
                                iframe.contentWindow.location.replace(iframe.src + "?enablejsapi=1")
                            }
                        }
                        var player = iframe.contentWindow;
                        var host = extractHost(url);
                        createYoutubeEventListener(player, host, iframeId);
                        AITag.addPlayer(type, player, videoId, src)
                    }
                    )(type, iframeId, videoId)
                } catch (e) {}
                break;
            case "wistia":
                (function(type, iframeId, src) {
                    var wistiaEmbed = document.getElementById(iframeId).wistiaApi;
                    if (wistiaEmbed) {
                        wistiaEmbed.bind("play", function(src) {
                            return function() {
                                AITag.sendClick(src, null, {
                                    yt: 1
                                });
                                return this.unbind
                            }
                        }(src))
                    }
                    AITag.addPlayer(type, wistiaEmbed, iframeId)
                }
                )(type, iframeId, src);
                break;
            case "vimeo":
                if (window.Vimeo !== undefined) {
                    var iframeSrc = iframe.src;
                    if (iframeSrc.indexOf(VIMEO_PLAYER_ID) == -1) {
                        iframeSrc = addParams2URI(iframeSrc, {
                            player_id: iframeId
                        })
                    }
                    iframe.contentWindow.location.replace(iframeSrc);
                    createVimeoEventListener(iframe, type)
                } else {
                    setTimeout(function() {
                        if (window.Vimeo !== undefined) {
                            var iframeSrc = iframe.src;
                            if (iframeSrc.indexOf(VIMEO_PLAYER_ID) == -1) {
                                iframeSrc = addParams2URI(iframeSrc, {
                                    player_id: iframeId
                                })
                            }
                            iframe.contentWindow.location.replace(iframeSrc);
                            createVimeoEventListener(iframe, type)
                        }
                    }, 250)
                }
                break
            }
            playersData.playerIds.push(src);
            count++;
            playersData.lastCount = count;
            return count
        };
        function extractHost(url, defaultProtocol) {
            var host;
            var originalUrl = url;
            if (url.indexOf("://") == -1) {
                if (typeof defaultProtocol != "undefined") {
                    url = defaultProtocol + "://" + url
                } else {
                    url = "https://" + url
                }
            }
            var el = document.createElement("a");
            el.href = url;
            if (el.protocol != undefined && el.protocol.trim() != "") {
                if (el.host.indexOf("youtube") > -1 && el.protocol == "http:") {
                    host = "https://" + el.host
                } else {
                    host = el.protocol + "//" + el.host
                }
            } else {
                host = originalUrl
            }
            return host
        }
        function createYoutubeEventListener(player, host, iframeId) {
            var addListener = typeof YT != "undefined" && YT.get && YT.get(iframeId) !== undefined;
            addMessageEvent(player, host, !addListener)
        }
        var addMessageEvent = function(player, host, addListener) {
            var interval = null;
            AITag.addEvent("message", window, function(event) {
                if (event.source == player) {
                    if (interval) {
                        interval = clearInterval(interval)
                    }
                    var json = JSON.parse(event.data);
                    if (json.info != null && typeof (json.info.playerState) != "undefined") {
                        var ytPlayer = playersData.players.youtube[json.info.videoData.video_id];
                        if (json.info.playerState == 1 && typeof ytPlayer.sendVideoAsset == "undefined") {
                            AITag.sendClick(ytPlayer.src, null, {
                                yt: 1
                            });
                            ytPlayer.sendVideoAsset = 1
                        }
                    }
                }
            });
            if (addListener) {
                var firstCall = null;
                interval = setInterval(function() {
                    if (!firstCall) {
                        firstCall = (new Date()).getTime()
                    }
                    if ((new Date()).getTime() - firstCall > 2000) {
                        interval = clearInterval(interval)
                    }
                    player.postMessage('{"event":"listening","id":"apiID"}', host)
                }, 250)
            }
        };
        function addVideoIframe(type, iframe, iframeMap, fancyboxLoad) {
            var iframeArray = videoIframeMap[type];
            var currentVideoArray = iframeMap[type]
              , valid = true;
            if (iframeArray === undefined || iframeArray == null) {
                iframeArray = [];
                addPlayersApis(type)
            }
            if (currentVideoArray === void 0 || currentVideoArray == null) {
                currentVideoArray = []
            }
            for (var i = 0; i < iframeArray.length; i++) {
                if (iframeArray[i].id === iframe.id && iframeArray[i].src == iframe.src) {
                    valid = false
                }
            }
            if (valid) {
                if (!fancyboxLoad) {
                    iframeArray.push(iframe);
                    videoIframeMap[type] = iframeArray
                }
                currentVideoArray.push(iframe);
                iframeMap[type] = currentVideoArray
            }
            return iframeMap
        }
        var getPlayersIframes = function(fancyboxLoad) {
            var iframes = document.getElementsByTagName("iframe");
            var iframeMap = {}, src;
            for (var i = 0; i < iframes.length; i++) {
                src = iframes[i].src;
                if (src.indexOf("youtube") != -1) {
                    iframeMap = addVideoIframe("youtube", iframes[i], iframeMap, fancyboxLoad)
                } else {
                    if (src.indexOf("wistia") != -1) {
                        iframeMap = addVideoIframe("wistia", iframes[i], iframeMap, fancyboxLoad)
                    } else {
                        if (src.indexOf("player.vimeo.com") != -1) {
                            iframeMap = addVideoIframe("vimeo", iframes[i], iframeMap, fancyboxLoad)
                        }
                    }
                }
            }
            setTimeout(function() {
                addPlayersListerners(iframeMap)
            }, 0)
        };
        var addPlayersListerners = function(iframeMap) {
            var iframesArr, count = 0;
            for (var i = 0; i < PLAYERS.length; i++) {
                iframesArr = iframeMap[PLAYERS[i]];
                if (iframesArr !== undefined && iframesArr != null && iframesArr.length > 0) {
                    for (var j = 0; j < iframesArr.length; j++) {
                        src = iframesArr[j].src;
                        if (src.indexOf("youtube") != -1) {
                            count = bindPlayerEvent("youtube", iframesArr[j], count, src)
                        } else {
                            if (src.indexOf("wistia") != -1) {
                                count = bindPlayerEvent("wistia", iframesArr[j], count, src)
                            } else {
                                if (src.indexOf("player.vimeo.com") != -1) {
                                    count = bindPlayerEvent("vimeo", iframesArr[j], count, src)
                                }
                            }
                        }
                    }
                }
            }
        };
        var addPlayersApis = function(type) {
            if (videoAPILoaded[type] !== void 0 && videoAPILoaded[type] == 1) {
                return
            }
            videoAPILoaded[type] = 1;
            switch (type) {
            case "youtube":
                break;
            case "vimeo":
                if (!window.Vimeo) {
                    addDynamicScript(cdnSrc + VIMEO_API_SRC)
                }
                break;
            case "wistia":
                addDynamicScript(WISTIA_API_SRC, null, true);
                break;
            default:
                throw "cannot import unknown type: " + type
            }
        };
        var createEvent = function(eventName, target) {
            var event;
            if (document.createEvent) {
                event = document.createEvent("MouseEvents");
                event.initMouseEvent(eventName, target.bubbles, target.cancelable, target.view, target.detail, target.screenX, target.screenY, target.clientX, target.clientY, target.ctrlKey, target.altKey, target.shiftKey, target.metaKey, target.button, target.relatedTarget)
            } else {
                if (document.createEventObject) {
                    event = document.createEventObject();
                    event.eventType = eventName
                }
            }
            event.eventName = eventName;
            return event
        };
        var dispatchEvent = function(el, event) {
            var eventName = event.eventName;
            if (el.dispatchEvent) {
                el.dispatchEvent(event)
            } else {
                if (el.fireEvent && window.htmlEvents && window.htmlEvents["on" + eventName]) {
                    el.fireEvent("on" + event.eventType, event)
                } else {
                    window.open(el.href, (typeof el.target == "undefined" || el.target == null || el.target == "") ? "_self" : el.target)
                }
            }
        };
        var addEventReport = function(target, e) {
            return function() {
                if (e.assetClicked === undefined) {
                    e.assetClicked = true;
                    dispatchEvent(target, e)
                }
            }
        };
        var checkIfAssetLink = function(targetElement, e) {
            var href = targetElement.href;
            if (assetLinks[href] && assetLinks[href] == 1) {
                return
            }
            var path = targetElement.pathname;
            var target = targetElement.target;
            var isExtensionFile = false;
            for (var i = 0; i < ASSETS_SUFFIX_ARR.length; i++) {
                var extentionIndex = path.lastIndexOf(ASSETS_SUFFIX_ARR[i]);
                if (extentionIndex != -1) {
                    isExtensionFile = true;
                    break
                }
            }
            if (isRecommendation(href)) {
                if (isExtensionFile) {
                    discoverRecommendation(href)
                } else {
                    (e.preventDefault) ? e.preventDefault() : e.returnValue = false;
                    discoverRecommendation(href, function() {
                        window.location = href
                    })
                }
            }
            if (typeof target == "undefined" || target == null || target == "") {
                target = "_self"
            }
            if (typeof href != "undefined" && href != null && typeof path != "undefined" && path != null) {
                for (var i = 0; i < ASSETS_SUFFIX_ARR.length; i++) {
                    var lastIndex = path.lastIndexOf(ASSETS_SUFFIX_ARR[i]);
                    if (lastIndex != -1 && (lastIndex + ASSETS_SUFFIX_ARR[i].length) == path.length) {
                        var assetlink = campaignLinks[href];
                        if (TARGET_VAL.indexOf(target) != -1 && !assetlink) {
                            if (e.preventDefault) {
                                e.preventDefault()
                            } else {
                                e.returnValue = false
                            }
                        }
                        assetLinks[href] = 1;
                        var event = createEvent("click", e);
                        event = addEventReport(targetElement, event);
                        AITag.setConfig("disableClick", false);
                        AITag.sendClick(encodeURIComponent(href), function() {
                            if (TARGET_VAL.indexOf(target) != -1 && !campaignLinks[href]) {
                                AITag.setConfig("disableClick", true);
                                event()
                            }
                        });
                        return
                    }
                }
                for (var i = 0; i < PLAYERS.length; i++) {
                    if (href.indexOf(PLAYERS[i]) != -1) {
                        setTimeout(function() {
                            getPlayersIframes(true)
                        }, 800);
                        break
                    }
                }
            }
        };
        this.addAssetCollector = function() {
            if (this.getConfig("trackAsset") || this.getConfig("discoverAsset")) {
                AITag.addEvent("click", document, function(e) {
                    if ((AITag.lastEvent && (new Date).getTime() - AITag.lastEvent < 100) || e.assetClicked || e._mchInRepost) {
                        return true
                    }
                    AITag.lastEvent = (new Date).getTime();
                    e = e || window.event;
                    var targetElement = e.target || e.srcElement;
                    var parent = AITag.jQuery(targetElement).parents("a");
                    if (targetElement.tagName == "A") {
                        checkIfAssetLink(targetElement, e)
                    } else {
                        if (parent.get(0)) {
                            var parentEl = parent.get(0);
                            if (parentEl.tagName == "A") {
                                checkIfAssetLink(parentEl, e)
                            }
                        }
                    }
                    if (targetElement.tagName == "VIDEO") {
                        AITag.sendAssetClick(function() {
                            var src = "";
                            src = targetElement.src;
                            if (src == null || src == "") {
                                var children = targetElement.children;
                                for (var i = 0; i < children.length; i++) {
                                    src = children[i].src;
                                    if (typeof src != "undefined" && src != "") {
                                        break
                                    }
                                }
                            }
                            AITag.sendClick(src)
                        })
                    }
                });
                try {
                    getPlayersIframes()
                } catch (e) {
                    console.log(e)
                }
            }
        }
        ;
        this.sendAssetClick = function(func) {
            func.apply(AITag)
        }
        ;
        this.setConfig = function(key, val) {
            config[key] = val
        }
        ;
        this.setDefaultConfig = function(key, val) {
            if (typeof config[key] === "undefined") {
                this.setConfig(key, val)
            }
        }
        ;
        this.escapePercent = function(text) {
            return encodeURIComponent(text)
        }
        ;
        this.getConfig = function(key) {
            var value = null;
            var scope = configScope[key];
            value = tagConfig[key];
            if (scope == tagScope && isDefined(value)) {
                return value
            }
            if (typeof config != "undefined") {
                value = config[key]
            }
            if (typeof value == "undefined" || value == null) {
                value = window.AIConfig[key]
            }
            return value
        }
        ;
        var getCookieDataArray = function(cookieName) {
            var val = AITag.getCookie(cookieName);
            if (val != null) {
                val = val.split(COOKIE_VAL_SEPERATOR)
            }
            return val
        }
          , getVisitorId = function() {
            var visitorId = null;
            var visitorArr = getCookieDataArray(COOKIE_TRW_VISITOR_UID);
            if (visitorArr != null && visitorArr.length > 0) {
                visitorId = visitorArr[0]
            }
            return visitorId
        }
          , getVisitorIdParam = function() {
            var visitorId = getVisitorId();
            if (visitorId != null) {
                return COOKIE_TRW_VISITOR_UID + "=" + visitorId
            }
            return ""
        }
          , getVisitCount = function() {
            var vc = null;
            var visitorArr = getCookieDataArray(COOKIE_TRW_VISITOR_UID);
            if (visitorArr != null && visitorArr.length > 1) {
                vc = parseInt(visitorArr[1], 10);
                if (isNaN(vc) || vc < 1) {
                    return null
                }
            }
            return vc
        }
          , getVCParam = function() {
            var vc = getVisitCount();
            if (vc != null) {
                return COOKIE_TRW_COUNT + "=" + vc
            }
            return ""
        }
          , getKnown = function() {
            var visitorArr = getCookieDataArray(COOKIE_TRW_VISITOR_UID);
            if (visitorArr != null && visitorArr.length > 2) {
                if (parseInt(visitorArr[2]) == 1) {
                    return 1
                }
            }
            return 0
        }
          , getKnownParam = function() {
            var known = getKnown();
            if (known == 1) {
                return COOKIE_TRW_IE_EMAIL + "=" + known
            }
            return ""
        }
          , getSessionId = function() {
            var sessionId = null;
            var sessionArr = getCookieDataArray(COOKIE_TRW_SESSION_ID);
            if (sessionArr != null && sessionArr.length > 0) {
                sessionId = sessionArr[0]
            }
            return sessionId
        }
          , getSessionIdParam = function() {
            var sessionId = getSessionId();
            if (sessionId != null) {
                return COOKIE_TRW_SESSION_ID + "=" + sessionId
            }
            return ""
        }
          , getClickPerVisit = function() {
            var cpv = null;
            var sessionArr = getCookieDataArray(COOKIE_TRW_SESSION_ID);
            if (sessionArr != null && sessionArr.length > 1) {
                cpv = parseInt(sessionArr[1]);
                if (isNaN(cpv) || cpv < 1) {
                    return null
                }
            }
            return cpv
        }
          , getCPVParam = function() {
            var cpv = getClickPerVisit();
            if (cpv != null) {
                return COOKIE_TRW_CLICK_PER_VISIT + "=" + cpv
            }
            return ""
        };
        var getEmail = function() {
            var email = AITag.getCookie(COOKIE_TRW_EMAIL);
            AITag.deleteCookie(COOKIE_TRW_EMAIL);
            return email
        }
          , getEmailParam = function() {
            var email = getEmail();
            if (email != null && email !== "null") {
                return COOKIE_TRW_EMAIL + "=" + encodeURIComponent(email)
            }
            return ""
        };
        var getClicksPerVisitFromCookie = function(cookie) {
            var cookieVal = cookie.substring(COOKIE_TRW_CLICK_PER_VISIT.length + 1, cookie.length);
            var tmpArr = cookieVal.split(encodeURIComponent(SESSION_ID_CLICKS_SEPERATOR));
            return COOKIE_TRW_CLICK_PER_VISIT + "=" + tmpArr[1]
        };
        this.getCookie = function(name) {
            var cookieVal = null
              , cookies = document.cookie.split(";");
            var cookieRegex = RegExp("^\\s*" + name + "=\\s*(.*?)\\s*$");
            for (var index = 0; index < cookies.length; index++) {
                var matches = cookies[index].match(cookieRegex);
                if (matches && matches[1]) {
                    cookieVal = decodeURIComponent(matches[1]);
                    break
                }
            }
            return cookieVal
        }
        ;
        this.setCookie = function(name, value, expires, path, secure) {
            var domain = getDomain(window.location.hostname, AITag.getConfig("domainLevel"));
            if (domain == "localhost") {
                domain = null
            }
            document.cookie = name + "=" + encodeURIComponent(value) + ((expires == null) ? "" : ("; expires=" + expires.toGMTString())) + "; path=" + ((path == null) ? "/" : path) + ((domain == null) ? "" : ("; domain=" + domain)) + ((secure == true || document.location.protocol == "https:") ? "; SameSite=None; secure" : "")
        }
        ;
        this.deleteCookie = function(name, path, domain, secure) {
            var domain = getDomain(window.location.hostname, AITag.getConfig("domainLevel"));
            if (domain == "localhost") {
                domain = null
            }
            var exp = new Date();
            exp.setTime(exp.getTime() - 1);
            var cval = this.getCookie(name);
            document.cookie = name + "=" + cval + "; expires=" + exp.toGMTString() + "; path=" + ((path == null) ? "/" : path) + ((domain == null) ? "" : ("; domain=" + domain)) + ((secure == true) ? "; secure" : "")
        }
        ;
        this.getCookieVal = function(offset) {
            var endstr = document.cookie.indexOf(";", offset);
            if (endstr == -1) {
                endstr = document.cookie.length
            }
            return decodeURIComponent(document.cookie.substring(offset, endstr))
        }
        ;
        addDynamicScript = function(src, callback, appendToBody, async) {
            var destination;
            if (appendToBody) {
                destination = document.getElementsByTagName("body")[0]
            } else {
                destination = document.getElementsByTagName("head")[0] || document.documentElement
            }
            var scriptTimeout = null;
            var newScript = document.createElement("script");
            newScript.type = "text/javascript";
            if (src.indexOf("http") != 0 && src.indexOf("https") != 0) {
                src = AITag.getConfig("protocol") + "://" + src
            }
            newScript.src = src;
            newScript.async = async || true;
            newScript.onload = newScript.onreadystatechange = function() {
                if (!this.readyState || this.readyState === "loaded" || this.readyState === "complete") {
                    newScript.onload = newScript.onreadystatechange = null;
                    clearTimeout(scriptTimeout);
                    scriptTimeout = null;
                    if (typeof callback === "function") {
                        callback.apply(AITag)
                    }
                    if (destination && newScript.parentNode) {
                        destination.removeChild(newScript)
                    }
                }
            }
            ;
            try {
                scriptTimeout = setTimeout(function() {
                    if (typeof callback === "function") {
                        callback.apply(AITag)
                    }
                }, AITag.getConfig("timeoutDelay") * second)
            } catch (e) {}
            if (appendToBody) {
                destination.appendChild(newScript)
            } else {
                destination.insertBefore(newScript, destination.firstChild)
            }
        }
        ;
        this.addDynamicJavaScript = function(scriptContent) {
            var script = document.createElement("script");
            script.type = "text/javascript";
            try {
                script.innerHTML = scriptContent
            } catch (e) {
                script.text = scriptContent
            }
            document.body.appendChild(script)
        }
        ;
        this.addDynamicStyle = function(src) {
            var headID = document.getElementsByTagName("head")[0];
            var newStyle = document.createElement("link");
            if (src.indexOf("http") == -1 && src.indexOf("https") == -1) {
                src = AITag.getConfig("protocol") + "://" + src
            }
            newStyle.type = "text/css";
            newStyle.href = src;
            newStyle.rel = "stylesheet";
            headID.appendChild(newStyle)
        }
        ;
        var addDynamicDiv = function(title, html) {
            var newDiv = document.getElementById("trwDialog");
            if (!newDiv) {
                newDiv = document.createElement("div");
                newDiv.setAttribute("id", "trwDialog");
                newDiv.setAttribute("style", "display:none;");
                document.body.appendChild(newDiv)
            }
            return AITag.jQuery(newDiv)
        };
        this.getStartIndxSearchQuery = function() {
            var start = -1;
            var ref = document.referrer;
            if (ref.search(/google\.*/i) != -1 || ref.search(/altavista\.*/i) != -1 || ref.search(/aol\.*/i) != -1 || ref.search(/alltheweb\.*/i) != -1 || ref.search(/ask\.*/i) != -1 || ref.search(/bing\.*/i) != -1 || ref.search(/walla\.*/i) != -1) {
                start = ref.search(/q=/)
            } else {
                if (ref.search(/yahoo\.*/i) != -1) {
                    start = ref.search(/p=/)
                } else {
                    if (ref.search(/lycos\.*/i) != -1) {
                        start = ref.search(/query=/)
                    } else {
                        if (ref.search(/dogpile\.*/i) != -1) {
                            start = ref.search(/\/search\/web\/=/)
                        } else {
                            if (ref.search(/dmoz\.*/i) != -1) {
                                start = ref.search(/search=/)
                            }
                        }
                    }
                }
            }
            return start
        }
        ;
        this.getSearchTerms = function() {
            var ref = document.referrer;
            var searchTerms = null;
            if (ref && ref != "") {
                var start = this.getStartIndxSearchQuery();
                if (start != -1) {
                    searchTerms = ref.substring(start + 2);
                    var end = searchTerms.search(/&/);
                    end = (end == -1) ? searchTerms.length : end;
                    searchTerms = searchTerms.substring(0, end);
                    if (searchTerms.length != 0) {
                        searchTerms = searchTerms.replace(/\+/g, " ");
                        searchTerms = decodeURIComponent(searchTerms)
                    }
                }
            }
            return searchTerms
        }
        ;
        function random() {
            return "xxxxxxxx".replace(/[x]/g, function(c) {
                var r = Math.random() * 16 | 0;
                return r.toString(16)
            })
        }
        function generateUUID() {
            var prefix = AITag.getConfig("accountId");
            if (prefix) {
                prefix = prefix.replace(".", "-")
            }
            return prefix + "-" + (new Date).getTime() + "-" + random()
        }
        var validateSessionId = function(uuid) {
            if (uuid == null) {
                return false
            }
            var token;
            var tokenized;
            tokenized = uuid.split("-");
            if (tokenized.length < 3) {
                return false
            }
            if (tokenized[tokenized.length - 1].length != 8) {
                return false
            }
            token = tokenized[tokenized.length - 2];
            if (!(/^\d+$/.test(token))) {
                return false
            }
            token = "";
            for (var i = 0; i < (tokenized.length - 2); i++) {
                token += tokenized[i] + "."
            }
            token = token.substring(0, token.length - 1);
            var aid = AITag.getConfig("accountId");
            if (!(token == aid.replace(/-/g, "."))) {
                return false
            }
            return true
        };
        this.createSessionId = function(isEvent) {
            if (this.isNewSession()) {
                newSession = true;
                sessionId = generateUUID()
            }
            var sessionArr = addCPV.call(this, sessionId, isEvent);
            this.deleteCookie(COOKIE_TRW_SESSION_ID);
            this.setCookie(COOKIE_TRW_SESSION_ID, sessionArr[0] + COOKIE_VAL_SEPERATOR + sessionArr[1], exp_30_minutes);
            if (!sessionReady) {
                sessionReady = true;
                try {
                    runSessionQueue()
                } catch (e) {
                    logMessage("failed to run session ready queue, error: " + e)
                }
            }
        }
        ;
        this.isNewSession = function() {
            sessionId = this.getCookie(COOKIE_TRW_SESSION_ID);
            if (!validateSessionId(getSessionId())) {
                sessionId = null
            }
            return (sessionId == null)
        }
        ;
        var addCPV = function(sessionId, isEvent) {
            var firstClick = false;
            var sessionArr = sessionId.split(COOKIE_VAL_SEPERATOR);
            if (sessionArr.length > 1) {
                sessionArr[1] = parseInt(sessionArr[1], 10);
                if (!isNaN(sessionArr[1]) && sessionArr[1] > 0) {
                    if (!isEvent && !eventFirstClick) {
                        sessionArr[1] += 1
                    }
                }
            } else {
                var count = this.getCookie(COOKIE_TRW_CLICK_PER_VISIT);
                if (count != null) {
                    var splitCount = count.split(SESSION_ID_CLICKS_SEPERATOR);
                    var newCount = parseInt(splitCount[1]);
                    if (newCount < 1 || isNaN(newCount)) {
                        newCount = 1
                    }
                    newCount = newCount + 1;
                    sessionArr[1] = newCount
                } else {
                    firstClick = true;
                    sessionArr[1] = 1
                }
            }
            if (isNaN(sessionArr[1]) || sessionArr[1] < 1) {
                sessionArr[1] = "1"
            }
            if (isEvent && firstClick && !eventFirstClick) {
                eventFirstClick = true
            } else {
                eventFirstClick = false
            }
            return sessionArr
        };
        var addVC = function(visitorId) {
            var visitorArr = visitorId.split(COOKIE_VAL_SEPERATOR);
            if (visitorArr.length > 1) {
                if (this.isNewSession()) {
                    var visitCount = parseInt(visitorArr[1], 10);
                    if (visitCount < 1 || isNaN(visitCount)) {
                        visitCount = 0
                    }
                    visitorArr[1] = visitCount + 1
                }
            } else {
                var count = this.getCookie(COOKIE_TRW_COUNT);
                if (count != null) {
                    visitorArr[1] = parseInt(count, 10);
                    if (visitorArr[1] < 1 || isNaN(visitorArr[1])) {
                        visitorArr[1] = 1
                    }
                    if (this.isNewSession()) {
                        visitorArr[1] = parseInt(visitorArr[1], 10) + 1
                    }
                } else {
                    visitorArr[1] = 1
                }
            }
            var visitCount = parseInt(visitorArr[1], 10);
            if (visitCount < 1 || isNaN(visitCount)) {
                visitorArr[1] = "1"
            }
            return visitorArr
        };
        function copyVcCookie() {
            var visitorId = AITag.getCookie(COOKIE_TRW_VISITOR_UID);
            if (visitorId != null) {
                var visitorArr = visitorId.split(COOKIE_VAL_SEPERATOR);
                var count = AITag.getCookie(COOKIE_TRW_COUNT);
                if (count != null) {
                    visitorArr[1] = parseInt(count);
                    var visitor = visitorArr[0] + COOKIE_VAL_SEPERATOR + visitorArr[1];
                    var known = AITag.getCookie(COOKIE_TRW_IE_EMAIL);
                    if (known != null) {
                        visitor += COOKIE_VAL_SEPERATOR + 1
                    }
                    AITag.setCookie(COOKIE_TRW_VISITOR_UID, visitor, exp_2_year)
                }
            }
        }
        function copyCpvCookie() {
            if (!AITag.isNewSession()) {
                var sessionArr = sessionId.split(COOKIE_VAL_SEPERATOR);
                var count = AITag.getCookie(COOKIE_TRW_CLICK_PER_VISIT);
                if (count != null) {
                    var splitCount = count.split(SESSION_ID_CLICKS_SEPERATOR);
                    var newCount = parseInt(splitCount[1]);
                    sessionArr[1] = newCount;
                    AITag.setCookie(COOKIE_TRW_SESSION_ID, sessionArr[0] + COOKIE_VAL_SEPERATOR + sessionArr[1], exp_30_minutes)
                }
            }
        }
        this.isNewVisitor = function() {
            var visitorId = this.getCookie(COOKIE_TRW_VISITOR_UID);
            if (visitorId == null) {
                newVisitor = true
            }
            return newVisitor
        }
        ;
        this.addVisitorId = function(isKnown) {
            var visitorId = this.getCookie(COOKIE_TRW_VISITOR_UID);
            if (visitorId == null) {
                newVisitor = true;
                visitorId = generateUUID()
            }
            var visitorArr = addVC.call(this, visitorId);
            var visitor = visitorArr[0] + COOKIE_VAL_SEPERATOR + visitorArr[1];
            if (visitorArr.length > 2 || isKnown) {
                visitor += ":1"
            }
            if (this.isBrowserMitigationSupported() && this.getConfig("enableITPMitigation") && this.fetchLPDomain()) {
                this.setMitigationUserId(visitor)
            } else {
                this.deleteCookie(COOKIE_TRW_VISITOR_UID);
                this.setCookie(COOKIE_TRW_VISITOR_UID, visitor, exp_2_year)
            }
        }
        ;
        this.extractMunchkinId = function() {
            var munchkinId = null;
            var mktoTrk = this.getCookie(COOKIE_MARKETO_TOKEN);
            if (typeof mktoTrk != "undefined" && mktoTrk != null && mktoTrk != "") {
                munchkinId = mktoTrk.split("id:").pop().split("&")[0]
            }
            return munchkinId
        }
        ;
        this.fetchLPDomain = function() {
            var trackingDomain = getHostDomain(window.location.hostname, AITag.getConfig("domainLevel"), false)
              , munchkinId = this.extractMunchkinId();
            if (munchkinId != null) {
                var lpDomain = localStorageApi.getValue(MKTO_LP_DOMAIN + munchkinId + "_" + trackingDomain);
                var lpDomainSecure = localStorageApi.getValue(MKTO_LP_SECURE + munchkinId);
                if (lpDomain === undefined || lpDomain === null || lpDomainSecure === undefined || lpDomainSecure === null) {
                    var getLpDomainUrl = munchkinId + ".mktoutil.com/mktoutil/lpDomain?_mchId=" + munchkinId + "&_mchTd=" + trackingDomain;
                    addDynamicScript(getLpDomainUrl, function(domainData) {
                        if (domainData !== {} && domainData !== undefined) {
                            localStorageApi.store(MKTO_LP_DOMAIN + munchkinId + "_" + trackingDomain, domainData.domain);
                            localStorageApi.store(MKTO_LP_SECURE + munchkinId, domainData.isSecure)
                        }
                    }, false, false);
                    return true
                } else {
                    return true
                }
            }
            return false
        }
        ;
        this.setMitigationUserId = function(visitor) {
            var trackingDomain = getHostDomain(window.location.hostname, AITag.getConfig("domainLevel"), false);
            var munchkinId = this.extractMunchkinId();
            var lpDomain = localStorageApi.getValue(MKTO_LP_DOMAIN + munchkinId + "_" + trackingDomain);
            var lpDomainSecure = localStorageApi.getValue(MKTO_LP_SECURE + munchkinId);
            if (lpDomainSecure) {
                var url = lpDomain + "/getRtpCookie?aid=" + AITag.getConfig("accountId") + "&domain=" + trackingDomain;
                addDynamicScript(url)
            } else {
                this.setCookie(COOKIE_TRW_VISITOR_UID, visitor, exp_2_year)
            }
        }
        ;
        this.isBrowserMitigationSupported = function() {
            var browserAgent = window.navigator.userAgent.toLowerCase();
            var isSafari = browserAgent.indexOf("safari") > -1;
            if (isSafari) {
                browserAgent = / version\/([0-9]+.[0-9]+)/.exec(browserAgent);
                return isSafari && browserAgent && parseFloat(browserAgent[1]) >= 12.1
            }
            return false
        }
        ;
        var addToTimelineImp = function(reactionId) {
            if (reactionId !== undefined && reactionId != null) {
                var reactions = AITag.getCookie(COOKIE_TRW_IE_CPN);
                if (reactions !== undefined && reactions != null && reactions.indexOf(reactionId) == -1) {
                    AITag.deleteCookie(COOKIE_TRW_IE_CPN);
                    reactions += COOKIE_VAL_SEPERATOR + reactionId + IMPRESSION_SUFFIX
                } else {
                    reactions = reactionId + IMPRESSION_SUFFIX
                }
                AITag.setCookie(COOKIE_TRW_IE_CPN, reactions, exp_2_year)
            }
        };
        var addToTimelineConv = function(reactionId) {
            if (reactionId !== undefined && reactionId != null) {
                var reactions = AITag.getCookie(COOKIE_TRW_IE_CPN);
                if (reactions !== undefined && reactions != null) {
                    AITag.deleteCookie(COOKIE_TRW_IE_CPN);
                    if (reactions.indexOf(reactionId) == -1) {
                        reactions += COOKIE_VAL_SEPERATOR + reactionId + CONVERSION_SUFFIX
                    } else {
                        reactions = reactions.replace(reactionId + IMPRESSION_SUFFIX, reactionId + CONVERSION_SUFFIX)
                    }
                } else {
                    reactions = reactionId + CONVERSION_SUFFIX
                }
                AITag.setCookie(COOKIE_TRW_IE_CPN, reactions, exp_2_year)
            }
        };
        this.disableTag = function(iframe) {
            config.disableClick = true;
            config.disablePoll = true;
            config.googleAnalytics = false;
            config.enableUAnalytics = false;
            config.enableSiteCatalyst = false;
            config.trackAsset = false;
            config.discoverAsset = false;
            config.enableRecommendationBar = false;
            if (window.rtp) {
                window.rtp.d = true
            }
            if (iframe) {
                AITag.updateInitialConfig();
                var email = getEmail();
                if (email !== undefined && email != null && email != "") {
                    sendLead(email)
                }
            }
        }
        ;
        this.previewCallback = function(msg, isBindConversion, onSite) {
            if (config === undefined || config == null) {
                config = {}
            }
            if (!window.AIConfig) {
                window.AIConfig = {}
            }
            if (!window.IeraPreview) {
                window.IeraPreview = {}
            }
            previewMode = true;
            localStorageApi.remove(WIDGET_OPEN);
            config.stratup = false;
            config.disableClick = true;
            config.disablePoll = true;
            config.googleAnalytics = false;
            config.enableUAnalytics = false;
            config.enableSiteCatalyst = false;
            config.trackAsset = false;
            config.discoverAsset = false;
            config.enableRecommendationBar = false;
            config.captureLeads = false;
            AITag.updateInitialConfig.apply(AITag);
            if (typeof window.IeraPreview.previewShowed != "undefined" && window.IeraPreview.previewShowed) {
                return
            } else {
                window.IeraPreview.previewShowed = true
            }
            AITag.loadCss.apply(AITag);
            var afterFullyLoaded = function() {
                this.fullyLoaded = true;
                runCampaignQueue()
            };
            if (!onSite) {
                msg = decodeURIComponent(msg)
            }
            var msgObj = JSON.parse(msg);
            AITag.msgCallback(msgObj, null, msgObj.isBindConversion);
            AITag.loadJQuery(afterFullyLoaded)
        }
        ;
        this.pollMsg = function(callback, showCampaign) {
            if (!checkIESupported()) {
                return
            }
            if (callback) {
                this.campaignCallback = callback
            }
            if (showCampaign) {
                this.showCampaign = showCampaign
            }
            AITag.pollIntervalCounter++;
            var url = AITag.getConfig("gwUrlPrefix") + "/msg?a=2&sid=" + getSessionId() + "&aid=" + AITag.getConfig("accountId");
            var mktoTrk = this.getCookie(COOKIE_MARKETO_TOKEN);
            if (typeof mktoTrk != "undefined" && mktoTrk != null && mktoTrk != "") {
                url += "&ma=" + encodeURIComponent(mktoTrk)
            }
            url = addToParams(url, {
                viewedTypes: encodeURIComponent(viewedTypes.join())
            });
            msgCallTime = new Date().getTime();
            if (AITag.getConfig("pollingPerPage") <= AITag.pollIntervalCounter) {
                addDynamicScript(url + "&" + Math.random() + "&rts=" + msgCallTime, AITag.showInzoneElements)
            } else {
                addDynamicScript(url + "&" + Math.random() + "&rts=" + msgCallTime)
            }
            logMessage("msg called. Time since view: " + (msgCallTime - viewTime))
        }
        ;
        var addCampaignToRTPGlobal = function(reactionId, type) {
            var rtpGlobal = window.rtp;
            if (rtpGlobal && rtpGlobal.userContext) {
                var campaignList;
                if (type == 0) {
                    campaignList = rtpGlobal.userContext.viewedCampaigns
                } else {
                    if (type == 1) {
                        campaignList = rtpGlobal.userContext.clickedCampaigns
                    }
                }
                if (!campaignList) {
                    campaignList = []
                }
                reactionId = reactionId.toString();
                if (campaignList.indexOf(reactionId) == -1) {
                    campaignList.push(reactionId)
                }
                if (type == 0) {
                    window.rtp.userContext.viewedCampaigns = campaignList
                } else {
                    if (type == 1) {
                        window.rtp.userContext.clickedCampaigns = campaignList
                    }
                }
            }
        };
        var runCounter = 0;
        function runAsyncQueue() {
            if (nonjQueryCampaignQueue && nonjQueryCampaignQueue.length > 0) {
                logMessageTime("try to run campaigns that doesn't require jQuery, intervarl number: " + runCounter + ". Time since DOM loading: ", new Date().getTime());
                runCounter++;
                inzoneCampginTimoutSet = false;
                var queueLength = nonjQueryCampaignQueue.length;
                for (var i = 0; i < queueLength; i++) {
                    var param = nonjQueryCampaignQueue.shift();
                    var method = param.shift();
                    try {
                        AITag[method].apply(AITag, param)
                    } catch (e) {}
                }
            }
        }
        this.runAsyncQueue = function() {
            runAsyncQueue()
        }
        ;
        function executeScript(scriptElement, callback) {
            var syncScriptUrl = null;
            var async = true;
            if (scriptElement.src !== undefined && scriptElement.src != "") {
                async = scriptElement.async !== undefined ? scriptElement.async : false;
                addDynamicScript(scriptElement.src, callback, false, async);
                syncScriptUrl = scriptElement.src
            } else {
                (function(script) {
                    eval.call(window, script)
                }
                )((scriptElement.text || scriptElement.textContent || scriptElement.innerHTML || ""));
                if (typeof callback === "function") {
                    callback.apply(AITag)
                }
            }
            return syncScriptUrl
        }
        function executeInnerScripts(campaign, isBindConversion, runBindOnload) {
            var div = document.createElement("div");
            div.innerHTML = "_" + campaign.content;
            var nodes = div.childNodes;
            div = null;
            var node;
            var syncScriptUrl = null;
            var pendingScripts = [];
            for (var i = 0; nodes[i] != null; i++) {
                node = nodes[i];
                if (typeof node.getElementsByTagName !== "undefined") {
                    var scripts = node.getElementsByTagName("script");
                    if (scripts !== undefined && scripts != null) {
                        for (var j = 0; j < scripts.length; j++) {
                            pendingScripts.push(scripts[j])
                        }
                    }
                }
                if (node.nodeName.toLowerCase() == "script") {
                    pendingScripts.push(node)
                }
            }
            var runNextScript = function(pendingScripts, scriptIndex) {
                return function() {
                    if (scriptIndex < pendingScripts.length) {
                        executeScript(pendingScripts[scriptIndex], runNextScript(pendingScripts, scriptIndex + 1))
                    } else {
                        AITag.afterFormsServed(campaign, isBindConversion, runBindOnload)
                    }
                }
            };
            if (pendingScripts.length > 0) {
                executeScript(pendingScripts[0], runNextScript(pendingScripts, 1))
            }
            return syncScriptUrl != null
        }
        this.afterFormsServed = function(campaign, isBindConversion, runBindOnload) {
            if (campaignForms2.indexOf(campaign.reactionId) > -1 && window.MktoForms2) {
                if (runBindOnload && (typeof isBindConversion === "undefined" || !isBindConversion)) {
                    AITag.bindConversion(campaign.reactionId, campaign.requestId, campaign.div)
                }
                MktoForms2.whenReady(function(form) {
                    if (allowedForms2.indexOf("" + form.getId()) > -1) {
                        if (campaign.div && campaign.div.showDialogCallback) {
                            campaign.div.showDialogCallback()
                        }
                    }
                })
            }
        }
        ;
        var presentedCampaigns = {}
          , hiddenElementsShown = false;
        function pushCampaignData(msg, requestId, isBindConversion) {
            try {
                if (isBindConversion === undefined && msg.sticky) {
                    campaignStorage.exc(["storeCampaign", msg, requestId])
                }
            } catch (e) {
                logMessage("failed to store sticky campaign")
            }
            if (isAnalyticsEnabled() && isBindConversion != false) {
                try {
                    iiq.push(["pushCampaign", msg.reactionId, "Impression", msg.reactionName, msg.segmentName, getSessionId(), msg.campaignLabels])
                } catch (e) {}
            }
            try {
                if (AITag.getConfig("userContextAPI")) {
                    var visitCount = getVisitCount();
                    if (visitCount != null) {
                        rtp("add", "toCmpTl", visitCount, msg.reactionId, 0)
                    }
                    addCampaignToRTPGlobal(msg.reactionId, 0)
                }
            } catch (e) {}
            presentedCampaigns[msg.reactionId] = true;
            if (!hiddenElementsShown) {
                AITag.showInzoneElements()
            }
        }
        var activeCampaignMap = {}
          , inzoneCampaignTimeout = null
          , inzoneCampginTimoutSet = false
          , MAX_LOADING_TIME = 5000
          , campaignForms2 = []
          , allowedForms2 = [];
        function injectInzoneCampaign(msg, requestId, isBindConversion) {
            var replaced = false;
            var targetDiv = msg.targetDiv;
            logMessage("trying to inject inzone campaign " + targetDiv);
            var element = document.getElementById(targetDiv);
            if (element && element.id == targetDiv) {
                executeInnerScripts(msg, isBindConversion, false);
                element.innerHTML = msg.content;
                if (typeof isBindConversion === "undefined" || !isBindConversion) {
                    cpq.push(["bindConversionListener", targetDiv, msg.reactionId, requestId])
                }
                pushCampaignData(msg, requestId, isBindConversion);
                logMessageTime("changed div " + targetDiv + " after: ", new Date().getTime());
                replaced = true
            } else {
                if (!activeCampaignMap[msg.reactionId]) {
                    activeCampaignMap[msg.reactionId] = [msg, requestId, isBindConversion];
                    injectScript("body", "LAST", "(function(){AITag.activateCampaign(" + msg.reactionId + ");})()")
                }
            }
            return replaced
        }
        this.activateCampaign = function(reactionId) {
            var campaignData = activeCampaignMap[reactionId];
            if (campaignData) {
                logMessage("Try to inject with script in body");
                AITag.msgCallback.apply(AITag, campaignData)
            }
        }
        ;
        this.msgCallback = function(msg, requestId, isBindConversion, storedCampaign) {
            try {
                if (AITag.campaignCallback && typeof AITag.campaignCallback == "function") {
                    AITag.campaignCallback(msg, requestId, isBindConversion);
                    if (!AITag.showCampaign) {
                        return
                    }
                }
            } catch (e) {}
            if (presentedCampaigns[msg.reactionId] > -1) {
                logMessage(msg.reactionId + " is already presented on page, can't show twice");
                return
            }
            msg.requestId = requestId;
            var targetDiv = msg.targetDiv;
            if (!previewMode && currentPageAllowedCampaigns.indexOf(msg.reactionId) == -1 && allPagesAllowedCampaigs.indexOf(msg.reactionId) == -1) {
                logMessage("Campaign " + msg.reactionName + " not allowed on current page");
                return
            }
            if (storedCampaign) {
                switch (msg.viewType) {
                case "WIDGET":
                case "DIALOG":
                    viewedTypes.push("dialog/widget");
                    break;
                case "IN_ZONE":
                    viewedTypes.push(msg.viewType + msg.targetDiv);
                    break;
                case "REDIRECT":
                    viewedTypes.push("$$$REDIRECT$$$");
                default:
                    logMessage("unsupported type: " + msg.viewType);
                    break
                }
            }
            if (msg.content.indexOf("MktoForms2") > -1) {
                campaignForms2.push(msg.reactionId)
            }
            if (!this.fullyLoaded) {
                if (targetDiv && targetDiv != "" && targetDiv != "widget" && (msg.useEffect != true && msg.useEffect != "true")) {
                    if (isReady) {
                        injectInzoneCampaign(msg, requestId, isBindConversion)
                    } else {
                        var isInjected = injectInzoneCampaign(msg, requestId, isBindConversion);
                        if (!isInjected) {
                            logMessage("Inzone campaign not injected, inserting into queue");
                            nonjQueryCampaignQueue.push(["msgCallback", msg, requestId, isBindConversion]);
                            (!inzoneCampginTimoutSet && (new Date().getTime() - scriptExecutionTime < MAX_LOADING_TIME) && AITag.getConfig("campaignInjectionInterval") > 0) ? (inzoneCampaignTimeout = setTimeout(runAsyncQueue, runCounter == 0 ? 10 : AITag.getConfig("campaignInjectionInterval")),
                            inzoneCampginTimoutSet = true) : false
                        }
                    }
                } else {
                    cpq.push(["msgCallback", msg, requestId, isBindConversion])
                }
                return
            }
            logMessageTime("displaying campaign " + msg.reactionName + " with regular flow. The time since DOM loading: ", new Date().getTime());
            pushCampaignData(msg, requestId, isBindConversion);
            var redirect = msg.redirect;
            if (redirect == true || redirect == "true") {
                var uri = msg.redirectUrl;
                if (uri.indexOf("http://") == -1 && uri.indexOf("https://") == -1) {
                    uri = "http://" + uri
                }
                window.location = uri
            } else {
                var $div = AITag.getReactionDiv(msg);
                AITag.putReactionInDiv.call(AITag, $div, msg.content, msg.reactionId, requestId, msg.targetDiv, msg.useEffect, msg.effectType, isBindConversion);
                if (msg.content.indexOf("MktoForms2") > -1) {
                    var forms = $div.find("form");
                    var id = null;
                    AITag.jQuery(forms).each(function() {
                        id = AITag.jQuery(this).attr("id");
                        id = id.split("_");
                        allowedForms2.push(id[1])
                    })
                }
                msg.div = $div;
                executeInnerScripts(msg, isBindConversion, true);
                if (msg.content.indexOf("MktoForms2") == -1) {
                    if (typeof $div != "undefined") {
                        if ($div.showDialogCallback) {
                            $div.showDialogCallback()
                        }
                    }
                }
            }
        }
        ;
        this.getReactionDiv = function(msg) {
            var targetDiv = msg.targetDiv;
            if (targetDiv == "widget") {
                var widgetContentId = InsighteraWidget.getContentId();
                if (!InsighteraWidget.isInitilized()) {
                    var widgetOpen = true;
                    if (ITLocalStorageAPI.supports()) {
                        var widgetOpenState = ITLocalStorageAPI.getValue(WIDGET_OPEN);
                        if (typeof widgetOpenState != "undefined" && widgetOpenState != null) {
                            widgetOpen = (widgetOpenState == "true")
                        } else {
                            if (msg.widgetMinimize) {
                                widgetOpen = false
                            }
                        }
                    }
                    InsighteraWidget.init({
                        widgetPositionTop: ((msg.widgetVerticalPosition === undefined) ? undefined : msg.widgetVerticalPosition + "%"),
                        widgetPositionSide: msg.widgetSidePosition,
                        tabColor: msg.widgetBackgroundColor,
                        contentColor: msg.widgetBackgroundColor,
                        showOnType: msg.displayDialogOnType,
                        widgetButtonType: msg.widgetButtonType,
                        widgetButtonColor: msg.widgetButtonColor,
                        widgetButtonSVGTemplate: msg.widgetButtonSVGTemplate,
                        widgetButtonOpenImgUrl: msg.widgetButtonOpenImgUrl,
                        widgetButtonCloseImgUrl: msg.widgetButtonCloseImgUrl,
                        widgetButtonText: msg.widgetButtonText,
                        widgetButtonTextColor: msg.widgetButtonTextColor,
                        widgetButtonTextSize: msg.widgetButtonTextSize,
                        widgetButtonTextAlignType: msg.widgetButtonTextAlignType,
                        widgetOpenKey: WIDGET_OPEN,
                        widgetOpen: widgetOpen,
                        dialogAnimationInEffectType: msg.dialogAnimationInEffectType,
                        dialogAnimationOutEffectType: msg.dialogAnimationOutEffectType,
                        dialogAnimationInDuration: msg.dialogAnimationInDuration,
                        dialogAnimationOutDuration: msg.dialogAnimationOutDuration,
                        widgetMinimize: msg.widgetMinimize,
                        widgetHide: true,
                        widgetAutoWidth: msg.autoWidth,
                        widgetFitWidth: msg.fitWidth,
                        widgetWidth: msg.width,
                        widgetHeight: msg.height
                    })
                }
                $div = AITag.jQuery("#" + widgetContentId);
                $div.showDialogCallback = function() {
                    showWidget(msg)
                }
                ;
                return $div
            } else {
                if (targetDiv && targetDiv != "") {
                    if (targetDiv == "redirect") {
                        var uri = AITag.jQuery(msg.content).text().trim();
                        if (uri.indexOf("http://") == -1 && uri.indexOf("https://") == -1) {
                            uri = "http://" + uri
                        }
                        window.location = uri
                    }
                    return AITag.jQuery("#" + targetDiv)
                } else {
                    var resizable = msg.resizable != undefined && (msg.resizable == true || msg.resizable == "true");
                    var modal = msg.modal != undefined && (msg.modal == true || msg.modal == "true");
                    var height = msg.height;
                    var width = msg.width;
                    var dialogShowDuration = msg.dialogOpenDuration;
                    var headerColor = msg.dialogHeaderBackground;
                    var contentBackgroundColor = msg.dialogContentBackground;
                    var title = msg.dialogTitle;
                    return showDialog(msg, msg.dialogType, msg.hPos, msg.vPos, title, modal, resizable, height, width, dialogShowDuration, headerColor, contentBackgroundColor, title, msg.autoWidth, msg.fitWidth, msg.dialogOpenDelay)
                }
            }
        }
        ;
        var showWidget = function(msg) {
            if (isWidgetOrDialogPresented) {
                return
            }
            isWidgetOrDialogPresented = true;
            var displayOn = msg.displayDialogOnType;
            if (displayOn) {
                switch (displayOn) {
                case "SCROLL":
                    var scrollWhen = msg.displayWhenScrollType;
                    if (scrollWhen) {
                        switch (scrollWhen) {
                        case "BELOW_FOLD":
                            var pxLimit = window.innerHeight;
                            window.addEventListener("scroll", function belowFold(e) {
                                catchMainScrollingElementForWidget(e, belowFold, pxLimit, true)
                            }, true);
                            break;
                        case "PIXEL":
                            var pxLimit = msg.scrollPixel;
                            window.addEventListener("scroll", function pixelShow(e) {
                                catchMainScrollingElementForWidget(e, pixelShow, pxLimit, false)
                            }, true);
                            break;
                        case "PERCENT":
                            var scrollPercent = msg.scrollPercent;
                            window.addEventListener("scroll", function percentShow(e) {
                                var scrollHeight = document.querySelector("html").scrollHeight;
                                var pxLimit = scrollHeight * (scrollPercent / 100);
                                catchMainScrollingElementForWidget(e, percentShow, pxLimit, false)
                            }, true);
                            break;
                        default:
                            console.log("Sorry, we are out of " + scrollWhen + ".");
                            break
                        }
                    }
                    break;
                case "DELAY":
                    var widgetOpenDelay = ((msg.displayDialogOnType != "DELAY" || msg.dialogOpenDelay === undefined || msg.dialogOpenDelay == null) ? 0 : msg.dialogOpenDelay);
                    setTimeout(function() {
                        InsighteraWidget.restore()
                    }, widgetOpenDelay * 1000);
                    break;
                case "PAGELOAD":
                default:
                    InsighteraWidget.restore();
                    break
                }
            }
        };
        function catchMainScrollingElementForWidget(e, callee, pxLimit, hideOnTop) {
            var target = e.target;
            if (target !== undefined && ((target == document) || (target.getBoundingClientRect() !== undefined && target.getBoundingClientRect().top == 0))) {
                window.removeEventListener("scroll", callee, true);
                target.addEventListener("scroll", function listenToScroll(e) {
                    openWidgetOnScroll(pxLimit, hideOnTop, listenToScroll, target)
                })
            }
        }
        function openWidgetOnScroll(pxLimit, hideOnTop, callee, target) {
            var scrollContainer = (target == document ? target.scrollingElement : target);
            if (getInternetExplorerVersion() > -1 && target == document) {
                scrollContainer = target.documentElement ? target.documentElement : target.body
            }
            if (scrollContainer) {
                var scrollTop = scrollContainer.scrollTop;
                if (scrollTop && scrollTop >= pxLimit) {
                    InsighteraWidget.restore();
                    target.removeEventListener("scroll", callee, false);
                    if (hideOnTop && hideOnTop == true) {
                        target.addEventListener("scroll", function hideOnTop() {
                            hideWidgetOnScrollTop(pxLimit, hideOnTop, target)
                        })
                    }
                }
            }
        }
        function hideWidgetOnScrollTop(topPixel, callee, target) {
            var scrollContainer = (target == document ? target.scrollingElement : target);
            if (getInternetExplorerVersion() > -1 && target == document) {
                scrollContainer = target.documentElement ? target.documentElement : target.body
            }
            if (scrollContainer) {
                var scrollTop = scrollContainer.scrollTop;
                if (scrollTop && scrollTop <= topPixel) {
                    InsighteraWidget.hide();
                    target.removeEventListener("scroll", callee, false)
                }
            }
        }
        var showDialog = function(msg, dialogType, hPos, vPos, title, isModal, isResizable, height, width, dialogShowDuration, headerColor, contentBackgroundColor, dialogTitle, autoWidth, fitWidth, dialogOpenDelay) {
            if (isDialogPresented) {
                return
            }
            if (isWidgetOrDialogPresented) {
                return
            }
            isDialogPresented = true;
            isWidgetOrDialogPresented = true;
            if (AITag.getConfig("title") != null && AITag.getConfig("title") != "") {
                title = AITag.getConfig("title")
            }
            if (typeof dialogTitle == "undefined" || dialogTitle == null) {
                dialogTitle = ""
            }
            var $div = addDynamicDiv(title);
            var hideSide = "left";
            if (!(/^\d+$/.test(hPos))) {
                hideSide = hPos
            } else {
                var windowWidth = AITag.jQuery(window).width();
                if (hPos * 1 > windowWidth / 2) {
                    hideSide = "right"
                }
            }
            if (vPos == "middle") {
                vPos = "center"
            }
            var hAt = hPos
              , vAt = vPos;
            if (/^\d+$/.test(hAt)) {
                hAt = "left+" + hAt
            }
            if (/^\d+$/.test(vAt)) {
                vAt = "top+" + vAt
            }
            var dialogArgs = AITag.getConfig("dialogArgs");
            if (dialogArgs == null) {
                dialogArgs = {
                    autoOpen: false,
                    resizable: isResizable,
                    title: dialogTitle,
                    modal: isModal,
                    minHeight: 50,
                    position: {
                        at: hAt + " " + vAt,
                        of: window
                    },
                    dialogClass: "insightera"
                }
            }
            var showEffect = "fadeIn";
            var showDirect = "";
            var showDuration = 1000;
            var hideEffect = "drop";
            var hideDirect = "right";
            var hideDuration = 1000;
            var entryEffect = msg.dialogAnimationInEffectType;
            if (entryEffect && entryEffect != "NO_EFFECT") {
                showEffect = entryEffect.toLowerCase();
                if (msg.dialogAnimationInDirectionType) {
                    showDirect = getDirection(msg.dialogAnimationInDirectionType, "show")
                }
                if (msg.dialogAnimationInDuration) {
                    showDuration = parseInt(msg.dialogAnimationInDuration)
                }
                dialogArgs.show = {
                    effect: showEffect,
                    direction: showDirect,
                    duration: showDuration
                }
            }
            var exitEffect = msg.dialogAnimationOutEffectType;
            if (exitEffect && exitEffect != "NO_EFFECT") {
                hideEffect = exitEffect.toLowerCase();
                if (msg.dialogAnimationOutDirectionType) {
                    hideDirect = getDirection(msg.dialogAnimationOutDirectionType, "hide")
                }
                if (msg.dialogAnimationOutDuration) {
                    hideDuration = parseInt(msg.dialogAnimationOutDuration)
                }
                dialogArgs.hide = {
                    effect: hideEffect,
                    direction: hideDirect,
                    duration: hideDuration
                }
            }
            if (height != undefined) {
                dialogArgs.height = height
            }
            if (autoWidth) {
                dialogArgs.width = "auto"
            } else {
                if (fitWidth) {
                    var winW = (window.innerWidth > 0) ? window.innerWidth : screen.width;
                    var winH = (window.innerHeight > 0) ? window.innerHeight : screen.height;
                    dialogArgs.height = winH;
                    dialogArgs.width = winW
                } else {
                    if (width != undefined) {
                        dialogArgs.width = width
                    }
                }
            }
            $div.showDialogCallback = function() {
                var dialog = $div.dialog(dialogArgs);
                dialog.parent(".ui-dialog").addClass("insightera-dialog");
                var parentNode = dialog[0].parentNode;
                parentNode.vPos = vPos;
                parentNode.hPos = hPos;
                parentNode.showEffect = showEffect;
                var closeBtnType = msg.customCloseButtonType;
                var customBtnPos = msg.customCloseBtnPosition;
                var imageUrl = msg.customCloseBtnImgUrl;
                var element;
                if (dialogType == "MODERN_TRIM") {
                    dialog.parent(".ui-dialog").addClass("insightera-dialog-modern-trim");
                    dialog.parent(".ui-dialog").find(".ui-widget-header").removeClass("ui-corner-all");
                    if (dialogTitle == "" || dialogTitle === undefined || dialogTitle == null) {
                        dialog.parent(".ui-dialog").find(".ui-dialog-title").html("")
                    }
                    imageUrl = (imageUrl == undefined ? cdnSrc + AITag.getConfig("dialogCloseButton") : imageUrl);
                    dialog.parent(".ui-dialog").addClass("notTransparent");
                    element = dialog.parent(".ui-dialog").find(".ui-widget-header .ui-dialog-titlebar-close")
                } else {
                    if (dialogType == "TRANSPARENT") {
                        dialog.parent(".ui-dialog").addClass("insightera-dialog-transparent");
                        element = dialog.parent(".ui-dialog").find(".ui-widget-header .ui-dialog-titlebar-close");
                        imageUrl = (imageUrl == undefined ? cdnSrc + AITag.getConfig("dialogTransparentButton") : imageUrl)
                    } else {
                        if (dialogType == "MODERN_TRIM_2") {
                            dialog.parent(".ui-dialog").addClass("insightera-dialog-modern-trim-2");
                            imageUrl = (imageUrl == undefined ? cdnSrc + AITag.getConfig("moderntrim2CloseButton") : imageUrl);
                            element = dialog.parent(".ui-dialog").find(".ui-widget-header .ui-dialog-titlebar-close")
                        } else {
                            if (dialogType == "BASIC") {
                                dialog.parent(".ui-dialog").addClass("insightera-dialog-basic");
                                element = dialog.parent(".ui-dialog").find(".ui-widget-header .ui-dialog-titlebar-close");
                                element.addClass("close-button-top-right")
                            }
                        }
                    }
                }
                if (dialogType != "BASIC") {
                    if (closeBtnType && closeBtnType == "IMAGE") {
                        setCloseButtonPositionAndImage(customBtnPos, imageUrl, element)
                    } else {
                        if (closeBtnType && closeBtnType == "COLOR") {
                            var svgUrl = cdnSrc + "/" + msg.customCloseBtnSVGTemplate;
                            var fillColor = msg.customCloseBtnFillColor;
                            var svgBody;
                            makeGetRequest(svgUrl, function(response) {
                                svgBody = response;
                                var regex = new RegExp("#000000","g");
                                if (svgBody && fillColor) {
                                    svgBody = svgBody.replace(regex, fillColor)
                                }
                                setCloseButtonPositionAndSVG(customBtnPos, svgBody, element)
                            })
                        }
                    }
                }
                try {
                    if (!AITag.getConfig("dialogCloseOnlyOnActualClose")) {
                        AITag.jQuery("body").bind("click", function(e) {
                            if (dialog.dialog("isOpen") && !AITag.jQuery(e.target).is(".ui-dialog, a") && !AITag.jQuery(e.target).closest(".ui-dialog").length) {
                                dialog.dialog("close")
                            }
                        })
                    }
                } catch (e) {}
                var delay = 0;
                var duration = 0;
                if (typeof dialogShowDuration != "undefined" && dialogShowDuration != null) {
                    duration = dialogShowDuration
                } else {
                    duration = AITag.getConfig("dialogCloseTime")
                }
                var displayOn = msg.displayDialogOnType;
                if (displayOn) {
                    switch (displayOn) {
                    case "SCROLL":
                        var scrollWhen = msg.displayWhenScrollType;
                        if (scrollWhen) {
                            switch (scrollWhen) {
                            case "BELOW_FOLD":
                                var pxLimit = window.innerHeight;
                                window.addEventListener("scroll", function belowFold(e) {
                                    catchMainScrollingElementForDialog(e, belowFold, dialog, pxLimit, true)
                                }, true);
                                break;
                            case "PIXEL":
                                var pxLimit = msg.scrollPixel;
                                window.addEventListener("scroll", function pixelShow(e) {
                                    catchMainScrollingElementForDialog(e, pixelShow, dialog, pxLimit, false)
                                }, true);
                                break;
                            case "PERCENT":
                                var scrollPercent = msg.scrollPercent;
                                window.addEventListener("scroll", function percentShow(e) {
                                    var scrollHeight = document.querySelector("html").scrollHeight;
                                    var pxLimit = scrollHeight * (scrollPercent / 100);
                                    catchMainScrollingElementForDialog(e, percentShow, dialog, pxLimit, false)
                                }, true);
                                break;
                            default:
                                console.log("Sorry, we are out of " + scrollWhen + ".");
                                break
                            }
                        }
                        break;
                    case "EXIT":
                        var htmlDoc = document.querySelector("html");
                        htmlDoc.addEventListener("mouseenter", onMouseEnter);
                        htmlDoc.addEventListener("mouseleave", onMouseLeave);
                        break;
                    case "DELAY":
                        if (dialogOpenDelay && dialogOpenDelay > 0) {
                            delay = (dialogOpenDelay * 1000) - (new Date().getTime() - scriptExecutionTime);
                            delay = delay > 0 ? delay : 0;
                            window.setTimeout(function() {
                                if (!(dialog.is(":visible"))) {
                                    dialog.dialog("open")
                                }
                                setDialogProperties()
                            }, delay)
                        } else {
                            dialog.dialog("open");
                            setDialogFocus()
                        }
                        break;
                    case "PAGELOAD":
                    default:
                        dialog.dialog("open");
                        setDialogProperties();
                        break
                    }
                } else {
                    dialog.dialog("open");
                    setDialogFocus()
                }
                if (typeof headerColor != "undefined" && headerColor != null && headerColor != "#CCCCCC" && headerColor != "#ccc" && headerColor != "" && dialogType != "TRANSPARENT") {
                    AITag.jQuery(".insightera .ui-widget-header").css("background", headerColor)
                }
                if (typeof contentBackgroundColor != "undefined" && contentBackgroundColor != null && contentBackgroundColor != "#FFFFFF" && contentBackgroundColor != "#fff" && contentBackgroundColor != "" && dialogType != "TRANSPARENT") {
                    AITag.jQuery(".insightera #trwDialog").css("background", contentBackgroundColor)
                }
                if (!AITag.jQuery.support.opacity) {
                    AITag.jQuery(".ui-widget-overlay").css("filter", "alpha(opacity=30)")
                }
                AITag.jQuery(".ui-widget-overlay").css("z-index", "9998");
                function onMouseEnter(e) {
                    window.didJustEnter = true;
                    requestAnimationFrame(function() {
                        window.didJustEnter = false
                    })
                }
                function onMouseLeave(e) {
                    if (!window.didJustEnter) {
                        var htmlDoc = document.querySelector("html");
                        onPageExit(dialog, htmlDoc, onMouseLeave)
                    }
                    window.didJustEnter = false
                }
                function setDialogProperties() {
                    setDialogFocus();
                    setAriaLabelForClose();
                    addAriaHeadingToTitle();
                    setDialogAlignment();
                    setDialogModal();
                    setDialogTimeout()
                }
                function setDialogModal() {
                    var modalColor = msg.modalColor;
                    if (modalColor) {
                        AITag.jQuery(".ui-widget-overlay").css("background", modalColor)
                    }
                }
                function setDialogAlignment() {
                    if (dialogArgs && (!dialogArgs.show || dialogArgs.show.effect == "fade_in")) {
                        if (typeof alignElementPosition !== "undefined") {
                            alignElementPosition(dialog.parent())
                        }
                    }
                }
                function setDialogTimeout() {
                    if (duration && duration > 0) {
                        window.setTimeout(function() {
                            if (dialog.is(":visible")) {
                                dialog.dialog("close")
                            }
                        }, duration * 1000)
                    }
                }
                function setDialogFocus() {
                    var dialogElement = document.querySelector(".insightera-dialog");
                    if (dialogElement) {
                        dialogElement.setAttribute("tabindex", "0");
                        dialogElement.focus()
                    }
                }
                function setAriaLabelForClose() {
                    var closeButton = document.querySelector(".ui-dialog-titlebar-close");
                    if (closeButton) {
                        closeButton.setAttribute("aria-label", "Close dialog")
                    }
                }
                function addAriaHeadingToTitle() {
                    var title = document.getElementById("ui-id-1");
                    if (title) {
                        title.setAttribute("role", "heading");
                        title.setAttribute("aria-level", "2")
                    }
                }
                function setCloseButtonPositionAndImage(position, imageUrl, element) {
                    var cssButtonPos = "close-button-top-right";
                    if (position != undefined) {
                        cssButtonPos = "close-button-" + msg.customCloseBtnPosition.toLowerCase().replace("_", "-")
                    }
                    element.css("background-image", "url(" + imageUrl + ")");
                    element.css("background-repeat", "no-repeat");
                    element.css("z-index", "9998");
                    element.css("background-position", "center");
                    element.addClass(cssButtonPos)
                }
                function setCloseButtonPositionAndSVG(position, svgInnerHTML, element) {
                    var cssButtonPos = "close-button-top-right";
                    if (position != undefined) {
                        cssButtonPos = "close-button-" + msg.customCloseBtnPosition.toLowerCase().replace("_", "-")
                    }
                    var domElement = element[0];
                    domElement.style.background = "none";
                    domElement.style.textIndent = "initial";
                    var mockDiv = document.createElement("div");
                    domElement.innerText = "";
                    domElement.appendChild(mockDiv);
                    mockDiv.innerHTML = svgInnerHTML;
                    mockDiv.style.display = "flex";
                    mockDiv.style.justifyContent = "center";
                    mockDiv.style.alignItems = "center";
                    mockDiv.style.width = "100%";
                    mockDiv.style.height = "100%";
                    element.css("z-index", "9998");
                    element.addClass(cssButtonPos)
                }
                function catchMainScrollingElementForDialog(e, callee, dialog, pxLimit, hideOnTop) {
                    var target = e.target;
                    if (target !== undefined && ((target == document) || (target.getBoundingClientRect() !== undefined && target.getBoundingClientRect().top == 0))) {
                        window.removeEventListener("scroll", callee, true);
                        target.addEventListener("scroll", function listenToScroll(e) {
                            openDialogOnScroll(dialog, pxLimit, hideOnTop, listenToScroll, target)
                        })
                    }
                }
                function openDialogOnScroll(dialog, pxLimit, hideOnTop, callee, target) {
                    var scrollContainer = (target == document ? target.scrollingElement : target);
                    if (getInternetExplorerVersion() > -1 && target == document) {
                        scrollContainer = target.documentElement ? target.documentElement : target.body
                    }
                    if (scrollContainer) {
                        var scrollTop = scrollContainer.scrollTop;
                        if (scrollTop && scrollTop >= pxLimit) {
                            dialog.dialog("reposition");
                            dialog.parent(".ui-dialog").css("left", 0);
                            dialog.dialog("open");
                            setDialogProperties();
                            target.removeEventListener("scroll", callee, false);
                            if (hideOnTop && hideOnTop == true) {
                                target.addEventListener("scroll", function hideOnTop() {
                                    hideOnScrollTop(dialog, pxLimit, hideOnTop, target)
                                })
                            }
                        }
                    }
                }
                function onPageExit(dialog, htmlDoc, callee) {
                    dialog.dialog("open");
                    setDialogProperties();
                    htmlDoc.removeEventListener("mouseleave", callee, false)
                }
                function hideOnScrollTop(dialog, topPixel, callee, target) {
                    var scrollContainer = (target == document ? target.scrollingElement : target);
                    if (getInternetExplorerVersion() > -1 && target == document) {
                        scrollContainer = target.documentElement ? target.documentElement : target.body
                    }
                    if (scrollContainer) {
                        var scrollTop = scrollContainer.scrollTop;
                        if (scrollTop && scrollTop <= topPixel) {
                            dialog.dialog("close");
                            target.removeEventListener("scroll", callee, false)
                        }
                    }
                }
            }
            ;
            return $div
        };
        this.conversionCallback = function() {
            AITag.loadState = "conversion callback"
        }
        ;
        var getConversionParams = function(reactionId, requestId) {
            var params = "";
            var visitorId = getVisitorId();
            if (InsighteraUtil.validateParam(visitorId)) {
                params += "&v=" + AITag.escapePercent(visitorId)
            }
            if (InsighteraUtil.validateParam(reactionId)) {
                params += "&s=" + reactionId
            }
            if (InsighteraUtil.validateParam(requestId)) {
                params += "&reqid=" + requestId
            }
            var sessionId = getSessionId();
            if (InsighteraUtil.validateParam(sessionId)) {
                params += "&trwsa.sid=" + sessionId
            }
            return params
        };
        function registerConversion(reactionId) {
            try {
                if (isAnalyticsEnabled()) {
                    iiq.push(["pushCampaign", reactionId, "Conversion", undefined, undefined, getSessionId()])
                }
            } catch (e) {}
            try {
                if (AITag.getConfig("userContextAPI")) {
                    var visitCount = getVisitCount();
                    if (visitCount != null) {
                        rtp("add", "toCmpTl", visitCount, reactionId, 1)
                    }
                    addCampaignToRTPGlobal(reactionId, 1)
                }
            } catch (e) {}
        }
        function getDirection(directionStr, show) {
            var direction = "";
            if (directionStr) {
                switch (directionStr) {
                case "DOWN":
                    direction = (show == "show" ? "up" : "down");
                    break;
                case "UP":
                    direction = (show == "show" ? "down" : "up");
                    break;
                case "LEFT":
                    direction = (show == "show" ? "right" : "left");
                    break;
                case "RIGHT":
                    direction = (show == "show" ? "left" : "right");
                    break;
                default:
                    console.log("Direction" + directionStr + " is unknown")
                }
            }
            return direction
        }
        this.bindConversionListener = function(elementId, reactionId, requestId) {
            var element = this.jQuery("#" + elementId);
            if (element && element.length > 0) {
                AITag.bindConversion(reactionId, requestId, element)
            }
        }
        ;
        this.bindConversion = function(reactionId, requestId, $div) {
            var urlBase = AITag.getConfig("gwUrlPrefix") + "/trw?cmd=1&aid=" + AITag.getConfig("accountId") + getConversionParams(reactionId, requestId);
            var mktoTrk = this.getCookie(COOKIE_MARKETO_TOKEN);
            if (typeof mktoTrk != "undefined" && mktoTrk != null && mktoTrk != "") {
                urlBase += "&ma=" + encodeURIComponent(mktoTrk)
            }
            if (campaignForms2.indexOf(reactionId) > -1) {
                var forms = $div.find("form");
                var id = null;
                AITag.jQuery(forms).each(function() {
                    id = AITag.jQuery(this).attr("id");
                    id = id.split("_");
                    allowedForms2.push(id[1])
                });
                if (window.MktoForms2) {
                    window.MktoForms2 && MktoForms2.whenReady(function(form) {
                        if (allowedForms2.indexOf("" + form.getId()) > -1) {
                            form.onSuccess(function(values, followUpUrl) {
                                var ajaxUrl = urlBase + "&cb=AITag.conversionCallback";
                                registerConversion(reactionId);
                                forms2Submitted(form);
                                addDynamicScript(ajaxUrl, (function(followUpUrl) {
                                    if (!form.doNotRedirect) {
                                        followUpUrl = removeURIParam(followUpUrl, ["iesrc"]);
                                        followUpUrl = addParams2URI(followUpUrl, {
                                            iesrc: "ctr"
                                        });
                                        location.href = followUpUrl
                                    }
                                }
                                )(followUpUrl));
                                return false
                            })
                        }
                    })
                } else {
                    setTimeout(function() {
                        window.MktoForms2 && MktoForms2.whenReady(function(form) {
                            if (allowedForms2.indexOf("" + form.getId()) > -1) {
                                form.onSuccess(function(values, followUpUrl) {
                                    var ajaxUrl = urlBase + "&cb=AITag.conversionCallback";
                                    registerConversion(reactionId);
                                    forms2Submitted(form);
                                    addDynamicScript(ajaxUrl, (function(followUpUrl) {
                                        if (!form.doNotRedirect) {
                                            followUpUrl = removeURIParam(followUpUrl, ["iesrc"]);
                                            followUpUrl = addParams2URI(followUpUrl, {
                                                iesrc: "ctr"
                                            });
                                            location.href = followUpUrl
                                        }
                                    }
                                    )(followUpUrl));
                                    return false
                                })
                            }
                        })
                    }, 6000)
                }
            } else {
                var forms = $div.find("form");
                if (forms && forms.length > 0) {
                    AITag.checkForEmailTextField()
                }
                $div.find("form").submit(function(event) {
                    var ajaxUrl = urlBase + "&cb=AITag.conversionCallback";
                    registerConversion(reactionId);
                    addDynamicScript(ajaxUrl)
                })
            }
            $div.find("a,:submit,:button").click(function(event) {
                var ajaxUrl = urlBase + "&cb=AITag.conversionCallback";
                var targetElement = event.target || event.srcElement;
                var parent = AITag.jQuery(targetElement).parents("a");
                if (targetElement.tagName == "A") {
                    var href = targetElement.href;
                    href = removeURIParam(href, ["iesrc"]);
                    href = addParams2URI(href, {
                        iesrc: "ctr"
                    });
                    targetElement.href = href
                } else {
                    if (parent.get(0)) {
                        var parentEl = parent.get(0);
                        if (parentEl.tagName == "A") {
                            var href = parentEl.href;
                            href = removeURIParam(href, ["iesrc"]);
                            href = addParams2URI(href, {
                                iesrc: "ctr"
                            });
                            parentEl.href = href
                        }
                    }
                }
                registerConversion(reactionId);
                addDynamicScript(ajaxUrl)
            });
            $div.find("a").each(function() {
                var $a = AITag.jQuery(this);
                var href = $a.attr("href");
                campaignLinks[href] = true;
                href = removeURIParam(href, ["iesrc"]);
                href = addParams2URI(href, {
                    iesrc: "ctr"
                });
                $a.attr("href", href)
            });
            $div.find("a").click(function(event) {
                var $a = AITag.jQuery(event.currentTarget);
                var href = $a.attr("href");
                var target = $a.attr("target");
                var first3 = href.substr(0, 3).toLowerCase();
                if (first3 != "www" && first3 != "htt") {
                    return
                }
                href = removeURIParam(href, ["iesrc"]);
                href = addParams2URI(href, {
                    iesrc: "ctr"
                });
                var redirectUrl = AITag.getConfig("protocol") + "://" + urlBase + "&rd=" + encodeURIComponent(href.replace(/\+/g, "%2B"));
                registerConversion(reactionId);
                if (!target || TARGET_VAL.indexOf(target) > -1) {
                    $a.attr("href", redirectUrl)
                }
            })
        }
        ;
        function injectContent(element, content) {
            var div = document.createElement("div");
            div.innerHTML = "_" + content;
            var nodes = div.childNodes;
            var node;
            for (var i = 0; nodes[i] != null; i++) {
                node = nodes[i];
                if (typeof node.getElementsByTagName !== "undefined") {
                    var scripts = node.getElementsByTagName("script");
                    if (scripts !== undefined && scripts != null) {
                        for (var j = scripts.length - 1; j >= 0; j--) {
                            scripts[j].parentNode.removeChild(scripts[j])
                        }
                    }
                }
                if (node.nodeName.toLowerCase() == "script") {
                    node.parentNode.removeChild(node)
                }
            }
            element.innerHTML = div.innerHTML.substring(1, div.innerHTML.length)
        }
        this.putReactionInDiv = function($div, reactionHTMLCode, reactionId, requestId, targetDiv, useEffect, effectType, isBindConversion) {
            if (typeof targetDiv != "undefined" && targetDiv != null && targetDiv != "widget" && (useEffect == true || useEffect == "true")) {
                logMessage("replacing div " + targetDiv + " with effect " + effectType);
                $div.hide(0, function() {
                    try {
                        injectContent($div.get(0), reactionHTMLCode);
                        var selectedEffect = effectType.toLowerCase();
                        var options = {};
                        if (selectedEffect === "scale") {
                            options = {
                                percent: 0
                            }
                        } else {
                            if (selectedEffect === "transfer") {
                                options = {
                                    to: "#button",
                                    className: "ui-effects-transfer"
                                }
                            } else {
                                if (selectedEffect === "size") {
                                    options = {
                                        to: {
                                            width: 200,
                                            height: 60
                                        }
                                    }
                                }
                            }
                        }
                        $div.effect(selectedEffect, options, 1000, function() {});
                        logMessage("HTML was replaced for div " + targetDiv + " with effect " + effectType);
                        if (typeof isBindConversion === "undefined" || !isBindConversion) {
                            AITag.bindConversion(reactionId, requestId, $div)
                        }
                    } catch (e) {
                        logMessage("failed to inject div " + targetDiv + ". error: " + e.message)
                    }
                })
            } else {
                injectContent($div.get(0), reactionHTMLCode);
                if (typeof isBindConversion === "undefined" || !isBindConversion) {
                    AITag.bindConversion(reactionId, requestId, $div)
                }
            }
        }
        ;
        this.emailCallback = function() {
            AITag.deleteCookie(COOKIE_TRW_EMAIL)
        }
        ;
        var getLeadParams = function(email) {
            var params = "";
            AITag.addVisitorId();
            var visitorId = getVisitorId();
            if (InsighteraUtil.validateParam(visitorId)) {
                params += "&v=" + visitorId
            }
            if (InsighteraUtil.validateParam(email)) {
                params += "&e=" + encodeURIComponent(email)
            }
            var sessionId = getSessionId();
            if (!InsighteraUtil.validateParam(sessionId)) {
                AITag.createSessionId(true);
                sessionId = getSessionId()
            }
            if (InsighteraUtil.validateParam(sessionId)) {
                params += "&trwsa.sid=" + sessionId
            }
            params += "&" + getVCParam();
            return params
        };
        var sendLead = function(email, callback) {
            try {
                var params = AITag.getConfig("gwUrlPrefix") + "/trw?cmd=2&cb=AITag.emailCallback&aid=" + AITag.getConfig("accountId") + getLeadParams(email);
                var mktoTrk = AITag.getCookie(COOKIE_MARKETO_TOKEN);
                if (typeof mktoTrk != "undefined" && mktoTrk != null && mktoTrk != "") {
                    params += "&ma=" + encodeURIComponent(mktoTrk)
                }
                if (AITag.getConfig("anonymizeIP")) {
                    params = addToParams(params, {
                        aip: 1
                    })
                }
                addDynamicScript(params, callback)
            } catch (e) {
                logMessage("failed to submit a lead " + e)
            }
        };
        this.foundEmail = function(email, callback) {
            if (email != null) {
                AITag.setCookie(COOKIE_TRW_EMAIL, email, exp_30_minutes);
                AITag.addVisitorId(true);
                sendLead(email, callback)
            }
        }
        ;
        this.checkForEmailTextFields = function() {
            if (AITag.getConfig("captureLeads")) {
                AITag.checkForEmailTextField()
            }
        }
        ;
        this.sendViewEventToRTP = function(element) {
            var asset;
            if (element && element.tagName && element.tagName.toLowerCase() === "a") {
                asset = element
            } else {
                var divElement = element + " .rtp_rcmd2_item_inner .rtp_rcmd2_img_container .rtp_rcmd2_link_hidden";
                asset = document.querySelector(divElement)
            }
            if (asset) {
                var href = asset.href;
                var paramsIndex = href.indexOf("?");
                if (paramsIndex != -1) {
                    var parameters = href.substring(paramsIndex);
                    if (parameters.indexOf("iesrc=rcmd") != -1) {
                        var assetId = RCMDStorage[extractAssetId](parameters);
                        if (AITag.richMediaMap[parameters] != undefined && !AITag.richMediaMap[parameters]) {
                            AITag.sendClick(encodeURIComponent(href), null, {
                                cmd: 4,
                                asid: assetId,
                                viewId: AITag.getViewId()
                            }, true);
                            AITag.richMediaMap[parameters] = true;
                            logMessage("Sending view event for asset: " + href)
                        }
                    }
                }
            }
        }
        ;
        this.getViewId = function() {
            if (!AITag.viewId) {
                AITag.viewId = generateUUID()
            }
            return AITag.viewId
        }
        ;
        this.sendHoverEventToRTP = function(element, duration) {
            var asset;
            if (element && element.tagName && element.tagName.toLowerCase() === "a") {
                asset = element
            } else {
                asset = element.querySelector(".rtp_rcmd2_img_container a")
            }
            if (asset) {
                var href = asset.href;
                var paramsIndex = href.indexOf("?");
                if (paramsIndex != -1) {
                    var parameters = href.substring(paramsIndex);
                    if (parameters.indexOf("iesrc=rcmd") != -1) {
                        var assetId = RCMDStorage[extractAssetId](parameters);
                        var matchedList = rcmdHoverAssetArray.filter(function(asset) {
                            return assetId === asset.assetId
                        });
                        if (matchedList.length > 0) {
                            matchedList[0].duration += duration
                        } else {
                            rcmdHoverAssetArray.push({
                                assetId: assetId,
                                viewId: AITag.getViewId(),
                                duration: duration,
                                uri: encodeURIComponent(href)
                            });
                            AITag.sendClick(encodeURIComponent(href), null, {
                                cmd: 5,
                                asid: assetId,
                                viewId: AITag.getViewId()
                            });
                            if (!hoverEvtInterval) {
                                AITag.sendHoverAtIntervals()
                            }
                        }
                    }
                }
            }
        }
        ;
        this.sendHoverAtIntervals = function() {
            var HOVER_TIME_SPENT_EVENT_DURATION_SECONDS = 300;
            hoverEvtInterval = setInterval(function() {
                for (var i = 0; i < rcmdHoverAssetArray.length; i++) {
                    var duration = (rcmdHoverAssetArray[i].duration) > HOVER_TIME_SPENT_EVENT_DURATION_SECONDS ? HOVER_TIME_SPENT_EVENT_DURATION_SECONDS : rcmdHoverAssetArray[i].duration;
                    if (duration > 0) {
                        AITag.sendClick(rcmdHoverAssetArray[i].uri, null, {
                            cmd: 6,
                            asid: rcmdHoverAssetArray[i].assetId,
                            viewId: rcmdHoverAssetArray[i].viewId,
                            duration: duration
                        });
                        rcmdHoverAssetArray[i].duration = 0
                    }
                }
            }, (HOVER_TIME_SPENT_EVENT_DURATION_SECONDS * 1000))
        }
        ;
        this.bindEvent = function(element, eventType, eventFunction, extraParameter) {
            AITag.jQuery(element).bind(eventType, eventFunction)
        }
        ;
        function bindMLMForm1() {
            var buttonAttached = false;
            try {
                var emailField = getEmailField("Email");
                var button = getButton("mktFrmSubmit");
                if (emailField.length > 0 && button.length > 0) {
                    buttonAttached = true;
                    var events = button.attr("onclick");
                    button.removeAttr("onclick");
                    AITag.bindEvent(button, "click", function(emailField, events) {
                        return function() {
                            var email = emailField.val();
                            var func;
                            if (typeof events === "function") {
                                func = events
                            } else {
                                func = new Function(events)
                            }
                            if (typeof email != "undefined" && email != "") {
                                var callback = function() {
                                    try {
                                        return func.apply()
                                    } catch (e) {}
                                    var form = emailField.parents("form");
                                    form.submit()
                                };
                                AITag.foundEmail(emailField.val(), callback);
                                return false
                            } else {
                                return func.apply()
                            }
                        }
                    }(emailField, events))
                }
            } catch (e) {
                logMessage("failed to bind MLM form 1")
            }
            return buttonAttached
        }
        function getButton(buttonName) {
            var $buttons = AITag.jQuery("a,:submit,:button");
            var button = $buttons.filter("#" + buttonName);
            if (button.length == 0) {
                button = $buttons.find('[name="' + buttonName + '"]');
                if (button.length == 0) {
                    button = $buttons.find('[value="' + buttonName + '"]');
                    if (button.length == 0) {
                        $buttons.each(function(index) {
                            if (AITag.jQuery(this).text() == buttonName) {
                                if (button.length == 0) {
                                    button = AITag.jQuery(this)
                                } else {
                                    button.add(this)
                                }
                            }
                        })
                    }
                }
            }
            return button
        }
        function getEmailField(fieldName) {
            var emailField = AITag.jQuery('input:text[name="' + fieldName + '"]');
            if (emailField.length == 0) {
                emailField = AITag.jQuery('input:text[id="' + fieldName + '"]')
            }
            if (emailField.length == 0) {
                emailField = AITag.jQuery('input[name="' + fieldName + '"]')
            }
            if (emailField.length == 0) {
                emailField = AITag.jQuery('input[id="' + fieldName + '"]')
            }
            if (emailField.length == 0) {
                emailField = AITag.jQuery('input[class|="' + fieldName + '"]')
            }
            return emailField
        }
        function formSubmitted(form) {
            var passwordFileds = AITag.jQuery(form).find("input[type='password']");
            if (passwordFileds && passwordFileds.length == 1) {
                return
            }
            AITag.jQuery(form).find(":input").each(function() {
                var val = AITag.jQuery(this).val();
                if (EMAIL_REG.test(val)) {
                    var email = val;
                    if (typeof email != "undefined" && email != "") {
                        AITag.foundEmail(email)
                    }
                }
            })
        }
        function forms2Submitted(form) {
            formSubmitted(form.getFormElem())
        }
        this.checkForEmailTextField = function() {
            if (window.MktoForms2) {
                MktoForms2.whenReady(function(form) {
                    form.onSubmit(function() {
                        if (allowedForms2.indexOf("" + form.getId()) == -1) {
                            forms2Submitted(form)
                        }
                    })
                })
            }
            var fieldNames = AITag.getConfig("mailTextField");
            var fieldButtonMap = AITag.getConfig("fieldButtonMap");
            var buttonNames = null, buttonName, buttonAttached = false;
            var emailField;
            buttonAttached = bindMLMForm1();
            if (fieldNames) {
                if (typeof fieldNames === "string") {
                    fieldNames = [fieldNames]
                }
                for (var i = 0; i < fieldNames.length; i++) {
                    var fieldName = fieldNames[i];
                    if (fieldName) {
                        try {
                            emailField = getEmailField(fieldName);
                            if (emailField.length == 0) {
                                continue
                            }
                        } catch (e) {
                            if (window.console && window.console.log && window.console.log.apply) {
                                console.log("fail to find input " + e)
                            }
                        }
                        if (fieldButtonMap !== undefined && fieldButtonMap != null) {
                            buttonNames = fieldButtonMap[fieldName]
                        } else {
                            buttonNames = AITag.getConfig("mailButton")
                        }
                        if (typeof buttonNames === "string") {
                            buttonNames = [buttonNames]
                        }
                        if (buttonNames) {
                            for (var j = 0; j < buttonNames.length; j++) {
                                buttonName = buttonNames[j];
                                if (buttonName) {
                                    try {
                                        var button = getButton(buttonName)
                                    } catch (e) {
                                        if (window.console && window.console.log && window.console.log.apply) {
                                            console.log("fail to find input button " + e)
                                        }
                                    }
                                    try {
                                        if (button.length > 0 && buttonName == "mktFrmSubmit") {
                                            buttonAttached = true;
                                            var events = button.attr("onclick");
                                            button.removeAttr("onclick");
                                            AITag.bindEvent(button, "click", function(emailField, events) {
                                                return function() {
                                                    var email = emailField.val();
                                                    var func;
                                                    if (typeof events === "function") {
                                                        func = events
                                                    } else {
                                                        func = new Function(events)
                                                    }
                                                    if (typeof email != "undefined" && email != "") {
                                                        var callback = function() {
                                                            try {
                                                                return func.apply()
                                                            } catch (e) {}
                                                            var form = emailField.parents("form");
                                                            form.submit()
                                                        };
                                                        AITag.foundEmail(emailField.val(), callback);
                                                        return false
                                                    } else {
                                                        return func.apply()
                                                    }
                                                }
                                            }(emailField, events))
                                        } else {
                                            if (button.length > 0) {
                                                buttonAttached = true;
                                                AITag.bindEvent(button, "click", function(emailField) {
                                                    return function() {
                                                        var email = emailField.val();
                                                        if (typeof email != "undefined" && email != "") {
                                                            AITag.foundEmail(email)
                                                        }
                                                    }
                                                }(emailField))
                                            }
                                        }
                                    } catch (e) {
                                        if (window.console && window.console.log && window.console.log.apply) {
                                            console.log("fail to bind click event on button " + e)
                                        }
                                    }
                                }
                            }
                        }
                        if (!buttonAttached) {
                            AITag.bindEvent(emailField.parents("form"), "submit", function(emailField) {
                                return function() {
                                    var email = AITag.jQuery(emailField).val();
                                    if (typeof email != "undefined" && email != "") {
                                        AITag.foundEmail(email)
                                    }
                                }
                            }(emailField))
                        }
                    }
                }
            } else {
                if (AITag.getConfig("getAllEmails")) {
                    var forms = AITag.jQuery("form");
                    forms.each(function() {
                        AITag.jQuery(this).submit(function() {
                            var id = AITag.jQuery(this).attr("id");
                            if (!id || id.indexOf("mktoForm") == -1) {
                                formSubmitted(this)
                            }
                        })
                    })
                }
            }
        }
        ;
        this.runAnalyticsIntegration = function() {
            if (isAnalyticsEnabled()) {
                logMessageTime("import analytics. Time since DOM loading: ", new Date().getTime());
                addDynamicScript(cdnSrc + GA_CDN_PATH, function() {})
            }
        }
        ;
        this.activateCampaignStorage = function() {
            try {
                if (checkIESupported()) {
                    campaignStorage.exc(["init"]);
                    campaignStorage.exc(["showStoredCampaigns"])
                }
            } catch (e) {
                logMessage("failed to load stored campaigns. error: " + e.message)
            }
        }
        ;
        this.state = "pre-startup";
        this.startup = function() {
            if (!window.AIConfig) {
                window.AIConfig = {}
            }
            this.updateInitialConfig.apply(AITag);
            this.loadState = "post-startup";
            if (typeof this.pollInterval == "undefined" || this.pollInterval == null) {
                this.pollInterval = ""
            }
            if (typeof isDialogPresented == "undefined" || isDialogPresented == null) {
                isDialogPresented = false
            }
            this.pollIntervalCounter = 0;
            this.activateCampaignStorage();
            try {
                this.activateRecommendation.apply(AITag)
            } catch (e) {}
            try {
                this.runAnalyticsIntegration.apply(AITag)
            } catch (e) {}
            this.go();
            try {
                bindMessageEvent()
            } catch (e) {}
        }
        ;
        this.loadCss = function() {
            if (!this.getConfig("jqueryUiCssExistsOnPage")) {
                this.addDynamicStyle(cdnSrc + this.getConfig("jQueryUICustomCssPath"))
            } else {
                this.addDynamicStyle(cdnSrc + this.getConfig("requiredCssPath"))
            }
        }
        ;
        function sendMktoToken() {
            var mktoParam = this.extractURLParameter("mkt_tok");
            if (mktoParam != null && mktoParam != "" && !mktoExistOnClick) {
                var firstIntervalTime = null;
                getMktoTokenInterval = setInterval(function() {
                    logMessage("get mkto cookie interval called");
                    if (firstIntervalTime == null) {
                        firstIntervalTime = (new Date).getTime()
                    }
                    var mktoTrkExists = false;
                    var mktoTrk = AITag.getCookie(COOKIE_MARKETO_TOKEN);
                    if (typeof mktoTrk != "undefined" && mktoTrk != null && mktoTrk != "") {
                        mktoTrkExists = true;
                        addDynamicScript(AITag.getConfig("gwUrlPrefix") + "/put/munchkinId?aid=" + AITag.getConfig("accountId") + "&id=" + encodeURIComponent(mktoTrk))
                    }
                    if (((new Date).getTime() - firstIntervalTime > 20000) || mktoTrkExists) {
                        logMessage("clearing interval for get marketo");
                        clearInterval(getMktoTokenInterval)
                    }
                }, 50)
            }
        }
        this.sendEvent = function(data) {
            this.addVisitorId();
            this.createSessionId(true);
            var query = "";
            var sid = getSessionIdParam();
            if (sid == "") {
                return
            }
            query += "&" + sid;
            var vid = getVisitorIdParam();
            if (vid == "") {
                return
            }
            query += "&" + vid;
            if (!data || typeof data != "object" || !data.value) {
                return
            }
            var vcParam = getVCParam();
            query += (vcParam != "") ? ("&" + vcParam) : "";
            if (typeof data.value === "string") {
                data.value = [data.value]
            }
            query = addToParams(query, {
                pm: currentPageAllowedCampaigns.join()
            });
            query += "&rtp.ev=" + JSON.stringify(data);
            var mktoTrk = this.getCookie(COOKIE_MARKETO_TOKEN);
            var params = this.getConfig("gwUrlPrefix") + "/evt?aid=" + this.getConfig("accountId") + query;
            if (typeof mktoTrk != "undefined" && mktoTrk != null && mktoTrk != "") {
                params += "&ma=" + encodeURIComponent(mktoTrk)
            }
            addDynamicScript(params)
        }
        ;
        var addDefaultParams = function() {
            var params = "";
            var vidParam = getVisitorIdParam();
            params += (vidParam != "") ? ("&" + vidParam) : "";
            var vcParam = getVCParam();
            params += (vcParam != "") ? ("&" + vcParam) : "";
            var knownParam = getKnownParam();
            params += (knownParam != "") ? ("&" + knownParam) : "";
            var sidParam = getSessionIdParam();
            params += (sidParam != "") ? ("&" + sidParam) : "";
            var cpvParam = getCPVParam();
            params += (cpvParam != "") ? ("&" + cpvParam) : "";
            var emailParam = getEmailParam();
            params += (emailParam != "") ? ("&" + emailParam) : "";
            var timeZoneOffset = getJodaTimeZone();
            if (timeZoneOffset) {
                params += ("&ctzo=" + timeZoneOffset)
            }
            return params
        };
        function getJodaTimeZone() {
            var offset = new Date().getTimezoneOffset()
              , o = Math.abs(offset);
            var hourPart = Math.floor(o / 60);
            if ((offset < 0 && hourPart > 14) || (offset > 0 && hourPart > 12)) {
                return null
            }
            return (offset < 0 ? "+" : "-") + ("00" + Math.floor(o / 60)).slice(-2) + ":" + ("00" + (o % 60)).slice(-2)
        }
        var getUserContextParams = function() {
            var userContextParams = "";
            if (window.rtp) {
                userContextParams = rtp.getTracker().get("userContextParams")
            }
            return userContextParams
        };
        function addToParams(paramsString, paramObj, orginalQueryString) {
            if (paramsString === void 0 || paramsString == null) {
                paramsString = ""
            }
            if (paramObj !== void 0 && paramObj != null) {
                for (var param in paramObj) {
                    if (paramObj.hasOwnProperty(param)) {
                        if (paramsString.length > 0 && paramsString.lastIndexOf("&") < paramsString.length - 1) {
                            paramsString += "&"
                        }
                        var exists = orginalQueryString !== undefined ? (isParamInString(paramsString, param) || isParamInString(orginalQueryString, param)) : isParamInString(paramsString, param);
                        if (!exists) {
                            paramsString += param + "=" + paramObj[param]
                        }
                    }
                }
            }
            return paramsString
        }
        function isParamInString(str, param) {
            var result = false;
            var paramIndex = str.indexOf("?");
            var parametersStr = str.substring(paramIndex + 1, str.length);
            var paramsArray = parametersStr.split("&");
            paramsArray.forEach(function(entry) {
                if (entry.length > 0 && entry.indexOf("=") > 0) {
                    var keyValue = entry.split("=");
                    if (keyValue.length == 2 && keyValue[1] != "") {
                        if (keyValue[0] == param) {
                            result = true
                        }
                    }
                }
            });
            return result
        }
        function addParams2URI(uri, paramsMap) {
            if (paramsMap !== undefined && paramsMap != null) {
                var queryString = ""
                  , orginalQueryString = "";
                var queryIndex = uri.indexOf("?");
                var hashIndex = uri.indexOf("#");
                if (queryIndex > -1) {
                    if (hashIndex == -1) {
                        hashIndex = uri.length - 1
                    }
                    orginalQueryString = uri.substring(queryIndex, hashIndex)
                }
                queryString = addToParams(queryString, paramsMap, orginalQueryString);
                var hashString = "";
                if (hashIndex > -1 && hashIndex != uri.length - 1) {
                    hashString = uri.substring(hashIndex);
                    uri = uri.substring(0, hashIndex)
                }
                if (queryIndex > -1) {
                    if (queryIndex + 1 < uri.length) {
                        uri += "&"
                    }
                } else {
                    uri += "?"
                }
                uri += queryString + hashString
            }
            return uri
        }
        function removeURIParam(uri, paramList) {
            var a = document.createElement("a");
            a.href = uri;
            var queryStr = a.search;
            var query = "";
            if (queryStr != "") {
                var queryStr = queryStr.substring(1, queryStr.length);
                var queryArr = queryStr.split("&");
                var queryMap = {};
                var param;
                for (var i = 0; i < queryArr.length; i++) {
                    param = queryArr[i].split("=");
                    if (param.length == 2) {
                        queryMap[param[0]] = param[1]
                    }
                }
                for (var i = 0; i < paramList.length; i++) {
                    delete queryMap[paramList[i]]
                }
                for (var key in queryMap) {
                    if (queryMap.hasOwnProperty(key)) {
                        query += query.length == 0 ? key + "=" + queryMap[key] : "&" + key + "=" + queryMap[key]
                    }
                }
                var queryIndex = a.href.indexOf("?");
                if (queryIndex == -1) {
                    queryIndex = a.href.length
                }
                uri = a.href.substring(0, queryIndex);
                if (query != "") {
                    uri += "?" + query
                }
                uri += a.hash
            }
            return uri
        }
        this.sendClick = function(uri, callback, params2Add, avoidCpv) {
            if (!AITag.getConfig("disableClick")) {
                this.addVisitorId();
                this.createSessionId(avoidCpv);
                var referrerExt = "";
                var ref = decodeURIComponent(document.referrer);
                if (ref != undefined && ref != "") {
                    referrerExt = "&ref=" + encodeURIComponent(ref)
                }
                var uriExt = "";
                if (typeof uri != "undefined" && uri != "") {
                    uriExt = "&uri=" + uri
                }
                var extraParam = "";
                if (typeof params2Add != "undefined") {
                    extraParam = "&" + addToParams(extraParam, params2Add)
                }
                var searchTerms = this.getSearchTerms();
                var mktoTrk = this.getCookie(COOKIE_MARKETO_TOKEN);
                var params = this.getConfig("gwUrlPrefix") + "/trw?aid=" + this.getConfig("accountId") + referrerExt + addDefaultParams() + uriExt + extraParam;
                if (typeof mktoTrk != "undefined" && mktoTrk != null && mktoTrk != "") {
                    params += "&ma=" + encodeURIComponent(mktoTrk);
                    mktoExistOnClick = true
                }
                if (searchTerms) {
                    params += "&" + COOKIE_TRW_SEARCH_TERMS + "=" + searchTerms
                }
                if (this.getConfig("userContextAPI")) {
                    var userContextParams = getUserContextParams();
                    if (userContextParams !== "") {
                        params += "&" + userContextParams
                    }
                }
                if (AITag.getConfig("viewBlocking") && potentialCampaignExist) {
                    params = addToParams(params, {
                        vb: 1
                    });
                    potentialCampaignExist = false
                }
                params = addToParams(params, {
                    pm: currentPageAllowedCampaigns.join()
                });
                params = addToParams(params, {
                    viewedTypes: encodeURIComponent(viewedTypes.join())
                });
                if (AITag.getConfig("anonymizeIP")) {
                    params = addToParams(params, {
                        aip: 1
                    })
                }
                viewTime = new Date().getTime();
                addDynamicScript(params + "&rts=" + viewTime, callback);
                logMessageTime("sending view call. Time since DOM loading: ", new Date().getTime());
                sendMktoToken.call(this)
            }
        }
        ;
        this.cookiesCleanup = function() {
            if (!config.disableClick) {
                copyVcCookie();
                copyCpvCookie();
                try {
                    for (var i = 0; i < unusedCookies.length; i++) {
                        this.deleteCookie(unusedCookies[i])
                    }
                } catch (e) {}
            }
        }
        ;
        this.main = function() {
            var uri = encodeURIComponent(window.location.href);
            this.sendClick(uri);
            this.loadJQuery(AITag.afterFullyLoaded);
            this.loadCss.apply(this);
            this.loadState = "post-main"
        }
        ;
        var readyQueue = [];
        function done() {
            if (!isReady) {
                var domReadyTime = new Date().getTime();
                logMessageTime("DOM is ready called. Time since DOM loading: ", domReadyTime, performanceTime && performanceTime.domContentLoadedEventStart)
            }
            isReady = true;
            if (inzoneCampaignTimeout) {
                clearTimeout(inzoneCampaignTimeout)
            }
            if (document.addEventListener || event.type === "load" || document.readyState === "complete") {
                if (document.addEventListener) {
                    document.removeEventListener("DOMContentLoaded", done, false);
                    window.removeEventListener("load", done, false)
                } else {
                    document.detachEvent("onreadystatechange", done);
                    window.detachEvent("onload", done)
                }
                var readyQueueLength = readyQueue.length;
                while (readyQueueLength) {
                    readyQueueLength--;
                    var next = readyQueue.shift();
                    var func = next.shift();
                    if (typeof func === "function") {
                        try {
                            func.apply(AITag, next[0])
                        } catch (e) {
                            logMessage("Error:: failed to run ready queue. Error message: " + e)
                        }
                    }
                }
            }
        }
        function addDOMReadyListener(afterFullyLoaded) {
            var args = [].slice.call(arguments, 1);
            readyQueue.push([afterFullyLoaded, args]);
            if (document.readyState === "complete" || isReady) {
                afterFullyLoaded.apply(AITag, args)
            } else {
                if (document.addEventListener) {
                    document.addEventListener("DOMContentLoaded", done, false);
                    window.addEventListener("load", done, false)
                } else {
                    document.attachEvent("onreadystatechange", done);
                    window.attachEvent("onload", done)
                }
            }
        }
        this.afterExternalJqueryReady = function(afterFullyLoaded) {
            logMessage("after fully loaded: " + afterFullyLoaded);
            this.afterJQueryLoaded();
            if (!this.getConfig("useExistingjQueryUI")) {
                if (typeof jQuery.ui != "undefined" && typeof jQuery.ui.dialog != "undefined") {
                    addDOMReadyListener(afterFullyLoaded)
                } else {
                    var url = this.getConfig("jqueryUIUrl");
                    if (this.getConfig("loadOnlyJQueryDialog")) {
                        url = cdnSrc + this.getConfig("jQueryUIDialogPath")
                    }
                    addDynamicScript(url, function() {
                        addDOMReadyListener(afterFullyLoaded)
                    })
                }
            } else {
                var firstInterval = null;
                var jQueryInterval = setInterval(function() {
                    firstInterval = !firstInterval && (new Date()).getTime();
                    if ((new Date()).getTime() - firstInterval > 2000) {
                        clearInterval(jQueryInterval)
                    }
                    if (window.$.ui !== void 0 || jQuery.ui !== void 0) {
                        clearInterval(jQueryInterval);
                        addDOMReadyListener(afterFullyLoaded)
                    }
                }, 100)
            }
        }
        ;
        this.loadJQuery = function(afterFullyLoaded) {
            var refThis = this;
            var isJQLoaded = typeof jQuery != "undefined" && window.jQuery != null && jQuery.fn;
            if (!AITag.getConfig("useExistingJQuery")) {
                function loadJQueryNow() {
                    addDynamicScript(this.getConfig("jqueryUrl"), function() {
                        this.afterJQueryLoaded();
                        if (parseFloat(jQuery.fn.jquery) < 3) {
                            jQuery.noConflict(true)
                        }
                        var url = this.getConfig("jqueryUIUrl");
                        if (this.getConfig("loadOnlyJQueryDialog")) {
                            url = cdnSrc + this.getConfig("jQueryUIDialogPath")
                        }
                        addDynamicScript(url, function() {
                            addDOMReadyListener(afterFullyLoaded)
                        })
                    })
                }
                if (isJQLoaded) {
                    if (jQuery.isReady) {
                        loadJQueryNow.apply(this)
                    } else {
                        jQuery(document).ready(function() {
                            loadJQueryNow.apply(refThis)
                        })
                    }
                } else {
                    loadJQueryNow.apply(this)
                }
            } else {
                addDOMReadyListener(this.afterExternalJqueryReady, afterFullyLoaded)
            }
        }
        ;
        this.afterJQueryLoaded = function() {
            this.loadState = "loaded jquery";
            this.jQuery = jQuery
        }
        ;
        this.getRCMD = function(action) {
            if (this.fullyLoaded) {
                if (checkIESupported()) {
                    ibq.push(["init", {
                        link_color: AITag.getConfig("link_color"),
                        font_family: AITag.getConfig("font_family"),
                        font_size: AITag.getConfig("font_size"),
                        font_color: AITag.getConfig("font_color"),
                        background_color: AITag.getConfig("background_color"),
                        bar_position: AITag.getConfig("bar_position"),
                        bar_tab_position: AITag.getConfig("bar_tab_position"),
                        icon_color: AITag.getConfig("barIconColor"),
                        icon_font: AITag.getConfig("barIconFont"),
                        icon_opacity: AITag.getConfig("barIconOpacity")
                    }]);
                    var rcmdHistoryParams = RCMDStorage[getAsParams].call(RCMDStorage);
                    setTimeout(function() {
                        ibq.push(["getREC", getSessionId(), getVisitorId(), getVisitCount(), action, getClickPerVisit(), rcmdHistoryParams, AITag.getCookie(COOKIE_MARKETO_TOKEN)])
                    }, 50)
                }
            } else {
                rcmdq.push(["getRCMD", action])
            }
        }
        ;
        function addSessionCreatedListener(callback) {
            sessionQueue.push(callback)
        }
        function runSessionQueue() {
            var length = sessionQueue.length;
            var func;
            for (var i = 0; i < length; i++) {
                func = sessionQueue.shift();
                func.apply(AITag)
            }
        }
        window.rtpRCMD = window.rtpRCMD || function() {
            (window.rtpRCMD.q = window.rtpRCMD.q || []).push(arguments)
        }
        ;
        function initRCMDWidget() {
            if (sessionReady) {
                var rcmdHistoryParams = RCMDStorage[getAsParams].call(RCMDStorage);
                window.rtpRCMD("init", {
                    serverUrl: AITag.getConfig("gwUrlPrefix"),
                    sid: getSessionId(),
                    vid: getVisitorId(),
                    vc: getVisitCount(),
                    cc: getClickPerVisit(),
                    consumedAssets: rcmdHistoryParams,
                    munchkinToken: AITag.getCookie(COOKIE_MARKETO_TOKEN)
                })
            } else {
                addSessionCreatedListener(initRCMDWidget)
            }
        }
        this.initRCMDWidget = function() {
            initRCMDWidget()
        }
        ;
        function isAnalyticsEnabled() {
            return AITag.getConfig("googleAnalytics") || AITag.getConfig("enableSiteCatalyst") || AITag.getConfig("enableUAnalytics") || AITag.getConfig("enableFBRemarketing")
        }
        function runCampaignQueue() {
            try {
                var cpqLength = cpq.length;
                while (cpqLength) {
                    cpqLength--;
                    var param = cpq.shift();
                    var method = param.shift();
                    AITag[method].apply(AITag, param)
                }
                cpq.push = function(params) {
                    var method = params.shift();
                    AITag[method].apply(AITag, params)
                }
            } catch (e) {
                logMessage("Error:: failed to run campaign queue. Error Message: " + e)
            }
        }
        this.afterAllJQueryLoaded = function() {
            this.fullyLoaded = true;
            logMessageTime("After All jQuery fully loaded called. Time since DOM loading: ", new Date().getTime());
            runCampaignQueue();
            this.checkForEmailTextFields.apply(this);
            if (this.getConfig("trackAsset") || this.getConfig("discoverAsset")) {
                this.addAssetCollector.apply(AITag)
            }
            try {
                if (isAnalyticsEnabled()) {
                    iiq.push(["pushCustomData", getSessionId()]);
                    iiq.push(["pushSegment"])
                }
            } catch (e) {}
            if (this.getConfig("enableRecommendationBar")) {
                if (rcmdq.length > 0) {
                    try {
                        var rcmdLength = rcmdq.length;
                        while (rcmdLength) {
                            rcmdLength--;
                            var param = rcmdq.shift();
                            var method = param.shift();
                            AITag[method].apply(AITag, param)
                        }
                        rcmdq.push = function(params) {
                            var method = params.shift();
                            AITag[method].apply(AITag, params)
                        }
                    } catch (e) {}
                } else {
                    this.getRCMD.call(this, true)
                }
            }
        }
        ;
        this.afterFullyLoaded = function() {
            this.loadState = "fully loaded";
            var pollDelay = this.getConfig("pollingDelay") * second;
            this.pollAmount = this.getConfig("pollingPerPage");
            if (this.pollAmount > 0 && !this.getConfig("disablePoll") && checkIESupported()) {
                setTimeout(function() {
                    if (AITag.pollInterval == "") {
                        AITag.startPoll();
                        if (AITag.pollIntervalCounter < AITag.pollAmount) {
                            AITag.pollInterval = window.setInterval(AITag.startPoll, pollDelay)
                        }
                    }
                }, this.getConfig("initialDelay"))
            }
            if (typeof this.loadCallBack === "function") {
                this.loadCallBack()
            }
            this.afterAllJQueryLoaded.apply(this)
        }
        ;
        this.startPoll = function() {
            AITag.loadState = "polling";
            AITag.pollMsg();
            if (AITag.pollIntervalCounter >= AITag.pollAmount) {
                clearInterval(AITag.pollInterval);
                AITag.pollInterval = ""
            }
        }
        ;
        this.go = function(callback) {
            if (AITag.loadState == "post-startup") {
                AITag.loadCallBack = callback;
                AITag.main.apply(AITag)
            } else {
                throw "AI logic failed to continue. Load State is " + AITag.state
            }
        }
        ;
        function isCampaignURLEmpty() {
            return campaignUrlData === "" || campaignUrlData === ""
        }
        function getURLMatchData(key) {
            return key.split("%rtp%")
        }
        var notAllowed = ["widget"]
          , ZONE_IDS_KEY = "zoneIds"
          , CAMPAIGN_IDS_KEY = "campaignIds";
        function extractHostname(key) {
            var hostname;
            if (key.indexOf("://") > -1) {
                hostname = key.split("/")[2]
            } else {
                hostname = key.split("/")[0]
            }
            hostname = hostname.split(":")[0];
            hostname = hostname.split("?")[0];
            return hostname
        }
        function compute(key) {
            var hostname = extractHostname(key);
            return key.replace(hostname, hostname.toLowerCase())
        }
        this.populatePageCampaignData = function() {
            var currentPageZones = [];
            if (!isCampaignURLEmpty()) {
                var matchData, isMatch;
                var currentURL = getCurrentUrlAsArray();
                for (var key in campaignUrlData) {
                    matchData = getURLMatchData(compute(key));
                    if (matchData.length > 1) {
                        isMatch = isURLMatch(currentURL, matchData[0], matchData[1]);
                        if (!containsWildcard(matchData[0]) && isMatch) {
                            currentPageZones = concatArray(currentPageZones, campaignUrlData[key][ZONE_IDS_KEY], notAllowed);
                            currentPageAllowedCampaigns = concatArray(currentPageAllowedCampaigns, campaignUrlData[key][CAMPAIGN_IDS_KEY])
                        } else {
                            if (isMatch) {
                                currentPageAllowedZones = concatArray(currentPageAllowedZones, campaignUrlData[key][ZONE_IDS_KEY], notAllowed);
                                currentPageAllowedCampaigns = concatArray(currentPageAllowedCampaigns, campaignUrlData[key][CAMPAIGN_IDS_KEY])
                            }
                        }
                    } else {
                        currentPageAllowedZones = concatArray(currentPageAllowedZones, campaignUrlData[key][ZONE_IDS_KEY], notAllowed);
                        allPagesAllowedCampaigs = concatArray(allPagesAllowedCampaigs, campaignUrlData[key][CAMPAIGN_IDS_KEY])
                    }
                }
            }
            potentialCampaignExist = currentPageZones.length > 0;
            currentPageAllowedZones = concatArray(currentPageAllowedZones, currentPageZones, notAllowed);
            return currentPageAllowedZones.length > 0
        }
        ;
        this.hideInzoneElements = function() {
            if (AITag.getConfig("inZoneBlockingTimeout") != 0 && !isCampaignURLEmpty()) {
                logMessage("hidden elmenets, setting timeout to: " + AITag.getConfig("inZoneBlockingTimeout"));
                var index;
                var css = "";
                for (index = 0; index < currentPageAllowedZones.length; ++index) {
                    if (index < currentPageAllowedZones.length - 1) {
                        css += "#" + currentPageAllowedZones[index] + ","
                    } else {
                        css += "#" + currentPageAllowedZones[index]
                    }
                }
                css += " { visibility: hidden; }";
                var head = document.head || document.getElementsByTagName("head")[0];
                var style = document.createElement("style");
                style.type = "text/css";
                style.id = "rtpInzoneIdsStyle";
                if (style.styleSheet) {
                    style.styleSheet.cssText = css
                } else {
                    style.appendChild(document.createTextNode(css))
                }
                head.appendChild(style);
                AITag.showInzoneElementsTimeOut = setTimeout(function() {
                    AITag.showInzoneElements(true)
                }, AITag.getConfig("inZoneBlockingTimeout"))
            }
        }
        ;
        this.showInzoneElements = function(forceShow) {
            if (AITag.getConfig("inZoneBlockingTimeout") != 0 && !isCampaignURLEmpty()) {
                var style = document.getElementById("rtpInzoneIdsStyle");
                if (style != null && typeof style != "undefined") {
                    var hiddenExists = false;
                    if (!forceShow) {
                        for (var index = 0; index < currentPageAllowedZones.length; ++index) {
                            if (document.getElementById(currentPageAllowedZones[index])) {
                                hiddenExists = true
                            }
                        }
                    }
                    if (forceShow || hiddenExists) {
                        logMessageTime("showing hidden elements. Show by timeout: " + forceShow + ". Time: ", new Date().getTime());
                        hiddenElementsShown = true;
                        var version = getInternetExplorerVersion();
                        if (version > -1 && version < 11) {
                            var cssText = style.styleSheet.cssText;
                            cssText = cssText.replace(/hidden/g, "visible");
                            style.styleSheet.cssText = cssText
                        } else {
                            var cssText = style.innerHTML;
                            cssText = cssText.replace("hidden", "visible");
                            style.innerHTML = cssText
                        }
                        if (AITag.showInzoneElementsTimeOut) {
                            clearTimeout(AITag.showInzoneElementsTimeOut)
                        }
                    }
                }
            }
        }
    }
    ;
    window.AITag = AITag;
    var inzoneExist = AITag.populatePageCampaignData();
    if (inzoneExist) {
        AITag.hideInzoneElements()
    }
    try {
        var isDebugCookieVal = AITag.getCookie("db_rtp");
        if (isDebugCookieVal) {
            isDebug = (isDebugCookieVal === "true")
        }
    } catch (e) {}
    if (window.rtp) {
        var aid = window.rtp && window.rtp.a;
        if (aid !== undefined && aid != null) {
            AITag.setConfig("accountId", aid)
        }
        var dl = window.rtp && window.rtp.l;
        if (dl !== undefined && dl != null) {
            AITag.setConfig("domainLevel", dl)
        }
    }
    if (window.self !== window.top) {
        if (location && location !== top.location) {
            AITag.disableTag(true)
        }
    }
    try {
        var pollCampaign = null;
        var previewUUID = AITag.extractURLParameter("rtp.cuid");
        if (pollCampaign != null) {} else {
            if (previewUUID != null) {
                AITag.disableTag();
                AITag.updateInitialConfig.apply(AITag)
            }
        }
    } catch (e) {}
    try {
        window.aiq = window.aiq || [];
        if (window.aiq) {
            var aiqLength = aiq.length;
            while (aiqLength) {
                aiqLength--;
                var param = aiq.shift();
                var method = param.shift();
                AITag[method].apply(AITag, param)
            }
            aiq.push = function(params) {
                var method = params.shift();
                AITag[method].apply(AITag, params)
            }
        }
    } catch (e) {}
    doRobotCheck();
    doPrivacyCheckup();
    try {
        window.AIConfig = window.AIConfig || {};
        var startup = (typeof AITag.getConfig("startup") != "undefined" && AITag.getConfig("startup") != null) ? AITag.getConfig("startup") : true;
        var isExcludeUrl = AITag.checkExcludeUrls.apply(AITag);
        if (isExcludeUrl) {
            AITag.disableTag()
        }
        var rtpExists = (window.rtp && window.rtp.a);
        if (!rtpExists && startup && !isExcludeUrl) {
            AITag.cookiesCleanup.apply(AITag);
            AITag.startup.apply(AITag)
        }
    } catch (e) {
        console.log("tag error: " + e)
    }
}
)();
(function(J) {
    var K = "widget"
      , G = "tab"
      , i = "content"
      , c = "insightera_widget_button"
      , r = "insightera_widget_container"
      , k = "insightera_widget_content"
      , e = "insightera-widget"
      , v = "insightera-widget-"
      , F = "insightera-widget-tab"
      , l = "insightera-widget-content"
      , y = "widget-1.2.css"
      , n = "http://rtp-static.marketo.com/rtp/libs"
      , s = "https://rtp-static.marketo.com/rtp/libs"
      , d = "/black_01.png"
      , I = "/black_02.png"
      , a = {
        widgetFontColor: "color",
        widgetPositionTop: "top",
        widgetHide: "display",
        tabColor: "backgroundColor",
        tabOpacity: "opacity",
        contentColor: "backgroundColor",
        contentOpacity: "opacity",
        contentMinHeight: "min-height",
        contentPadding: "padding",
        widgetHeight: "height",
        widgetWidth: "width"
    }
      , H = ["widgetTabTooltip", "widgetTabText", "widgetHide", "widgetTabImage", "tabHeight", "tabWidth"]
      , D = null
      , o = (("https:" == document.location.protocol) ? "https" : "http")
      , b = ((o == "http") ? n : s)
      , C = "/arrows/1/"
      , p = {
        widgetFontColor: "white",
        widgetPositionTop: "50%",
        widgetPositionSide: "right",
        widgetDelay: 0,
        showOnType: "PAGELOAD",
        widgetButtonType: "ARROW",
        widgetButtonColor: "rgb(255, 255, 255)",
        widgetButtonSVGTemplate: "arrow1.svg",
        widgetButtonOpenImgUrl: "",
        widgetButtonCloseImgUrl: "",
        widgetButtonText: "",
        widgetButtonTextColor: "rgb(255, 255, 255)",
        widgetButtonTextSize: "12",
        widgetButtonTextAlignType: "VERTICAL",
        dialogAnimationInEffectType: "fadeIn",
        dialogAnimationOutEffectType: "fadeIn",
        dialogAnimationInDuration: 1000,
        dialogAnimationOutDuration: 1000,
        widgetMaxPosition: null,
        widgetOpenKey: "",
        widgetOpen: true,
        widgetMinimize: false,
        widgetHide: false,
        widgetAutoWidth: false,
        widgetFitWidth: false,
        widgetHeight: "",
        widgetWidth: ""
    }
      , u = {
        tabColor: "rgba(0,0,0,0.6)",
        tabImageClosed: b + d,
        tabImageOpened: b + I,
        tabTooltip: "",
        tabHeight: "59px",
        tabWidth: " 35px"
    }
      , t = {
        contentColor: "rgba(0,0,0,0.6)",
        contentMinHeight: "55px",
        contentPadding: "0.2em"
    }
      , N = null
      , m = null
      , h = null
      , B = function() {
        var Q = document.createElement("div");
        Q.style.cssText = "background-color:rgba(150,255,150,.5)";
        if (!!~("" + Q.style.backgroundColor).indexOf("rgba")) {
            return true
        }
        return false
    }
      , z = function(Q) {
        if (Q && Q.indexOf("http") == -1 && Q.indexOf("https") == -1) {
            Q = o + "//" + Q
        }
        return Q
    }
      , O = function(R) {
        var T;
        var Q = ""
          , U = "";
        var S = 0;
        if (R.indexOf("rgba") == -1) {
            T = R.replace("rgb(", "").replace(")", "").replace(" ", "").split(",")
        } else {
            T = R.replace("rgba(", "").replace(")", "").replace(" ", "").split(",")
        }
        if (T.length == 3) {
            Q += "#ff"
        } else {
            Q += Math.floor(T[3] * 255).toString(16);
            if (Q.length == 1) {
                Q = "#0" + Q
            } else {
                Q = "#" + Q
            }
        }
        while (S < 3) {
            U = (T[S] * 1).toString(16);
            U = U.length == 1 ? "0" + U : U;
            Q += U;
            S++
        }
        return Q
    }
      , A = function(S, R, Q) {
        if (R.addEventListener) {
            R.addEventListener(S, Q, true)
        } else {
            if (R.attachEvent) {
                R.attachEvent("on" + S, Q)
            } else {
                R[S] = Q
            }
        }
    }
      , g = function(S, Q, R) {
        if (S.removeEventListener) {
            S.removeEventListener(Q, R, false)
        }
        if (S.detachEvent) {
            S.detachEvent("on" + Q, R)
        }
    }
      , L = function(Q) {
        return {
            width: Q.offsetWidth,
            height: Q.offsetHeight
        }
    }
      , w = function(S) {
        var Q = document.getElementsByTagName("head")[0];
        var R = document.createElement("link");
        if (S.indexOf("http") == -1 && S.indexOf("https") == -1) {
            S = o + "://" + S
        }
        R.type = "text/css";
        R.href = S;
        R.rel = "stylesheet";
        Q.appendChild(R)
    }
      , j = function() {
        if (AITag.jQuery) {
            D = AITag.jQuery
        }
    }
      , x = function() {
        w(b + "/" + y)
    }
      , E = function() {
        if (!N) {
            N = document.createElement("div");
            N.setAttribute("id", c);
            N.setAttribute("class", e + " " + F + " " + v + p.widgetPositionSide);
            N.style.display = "none";
            document.body.appendChild(N);
            A("click", N, function() {
                var S = h.style.display == "none";
                var R = document.getElementById("tabImageOpened");
                if (R) {
                    R.setAttribute("aria-expanded", S ? "true" : "false")
                }
                var T = document.getElementById("tabImageClosed");
                if (T) {
                    T.setAttribute("aria-expanded", S ? "true" : "false")
                }
                var Q;
                if (S) {
                    Q = function() {
                        showWidgetCampaign();
                        if (R) {
                            R.focus()
                        }
                    }
                } else {
                    Q = function() {
                        hide();
                        if (T) {
                            T.focus()
                        }
                    }
                }
                setTimeout(Q, 100)
            })
        }
        if (!m) {
            m = document.createElement("div");
            m.setAttribute("id", k);
            m.setAttribute("class", l);
            h = document.createElement("div");
            h.setAttribute("id", r);
            h.setAttribute("class", e + " " + v + p.widgetPositionSide);
            h.appendChild(m);
            h.style.display = "none";
            h.style.zIndex = 9000;
            document.body.appendChild(h)
        }
    }
      , q = function(Q) {
        for (property in Q) {
            if (Q.hasOwnProperty(property)) {
                if (property == "tabImageClosed" || property == "tabImageOpened") {
                    u[property] = z(Q[property])
                } else {
                    if (Q[property] != null && typeof Q[property] != "undefined") {
                        if (p.hasOwnProperty(property)) {
                            p[property] = Q[property];
                            if (property == "widgetPositionSide" && (Q[property] == "bottom" || Q[property] == "top")) {
                                a.widgetPositionTop = "left"
                            }
                        } else {
                            if (u.hasOwnProperty(property)) {
                                u[property] = Q[property]
                            } else {
                                if (t.hasOwnProperty(property)) {
                                    t[property] = Q[property]
                                }
                            }
                        }
                    }
                }
            }
        }
    }
      , f = function(S) {
        setButtonWidthHeight();
        if (S) {
            P("tabImageOpened");
            P("insightera_widget_container")
        }
        function U(ag) {
            var aa = h.style.display;
            var ab = h.style.left;
            var ah = h.style.right;
            var am = h.style.bottom;
            var ae = h.style.top;
            h.style.left = "";
            h.style.bottom = "";
            h.style.right = "";
            h.style.top = "";
            h.style.display = "block";
            var Y = L(h);
            h.style.display = aa;
            h.style.left = ab;
            h.style.right = ah;
            h.style.bottom = am;
            h.style.top = ae;
            var Z = (ag == "left" || ag == "right");
            var af = Z ? J.innerHeight : J.innerWidth;
            var ak = af * (p.widgetMaxPosition != null ? p.widgetMaxPosition : (parseInt(p.widgetPositionTop.replace("%", "")))) / 100;
            var ad = p.widgetButtonType == "TEXT" && p.widgetButtonTextAlignType == "VERTICAL";
            var aj = L(N);
            var an = getInternetExplorerVersion() > -1 || navigator.userAgent.indexOf("Safari") > -1;
            var ac = S ? p.dialogAnimationInEffectType : p.dialogAnimationOutEffectType;
            if ((ac == "BLIND" || ac == "SLIDE") && an) {
                if (Z) {
                    var al = parseInt(p.widgetPositionTop.replace("%", ""));
                    h.style.top = J.innerHeight / 100 * al + "px";
                    if (ag == "right") {
                        h.style.left = J.innerWidth - Y.width - (ad ? aj.height : aj.width) + "px"
                    }
                } else {
                    if (ag == "bottom") {
                        h.style.top = J.innerHeight - Y.height - (ad ? aj.width : aj.height) + "px"
                    }
                }
            }
            var ai = ak + (Z ? Y.height : Y.width);
            if (ai > af) {
                if (Z) {
                    h.style.top = ak - Y.height + (ad ? aj.width : aj.height) + "px"
                } else {
                    h.style.left = ak - Y.width + (ad ? aj.height : aj.width) + "px"
                }
            }
        }
        function R() {
            var ae = p.widgetHeight;
            var Y = p.widgetWidth;
            var aj = p.widgetAutoWidth;
            var ag = p.widgetFitWidth;
            var ai = (J.innerWidth > 0) ? J.innerWidth : screen.width;
            var ad = (J.innerHeight > 0) ? J.innerHeight : screen.height;
            var ah = p.widgetPositionSide;
            var ab = (ah == "left" || ah == "right");
            var af = ab ? N.offsetWidth : 0;
            var aa = ab ? 0 : N.offsetHeight;
            var ac = 16;
            if (ae != undefined) {
                h.style.height = ae + "px"
            }
            if (aj) {
                h.style.width = "auto"
            } else {
                if (ag) {
                    if (p.widgetButtonType == "TEXT") {
                        if (p.widgetButtonTextAlignType == "VERTICAL") {
                            if (!ab) {
                                aa = N.offsetWidth
                            } else {
                                af = N.offsetHeight
                            }
                        }
                        if (p.widgetButtonTextAlignType != "VERTICAL" && ab) {
                            af = N.offsetWidth
                        }
                    }
                    var Z = ac * parseFloat(t.contentPadding.replace("em", ""));
                    ad = ad - aa - Math.ceil(Z * 2);
                    ai = ai - af - Math.ceil(Z * 2);
                    h.style.width = ai + "px";
                    h.style.height = ad + "px"
                } else {
                    if (Y != undefined) {
                        h.style.width = Y + "px"
                    }
                }
            }
        }
        if (D) {
            var Q = D("#insightera_widget_container");
            var X = p.widgetPositionSide;
            var T = S ? p.dialogAnimationInEffectType : p.dialogAnimationOutEffectType;
            var W = parseInt(S ? p.dialogAnimationInDuration : p.dialogAnimationOutDuration);
            var V = getDirectionForWidgetSide(T, X, S);
            R();
            U(X);
            if (T == "DROP") {
                N.className += " drop"
            }
            if (T != null && T != "NO_EFFECT") {
                if (T != "FADE_IN") {
                    T = T.toLowerCase();
                    if (S) {
                        Q.show(T, {
                            direction: V
                        }, W)
                    } else {
                        Q.hide(T, {
                            direction: V
                        }, W)
                    }
                } else {
                    if (S) {
                        Q.show(W)
                    } else {
                        Q.hide(W)
                    }
                }
                M(S);
                return
            }
        }
        h.style.display = S ? "block" : "none";
        M(S)
    }
      , M = function(R) {
        var Q = document.getElementById("tabImageOpened");
        var T = document.getElementById("tabImageClosed");
        if (typeof Q != "undefined" && Q != null && typeof T != "undefined" && T != null) {
            var S = (getInternetExplorerVersion() > 0 && getInternetExplorerVersion() <= 10) ? "" : "flex";
            Q.style.display = R ? S : "none";
            T.style.display = R ? "none" : S
        }
    }
      , P = function(Q) {
        var S = function(T) {
            if (T.keyCode === 27) {
                document.getElementById(Q).removeEventListener("focus", R, true);
                f(false)
            }
        };
        var R = function() {
            J.addEventListener("keydown", S, true)
        };
        document.getElementById(Q).addEventListener("focus", R, true);
        document.getElementById(Q).addEventListener("blur", function() {
            J.removeEventListener("keydown", S, true)
        }, true)
    };
    setButtonWidthHeight = function() {
        var T = p.widgetPositionSide;
        var R = (T == "left" || T == "right");
        N.style[R ? "top" : "left"] = "50%";
        N.style.display = "block";
        var V = L(N);
        N.style[R ? "top" : "left"] = p.widgetPositionTop;
        var X = (R ? V.height : V.width);
        h.style[T] = (R ? V.width : V.height) + "px";
        if (p.widgetButtonType == "TEXT" && p.widgetButtonTextAlignType == "VERTICAL") {
            X = (R ? V.width : V.height);
            h.style[T] = (R ? V.height : V.width) + "px";
            var W = (T == "right" ? V.width - V.height : 0);
            var U = (T == "bottom" ? -V.height + "px" : "-100%");
            N.style.transform = "rotate(270deg) translate(" + U + ", " + W + "px)";
            N.style.transformOrigin = "0 0"
        }
        var S = (1 - X / (R ? J.innerHeight : J.innerWidth)) * 100;
        if (S < p.widgetPositionTop.replace("%", "")) {
            p.widgetPositionTop = S + "%";
            var Q = (R ? J.innerHeight : J.innerWidth) * (S / 100);
            N.style[R ? "top" : "left"] = Q + "px";
            p.widgetMaxPosition = S
        }
    }
    ,
    getDirectionForWidgetSide = function(S, R, Q) {
        if (typeof S != "undefined" && (S == "BLIND" || S == "SLIDE" || S == "DROP")) {
            switch (R) {
            case "top":
                return "up";
            case "bottom":
                return "down";
            case "left":
                return "left";
            case "right":
                return "right";
            default:
                return null
            }
        } else {
            return null
        }
    }
    ,
    updateWidget = function() {
        for (var aa in p) {
            if (p.hasOwnProperty(aa)) {
                if (H.indexOf(aa) == -1) {
                    N.style[a[aa]] = p[aa];
                    h.style[a[aa]] = p[aa]
                }
            }
        }
        for (var aa in u) {
            if (u.hasOwnProperty(aa)) {
                if (H.indexOf(aa) == -1) {
                    if (aa == "tabColor" && u[aa].indexOf("rgba") != -1 && !B()) {
                        continue
                    }
                    N.style[a[aa]] = u[aa]
                }
            }
        }
        if (u.tabColor.indexOf("rgba") != -1 && !B()) {
            var ad = O(u.tabColor);
            N.style.filter = "progid:DXImageTransform.Microsoft.gradient(startColorstr=" + ad + ", endColorstr=" + ad + ")"
        }
        for (var aa in t) {
            if (t.hasOwnProperty(aa) && H.indexOf(aa) == -1) {
                if (aa == "contentColor" && t[aa].indexOf("rgba") != -1 && !B()) {
                    continue
                }
                h.style[a[aa]] = t[aa]
            }
        }
        if (t.contentColor.indexOf("rgba") != -1 && !B()) {
            var ad = O(t.contentColor);
            h.style.filter = "progid:DXImageTransform.Microsoft.gradient(startColorstr=" + ad + ", endColorstr=" + ad + ")"
        }
        N.setAttribute("title", u.tabTooltip);
        if (!p.widgetHide) {
            N.style.display = "block"
        }
        var X = "none";
        var ac = "flex";
        if (p.widgetOpen == false) {
            X = "flex";
            ac = "none"
        }
        if (u.tabImageClosed == "" || u.tabImageClosed === undefined) {
            N.style.height = u.tabHeight;
            N.style.width = u.tabWidth
        } else {
            var U, R;
            var T = '<a role="button" href="javascript:void(0);" id="tabImageOpened" aria-label="close pop-up"  aria-haspopup="true" aria-expanded="true" style="display:' + ac + ';"';
            var V = '<a role="button" href="javascript:void(0);" id="tabImageClosed" aria-label="open pop-up" aria-haspopup="true" aria-expanded="false" style="display:' + X + ';"';
            switch (p.widgetButtonType) {
            case "ARROW":
                var S = p.widgetButtonColor;
                var W;
                R = b + C + Y(p.widgetPositionSide) + "-" + p.widgetButtonSVGTemplate;
                makeGetRequest(R, function(ae) {
                    W = ae;
                    var af = new RegExp("#000000","g");
                    W = W.replace(af, S);
                    N.innerHTML += T + ">" + W + "</a>"
                });
                U = b + C + p.widgetPositionSide + "-" + p.widgetButtonSVGTemplate;
                makeGetRequest(U, function(ae) {
                    W = ae;
                    var af = new RegExp("#000000","g");
                    W = W.replace(af, S);
                    N.innerHTML += V + ">" + W + "</a>"
                });
                break;
            case "CUSTOM":
                U = p.widgetButtonCloseImgUrl;
                R = p.widgetButtonOpenImgUrl;
                if (p.widgetOpen) {
                    N.innerHTML += T + '><img src="' + R + '"/></a>';
                    N.innerHTML += V + '><img src="' + U + '"/></a>'
                } else {
                    N.innerHTML += V + '><img src="' + U + '"/></a>';
                    N.innerHTML += T + '><img src="' + R + '"/></a>'
                }
                break;
            case "TEXT":
                var ab = p.widgetButtonText;
                var Q = p.widgetButtonTextSize;
                var Z = p.widgetButtonTextColor;
                N.innerHTML += V.substring(0, V.length - 1) + "padding:5px;white-space: nowrap;text-decoration:none;padding-left:10px;padding-right:10px;font-size: " + Q + "px;color: " + Z + ';">' + ab + "</a>";
                N.innerHTML += T.substring(0, T.length - 1) + "padding:5px;white-space: nowrap;text-decoration:none;padding-left:10px;padding-right:10px;font-size: " + Q + "px;color: " + Z + ';">' + ab + "</a>";
                break;
            default:
                if (p.widgetPositionSide == "right") {
                    U = u.tabImageClosed;
                    R = u.tabImageOpened
                } else {
                    U = u.tabImageOpened;
                    R = u.tabImageClosed
                }
                N.innerHTML += V + '><img src="' + U + '"/></a>';
                if (typeof u.tabImageOpened != "undefined") {
                    N.innerHTML += T + '><img src="' + R + '"/></a>'
                }
                break
            }
            N.style.display = "none";
            function Y(ae) {
                if (ae !== undefined) {
                    switch (ae) {
                    case "left":
                        return "right";
                    case "right":
                        return "left";
                    case "top":
                        return "bottom";
                    case "bottom":
                        return "top"
                    }
                }
            }
        }
    }
    ,
    initialize = function(Q) {
        x();
        q(Q);
        E();
        updateWidget()
    }
    ,
    showWidgetCampaign = function() {
        if (ITLocalStorageAPI.supports()) {
            ITLocalStorageAPI.store(p.widgetOpenKey, true)
        }
        if (D == null) {
            j()
        }
        var R = N.style.display;
        N.style.display = "block";
        var S = L(N);
        N.style.display = R;
        if (document.readyState !== "complete" || S.width == 0 || S.height == 0) {
            var T = document.getElementById("tabImageOpened");
            var Q = new Image();
            if (!p.widgetOpen) {
                T = document.getElementById("tabImageClosed")
            }
            if (T != null && typeof T.src != "undefined") {
                Q.src = T.src
            } else {
                setTimeout(function() {
                    f(true)
                }, 1000)
            }
            Q.onload = function() {
                f(true)
            }
        } else {
            f(true)
        }
    }
    ,
    hide = function() {
        if (document.getElementsByClassName("ui-effects-wrapper").length > 0) {
            return
        }
        f(false);
        if (ITLocalStorageAPI.supports()) {
            ITLocalStorageAPI.store(p.widgetOpenKey, false)
        }
    }
    ,
    injectContent = function(Q) {
        m.innerHTML = Q
    }
    ,
    execute = function(Q) {
        try {
            return Q()
        } catch (R) {
            if (J.console && J.console.log) {
                J.console.log("widget Error: ", R)
            }
        }
    }
    ,
    destroy = function() {
        var Q = document.getElementsByTagName("body")[0];
        if (N && Q) {
            Q.removeChild(N);
            N = null
        }
        if (h && Q) {
            Q.removeChild(h);
            h = null;
            m = null
        }
    }
    ,
    InsighteraWidget = {
        init: function(Q) {
            execute(function() {
                initialize(Q)
            })
        },
        injectContent: function(Q) {
            execute(function() {
                injectContent(Q)
            })
        },
        show: function() {
            execute(function() {
                showWidgetCampaign()
            })
        },
        hide: function() {
            execute(function() {
                hide()
            })
        },
        restore: function() {
            execute(function() {
                if (ITLocalStorageAPI.supports()) {
                    var Q = ITLocalStorageAPI.getValue(p.widgetOpenKey);
                    if (typeof Q != "undefined" && Q != null) {
                        if (Q == "true") {
                            showWidgetCampaign()
                        } else {
                            hide()
                        }
                    } else {
                        if (!p.widgetMinimize) {
                            showWidgetCampaign()
                        } else {
                            hide()
                        }
                    }
                } else {
                    showWidgetCampaign()
                }
            })
        },
        getContentId: function() {
            return k
        },
        isInitilized: function() {
            if (N && m && h) {
                return true
            }
            return false
        },
        destroy: function() {
            execute(function() {
                destroy()
            })
        }
    };
    if (J.InsighteraWidget === undefined) {
        J.InsighteraWidget = InsighteraWidget
    }
}
)(window);
function getInternetExplorerVersion() {
    var c = -1;
    if (navigator.appName == "Microsoft Internet Explorer") {
        var a = navigator.userAgent;
        var b = new RegExp("MSIE ([0-9]{1,}[.0-9]{0,})");
        if (b.exec(a) != null) {
            c = parseFloat(RegExp.$1)
        }
    } else {
        if (navigator.appName == "Netscape") {
            var a = navigator.userAgent;
            var b = new RegExp("Trident/.*rv:([0-9]{1,}[.0-9]{0,})");
            if (b.exec(a) != null) {
                c = parseFloat(RegExp.$1)
            }
        }
    }
    return c
}
function consoleMessage(a) {
    if (console && console.log) {
        console.log(a)
    }
}
function makeGetRequest(a, b) {
    if (a === undefined || a == null || a == "") {
        return
    }
    if (a.indexOf("http") != 0 && a.indexOf("https") != 0) {
        a = AITag.getConfig("protocol") + "://" + a
    }
    var c = createCORSRequest("GET", a);
    if (!c) {
        consoleMessage("CORS not supported");
        return
    }
    c.onload = function() {
        var d = c.responseText;
        if (typeof b == "function") {
            b(d)
        }
    }
    ;
    c.onerror = function() {}
    ;
    c.send()
}
function createCORSRequest(d, a) {
    var c = new XMLHttpRequest();
    if ("withCredentials"in c) {
        try {
            c.open(d, a, true)
        } catch (b) {
            consoleMessage(b)
        }
    } else {
        if (typeof XDomainRequest != "undefined") {
            c = new XDomainRequest();
            c.open(d, a)
        } else {
            c = null
        }
    }
    return c
}
;if (!Object.keys) {
    Object.keys = (function() {
        var c = Object.prototype.hasOwnProperty
          , d = !({
            toString: null
        }).propertyIsEnumerable("toString")
          , b = ["toString", "toLocaleString", "valueOf", "hasOwnProperty", "isPrototypeOf", "propertyIsEnumerable", "constructor"]
          , a = b.length;
        return function(g) {
            if (typeof g !== "object" && (typeof g !== "function" || g === null)) {
                throw new TypeError("Object.keys called on non-object")
            }
            var e = [], h, f;
            for (h in g) {
                if (c.call(g, h)) {
                    e.push(h)
                }
            }
            if (d) {
                for (f = 0; f < a; f++) {
                    if (c.call(g, b[f])) {
                        e.push(b[f])
                    }
                }
            }
            return e
        }
    }())
}
(function() {
    var i = window
      , y = i.document
      , af = i.encodeURIComponent
      , bN = i.decodeURIComponent;
    setTimeoutFunction = i.setTimeout;
    parseInt = i.parseInt;
    var aW = "send"
      , bo = "get"
      , a7 = "set"
      , bs = "add"
      , bE = "setAccount"
      , l = "getTracker"
      , e = "checkPattern"
      , m = "campaign"
      , aM = "rcmd"
      , cg = "view"
      , am = "event"
      , Q = "visitor"
      , aq = "redirect"
      , a = "ucontext"
      , j = "customVar"
      , Y = "toCmpTl"
      , an = "userContextParams"
      , K = "settings"
      , G = "userContextReady"
      , O = new RegExp("^" + j + "([1-5]{1})$")
      , ck = [aW, bo, a7, bs]
      , a4 = [cg, am, m, Q, aM, aq, a, Y, an, K]
      , U = [bE, l]
      , bx = [G]
      , aS = [cg, am, m, aM]
      , bI = ["anonymizeIP"]
      , p = false
      , n = "rtp"
      , ca = "trackerData"
      , bv = "trackers"
      , R = "aid"
      , a3 = "name"
      , bF = "userContext"
      , c = "length"
      , b2 = "shift"
      , N = "apply"
      , b1 = "push"
      , cD = "location"
      , cd = "href"
      , ai = "indexOf"
      , H = "protocol"
      , bw = "slice"
      , bZ = "call"
      , cx = "object"
      , z = "prototype"
      , cw = "hasOwnProperty"
      , S = "function"
      , bM = "hostname"
      , bm = "match"
      , ap = "createElement"
      , k = "unshift"
      , aE = "toLowerCase"
      , b0 = "getElementsByTagName"
      , f = "splice"
      , a5 = "split"
      , v = "substring";
    var d = "?"
      , x = "="
      , b9 = "&"
      , bW = "http"
      , aN = "https"
      , aR = ((aN + ":" == y[cD][H]) ? aN : bW);
    aR = aR + "://",
    exp_2_year = new Date();
    exp_2_year.setTime(exp_2_year.getTime() + (730 * 24 * 60 * 60 * 1000));
    var cB = "rtp"
      , cp = "aiq";
    var bj = i.AITag
      , ay = "setConfig"
      , aT = "getConfig"
      , bV = "startup"
      , cc = "updateInitialConfig"
      , aK = "sendClick"
      , u = "accountId"
      , bz = "pollMsg"
      , A = "sendEvent"
      , cv = "addAssetCollector"
      , bQ = "activateRecommendation"
      , t = "runAnalyticsIntegration"
      , bB = "loadCss"
      , aG = "runAsyncQueue"
      , bf = "afterAllJQueryLoaded"
      , M = "getRCMD"
      , cy = "rh"
      , cf = "loadJQuery"
      , bu = "cookiesCleanup"
      , bU = "trwsa.sid"
      , ad = "trwv.uid"
      , ac = "requestInterval"
      , aw = "isNewVisitor"
      , bk = "addVisitorId"
      , b7 = "userContextAPI"
      , h = "pollingDelay"
      , br = "pollingPerPage"
      , bY = "activateCampaignStorage"
      , b = (bj && bj[aT](ac) != null) ? bj[aT](ac) : 50
      , F = (bj && bj[aw]()) ? true : false
      , cr = (bj && bj[aT](b7)) ? true : false
      , bR = true
      , C = 1
      , aU = null
      , aQ = (bj && bj[aT](br) != null) ? bj[aT](br) : 1
      , bp = (bj && bj[aT](h) != null) ? (bj[aT](h) * 1000) : 50
      , J = [];
    var aY = "nld1rtp1.marketo.com/gw1"
      , aD = null
      , aJ = "/rtp/api/v1_1/"
      , a2 = "visitor"
      , a6 = "sid"
      , g = "aid";
    var ch = null
      , T = null
      , bd = null
      , aV = false
      , be = []
      , bi = false
      , au = false
      , cb = (i[cB] && i[cB].hb) ? i[cB].hb : false
      , bH = false;
    var aC = i.IeraPreview && i.IeraPreview.previewShowed || (typeof i.rtp !== "undefined" && i.rtp.d);
    var aF = i.ITLocalStorageAPI;
    p = i[cB] && i[cB].d;
    var ae = i[cp] = i[cp] || [];
    if (F && !aC) {
        bj && bj[bk]
    }
    if (!aC) {
        ae[b1]([cc]);
        ae[b1]([cf, function() {
            ae[b1]([bf])
        }
        ]);
        ae[b1]([bB])
    }
    if (!o() && !aC) {
        ae[b1]([bY]);
        i.rtpRCMD = i.rtpRCMD || function() {
            (i.rtpRCMD.q = i.rtpRCMD.q || []).push(arguments)
        }
        ;
        ae[b1](["initRCMDWidget"])
    }
    function bS(cF, cE, cG) {
        cF[cE] = function() {
            return cG[N](this, arguments)
        }
    }
    function o() {
        return p
    }
    function aI() {
        return (new Date).getTime()
    }
    function al(cE) {
        if (window.console && window.console.log) {
            console.log("RTP message: " + cE)
        }
    }
    function bL() {
        var cE = Array[z][bw][bZ](arguments);
        for (var cF = 0; cF < cE[c]; cF++) {
            if (cE[cF] === undefined || cE[cF] == null || cE[cF] === "") {
                return true
            }
        }
        return false
    }
    function b3(cF) {
        var cE = parseInt(cF);
        if (!isNaN(cE)) {
            return cE
        }
        return null
    }
    function cA(cE) {
        return Object[z].toString[bZ](cE) === "[object Object]"
    }
    function q(cE) {
        return typeof cE === "function"
    }
    function I(cH, cE) {
        var cG = new XMLHttpRequest();
        if ("withCredentials"in cG) {
            try {
                cG.open(cH, cE, true)
            } catch (cF) {
                al(cF)
            }
        } else {
            if (typeof XDomainRequest != "undefined") {
                cG = new XDomainRequest();
                cG.open(cH, cE)
            } else {
                cG = null
            }
        }
        return cG
    }
    function bl(cE, cF) {
        if (cE === undefined || cE == null || cE == "") {
            return
        }
        if (cE[ai](bW) != 0 && cE[ai](aN) != 0) {
            cE = aR + cE
        }
        cE += "&" + aI();
        var cG = I("GET", cE);
        if (!cG) {
            al("CORS not supported");
            return
        }
        cG.onload = function() {
            var cI = cG.responseText;
            var cH = JSON.parse(cI);
            if (cH.status == 200) {
                if (typeof cF == S) {
                    cF(cH)
                }
            } else {
                al(cH.errorMessage)
            }
        }
        ;
        cG.onerror = function() {}
        ;
        cG.send()
    }
    function X(cG) {
        var cF = y[b0]("head")[0] || y.documentElement;
        var cE = y[ap]("script");
        cE.type = "text/javascript";
        cE.async = true;
        if (cG[ai](bW) != 0 && cG[ai](aN) != 0) {
            cG = aR + cG
        }
        cE.src = cG;
        cF.insertBefore(cE, cF.firstChild)
    }
    function D(cE, cG) {
        for (var cF in cG) {
            if (cG[cw](cF)) {
                if (cE == "" || cE == d) {
                    cE += cF + "=" + af(cG[cF])
                } else {
                    cE += "&" + cF + "=" + af(cG[cF])
                }
            }
        }
        return cE
    }
    function bO(cE, cI) {
        if (cE && cI) {
            var cG = i[cD]["search"];
            var cH = "";
            if (cE[ai](d) == -1) {
                cH += d
            }
            cH = D(cH, cI);
            if (cH != "" && cH != d) {
                if (cE[ai]("#") > -1) {
                    var cF = cE[v](0, cE[ai]("#"));
                    if (cG && cG.length > 0) {
                        cF += "&"
                    }
                    cF += cH;
                    cF += cE[v](cE[ai]("#"), cE[c]);
                    cE = cF
                } else {
                    if (cG && cG.length > 0) {
                        cE += "&"
                    }
                    cE += cH
                }
            }
        }
        return cE
    }
    function cz(cG, cE) {
        if (cE && cE === parseInt(cE)) {
            for (var cF = cG[a5]("."); cF.length > cE && cF.length > 2; ) {
                cF.shift()
            }
            return cF.join(".")
        }
        cF = /([^.]+\.[^.]{3,})$/i.exec(cG);
        return cF != null ? cF[1] : (cF = /([^.]+\.[^.]+\.[^.]{2})$/i.exec(cG),
        cF != null ? cF[1] : cG)
    }
    function P() {
        return "xxxxxxxx".replace(/[x]/g, function(cF) {
            var cE = Math.random() * 16 | 0;
            return cE.toString(16)
        })
    }
    function E() {
        var cE = aD;
        if (cE) {
            cE = cE.replace(".", "-")
        }
        return cE + "-" + aI() + "-" + P()
    }
    function cs(cF) {
        var cE = b4.getValue(cF);
        if (cE != null) {
            cE = cE[a5](b4.seperator)
        }
        return cE
    }
    function cl() {
        var cF = null;
        var cE = cs(bU);
        if (cE != null && cE.length > 0) {
            cF = cE[0]
        }
        return cF
    }
    function aO() {
        var cE = cs(ad);
        if (cE == null) {
            newVisitor = true;
            visitorId = E();
            var cF = visitorId + b4.seperator + "0";
            b4.store(ad, cF, exp_2_year)
        }
    }
    function aX() {
        var cF = null;
        var cE = cs(ad);
        if (cE != null && cE.length > 0) {
            cF = cE[0]
        }
        return cF
    }
    var b4 = new function() {
        this.seperator = ":";
        this.store = function(cF, cH, cE, cJ, cI) {
            var cG = cz(i[cD][bM], void 0);
            if (cG == "localhost") {
                cG = null
            }
            y.cookie = cF + "=" + af(cH) + ((cE == null) ? "" : ("; expires=" + cE.toGMTString())) + "; path=" + ((cJ == null) ? "/" : cJ) + ((cG == null) ? "" : ("; domain=" + cG)) + ((cI == true) ? "; secure" : "")
        }
        ;
        this.remove = function(cE, cH, cF, cG) {
            var cJ = new Date();
            cJ.setTime(cJ.getTime() - 1);
            var cI = this.getValue(cE);
            this.store(cE, cI, cJ, cH, cG)
        }
        ;
        this.getValue = function(cG) {
            var cF = null
              , cH = y.cookie[a5](";");
            var cJ = RegExp("^\\s*" + cG + "=\\s*(.*?)\\s*$");
            for (var cE = 0; cE < cH.length; cE++) {
                var cI = cH[cE][bm](cJ);
                if (cI && cI[1]) {
                    cF = bN(cI[1]);
                    break
                }
            }
            return cF
        }
    }
    ;
    function a9(cF) {
        var cE = y[ap]("a");
        cE[cd] = cF;
        cF = cE[cd];
        return cF
    }
    function bg(cE) {
        bi = true;
        if (cE[ai](bW) != 0 && cE[ai](aN) != 0) {
            cE = aR + cE
        }
        i[cD] = cE
    }
    function cq(cE) {
        if (cE !== void 0) {
            for (var cF = 0; cF < cE[c]; cF++) {
                if (typeof cE[cF] === "string") {
                    cE[cF] = cE[cF][aE]()
                }
            }
        }
        return cE
    }
    var B = "rtpUC"
      , bT = "vid"
      , aL = "cv"
      , bD = "ct"
      , cm = "vc"
      , s = 0
      , bC = 6
      , cu = 10
      , bK = 10
      , w = 100
      , W = 1000
      , bh = new RegExp("^" + aL + "([1-5]{1})$")
      , a0 = 0
      , bA = 1
      , a1 = "viewedCampaigns"
      , bn = "clickedCampaigns";
    var co = "ctv"
      , b8 = "ctc";
    var r = "getUserContext"
      , aB = "storeUserContext"
      , ag = "storeCustomVar"
      , bX = "storeCampaing"
      , ba = "validateVisitCount"
      , aH = "validateCampaignId"
      , az = "validateCampaigntype"
      , L = "addVisitorId"
      , bP = "getUserContexParams"
      , ce = "getCampaignVisit";
    var aa = {};
    function ak() {
        return cr
    }
    aa[r] = function() {
        if (aF.supports() && ak()) {
            var cF = aF.getValue(B);
            if (cF !== void 0) {
                cF = JSON.parse(cF);
                var cE = aX();
                if (cF[bT]) {
                    if (cF[bT] === cE) {
                        return cF
                    }
                }
            }
            cF = {};
            return this[L](cF)
        }
        return null
    }
    ;
    aa[aB] = function() {
        if (aF.supports() && arguments[c] > 0 && ak()) {
            var cE = arguments[0];
            if (cE !== undefined && cE !== null && cA(cE)) {
                cE = JSON.stringify(cE);
                aF.store(B, cE)
            }
        }
    }
    ;
    aa[L] = function(cF) {
        if (!cF[bT]) {
            var cE = aX();
            if (cE === void 0 || cE == null) {
                aO()
            }
            cF[bT] = aX()
        }
        return cF
    }
    ;
    aa[ag] = function(cE, cF) {
        if (aF.supports() && ak()) {
            cE = b3(cE);
            if (!bL(cE, cF) && cE > s && cE < bC && cF.length < w) {
                var cG = this[r]();
                if (cG !== null && cA(cG)) {
                    cG[aL + cE] = cF;
                    this[aB](cG)
                }
            }
        }
    }
    ;
    aa[bX] = function(cF, cE, cG) {
        if (aF.supports() && ak()) {
            if (!bL(cF, cE, cG)) {
                cF = b3(cF);
                cE = b3(cE);
                cG = b3(cG);
                if (this[ba](cF) && this[aH](cE) && this[az](cG)) {
                    var cK = this[r]();
                    if (cK !== null && cA(cK)) {
                        var cJ = {};
                        cJ[cE] = cG;
                        if (!cK[bD]) {
                            cK[bD] = {}
                        }
                        var cI = this[ce](cK, cF);
                        for (var cH = 0; cH < cI[c]; cH++) {
                            if (!bL(cI[cH][cE]) && cI[cH][cE] == cG) {
                                return
                            }
                        }
                        if (cI[c] >= bK) {
                            cI[b2]()
                        }
                        cI[b1](cJ);
                        cK[bD][cm + cF] = cI;
                        this[aB](cK)
                    }
                }
            }
        }
    }
    ;
    aa[ce] = function(cH, cE) {
        var cF = cH[bD][cm + cE];
        if (bL(cF)) {
            cF = [];
            var cG = Object.keys(cH[bD]);
            if (cG.length >= cu) {
                delete cH[bD][cG[0]]
            }
        }
        return cF
    }
    ;
    aa[ba] = function(cE) {
        if (cE > 0 && cE < W) {
            return true
        }
        return false
    }
    ;
    aa[aH] = function(cE) {
        if (cE > 0) {
            return true
        }
        return false
    }
    ;
    aa[az] = function(cE) {
        if (cE == 0 || cE == 1) {
            return true
        }
        return false
    }
    ;
    aa[bP] = function() {
        var cE = "", cF;
        var cG = this[r]();
        cF = av(cG);
        cE = D(cE, cF);
        return cE
    }
    ;
    function av(cJ) {
        var cE, cH = {}, cF;
        if (!bL(cJ) && cA(cJ) && ak()) {
            var cG = aX();
            if (cJ[bT] && cJ[bT] == cG) {
                for (var cI in cJ) {
                    if (cJ[cw](cI)) {
                        cE = bh.exec(cI);
                        if (cE) {
                            cH[cI] = cJ[cI]
                        } else {
                            if (cI == bD) {
                                cF = cJ[cI];
                                cH = aP(cF, cH)
                            }
                        }
                    }
                }
            }
        }
        return cH
    }
    function ar(cG, cK) {
        var cE, cI = {}, cF;
        if (ak() && !bL(cK) && cA(cK)) {
            var cH = aX();
            if (cK[bT] && cK[bT] == cH) {
                for (var cJ in cK) {
                    if (cK[cw](cJ)) {
                        cE = bh.exec(cJ);
                        if (cE) {
                            cG[j + cE[1]] = cK[cJ]
                        } else {
                            if (cJ == bD) {
                                cF = cK[cJ];
                                cI = aP(cF, cI);
                                if (cI[co]) {
                                    cG[a1] = cI[co][a5](",")
                                }
                                if (cI[b8]) {
                                    cG[bn] = cI[b8][a5](",")
                                }
                            }
                        }
                    }
                }
            }
        }
        cG.viewedCampaign = V;
        cG.clickedCampaign = ax
    }
    function V(cF) {
        if (bL(cF)) {
            return false
        }
        if (!(cF instanceof Array)) {
            cF = [cF]
        }
        var cE = b5[bF][a1];
        if (bL(cE)) {
            return false
        }
        for (var cG = 0; cG < cF[c]; cG++) {
            if (cE[ai](cF[cG].toString()) > -1) {
                return true
            }
        }
        return false
    }
    function ax(cE) {
        if (bL(cE)) {
            return false
        }
        if (!(cE instanceof Array)) {
            cE = [cE]
        }
        var cF = b5[bF][bn];
        if (bL(cF)) {
            return false
        }
        for (var cG = 0; cG < cE[c]; cG++) {
            if (cF[ai](cE[cG].toString()) > -1) {
                return true
            }
        }
        return false
    }
    function aP(cG, cE) {
        var cM, cF, cH = [], cJ = [];
        for (var cL in cG) {
            if (cG[cw](cL)) {
                cL = cG[cL];
                for (var cI = 0; cI < cL[c]; cI++) {
                    cF = cL[cI];
                    for (var cK in cF) {
                        if (cF[cw](cK)) {
                            if (cF[cK] == a0 && cH[ai](cK) == -1) {
                                cH[b1](cK)
                            } else {
                                if (cF[cK] == bA && cJ[ai](cK) == -1) {
                                    cJ[b1](cK)
                                }
                            }
                        }
                    }
                }
            }
        }
        if (cH[c] > 0) {
            cE[co] = cH.join()
        }
        if (cJ[c] > 0) {
            cE[b8] = cJ.join()
        }
        return cE
    }
    var ah = 5
      , ct = "addTrigger"
      , bb = "trigger";
    var cn = []
      , aj = {};
    var cC = {};
    cC[ct] = function(cF, cG) {
        if (q(cG)) {
            if (cn[ai](cF) > -1 && cG) {
                return cG[N]()
            } else {
                var cE = aj[cF];
                if (!b6(cE)) {
                    cE = []
                }
                if (cE[c] < ah) {
                    cE[b1](cG)
                }
                aj[cF] = cE
            }
        }
    }
    ;
    cC[bb] = function(cF) {
        if (cn[ai](cF) == -1) {
            cn[b1](cF);
            var cE = aj[cF];
            if (b6(cE)) {
                for (var cG = 0; cG < cE[c]; cG++) {
                    try {
                        cE[cG][N]()
                    } catch (cH) {}
                }
            }
        }
    }
    ;
    var ao = {};
    ao.callMethod = function() {
        var cG;
        for (var cF = 0; cF < arguments[c]; cF++) {
            cG = arguments[cF];
            var cI = cG[0];
            if (U[ai](cI) > -1) {
                this.applyRtp(b5, cI, cG)
            } else {
                if (ck[ai](cI) > -1) {
                    var cH = b5.getTracker(n);
                    this.applyRtp(cH, cI, cG)
                } else {
                    if (bx[ai](cI) > -1) {
                        var cE = [][bw][bZ](cG, 0);
                        cE[k]("");
                        this.applyRtp(cC, ct, cE)
                    }
                }
            }
        }
    }
    ;
    ao.applyRtp = function(cF, cG, cE) {
        cE = [][bw][bZ](cE, 1);
        return cF[cG][N](cF, cE)
    }
    ;
    var aZ = {};
    aZ[cg] = function() {
        var cE = i[cD][cd];
        if (arguments[c] > 0) {
            var cF = arguments[0];
            if (typeof cF == cx) {
                cE = bO(cE, cF)
            } else {
                if (typeof cF == "string") {
                    cE = a9(cF)
                }
            }
        }
        cE = af(cE);
        ch = aI();
        ae[b1]([aK, cE, by])
    }
    ;
    aZ[am] = function() {
        if (arguments[c] > 0) {
            var cG = arguments[0];
            if (typeof cG == cx && JSON.stringify(cG).length < 255) {
                var cF = 0;
                for (var cH in cG) {
                    if (cG[cw](cH)) {
                        cF++
                    }
                }
                if (cF < 5) {
                    var cE = ch ? aI() - ch : null;
                    if (ch && cE && cE < b) {
                        setTimeoutFunction(function() {
                            T = aI();
                            rtp(aW, am, cG)
                        }, (b - cE))
                    } else {
                        T = aI();
                        ae[b1]([A, cG])
                    }
                }
            }
        }
    }
    ;
    aZ[m] = function() {
        if (arguments[c] > 0) {
            bH = true;
            var cG = void 0
              , cF = void 0;
            var cE = arguments[0];
            typeof cE == "boolean" ? cF = cE : ((typeof cE == S) ? cG = cE : void 0);
            if (cG) {
                ab(cG, cF)
            } else {
                if (cF) {
                    ab(cG, cF)
                }
            }
        }
    }
    ;
    aZ[Q] = function() {
        if (arguments[c] > 0) {
            var cI = arguments[0];
            if (typeof cI == S) {
                var cF = aR + aY + aJ + a2;
                var cE = aI()
                  , cG = ch ? cE - ch : null
                  , cH = T ? cE - T : null;
                if ((cG && cG < b) || (cH && cH < b)) {
                    setTimeoutFunction(function() {
                        rtp(bo, Q, cI)
                    }, b)
                } else {
                    bJ(cF, cI)
                }
            }
        }
    }
    ;
    aZ[aM] = function() {
        var cI = null;
        if (arguments[c] > 0) {
            var cE = arguments[0]
              , cG = void 0
              , cF = void 0;
            if (arguments.length > 1) {
                cG = arguments[1];
                cF = arguments[2]
            }
            var cH = aI() - ch;
            if (ch && cH < b) {
                setTimeoutFunction(function() {
                    if (cG) {
                        rtp(bo, aM, cE, cG, cF)
                    } else {
                        rtp(bo, aM, cE)
                    }
                }, cH)
            } else {
                if (typeof cE === "boolean" || typeof cE === "function") {
                    ae[b1]([M, cE])
                } else {
                    switch (cE) {
                    case "richmedia":
                        if (arguments.length > 1) {
                            i.rtpRCMD("change", arguments[1], arguments[2])
                        } else {
                            i.rtpRCMD("get")
                        }
                        break;
                    default:
                        throw cE + " isn't defined"
                    }
                }
            }
        }
    }
    ;
    aZ[aq] = function() {
        try {
            if (arguments[c] > 2) {
                var cL = arguments[0]
                  , cK = arguments[1]
                  , cE = arguments[2]
                  , cG = false;
                var cM = i[cD][cd];
                var cH = cM[ai](cE);
                if (cH != -1 && cH < 8) {
                    return
                }
                if (arguments[c] > 3) {
                    cG = arguments[3]
                }
                if (cb && !au) {
                    bc()
                }
                if (aV) {
                    if (bd == null) {
                        be[b1]([cL, cK, cE, cG])
                    } else {
                        Z(bd, cL, cK, cE, cG)
                    }
                } else {
                    aV = true;
                    be[b1]([cL, cK, cE, cG]);
                    aZ[Q](at)
                }
                if (cb) {
                    bG()
                }
            } else {
                if (arguments[c] == 2) {
                    var cL = arguments[0]
                      , cI = arguments[1]
                      , cG = false;
                    var cM = i[cD][cd];
                    if (cb && !au) {
                        bc()
                    }
                    for (var cF in cI) {
                        var cH = cM[ai](cF);
                        if (cH != -1 && cH < 8) {
                            continue
                        }
                        if (aV) {
                            if (bd == null) {
                                be[b1]([cL, cI[cF], cF, cG])
                            } else {
                                Z(bd, cL, cI[cF], cF, cG)
                            }
                        } else {
                            aV = true;
                            be[b1]([cL, cI[cF], cF, cG]);
                            aZ[Q](at)
                        }
                    }
                    if (cb) {
                        bG()
                    }
                }
            }
        } catch (cJ) {}
    }
    ;
    aZ[a] = function() {
        if (arguments[c] > 0) {
            var cE = arguments[0];
            var cF = aa[r]();
            ar(cE, cF);
            b5[bF] = cE;
            cC[bb](G)
        }
    }
    ;
    aZ[j] = function() {
        aa[ag][N](aa, arguments)
    }
    ;
    aZ[Y] = function() {
        aa[bX][N](aa, arguments)
    }
    ;
    aZ[an] = function() {
        return aa[bP][N](aa)
    }
    ;
    aZ[K] = function() {
        try {
            if (arguments.length < 1) {
                al("invalid argument, cannot add new settings without settings object")
            }
            var cE = arguments[0];
            if (!cA(cE)) {
                al("invalid argument, cannot add new settings without settings object")
            }
            for (var cG in cE) {
                if (cE.hasOwnProperty(cG) && bI[ai](cG) > -1) {
                    ae[b1]([ay, cG, cE[cG]])
                }
            }
        } catch (cF) {
            al("Error:: failed to set configuration")
        }
    }
    ;
    function by() {
        if (bH) {
            ae[b1]([aG])
        }
        ae[b1]([bz])
    }
    function Z(cJ, cN, cM, cE, cF) {
        if (bi) {
            return
        }
        if (cN !== void 0 && cM !== void 0 && cE !== void 0) {
            var cK = cN[a5](".");
            var cG = null;
            cJ = cJ[cK[0]];
            if (cK[c] > 1) {
                cG = cK[1]
            }
            cM = cq(cM);
            if (!(cJ instanceof Array)) {
                cJ = [cJ]
            }
            var cL;
            var cI = -1;
            for (var cH = 0; cH < cJ[c]; cH++) {
                if (Object[z].toString[bZ](cJ[cH]) === "[object Object]") {
                    if (cJ[cH][cw](cG)) {
                        cL = cJ[cH][cG]
                    } else {
                        al("can't match against uknown value");
                        break
                    }
                } else {
                    cL = cJ[cH]
                }
                if (typeof cL === "string") {
                    cL = cL[aE]()
                }
                if (cM[ai](cL) > -1) {
                    cI = cH;
                    if (!cF) {
                        break
                    }
                }
            }
            if (!cF && cI > -1) {
                bg(cE)
            } else {
                if (cF && cI == -1) {
                    bg(cE)
                }
            }
        }
    }
    function at(cE) {
        if (cE.status == 200) {
            bd = cE.results;
            while (be.length) {
                var cF = be[b2]();
                cF[k](bd);
                Z.apply(this, cF)
            }
        }
    }
    function bc() {
        var cF = "body { display: none; }"
          , cE = y.head || y[b0]("head")[0] || y.documentElement
          , cG = y[ap]("style");
        cG.type = "text/css";
        if (cG.styleSheet) {
            cG.styleSheet.cssText = cF
        } else {
            cG.appendChild(y.createTextNode(cF))
        }
        cE.appendChild(cG)
    }
    function bG() {
        setTimeout(function() {
            if (y[b0]("body")[0] !== undefined) {
                y[b0]("body")[0].style.display = "inline"
            } else {
                y.addEventListener("DOMContentLoaded", function() {
                    y[b0]("body")[0].style.display = "inline"
                }, false)
            }
        }, 1000)
    }
    function bJ(cG, cI) {
        if (cG && aD != null) {
            if (typeof cI == S) {
                var cF = cl();
                var cH = d;
                if (cF) {
                    cH += a6 + x + cF + b9
                }
                cH += g + x + aD;
                var cE = bj[aT]("anonymizeIP");
                if (cE) {
                    cH += b9 + "aip" + x + "1"
                }
                bl(cG + cH, cI)
            }
        }
    }
    function ab(cG, cF) {
        var cE = aI();
        if ((ch && (cE - ch) < b) || (T && (cE - T) < b)) {
            setTimeoutFunction(function() {
                ab(cG, cF)
            }, b)
        } else {
            if (bR) {
                bR = false;
                J.push([cG, cF]);
                aU = setInterval(bq, bp)
            }
            ae[b1]([bz, cG, cF])
        }
    }
    function bq() {
        C++;
        if (C > aQ) {
            if (aU) {
                clearInterval(aU)
            }
            return
        }
        var cE = J.shift();
        J.push(cE);
        ab(cE)
    }
    var ci = function(cF, cE) {
        this[ca] = {};
        if (!cE || cE == "") {
            cE = n
        }
        this[ca][a3] = cE;
        this[ca][R] = cF
    };
    var bt = function() {
        if (arguments[c] > 0) {
            var cE = arguments[0];
            if (a4[ai](cE) > -1) {
                if (o() && aS[ai](cE) > -1) {
                    return
                }
                return ao.applyRtp(aZ, cE, arguments)
            }
        }
    };
    ci[z].get = bt;
    ci[z].send = bt;
    ci[z].set = function() {
        if (arguments[c] > 0) {
            var cG = arguments[0];
            var cE = O.exec(cG);
            if (cE !== null) {
                var cF = [][bw][bZ](arguments, 0);
                cF[f](1, 0, cE[1]);
                return ao.applyRtp(aZ, j, cF)
            } else {
                if (a4[ai](cG) > -1) {
                    if (o() && aS[ai](cG) > -1) {
                        return
                    }
                    return ao.applyRtp(aZ, cG, arguments)
                }
            }
        }
    }
    ;
    ci[z].add = bt;
    var b5 = function() {
        return ao.callMethod[N](ao, [arguments])
    };
    b5[bE] = function() {
        var cG = arguments[0];
        if (b6(cG)) {
            var cF = n;
            if (arguments[c] > 1) {
                cF = arguments[1]
            }
            if (this[bv][cF]) {
                return this[bv][cF]
            }
            var cE = new ci(cG,cF);
            cj(cE);
            ae[b1]([ay, u, cG]);
            return cE
        }
    }
    ;
    b5[l] = function() {
        var cE = (arguments[c] > 0 && arguments[0] && arguments[0] != "") ? arguments[0] : n;
        return b5[bv][cE]
    }
    ;
    b5[bv] = {};
    bS(b5, l, b5[l]);
    bS(b5, e, InsighteraUtil.checkPattern);
    function cj(cE) {
        if (!b5[bv][cE[ca][a3]]) {
            b5[bv][cE[ca][a3]] = cE
        }
    }
    function a8() {
        al("campaign callback called")
    }
    function b6(cE) {
        return void 0 !== cE && cE != null && cE !== ""
    }
    if (!aC && i[cB]) {
        var aA = i[cB] && (i[cB].q || []);
        aD = i[cB] && i[cB].a;
        if (aD) {
            b5[bE](aD)
        }
        i[cB] = b5;
        ao.callMethod[N](ao, aA)
    }
    if (!o() && !aC) {
        ae[b1]([ay, bV, false]);
        ae[b1]([bQ]);
        ae[b1]([t]);
        ae[b1]([bu])
    }
}
)();
(function() {
    var aj = window
      , aw = false
      , O = false;
    try {
        var d = AITag.getCookie("db_rtp");
        if (d) {
            O = (d === "true")
        }
    } catch (Y) {}
    var aW = "length"
      , a2 = "apply"
      , U = "querySelectorAll"
      , v = "call"
      , r = "slice"
      , al = "prototype"
      , aN = "change"
      , ap = "init"
      , aR = "get";
    var N = null
      , aA = null
      , H = null
      , aC = null
      , m = null
      , t = null
      , C = null
      , au = null;
    var aG = "rtp";
    var g = "/rtp/api/v1_2"
      , aH = g + "/rcmd/richmedia/trending.json";
    var n = "thumbnail"
      , aU = "assetId"
      , j = "url"
      , aB = "ctaText"
      , I = "header"
      , Q = "description";
    var aJ = "html"
      , aK = "data"
      , af = "settings"
      , aZ = "containerSelector"
      , x = "data-widget-rtp-id";
    var ax = "data-rtp-id";
    var ab = "//rtp-static.marketo.com/rtp/rcmd/template/v2/";
    var f = []
      , P = []
      , aO = [];
    function l(a3) {
        try {
            if (O && window.console && window.console.log) {
                console.log(a3)
            }
        } catch (a4) {}
    }
    function ar(e) {
        return e !== undefined && e != null
    }
    function X(e) {
        return ar(e) && e != ""
    }
    function b(e) {
        return ar(e) && Object.prototype.toString.call(e) === "[object Array]"
    }
    function J(e) {
        return b(e) && e.length == 0
    }
    function K(e) {
        return ar(e) && typeof e == "object"
    }
    function V(e, a3) {
        return e.hasOwnProperty(a3)
    }
    function M(e) {
        return T(document, e)
    }
    function T(a3, e) {
        if (X(e)) {
            return a3[U](e)
        }
        throw "can't find elements for empty selector"
    }
    function at() {
        return "xxxxxxxx".replace(/[x]/g, function(a3) {
            var e = Math.random() * 16 | 0;
            return e.toString(16)
        })
    }
    function L(a5, a3, e) {
        if (a5 === void 0 || a5 == null) {
            a5 = ""
        }
        if (a3 !== void 0 && a3 != null) {
            for (var a6 in a3) {
                if (a3.hasOwnProperty(a6) && X(a3[a6])) {
                    if (a5.length > 0 && a5.lastIndexOf("&") < a5.length - 1) {
                        a5 += "&"
                    }
                    var a4 = e !== undefined ? (a5.indexOf(a6 + "=") != -1 || e.indexOf(a6 + "=") != -1) : a5.indexOf(a6 + "=") != -1;
                    if (!a4) {
                        a5 += a6 + "=" + a3[a6]
                    }
                }
            }
        }
        return a5
    }
    function W(a4, a6) {
        if (a6 !== undefined && a6 != null) {
            var a8 = ""
              , e = "";
            var a7 = a4.indexOf("?");
            var a3 = a4.indexOf("#");
            if (a7 > -1) {
                if (a3 == -1) {
                    a3 = a4.length - 1
                }
                e = a4.substring(a7, a3)
            }
            a8 = L(a8, a6, e);
            var a5 = "";
            if (a3 > -1 && a3 != a4.length - 1) {
                a5 = a4.substring(a3);
                a4 = a4.substring(0, a3)
            }
            if (a7 > -1) {
                if (a7 + 1 < a4.length) {
                    a4 += "&"
                }
            } else {
                a4 += "?"
            }
            a4 += a8 + a5
        }
        return a4
    }
    function a1(a6, a3) {
        var a5 = new XMLHttpRequest();
        if ("withCredentials"in a5) {
            try {
                a5.open(a6, a3, true)
            } catch (a4) {
                consoleMessage(a4)
            }
        } else {
            if (typeof XDomainRequest != "undefined") {
                a5 = new XDomainRequest();
                a5.open(a6, a3)
            } else {
                a5 = null
            }
        }
        return a5
    }
    function an(a3, a4, e) {
        return function() {
            if (!X(a3)) {
                return
            }
            if (a3.indexOf("//") == -1) {
                a3 = "//" + a3
            }
            var a5 = a1("GET", a3);
            if (!a5) {
                l("CORS not supported");
                return
            }
            a5.onload = function() {
                var a6 = a5.responseText;
                if (typeof a4 == "function") {
                    e.unshift(a6);
                    a4.apply(null, e)
                }
            }
            ;
            a5.onerror = function() {}
            ;
            a5.onprogress = function() {}
            ;
            a5.ontimeout = function() {}
            ;
            a5.timeout = 3000;
            a5.send()
        }
    }
    function aF(a3) {
        var a4 = document.createElement("div");
        a4.innerHTML = a3;
        var e = a4.childNodes;
        return e
    }
    function aP(a3) {
        var a8 = a3.length, a5, a6;
        for (var a4 = 0; a4 < a8; a4++) {
            try {
                a5 = a3.shift();
                a6 = a5.shift();
                a6[a2](u, a5)
            } catch (a7) {
                l("failed to run function " + a6 + ", error: " + a7)
            }
        }
    }
    function ai(a3) {
        var a6 = a3.length;
        while (a6 > 0) {
            a6--;
            var a4 = a3.shift();
            try {
                y.callMethod[a2](y, a4)
            } catch (a5) {
                l("Error:: failed to run queue. Error message: " + a5)
            }
        }
    }
    var a0 = []
      , ae = false;
    function aE() {
        ae = true;
        if (document.addEventListener || event.type === "load" || document.readyState === "complete") {
            if (document.addEventListener) {
                document.removeEventListener("DOMContentLoaded", aE, false);
                window.removeEventListener("load", aE, false)
            } else {
                document.detachEvent("onreadystatechange", aE);
                window.detachEvent("onload", aE)
            }
            var a3 = a0.length;
            while (a3 > 0) {
                a3--;
                var a4 = a0.shift();
                try {
                    y.callMethod[a2](y, a4[0])
                } catch (a5) {
                    l("Error:: failed to run ready queue. Error message: " + a5)
                }
            }
        }
    }
    function c() {
        a0.push(arguments);
        if (document.readyState === "complete" || ae) {
            ae = true;
            l("DOM ready, running the method");
            y.callMethod[a2](y, arguments[0])
        } else {
            if (document.addEventListener) {
                document.addEventListener("DOMContentLoaded", aE, false);
                window.addEventListener("load", aE, false)
            } else {
                document.attachEvent("onreadystatechange", aE);
                window.attachEvent("onload", aE)
            }
        }
    }
    var aM = "font"
      , ao = "background"
      , aD = "text";
    var ad = {
        font: {
            family: "font-family",
            size: "font-size",
            color: "color"
        },
        background: {
            color: "background-color"
        }
    };
    var Z = {
        general: {
            allowedHigher: [aM]
        },
        title: {
            allowedHigher: [aD, aM, ao]
        },
        description: {
            allowedHigher: [aM]
        },
        cta: {
            allowedHigher: [aD, aM, ao]
        },
        content: {
            allowedHigher: [ao]
        }
    };
    var z = {
        template1: {
            assetCount: 3,
            containerSelector: "div[data-rtp-widget-container]",
            data: [{
                selector: "img.rtp_rcmd2_img",
                attribute: "src",
                property: n,
                handler: function(e) {
                    return decodeURIComponent(e)
                }
            }, {
                selector: ".rtp_rcmd2_link_hidden > h4",
                attribute: "innertext",
                property: I,
                handler: function(e) {
                    return decodeURIComponent(e)
                }
            }, {
                selector: ".rtp_rcmd2_description > p",
                attribute: "innertext",
                property: Q,
                handler: function(e) {
                    return decodeURIComponent(e)
                }
            }, {
                selector: ".rtp_rcmd2_link",
                attribute: "href",
                property: j,
                handler: function(e, a3) {
                    return decodeURIComponent(e)
                }
            }, {
                selector: ".rtp_rcmd2_link_hidden",
                attribute: "href",
                property: j,
                handler: function(e, a3) {
                    return decodeURIComponent(e)
                }
            }],
            styleSelectors: {
                general: "div[data-rtp-widget-container]",
                title: ".rtp_rcmd2_title",
                description: ".rtp_rcmd2_description > p",
                cta: ".rtp_rcmd2_link",
                content: ".rtp_rcmd2_content_container"
            },
            defaultSettings: {
                "rcmd.title.text": "Content Recommendations",
                "rcmd.cta.text": "Click Here"
            }
        },
        template2: {
            assetCount: 3,
            containerSelector: "div[data-rtp-widget-container]",
            data: [{
                selector: "img.rtp_rcmd2_img",
                attribute: "src",
                property: n,
                handler: function(e) {
                    return decodeURIComponent(e)
                }
            }, {
                selector: ".rtp_rcmd2_label .rtp_rcmd2_link_hidden",
                attribute: "innertext",
                property: I,
                handler: function(e) {
                    return decodeURIComponent(e)
                }
            }, {
                selector: ".rtp_rcmd2_description",
                attribute: "innertext",
                property: Q,
                handler: function(e) {
                    return decodeURIComponent(e)
                }
            }, {
                selector: ".rtp_rcmd2_link",
                attribute: "href",
                property: j,
                handler: function(e, a3) {
                    return decodeURIComponent(e)
                }
            }, {
                selector: ".rtp_rcmd2_link_hidden",
                attribute: "href",
                property: j,
                handler: function(e, a3) {
                    return decodeURIComponent(e)
                }
            }],
            styleSelectors: {
                general: "div[data-rtp-widget-container]",
                title: ".rtp_rcmd2_title",
                description: ".rtp_rcmd2_description",
                cta: ".rtp_rcmd2_link",
                content: ".rtp_rcmd2_content_container"
            },
            defaultSettings: {
                "rcmd.title.text": "Content Recommendations",
                "rcmd.cta.text": "Click Here"
            }
        },
        template3: {
            assetCount: 3,
            containerSelector: "div[data-rtp-widget-container]",
            data: [{
                selector: ".rtp_rcmd2_label .rtp_rcmd2_link_hidden",
                attribute: "innertext",
                property: I,
                handler: function(e) {
                    return decodeURIComponent(e)
                }
            }, {
                selector: ".rtp_rcmd2_description .rtp_rcmd2_link_hidden",
                attribute: "innertext",
                property: Q,
                handler: function(e) {
                    return decodeURIComponent(e)
                }
            }, {
                selector: ".rtp_rcmd2_link_hidden",
                attribute: "href",
                property: j,
                handler: function(e, a3) {
                    return decodeURIComponent(e)
                }
            }],
            styleSelectors: {
                general: "div[data-rtp-widget-container]",
                title: ".rtp_rcmd2_title",
                description: ".rtp_rcmd2_description",
                cta: ".rtp_rcmd2_link",
                content: ".rtp_rcmd2_content_container"
            },
            defaultSettings: {
                "rcmd.title.text": "Content Recommendations",
                "rcmd.cta.text": "Click Here"
            }
        }
    }
      , aQ = {};
    function o(a3, bd, a6, ba) {
        if (ar(a6) && K(a6)) {
            var a4, bb, a5, a7, a8, bc;
            for (var e in a6) {
                a4 = e.split(".");
                if (a4.length > 2) {
                    bb = Z[a4[1]];
                    a7 = ba.styleSelectors[a4[1]];
                    if (bb && bb.allowedHigher.indexOf(a4[2]) > -1) {
                        a5 = a4[2];
                        if (a5 == aD) {
                            a8 = T(a3, a7);
                            if (a8) {
                                for (var a9 = 0; a9 < a8.length; a9++) {
                                    a8[a9].innerHTML = a6[e]
                                }
                            }
                        } else {
                            bc = a7 + "{" + ad[a4[2]][a4[3]] + ":" + a6[e] + "!important }";
                            if (bd.styleSheet) {
                                bd.styleSheet.cssText += bc
                            } else {
                                bd.appendChild(document.createTextNode(bc))
                            }
                        }
                    }
                }
            }
        }
    }
    function aI(e, a6, a5, a3) {
        var a4 = a3.defaultSettings;
        if (ar(a4) && K(a4)) {
            o(e, a6, a4, a3)
        }
        o(e, a6, a5, a3)
    }
    function am(a5, a3) {
        if (!X(a5)) {
            throw "can't change settings for null or empty widgetId"
        }
        if (!K(a3)) {
            throw "settings can't be null or not an object"
        }
        var a4 = z[a5];
        if (a4) {
            if (!a4[af]) {
                a4[af] = {}
            }
            for (var e in a3) {
                if (V(a3, e) && ar(a3[e])) {
                    a4[af][e] = a3[e]
                }
            }
            z[a5]
        }
    }
    function aL(a3, a8, a7) {
        var a6, e;
        for (var a5 = 0; a5 < a8.length; a5++) {
            e = a7[a8[a5].property];
            if (!ar(e)) {
                throw "cannot set undefined value for recommendation"
            }
            if (a8[a5].handler && typeof a8[a5].handler == "function") {
                e = a8[a5].handler(e, a7)
            }
            a6 = T(a3, a8[a5].selector);
            if (ar(a6) && a6.length > 0) {
                if (a8[a5].attribute == "innertext") {
                    a6[0].innerHTML = e.replace(/(?:\r\n|\r|\n)/g, "<br/>")
                } else {
                    for (var a4 = 0; a4 < a6.length; a4++) {
                        a6[a4].setAttribute(a8[a5].attribute, e)
                    }
                }
            } else {
                throw "no nodes found for: " + a8[a5].selector
            }
        }
    }
    function B(e) {
        for (var a3 = 0; a3 < e.length; a3++) {
            if (e[a3].getAttribute && e[a3].getAttribute(ax)) {
                return e[a3]
            }
        }
        return null
    }
    function F(e) {
        for (var a3 = 0; a3 < e.length; a3++) {
            if (e[a3].tagName.toLowerCase() == "style") {
                return e[a3]
            }
        }
        return null
    }
    function s(a4, e) {
        for (var a3 = 0; a3 < e.length; a3++) {
            a4.appendChild(e[a3])
        }
    }
    function av() {
        aP(P)
    }
    function aq(a6, a9, bc) {
        var a5 = JSON.parse(a6);
        if (!ar(a5.rcmd) || !ar(a5.rcmd.assets)) {
            document.getElementById(bc).style.display = "none"
        } else {
            document.getElementById(bc).style.display = ""
        }
        if (aO.indexOf(bc) > -1) {
            l("the " + bc + " already injected");
            return
        }
        var bb;
        var be = z[a9];
        var a8 = be[aK];
        var ba = be[aJ];
        if (!X(ba)) {
            P.push([aq, a6, a9, bc]);
            return
        }
        var bd = a5.rcmd.assets;
        var a3 = aF(ba);
        var e = B(a3);
        var bf = F(a3);
        if (e == null) {
            throw "can't find main container in HTML"
        }
        var a4 = T(e, be[aZ]);
        if (a4.length != bd.length) {
            throw "number of recommnedations doesn't match number of widget containers"
        }
        aI(e, bf, be[af], be);
        for (var a7 = 0; a7 < a4.length; a7++) {
            bb = bd[a7];
            aL(a4[a7], a8, bb)
        }
        s(document.getElementById(bc), a3);
        aO.push(bc)
    }
    function E(a4, e, a6) {
        var a5 = null;
        if (C) {
            a5 = C.rch
        }
        var a3 = "";
        if (au != null) {
            a3 = au
        }
        return W(N + aH, {
            session_id: aA,
            visitor_id: H,
            account_id: m,
            template_id: a6,
            visit_count: aC,
            view_count: t,
            categories: e != void 0 ? e.join(",") : void 0,
            consumed: a5,
            mkto_tok: encodeURIComponent(a3)
        })
    }
    function a() {
        var a5, a8 = 0, a4, a7;
        for (var e in aQ) {
            if (V(aQ, e) && aO.indexOf(e) == -1) {
                a7 = aQ[e];
                a5 = z[a7];
                var a6 = a5[af];
                var a3 = void 0;
                if (ar(a6)) {
                    if (ar(a6.category) && !J(a6.category)) {
                        a3 = a6.category
                    }
                }
                a8 = a5.assetCount;
                a4 = E(a8, a3, a7);
                an(a4, aq, [a7, e])()
            } else {
                l("can't get data for container " + e + ", the container alreay has data")
            }
        }
    }
    function aT() {
        var a4 = M("div[data-rtp-template-id]");
        if (a4.length == 0) {
            l("cannot find any widgets on the page");
            return
        }
        var a6, e, a5;
        for (var a3 = 0; a3 < a4.length; a3++) {
            a6 = a4[a3];
            e = "rtp-widget-container-" + at();
            a6.setAttribute("id", e);
            a5 = a6.getAttribute("data-rtp-template-id");
            aQ[e] = a5
        }
    }
    function A(e, a4) {
        var a3 = z[a4];
        if (a3) {
            a3[aJ] = e;
            z[a4] = a3;
            av()
        } else {
            l("cannot find widget for widget id: " + a4)
        }
    }
    function aS(e) {
        return ab + "/" + e + ".html?" + at()
    }
    function S(a5) {
        if (J(a5)) {
            throw "can't fetch templates for empty array"
        }
        var a3;
        var e = [];
        for (var a4 = 0; a4 < a5.length; a4++) {
            if (e.indexOf(a5[a4]) == -1) {
                e.push(a5[a4]);
                a3 = aS(a5[a4]);
                an(a3, A, [a5[a4]])()
            }
        }
    }
    var az = function() {
        return y.callMethod[a2](y, [arguments])
    };
    az.getSettings = function(e) {
        return z[e] && z[e][af]
    }
    ;
    var y = {};
    y.callMethod = function() {
        if (!ae) {
            c(arguments);
            return
        }
        var a4, a6 = null;
        for (var a3 = 0; a3 < arguments[aW]; a3++) {
            try {
                a4 = arguments[a3];
                a6 = a4[0];
                this.applyMethod(u, a6, a4)
            } catch (a5) {
                l("failed to run " + a6 + ". Error: " + a5)
            }
        }
    }
    ;
    y.applyMethod = function(a3, a4, e) {
        e = [][r][v](e, 1);
        return a3[a4][a2](a3, e)
    }
    ;
    var u = {};
    u[aN] = function() {
        l("changing widget settings");
        var e = arguments[0];
        if (K(e)) {
            for (var a3 in e) {
                am(a3, e[a3])
            }
        } else {
            am(e, arguments[1])
        }
    }
    ;
    u[ap] = function() {
        l("initiating RCMD widget");
        var e = arguments[0];
        if (!K(e)) {
            throw "expected configurations object for recommendation widget"
        }
        N = e.serverUrl;
        if (!X(N)) {
            throw "server URL can't be null or empty"
        }
        if (N.indexOf("//") == -1) {
            N = "//" + N
        }
        aA = e.sid;
        if (!X(aA)) {
            throw "session id can't be null or empty"
        }
        H = e.vid;
        if (!X(H)) {
            throw "visitor id can't be null or empty"
        }
        aC = e.vc;
        if (isNaN(aC) || aC < 1) {
            throw "visit must be a number and bigger than 0"
        }
        t = e.cc;
        if (isNaN(t) || t < 1) {
            throw "click count must be a number and bigger than 0"
        }
        C = e.consumedAssets;
        if (X(e.munchkinToken)) {
            au = e.munchkinToken
        }
        aT();
        var a4 = [];
        for (var a3 in aQ) {
            if (V(aQ, a3)) {
                a4.push(aQ[a3])
            }
        }
        S(a4);
        aw = true;
        ai(f)
    }
    ;
    u[aR] = function() {
        if (!aw) {
            l("can't get RCMD, script isn't initialized, waiting...");
            f.push([["get"]]);
            return
        }
        l("getting RCMD content");
        a()
    }
    ;
    m = !aj.rtp.d && aj[aG] && (typeof aj[aG].getTracker === "function" && aj[aG].getTracker().trackerData.aid);
    if (m === undefined || m == null) {
        throw "cannot get recommendations without rtp account id"
    }
    var k = aj.rtpRCMD.q;
    aj.rtpRCMD = az;
    k && y.callMethod[a2](y, k);
    var w;
    var h = [];
    var ac = [];
    var ah = [];
    var R = [];
    var ag = [];
    window.loadRichMediaImage = function() {
        w = ay();
        aX();
        i();
        if (w) {
            window.addEventListener("scroll", D);
            window.addEventListener("scroll", q);
            window.addEventListener("scroll", aY);
            D();
            q();
            aY()
        } else {
            window.addEventListener("scroll", G);
            G()
        }
    }
    ;
    function ay() {
        var e = document.querySelector('[data-rtp-id="rtp_rcmd2_tpl_2"]');
        return e != undefined
    }
    var G = function() {
        aa(G, 0)
    };
    var D = function() {
        aa(D, 1)
    };
    var q = function() {
        aa(q, 2)
    };
    var aY = function() {
        aa(aY, 3)
    };
    function aa(a5, e) {
        var a3 = ag[e];
        if (ah[e] && !aV(a3)) {
            l("Rich media was scrolled and not exposed, removing timer event.");
            clearTimeout(ah[e]);
            ah[e] = null
        }
        if (ac[e]) {
            if (aV(a3)) {
                var a4 = new Date();
                if (a4.getTime() > R[e].getTime() && !h[e]) {
                    h[e] = true;
                    p(window, "scroll", a5);
                    if (ah[e]) {
                        clearTimeout(ah[e]);
                        ah[e] = null
                    }
                    ak(a3);
                    l("Rich media was exposed for more than 1 second (with scrolling)")
                }
            } else {
                ac[e] = false;
                l("Rich media was not exposed for more than 1 second, a scroll broke it")
            }
            return
        }
        if (aV(a3)) {
            l("75% has reached, creating 1 second timer and keep checking");
            R[e] = new Date(Date.now() + 1000);
            ac[e] = true;
            ah[e] = setTimeout(function() {
                if (!h[e] && aV(a3)) {
                    p(window, "scroll", a5);
                    ak(a3);
                    h[e] = true;
                    l("Rich media was viewed for more than 1 second (by timer)")
                }
            }, 1000)
        }
    }
    function aV(a5) {
        var a8 = document.querySelector(a5);
        var a3 = false;
        if (a8) {
            var a6 = a8.getBoundingClientRect();
            var a7 = a6.top;
            var a4 = a6.bottom;
            if (a7 < 0 && a4 < 0) {
                return a3
            }
            var e = a6.height;
            var a9 = window.innerHeight;
            if (a7 > 0) {
                a3 = a7 + 0.75 * e <= a9
            } else {
                a3 = a4 >= 0.75 * e
            }
        }
        return a3
    }
    function ak(a3) {
        if (w) {
            AITag.sendViewEventToRTP(a3)
        } else {
            for (var e = 1; e < ag.length; e++) {
                AITag.sendViewEventToRTP(ag[e])
            }
        }
    }
    function aX() {
        var e = '[data-rtp-widget-container="rtp_rcmd2_item_';
        for (var a4 = 1; a4 < 4; a4++) {
            ag[a4] = e + a4 + '"]'
        }
        ag[0] = ag[1];
        var e = " .rtp_rcmd2_item_inner .rtp_rcmd2_img_container .rtp_rcmd2_link_hidden";
        var a6;
        for (var a4 = 1; a4 < ag.length; a4++) {
            a6 = ag[a4] + e;
            a6 = document.querySelector(a6);
            var a3 = a6.href.indexOf("?");
            var a5 = a6.href.substring(a3);
            AITag.richMediaMap[a5] = false
        }
    }
    function p(a4, e, a3) {
        if (a4.removeEventListener) {
            a4.removeEventListener(e, a3)
        }
        if (a4.detachEvent) {
            a4.detachEvent("on" + e, a3)
        }
    }
    function i() {
        var a7, a3;
        var a5 = function() {
            a7 = new Date().getTime();
            this.removeEventListener("mouseenter", a5);
            this.addEventListener("mouseleave", e)
        };
        var e = function(a9) {
            a3 = new Date().getTime();
            var bb = a3 - a7;
            if (bb > 500) {
                var ba = bb / 1000;
                AITag.sendHoverEventToRTP(this, ba);
                this.removeEventListener("mouseleave", e)
            }
            this.addEventListener("mouseenter", a5)
        };
        var a8 = ".rtp_rcmd2_item_inner";
        var a4 = document.querySelectorAll(a8);
        for (var a6 = 0; a6 < a4.length; a6++) {
            a4[a6].addEventListener("mouseenter", a5)
        }
    }
}
)();
