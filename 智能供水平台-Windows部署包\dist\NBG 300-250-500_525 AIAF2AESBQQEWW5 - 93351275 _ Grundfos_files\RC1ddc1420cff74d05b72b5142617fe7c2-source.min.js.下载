// For license information, see `https://assets.adobedtm.com/5e107551ef5d/89d0c5ed902a/4021f82e766f/RC1ddc1420cff74d05b72b5142617fe7c2-source.js`.
_satellite.__registerScript('https://assets.adobedtm.com/5e107551ef5d/89d0c5ed902a/4021f82e766f/RC1ddc1420cff74d05b72b5142617fe7c2-source.min.js', "try{var eventMethod=window.addEventListener?\"addEventListener\":\"attachEvent\",eventer=window[eventMethod],messageEvent=\"attachEvent\"==eventMethod?\"onmessage\":\"message\",chatWindow=document.querySelector(\"#fn-chat-iframe-container\");eventer(messageEvent,(function(e){var t=[];if(\"https://cdn.prod.eu.five9.net\"===e.origin)try{var n=e[e.message?\"message\":\"data\"];t.push(n.publishEvent.appState.type),\"formSubmitted\"==n.publishEvent.appState.type?(_satellite.track(\"Chat button clicked\"),sessionStorage.setItem(\"five9ChatStarted\",\"true\")):\"conversationTerminated\"==n.publishEvent.appState.type&&(QSI.API.unload(),QSI.API.load(),QSI.API.run()),console.log(\"preEvent \",t)}catch(e){console.log(e)}}),!1)}catch(e){console.log(\"F9 \",e)}");