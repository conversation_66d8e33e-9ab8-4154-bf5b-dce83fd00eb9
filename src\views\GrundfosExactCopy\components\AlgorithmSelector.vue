<template>
  <div class="algorithm-selector">
    <h3 class="section-title">选择拟合算法</h3>
    
    <div class="algorithm-cards">
      <div 
        v-for="algo in algorithms" 
        :key="algo.id"
        class="algorithm-card"
        :class="{ active: selectedAlgorithm === algo.id }"
        @click="$emit('update:selectedAlgorithm', algo.id)"
      >
        <div class="card-header">
          <div class="card-icon" v-html="algo.icon"></div>
          <div class="card-title">{{ algo.title }}</div>
        </div>
        
        <div class="card-content">
          <p class="card-description">{{ algo.description }}</p>
          
          <div class="rating-wrapper">
            <div class="rating">
              <span class="rating-label">精度:</span>
              <div class="rating-stars">
                <div 
                  class="stars-filled" 
                  :style="{width: `${algo.accuracy * 20}%`}"
                ></div>
              </div>
            </div>
            
            <div class="rating">
              <span class="rating-label">速度:</span>
              <div class="rating-stars">
                <div 
                  class="stars-filled" 
                  :style="{width: `${algo.speed * 20}%`}"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Algorithm } from './types'

const props = defineProps<{
  selectedAlgorithm: string
}>()

defineEmits<{
  (e: 'update:selectedAlgorithm', value: string): void
}>()

const algorithms: Algorithm[] = [
  {
    id: 'linear',
    title: '最小二乘法',
    icon: '<svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="2" fill="none"><path d="M3 3v18h18"></path><path d="M19 9l-5 5-4-4-3 3"></path></svg>',
    description: '线性回归，适用于线性关系数据',
    accuracy: 3,
    speed: 5
  },
  {
    id: 'polynomial',
    title: '多项式拟合',
    icon: '<svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="2" fill="none"><path d="M3 3v18h18"></path><path d="M3 12c8 0 8-9 16-9"></path><path d="M3 18c8 0 8-12 16-12"></path></svg>',
    description: '高阶多项式，适用于复杂曲线',
    accuracy: 4,
    speed: 4
  },
  {
    id: 'spline',
    title: '样条插值',
    icon: '<svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="2" fill="none"><path d="M3 3v18h18"></path><path d="M3 15s2-4 6-4 5 4 8 4 4-4 4-4"></path></svg>',
    description: '平滑曲线，保持数据点连续性',
    accuracy: 5,
    speed: 3
  },
  {
    id: 'neural',
    title: '神经网络',
    icon: '<svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="2" fill="none"><circle cx="12" cy="6" r="3"></circle><circle cx="6" cy="16" r="3"></circle><circle cx="18" cy="16" r="3"></circle><path d="M12 9v3m-3 4l-2-2m8 0l-2-2"></path></svg>',
    description: '深度学习，处理复杂非线性关系',
    accuracy: 5,
    speed: 2
  },
  {
    id: 'bezier',
    title: '贝塞尔曲线',
    icon: '<svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="2" fill="none"><path d="M3 3v18h18"></path><path d="M3 17c0 0 8-10 18-10"></path></svg>',
    description: '平滑曲线，视觉效果优美',
    accuracy: 3,
    speed: 4
  }
]
</script>

<style lang="scss" scoped>
.algorithm-selector {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--el-color-primary-dark-2);
  position: relative;
  padding-left: 0.75rem;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0.25rem;
    bottom: 0.25rem;
    width: 4px;
    background-color: var(--el-color-primary);
    border-radius: 2px;
  }
}

.algorithm-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.algorithm-card {
  position: relative;
  background-color: var(--el-bg-color);
  border-radius: 0.5rem;
  padding: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  cursor: pointer;
  border: 1px solid var(--el-border-color-light);
  overflow: hidden;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    border-color: var(--el-color-primary-light-7);
  }
  
  &.active {
    border-color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background-color: var(--el-color-primary);
    }
  }
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  
  .card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    margin-right: 0.75rem;
    color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
    border-radius: 8px;
    
    svg {
      width: 24px;
      height: 24px;
    }
    
    .active & {
      color: white;
      background-color: var(--el-color-primary);
    }
  }
  
  .card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.card-content {
  padding-left: calc(40px + 0.75rem);
}

.card-description {
  margin: 0 0 0.75rem;
  font-size: 0.875rem;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

.rating-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.rating {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  
  .rating-label {
    width: 3rem;
    color: var(--el-text-color-secondary);
  }
  
  .rating-stars {
    position: relative;
    width: 100px;
    height: 8px;
    background-color: var(--el-fill-color-darker);
    border-radius: 4px;
    overflow: hidden;
    
    .stars-filled {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      background-color: var(--el-color-primary);
      border-radius: 4px;
    }
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .algorithm-cards {
    grid-template-columns: 1fr;
  }
}
</style> 