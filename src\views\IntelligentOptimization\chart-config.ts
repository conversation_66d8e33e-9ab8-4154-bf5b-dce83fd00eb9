import * as echarts from 'echarts'
import type { OptimizationResult } from '@/types'
import { createYAxisConfig } from '@/utils/chart-axis-config'

/**
 * 创建Y轴配置的通用方法
 * @param type 轴类型：'head'(扬程) 或 'efficiency'(效率) 或 'power'(功率)
 * @param options 自定义选项
 * @returns ECharts的Y轴配置对象
 */
function createYAxisConfig_old(type: 'head' | 'efficiency' | 'power', options?: any) {
  const config: any = {
    type: 'value',
    nameLocation: 'middle',
    nameGap: 50,
    min: 0,
    splitNumber: 12,
  }
  
  // 根据类型设置不同的配置
  switch (type) {
    case 'head':
      config.name = '扬程 (m)'
      config.max = 55
      config.interval = 5
      config.splitNumber = 11
      config.axisLabel = {
        formatter: '{value} m'
      }
      break
    case 'efficiency':
      config.name = '效率 (%)'
      config.max = 220
      config.interval = 20
      config.axisLabel = {
        formatter: function(value: number) {
          // 只显示0-100%范围内的刻度值
          if (value <= 100) {
            return value + '%'
          }
          return '' // 100%以上不显示刻度值
        }
      }
      break
    case 'power':
      config.name = '功率 (kW)'
      config.axisLabel = {
        formatter: '{value} kW'
      }
      break
  }
  
  // 合并自定义选项
  if (options) {
    Object.assign(config, options)
  }
  
  return config
}

export function initChart(container: HTMLElement) {
  return echarts.init(container)
}

export function updateChart(
  chart: echarts.ECharts,
  curveData: any,
  chartType: string,
  inputParams: { Q: number; H: number },
  optimizationResult: OptimizationResult | null
) {
  const { QHData, QETAData, QPData } = curveData
  
  // 基础配置
  const baseOption = {
    title: {
      text: '水泵特性曲线',
      left: 'center',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: function(params: any) {
        let result = `流量: ${params[0].data[0].toFixed(1)} m³/h<br/>`
        params.forEach((param: any) => {
          const unit = param.seriesName.includes('扬程') ? 'm' : 
                      param.seriesName.includes('效率') ? '%' : 'kW'
          result += `${param.seriesName}: ${param.data[1].toFixed(2)} ${unit}<br/>`
        })
        return result
      }
    },
    legend: {
      top: 30,
      data: []
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '流量 (m³/h)',
      nameLocation: 'middle',
      nameGap: 30,
      interval: 50,
      axisLabel: {
        formatter: '{value}'
      }
    },
    yAxis: [],
    series: []
  }

  // 根据图表类型配置
  switch (chartType) {
    case 'qh':
      configureQHChart(baseOption, QHData, inputParams, optimizationResult)
      break
    case 'qeta':
      configureQETAChart(baseOption, QETAData, inputParams, optimizationResult)
      break
    case 'qp':
      configureQPChart(baseOption, QPData, inputParams, optimizationResult)
      break
    default:
      configureAllChart(baseOption, { QHData, QETAData, QPData }, inputParams, optimizationResult)
  }

  chart.setOption(baseOption, true)
}

function configureQHChart(option: any, data: any[], inputParams: any, optimizationResult: any) {
  // 动态构建图例数据
  const legendData = ['Q-H曲线']

  // 计算最大扬程值
  const maxHead = Math.max(...data.map(point => point[1]))
  
  // 使用新的Y轴配置工具
  option.yAxis = [createYAxisConfig(maxHead, 'head', {
    nameLocation: 'middle',
    nameGap: 50
  })]

  option.series = [
    {
      name: 'Q-H曲线',
      type: 'line',
      data: data,
      smooth: true,
      lineStyle: {
        width: 3,
        color: '#409EFF'
      },
      symbol: 'none'
    }
  ]

  // 添加目标点
  if (inputParams.Q > 0 && inputParams.H > 0) {
    legendData.push('目标点')
    option.series.push({
      name: '目标点',
      type: 'scatter',
      data: [[inputParams.Q, inputParams.H]],
      symbolSize: 12,
      itemStyle: {
        color: '#E6A23C'
      }
    })
  }

  // 添加优化点
  if (optimizationResult) {
    legendData.push('优化点')
    option.series.push({
      name: '优化点',
      type: 'scatter',
      data: [[optimizationResult.flow, optimizationResult.head]],
      symbolSize: 15,
      itemStyle: {
        color: '#67C23A'
      }
    })
  }

  // 设置图例数据
  option.legend.data = legendData
}

function configureQETAChart(option: any, data: any[], inputParams: any, optimizationResult: any) {
  // 动态构建图例数据
  const legendData = ['Q-η曲线']

  // 计算最大效率值
  const maxEfficiency = Math.max(...data.map(point => point[1]))
  
  // 使用新的Y轴配置工具
  option.yAxis = [createYAxisConfig(maxEfficiency, 'efficiency', {
    nameLocation: 'middle',
    nameGap: 50
  })]

  option.series = [
    {
      name: 'Q-η曲线',
      type: 'line',
      data: data,
      smooth: true,
      lineStyle: {
        width: 3,
        color: '#67C23A'
      },
      symbol: 'none'
    }
  ]

  // 添加优化效率点
  if (optimizationResult) {
    legendData.push('优化效率点')
    option.series.push({
      name: '优化效率点',
      type: 'scatter',
      data: [[optimizationResult.flow, optimizationResult.efficiency]],
      symbolSize: 15,
      itemStyle: {
        color: '#F56C6C'
      }
    })
  }

  // 设置图例数据
  option.legend.data = legendData
}

function configureQPChart(option: any, data: any[], inputParams: any, optimizationResult: any) {
  // 动态构建图例数据
  const legendData = ['Q-P曲线']

  // 计算最大功率值
  const maxPower = Math.max(...data.map(point => point[1]))
  
  // 使用新的Y轴配置工具
  option.yAxis = [createYAxisConfig(maxPower, 'power', {
    nameLocation: 'middle',
    nameGap: 50
  })]

  option.series = [
    {
      name: 'Q-P曲线',
      type: 'line',
      data: data,
      smooth: true,
      lineStyle: {
        width: 3,
        color: '#E6A23C'
      },
      symbol: 'none'
    }
  ]

  // 添加优化功率点
  if (optimizationResult) {
    legendData.push('优化功率点')
    option.series.push({
      name: '优化功率点',
      type: 'scatter',
      data: [[optimizationResult.flow, optimizationResult.power]],
      symbolSize: 15,
      itemStyle: {
        color: '#F56C6C'
      }
    })
  }

  // 设置图例数据
  option.legend.data = legendData
}

function configureAllChart(option: any, data: any, inputParams: any, optimizationResult: any) {
  const { QHData, QETAData, QPData } = data
  const legendData = ['Q-H曲线', 'Q-η曲线', 'Q-P曲线']

  option.yAxis = [
    createYAxisConfig(Math.max(...QHData.map((point: number[]) => point[1])), 'head', {
      position: 'left'
    }),
    createYAxisConfig(Math.max(...QETAData.map((point: number[]) => point[1])), 'efficiency', {
      position: 'right',
      name: '效率 (%) / 功率 (kW)'
    })
  ]

  option.series = [
    {
      name: 'Q-H曲线',
      type: 'line',
      yAxisIndex: 0,
      data: QHData,
      smooth: true,
      lineStyle: {
        width: 3,
        color: '#409EFF'
      },
      symbol: 'none'
    },
    {
      name: 'Q-η曲线',
      type: 'line',
      yAxisIndex: 1,
      data: QETAData,
      smooth: true,
      lineStyle: {
        width: 3,
        color: '#67C23A'
      },
      symbol: 'none'
    },
    {
      name: 'Q-P曲线',
      type: 'line',
      yAxisIndex: 1,
      data: QPData,
      smooth: true,
      lineStyle: {
        width: 3,
        color: '#E6A23C'
      },
      symbol: 'none'
    }
  ]

  // 添加目标点和优化点
  if (inputParams.Q > 0 && inputParams.H > 0) {
    legendData.push('目标点')
    option.series.push({
      name: '目标点',
      type: 'scatter',
      yAxisIndex: 0,
      data: [[inputParams.Q, inputParams.H]],
      symbolSize: 12,
      itemStyle: {
        color: '#F56C6C'
      }
    })
  }

  if (optimizationResult) {
    legendData.push('优化点')
    option.series.push({
      name: '优化点',
      type: 'scatter',
      yAxisIndex: 0,
      data: [[optimizationResult.flow, optimizationResult.head]],
      symbolSize: 15,
      itemStyle: {
        color: '#909399'
      }
    })
  }

  // 设置图例数据
  option.legend.data = legendData
}
