<template>
  <div class="quick-actions-panel">
    <div class="quick-action-card">
      <div class="card-icon">
        <i class="icon-preset"></i>
      </div>
      <div class="card-content">
        <h4>使用预设数据</h4>
        <p>快速导入格兰富预设的标准数据点</p>
        <button class="btn-card-action" @click="$emit('import-grundfos-data')">
          导入格兰富数据
        </button>
      </div>
    </div>
    
    <div class="quick-action-card">
      <div class="card-icon">
        <i class="icon-add"></i>
      </div>
      <div class="card-content">
        <h4>批量添加数据</h4>
        <p>一次添加多个数据点</p>
        <div class="action-inputs">
          <el-input-number v-model="batchCount" :min="1" :max="10" size="small" />
          <button class="btn-card-action" @click="handleBatchAdd">
            批量添加
          </button>
        </div>
      </div>
    </div>
    
    <div class="quick-action-card">
      <div class="card-icon">
        <i class="icon-interpolate"></i>
      </div>
      <div class="card-content">
        <h4>插值计算</h4>
        <p>根据已有数据点自动插值计算</p>
        <button class="btn-card-action" @click="$emit('interpolate')">
          自动插值
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const emit = defineEmits<{
  (e: 'import-grundfos-data'): void
  (e: 'batch-add', count: number): void
  (e: 'interpolate'): void
}>()

const batchCount = ref(3)

const handleBatchAdd = () => {
  emit('batch-add', batchCount.value)
}
</script>

<style lang="scss" scoped>
.quick-actions-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 10px;
  
  .quick-action-card {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    border-radius: 12px;
    background: white;
    box-shadow: 0 4px 16px rgba(0,0,0,0.06);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 24px rgba(0,0,0,0.1);
    }
    
    .card-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      font-size: 24px;
    }
    
    .card-content {
      flex: 1;
      
      h4 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
      }
      
      p {
        margin: 0 0 16px 0;
        font-size: 14px;
        color: #6c757d;
      }
      
      .btn-card-action {
        padding: 8px 16px;
        border: none;
        border-radius: 6px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
      }
      
      .action-inputs {
        display: flex;
        align-items: center;
        gap: 10px;
      }
    }
  }
}
</style> 