<template>
  <div class="grundfos-advanced-chart">
    <!-- 图表工具栏 -->
    <div class="chart-toolbar">
      <div class="chart-toolbar__left">
        <h3 class="chart-title">Performance Curves - NBG 300-250-500/525</h3>
        <span class="chart-subtitle">Impeller: 525mm | Speed: 2950 rpm | 50Hz</span>
      </div>
      <div class="chart-toolbar__right">
        <button class="chart-btn" @click="toggleFullscreen" title="全屏">
          <svg width="16" height="16" viewBox="0 0 24 24">
            <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
          </svg>
        </button>
        <button class="chart-btn" @click="downloadChart" title="下载">
          <svg width="16" height="16" viewBox="0 0 24 24">
            <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
          </svg>
        </button>
        <button class="chart-btn" @click="printChart" title="打印">
          <svg width="16" height="16" viewBox="0 0 24 24">
            <path d="M19 8H5c-1.66 0-3 1.34-3 3v6h4v4h12v-4h4v-6c0-1.66-1.34-3-3-3zm-3 11H8v-5h8v5zm3-7c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm-1-9H6v4h12V3z"/>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- 主图表区域 -->
    <div class="chart-container" ref="chartContainer">
      <canvas 
        ref="chartCanvas"
        class="chart-canvas"
        :width="1200"
        :height="800"
        @mousemove="handleMouseMove"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
        @click="handleClick"
        @wheel="handleWheel"
        @dblclick="handleDoubleClick"
      ></canvas>
      
      <!-- 高级数据提示框 -->
      <div 
        v-if="tooltip.visible"
        class="advanced-tooltip"
        :style="tooltipStyle"
      >
        <div class="tooltip-header">
          <span class="tooltip-title">Operating Point</span>
          <span class="tooltip-close" @click="tooltip.visible = false">×</span>
        </div>
        <div class="tooltip-content">
          <div class="tooltip-row">
            <span class="tooltip-label">Flow Rate:</span>
            <span class="tooltip-value">{{ tooltip.data.flow }} m³/h</span>
          </div>
          <div class="tooltip-row">
            <span class="tooltip-label">Head:</span>
            <span class="tooltip-value">{{ tooltip.data.head }} m</span>
          </div>
          <div class="tooltip-row">
            <span class="tooltip-label">Efficiency:</span>
            <span class="tooltip-value efficiency">{{ tooltip.data.efficiency }}%</span>
          </div>
          <div class="tooltip-row">
            <span class="tooltip-label">Power:</span>
            <span class="tooltip-value power">{{ tooltip.data.power }} kW</span>
          </div>
          <div class="tooltip-row">
            <span class="tooltip-label">NPSH:</span>
            <span class="tooltip-value npsh">{{ tooltip.data.npsh }} m</span>
          </div>
        </div>
        <div class="tooltip-footer">
          <button class="tooltip-btn" @click="setAsOperatingPoint">Set as Operating Point</button>
        </div>
      </div>
      
      <!-- 缩放控制 -->
      <div class="zoom-controls">
        <button class="zoom-btn" @click="zoomIn" title="放大">+</button>
        <button class="zoom-btn" @click="zoomOut" title="缩小">-</button>
        <button class="zoom-btn" @click="resetZoom" title="重置">⌂</button>
        <div class="zoom-level">{{ Math.round(zoom.scale * 100) }}%</div>
      </div>
      
      <!-- 图表信息面板 -->
      <div class="chart-info-panel">
        <div class="info-section">
          <h4>Current Selection</h4>
          <div class="info-item">
            <span>Model:</span>
            <span>NBG 300-250-500/525</span>
          </div>
          <div class="info-item">
            <span>Product No:</span>
            <span>93351275</span>
          </div>
          <div class="info-item">
            <span>Type:</span>
            <span>AIAF2AESBQQEWW5</span>
          </div>
        </div>
        
        <div class="info-section">
          <h4>Performance Data</h4>
          <div class="info-item">
            <span>Max Flow:</span>
            <span>900 m³/h</span>
          </div>
          <div class="info-item">
            <span>Max Head:</span>
            <span>42.5 m</span>
          </div>
          <div class="info-item">
            <span>Max Efficiency:</span>
            <span>82%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { 
  GRUNDFOS_CURVE_DATA, 
  GRUNDFOS_COLORS, 
  calculatePumpParameters 
} from '@/utils/grundfosDataProcessor'

// 响应式数据
const chartContainer = ref<HTMLDivElement>()
const chartCanvas = ref<HTMLCanvasElement>()

// 鼠标和交互状态
const mouse = reactive({
  x: 0,
  y: 0,
  isOver: false
})

// 高级提示框
const tooltip = reactive({
  visible: false,
  x: 0,
  y: 0,
  data: {
    flow: 0,
    head: 0,
    efficiency: 0,
    power: 0,
    npsh: 0
  }
})

// 缩放状态
const zoom = reactive({
  scale: 1,
  offsetX: 0,
  offsetY: 0,
  minScale: 0.5,
  maxScale: 5
})

// 图表区域定义
const chartArea = {
  x: 80,
  y: 80,
  width: 1000,
  height: 600
}

// 计算提示框样式
const tooltipStyle = computed(() => ({
  left: `${tooltip.x + 10}px`,
  top: `${tooltip.y - 10}px`,
  transform: tooltip.x > 800 ? 'translateX(-100%)' : 'none'
}))

// 事件处理函数
const handleMouseMove = (event: MouseEvent) => {
  const rect = chartCanvas.value?.getBoundingClientRect()
  if (!rect) return
  
  mouse.x = event.clientX - rect.left
  mouse.y = event.clientY - rect.top
  
  updateTooltip()
  redrawChart()
}

const handleMouseEnter = () => {
  mouse.isOver = true
  if (chartCanvas.value) {
    chartCanvas.value.style.cursor = 'crosshair'
  }
}

const handleMouseLeave = () => {
  mouse.isOver = false
  tooltip.visible = false
  if (chartCanvas.value) {
    chartCanvas.value.style.cursor = 'default'
  }
  redrawChart()
}

const handleClick = () => {
  if (tooltip.visible) {
    // 可以在这里添加点击处理逻辑
  }
}

const handleWheel = (event: WheelEvent) => {
  event.preventDefault()
  
  const rect = chartCanvas.value?.getBoundingClientRect()
  if (!rect) return
  
  const mouseX = event.clientX - rect.left
  const mouseY = event.clientY - rect.top
  
  const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1
  const newScale = Math.max(zoom.minScale, Math.min(zoom.maxScale, zoom.scale * zoomFactor))
  
  if (newScale !== zoom.scale) {
    const scaleChange = newScale / zoom.scale
    
    zoom.offsetX = mouseX - (mouseX - zoom.offsetX) * scaleChange
    zoom.offsetY = mouseY - (mouseY - zoom.offsetY) * scaleChange
    zoom.scale = newScale
    
    redrawChart()
  }
}

const handleDoubleClick = () => {
  resetZoom()
}

// 更新提示框
const updateTooltip = () => {
  if (!mouse.isOver) {
    tooltip.visible = false
    return
  }
  
  // 检查鼠标是否在图表区域内
  if (mouse.x >= chartArea.x && mouse.x <= chartArea.x + chartArea.width &&
      mouse.y >= chartArea.y && mouse.y <= chartArea.y + chartArea.height) {
    
    const flow = ((mouse.x - chartArea.x) / chartArea.width) * 900
    const calculatedData = calculatePumpParameters(flow)
    
    tooltip.visible = true
    tooltip.x = mouse.x
    tooltip.y = mouse.y
    tooltip.data = {
      flow: Math.round(calculatedData.flow),
      head: Math.round(calculatedData.head * 10) / 10,
      efficiency: Math.round(calculatedData.efficiency * 10) / 10,
      power: Math.round(calculatedData.power * 10) / 10,
      npsh: Math.round(calculatedData.npsh * 10) / 10
    }
  } else {
    tooltip.visible = false
  }
}

// 绘制图表
const drawChart = () => {
  const canvas = chartCanvas.value
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')
  if (!ctx) return
  
  // 清空画布
  ctx.clearRect(0, 0, 1200, 800)
  
  // 应用缩放
  ctx.save()
  ctx.translate(zoom.offsetX, zoom.offsetY)
  ctx.scale(zoom.scale, zoom.scale)
  
  // 绘制背景
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, 1200, 800)
  
  // 绘制专业网格
  drawProfessionalGrid(ctx)
  
  // 绘制坐标轴
  drawProfessionalAxes(ctx)
  
  // 绘制曲线
  drawProfessionalCurves(ctx)
  
  // 绘制鼠标交互
  if (mouse.isOver) {
    drawMouseInteraction(ctx)
  }
  
  ctx.restore()
}

// 绘制专业网格
const drawProfessionalGrid = (ctx: CanvasRenderingContext2D) => {
  // 主网格线
  ctx.strokeStyle = '#d0d0d0'
  ctx.lineWidth = 1
  
  // 垂直主网格线
  for (let i = 0; i <= 9; i++) {
    const x = chartArea.x + (i / 9) * chartArea.width
    ctx.beginPath()
    ctx.moveTo(x, chartArea.y)
    ctx.lineTo(x, chartArea.y + chartArea.height)
    ctx.stroke()
  }
  
  // 水平主网格线
  for (let i = 0; i <= 5; i++) {
    const y = chartArea.y + chartArea.height - (i / 5) * chartArea.height
    ctx.beginPath()
    ctx.moveTo(chartArea.x, y)
    ctx.lineTo(chartArea.x + chartArea.width, y)
    ctx.stroke()
  }
}

// 绘制专业坐标轴
const drawProfessionalAxes = (ctx: CanvasRenderingContext2D) => {
  ctx.strokeStyle = '#333333'
  ctx.lineWidth = 2
  
  // 绘制坐标轴
  ctx.beginPath()
  ctx.moveTo(chartArea.x, chartArea.y + chartArea.height)
  ctx.lineTo(chartArea.x + chartArea.width, chartArea.y + chartArea.height)
  ctx.moveTo(chartArea.x, chartArea.y)
  ctx.lineTo(chartArea.x, chartArea.y + chartArea.height)
  ctx.stroke()
  
  // 绘制标签
  ctx.fillStyle = '#333333'
  ctx.font = '12px Arial'
  ctx.textAlign = 'center'
  
  // X轴标签
  for (let i = 0; i <= 9; i++) {
    const flow = i * 100
    const x = chartArea.x + (i / 9) * chartArea.width
    ctx.fillText(flow.toString(), x, chartArea.y + chartArea.height + 20)
  }
}

// 绘制专业曲线
const drawProfessionalCurves = (ctx: CanvasRenderingContext2D) => {
  // 扬程曲线
  ctx.strokeStyle = '#1f5582'
  ctx.lineWidth = 3
  ctx.beginPath()
  
  GRUNDFOS_CURVE_DATA.headCurve.forEach((point, index) => {
    const x = chartArea.x + (point.flow / 900) * chartArea.width
    const y = chartArea.y + chartArea.height - (point.head / 50) * chartArea.height
    
    if (index === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })
  ctx.stroke()
  
  // 效率曲线
  ctx.strokeStyle = '#ff8c00'
  ctx.lineWidth = 3
  ctx.beginPath()
  
  GRUNDFOS_CURVE_DATA.efficiencyCurve.forEach((point, index) => {
    const x = chartArea.x + (point.flow / 900) * chartArea.width
    const y = chartArea.y + chartArea.height - (point.efficiency / 100) * chartArea.height
    
    if (index === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })
  ctx.stroke()
}

// 绘制鼠标交互
const drawMouseInteraction = (ctx: CanvasRenderingContext2D) => {
  if (!tooltip.visible) return
  
  ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)'
  ctx.lineWidth = 1
  ctx.setLineDash([3, 3])
  
  // 十字线
  ctx.beginPath()
  ctx.moveTo(mouse.x, chartArea.y)
  ctx.lineTo(mouse.x, chartArea.y + chartArea.height)
  ctx.moveTo(chartArea.x, mouse.y)
  ctx.lineTo(chartArea.x + chartArea.width, mouse.y)
  ctx.stroke()
  
  ctx.setLineDash([])
}

// 控制函数
const redrawChart = () => {
  drawChart()
}

const zoomIn = () => {
  zoom.scale = Math.min(zoom.maxScale, zoom.scale * 1.2)
  redrawChart()
}

const zoomOut = () => {
  zoom.scale = Math.max(zoom.minScale, zoom.scale / 1.2)
  redrawChart()
}

const resetZoom = () => {
  zoom.scale = 1
  zoom.offsetX = 0
  zoom.offsetY = 0
  redrawChart()
}

const toggleFullscreen = () => {
  if (chartContainer.value) {
    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      chartContainer.value.requestFullscreen()
    }
  }
}

const downloadChart = () => {
  const canvas = chartCanvas.value
  if (!canvas) return
  
  const link = document.createElement('a')
  link.download = 'grundfos-advanced-chart.png'
  link.href = canvas.toDataURL()
  link.click()
}

const printChart = () => {
  window.print()
}

const setAsOperatingPoint = () => {
  // 设置为工作点的逻辑
  console.log('Setting as operating point:', tooltip.data)
  tooltip.visible = false
}

// 初始化
onMounted(async () => {
  await nextTick()
  drawChart()
})
</script>

<style lang="scss" scoped>
.grundfos-advanced-chart {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  .chart-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #1f5582 0%, #2a6b94 100%);
    color: white;
    
    &__left {
      .chart-title {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 600;
      }
      
      .chart-subtitle {
        font-size: 12px;
        opacity: 0.9;
      }
    }
    
    &__right {
      display: flex;
      gap: 8px;
    }
  }
  
  .chart-btn {
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
    
    svg {
      fill: currentColor;
    }
  }
  
  .chart-container {
    position: relative;
    padding: 20px;
  }
  
  .chart-canvas {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    cursor: crosshair;
  }
  
  .advanced-tooltip {
    position: absolute;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    z-index: 1000;
    
    .tooltip-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      border-radius: 8px 8px 0 0;
      
      .tooltip-title {
        font-weight: 600;
        color: #1f5582;
      }
      
      .tooltip-close {
        cursor: pointer;
        font-size: 18px;
        color: #999;
        
        &:hover {
          color: #666;
        }
      }
    }
    
    .tooltip-content {
      padding: 12px 16px;
      
      .tooltip-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .tooltip-label {
          color: #666;
          font-size: 12px;
        }
        
        .tooltip-value {
          font-weight: 600;
          font-size: 12px;
          
          &.efficiency {
            color: #ff8c00;
          }
          
          &.power {
            color: #00aa00;
          }
          
          &.npsh {
            color: #ff0000;
          }
        }
      }
    }
    
    .tooltip-footer {
      padding: 8px 16px;
      border-top: 1px solid #e9ecef;
      
      .tooltip-btn {
        width: 100%;
        padding: 6px 12px;
        background: #1f5582;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        
        &:hover {
          background: #164466;
        }
      }
    }
  }
  
  .zoom-controls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    
    .zoom-btn {
      width: 32px;
      height: 32px;
      background: white;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      
      &:hover {
        background: #f5f5f5;
      }
    }
    
    .zoom-level {
      text-align: center;
      font-size: 10px;
      color: #666;
      padding: 4px;
    }
  }
  
  .chart-info-panel {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 12px;
    min-width: 200px;
    
    .info-section {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      h4 {
        margin: 0 0 8px 0;
        font-size: 12px;
        color: #1f5582;
        font-weight: 600;
      }
      
      .info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        font-size: 11px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        span:first-child {
          color: #666;
        }
        
        span:last-child {
          font-weight: 500;
          color: #333;
        }
      }
    }
  }
}
</style>
