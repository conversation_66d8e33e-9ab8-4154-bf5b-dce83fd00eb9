# 智能供水平台 - Windows部署包

## 📦 包含文件

- `smart-water-server.exe` - Go HTTP服务器可执行文件
- `启动服务器.bat` - Windows启动脚本
- `dist/` - 前端打包文件目录
- `README-部署说明.md` - 本说明文件

## 🚀 快速启动

### 方法1：双击启动（推荐）
直接双击 `启动服务器.bat` 文件即可启动服务器

### 方法2：命令行启动
```cmd
smart-water-server.exe
```

## 🌐 访问地址

启动成功后，在浏览器中访问：
- **本地访问**: http://localhost:8080
- **局域网访问**: http://你的IP地址:8080

## 📋 系统要求

- Windows 7/8/10/11
- 无需安装Go环境（已编译为可执行文件）
- 端口8080未被占用

## 🔧 配置说明

### 修改端口
如果需要修改端口，请编辑 `server.go` 文件中的 `port := "8080"` 行，然后重新编译：
```cmd
go build -o smart-water-server.exe server.go
```

### 功能特性
- ✅ 支持Vue Router单页应用路由
- ✅ 自动处理CORS跨域问题
- ✅ 高性能Go HTTP服务器
- ✅ 支持所有静态资源（CSS、JS、图片等）

## 🛠️ 故障排除

### 端口被占用
如果提示端口8080被占用，请：
1. 关闭占用8080端口的其他程序
2. 或修改服务器端口配置

### 找不到dist目录
请确保：
1. `dist` 目录存在于当前目录下
2. 已运行前端打包命令生成dist文件

### 防火墙问题
如果局域网无法访问，请检查Windows防火墙设置，允许程序通过防火墙。

## 📞 技术支持

如有问题，请检查：
1. 控制台输出的错误信息
2. 浏览器开发者工具的网络请求
3. Windows事件查看器的应用程序日志

---
*智能供水平台 v2.0.0*