/*!************************************************************************
*
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2017 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
if(typeof s7viewers=="undefined"){s7viewers={}}else{if(typeof s7viewers!="object"){throw new Error("Cannot initialize a root 's7viewers' package. s7viewers is not an object")}}if(!s7viewers.ZoomVerticalViewer){(function(){var a;s7viewers.ZoomVerticalViewer=function(b){this.sdkBasePath="../../s7viewersdk/2025.5/ZoomVerticalViewer/";this.containerId=null;this.params={};this.handlers=[];this.onInitFail=null;this.initializationComplete=false;this.initCalled=false;this.firstMediasetParsed=false;this.isDisposed=false;this.utilsScriptElm=null;this.fixinputmarker=null;this.indicatorMode="item";this.numberOfItems=null;this.sdkProvided=false;this.lockurldomains=true;this.defaultCSS="ZoomVerticalViewer_light.css";if(typeof b=="object"){if(b.containerId){this.setContainerId(b.containerId)}if(b.params){for(var c in b.params){if(b.params.hasOwnProperty(c)&&b.params.propertyIsEnumerable(c)){this.setParam(c,b.params[c])}}}if(b.handlers){this.setHandlers(b.handlers)}if(b.localizedTexts){this.setLocalizedTexts(b.localizedTexts)}}};s7viewers.ZoomVerticalViewer.cssClassName="s7zoomverticalviewer";s7viewers.ZoomVerticalViewer.prototype.modifiers={indicatorMode:{params:["indicatormode"],defaults:["item"],ranges:[["item","page"]]}};s7viewers.ZoomVerticalViewer.prototype.setContainerId=function(b){if(this.isDisposed){return}this.containerId=b||null};s7viewers.ZoomVerticalViewer.getCodeBase=function(){var h="";var c="";var f=null;if(document.scripts){f=document.scripts}else{f=document.getElementsByTagName("script")}for(var e=0;e<f.length;e++){var g=f[e].src;var b=/^\s*(http[s]?:\/\/[^\/]*)?(.*)(\/(js|js_orig)\/ZoomVerticalViewer\.js)/.exec(g);if(b&&b.length==5){if(typeof b[1]!=="undefined"){h=b[1]}h+=b[2];c=g;break}}if((h!="")&&(h.lastIndexOf("/")!=h.length-1)){h+="/"}var d=/\/etc\/dam\/viewers\//;s7viewers.ZoomVerticalViewer.codebase={contentUrl:h,isDAM:d.test(c)}};s7viewers.ZoomVerticalViewer.getCodeBase();s7viewers.ZoomVerticalViewer.prototype.getContentUrl=function(){return s7viewers.ZoomVerticalViewer.codebase.contentUrl};s7viewers.ZoomVerticalViewer.prototype.symbols={"Container.LABEL":"Zoom viewer","PanRightButton.TOOLTIP":"","PanLeftButton.TOOLTIP":"","ScrollRightButton.TOOLTIP":"","ScrollLeftButton.TOOLTIP":""};s7viewers.ZoomVerticalViewer.prototype.includeViewer=function(){a.Util.lib.include("s7sdk.set.MediaSet");a.Util.lib.include("s7sdk.image.ZoomView");a.Util.lib.include("s7sdk.image.FlyoutZoomView");a.Util.lib.include("s7sdk.common.Button");a.Util.lib.include("s7sdk.common.Container");a.Util.lib.include("s7sdk.set.Swatches2");a.Util.lib.include("s7sdk.set.SetIndicator");this.trackingManager=new a.TrackingManager();var d={en:this.symbols,defaultLocale:"en"};this.s7params=new a.ParameterManager(null,null,{asset:"MediaSet.asset"},this.getContentUrl()+this.defaultCSS,this.lockurldomains);var f="";if(this.s7params.params.config&&(typeof(this.s7params.params.config)=="string")){f=",";if(this.s7params.params.config.indexOf("/")>-1){f+=this.s7params.params.config.split("/")[1]}else{f+=this.s7params.params.config}}this.s7params.setViewer("515,2025.5.0"+f);this.s7params.setDefaultLocalizedTexts(d);for(var b in this.params){if(b!="localizedtexts"){this.s7params.push(b,this.params[b])}else{this.s7params.setLocalizedTexts(this.params[b])}}this.s7params.push("OOTBPresetCSSFileToClassMap",{html5_zoomverticalviewer_dark:"s7zoomverticalviewer_dark",html5_zoomverticalviewer_light:""});this.container=null;this.swatchesContainerElm=null;this.viewContainerElm=null;this.pageIndicatorContainerElm=null;this.zoomView=null;this.flyoutZoomView=null;this.activeView=null;this.isFlyoutView=null;this.mediaSet=null;this.nextButton=null;this.prevButton=null;this.swatches=null;this.singleImage=null;this.setIndicator=null;this.innerContainer=null;this.initialFrame=0;this.currentFrame=null;this.isOrientationMarkerForcedChanged=false;var c=this;function g(){c.s7params.params.aemmode=s7viewers.ZoomVerticalViewer.codebase.isDAM?"1":"0";c.s7params.push("ZoomView.frametransition","slide");c.s7params.push("FlyoutZoomView.frametransition","fade");c.s7params.push("Swatches2.partialswatches","1");c.s7params.push("Swatches2.autoscroll","1");c.s7params.push("SetIndicator.autohide","1,10");c.s7params.push("FlyoutZoomView.enablehd","never");if(a.browser.device.name=="desktop"){c.s7params.push("ZoomView.singleclick","zoomReset")}if(a.browser.device.name=="desktop"){c.s7params.push("ZoomView.doubleclick","reset")}var l=c.getParam("fixinputmarker");if(l){c.fixinputmarker=(l=="s7touchinput"||l=="s7mouseinput")?c.fixinputmarker=l:null}var h=c.getURLParameter("fixinputmarker");if(h){c.fixinputmarker=(h=="s7touchinput"||h=="s7mouseinput")?c.fixinputmarker=h:null}if(c.fixinputmarker){if(c.fixinputmarker==="s7mouseinput"){c.addClass(c.containerId,"s7mouseinput")}else{if(c.fixinputmarker==="s7touchinput"){c.addClass(c.containerId,"s7touchinput")}}}else{if(a.browser.supportsTouch()){c.addClass(c.containerId,"s7touchinput")}else{c.addClass(c.containerId,"s7mouseinput")}}var k=c.s7params.get("presetClasses");if(k&&k.length>0){k.forEach(function(m){c.addClass(c.containerId,m)})}var j=c.getParam("indicatormode");if(j){c.indicatorMode=(j=="page"||j=="item")?c.indicatorMode=j:"page"}var i=c.getURLParameter("indicatormode");if(i){c.indicatorMode=(i=="page"||i=="item")?c.indicatorMode=i:"page"}c.parseMods();c.pageIndicatorContainerElm=document.createElement("div");c.pageIndicatorContainerElm.id=c.containerId+"_pageIndicatorContainer";c.pageIndicatorContainerElm.className="s7pageindicatorcontainer";c.swatchesContainerElm=document.createElement("div");c.swatchesContainerElm.id=c.containerId+"_swatchesContainer";c.swatchesContainerElm.className="s7swatchescontainer";c.viewContainerElm=document.createElement("div");c.viewContainerElm.id=c.containerId+"_viewContainer";c.viewContainerElm.className="s7viewcontainer";c.container=new a.common.Container(c.containerId,c.s7params,c.containerId+"_container");if(c.container.isInLayout()){e()}else{c.container.addEventListener(a.event.ResizeEvent.ADDED_TO_LAYOUT,e,false)}}function e(){var p=(a.browser.device.name=="desktop");c.container.removeEventListener(a.event.ResizeEvent.ADDED_TO_LAYOUT,e,false);var r=document.getElementById(c.containerId);var n=r.style.minHeight;r.style.minHeight="1px";var s=document.createElement("div");s.style.position="relative";s.style.width="100%";s.style.height="100%";r.appendChild(s);var l=s.offsetHeight;if(s.offsetHeight<=1){r.style.height="100%";l=s.offsetHeight}r.removeChild(s);r.style.minHeight=n;var o=false;switch(c.s7params.get("responsive","auto")){case"fit":o=false;break;case"constrain":o=true;break;default:o=l==0;break}c.updateCSSMarkers();c.updateOrientationMarkers();if(c.container.isFixedSize()){c.viewerMode="fixed"}else{if(o){c.viewerMode="ratio"}else{c.viewerMode="free"}}c.innerContainer=document.getElementById(c.container.getInnerContainerId());c.innerContainer.appendChild(c.swatchesContainerElm);c.innerContainer.appendChild(c.pageIndicatorContainerElm);c.innerContainer.appendChild(c.viewContainerElm);c.flyoutZoomView=new a.image.FlyoutZoomView(c.viewContainerElm.id,c.s7params,c.containerId+"_flyoutZoomView");c.zoomView=new a.image.ZoomView(c.viewContainerElm.id,c.s7params,c.containerId+"_zoomView");c.isFlyoutView=!a.browser.supportsTouch();if(c.isFlyoutView){c.trackingManager.attach(c.flyoutZoomView);c.zoomView.setCSS(".s7zoomview","display","none")}else{c.trackingManager.attach(c.zoomView);c.flyoutZoomView.setCSS(".s7flyoutzoomview","display","none")}c.prevButton=new a.PanLeftButton(c.viewContainerElm.id,c.s7params,c.containerId+"_prevButton");c.nextButton=new a.PanRightButton(c.viewContainerElm.id,c.s7params,c.containerId+"_nextButton");c.prevButton.setCSS(".s7panleftbutton","visibility","hidden");c.nextButton.setCSS(".s7panrightbutton","visibility","hidden");c.setIndicator=new a.set.SetIndicator(c.pageIndicatorContainerElm.id,c.s7params,c.containerId+"_setIndicator");c.swatches=new a.set.Swatches2(c.swatchesContainerElm.id,c.s7params,c.containerId+"_swatches");c.trackingManager.attach(c.swatches);w();c.mediaSet=new a.set.MediaSet(null,c.s7params,c.containerId+"_mediaset");c.trackingManager.attach(c.mediaSet);c.notCustomSize=c.container.isPopup()&&!c.container.isFixedSize();c.updateCSSMarkers();c.updateOrientationMarkers();c.trackingManager.setCallback(u);if((typeof(AppMeasurementBridge)=="function")&&(c.isConfig2Exist==true)){c.appMeasurementBridge=new AppMeasurementBridge(c.trackingParams)}if(c.viewerMode=="ratio"){r.style.height="auto"}if(!c.isFlyoutView){c.zoomView.addEventListener(a.event.AssetEvent.ASSET_CHANGED,k,false)}c.setIndicator.addEventListener(a.event.SwatchEvent.SWATCH_PAGE_CHANGE,j);c.swatches.addEventListener(a.AssetEvent.SWATCH_SELECTED_EVENT,v,false);c.swatches.addEventListener(a.event.SwatchEvent.SWATCH_PAGE_CHANGE,m,false);c.mediaSet.addEventListener(a.AssetEvent.NOTF_SET_PARSED,t,false);c.container.addEventListener(a.event.ResizeEvent.COMPONENT_RESIZE,q,false);c.container.addEventListener(a.event.ResizeEvent.SIZE_MARKER_CHANGE,h,false);c.prevButton.addEventListener("click",function(){var x=c.currentFrame;if(x>0){x=x-1}else{x=0}c.swatches.selectSwatch(x)});c.nextButton.addEventListener("click",function(){var x=c.currentFrame;if(x<c.numberOfItems-1){x=x+1}else{x=c.numberOfItems-1}c.swatches.selectSwatch(x)});function t(z){var x=z.s7event.asset;c.currentFrame=null;c.initialFrame=Math.max(0,parseInt((typeof(c.s7params.get("initialframe"))!="undefined")?c.s7params.get("initialframe"):0));if(c.initialFrame<x.items.length){}else{c.initialFrame=0}var y;if(c.viewerMode=="ratio"){var A=x.items[0];y=A.width/A.height}c.numberOfItems=x.items.length;if(x.items.length==1){c.singleImage=true;c.prevButton.setCSS(".s7panleftbutton","visibility","hidden");c.nextButton.setCSS(".s7panrightbutton","visibility","hidden")}else{c.singleImage=false;c.prevButton.setCSS(".s7panleftbutton","visibility","");c.nextButton.setCSS(".s7panrightbutton","visibility","")}if(c.viewerMode=="fixed"){w()}else{if(c.viewerMode=="ratio"){c.container.setModifier({aspect:y})}else{w()}}c.swatches.setMediaSet(x);c.swatches.selectSwatch(c.initialFrame);c.currentFrame=c.initialFrame;i();if((c.handlers.initComplete!=null)&&(typeof c.handlers.initComplete=="function")&&!c.firstMediasetParsed){if(typeof window.s7sdk=="undefined"){window.s7sdk=a}c.handlers.initComplete()}c.firstMediasetParsed=true}function m(x){var y=x.s7event.page;if(c.indicatorMode==="page"){c.setIndicator.setSelectedPage(y)}}function w(){if(c.swatchesContainerElm.offsetWidth!=0&&c.swatchesContainerElm.offsetHeight!=0){c.swatches.resize(c.swatchesContainerElm.offsetWidth,c.swatchesContainerElm.offsetHeight)}if(c.pageIndicatorContainerElm.offsetWidth!=0&&c.pageIndicatorContainerElm.offsetHeight!=0){c.setIndicator.resize(c.pageIndicatorContainerElm.offsetWidth,c.pageIndicatorContainerElm.offsetHeight)}if(!c.isFlyoutView){if(c.viewContainerElm.offsetWidth!=0&&c.viewContainerElm.offsetHeight!=0){c.zoomView.resize(c.viewContainerElm.offsetWidth,c.viewContainerElm.offsetHeight)}}else{if(c.viewContainerElm.offsetWidth!=0&&c.viewContainerElm.offsetHeight!=0){c.flyoutZoomView.resize(c.viewContainerElm.offsetWidth,c.viewContainerElm.offsetHeight)}}}function q(x){if((typeof(x.target)=="undefined")||(x.target==document.getElementById(c.containerId+"_container"))){if(!c.container.isInLayout()){return}w()}}function i(){var x=c.swatches.getPageCount();if(c.indicatorMode==="item"){c.setIndicator.setNumberOfPages(c.numberOfItems)}else{c.setIndicator.setNumberOfPages(x)}}function h(x){c.updateCSSMarkers();w()}function v(y){var x=y.s7event.asset;if(c.currentFrame!=y.s7event.frame){c.currentFrame=y.s7event.frame;if(c.isFlyoutView){c.flyoutZoomView.setItem(x)}else{c.zoomView.setModifier({frametransition:"fade"});c.zoomView.setItem(x)}if(c.setIndicator.getSelectedPage()!=c.currentFrame){if(c.indicatorMode==="item"){c.setIndicator.setSelectedPage(y.s7event.frame)}else{c.setIndicator.setSelectedPage(c.swatches.getCurrentPage())}}if(c.numberOfItems>1){c.prevButton.activate();c.nextButton.activate()}if(c.currentFrame==c.numberOfItems-1){c.nextButton.deactivate()}else{if(c.currentFrame==0){c.prevButton.deactivate()}}}}function k(x){c.swatches.selectSwatch(x.s7event.frame);c.zoomView.setModifier({frametransition:"slide"})}function j(x){if(c.indicatorMode==="item"){c.swatches.selectSwatch(x.s7event.page)}else{c.swatches.setCurrentPage(x.s7event.page)}}function u(z,y,C,x,A){if(!c.handlers.trackEvent&&c.isConfig2Exist!=true&&a.Modifier.parse(c.s7params.get("launch","true"),[true]).values[0]){if(typeof(_satellite)!="undefined"&&_satellite._dmviewers_v001){c.handlers.trackEvent=_satellite._dmviewers_v001().trackingFn}}if(c.appMeasurementBridge){c.appMeasurementBridge.track(z,y,C,x,A)}if(c.handlers.trackEvent){if(typeof window.s7sdk=="undefined"){window.s7sdk=a}var B=c.containerId;c.handlers.trackEvent(B,y,C,x,A)}if("s7ComponentEvent" in window){s7ComponentEvent(z,y,C,x,A)}}}this.s7params.addEventListener(a.Event.SDK_READY,function(){c.initSiteCatalyst(c.s7params,g)},false);this.s7params.setProvidedSdk(this.sdkProvided);this.s7params.init()};s7viewers.ZoomVerticalViewer.prototype.setParam=function(b,c){if(this.isDisposed){return}this.params[b]=c};s7viewers.ZoomVerticalViewer.prototype.getParam=function(c){var d=c.toLowerCase();for(var b in this.params){if(b.toLowerCase()==d){return this.params[b]}}return null};s7viewers.ZoomVerticalViewer.prototype.setParams=function(b){if(this.isDisposed){return}var e=b.split("&");for(var c=0;c<e.length;c++){var d=e[c].split("=");if(d.length>1){this.setParam(d[0],decodeURIComponent(e[c].split("=")[1]))}}};s7viewers.ZoomVerticalViewer.prototype.s7sdkUtilsAvailable=function(){if(s7viewers.ZoomVerticalViewer.codebase.isDAM){return typeof(s7viewers.s7sdk)!="undefined"}else{return(typeof(s7classic)!="undefined")&&(typeof(s7classic.s7sdk)!="undefined")}};s7viewers.ZoomVerticalViewer.prototype.init=function(){if(this.isDisposed){return}if(this.initCalled){return}this.initCalled=true;if(this.initializationComplete){return this}this.lockurldomains=(Boolean(Number(this.params.lockurldomains))||typeof this.params.lockurldomains=="undefined")?1:0;var i=document.getElementById(this.containerId);if(i.className!=""){if(i.className.indexOf(s7viewers.ZoomVerticalViewer.cssClassName)!=-1){}else{i.className+=" "+s7viewers.ZoomVerticalViewer.cssClassName}}else{i.className=s7viewers.ZoomVerticalViewer.cssClassName}this.s7sdkNamespace=s7viewers.ZoomVerticalViewer.codebase.isDAM?"s7viewers":"s7classic";var d=this.getContentUrl()+this.sdkBasePath+"js/s7sdk/utils/Utils.js?namespace="+this.s7sdkNamespace;var f=null;if(document.scripts){f=document.scripts}else{f=document.getElementsByTagName("script")}if(this.s7sdkUtilsAvailable()){a=(s7viewers.ZoomVerticalViewer.codebase.isDAM?s7viewers.s7sdk:s7classic.s7sdk);this.sdkProvided=true;if(this.isDisposed){return}a.Util.init();this.includeViewer();this.initializationComplete=true}else{if(!this.s7sdkUtilsAvailable()&&(s7viewers.ZoomVerticalViewer.codebase.isDAM?s7viewers.S7SDK_S7VIEWERS_LOAD_STARTED:s7viewers.S7SDK_S7CLASSIC_LOAD_STARTED)){this.sdkProvided=true;var h=this;var g=setInterval(function(){if(h.s7sdkUtilsAvailable()){clearInterval(g);a=(s7viewers.ZoomVerticalViewer.codebase.isDAM?s7viewers.s7sdk:s7classic.s7sdk);if(h.isDisposed){return}a.Util.init();h.includeViewer();h.initializationComplete=true}},100)}else{this.utilsScriptElm=document.createElement("script");this.utilsScriptElm.setAttribute("language","javascript");this.utilsScriptElm.setAttribute("type","text/javascript");var e=document.getElementsByTagName("head")[0];var c=this;function b(){if(!c.utilsScriptElm.executed){c.utilsScriptElm.executed=true;a=(s7viewers.ZoomVerticalViewer.codebase.isDAM?s7viewers.s7sdk:s7classic.s7sdk);if(c.s7sdkUtilsAvailable()&&a.Util){if(c.isDisposed){return}a.Util.init();c.includeViewer();c.initializationComplete=true;c.utilsScriptElm.onreadystatechange=null;c.utilsScriptElm.onload=null;c.utilsScriptElm.onerror=null}}}if(typeof(c.utilsScriptElm.readyState)!="undefined"){c.utilsScriptElm.onreadystatechange=function(){if(c.utilsScriptElm.readyState=="loaded"){e.appendChild(c.utilsScriptElm)}else{if(c.utilsScriptElm.readyState=="complete"){b()}}};c.utilsScriptElm.setAttribute("src",d)}else{c.utilsScriptElm.onload=function(){b()};c.utilsScriptElm.onerror=function(){};c.utilsScriptElm.setAttribute("src",d);e.appendChild(c.utilsScriptElm);c.utilsScriptElm.setAttribute("data-src",c.utilsScriptElm.getAttribute("src"));c.utilsScriptElm.setAttribute("src","?namespace="+c.s7sdkNamespace)}if(s7viewers.ZoomVerticalViewer.codebase.isDAM){s7viewers.S7SDK_S7VIEWERS_LOAD_STARTED=true}else{s7viewers.S7SDK_S7CLASSIC_LOAD_STARTED=true}}}return this};s7viewers.ZoomVerticalViewer.prototype.getDomain=function(b){var c=/(^http[s]?:\/\/[^\/]+)/i.exec(b);if(c==null){return""}else{return c[1]}};s7viewers.ZoomVerticalViewer.prototype.setAsset=function(b){if(this.isDisposed){return}if(this.mediaSet){this.mediaSet.setAsset(b)}else{this.setParam("asset",b)}};s7viewers.ZoomVerticalViewer.prototype.setLocalizedTexts=function(b){if(this.isDisposed){return}if(this.s7params){this.s7params.setLocalizedTexts(b)}else{this.setParam("localizedtexts",b)}};s7viewers.ZoomVerticalViewer.prototype.initSiteCatalyst=function(i,c){var f=i.get("asset",null,"MediaSet").split(",")[0].split(":")[0];this.isConfig2Exist=false;if(f.indexOf("/")!=-1){var d=a.MediaSetParser.findCompanyNameInAsset(f);var h=i.get("config2");this.isConfig2Exist=(h!=""&&typeof h!="undefined");if(this.isConfig2Exist){this.trackingParams={siteCatalystCompany:d,config2:h,isRoot:i.get("serverurl")};var b=this.getContentUrl()+"../AppMeasurementBridge.jsp?company="+d+(h==""?"":"&preset="+h);if(i.get("serverurl",null)){b+="&isRoot="+i.get("serverurl")}var g=document.createElement("script");g.setAttribute("language","javascript");g.setAttribute("type","text/javascript");g.setAttribute("src",b);var e=document.getElementsByTagName("head");g.onload=g.onerror=function(){if(!g.executed){g.executed=true;if(typeof c=="function"){c()}g.onreadystatechange=null;g.onload=null;g.onerror=null}};g.onreadystatechange=function(){if(g.readyState=="complete"||g.readyState=="loaded"){setTimeout(function(){if(!g.executed){g.executed=true;if(typeof c=="function"){c()}}g.onreadystatechange=null;g.onload=null;g.onerror=null},0)}};e[0].appendChild(g)}else{if(typeof c=="function"){c()}}}};s7viewers.ZoomVerticalViewer.prototype.getComponent=function(b){if(this.isDisposed){return null}switch(b){case"container":return this.container||null;case"mediaSet":return this.mediaSet||null;case"flyoutZoomView":return this.flyoutZoomView||null;case"zoomView":return this.zoomView||null;case"nextButton":return this.nextButton||null;case"prevButton":return this.prevButton||null;case"swatches":return this.swatches||null;case"setIndicator":return this.setIndicator||null;case"parameterManager":return this.s7params||null;default:return null}};s7viewers.ZoomVerticalViewer.prototype.setHandlers=function(c){if(this.isDisposed){return}if(this.initCalled){return}this.handlers=[];for(var b in c){if(!c.hasOwnProperty(b)){continue}if(typeof c[b]!="function"){continue}this.handlers[b]=c[b]}};s7viewers.ZoomVerticalViewer.prototype.getModifiers=function(){return this.modifiers};s7viewers.ZoomVerticalViewer.prototype.setModifier=function(f){if(this.isDisposed){return}var h,c,j,b,g,e;for(h in f){if(!this.modifiers.hasOwnProperty(h)){continue}c=this.modifiers[h];try{b=f[h];if(c.parseParams===false){g=new a.Modifier([b!=""?b:c.defaults[0]])}else{g=a.Modifier.parse(b,c.defaults,c.ranges)}if(g.values.length==1){this[h]=g.values[0];this.setModifierInternal(h)}else{if(g.values.length>1){j={};for(e=0;e<g.values.length;e++){j[c.params[e]]=g.values[e]}this[h]=j;this.setModifierInternal(h)}}}catch(d){throw new Error("Unable to process modifier: '"+h+"'. "+d)}}};s7viewers.ZoomVerticalViewer.prototype.setModifierInternal=function(b){switch(b){default:break}};s7viewers.ZoomVerticalViewer.prototype.parseMods=function(){var g,c,h,b,f,e;for(g in this.modifiers){if(!this.modifiers.hasOwnProperty(g)){continue}c=this.modifiers[g];try{b=this.s7params.get(g,"");if(c.parseParams===false){f=new a.Modifier([b!=""?b:c.defaults[0]])}else{f=a.Modifier.parse(b,c.defaults,c.ranges)}if(f.values.length==1){this[g]=f.values[0]}else{if(f.values.length>1){h={};for(e=0;e<f.values.length;e++){h[c.params[e]]=f.values[e]}this[g]=h}}}catch(d){throw new Error("Unable to process modifier: '"+g+"'. "+d)}}};s7viewers.ZoomVerticalViewer.prototype.updateCSSMarkers=function(){var c=this.container.getSizeMarker();var b;if(c==a.common.Container.SIZE_MARKER_NONE){return}if(c==a.common.Container.SIZE_MARKER_LARGE){b="s7size_large"}else{if(c==a.common.Container.SIZE_MARKER_SMALL){b="s7size_small"}else{if(c==a.common.Container.SIZE_MARKER_MEDIUM){b="s7size_medium"}}}if(this.containerId){this.setNewSizeMarker(this.containerId,b)}this.reloadInnerComponents()};s7viewers.ZoomVerticalViewer.prototype.reloadInnerComponents=function(){var c=this.s7params.getRegisteredComponents();for(var b=0;b<c.length;b++){if(c[b]&&c[b].restrictedStylesInvalidated()){c[b].reload()}}};s7viewers.ZoomVerticalViewer.prototype.setNewSizeMarker=function(f,c){var b=document.getElementById(f).className;var d=/^(.*)(s7size_small|s7size_medium|s7size_large)(.*)$/gi;var e;if(b.match(d)){e=b.replace(d,"$1"+c+"$3")}else{e=b+" "+c}if(b!=e){document.getElementById(f).className=e}};s7viewers.ZoomVerticalViewer.prototype.dispose=function(){if(this.appMeasurementBridge){this.appMeasurementBridge.dispose();this.appMeasurementBridge=null}if(this.trackingManager){this.trackingManager.dispose();this.trackingManager=null}if(this.setIndicator){this.setIndicator.dispose();this.setIndicator=null}if(this.nextButton){this.nextButton.dispose();this.nextButton=null}if(this.prevButton){this.prevButton.dispose();this.prevButton=null}if(this.swatches){this.swatches.dispose();this.swatches=null}if(this.zoomView){this.zoomView.dispose();this.zoomView=null}if(this.flyoutZoomView){this.flyoutZoomView.dispose();this.flyoutZoomView=null}if(this.mediaSet){this.mediaSet.dispose();this.mediaSet=null}if(this.s7params){this.s7params.dispose();this.s7params=null}if(this.container){if(this.innerContainer){this.innerContainer.removeChild(this.swatchesContainerElm);this.innerContainer.removeChild(this.pageIndicatorContainerElm);this.innerContainer.removeChild(this.viewContainerElm)}var e=[s7viewers.ZoomVerticalViewer.cssClassName,"s7touchinput","s7mouseinput","s7size_large","s7size_small","s7size_medium"];var c=document.getElementById(this.containerId).className.split(" ");for(var d=0;d<e.length;d++){var b=c.indexOf(e[d]);if(b!=-1){c.splice(b,1)}}document.getElementById(this.containerId).className=c.join(" ");this.container.dispose();this.container=null}this.handlers=[];this.isDisposed=true};s7viewers.ZoomVerticalViewer.prototype.updateOrientationMarkers=function(){if(!this.isOrientationMarkerForcedChanged){var b;if(window.innerWidth>window.innerHeight){b="s7device_landscape"}else{b="s7device_portrait"}if(document.getElementById(this.containerId).className.indexOf(b)==-1){this.setNewOrientationMarker(this.containerId,b);this.reloadInnerComponents()}}};s7viewers.ZoomVerticalViewer.prototype.setNewOrientationMarker=function(f,c){var b=document.getElementById(f).className;var d=/^(.*)(s7device_landscape|s7device_portrait)(.*)$/gi;var e;if(b.match(d)){e=b.replace(d,"$1"+c+"$3")}else{e=b+" "+c}if(b!=e){document.getElementById(f).className=e}};s7viewers.ZoomVerticalViewer.prototype.forceDeviceOrientationMarker=function(b){switch(b){case"s7device_portrait":case"s7device_landscape":this.isOrientationMarkerForcedChanged=true;if(this.containerId){this.setNewOrientationMarker(this.containerId,b)}this.reloadInnerComponents();break;case null:this.isOrientationMarkerForcedChanged=false;this.updateOrientationMarkers();break;default:break}};s7viewers.ZoomVerticalViewer.prototype.getURLParameter=function(c){var b=a.ParameterManager.getSanitizedParameters(a.query.params,this.lockurldomains);return b[c]};s7viewers.ZoomVerticalViewer.prototype.addClass=function(d,c){var b=document.getElementById(d).className.split(" ");if(b.indexOf(c)==-1){b[b.length]=c;document.getElementById(d).className=b.join(" ")}}})()};