<template>
  <div class="hydraulic-layout">
    <div class="layout-header">
      <h3>水力布局</h3>
      <p>配置泵系统的安装方式和运行参数</p>
    </div>
    
    <div class="layout-form">
      <!-- 安装类型 -->
      <div class="form-group">
        <label>安装类型</label>
        <el-select 
          v-model="config.installationType" 
          @change="onInstallationTypeChange"
          placeholder="选择安装类型"
        >
          <el-option
            v-for="option in configOptions.installationTypes"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </div>
      
      <!-- 并联配置 -->
      <template v-if="config.installationType === 'parallel'">
        <!-- 安装类型（泵数量） -->
        <div class="form-group">
          <label>安装类型</label>
          <el-select
            v-model="config.pumpCount"
            @change="onPumpCountChange"
            placeholder="选择安装类型"
          >
            <el-option value="1" label="单泵" />
            <el-option value="2" label="2个并联泵" />
            <el-option value="3" label="3个并联泵" />
            <el-option value="4" label="4个并联泵" />
            <el-option value="5" label="5个并联泵" />
            <el-option value="6" label="6个并联泵" />
            <el-option value="7" label="7台并联泵" />
            <el-option value="8" label="8台并联泵" />
            <el-option value="9" label="9 parallel-connected pumps" />
            <el-option value="10" label="10 parallel-connected pumps" />
            <el-option value="11" label="11 parallel-connected pumps" />
            <el-option value="12" label="12 parallel-connected pumps" />
            <el-option value="13" label="13 parallel-connected pumps" />
            <el-option value="14" label="14 parallel-connected pumps" />
            <el-option value="15" label="15 parallel-connected pumps" />
          </el-select>
        </div>
        
        <!-- 备用泵数量 -->
        <div class="form-group">
          <label>其中：备用泵的数量</label>
          <el-select
            v-model="config.standbyPumps"
            @change="onStandbyChange"
            placeholder="选择备用泵数量"
          >
            <el-option value="0" label="0" />
            <el-option value="1" label="1" />
            <el-option value="2" label="2" />
            <el-option value="3" label="3" />
          </el-select>
        </div>

        <!-- 运行泵数量 -->
        <div class="form-group">
          <label>Number of running pumps</label>
          <el-select
            v-model="config.runningPumps"
            placeholder="选择运行泵数量"
            @change="onRunningPumpsChange"
          >
            <el-option value="" label="" />
            <el-option
              v-for="num in availableRunningPumps"
              :key="num"
              :label="num.toString()"
              :value="num"
            />
          </el-select>
        </div>
        
        <!-- 变速控制 -->
        <div class="form-group">
          <label>变速</label>
          <el-select
            v-model="config.variableSpeed"
            placeholder="选择变速控制"
            @change="onVariableSpeedChange"
          >
            <el-option value="none" label="不是的" />
            <el-option value="single" label="只有一个泵" />
            <el-option value="all" label="所有泵" />
          </el-select>
        </div>

        <!-- 频率设置 -->
        <template v-if="config.variableSpeed !== 'none'">
          <div class="form-group">
            <label>Minimum frequency *</label>
            <div class="frequency-input-group">
              <el-input-number
                v-model="minFrequency"
                :min="0"
                :max="100"
                :step="1"
                :precision="0"
                placeholder="0"
                @change="onFrequencyChange"
              />
              <span class="unit">%</span>
            </div>
          </div>

          <div class="form-group">
            <label>最高频率 *</label>
            <div class="frequency-input-group">
              <el-input-number
                v-model="maxFrequency"
                :min="0"
                :max="100"
                :step="1"
                :precision="0"
                placeholder="100"
                @change="onFrequencyChange"
              />
              <span class="unit">%</span>
            </div>
          </div>
        </template>
      </template>
      
      <!-- 串联配置 -->
      <div class="form-group">
        <label>串联</label>
        <el-select 
          v-model="config.seriesConnection" 
          placeholder="选择串联方式"
        >
          <el-option
            v-for="option in configOptions.seriesOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </div>
      
      <!-- Solar-relevant curves -->
      <div class="form-group">
        <label>Solar-relevant curves</label>
        <el-checkbox v-model="config.solarRelevant" />
      </div>
      
      <!-- 单泵变速 -->
      <template v-if="config.installationType === 'single'">
        <div class="form-group">
          <label>变速</label>
          <el-radio-group v-model="singlePumpVariableSpeed" @change="onSinglePumpVariableSpeedChange">
            <el-radio value="none">不是的</el-radio>
            <el-radio value="yes">是的</el-radio>
          </el-radio-group>
        </div>

        <!-- 单泵变频设置 -->
        <template v-if="singlePumpVariableSpeed === 'yes'">
          <div class="form-group">
            <label>变频设置</label>
            <div class="frequency-controls">
              <div class="frequency-input">
                <label>额定频率 (Hz)</label>
                <el-input-number
                  v-model="ratedFrequency"
                  :min="30"
                  :max="100"
                  :step="1"
                  :precision="0"
                  placeholder="50"
                />
              </div>
              <div class="frequency-input">
                <label>运行频率 (Hz)</label>
                <el-input-number
                  v-model="operatingFrequency"
                  :min="20"
                  :max="100"
                  :step="0.1"
                  :precision="1"
                  placeholder="50.0"
                  @change="onFrequencyChange"
                />
              </div>
              <div class="frequency-ratio">
                <span>频率比: {{ frequencyRatio.toFixed(3) }}</span>
              </div>
            </div>
          </div>
        </template>
      </template>
    </div>
    
    <!-- 系统性能预览 -->
    <div class="performance-preview" v-if="systemPerformance">
      <h4>系统性能预览</h4>
      <div class="performance-stats">
        <div class="stat-item">
          <span class="label">最大流量:</span>
          <span class="value">{{ systemPerformance.operatingRange.maxFlow.toFixed(1) }} m³/h</span>
        </div>
        <div class="stat-item">
          <span class="label">最大扬程:</span>
          <span class="value">{{ systemPerformance.operatingRange.maxHead.toFixed(1) }} m</span>
        </div>
        <div class="stat-item">
          <span class="label">最高效率:</span>
          <span class="value">{{ systemPerformance.efficiency.maxEfficiency.toFixed(1) }}%</span>
        </div>
        <div class="stat-item">
          <span class="label">最佳效率点:</span>
          <span class="value">
            {{ systemPerformance.efficiency.bestEfficiencyPoint.flow.toFixed(1) }} m³/h @ 
            {{ systemPerformance.efficiency.bestEfficiencyPoint.head.toFixed(1) }} m
          </span>
        </div>
      </div>
    </div>
    
    <!-- 配置验证 -->
    <div class="config-validation" v-if="validationErrors.length > 0">
      <el-alert
        v-for="error in validationErrors"
        :key="error"
        :title="error"
        type="warning"
        :closable="false"
        style="margin-bottom: 8px;"
      />
    </div>
    
    <!-- 操作按钮 -->
    <div class="layout-actions">
      <el-button type="primary" @click="applyConfiguration">
        应用配置
      </el-button>
      <el-button @click="resetConfiguration">
        重置
      </el-button>
      <el-button type="success" @click="getRecommendation">
        智能推荐
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { 
  GrundfosSystemCalculator, 
  GrundfosConfigValidator,
  type HydraulicLayoutConfig,
  type PumpDataPoint,
  type NPSHDataPoint,
  type SystemPerformanceResult
} from '@/utils/pump-system-calculator'

// Props
interface Props {
  singlePumpData: PumpDataPoint[]
  npshData: NPSHDataPoint[]
  modelValue: HydraulicLayoutConfig
}

const props = withDefaults(defineProps<Props>(), {
  singlePumpData: () => [],
  npshData: () => []
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [config: HydraulicLayoutConfig]
  'configuration-changed': [result: SystemPerformanceResult]
}>()

// 响应式数据
const config = ref<HydraulicLayoutConfig>({ ...props.modelValue })
const singlePumpVariableSpeed = ref<'none' | 'yes'>('none')
const systemPerformance = ref<SystemPerformanceResult | null>(null)
const validationErrors = ref<string[]>([])

// 变频相关数据
const minFrequency = ref(0) // 最小频率 %
const maxFrequency = ref(100) // 最高频率 %
const ratedFrequency = ref(50) // 额定频率 50Hz
const operatingFrequency = ref(50.0) // 运行频率
const frequencyRatio = computed(() => operatingFrequency.value / ratedFrequency.value)

// 配置选项
const configOptions = GrundfosSystemCalculator.getGrundfosConfigOptions()

// 计算可用的运行泵数量
const availableRunningPumps = computed(() => {
  const maxRunning = config.value.pumpCount - config.value.standbyPumps
  return Array.from({ length: maxRunning }, (_, i) => i + 1)
})

// 运行泵数量变化处理
const onRunningPumpsChange = () => {
  console.log('🔄 运行泵数量变化', config.value.runningPumps)
  calculateSystemPerformance()
}

// 监听配置变化 - 使用防抖避免无限循环
let isCalculating = false
watch(config, (newConfig) => {
  emit('update:modelValue', newConfig)

  // 防止在计算过程中触发新的计算
  if (!isCalculating) {
    calculateSystemPerformance()
    validateConfiguration()
  }
}, { deep: true })

// 安装类型变化处理
const onInstallationTypeChange = () => {
  if (config.value.installationType === 'single') {
    config.value.pumpCount = 1
    config.value.standbyPumps = 0
    config.value.runningPumps = 1
    config.value.variableSpeed = 'none'
  } else {
    config.value.pumpCount = 2
    config.value.standbyPumps = 0
    config.value.runningPumps = 2
  }
}

// 泵数量变化处理
const onPumpCountChange = () => {
  // 确保备用泵数量不超过总数
  if (config.value.standbyPumps >= config.value.pumpCount) {
    config.value.standbyPumps = Math.max(0, config.value.pumpCount - 1)
  }
  
  // 确保运行泵数量合理
  const maxRunning = config.value.pumpCount - config.value.standbyPumps
  if (config.value.runningPumps > maxRunning) {
    config.value.runningPumps = maxRunning
  }
}

// 备用泵数量变化处理
const onStandbyChange = () => {
  const maxRunning = config.value.pumpCount - config.value.standbyPumps
  if (config.value.runningPumps > maxRunning) {
    config.value.runningPumps = maxRunning
  }
}

// 变速控制变化处理
const onVariableSpeedChange = () => {
  console.log('🔄 变速设置变化', config.value.variableSpeed)

  if (config.value.variableSpeed === 'none') {
    operatingFrequency.value = ratedFrequency.value
    minFrequency.value = 0
    maxFrequency.value = 100
  }

  calculateSystemPerformance()
}

// 单泵变速变化处理
const onSinglePumpVariableSpeedChange = () => {
  if (singlePumpVariableSpeed.value === 'none') {
    operatingFrequency.value = ratedFrequency.value
  }
  calculateSystemPerformance()
}

// 频率变化处理
const onFrequencyChange = () => {
  console.log('🔄 变频设置变化', {
    minFrequency: minFrequency.value,
    maxFrequency: maxFrequency.value,
    ratedFrequency: ratedFrequency.value,
    operatingFrequency: operatingFrequency.value,
    frequencyRatio: frequencyRatio.value
  })

  // 根据频率百分比计算实际运行频率
  const frequencyPercent = (minFrequency.value + maxFrequency.value) / 2
  operatingFrequency.value = ratedFrequency.value * (frequencyPercent / 100)

  calculateSystemPerformance()
}

// 计算系统性能
const calculateSystemPerformance = () => {
  if (props.singlePumpData.length === 0) return
  if (isCalculating) return // 防止重复计算

  try {
    isCalculating = true
    // 确定是否启用变频
    const isVariableSpeedEnabled =
      (config.value.installationType === 'parallel' && config.value.variableSpeed !== 'none') ||
      (config.value.installationType === 'single' && singlePumpVariableSpeed.value === 'yes')

    // 计算变频泵数量
    let variableSpeedPumpCount = 0
    if (isVariableSpeedEnabled) {
      switch (config.value.variableSpeed) {
        case 'single':
          variableSpeedPumpCount = 1
          break
        case 'all':
          variableSpeedPumpCount = config.value.runningPumps
          break
        default:
          variableSpeedPumpCount = 0
      }
    }

    // 计算转速比（频率比）
    const speedRatio = isVariableSpeedEnabled ? frequencyRatio.value : 1.0

    console.log('🔧 系统性能计算', {
      installationType: config.value.installationType,
      isVariableSpeedEnabled,
      variableSpeedPumpCount,
      ratedFrequency: ratedFrequency.value,
      operatingFrequency: operatingFrequency.value,
      speedRatio
    })

    const result = GrundfosSystemCalculator.calculateComplexSystem(
      props.singlePumpData,
      props.npshData,
      config.value,
      speedRatio,
      variableSpeedPumpCount
    )

    systemPerformance.value = result
    emit('configuration-changed', result)
  } catch (error) {
    console.error('❌ 计算系统性能失败:', error)
  } finally {
    isCalculating = false
  }
}

// 验证配置
const validateConfiguration = () => {
  validationErrors.value = GrundfosConfigValidator.validateConfig(config.value)
}

// 应用配置
const applyConfiguration = () => {
  if (validationErrors.value.length > 0) {
    console.warn('⚠️ 水力布局配置有误:', validationErrors.value)
    return
  }

  calculateSystemPerformance()
  console.log('✅ 水力布局配置已应用', {
    config: config.value,
    systemPerformance: systemPerformance.value
  })
}

// 重置配置
const resetConfiguration = () => {
  config.value = {
    installationType: 'single',
    pumpCount: 1,
    standbyPumps: 0,
    runningPumps: 1,
    seriesConnection: 'none',
    solarRelevant: false,
    variableSpeed: 'none'
  }
}

// 智能推荐
const getRecommendation = () => {
  if (props.singlePumpData.length === 0) {
    console.warn('⚠️ 智能推荐需要泵性能数据，当前数据点数量:', props.singlePumpData.length)
    return
  }

  // 这里可以根据用户输入的需求来推荐
  // 暂时使用默认值
  const recommended = GrundfosConfigValidator.getRecommendedConfig(
    400, // 需求流量
    35,  // 需求扬程
    props.singlePumpData
  )

  config.value = recommended
  console.log('🤖 已应用智能推荐配置', {
    requirements: { flow: 400, head: 35 },
    recommendedConfig: recommended,
    dataPointsUsed: props.singlePumpData.length
  })
}

// 组件挂载时初始化
onMounted(() => {
  calculateSystemPerformance()
  validateConfiguration()
})
</script>

<style lang="scss" scoped>
.hydraulic-layout {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .layout-header {
    margin-bottom: 20px;
    
    h3 {
      margin: 0 0 8px;
      color: #333;
      font-size: 1.2rem;
    }
    
    p {
      margin: 0;
      color: #666;
      font-size: 0.9rem;
    }
  }
  
  .layout-form {
    .form-group {
      margin-bottom: 16px;
      
      label {
        display: block;
        margin-bottom: 6px;
        font-weight: 500;
        color: #333;
        font-size: 0.9rem;
      }
      
      .el-select,
      .el-radio-group {
        width: 100%;
      }

      .frequency-input-group {
        display: flex;
        align-items: center;
        gap: 8px;

        .el-input-number {
          flex: 1;
          max-width: 120px;
        }

        .unit {
          font-size: 0.9rem;
          color: #666;
          font-weight: 500;
        }
      }

      .frequency-controls {
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e1e6ef;

        .frequency-input {
          display: flex;
          justify-content: space-between;
          align-items: center;

          label {
            margin: 0;
            font-size: 0.85rem;
            color: #666;
            min-width: 100px;
          }

          .el-input-number {
            width: 120px;
          }
        }

        .frequency-ratio {
          text-align: center;
          padding: 8px;
          background: #e3f2fd;
          border-radius: 4px;
          font-weight: 500;
          color: #1976d2;
          font-size: 0.9rem;
        }
      }
    }
  }
  
  .performance-preview {
    margin: 20px 0;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #409eff;
    
    h4 {
      margin: 0 0 12px;
      color: #333;
      font-size: 1rem;
    }
    
    .performance-stats {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
      
      .stat-item {
        display: flex;
        justify-content: space-between;
        
        .label {
          color: #666;
          font-size: 0.85rem;
        }
        
        .value {
          font-weight: 500;
          color: #333;
          font-size: 0.85rem;
        }
      }
    }
  }
  
  .config-validation {
    margin: 16px 0;
  }
  
  .layout-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
    
    .el-button {
      flex: 1;
    }
  }
}

@media (max-width: 768px) {
  .hydraulic-layout {
    padding: 16px;
    
    .performance-stats {
      grid-template-columns: 1fr;
    }
    
    .layout-actions {
      flex-direction: column;
    }
  }
}
</style>
