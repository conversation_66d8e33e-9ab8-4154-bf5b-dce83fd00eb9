import{_ as S}from"./_plugin-vue_export-helper-C6jw_E77.js";/* empty css                  *//* empty css                        *//* empty css                   *//* empty css                         *//* empty css                  *//* empty css                     */import{d as j,r as x,c as m,u as G,a as J,b as p,e,w as t,f as r,g as K,E as P,h as s,i as U,j as O,F as Q,k as W,l as X,m as Y,n as Z,o as c,p as $,q as ee,s as te,t as ne,v as i,x as k,y as ae,z as oe,A as le,B as se,C as de,D as ue,G as _e,H as ce,I as ie,J as re,T as me,K as pe,L as l,M as fe,N as b}from"./index-8zz4iTME.js";const ve={class:"layout-container"},Ee={class:"logo"},ge={key:0,class:"logo-text"},we={class:"header-left"},xe={class:"header-right"},ke=j({__name:"index",setup(be){const f=J(),h=G(),d=x(!1),v=x(!1),y=m(()=>{var a;return((a=h.getRoutes().find(n=>n.name==="Layout"))==null?void 0:a.children)||[]}),C=m(()=>f.path),B=m(()=>{var a;return((a=f.meta)==null?void 0:a.title)||"首页"}),D=()=>{d.value=!d.value},V=a=>{document.documentElement.classList.toggle("dark",a)};return(a,n)=>{const u=P,I=fe,M=O,N=X,E=Z,g=ne,T=te,F=ae,w=ce,L=_e,R=se,z=Y,q=re("router-view"),A=ie,H=pe;return l(),p("div",ve,[e(N,{width:d.value?"64px":"240px",class:"sidebar"},{default:t(()=>[r("div",Ee,[e(u,{size:"32",color:"#409EFF"},{default:t(()=>[e(s(U))]),_:1}),d.value?K("",!0):(l(),p("span",ge,"智慧水务平台"))]),e(M,{"default-active":C.value,collapse:d.value,"unique-opened":!0,router:"",class:"sidebar-menu"},{default:t(()=>[(l(!0),p(Q,null,W(y.value,o=>(l(),c(I,{key:o.path,index:o.path},{title:t(()=>{var _;return[i(k((_=o.meta)==null?void 0:_.title),1)]}),default:t(()=>[e(u,null,{default:t(()=>{var _;return[(l(),c(b((_=o.meta)==null?void 0:_.icon)))]}),_:2},1024)]),_:2},1032,["index"]))),128))]),_:1},8,["default-active","collapse"])]),_:1},8,["width"]),e(H,{class:"main-container"},{default:t(()=>[e(z,{class:"header"},{default:t(()=>[r("div",we,[e(E,{link:"",onClick:D,class:"collapse-btn"},{default:t(()=>[e(u,{size:"20"},{default:t(()=>[d.value?(l(),c(s($),{key:0})):(l(),c(s(ee),{key:1}))]),_:1})]),_:1}),e(T,{separator:"/"},{default:t(()=>[e(g,null,{default:t(()=>n[1]||(n[1]=[i("智慧水务平台")])),_:1,__:[1]}),e(g,null,{default:t(()=>[i(k(B.value),1)]),_:1})]),_:1})]),r("div",xe,[e(F,{modelValue:v.value,"onUpdate:modelValue":n[0]||(n[0]=o=>v.value=o),onChange:V,"inline-prompt":"","active-icon":s(le),"inactive-icon":s(oe),"active-text":"暗色","inactive-text":"亮色"},null,8,["modelValue","active-icon","inactive-icon"]),e(R,{trigger:"click"},{dropdown:t(()=>[e(L,null,{default:t(()=>[e(w,null,{default:t(()=>n[3]||(n[3]=[i("个人中心")])),_:1,__:[3]}),e(w,{divided:""},{default:t(()=>n[4]||(n[4]=[i("退出登录")])),_:1,__:[4]})]),_:1})]),default:t(()=>[e(E,{link:"",class:"user-btn"},{default:t(()=>[e(u,null,{default:t(()=>[e(s(de))]),_:1}),n[2]||(n[2]=r("span",null,"管理员",-1)),e(u,null,{default:t(()=>[e(s(ue))]),_:1})]),_:1,__:[2]})]),_:1})])]),_:1}),e(A,{class:"main-content"},{default:t(()=>[e(q,null,{default:t(({Component:o})=>[e(me,{name:"fade-transform",mode:"out-in"},{default:t(()=>[(l(),c(b(o)))]),_:2},1024)]),_:1})]),_:1})]),_:1})])}}}),Ne=S(ke,[["__scopeId","data-v-4e37bdd0"]]);export{Ne as default};
