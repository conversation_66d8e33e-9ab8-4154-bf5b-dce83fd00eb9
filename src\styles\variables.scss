// 颜色变量
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

// 响应式断点变量
$breakpoint-xs: 480px;   // 超小屏幕（手机竖屏）
$breakpoint-sm: 768px;   // 小屏幕（手机横屏/小平板）
$breakpoint-md: 992px;   // 中等屏幕（平板）
$breakpoint-lg: 1200px;  // 大屏幕（桌面）
$breakpoint-xl: 1920px;  // 超大屏幕（大桌面）

// 尺寸变量
$sidebar-width: 240px;
$sidebar-collapsed-width: 64px;
$sidebar-mobile-width: 280px;  // 移动端侧边栏宽度
$header-height: 60px;
$header-mobile-height: 56px;   // 移动端头部高度

// 间距变量
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 移动端间距变量
$spacing-mobile-xs: 2px;
$spacing-mobile-sm: 4px;
$spacing-mobile-md: 8px;
$spacing-mobile-lg: 12px;
$spacing-mobile-xl: 16px;

// 圆角变量
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;

// 阴影变量
$box-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
$box-shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.15);
$box-shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.2);

// 字体变量
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;

// 移动端字体变量
$font-size-mobile-xs: 10px;
$font-size-mobile-sm: 12px;
$font-size-mobile-md: 14px;
$font-size-mobile-lg: 16px;
$font-size-mobile-xl: 18px;

// Z-index 层级
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 动画变量
$transition-fast: 0.2s;
$transition-medium: 0.3s;
$transition-slow: 0.5s;
