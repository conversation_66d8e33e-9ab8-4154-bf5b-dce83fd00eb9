<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import type { FormInstance } from 'element-plus';
import { ElMessage } from 'element-plus';
import { useGatewayStore } from '../../stores/gateway';
import type { MQTTConfig } from '../../types/gateway';
import * as gatewayApi from '../../api/gateway';

const props = defineProps<{
  deviceId?: string | null;
}>();

const gatewayStore = useGatewayStore();
const loading = ref(true);
const saving = ref(false);
const config = ref<MQTTConfig | null>(null);
const configForm = reactive<MQTTConfig>({
  brokerUrl: '',
  port: 1883,
  clientId: '',
  topicPrefix: '',
  publishTopic: '',
  subscribeTopic: '',
  topics: [],
  qos: 0,
  reconnectPeriod: 5000,
  keepAlive: 60,
  username: '',
  password: ''
});
const newTopic = ref('');
const testing = ref(false);
const testResultVisible = ref(false);
const testSuccess = ref<boolean | null>(null);
const testMessage = ref('');

// 表单校验规则
const formRules = {
  brokerUrl: [
    { required: true, message: '请输入MQTT服务器地址', trigger: 'blur' },
    { pattern: /^mqtt:\/\/\S+/, message: '地址格式应为 mqtt://xxx', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口号必须在1-65535之间', trigger: 'blur' }
  ],
  clientId: [
    { required: true, message: '请输入客户端ID', trigger: 'blur' }
  ],
  topics: [
    { required: true, message: '请至少添加一个主题', trigger: 'change' }
  ]
};

// 加载MQTT配置
const loadConfig = async () => {
  if (!props.deviceId) return;
  
  try {
    loading.value = true;
    const loadedConfig = await gatewayApi.getDeviceMQTTConfig(props.deviceId);
    
    if (loadedConfig) {
      config.value = loadedConfig;
      Object.assign(configForm, loadedConfig);
    }
  } catch (error) {
    console.error('加载MQTT配置失败:', error);
    ElMessage.error(`加载配置失败: ${(error as Error).message}`);
  } finally {
    loading.value = false;
  }
};

// 保存MQTT配置
const saveConfig = async () => {
  if (!props.deviceId) return;
  
  try {
    await gatewayApi.updateDeviceMQTTConfig(props.deviceId, { ...configForm });
    ElMessage.success('MQTT配置保存成功');
    config.value = { ...configForm };
  } catch (error) {
    console.error('保存MQTT配置失败:', error);
    ElMessage.error('保存MQTT配置失败');
  }
};

// 添加主题
const addTopic = () => {
  if (!newTopic.value) {
    ElMessage.warning('请输入主题');
    return;
  }
  
  if (configForm.topics.includes(newTopic.value)) {
    ElMessage.warning('该主题已存在');
    return;
  }
  
  configForm.topics.push(newTopic.value);
  newTopic.value = '';
};

// 删除主题
const removeTopic = (topic: string) => {
  const index = configForm.topics.indexOf(topic);
  if (index !== -1) {
    configForm.topics.splice(index, 1);
  }
};

// 生成随机客户端ID
const generateClientId = (): string => {
  const prefix = props.deviceId ? `inverter-${props.deviceId}` : 'inverter';
  const randomId = Math.random().toString(36).substring(2, 10);
  return `${prefix}-${randomId}-${Date.now()}`;
};

// 创建配置
const handleCreateConfig = () => {
  config.value = { ...configForm };
  ElMessage.success('配置已创建');
};

// 重置配置
const handleReset = () => {
  Object.assign(configForm, {
    brokerUrl: '',
    port: 1883,
    clientId: '',
    topicPrefix: '',
    publishTopic: '',
    subscribeTopic: '',
    topics: [],
    qos: 0,
    reconnectPeriod: 5000,
    keepAlive: 60,
    username: '',
    password: ''
  });
  ElMessage.success('配置已重置');
};

// 组件挂载时加载配置
onMounted(() => {
  if (props.deviceId) {
    loadConfig();
  } else {
    loading.value = false;
  }
});

// 测试连接
const handleTest = async () => {
  if (!props.deviceId) return;
  
  try {
    await gatewayApi.testMQTTConnection({ ...configForm });
    ElMessage.success('连接测试成功');
    testSuccess.value = true;
    testMessage.value = '连接成功';
  } catch (error) {
    console.error('测试连接失败:', error);
    ElMessage.error('连接测试失败');
    testSuccess.value = false;
    testMessage.value = (error as Error).message;
  } finally {
    testing.value = false;
  }
};
</script>

<template>
  <div class="mqtt-config-panel">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>
    
    <template v-else>
      <div v-if="!config" class="no-config">
        <el-empty description="没有MQTT配置信息">
          <template #extra>
            <el-button type="primary" @click="handleCreateConfig">创建配置</el-button>
          </template>
        </el-empty>
      </div>
      
      <div v-else class="has-config">
        <el-form
          ref="formRef"
          :model="configForm"
          :rules="formRules"
          label-position="top"
        >
          <el-form-item label="MQTT代理地址" prop="brokerUrl">
            <el-input v-model="configForm.brokerUrl" placeholder="例如: mqtt://example.com 或 mqtt://************" />
          </el-form-item>
          
          <el-form-item label="端口" prop="port">
            <el-input-number v-model="configForm.port" :min="1" :max="65535" />
          </el-form-item>
          
          <el-form-item label="客户端ID" prop="clientId">
            <el-input v-model="configForm.clientId" placeholder="客户端唯一标识符" />
            <template #extra>
              <el-button 
                link 
                type="primary" 
                @click="configForm.clientId = generateClientId()"
              >
                生成随机ID
              </el-button>
            </template>
          </el-form-item>
          
          <div class="form-row">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="configForm.username" placeholder="用户名（可选）" clearable />
            </el-form-item>
            
            <el-form-item label="密码" prop="password">
              <el-input 
                v-model="configForm.password" 
                placeholder="密码（可选）" 
                type="password" 
                show-password
                clearable
              />
            </el-form-item>
          </div>
          
          <div class="form-row">
            <el-form-item label="主题前缀" prop="topicPrefix">
              <el-input v-model="configForm.topicPrefix" placeholder="例如: devices/inverters" />
            </el-form-item>
            
            <el-form-item label="QoS" prop="qos">
              <el-select v-model="configForm.qos" placeholder="服务质量">
                <el-option :value="0" label="0 - 最多一次" />
                <el-option :value="1" label="1 - 至少一次" />
                <el-option :value="2" label="2 - 恰好一次" />
              </el-select>
            </el-form-item>
          </div>
          
          <div class="form-row">
            <el-form-item label="发布主题" prop="publishTopic">
              <el-input v-model="configForm.publishTopic" placeholder="例如: devices/inverters/{deviceId}/data" />
            </el-form-item>
            
            <el-form-item label="订阅主题" prop="subscribeTopic">
              <el-input v-model="configForm.subscribeTopic" placeholder="例如: devices/inverters/{deviceId}/cmd" />
            </el-form-item>
          </div>
          
          <div class="form-row">
            <el-form-item label="保活时间（秒）" prop="keepAlive">
              <el-input-number 
                v-model="configForm.keepAlive" 
                :min="5" 
                :max="300"
                :step="5"
                controls-position="right"
              />
            </el-form-item>
            
            <el-form-item label="重连间隔（毫秒）" prop="reconnectPeriod">
              <el-input-number 
                v-model="configForm.reconnectPeriod" 
                :min="1000" 
                :max="30000"
                :step="1000"
                controls-position="right"
              />
            </el-form-item>
          </div>
          
          <div class="form-actions">
            <el-button @click="handleReset">重置</el-button>
            <el-button type="primary" @click="saveConfig" :loading="saving">保存配置</el-button>
            <el-button 
              type="success"
              @click="handleTest"
              :loading="testing"
            >
              测试连接
            </el-button>
          </div>
        </el-form>
      </div>
    </template>
    
    <el-dialog
      v-model="testResultVisible"
      title="连接测试结果"
      width="400px"
      center
      :close-on-click-modal="false"
    >
      <div class="test-result">
        <template v-if="testSuccess !== null">
          <el-result
            :icon="testSuccess ? 'success' : 'error'"
            :title="testSuccess ? '连接成功' : '连接失败'"
            :sub-title="testMessage"
          >
            <template #extra>
              <el-button @click="testResultVisible = false">关闭</el-button>
            </template>
          </el-result>
        </template>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.mqtt-config-panel {
  padding: 10px;
  
  .loading-container {
    padding: 20px;
  }
  
  .no-config {
    padding: 30px 0;
  }
  
  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }
  
  .test-result {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
  }
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
}
</style> 