<template>
  <div class="responsive-chart" :class="{ 'responsive-chart--mobile': isMobile }">
    <!-- 移动端图表头部 -->
    <div v-if="isMobile" class="responsive-chart__mobile-header">
      <h3 class="responsive-chart__title">{{ title }}</h3>
      <div class="responsive-chart__mobile-controls">
        <el-button 
          size="small" 
          @click="toggleFullscreen"
          :icon="FullScreen"
          circle
        />
        <el-button 
          size="small" 
          @click="downloadChart"
          :icon="Download"
          circle
        />
      </div>
    </div>
    
    <!-- 图表容器 -->
    <div 
      ref="chartContainer" 
      class="responsive-chart__container"
      :style="containerStyle"
    >
      <div 
        ref="chartElement" 
        class="responsive-chart__element"
        :style="chartStyle"
      ></div>
      
      <!-- 移动端加载状态 -->
      <div v-if="loading && isMobile" class="responsive-chart__loading">
        <el-loading-spinner />
        <span>图表加载中...</span>
      </div>
    </div>
    
    <!-- 移动端图例 -->
    <div v-if="isMobile && legend.length > 0" class="responsive-chart__mobile-legend">
      <div 
        v-for="item in legend" 
        :key="item.name"
        class="responsive-chart__legend-item"
        @click="toggleLegendItem(item)"
      >
        <span 
          class="responsive-chart__legend-color"
          :style="{ backgroundColor: item.color }"
        ></span>
        <span class="responsive-chart__legend-name">{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { FullScreen, Download } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

interface Props {
  title?: string
  options: any
  height?: string | number
  loading?: boolean
  legend?: Array<{ name: string; color: string; visible: boolean }>
}

interface Emits {
  (e: 'fullscreen'): void
  (e: 'download'): void
  (e: 'legendToggle', item: any): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  height: 400,
  loading: false,
  legend: () => []
})

const emit = defineEmits<Emits>()

// 响应式数据
const chartContainer = ref<HTMLElement>()
const chartElement = ref<HTMLElement>()
const chartInstance = ref<echarts.ECharts>()
const isMobile = ref(false)
const containerWidth = ref(0)

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
}

// 监听窗口大小变化
const handleResize = () => {
  checkMobile()
  updateContainerWidth()
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

// 更新容器宽度
const updateContainerWidth = () => {
  if (chartContainer.value) {
    containerWidth.value = chartContainer.value.offsetWidth
  }
}

// 计算样式
const containerStyle = computed(() => ({
  height: typeof props.height === 'number' ? `${props.height}px` : props.height
}))

const chartStyle = computed(() => {
  if (isMobile.value) {
    return {
      width: '100%',
      height: '100%',
      minHeight: '300px'
    }
  }
  return {
    width: '100%',
    height: '100%'
  }
})

// 初始化图表
const initChart = async () => {
  await nextTick()
  if (!chartElement.value) return
  
  chartInstance.value = echarts.init(chartElement.value)
  updateChart()
}

// 更新图表
const updateChart = () => {
  if (!chartInstance.value) return
  
  const mobileOptions = isMobile.value ? getMobileOptions() : {}
  const finalOptions = {
    ...props.options,
    ...mobileOptions
  }
  
  chartInstance.value.setOption(finalOptions, true)
}

// 获取移动端优化选项
const getMobileOptions = () => {
  return {
    grid: {
      left: '5%',
      right: '5%',
      top: '15%',
      bottom: '15%',
      containLabel: true
    },
    legend: {
      show: false // 移动端隐藏内置图例，使用自定义图例
    },
    tooltip: {
      trigger: 'axis',
      confine: true,
      textStyle: {
        fontSize: 12
      },
      position: function (point: number[], params: any, dom: HTMLElement, rect: any, size: any) {
        // 移动端tooltip位置优化
        const x = point[0]
        const y = point[1]
        const viewWidth = size.viewSize[0]
        const viewHeight = size.viewSize[1]
        const boxWidth = size.contentSize[0]
        const boxHeight = size.contentSize[1]
        
        let posX = x + 10
        let posY = y - boxHeight / 2
        
        // 防止超出右边界
        if (posX + boxWidth > viewWidth) {
          posX = x - boxWidth - 10
        }
        
        // 防止超出上下边界
        if (posY < 0) {
          posY = 10
        } else if (posY + boxHeight > viewHeight) {
          posY = viewHeight - boxHeight - 10
        }
        
        return [posX, posY]
      }
    },
    xAxis: {
      axisLabel: {
        fontSize: 10,
        rotate: isMobile.value ? 45 : 0
      }
    },
    yAxis: {
      axisLabel: {
        fontSize: 10
      }
    }
  }
}

// 切换全屏
const toggleFullscreen = () => {
  emit('fullscreen')
}

// 下载图表
const downloadChart = () => {
  if (chartInstance.value) {
    const url = chartInstance.value.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff'
    })
    
    const link = document.createElement('a')
    link.download = `${props.title || 'chart'}.png`
    link.href = url
    link.click()
  }
  emit('download')
}

// 切换图例项
const toggleLegendItem = (item: any) => {
  emit('legendToggle', item)
}

// 监听选项变化
watch(() => props.options, updateChart, { deep: true })
watch(isMobile, updateChart)

onMounted(() => {
  checkMobile()
  updateContainerWidth()
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
})

// 暴露图表实例
defineExpose({
  chartInstance,
  resize: () => chartInstance.value?.resize()
})
</script>

<style lang="scss" scoped>
@use '@/styles/mixins.scss' as *;
@use '@/styles/variables.scss' as *;

.responsive-chart {
  @include card-style;
  
  &--mobile {
    margin: $spacing-mobile-sm;
    border-radius: $border-radius-sm;
  }
  
  &__mobile-header {
    @include flex-between;
    padding: $spacing-mobile-md;
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    @include min-width(md) {
      display: none;
    }
  }
  
  &__title {
    @include responsive-font-size(16px, 14px);
    font-weight: 600;
    margin: 0;
    color: var(--el-text-color-primary);
  }
  
  &__mobile-controls {
    @include flex-center;
    gap: $spacing-mobile-sm;
  }
  
  &__container {
    position: relative;
    width: 100%;
    
    @include respond-to(mobile) {
      min-height: 300px;
    }
  }
  
  &__element {
    width: 100%;
    height: 100%;
  }
  
  &__loading {
    @include absolute-center;
    @include flex-column-center;
    gap: $spacing-mobile-sm;
    color: var(--el-text-color-secondary);
    font-size: $font-size-mobile-sm;
  }
  
  &__mobile-legend {
    display: none;
    
    @include respond-to(mobile) {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-mobile-sm;
      padding: $spacing-mobile-md;
      border-top: 1px solid var(--el-border-color-lighter);
    }
  }
  
  &__legend-item {
    @include flex-center;
    gap: $spacing-mobile-xs;
    padding: $spacing-mobile-xs $spacing-mobile-sm;
    border-radius: $border-radius-sm;
    background: var(--el-bg-color-page);
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background: var(--el-color-primary-light-9);
    }
  }
  
  &__legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }
  
  &__legend-name {
    font-size: $font-size-mobile-xs;
    color: var(--el-text-color-regular);
  }
}
</style>
