// 界面初始化
var isUI = JSON.parse(localStorage.getItem("isUI"))

// 加载配置
var myConfig = JSON.parse(localStorage.getItem("myConfig"))
var isIndex = myConfig["isIndex"]



// 数据初始化
// var isData = JSON.parse(localStorage.getItem("isData"))

// console.log(isUI)


createUI()


function createUI() {
    for (key in isUI) {

        a = document.createElement("a")
        span = document.createElement("span")
        span.innerText = key
        // a.href = isUI[key]

        a.appendChild(span)
        document.getElementById("mySidenav").appendChild(a)
        
    }
}


$(document).ready(function() {
    $("a").bind('click', function() {
        document.getElementById("mySidenav").children[isIndex].children[0].style.background = "cornflowerblue";
        isIndex = $(this).index()
        console.log(isIndex)
        myConfig["isIndex"] = isIndex
        localStorage.setItem("myConfig", JSON.stringify(myConfig));
        document.getElementById("mySidenav").children[isIndex].children[0].style.background = "#818181";
        localStorage.setItem("myConfig", JSON.stringify(myConfig))
    })
})

document.getElementById("mySidenav").children[isIndex].children[0].style.background = "#818181";
