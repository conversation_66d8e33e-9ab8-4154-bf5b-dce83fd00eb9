
/*@preserve
***Version 2.30.0***
*/

/*@license
 *                       Copyright 2002 - 2018 Qualtrics, LLC.
 *                                All rights reserved.
 *
 * Notice: All code, text, concepts, and other information herein (collectively, the
 * "Materials") are the sole property of Qualtrics, LLC, except to the extent
 * otherwise indicated. The Materials are proprietary to Qualtrics and are protected
 * under all applicable laws, including copyright, patent (as applicable), trade
 * secret, and contract law. Disclosure or reproduction of any Materials is strictly
 * prohibited without the express prior written consent of an authorized signatory
 * of Qualtrics. For disclosure requests, <NAME_EMAIL>.
 */

try {
  !function(e){var t={};function i(n){if(t[n])return t[n].exports;var s=t[n]={i:n,l:!1,exports:{}};return e[n].call(s.exports,s,s.exports,i),s.l=!0,s.exports}i.m=e,i.c=t,i.d=function(e,t,n){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)i.d(n,s,function(t){return e[t]}.bind(null,s));return n},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=0)}([function(e,t,i){i(1),i(2),e.exports=i(3)},function(e,t){(void 0===window.QSI.ScreenCaptureRectangle||window.QTest)&&(QSI.ScreenCaptureRectangle=QSI.util.Class({initialize:function(e,t){this.isCanvasDirty=!1,this.rectangles=[],this.canvas=QSI.util.build("canvas",{"data-qsi-sc-class":"qsi_sc_annotations_canvas",width:e.width,height:e.height},[]),this.context=this.canvas.getContext("2d"),this.selectedRectangle=null,this.clickedObject=null,this.setupMouseListeners(),setInterval(this.mainDraw.bind(this),t)},getCanvas:function(){return this.canvas},getCleanedCanvas:function(){return this.selectedRectangle&&this.selectedRectangle.deselect(),this.isCanvasDirty=!0,this.mainDraw(),this.canvas},addRectangle:function(e,t,i,n,s,a){a||(a=1);var r=new this.Rectangle(this,e,t,i,n,s,a);this.rectangles.push(r),r.select(),this.isCanvasDirty=!0},save:function(){this.savedRectangles=[],this.cloneRectangles(this.rectangles,this.savedRectangles)},cancel:function(){this.savedRectangles?this.cloneRectangles(this.savedRectangles,this.rectangles):this.rectangles=[],this.isCanvasDirty=!0},cloneRectangles:function(e,t){t.splice(0,t.length);for(var i=0;i<e.length;i++){var n=e[i];t.push(new this.Rectangle(this,n.x,n.y,n.w,n.h,n.color,n.opacity))}},mainDraw:function(){try{if(!this.isCanvasDirty)return;this.clearContext(this.context);for(var e=0;e<this.rectangles.length;e++)this.rectangles[e].draw(this.context);this.isCanvasDirty=!1}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}},setupMouseListeners:function(){this.mouseState={isMouseDown:!1,mouseDownPosition:{x:0,y:0}},QSI.util.observe(this.canvas,"mousedown",this.mouseDownHandler.bind(this)),QSI.util.observe(this.canvas,"mousemove",this.mouseMoveHandler.bind(this)),QSI.util.observe(this.canvas,"mouseup",this.upHandler.bind(this)),QSI.util.observe(this.canvas,"mouseout",this.upHandler.bind(this)),QSI.util.observe(this.canvas,"touchstart",this.touchStartHandler.bind(this)),QSI.util.observe(this.canvas,"touchmove",this.touchMoveHandler.bind(this)),QSI.util.observe(this.canvas,"touchend",this.upHandler.bind(this))},mouseDownHandler:function(e){try{var t=this.getMouseLocationInCanvas(e);if(!t)return;this.downHandler(e,t.x,t.y),e.preventDefault()}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}},touchStartHandler:function(e){try{var t=this.getTouchLocationInCanvas(e);if(!t)return;this.downHandler(e,t.x,t.y)}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}},downHandler:function(e,t,i){e.preventDefault(),this.mouseState.isMouseDown=!0,this.mouseState.mouseDownPosition.x=t,this.mouseState.mouseDownPosition.y=i,this.clickedObject=this.getObjectAtCoordinates(t,i),this.clickedObject&&this.clickedObject.select(),this.isCanvasDirty=!0,this.clickedObject===this.selectedRectangle||this.clickedObject instanceof this.ResizePoint||this.clickedObject instanceof this.RemoveButton||(this.selectedRectangle.deselect(),this.selectedRectangle=null),this.isCanvasDirty=!0},mouseMoveHandler:function(e){try{var t=this.getMouseLocationInCanvas(e);this.moveHandler(e,t.x,t.y)}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}},touchMoveHandler:function(e){try{var t=this.getTouchLocationInCanvas(e);if(t.x<0||t.x>this.canvas.width||t.y<0||t.y>this.canvas.height)return void this.upHandler(e);this.moveHandler(e,t.x,t.y)}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}},moveHandler:function(e,t,i){if(e.preventDefault(),this.mouseState.isMouseDown){var n=t-this.mouseState.mouseDownPosition.x,s=i-this.mouseState.mouseDownPosition.y;this.clickedObject&&this.clickedObject.drag(n,s),this.mouseState.mouseDownPosition.x=t,this.mouseState.mouseDownPosition.y=i,this.isCanvasDirty=!0}else{var a=this.getObjectAtCoordinates(t,i);a?a.hover(this.canvas):this.canvas.style.cursor="auto"}},upHandler:function(e){try{this.mouseState.isMouseDown=!1,e.preventDefault()}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}},getTouchLocationInCanvas:function(e){if(!e||!e.targetTouches||!e.targetTouches[0])return null;var t=e.targetTouches[0];return this.getLocationInCanvas(t.clientX,t.clientY)},getMouseLocationInCanvas:function(e){return e?this.getLocationInCanvas(e.clientX,e.clientY):null},getLocationInCanvas:function(e,t){this.canvas.getBoundingClientRect();var i=parseInt(this.canvas.parentElement.style.top,10);return{x:e-=parseInt(this.canvas.parentElement.style.left,10),y:t-=i}},getObjectAtCoordinates:function(e,t){var i=[];if(this.selectedRectangle){i.push(this.selectedRectangle.removeButton);for(var n=this.selectedRectangle.resizePoints,s=0;s<n.length;s++)i.push(n[s])}for(var a=this.rectangles.length-1;a>=0;a--)i.push(this.rectangles[a]);for(var r=0;r<i.length;r++)if(i[r].isAtLocation(e,t))return i[r];return null},clearContext:function(e){e.clearRect(0,0,this.canvas.width,this.canvas.height)},RemoveButton:QSI.util.Class({initialize:function(e,t){this.rectangle=e,this.xOffset=18,this.yOffset=-16,this.interactionSquareLength=t},getButtonLocation:function(){return{x:this.rectangle.x+this.rectangle.w+this.xOffset,y:this.rectangle.y+this.yOffset}},isAtLocation:function(e,t){var i=this.getButtonLocation(),n=this.interactionSquareLength/2;return e>=i.x-n&&e<=i.x+n&&t>=i.y-n&&t<=i.y+n},select:function(){this.rectangle.destroy()},hover:function(e){e.style.cursor="pointer"},drag:function(){},draw:function(e){var t=this.getButtonLocation();this.drawHelper(e,t.x,t.y,4,5,"white",.5),this.drawHelper(e,t.x,t.y,2,4,"black",1)},drawHelper:function(e,t,i,n,s,a,r){e.beginPath(),e.lineWidth=n,e.strokeStyle=a,e.lineCap="round",e.globalAlpha=r,e.moveTo(t-s,i-s),e.lineTo(t+s,i+s),e.stroke(),e.moveTo(t+s,i-5),e.lineTo(t-s,i+s),e.stroke()}}),ResizePoint:QSI.util.Class({initialize:function(e,t,i,n,s){this.rectangleAnnotations=e,this.rectangle=t,this.direction=i,this.circleRadius=n,this.interactionSquareLength=s},getPointLocation:function(){var e=0,t=0;switch(this.direction){case"nw":e=this.rectangle.x,t=this.rectangle.y;break;case"ne":e=this.rectangle.x+this.rectangle.w,t=this.rectangle.y;break;case"se":e=this.rectangle.x+this.rectangle.w,t=this.rectangle.y+this.rectangle.h;break;case"sw":e=this.rectangle.x,t=this.rectangle.y+this.rectangle.h}return{x:e,y:t}},isAtLocation:function(e,t){var i=this.getPointLocation(),n=this.interactionSquareLength/2;return e>=i.x-n&&e<=i.x+n&&t>=i.y-n&&t<=i.y+n},draw:function(e){var t=this.getPointLocation();e.beginPath(),e.arc(t.x,t.y,this.circleRadius,0,2*Math.PI,!1),e.fillStyle="red",e.fill(),e.lineWidth=1,e.strokeStyle="red",e.stroke()},select:function(){},drag:function(e,t){switch(this.direction){case"nw":this.transform(e,t,-e,-t);break;case"ne":this.transform(0,t,e,-t);break;case"se":this.transform(0,0,e,t);break;case"sw":this.transform(e,0,-e,t)}},transform:function(e,t,i,n){this.rectangle.x+e<0?this.rectangle.x=0:this.rectangle.w+i<0?this.rectangle.w=0:this.rectangle.x+e+this.rectangle.w+i>this.rectangleAnnotations.canvas.width?this.rectangle.w=this.rectangleAnnotations.canvas.width-this.rectangle.x:(this.rectangle.x+=e,this.rectangle.w+=i),this.rectangle.y+t<0?this.rectangle.y=0:this.rectangle.h+n<0?this.rectangle.h=0:this.rectangle.y+t+this.rectangle.h+n>this.rectangleAnnotations.canvas.height?this.rectangle.h=this.rectangleAnnotations.canvas.height-this.rectangle.y:(this.rectangle.y+=t,this.rectangle.h+=n)},hover:function(e){switch(this.direction){case"nw":case"se":e.style.cursor="nwse-resize";break;case"ne":case"sw":e.style.cursor="nesw-resize"}}}),Rectangle:QSI.util.Class({initialize:function(e,t,i,n,s,a,r){this.rectangleAnnotations=e,this.x=t,this.y=i,this.w=n,this.h=s,this.color=a,this.opacity=r||1,this.resizePoints=[],this.removeButton=null,this.isSelected=!1},isAtLocation:function(e,t){return e>=this.x&&e<=this.x+this.w&&t>=this.y&&t<=this.y+this.h},select:function(){if(0===this.resizePoints.length)for(var e=["nw","ne","se","sw"],t=0;t<e.length;t++)this.resizePoints.push(new this.rectangleAnnotations.ResizePoint(this.rectangleAnnotations,this,e[t],3,24));this.removeButton||(this.removeButton=new this.rectangleAnnotations.RemoveButton(this,26)),this.rectangleAnnotations.selectedRectangle&&this.rectangleAnnotations.selectedRectangle.deselect(),this.rectangleAnnotations.selectedRectangle=this,this.isSelected=!0},deselect:function(){this.isSelected=!1},destroy:function(){this.rectangleAnnotations.selectedRectangle=null;var e=this.rectangleAnnotations.rectangles;e.splice(e.indexOf(this),1)},draw:function(e){if(e.fillStyle=this.color,e.globalAlpha=this.opacity,e.fillRect(this.x,this.y,this.w,this.h),e.globalAlpha=1,this.isSelected){e.strokeStyle="red",e.lineWidth=1,e.strokeRect(this.x,this.y,this.w,this.h);for(var t=0;t<this.resizePoints.length;t++)this.resizePoints[t].draw(e);this.removeButton.draw(e)}},drag:function(e,t){this.x+=e,this.y+=t,this.x<0&&(this.x=0),this.y<0&&(this.y=0),this.x+this.w>this.rectangleAnnotations.canvas.width&&(this.x=this.rectangleAnnotations.canvas.width-this.w),this.y+this.h>this.rectangleAnnotations.canvas.height&&(this.y=this.rectangleAnnotations.canvas.height-this.h)},hover:function(e){e.style.cursor="move"}})}))},function(e,t){(void 0===window.QSI.ScreenCaptureHandler||window.QTest)&&(QSI.ScreenCaptureHandler=QSI.util.Class({initialize:function(e,t,i,n,s){this.defaultScreenCaptureTimeout=1e4,this.intercept=e,this.embeddedTarget=t,this.surveySessionId=i,this.scZIndex=QSI.global.currentZIndex++,this.translations=n||{},this.surveyOrigin=s,this.createCapturingScreen(this.translations)},createCapturingScreen:function(e){var t=this,i=QSI.util.build("div",{"data-qsi-sc-class":"qsi_sc_backdrop",style:{width:"100%",height:"100%",backgroundColor:"black",opacity:"0.7",filter:"alpha(opacity=70)",position:"fixed",top:"0px",left:"0px",zIndex:this.scZIndex-1}},[]),n=QSI.util.build("img",{"data-qsi-sc-class":"qsi_sc_loading_icon",src:QSI.baseURL+"../WRQualtricsShared/Graphics/siteintercept/building_preview.gif",style:{width:"100px"},alt:""},[]),s=QSI.util.build("img",{"data-qsi-sc-class":"qsi_sc_cancel_capture_button",src:QSI.baseURL+"../WRQualtricsShared/Graphics/siteintercept/remove_screen_capture.png",title:"Cancel Capturing Screen",style:{"-webkit-filter":"brightness(0) invert(1)",filter:"brightness(0) invert(1)",display:"block",width:"15px",height:"15px",marginLeft:"100px",cursor:"pointer"},alt:"Cancel"},[]);this.isScreenCaptureCancelled=!1,QSI.util.observe(s,"click",(function(){try{t.isScreenCaptureCancelled=!0,t.hideCapturingScreen()}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}}));var a=QSI.util.build("div",{"data-qsi-sc-class":"qsi_sc_building_preview_text",style:{color:"white"}},[]);a.innerText=e.ScreenCaptureBuilding||"Building Preview ...";var r=parseInt(window.innerWidth,10),o=parseInt(window.innerHeight,10);this.scCapturingScreen=QSI.util.build("div",{"data-qsi-sc-class":"qsi_sc_capturing_screen",style:{position:"fixed",top:o/2+"px",left:r/2+"px",marginTop:"-50px",marginLeft:"-50px",zIndex:this.scZIndex}},[s,n,a]),this.scScreen=QSI.util.build("div",{className:"QSI_ScreenCapture",style:{visibility:"hidden"}},[i,this.scCapturingScreen]),document.body.appendChild(this.scScreen)},showCapturingScreen:function(){this.setInterceptVisibilty("hidden"),this.scScreen.style.visibility="visible"},hideCapturingScreen:function(){this.setInterceptVisibilty("visible"),this.scScreen.style.visibility="hidden"},setInterceptVisibilty:function(e){if(this.intercept.container?this.intercept.container.style.visibility=e:this.intercept.link&&"FeedbackLink"===this.intercept.type?this.intercept.link.style.visibility=e:this.intercept.bar?this.intercept.bar.style.visibility=e:this.intercept.setVisibility&&this.intercept.setVisibility(e),this.intercept.embeddedWindows)for(var t=0;t<this.intercept.embeddedWindows.length;t++)this.intercept.embeddedWindows[t].container.style.visibility=e},appendCSSInJSStyles:function(e){var t=[].slice.call(document.querySelectorAll("style")).map((function(e){return e.innerHTML&&0!==e.innerHTML.trim().length?"":[].slice.call(e.sheet.cssRules).map((function(e){return e.cssText})).join("\n")})).join("\n");if(t){var i=e.createElement("style");i.innerHTML=t,e.head.appendChild(i)}},captureScreen:function(e){this.showCapturingScreen();var t="<!DOCTYPE html>"+this.getCleanedHTML().documentElement.outerHTML,i=this,n="";QSI.Orchestrator&&QSI.Orchestrator.getClientVersionQueryString&&(n="?"+QSI.Orchestrator.getClientVersionQueryString());var s={html:t,viewportData:this.getViewPortDimensions(),url:window.location.href,browserType:"CHROME"};QSI.util.sendHttpRequest({type:"POST",url:QSI.global.screenCaptureServiceBaseURL+"screencapture"+n,header:{"Content-type":"application/json"},data:JSON.stringify(s),successCallback:function(t){var n="data:image/jpeg;base64,"+(t.response?JSON.parse(t.response):JSON.parse(t.responseText)).imageData;i.isScreenCaptureCancelled?i.isScreenCaptureCancelled=!1:i.succeededScreenCapture(e,n,i.translations)},errorCallback:function(e){i.isScreenCaptureCancelled?i.isScreenCaptureCancelled=!1:i.failedScreenCapture("ScreenCapture call POST request to "+QSI.global.screenCaptureServiceBaseURL+"screencapture returned with readyState="+e.readyState+" and status="+e.status,i.translations)},timeout:QSI.overrides.screenCaptureTimeout?QSI.overrides.screenCaptureTimeout:this.defaultScreenCaptureTimeout,timeoutCallback:function(){QSI.dbg.e("ScreenCapture call POST request to "+QSI.global.screenCaptureServiceBaseURL+"screencapture timed out")}})},editAnnotations:function(e){this.screenCaptureAnnotationsMap&&this.screenCaptureAnnotationsMap[e]&&this.screenCaptureAnnotationsMap[e].showAnnotationsArea()},succeededScreenCapture:function(e,t,i){this.scScreen.style.visibility="hidden",this.screenCaptureAnnotationsMap||(this.screenCaptureAnnotationsMap={}),this.screenCaptureAnnotationsMap[e]=new QSI.ScreenCaptureAnnotations(this,e,t,i||{})},saveScreenCapture:function(e,t){this.setInterceptVisibilty("visible");var i={event:"saveScreenCapture",from:"SI",to:"JFE",sessionId:this.surveySessionId,questionId:e,imageData:t};this.sendMessageToSurvey(i)},failedScreenCapture:function(e,t){QSI.dbg.e(e),this.scScreen.style.visibility="visible",this.scCapturingScreen.style.visibility="hidden";var i=QSI.util.build("p",{"data-qsi-sc-class":"qsi_sc_failure_message",style:{textAlign:"left",width:"150px",fontSize:"13px",lineHeight:"18px",marginTop:"5px",marginBottom:"10px"}},[]);i.innerText=t.ScreenCaptureBuildingFailed||"Sorry, we couldn't create a screenshot. Please try again later.";var n=QSI.util.build("img",{"data-qsi-sc-class":"qsi_sc_failure_icon",src:QSI.baseURL+"../WRQualtricsShared/Graphics/siteintercept/error_icon.png",style:{display:"inline-block",verticalAlign:"top",height:"30px",width:"30px",marginTop:"7px"},alt:"Error"},[]),s=QSI.util.build("p",{"data-qsi-sc-class":"qsi_sc_failure_confirm",style:{display:"table-cell",verticalAlign:"middle",backgroundColor:"#1cb06f",color:"white",width:"100px",height:"30px",borderRadius:"5px",fontSize:"13px",cursor:"pointer"}},[]);s.innerText=t.ScreenCaptureOK||"OK";var a=QSI.util.build("div",{"data-qsi-sc-class":"qsi_sc_failure_message_container",style:{display:"inline-block",marginLeft:"15px"}},[i,s]),r=QSI.util.build("div",{"data-qsi-sc-class":"qsi_sc_failure_popover",style:{position:"fixed",top:"50%",left:"50%",marginTop:"-55px",marginLeft:"-110px",width:"220px",height:"110px",textAlign:"center",fontFamily:"Arial",borderRadius:"3px",background:"rgba(255, 255, 255, 0.95)",zIndex:this.scZIndex}},[n,a]),o=this;QSI.util.observe(s,"click",(function(){try{QSI.util.remove(r),o.scCapturingScreen.style.visibility="inherit",o.hideCapturingScreen()}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}})),this.scScreen.appendChild(r)},removeScreenCapture:function(e){this.screenCaptureAnnotationsMap&&this.screenCaptureAnnotationsMap[e]&&(this.screenCaptureAnnotationsMap[e].destroyAnnotationsArea(),delete this.screenCaptureAnnotationsMap[e])},removeAllScreenCaptures:function(){if(this.screenCaptureAnnotationsMap)for(var e in this.screenCaptureAnnotationsMap)Object.prototype.hasOwnProperty.call(this.screenCaptureAnnotationsMap,e)&&this.removeScreenCapture(e)},getCleanedHTML:function(){var e=document.cloneNode(!0);this.appendCSSInJSStyles(e);for(var t=document.body.getElementsByTagName("*"),i=e.body.getElementsByTagName("*"),n=[],s=0;s<i.length;s++)if(window.getComputedStyle){var a=QSI.util.getComputedStyle(t[s]);i[s].style.boxSizing=a.boxSizing;var r=window.getComputedStyle(t[s],":before").display,o=window.getComputedStyle(t[s],":after").display;if("table"!==r&&"block"!==r&&"table"!==o&&"block"!==o||(i[s].style.height=a.height),i[s].style.display=a.display,0===parseInt(a.height,10)&&(i[s].style.height="0px"),this.isElementAdvertisement(i[s])){var c=QSI.util.build("span",{style:{color:"black",fontSize:"15px",lineHeight:"15px"}},[]);c.innerText="ADVERTISEMENT";var l=QSI.util.build("div",{style:{width:a.width,height:a.height,backgroundColor:"white",border:"1px solid black",padding:"3px",textAlign:"center"}},[c]);n.push({originalElement:i[s],replacementElement:l})}}for(var h=0;h<n.length;h++)n[h].originalElement.parentNode.replaceChild(n[h].replacementElement,n[h].originalElement);return document.body.style.backgroundColor||e.body.setAttribute("style",e.body.getAttribute("style")+"; background-color:white;"),QSI.util.removeAllByQuery(e,"noscript"),QSI.util.removeAllByQuery(e,'[data-qsi-sc-class="qsi_sc_capturing_screen"]'),QSI.util.removeAllByQuery(e,'[data-qsi-sc-class="qsi_sc_backdrop"]'),QSI.util.removeAllByQuery(e.head,"iframe"),e},isElementAdvertisement:function(e){return"div"===e.tagName.toLowerCase()&&-1!==e.id.indexOf("google_ads_iframe")},getViewPortDimensions:function(){var e=QSI.util.getScrollOffsets();return{width:window.innerWidth,height:window.innerHeight,top:e.top,left:e.left}},sendMessageToSurvey:function(e){var t=JSON.stringify(e);this.embeddedTarget.postMessage(t,this.surveyOrigin)}})),QSI.screenCaptureListenerRegistered||(QSI.screenCaptureListenerRegistered=!0,window.addEventListener("message",(function(e){try{if(QSI.Orchestrator&&QSI.Orchestrator.isMessageEventOriginAllowed&&!QSI.Orchestrator.isMessageEventOriginAllowed(e.origin))return;var t=QSI.util.getOriginInterceptOfMessage(e.source);if(!t)return;var i=e.data;if("string"==typeof i)try{i=JSON.parse(i)}catch(e){return}if(!i||"JFE"!==i.from||"SI"!==i.to)return;if(QSI.screenCaptureHandlers||(QSI.screenCaptureHandlers={}),"canScreenCapture"===i.event){return void e.source.postMessage(JSON.stringify({event:"canScreenCapture",from:"SI",to:"JFE",canScreenCapture:!0}),e.origin)}switch(QSI.screenCaptureHandlers[i.sessionId]||(QSI.screenCaptureHandlers[i.sessionId]=new QSI.ScreenCaptureHandler(t,e.source,i.sessionId,i.translations)),i.event){case"startScreenCapture":QSI.screenCaptureHandlers[i.sessionId].captureScreen(i.questionId);break;case"editScreenCapture":QSI.screenCaptureHandlers[i.sessionId].editAnnotations(i.questionId);break;case"removeScreenCapture":QSI.screenCaptureHandlers[i.sessionId].removeScreenCapture(i.questionId);break;case"sessionFinished":QSI.screenCaptureHandlers[i.sessionId].removeAllScreenCaptures();break;default:return}}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}})))},function(e,t){(void 0===window.QSI.ScreenCaptureAnnotations||window.QTest)&&(QSI.ScreenCaptureAnnotations=QSI.util.Class({initialize:function(e,t,i,n){this.annotationsZIndex=QSI.global.currentZIndex++,this.isScreenCaptureSaved=!1,this.createAnnotationsArea(e,t,i,n)},createAnnotationsArea:function(e,t,i,n){var s=parseInt(window.innerWidth,10),a=parseInt(window.innerHeight,10),r=QSI.Browser.isMobile?.7:.95,o=a*(1-r)/2,c=a*r-36-8-16,l=c/a*s,h=this.createCanvas(i,l,c),u=this.createActionsBar(e,t,h,36,8,n),d=this.screenCaptureRectangle.getCanvas();QSI.util.setStyle(d,{position:"absolute",top:"8px",left:"8px"});var p=QSI.util.build("div",{"data-qsi-sc-class":"qsi_sc_captured_image_area",style:{position:"fixed",top:o+"px",left:s/2-l/2+"px",width:l+"px",zIndex:this.annotationsZIndex,backgroundColor:"white",boxSizing:"content-box",padding:"8px",borderRadius:"6px",mozBorderRadius:"6px",boxShadow:"0px 0px 16px 0px rgba(0,0,0,0.5)"}},[h,d,u]);this.annotationsScreen=QSI.util.build("div",{className:"QSI_ScreenCaptureAnnotations"},[this.createBackDrop(),p]),document.body.appendChild(this.annotationsScreen)},createActionsBar:function(e,t,i,n,s,a){var r=this.createSaveActions(e,t,i,n,a),o=this.createAnnotationActions(i,n);return QSI.util.build("div",{"data-qsi-sc-class":"qsi_sc_actions_bar",style:{width:"100%",height:n+"px",marginTop:s+"px"}},[o,r])},createAnnotationActions:function(e,t){this.screenCaptureRectangle=new QSI.ScreenCaptureRectangle(e,5);var i=QSI.baseURL+"../WRQualtricsShared/Graphics/siteintercept/sc_annotations_sprites.png",n=QSI.util.build("div",{"data-qsi-sc-class":"qsi_sc_highlight_button",title:"Highlight",style:{display:"inline-block",height:"23px",width:"23px",cursor:"pointer",background:"url("+i+") 139px 0px"}},[]),s=QSI.util.build("div",{"data-qsi-sc-class":"qsi_sc_blackout_button",title:"Blackout",style:{display:"inline-block",height:"23px",width:"23px",cursor:"pointer",marginLeft:"8px",background:"url("+i+") 110px 0px"}},[]),a=parseInt(e.width,10)/2,r=parseInt(e.height,10)/2,o=this;QSI.util.observe(n,"click",(function(){try{o.screenCaptureRectangle.addRectangle(a-50,r-25,100,50,"yellow",.3)}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}})),QSI.util.observe(s,"click",(function(){try{o.screenCaptureRectangle.addRectangle(a-50,r-25,100,50,"black",1)}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}}));var c=(t-23)/2;return QSI.util.build("div",{"data-qsi-sc-class":"qsi_sc_annotation_actions",style:{float:"left",boxSizing:"border-box",height:t+"px",padding:c+"px",backgroundColor:"white"}},[n,s])},createSaveActions:function(e,t,i,n,s){s=s||{};var a=QSI.util.build("div",{"data-qsi-sc-class":"qsi_sc_annotations_cancel_button",style:{display:"inline-block",fontSize:"15px",color:"black",cursor:"pointer",lineHeight:"30px"}},[]),r=this,o=function(){try{r.cancelButtonCallback(e,t)}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}};QSI.util.observe(a,"click",o),a.innerText=s.ScreenCaptureCancel||"Cancel";var c=QSI.util.build("div",{"data-qsi-sc-class":"qsi_sc_annotations_save_button",style:{display:"inline-block",fontSize:"15px",color:"white",backgroundColor:"#14a857",cursor:"pointer",lineHeight:"30px",borderRadius:"4px",padding:"0px 8px 0px 8px",marginLeft:"16px"}},[]),l=function(){try{r.isScreenCaptureSaved=!0,r.hideAnnotationsArea(),r.screenCaptureRectangle.save(),e.saveScreenCapture(t,r.getAnnotatedImageData(i))}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}};QSI.util.observe(c,"click",l),c.innerText=s.ScreenCaptureSave||"Save";var h=QSI.util.build("div",{"data-qsi-sc-class":"qsi_sc_save_actions",style:{float:"right",lineHeight:n+"px"}},[a,c]);return this.escapeCallBack=function(e){try{27===e.keyCode&&o()}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}},QSI.util.observe(window,"keydown",this.escapeCallBack),this.returnKeyCallBack=function(e){try{13===e.keyCode&&l()}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}},QSI.util.observe(window,"keydown",this.returnKeyCallBack),h},getAnnotatedImageData:function(e){var t=QSI.util.build("canvas",{width:e.width,height:e.height},[]),i=t.getContext("2d");return i.drawImage(e,0,0),i.drawImage(this.screenCaptureRectangle.getCleanedCanvas(),0,0),t.toDataURL("image/jpeg")},cancelButtonCallback:function(e,t){this.screenCaptureRectangle.cancel(),this.isScreenCaptureSaved?this.hideAnnotationsArea():e.removeScreenCapture(t),e.hideCapturingScreen()},destroyAnnotationsArea:function(){QSI.util.stopObserving(window,"keydown",this.escapeCallBack),QSI.util.stopObserving(window,"keydown",this.returnKeyCallBack),QSI.util.remove(this.annotationsScreen)},hideAnnotationsArea:function(){QSI.util.stopObserving(window,"keydown",this.escapeCallBack),QSI.util.stopObserving(window,"keydown",this.returnKeyCallBack),this.annotationsScreen.style.visibility="hidden"},showAnnotationsArea:function(){QSI.util.observe(window,"keydown",this.escapeCallBack),QSI.util.observe(window,"keydown",this.returnKeyCallBack),this.annotationsScreen.style.visibility="visible"},createCanvas:function(e,t,i){var n=QSI.util.build("canvas",{"data-qsi-sc-class":"qsi_sc_capturedimage_canvas",width:t+"px",height:i+"px",style:{boxSizing:"border-box",borderRadius:"4px",mozBorderRadius:"4px",boxShadow:"0px 0px 1px 2px rgba(0,0,0,0.1)"}},[]),s=n.getContext("2d"),a=new Image;return a.onload=function(){s.drawImage(a,0,0,t,i)},a.src=e,n},createBackDrop:function(){return QSI.util.build("div",{"data-qsi-sc-class":"qsi_sc_annotations_backdrop",style:{width:"100%",height:"100%",backgroundColor:"black",opacity:"0.5",filter:"alpha(opacity=70)",position:"fixed",top:"0px",left:"0px",zIndex:this.annotationsZIndex-1}},[])}}))}]);
} catch(e) {
  if (typeof QSI !== 'undefined' && QSI.dbg && QSI.dbg.e) {
    QSI.dbg.e(e);
  }
}