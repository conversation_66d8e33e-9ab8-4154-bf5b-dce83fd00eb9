/*!************************************************************************
*
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2013 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
if(typeof s7viewers=="undefined"){s7viewers={}}else{if(typeof s7viewers!="object"){throw new Error("Cannot initialize a root 's7viewers' package. s7viewers is not an object")}}if(!s7viewers.VideoViewer){(function(){var a;s7viewers.VideoViewer=function(b){this.sdkBasePath="../../s7viewersdk/2025.5/VideoViewer/";this.viewerFileName="VideoViewer.js";this.cssSrcURL="VideoViewer.css";this.utilsFilePath="js/s7sdk/utils/Utils.js";this.containerId=null;this.params={};this.handlers=[];this.onInitComplete=null;this.onInitFail=null;this.initializationComplete=false;this.initCalled=false;this.firstMediasetParsed=false;this.metadataLoaded=false;this.isDisposed=false;this.utilsScriptElm=null;this.fixinputmarker=null;this.sdkProvided=false;this.lockurldomains=true;if(typeof b=="object"){if(b.containerId){this.setContainerId(b.containerId)}if(b.params){for(var c in b.params){if(b.params.hasOwnProperty(c)&&b.params.propertyIsEnumerable(c)){this.setParam(c,b.params[c])}}}if(b.handlers){this.setHandlers(b.handlers)}if(b.localizedTexts){this.setLocalizedTexts(b.localizedTexts)}}};s7viewers.VideoViewer.cssClassName="s7videoviewer";s7viewers.VideoViewer.prototype.modifiers={};s7viewers.VideoViewer.prototype.setContainerId=function(b){if(this.isDisposed){return}this.containerId=b||null};s7viewers.VideoViewer.getCodeBase=function(){var h="";var c="";var f=null;if(document.scripts){f=document.scripts}else{f=document.getElementsByTagName("script")}for(var e=0;e<f.length;e++){var g=f[e].src;var b=/^\s*(http[s]?:\/\/[^\/]*)?(.*)(\/(js|js_orig)\/VideoViewer\.js)/.exec(g);if(b&&b.length==5){if(typeof b[1]!=="undefined"){h=b[1]}h+=b[2];c=g;break}}if((h!="")&&(h.lastIndexOf("/")!=h.length-1)){h+="/"}var d=/\/etc\/dam\/viewers\//;s7viewers.VideoViewer.codebase={contentUrl:h,isDAM:d.test(c)}};s7viewers.VideoViewer.getCodeBase();s7viewers.VideoViewer.prototype.getContentUrl=function(){return s7viewers.VideoViewer.codebase.contentUrl};s7viewers.VideoViewer.prototype.symbols={"Container.LABEL":"Video viewer"};s7viewers.VideoViewer.prototype.includeViewer=function(){a.Util.lib.include("s7sdk.common.Button");a.Util.lib.include("s7sdk.common.Container");a.Util.lib.include("s7sdk.event.Event");a.Util.lib.include("s7sdk.video.VideoControls");a.Util.lib.include("s7sdk.video.VideoPlayer");a.Util.lib.include("s7sdk.common.ControlBar");a.Util.lib.include("s7sdk.set.MediaSet");a.Util.lib.include("s7sdk.share.Share");this.s7params=new a.ParameterManager(null,null,{asset:"MediaSet.asset"},this.getContentUrl()+this.cssSrcURL,this.lockurldomains);var f="";if(this.s7params.params.config&&(typeof(this.s7params.params.config)=="string")){f=",";if(this.s7params.params.config.indexOf("/")>-1){f+=this.s7params.params.config.split("/")[1]}else{f+=this.s7params.params.config}}this.s7params.setViewer("506,2025.5.0"+f);var d={en:this.symbols,defaultLocale:"en"};this.s7params.setDefaultLocalizedTexts(d);for(var b in this.params){if(b!="localizedtexts"){this.s7params.push(b,this.params[b])}else{this.s7params.setLocalizedTexts(this.params[b])}}this.s7params.push("OOTBPresetCSSFileToClassMap",{html5_videoviewer:"s7videoviewer_nosocial",html5_videoviewersocial:""});this.trackingManager=new a.TrackingManager();this.mediaSet=null;this.container=null;this.videoplayer=null;this.controls=null;this.playPauseButton=null;this.videoScrubber=null;this.videoTime=null;this.mutableVolume=null;this.fullScreenButton=null;this.closedCaptionButton=null;this.storedPlayingState=false;this.socialShare=null;this.emailShare=null;this.embedShare=null;this.linkShare=null;this.twitterShare=null;this.facebookShare=null;this.isCaption=true;this.curCaption=null;this.storedCaptionEnabled=null;this.isNavigation=null;this.isPosterImage=null;this.fixTrackCSS=false;this.storedSocialShareDisplayProp=null;this.supportsInline=null;this.isOrientationMarkerForcedChanged=false;var c=this;function g(){c.s7params.params.aemmode=s7viewers.VideoViewer.codebase.isDAM?"1":"0";var j=c.containerId+"_container";c.s7params.push("autoplay","0");c.s7params.push("singleclick","playPause");c.s7params.push("iconeffect","1,-1,0.3,0");c.s7params.push("bearing","fit-vertical");c.s7params.push("initialbitrate","1400");var k=c.getParam("fixinputmarker");if(k){c.fixinputmarker=(k=="s7touchinput"||k=="s7mouseinput")?c.fixinputmarker=k:null}var h=c.getURLParameter("fixinputmarker");if(h){c.fixinputmarker=(h=="s7touchinput"||h=="s7mouseinput")?c.fixinputmarker=h:null}if(c.fixinputmarker){if(c.fixinputmarker==="s7mouseinput"){c.addClass(c.containerId,"s7mouseinput")}else{if(c.fixinputmarker==="s7touchinput"){c.addClass(c.containerId,"s7touchinput")}}}else{if(a.browser.supportsTouch()){c.addClass(c.containerId,"s7touchinput")}else{c.addClass(c.containerId,"s7mouseinput")}}var i=c.s7params.get("presetClasses");if(i&&i.length>0){i.forEach(function(l){c.addClass(c.containerId,l)})}c.parseMods();c.container=new a.common.Container(c.containerId,c.s7params,j);c.trackingManager.attach(c.container);if(c.container.isInLayout()){e()}else{c.container.addEventListener(a.event.ResizeEvent.ADDED_TO_LAYOUT,e,false)}}function e(){c.container.removeEventListener(a.event.ResizeEvent.ADDED_TO_LAYOUT,e,false);var r=document.getElementById(c.containerId);var C=r.style.minHeight;r.style.minHeight="1px";var E=document.createElement("div");E.style.position="relative";E.style.width="100%";E.style.height="100%";r.appendChild(E);var h=E.offsetHeight;if(E.offsetHeight<=1){r.style.height="100%";h=E.offsetHeight}r.removeChild(E);r.style.minHeight=C;var i=false;switch(c.s7params.get("responsive","auto")){case"fit":i=false;break;case"constrain":i=true;break;default:i=h==0;break}c.updateCSSMarkers();c.updateOrientationMarkers();if(c.container.isFixedSize()){c.viewerMode="fixed"}else{if(i){c.viewerMode="ratio"}else{c.viewerMode="free"}}c.mediaSet=new a.MediaSet(null,c.s7params,c.containerId+"_mediaSet");c.videoplayer=new a.video.VideoPlayer(c.container,c.s7params,c.containerId+"_videoPlayer");c.videoplayer.getObj().setAttribute("aria-live","polite");c.socialShare=new a.share.SocialShare(c.container,c.s7params,c.containerId+"_socialShare");c.emailShare=new a.share.EmailShare(c.containerId+"_socialShare",c.s7params,c.containerId+"_emailShare");c.embedShare=new a.share.EmbedShare(c.containerId+"_socialShare",c.s7params,c.containerId+"_embedShare");c.linkShare=new a.share.LinkShare(c.containerId+"_socialShare",c.s7params,c.containerId+"_linkShare");c.twitterShare=new a.share.TwitterShare(c.containerId+"_socialShare",c.s7params,c.containerId+"_twitterShare");c.facebookShare=new a.share.FacebookShare(c.containerId+"_socialShare",c.s7params,c.containerId+"_facebookShare");c.emailShare.addEventListener(a.event.SocialEvent.NOTF_SOCIAL_ACTIVATED,t,false);c.embedShare.addEventListener(a.event.SocialEvent.NOTF_SOCIAL_ACTIVATED,t,false);c.linkShare.addEventListener(a.event.SocialEvent.NOTF_SOCIAL_ACTIVATED,t,false);c.twitterShare.addEventListener(a.event.SocialEvent.NOTF_SOCIAL_ACTIVATED,t,false);c.facebookShare.addEventListener(a.event.SocialEvent.NOTF_SOCIAL_ACTIVATED,t,false);c.socialShare.addEventListener("mouseover",function(O){c.controls.allowAutoHide(false)});c.socialShare.addEventListener("mouseout",function(O){c.controls.allowAutoHide(true)});c.linkShare.setContentUrl(document.URL);c.emailShare.setOriginUrl(window.location.hostname);c.emailShare.setContentUrl(document.URL);c.supportsInline=c.videoplayer.supportsInline();c.controls=new a.common.ControlBar(c.container,c.s7params,c.containerId+"_controls");c.controls.setCSS(".s7controlbar","visibility","hidden");c.controls.attachView(c.videoplayer,false);if(a.browser.device.name!="iphone"){c.controls.attach(c.socialShare)}c.socialShare.addTrackedComponent(c.controls);c.playPauseButton=new a.common.PlayPauseButton(c.containerId+"_controls",c.s7params,c.containerId+"_playPauseButton");c.videoScrubber=new a.video.VideoScrubber(c.containerId+"_controls",c.s7params,c.containerId+"_videoScrubber");c.fixTrackCSS=(a.Util.getStyle(c.videoScrubber.component.track,"width")=="310px")||(a.Util.getStyle(c.videoScrubber.component.track,"width")=="365px");c.videoTime=new a.VideoTime(c.containerId+"_controls",c.s7params,c.containerId+"_videoTime");c.closedCaptionButton=new a.common.ClosedCaptionButton(c.containerId+"_controls",c.s7params,c.containerId+"_closedCaptionButton");c.closedCaptionButton.addEventListener("click",w);c.closedCaptionButton.setSelected(c.videoplayer.getCaptionEnabled());c.videoplayer.setCaptionEnabled(c.videoplayer.getCaptionEnabled());c.storedCaptionEnabled=c.videoplayer.getCaptionEnabled();c.audioCaptionsSelector=new a.video.AudioCaptions(c.containerId+"_controls",c.s7params,c.containerId+"_audioCaptionsSelector");c.mutableVolume=new a.video.MutableVolume(c.containerId+"_controls",c.s7params,c.containerId+"_mutableVolume");c.mutableVolume.setSelected(c.videoplayer.muted());if(!c.s7params.get("caption")){c.isCaption=false}else{c.curCaption=c.s7params.params.caption}if(c.s7params.get("navigation")){c.isNavigation=c.s7params.get("navigation")}c.fullScreenButton=new a.common.FullScreenButton(c.containerId+"_controls",c.s7params,c.containerId+"_fullScreenButton");c.mediaSet.addEventListener(a.AssetEvent.NOTF_SET_PARSED,x,false);c.container.addEventListener(a.event.ResizeEvent.COMPONENT_RESIZE,l,false);c.container.addEventListener(a.event.ResizeEvent.FULLSCREEN_RESIZE,n,false);c.container.addEventListener(a.event.ResizeEvent.REMOVED_FROM_LAYOUT,k,false);c.container.addEventListener(a.event.ResizeEvent.ADDED_TO_LAYOUT,G,false);c.container.addEventListener(a.event.ResizeEvent.SIZE_MARKER_CHANGE,z,false);c.videoplayer.addEventListener(a.event.CapabilityStateEvent.NOTF_VIDEO_CAPABILITY_STATE,u,false);c.videoplayer.addEventListener(a.event.VideoEvent.NOTF_DURATION,y,false);c.videoplayer.addEventListener(a.event.VideoEvent.NOTF_LOAD_PROGRESS,M,false);c.videoplayer.addEventListener(a.event.VideoEvent.NOTF_CURRENT_TIME,A,false);c.videoplayer.addEventListener(a.event.VideoEvent.NOTF_NAVIGATION,H,false);c.videoplayer.addEventListener(a.event.VideoEvent.NOTF_METADATA_LOAD_COMPLETE,s);c.playPauseButton.addEventListener("click",I);c.videoScrubber.addEventListener(a.SliderEvent.NOTF_SLIDER_UP,p,false);c.mutableVolume.addEventListener("click",m);c.mutableVolume.addEventListener(a.SliderEvent.NOTF_SLIDER_DOWN,N,false);c.mutableVolume.addEventListener(a.SliderEvent.NOTF_SLIDER_MOVE,j,false);c.mutableVolume.addEventListener(a.SliderEvent.NOTF_SLIDER_UP,j,false);c.fullScreenButton.addEventListener("click",o);c.trackingManager.setCallback(B);c.audioCaptionsSelector.addEventListener(a.event.AudioCaptionEvent.NOTF_AUDIO_CHANGE,function(O){c.videoplayer.selectAudioTrack(O.s7event.data)});c.audioCaptionsSelector.addEventListener(a.event.AudioCaptionEvent.NOTF_SUBTITLE_CHANGE,function(O){var P=O.s7event.data;if(P==="off"){c.videoplayer.disableTextTrack();return}c.videoplayer.selectTextTrack(O.s7event.data);c.closedCaptionButton.setSelected(false)});c.audioCaptionsSelector.addEventListener(a.event.AudioCaptionEvent.NOTF_MENU_STATE_CHANGE,function(P){var O=P.s7event.data;c.controls.allowAutoHide(!O)});if((typeof(AppMeasurementBridge)=="function")&&(c.isConfig2Exist==true)){c.appMeasurementBridge=new AppMeasurementBridge(c.trackingParams);c.appMeasurementBridge.setVideoPlayer(c.videoplayer)}if(c.viewerMode=="ratio"){r.style.height="auto"}if(c.container.getWidth()>0&&c.container.getHeight()>0){v(c.container.getWidth(),c.container.getHeight())}function B(Q,P,T,O,R){if(!c.handlers.trackEvent&&c.isConfig2Exist!=true&&a.Modifier.parse(c.s7params.get("launch","true"),[true]).values[0]){if(typeof(_satellite)!="undefined"&&_satellite._dmviewers_v001){c.handlers.trackEvent=_satellite._dmviewers_v001().trackingFn}}if(c.appMeasurementBridge){c.appMeasurementBridge.track(Q,P,T,O,R)}if(c.handlers.trackEvent){if(typeof window.s7sdk=="undefined"){window.s7sdk=a}var S=c.containerId;c.handlers.trackEvent(S,P,T,O,R)}if("s7ComponentEvent" in window){s7ComponentEvent(Q,P,T,O,R)}}function t(O){c.videoplayer.pause()}function x(P){var O=P.s7event.asset;if(O instanceof a.MediaSetDesc){var S={};if(c.viewerMode=="ratio"){var R=O.items[0];var Q=R.width/R.height;c.container.setModifier({aspect:Q})}S.navigation=c.isNavigation?c.isNavigation:",0";S.posterimage=c.isPosterImage?c.isPosterImage:"";S.caption=c.curCaption?c.curCaption:",0";c.videoplayer.setModifier(S);if(O.type==a.ItemDescType.VIDEO_SET||O.type==a.ItemDescType.VIDEO_GROUP){c.videoplayer.setItem(O)}else{c.videoplayer.setItem(O.items[0])}}else{throw new Error("Failed to get meta data for video: "+P.s7event.asset)}v(c.container.getWidth(),c.container.getHeight());if(c.emailShare){c.emailShare.setThumbnail(P.s7event.asset.name)}if(c.embedShare){c.embedShare.setEmbedCode(q())}if((c.handlers.initComplete!=null)&&(typeof c.handlers.initComplete=="function")&&!c.firstMediasetParsed){if(typeof window.s7sdk=="undefined"){window.s7sdk=a}c.handlers.initComplete()}c.firstMediasetParsed=true;if(c.controls){c.controls.setCSS(".s7controlbar","visibility","inherit")}L()}function l(O){if((typeof(O.target)=="undefined")||(O.target==document.getElementById(c.containerId+"_container"))){if(!c.container.isInLayout()){return}v(O.s7event.w,O.s7event.h);c.fullScreenButton.setSelected(c.container.isFullScreen())}}function n(O){v(O.s7event.w,O.s7event.h);c.fullScreenButton.setSelected(c.container.isFullScreen());if(!c.container.isFullScreen()){c.onFullScreenExit(O)}else{c.onFullScreenEnter(O)}}function z(O){c.updateCSSMarkers()}function G(O){if(a.browser.device.name!="desktop"){}else{if(c.storedPlayingState){c.videoplayer.play();c.storedPlayingState=false}}}function k(O){if(a.browser.device.name!="desktop"){}else{}if(c.videoplayer.getCapabilityState().hasCapability(a.VideoCapabilityState.PAUSE)){c.storedPlayingState=true;a.Logger.log(a.Logger.INFO,"Pause video");c.videoplayer.pause()}}function u(P){var O=P.s7event.state;if(O.hasCapability(a.VideoCapabilityState.PAUSE)){c.playPauseButton.setSelected(false)}else{if(O.hasCapability(a.VideoCapabilityState.PLAY)||O.hasCapability(a.VideoCapabilityState.REPLAY)){c.playPauseButton.setSelected(true)}}c.playPauseButton.enableReplay(O.hasCapability(a.VideoCapabilityState.REPLAY))}function y(O){c.videoTime.setDuration(O.s7event.data);c.videoScrubber.setDuration(O.s7event.data)}function M(O){c.videoScrubber.setLoadedPosition(O.s7event.data)}function A(O){c.videoTime.setPlayedTime(O.s7event.data);c.videoScrubber.setPlayedTime(O.s7event.data)}function H(O){c.videoScrubber.setNavigation(O.s7event.data)}function s(){var P=c.videoplayer.getAudioTracks();var O=c.videoplayer.getSubtitlesAndCaptions();c.metadataLoaded=true;c.audioCaptionsSelector.setTracks(P,O);L()}function I(O){var P=c.videoplayer.getObj();if(!c.playPauseButton.isSelected()){var Q=c.videoplayer.getDuration()-c.videoplayer.getCurrentTime();if(Q<=1){c.videoplayer.seek(0)}c.videoplayer.play();console.log("video is play");P.setAttribute("aria-label","video is playing")}else{c.videoplayer.pause();console.log("video is pause");P.setAttribute("aria-label","video is paused")}}function p(O){c.videoplayer.seek(O.s7event.position*c.videoplayer.getDuration())}function m(O){var P=c.videoplayer.getObj();if(c.mutableVolume.isSelected()){c.videoplayer.mute();console.log("video is mute");P.setAttribute("aria-label","video is muted")}else{c.videoplayer.unmute();console.log("video is unmute");P.setAttribute("aria-label","video is unmuted");c.videoplayer.setVolume(c.mutableVolume.getPosition())}}function N(O){c.videoplayer.unmute()}function j(O){c.videoplayer.setVolume(O.s7event.position)}function o(O){if(c.videoplayer.fullScreenAllowed()){c.fullScreenButton.setSelected(c.videoplayer.isFullScreen());c.videoplayer.toggleFullScreen()}else{if(!c.container.isFullScreen()){c.container.requestFullScreen()}else{c.container.cancelFullScreen()}}}function w(){c.videoplayer.setCaptionEnabled(c.closedCaptionButton.isSelected())}function q(){var O="";if(c.s7params.params.style!=""&&c.s7params.params.style!=undefined){O='    videoViewer.setParam("style", "'+c.s7params.params.style+'"); \n'}if(c.isCaption&&c.curCaption!=""&&c.curCaption!=undefined){O+='    videoViewer.setParam("caption", "'+c.curCaption+'"); \n'}if(c.isNavigation&&c.isNavigation!=""&&c.isNavigation!=undefined){O+='    videoViewer.setParam("navigation", "'+c.isNavigation+'"); \n'}var P="";if(c.s7params.params.config!=""&&c.s7params.params.config!=undefined){P='    videoViewer.setParam("config", "'+c.s7params.params.config+'"); \n'}var R="";if(c.s7params.params.config2!=""&&c.s7params.params.config2!=undefined){R='		videoViewer.setParam("config2", "'+c.s7params.params.config2+'"); \n'}var Q='<script language="javascript" type="text/javascript" src="'+(s7viewers.VideoViewer.codebase.contentUrl+"js/"+c.viewerFileName)+'"><\/script> \n<div id="'+c.containerId+'"></div> \n<script type="text/javascript"> \n    var videoViewer = new s7viewers.VideoViewer(); \n    videoViewer.setParam("videoserverurl", "'+F(c.videoplayer.component.videoServerUrl)+'"); \n    videoViewer.setParam("serverurl", "'+F(c.videoplayer.component.serverUrl)+'"); \n    videoViewer.setParam("contenturl", "'+F(c.s7params.get("contenturl","/is/content"))+'"); \n'+O+"    videoViewer."+(c.mediaSet.component.asset!=""?'setAsset("'+c.mediaSet.component.asset:'setVideo("'+c.videoplayer.getCurrentAsset())+'"); \n    videoViewer.setParam("stagesize", "$EMBED_WIDTH$,$EMBED_HEIGHT$"); \n	 videoViewer.setParam("emailurl", "'+F(c.emailShare.component.emailurl)+'"); \n'+((c.videoplayer.component.assetSpecificPosterImage||c.videoplayer.component.posterimage)?'	 videoViewer.setParam("posterimage", "'+(c.videoplayer.component.assetSpecificPosterImage||c.videoplayer.component.posterimage)+'"); \n':"")+P+R+'	 videoViewer.setContainerId("'+c.containerId+'"); \n	 videoViewer.init(); \n<\/script> \n';return Q}function F(O){if(O&&((O.indexOf("http://")==0)||(O.indexOf("https://")==0))){return O}var P=document.location.protocol+"//"+document.location.host;if(!O||O.indexOf("/")!=0){P+="/"}if(O){P+=O}return P}function v(O,P){c.updateOrientationMarkers();c.videoplayer.resize(O,P);c.videoScrubber.resize(0,0);c.controls.resize(O,c.controls.getHeight());J(O)}function J(P){if(c.supportsInline!=true){return}var T=document.getElementById(c.containerId+"_controls");var S=a.Util.getStyle(T,"display");T.style.display="block";c.videoTime.autoSize();var R=document.getElementById(c.containerId+"_playPauseButton").getBoundingClientRect();var Q=document.getElementById(c.containerId+"_videoTime").getBoundingClientRect();var O=document.getElementById(c.containerId+"_videoScrubber").getBoundingClientRect();c.videoScrubber.resize(Q.left-R.right-10,(O.bottom-O.top));T.style.display=S;L()}function L(){var aa=5;var S=2;var T=c.container.isPopup()&&!c.container.isFixedSize()&&!c.container.supportsNativeFullScreen()&&!c.videoplayer.fullScreenAllowed();var Z=!!c.videoplayer.getCurrentVideo();var O=c.firstMediasetParsed||(Z&&c.metadataLoaded);var P=O&&c.videoplayer.hasMultiAudioMultiCaptions();var Y=O&&c.isCaption&&!P;var U=[{id:c.containerId+"_fullScreenButton",isEnabled:!T,hide:function(){c.fullScreenButton.setCSS(".s7fullscreenbutton","display","none")},setPosition:function(ab){c.fullScreenButton.setCSS(".s7fullscreenbutton","right",ab)},show:function(){c.fullScreenButton.setCSS(".s7fullscreenbutton","display","block")},},{id:c.containerId+"_mutableVolume",isEnabled:c.videoplayer.supportsVolumeControl(),hide:function(){c.mutableVolume.setCSS(".s7mutablevolume","display","none")},setPosition:function(ab){c.mutableVolume.setCSS(".s7mutablevolume","right",ab)},show:function(){c.mutableVolume.setCSS(".s7mutablevolume","display","block")},},{id:c.containerId+"_audioCaptionsSelector",isEnabled:P,hide:function(){c.audioCaptionsSelector.setCSS(".s7audiocaptions","display","none")},setPosition:function(ab){c.audioCaptionsSelector.setCSS(".s7audiocaptions","right",ab)},show:function(){c.audioCaptionsSelector.setCSS(".s7audiocaptions","display","block")},},{id:c.containerId+"_closedCaptionButton",isEnabled:Y,hide:function(){c.closedCaptionButton.setCSS(".s7closedcaptionbutton","display","none")},setPosition:function(ab){c.closedCaptionButton.setCSS(".s7closedcaptionbutton","right",ab)},show:function(){c.closedCaptionButton.setCSS(".s7closedcaptionbutton","display","block")},},{id:c.containerId+"_videoTime",isEnabled:true,hide:function(){c.videoTime.setCSS(".s7videotime","display","none")},setPosition:function(ab){c.videoTime.setCSS(".s7videotime","right",ab)},show:function(){c.videoTime.setCSS(".s7videotime","display","block")},}];var R=aa;for(var W=0;W<U.length;W++){var X=U[W];if(X.isEnabled){X.setPosition(R+"px");X.show();var Q=D(document.getElementById(X.id),"width");Q=Number(Q.substring(0,Q.length-2));R+=Q+S}else{X.hide()}}var V=document.getElementById(c.containerId+"_playPauseButton").getBoundingClientRect();if(c.fixTrackCSS){c.videoScrubber.setCSS(".s7videoscrubber .s7track","width",(document.getElementById(c.containerId+"_videoTime").getBoundingClientRect().left-V.right-10)+"px")}c.videoScrubber.resize(document.getElementById(c.containerId+"_videoTime").getBoundingClientRect().left-V.right-10,document.getElementById(c.containerId+"_videoScrubber").getBoundingClientRect().height)}function D(R,Q){var P,O,S;if(R&&R.style){Q=Q.toLowerCase();O=Q.replace(/\-([a-z])/g,function(U,T){return T.toUpperCase()});S=R.style[O];if(!S){P=document.defaultView||window;if(P.getComputedStyle){S=P.getComputedStyle(R,"").getPropertyValue(Q)}else{if(R.currentStyle){S=R.currentStyle[O]}}}}return S||""}if(c.supportsInline){var K=c.container.getWidth();J(K)}else{c.controls.setCSS(".s7controlbar","display","none")}if((c.onInitComplete!=null)&&(typeof c.onInitComplete=="function")){c.onInitComplete()}if(!c.s7params.get("asset",null,"MediaSet")){L();if(c.embedShare){c.embedShare.setEmbedCode(q())}if((c.handlers.initComplete!=null)&&(typeof c.handlers.initComplete=="function")){c.handlers.initComplete()}c.controls.setCSS(".s7controlbar","visibility","inherit")}}this.s7params.addEventListener(a.Event.SDK_READY,function(){c.initSiteCatalyst(c.s7params,g)},false);this.s7params.setProvidedSdk(this.sdkProvided);this.s7params.init()};s7viewers.VideoViewer.prototype.setParam=function(b,c){if(this.isDisposed){return}this.params[b]=c};s7viewers.VideoViewer.prototype.getParam=function(c){var d=c.toLowerCase();for(var b in this.params){if(b.toLowerCase()==d){return this.params[b]}}return null};s7viewers.VideoViewer.prototype.setParams=function(b){if(this.isDisposed){return}var e=b.split("&");for(var c=0;c<e.length;c++){var d=e[c].split("=");if(d.length>1){this.setParam(d[0],decodeURIComponent(e[c].split("=")[1]))}}};s7viewers.VideoViewer.prototype.s7sdkUtilsAvailable=function(){if(s7viewers.VideoViewer.codebase.isDAM){return typeof(s7viewers.s7sdk)!="undefined"}else{return(typeof(s7classic)!="undefined")&&(typeof(s7classic.s7sdk)!="undefined")}};s7viewers.VideoViewer.prototype.resize=function(b,c){this.container.resize(b,c)};s7viewers.VideoViewer.prototype.init=function(){if(this.isDisposed){return}if(this.initCalled){return}this.initCalled=true;if(this.initializationComplete){return this}this.lockurldomains=(Boolean(Number(this.params.lockurldomains))||typeof this.params.lockurldomains=="undefined")?1:0;var i=document.getElementById(this.containerId);if(i){if(i.className!=""){if(i.className.indexOf(s7viewers.VideoViewer.cssClassName)!=-1){}else{i.className+=" "+s7viewers.VideoViewer.cssClassName}}else{i.className=s7viewers.VideoViewer.cssClassName}}this.s7sdkNamespace=s7viewers.VideoViewer.codebase.isDAM?"s7viewers":"s7classic";var d=this.getContentUrl()+this.sdkBasePath+"js/s7sdk/utils/Utils.js?namespace="+this.s7sdkNamespace;var f=null;if(document.scripts){f=document.scripts}else{f=document.getElementsByTagName("script")}if(this.s7sdkUtilsAvailable()){a=(s7viewers.VideoViewer.codebase.isDAM?s7viewers.s7sdk:s7classic.s7sdk);this.sdkProvided=true;if(this.isDisposed){return}a.Util.init();this.includeViewer();this.initializationComplete=true}else{if(!this.s7sdkUtilsAvailable()&&(s7viewers.VideoViewer.codebase.isDAM?s7viewers.S7SDK_S7VIEWERS_LOAD_STARTED:s7viewers.S7SDK_S7CLASSIC_LOAD_STARTED)){this.sdkProvided=true;var h=this;var g=setInterval(function(){if(h.s7sdkUtilsAvailable()){clearInterval(g);a=(s7viewers.VideoViewer.codebase.isDAM?s7viewers.s7sdk:s7classic.s7sdk);if(h.isDisposed){return}a.Util.init();h.includeViewer();h.initializationComplete=true}},100)}else{this.utilsScriptElm=document.createElement("script");this.utilsScriptElm.setAttribute("language","javascript");this.utilsScriptElm.setAttribute("type","text/javascript");var e=document.getElementsByTagName("head")[0];var c=this;function b(){if(!c.utilsScriptElm.executed){c.utilsScriptElm.executed=true;a=(s7viewers.VideoViewer.codebase.isDAM?s7viewers.s7sdk:s7classic.s7sdk);if(c.s7sdkUtilsAvailable()&&a.Util){if(c.isDisposed){return}a.Util.init();c.includeViewer();c.initializationComplete=true;c.utilsScriptElm.onreadystatechange=null;c.utilsScriptElm.onload=null;c.utilsScriptElm.onerror=null}}}if(typeof(c.utilsScriptElm.readyState)!="undefined"){c.utilsScriptElm.onreadystatechange=function(){if(c.utilsScriptElm.readyState=="loaded"){e.appendChild(c.utilsScriptElm)}else{if(c.utilsScriptElm.readyState=="complete"){b()}}};c.utilsScriptElm.setAttribute("src",d)}else{c.utilsScriptElm.onload=function(){b()};c.utilsScriptElm.onerror=function(){};c.utilsScriptElm.setAttribute("src",d);e.appendChild(c.utilsScriptElm);c.utilsScriptElm.setAttribute("data-src",c.utilsScriptElm.getAttribute("src"));c.utilsScriptElm.setAttribute("src","?namespace="+this.s7sdkNamespace)}if(s7viewers.VideoViewer.codebase.isDAM){s7viewers.S7SDK_S7VIEWERS_LOAD_STARTED=true}else{s7viewers.S7SDK_S7CLASSIC_LOAD_STARTED=true}}}return this};s7viewers.VideoViewer.prototype.getDomScriptTag=function(b){var d;if(document.scripts){d=document.scripts}else{d=document.getElementsByTagName("script")}for(var c=0;c<d.length;c++){if(d[c]&&d[c].getAttribute("src")!=null&&d[c].getAttribute("src").indexOf(b)!=-1){return d[c];break}}return null};s7viewers.VideoViewer.prototype.getDomain=function(b){var c=/(^http[s]?:\/\/[^\/]+)/i.exec(b);if(c==null){return""}else{return c[1]}};s7viewers.VideoViewer.prototype.setAsset=function(b,e){if(this.isDisposed){return}var d=null,c=null,f=null;if(e){if(Object.prototype.toString.apply(e)==="[object String]"){d=e}else{if(typeof e=="object"){if(e.caption){d=e.caption}if(e.navigation){c=e.navigation}if(e.posterimage){f=e.posterimage}}}}this.metadataLoaded=false;if(this.mediaSet){this.videoplayer.resetQosMetric();this.mediaSet.setAsset(b);if(d){this.isCaption=true;this.curCaption=d+",1";this.videoplayer.setCaption(d);this.videoplayer.setCaptionEnabled(this.storedCaptionEnabled)}else{this.isCaption=false;this.curCaption=null;this.videoplayer.setCaptionEnabled(false)}this.isNavigation=(c)?c:null;this.isPosterImage=(f)?f:null;this.closedCaptionButton.setSelected(this.storedCaptionEnabled);if(this.emailShare){this.emailShare.setThumbnail(b)}}else{this.setParam("asset",b)}};s7viewers.VideoViewer.prototype.setVideo=function(b,e){if(this.isDisposed){return}var d=null,c=null,f=null;if(e){if(Object.prototype.toString.apply(e)==="[object String]"){d=e}else{if(typeof e=="object"){if(e.caption){d=e.caption}if(e.navigation){c=e.navigation}if(e.posterimage){f=e.posterimage}}}}this.metadataLoaded=false;if(this.videoplayer){this.videoplayer.setVideo(b,f);if(d){this.isCaption=true;this.curCaption=d+",1";this.videoplayer.setCaption(d);this.videoplayer.setCaptionEnabled(this.storedCaptionEnabled)}else{this.isCaption=false;this.curCaption=null;this.videoplayer.setCaptionEnabled(false)}this.isNavigation=(c)?c:null;this.isPosterImage=(f)?f:null;this.closedCaptionButton.setSelected(this.storedCaptionEnabled)}else{if(b){this.setParam("video",b)}if(d){this.setParam("caption",d)}if(c){this.setParam("navigation",c)}if(f){this.setParam("posterimage",f)}}};s7viewers.VideoViewer.prototype.setLocalizedTexts=function(b){if(this.isDisposed){return}if(this.s7params){this.s7params.setLocalizedTexts(b)}else{this.setParam("localizedtexts",b)}};s7viewers.VideoViewer.prototype.initSiteCatalyst=function(i,c){if(i.get("asset",null,"MediaSet")){var f=i.get("asset",null,"MediaSet").split(",")[0].split(":")[0];this.isConfig2Exist=false;if(f.indexOf("/")!=-1){var d=a.MediaSetParser.findCompanyNameInAsset(f);var h=i.get("config2");this.isConfig2Exist=(h!=""&&typeof h!="undefined");if(this.isConfig2Exist){this.trackingParams={siteCatalystCompany:d,config2:h,isRoot:i.get("serverurl"),contentUrl:this.getContentUrl()};var b=this.getContentUrl()+"../AppMeasurementBridge.jsp?company="+d+(h==""?"":"&preset="+h);if(i.get("serverurl",null)){b+="&isRoot="+i.get("serverurl")}var g=document.createElement("script");g.setAttribute("language","javascript");g.setAttribute("type","text/javascript");g.setAttribute("src",b);var e=document.getElementsByTagName("head");g.onload=g.onerror=function(){if(!g.executed){g.executed=true;if(typeof c=="function"){c()}g.onreadystatechange=null;g.onload=null;g.onerror=null}};g.onreadystatechange=function(){if(g.readyState=="complete"||g.readyState=="loaded"){setTimeout(function(){if(!g.executed){g.executed=true;if(typeof c=="function"){c()}}g.onreadystatechange=null;g.onload=null;g.onerror=null},0)}};e[0].appendChild(g)}else{if(typeof c=="function"){c()}}}}else{if(typeof c=="function"){c()}}};s7viewers.VideoViewer.prototype.onFullScreenEnter=function(b){this.storedSocialShareDisplayProp=a.Util.getStyle(this.socialShare.getObj(),"display");this.socialShare.setCSS(".s7socialshare","display","none")};s7viewers.VideoViewer.prototype.onFullScreenExit=function(b){this.socialShare.setCSS(".s7socialshare","display",this.storedSocialShareDisplayProp)};s7viewers.VideoViewer.prototype.getComponent=function(b){if(this.isDisposed){return}switch(b){case"container":return this.container||null;case"mediaSet":return this.mediaSet||null;case"videoPlayer":return this.videoplayer||null;case"controls":return this.controls||null;case"videoScrubber":return this.videoScrubber||null;case"videoTime":return this.videoTime||null;case"mutableVolume":return this.mutableVolume||null;case"playPauseButton":return this.playPauseButton||null;case"closedCaptionButton":return this.closedCaptionButton||null;case"fullScreenButton":return this.fullScreenButton||null;case"twitterShare":return this.twitterShare||null;case"facebookShare":return this.facebookShare||null;case"linkShare":return this.linkShare||null;case"socialShare":return this.socialShare||null;case"emailShare":return this.emailShare||null;case"embedShare":return this.embedShare||null;case"parameterManager":return this.s7params||null;default:return null}};s7viewers.VideoViewer.prototype.setHandlers=function(c){if(this.isDisposed||this.initCalled){return}this.handlers=[];for(var b in c){if(!c.hasOwnProperty(b)){continue}if(typeof c[b]!="function"){continue}this.handlers[b]=c[b]}};s7viewers.VideoViewer.prototype.getModifiers=function(){return this.modifiers};s7viewers.VideoViewer.prototype.setModifier=function(f){if(this.isDisposed){return}var h,c,j,b,g,e;for(h in f){if(!this.modifiers.hasOwnProperty(h)){continue}c=this.modifiers[h];try{b=f[h];if(c.parseParams===false){g=new a.Modifier([b!=""?b:c.defaults[0]])}else{g=a.Modifier.parse(b,c.defaults,c.ranges)}if(g.values.length==1){this[h]=g.values[0];this.setModifierInternal(h)}else{if(g.values.length>1){j={};for(e=0;e<g.values.length;e++){j[c.params[e]]=g.values[e]}this[h]=j;this.setModifierInternal(h)}}}catch(d){throw new Error("Unable to process modifier: '"+h+"'. "+d)}}};s7viewers.VideoViewer.prototype.setModifierInternal=function(b){switch(b){default:break}};s7viewers.VideoViewer.prototype.parseMods=function(){var g,c,h,b,f,e;for(g in this.modifiers){if(!this.modifiers.hasOwnProperty(g)){continue}c=this.modifiers[g];try{b=this.s7params.get(g,"");if(c.parseParams===false){f=new a.Modifier([b!=""?b:c.defaults[0]])}else{f=a.Modifier.parse(b,c.defaults,c.ranges)}if(f.values.length==1){this[g]=f.values[0]}else{if(f.values.length>1){h={};for(e=0;e<f.values.length;e++){h[c.params[e]]=f.values[e]}this[g]=h}}}catch(d){throw new Error("Unable to process modifier: '"+g+"'. "+d)}}};s7viewers.VideoViewer.prototype.updateCSSMarkers=function(){var c=this.container.getSizeMarker();var b;if(c==a.common.Container.SIZE_MARKER_NONE){return}if(c==a.common.Container.SIZE_MARKER_LARGE){b="s7size_large"}else{if(c==a.common.Container.SIZE_MARKER_SMALL){b="s7size_small"}else{if(c==a.common.Container.SIZE_MARKER_MEDIUM){b="s7size_medium"}}}if(this.containerId){this.setNewSizeMarker(this.containerId,b)}this.reloadInnerComponents()};s7viewers.VideoViewer.prototype.reloadInnerComponents=function(){var c=this.s7params.getRegisteredComponents();for(var b=0;b<c.length;b++){if(c[b]&&c[b].restrictedStylesInvalidated()){c[b].reload()}}};s7viewers.VideoViewer.prototype.setNewSizeMarker=function(f,c){var b=document.getElementById(f).className;var d=/^(.*)(s7size_small|s7size_medium|s7size_large)(.*)$/gi;var e;if(b.match(d)){e=b.replace(d,"$1"+c+"$3")}else{e=b+" "+c}if(b!=e){document.getElementById(f).className=e}};s7viewers.VideoViewer.prototype.dispose=function(){if(this.appMeasurementBridge){this.appMeasurementBridge.dispose();this.appMeasurementBridge=null}if(this.trackingManager){this.trackingManager.dispose();this.trackingManager=null}if(this.videoplayer){this.videoplayer.dispose();this.videoplayer=null}if(this.facebookShare){this.facebookShare.dispose();this.facebookShare=null}if(this.twitterShare){this.twitterShare.dispose();this.twitterShare=null}if(this.linkShare){this.linkShare.dispose();this.linkShare=null}if(this.embedShare){this.embedShare.dispose();this.embedShare=null}if(this.emailShare){this.emailShare.dispose();this.emailShare=null}if(this.socialShare){this.socialShare.dispose();this.socialShare=null}if(this.closedCaptionButton){this.closedCaptionButton.dispose();this.closedCaptionButton=null}if(this.fullScreenButton){this.fullScreenButton.dispose();this.fullScreenButton=null}if(this.mutableVolume){this.mutableVolume.dispose();this.mutableVolume=null}if(this.videoTime){this.videoTime.dispose();this.videoTime=null}if(this.videoScrubber){this.videoScrubber.dispose();this.videoScrubber=null}if(this.playPauseButton){this.playPauseButton.dispose();this.playPauseButton=null}if(this.controls){this.controls.dispose();this.controls=null}if(this.mediaSet){this.mediaSet.dispose();this.mediaSet=null}if(this.s7params){this.s7params.dispose();this.s7params=null}if(this.container){var e=[s7viewers.VideoViewer.cssClassName,"s7touchinput","s7mouseinput","s7size_large","s7size_small","s7size_medium"];var c=document.getElementById(this.containerId).className.split(" ");for(var d=0;d<e.length;d++){var b=c.indexOf(e[d]);if(b!=-1){c.splice(b,1)}}document.getElementById(this.containerId).className=c.join(" ");this.container.dispose();this.container=null}this.params={};this.handlers=[];this.isDisposed=true};s7viewers.VideoViewer.prototype.updateOrientationMarkers=function(){if(!this.isOrientationMarkerForcedChanged){var b;if(window.innerWidth>window.innerHeight){b="s7device_landscape"}else{b="s7device_portrait"}if(document.getElementById(this.containerId).className.indexOf(b)==-1){this.setNewOrientationMarker(this.containerId,b);this.reloadInnerComponents()}}};s7viewers.VideoViewer.prototype.setNewOrientationMarker=function(f,c){var b=document.getElementById(f).className;var d=/^(.*)(s7device_landscape|s7device_portrait)(.*)$/gi;var e;if(b.match(d)){e=b.replace(d,"$1"+c+"$3")}else{e=b+" "+c}if(b!=e){document.getElementById(f).className=e}};s7viewers.VideoViewer.prototype.forceDeviceOrientationMarker=function(b){switch(b){case"s7device_portrait":case"s7device_landscape":this.isOrientationMarkerForcedChanged=true;if(this.containerId){this.setNewOrientationMarker(this.containerId,b)}this.reloadInnerComponents();break;case null:this.isOrientationMarkerForcedChanged=false;this.updateOrientationMarkers();break;default:break}};s7viewers.VideoViewer.prototype.getURLParameter=function(c){var b=a.ParameterManager.getSanitizedParameters(a.query.params,this.lockurldomains);return b[c]};s7viewers.VideoViewer.prototype.addClass=function(d,c){var b=document.getElementById(d).className.split(" ");if(b.indexOf(c)==-1){b[b.length]=c;document.getElementById(d).className=b.join(" ")}}})()};