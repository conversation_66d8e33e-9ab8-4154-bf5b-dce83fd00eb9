<template>
  <div class="energy-statistics">
    <!-- 控制面板 -->
    <el-card class="control-panel">
      <template #header>
        <div class="card-header">
          <el-icon><DataAnalysis /></el-icon>
          <span>能耗统计分析</span>
          <div class="header-actions">
            <el-button-group size="small">
              <el-button
                v-for="period in periods"
                :key="period.value"
                :type="energyStore.currentPeriod === period.value ? 'primary' : ''"
                @click="setPeriod(period.value)"
              >
                {{ period.label }}
              </el-button>
            </el-button-group>

            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="onDateRangeChange"
              size="small"
            />

            <el-button size="small" @click="exportReport" :loading="exportLoading">
              <el-icon><Download /></el-icon>
              导出报表
            </el-button>

            <el-switch
              v-model="energyStore.realTimeEnabled"
              @change="toggleRealTime"
              inline-prompt
              active-text="实时"
              inactive-text="静态"
              size="small"
            />
          </div>
        </div>
      </template>

      <!-- 预警信息 -->
      <div v-if="unresolvedAlerts.length > 0" class="alerts-section">
        <el-alert
          v-for="alert in unresolvedAlerts.slice(0, 3)"
          :key="alert.id"
          :title="alert.title"
          :description="alert.message"
          :type="alert.level === 'critical' ? 'error' : alert.level"
          show-icon
          :closable="true"
          @close="resolveAlert(alert.id)"
        />
        <el-link v-if="unresolvedAlerts.length > 3" type="primary" @click="showAllAlerts">
          查看全部 {{ unresolvedAlerts.length }} 条预警
        </el-link>
      </div>
    </el-card>

    <!-- KPI 指标卡片 -->
    <div class="kpi-section">
      <el-row :gutter="20">
        <el-col :span="4" v-for="kpi in kpiCards" :key="kpi.key">
          <el-card class="kpi-card" :class="kpi.trendClass">
            <div class="kpi-content">
              <div class="kpi-icon">
                <el-icon :size="32" :color="kpi.color">
                  <component :is="kpi.icon" />
                </el-icon>
              </div>
              <div class="kpi-data">
                <div class="kpi-value">{{ kpi.value }}</div>
                <div class="kpi-label">{{ kpi.label }}</div>
                <div class="kpi-trend" v-if="kpi.trend !== null">
                  <el-icon :class="kpi.trendClass">
                    <component :is="kpi.trendIcon" />
                  </el-icon>
                  <span>{{ Math.abs(kpi.trend) }}%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <!-- 能耗趋势图 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="chart-header">
              <span>能耗趋势分析</span>
              <el-radio-group v-model="trendChartType" size="small">
<el-radio-button value="energy">能耗</el-radio-button>
<el-radio-button value="cost">成本</el-radio-button>
<el-radio-button value="efficiency">效率</el-radio-button>
</el-radio-group>
            </div>
          </template>
          <div ref="trendChartRef" class="chart" />
        </el-card>
      </el-col>

      <!-- 功率分布图 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>功率分布分析</span>
          </template>
          <div ref="powerChartRef" class="chart" />
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-section">
      <!-- 效率热力图 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>效率热力图</span>
          </template>
          <div ref="heatmapChartRef" class="chart" />
        </el-card>
      </el-col>

      <!-- 成本构成图 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>成本构成分析</span>
          </template>
          <div ref="costChartRef" class="chart" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计表格 -->
    <el-card class="table-section">
      <template #header>
        <div class="table-header">
          <span>详细统计数据</span>
          <el-button size="small" @click="refreshData" :loading="energyStore.loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </template>

      <StatisticsTable
        :current-stats="energyStore.currentStatistics"
        :previous-stats="energyStore.previousStatistics"
        :comparison="energyStore.comparisonData"
      />
    </el-card>

    <!-- 报表导出对话框 -->
    <ReportExportDialog
      v-model="showExportDialog"
      @export="handleExport"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import {
  DataAnalysis,
  Download,
  Refresh,
  Lightning,
  TrendCharts,
  Money,
  Setting,
  ArrowUp,
  ArrowDown,
  Minus
} from '@element-plus/icons-vue'
import { useEnergyStore } from '@/stores/energy'
import { initEnergyCharts } from './chart-config'
import StatisticsTable from './StatisticsTable.vue'
import ReportExportDialog from './ReportExportDialog.vue'
import type { StatisticsPeriod } from '@/types'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const energyStore = useEnergyStore()
const exportLoading = ref(false)
const showExportDialog = ref(false)
const trendChartType = ref('energy')

// 图表引用
const trendChartRef = ref<HTMLElement>()
const powerChartRef = ref<HTMLElement>()
const heatmapChartRef = ref<HTMLElement>()
const costChartRef = ref<HTMLElement>()

// 日期范围
const dateRange = ref<[string, string]>([
  energyStore.dateRange.start,
  energyStore.dateRange.end
])

// 周期选项
const periods = [
  { label: '日', value: 'day' as StatisticsPeriod },
  { label: '周', value: 'week' as StatisticsPeriod },
  { label: '月', value: 'month' as StatisticsPeriod },
  { label: '季', value: 'quarter' as StatisticsPeriod },
  { label: '年', value: 'year' as StatisticsPeriod }
]

// 计算属性
const unresolvedAlerts = computed(() =>
  energyStore.alerts.filter(alert => !alert.resolved)
)

const kpiCards = computed(() => {
  const kpis = energyStore.getKPIs
  if (!kpis) return []

  const energyTrend = energyStore.getEnergyTrend
  const costTrend = energyStore.getCostTrend
  const efficiencyTrend = energyStore.getEfficiencyTrend

  return [
    {
      key: 'totalEnergy',
      label: '总能耗',
      value: `${kpis.totalEnergy.toLocaleString()} kWh`,
      icon: 'Lightning',
      color: '#409EFF',
      trend: energyTrend,
      trendIcon: energyTrend > 0 ? 'ArrowUp' : energyTrend < 0 ? 'ArrowDown' : 'Minus',
      trendClass: energyTrend > 0 ? 'trend-up' : energyTrend < 0 ? 'trend-down' : 'trend-stable'
    },
    {
      key: 'totalCost',
      label: '总成本',
      value: `¥${kpis.totalCost.toLocaleString()}`,
      icon: 'Money',
      color: '#E6A23C',
      trend: costTrend,
      trendIcon: costTrend > 0 ? 'ArrowUp' : costTrend < 0 ? 'ArrowDown' : 'Minus',
      trendClass: costTrend > 0 ? 'trend-up' : costTrend < 0 ? 'trend-down' : 'trend-stable'
    },
    {
      key: 'avgEfficiency',
      label: '平均效率',
      value: `${kpis.avgEfficiency.toFixed(1)}%`,
      icon: 'TrendCharts',
      color: '#67C23A',
      trend: efficiencyTrend,
      trendIcon: efficiencyTrend > 0 ? 'ArrowUp' : efficiencyTrend < 0 ? 'ArrowDown' : 'Minus',
      trendClass: efficiencyTrend > 0 ? 'trend-down' : efficiencyTrend < 0 ? 'trend-up' : 'trend-stable' // 效率提升是好事
    },
    {
      key: 'energyPerUnit',
      label: '单位能耗',
      value: `${kpis.energyPerUnit.toFixed(3)} kWh/m³`,
      icon: 'DataAnalysis',
      color: '#909399',
      trend: null,
      trendIcon: 'Minus',
      trendClass: 'trend-stable'
    },
    {
      key: 'carbonEmission',
      label: '碳排放',
      value: `${kpis.carbonEmission.toFixed(1)} kg`,
      icon: 'Setting',
      color: '#F56C6C',
      trend: null,
      trendIcon: 'Minus',
      trendClass: 'trend-stable'
    },
    {
      key: 'loadFactor',
      label: '负荷因子',
      value: kpis.loadFactor.toFixed(3),
      icon: 'TrendCharts',
      color: '#909399',
      trend: null,
      trendIcon: 'Minus',
      trendClass: 'trend-stable'
    }
  ]
})

// 图表实例
let chartInstances: any = {}

// 方法
const setPeriod = (period: StatisticsPeriod) => {
  energyStore.setPeriod(period)
}

const onDateRangeChange = (range: [string, string] | null) => {
  if (range) {
    energyStore.setDateRange(range[0], range[1])
  }
}

const toggleRealTime = (enabled: boolean) => {
  if (enabled) {
    energyStore.startRealTimeUpdate()
    ElMessage.success('已开启实时数据更新')
  } else {
    energyStore.stopRealTimeUpdate()
    ElMessage.info('已关闭实时数据更新')
  }
}

const resolveAlert = (alertId: string) => {
  energyStore.resolveAlert(alertId)
  ElMessage.success('预警已处理')
}

const showAllAlerts = () => {
  ElMessageBox.alert(
    `共有 ${unresolvedAlerts.value.length} 条未处理的预警信息`,
    '预警详情',
    {
      confirmButtonText: '确定',
      type: 'warning'
    }
  )
}

const exportReport = () => {
  showExportDialog.value = true
}

const handleExport = async (config: any) => {
  exportLoading.value = true
  try {
    const result = await energyStore.exportReport(config)
    ElMessage.success('报表导出成功')
    showExportDialog.value = false
  } catch (error) {
    ElMessage.error('报表导出失败')
  } finally {
    exportLoading.value = false
  }
}

const refreshData = () => {
  energyStore.loadEnergyData()
}

const updateCharts = () => {
  if (!energyStore.currentStatistics) return

  nextTick(() => {
    chartInstances = initEnergyCharts({
      trendChart: trendChartRef.value,
      powerChart: powerChartRef.value,
      heatmapChart: heatmapChartRef.value,
      costChart: costChartRef.value
    }, {
      trendData: energyStore.trendData || [],
      energyData: energyStore.energyData || [],
      statistics: energyStore.currentStatistics as any,
      trendType: trendChartType.value
    })
  })
}

// 监听趋势图类型变化
watch(trendChartType, () => {
  updateCharts()
})

// 监听数据变化
watch(() => energyStore.currentStatistics, () => {
  updateCharts()
}, { deep: true })

// 生命周期
onMounted(async () => {
  await energyStore.loadEnergyData()
  await nextTick()
  updateCharts()
})
</script>

<style lang="scss" scoped>
.energy-statistics {
  .control-panel {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      > div:first-child {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }

    .alerts-section {
      margin-top: 16px;

      .el-alert {
        margin-bottom: 8px;
      }
    }
  }

  .kpi-section {
    margin-bottom: 20px;

    .kpi-card {
      height: 120px;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .kpi-content {
        display: flex;
        align-items: center;
        height: 100%;

        .kpi-icon {
          margin-right: 16px;
        }

        .kpi-data {
          flex: 1;

          .kpi-value {
            font-size: 24px;
            font-weight: bold;
            color: var(--el-text-color-primary);
            margin-bottom: 4px;
          }

          .kpi-label {
            font-size: 14px;
            color: var(--el-text-color-regular);
            margin-bottom: 8px;
          }

          .kpi-trend {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;

            &.trend-up {
              color: #F56C6C;
            }

            &.trend-down {
              color: #67C23A;
            }

            &.trend-stable {
              color: var(--el-text-color-regular);
            }
          }
        }
      }

      &.trend-up {
        border-left: 4px solid #F56C6C;
      }

      &.trend-down {
        border-left: 4px solid #67C23A;
      }

      &.trend-stable {
        border-left: 4px solid var(--el-border-color);
      }
    }
  }

  .charts-section {
    margin-bottom: 20px;

    .chart-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 600;
    }

    .chart {
      height: 350px;
      width: 100%;
    }
  }

  .table-section {
    .table-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 600;
    }
  }
}

// 全局样式覆盖
:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-button-group .el-button) {
  padding: 8px 12px;
}
</style>
