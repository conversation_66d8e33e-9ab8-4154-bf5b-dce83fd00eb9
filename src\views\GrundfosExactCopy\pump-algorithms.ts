/**
 * 水泵曲线算法与计算函数
 * 包含多项式拟合、样条插值、亲和定律和多泵性能计算等功能
 */

/**
 * 多项式拟合算法
 * @param points 数据点数组 [x, y]
 * @param degree 多项式阶数
 * @param regularization 正则化参数，防止过拟合
 * @returns 多项式系数数组
 */
export function polynomialFit(
  points: number[][],
  degree: number = 3,
  regularization: number = 0.01
): number[] {
  // 确保有足够的点进行拟合
  if (points.length < degree + 1) {
    console.warn('多项式拟合需要至少 degree + 1 个点');
    degree = Math.max(1, points.length - 1);
  }

  // 提取 x 和 y 数组
  const x = points.map(p => p[0]);
  const y = points.map(p => p[1]);
  const n = x.length;

  // 创建范德蒙德矩阵
  const X = [];
  for (let i = 0; i < n; i++) {
    const row = [];
    for (let j = 0; j <= degree; j++) {
      row.push(Math.pow(x[i], j));
    }
    X.push(row);
  }

  // 使用正则化的最小二乘法解决系数
  // (X^T * X + λI)^(-1) * X^T * y
  const XT = transpose(X);
  const XTX = matrixMultiply(XT, X);
  
  // 添加正则化项
  for (let i = 0; i <= degree; i++) {
    XTX[i][i] += regularization;
  }
  
  const XTXInv = matrixInverse(XTX);
  const XTY = matrixVectorMultiply(XT, y);
  const coefficients = matrixVectorMultiply(XTXInv, XTY);

  return coefficients;
}

/**
 * 使用多项式系数计算y值
 * @param x x坐标
 * @param coefficients 多项式系数
 * @returns y坐标
 */
export function evaluatePolynomial(x: number, coefficients: number[]): number {
  let y = 0;
  for (let i = 0; i < coefficients.length; i++) {
    y += coefficients[i] * Math.pow(x, i);
  }
  return y;
}

/**
 * 自然三次样条插值算法
 * @param points 数据点数组 [x, y]
 * @returns 样条参数数组，每段曲线包含4个系数
 */
export function naturalCubicSpline(points: number[][]): { x: number; coefficients: number[] }[] {
  // 确保点按x值排序
  const sortedPoints = [...points].sort((a, b) => a[0] - b[0]);
  
  const n = sortedPoints.length - 1;
  if (n < 1) return [];
  
  const x = sortedPoints.map(p => p[0]);
  const y = sortedPoints.map(p => p[1]);
  
  // 计算每段的长度
  const h = [];
  for (let i = 0; i < n; i++) {
    h.push(x[i + 1] - x[i]);
  }
  
  // 构建三对角矩阵
  const alpha = Array(n - 1).fill(0);
  for (let i = 1; i < n; i++) {
    alpha[i - 1] = 3 / h[i] * (y[i + 1] - y[i]) - 3 / h[i - 1] * (y[i] - y[i - 1]);
  }
  
  // 求解三对角矩阵以获得二阶导数
  const l = Array(n + 1).fill(0);
  const mu = Array(n + 1).fill(0);
  const z = Array(n + 1).fill(0);
  
  l[0] = 1;
  mu[0] = 0;
  z[0] = 0;
  
  for (let i = 1; i < n; i++) {
    l[i] = 2 * (x[i + 1] - x[i - 1]) - h[i - 1] * mu[i - 1];
    mu[i] = h[i] / l[i];
    z[i] = (alpha[i - 1] - h[i - 1] * z[i - 1]) / l[i];
  }
  
  l[n] = 1;
  z[n] = 0;
  
  // 计算二阶导数
  const c = Array(n + 1).fill(0);
  for (let j = n - 1; j >= 0; j--) {
    c[j] = z[j] - mu[j] * c[j + 1];
  }
  
  // 计算剩余的系数
  const b = Array(n).fill(0);
  const d = Array(n).fill(0);
  
  for (let j = 0; j < n; j++) {
    b[j] = (y[j + 1] - y[j]) / h[j] - h[j] * (c[j + 1] + 2 * c[j]) / 3;
    d[j] = (c[j + 1] - c[j]) / (3 * h[j]);
  }
  
  // 构建样条参数
  const splines = [];
  for (let i = 0; i < n; i++) {
    splines.push({
      x: x[i],
      coefficients: [y[i], b[i], c[i], d[i]]
    });
  }
  
  return splines;
}

/**
 * 使用样条参数计算y值
 * @param x x坐标
 * @param splines 样条参数数组
 * @returns y坐标
 */
export function evaluateSpline(x: number, splines: { x: number; coefficients: number[] }[]): number {
  // 找到对应的样条段
  let i = 0;
  while (i < splines.length - 1 && x > splines[i + 1].x) {
    i++;
  }
  
  if (i >= splines.length) {
    i = splines.length - 1;
  }
  
  // 计算x与样条起点的差值
  const dx = x - splines[i].x;
  
  // 使用系数计算y值: a + b*dx + c*dx^2 + d*dx^3
  const [a, b, c, d] = splines[i].coefficients;
  return a + b * dx + c * dx * dx + d * dx * dx * dx;
}

/**
 * 根据指定方法生成平滑曲线
 * @param points 原始数据点 [x, y]
 * @param numPoints 生成的点数量
 * @param method 拟合方法: 'polynomial' 或 'spline'
 * @param params 算法参数
 * @returns 生成的曲线点 [x, y]
 */
export function generateCurveFromPoints(
  points: number[][],
  numPoints: number = 200,
  method: string = 'spline',
  params: any = {}
): number[][] {
  if (points.length < 2) return points;
  
  // 确保点按x排序
  const sortedPoints = [...points].sort((a, b) => a[0] - b[0]);
  
  // 获取x的范围
  const minX = sortedPoints[0][0];
  const maxX = sortedPoints[sortedPoints.length - 1][0];
  const step = (maxX - minX) / (numPoints - 1);
  
  const result = [];
  
  if (method === 'polynomial') {
    // 多项式拟合
    const degree = params.degree || 4;
    const regularization = params.regularization || 0.005;
    const coefficients = polynomialFit(sortedPoints, degree, regularization);
    
    for (let i = 0; i < numPoints; i++) {
      const x = minX + i * step;
      const y = evaluatePolynomial(x, coefficients);
      result.push([x, y]);
    }
  } else if (method === 'spline') {
    // 样条插值
    const splines = naturalCubicSpline(sortedPoints);
    
    for (let i = 0; i < numPoints; i++) {
      const x = minX + i * step;
      const y = evaluateSpline(x, splines);
      result.push([x, y]);
    }
  } else {
    // 默认使用线性插值
    for (let i = 0; i < numPoints; i++) {
      const x = minX + i * step;
      
      // 找到最接近的两个点
      let j = 0;
      while (j < sortedPoints.length - 1 && x > sortedPoints[j + 1][0]) {
        j++;
      }
      
      if (j === sortedPoints.length - 1) {
        result.push([x, sortedPoints[j][1]]);
      } else {
        // 线性插值
        const x0 = sortedPoints[j][0];
        const x1 = sortedPoints[j + 1][0];
        const y0 = sortedPoints[j][1];
        const y1 = sortedPoints[j + 1][1];
        
        const y = y0 + (y1 - y0) * (x - x0) / (x1 - x0);
        result.push([x, y]);
      }
    }
  }
  
  return result;
}

/**
 * 亲和定律计算 - 计算不同转速下的流量、扬程和功率
 * @param flow 原始流量
 * @param head 原始扬程
 * @param power 原始功率 (如果不需要计算功率可以为0)
 * @param oldSpeed 原始转速
 * @param newSpeed 新转速
 * @returns 新的流量、扬程和功率
 */
export function affinityLaw(
  flow: number,
  head: number,
  power: number = 0,
  oldSpeed: number,
  newSpeed: number
): { flow: number; head: number; power: number } {
  // 流量与转速成正比
  const newFlow = flow * (newSpeed / oldSpeed);
  
  // 扬程与转速的平方成正比
  const newHead = head * Math.pow(newSpeed / oldSpeed, 2);
  
  // 功率与转速的立方成正比
  const newPower = power * Math.pow(newSpeed / oldSpeed, 3);
  
  return { flow: newFlow, head: newHead, power: newPower };
}

/**
 * 计算多泵系统性能
 * @param flow 单泵流量
 * @param head 单泵扬程
 * @param pumpCount 泵数量
 * @param isParallel 是否并联 (true: 并联, false: 串联)
 * @returns [新流量, 新扬程]
 */
export function calculateMultiPumpPerformance(
  flow: number,
  head: number,
  pumpCount: number,
  isParallel: boolean
): [number, number] {
  if (isParallel) {
    // 并联: 流量增加n倍，扬程不变
    // 注意：实际并联时，由于管路损失和相互影响，流量会略小于理论值
    // 使用修正系数模拟真实情况
    let parallelEfficiencyFactor = 1.0;
    
    // 根据泵数量确定效率系数
    if (pumpCount === 2) {
      parallelEfficiencyFactor = 0.98;
    } else if (pumpCount === 3) {
      parallelEfficiencyFactor = 0.96;
    } else if (pumpCount === 4) {
      parallelEfficiencyFactor = 0.94;
    } else if (pumpCount === 5) {
      parallelEfficiencyFactor = 0.92; // 特别为5泵并联优化
    } else if (pumpCount <= 6) {
      parallelEfficiencyFactor = 0.90;
    } else if (pumpCount <= 8) {
      parallelEfficiencyFactor = 0.88;
    } else {
      parallelEfficiencyFactor = 0.85; // 超过8泵时效率系数进一步降低
    }
    
    return [flow * pumpCount * parallelEfficiencyFactor, head];
  } else {
    // 串联: 扬程增加n倍，流量不变
    // 注意：实际串联时，由于管路损失和相互影响，扬程会略小于理论值
    const seriesEfficiencyFactor = 0.95;
    
    return [flow, head * pumpCount * seriesEfficiencyFactor];
  }
}

// 辅助函数: 矩阵转置
function transpose(matrix: number[][]): number[][] {
  const rows = matrix.length;
  const cols = matrix[0].length;
  const result = Array(cols).fill(0).map(() => Array(rows).fill(0));
  
  for (let i = 0; i < rows; i++) {
    for (let j = 0; j < cols; j++) {
      result[j][i] = matrix[i][j];
    }
  }
  
  return result;
}

// 辅助函数: 矩阵乘法
function matrixMultiply(a: number[][], b: number[][]): number[][] {
  const rowsA = a.length;
  const colsA = a[0].length;
  const colsB = b[0].length;
  const result = Array(rowsA).fill(0).map(() => Array(colsB).fill(0));
  
  for (let i = 0; i < rowsA; i++) {
    for (let j = 0; j < colsB; j++) {
      for (let k = 0; k < colsA; k++) {
        result[i][j] += a[i][k] * b[k][j];
      }
    }
  }
  
  return result;
}

// 辅助函数: 矩阵向量乘法
function matrixVectorMultiply(matrix: number[][], vector: number[]): number[] {
  const rows = matrix.length;
  const cols = matrix[0].length;
  const result = Array(rows).fill(0);
  
  for (let i = 0; i < rows; i++) {
    for (let j = 0; j < cols; j++) {
      result[i] += matrix[i][j] * vector[j];
    }
  }
  
  return result;
}

// 辅助函数: 矩阵求逆 (使用高斯-约当消元法)
function matrixInverse(matrix: number[][]): number[][] {
  const n = matrix.length;
  
  // 创建增广矩阵 [A|I]
  const augmented = [];
  for (let i = 0; i < n; i++) {
    augmented[i] = [...matrix[i]];
    for (let j = 0; j < n; j++) {
      augmented[i].push(i === j ? 1 : 0);
    }
  }
  
  // 高斯-约当消元
  for (let i = 0; i < n; i++) {
    // 寻找主元
    let maxRow = i;
    for (let j = i + 1; j < n; j++) {
      if (Math.abs(augmented[j][i]) > Math.abs(augmented[maxRow][i])) {
        maxRow = j;
      }
    }
    
    // 交换行
    if (maxRow !== i) {
      [augmented[i], augmented[maxRow]] = [augmented[maxRow], augmented[i]];
    }
    
    // 将主元归一化
    const pivot = augmented[i][i];
    for (let j = i; j < 2 * n; j++) {
      augmented[i][j] /= pivot;
    }
    
    // 消元
    for (let j = 0; j < n; j++) {
      if (j !== i) {
        const factor = augmented[j][i];
        for (let k = i; k < 2 * n; k++) {
          augmented[j][k] -= factor * augmented[i][k];
        }
      }
    }
  }
  
  // 提取逆矩阵
  const inverse = [];
  for (let i = 0; i < n; i++) {
    inverse[i] = augmented[i].slice(n);
  }
  
  return inverse;
} 