import { DatabaseManager, type DatabaseConfig } from './database'
import type { 
  SystemSettings, 
  User, 
  Role, 
  Permission, 
  WarningInfo, 
  DeviceStatus, 
  SystemLog,
  PumpCurveData,
  EnergyData,
  OptimizationData
} from '@/types'

/**
 * 数据库配置
 */
export const databaseConfig: DatabaseConfig = {
  name: 'smart_water_platform',
  version: 1,
  stores: [
    {
      name: 'settings',
      keyPath: 'id',
      indexes: [
        { name: 'section', keyPath: 'section' },
        { name: 'updatedAt', keyPath: 'updatedAt' }
      ]
    },
    {
      name: 'users',
      keyPath: 'id',
      indexes: [
        { name: 'username', keyPath: 'username', unique: true },
        { name: 'email', keyPath: 'email', unique: true },
        { name: 'role', keyPath: 'role' },
        { name: 'status', keyPath: 'status' },
        { name: 'department', keyPath: 'department' },
        { name: 'createdAt', keyPath: 'createdAt' },
        { name: 'lastLogin', keyPath: 'lastLogin' }
      ]
    },
    {
      name: 'roles',
      keyPath: 'id',
      indexes: [
        { name: 'name', keyPath: 'name' },
        { name: 'isSystem', keyPath: 'isSystem' },
        { name: 'updatedAt', keyPath: 'updatedAt' }
      ]
    },
    {
      name: 'permissions',
      keyPath: 'id',
      indexes: [
        { name: 'category', keyPath: 'category' },
        { name: 'resource', keyPath: 'resource' }
      ]
    },
    {
      name: 'warnings',
      keyPath: 'id',
      indexes: [
        { name: 'category', keyPath: 'category' },
        { name: 'severity', keyPath: 'severity' },
        { name: 'resolved', keyPath: 'resolved' },
        { name: 'source', keyPath: 'source' },
        { name: 'timestamp', keyPath: 'timestamp' },
        { name: 'resolvedAt', keyPath: 'resolvedAt' }
      ]
    },
    {
      name: 'devices',
      keyPath: 'id',
      indexes: [
        { name: 'name', keyPath: 'name' },
        { name: 'type', keyPath: 'type' },
        { name: 'status', keyPath: 'status' },
        { name: 'health', keyPath: 'health' },
        { name: 'lastUpdate', keyPath: 'lastUpdate' },
        { name: 'nextMaintenance', keyPath: 'nextMaintenance' }
      ]
    },
    {
      name: 'system_logs',
      keyPath: 'id',
      indexes: [
        { name: 'level', keyPath: 'level' },
        { name: 'category', keyPath: 'category' },
        { name: 'timestamp', keyPath: 'timestamp' },
        { name: 'userId', keyPath: 'userId' },
        { name: 'ip', keyPath: 'ip' }
      ]
    },
    {
      name: 'pump_curves',
      keyPath: 'id',
      indexes: [
        { name: 'name', keyPath: 'name' },
        { name: 'type', keyPath: 'type' },
        { name: 'createdAt', keyPath: 'createdAt' },
        { name: 'isActive', keyPath: 'isActive' }
      ]
    },
    {
      name: 'energy_data',
      keyPath: 'id',
      indexes: [
        { name: 'timestamp', keyPath: 'timestamp' },
        { name: 'type', keyPath: 'type' },
        { name: 'deviceId', keyPath: 'deviceId' },
        { name: 'date', keyPath: 'date' }
      ]
    },
    {
      name: 'optimization_data',
      keyPath: 'id',
      indexes: [
        { name: 'timestamp', keyPath: 'timestamp' },
        { name: 'type', keyPath: 'type' },
        { name: 'status', keyPath: 'status' },
        { name: 'createdAt', keyPath: 'createdAt' }
      ]
    }
  ]
}

/**
 * 数据库管理器实例
 */
export const dbManager = new DatabaseManager(databaseConfig)

/**
 * 数据访问对象
 */
export class SettingsDAO {
  private db: DatabaseManager

  constructor(db: DatabaseManager) {
    this.db = db
  }

  async saveSetting(section: string, data: any): Promise<void> {
    const setting = {
      id: section,
      section,
      data,
      updatedAt: new Date().toISOString()
    }

    const transaction = this.db.getDatabase().transaction(['settings'], 'readwrite')
    const store = transaction.objectStore('settings')
    await new Promise<void>((resolve, reject) => {
      const request = store.put(setting)
      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }

  async getSetting(section: string): Promise<any> {
    const transaction = this.db.getDatabase().transaction(['settings'], 'readonly')
    const store = transaction.objectStore('settings')
    
    return new Promise((resolve, reject) => {
      const request = store.get(section)
      request.onsuccess = () => {
        const result = request.result
        resolve(result ? result.data : null)
      }
      request.onerror = () => reject(request.error)
    })
  }

  async getAllSettings(): Promise<SystemSettings | null> {
    const transaction = this.db.getDatabase().transaction(['settings'], 'readonly')
    const store = transaction.objectStore('settings')
    
    return new Promise((resolve, reject) => {
      const request = store.getAll()
      request.onsuccess = () => {
        const results = request.result
        if (results.length === 0) {
          resolve(null)
        } else {
          const settings: any = {}
          results.forEach((item: any) => {
            settings[item.section] = item.data
          })
          resolve(settings as SystemSettings)
        }
      }
      request.onerror = () => reject(request.error)
    })
  }
}

/**
 * 数据库初始化器
 */
export class DatabaseInitializer {
  private db: DatabaseManager

  constructor(db: DatabaseManager) {
    this.db = db
  }

  /**
   * 初始化数据库并填充初始数据
   */
  async initialize(): Promise<void> {
    try {
      await this.db.init()
      console.log('Database initialized successfully')
      
      // 检查是否需要填充初始数据
      const hasData = await this.checkExistingData()
      if (!hasData) {
        await this.seedInitialData()
        console.log('Initial data seeded successfully')
      }
    } catch (error) {
      console.error('Failed to initialize database:', error)
      throw error
    }
  }

  /**
   * 检查是否已有数据
   */
  private async checkExistingData(): Promise<boolean> {
    try {
      const transaction = this.db.getDatabase().transaction(['users'], 'readonly')
      const store = transaction.objectStore('users')
      
      return new Promise((resolve, reject) => {
        const request = store.count()
        request.onsuccess = () => resolve(request.result > 0)
        request.onerror = () => reject(request.error)
      })
    } catch (error) {
      return false
    }
  }

  /**
   * 填充初始数据
   */
  private async seedInitialData(): Promise<void> {
    // 这里会调用各个数据生成器来填充初始数据
    // 由于篇幅限制，具体实现将在后续文件中完成
    console.log('Seeding initial data...')
  }

  /**
   * 清空所有数据
   */
  async clearAllData(): Promise<void> {
    const storeNames = databaseConfig.stores.map(store => store.name)
    const transaction = this.db.getDatabase().transaction(storeNames, 'readwrite')
    
    const promises = storeNames.map(storeName => {
      return new Promise<void>((resolve, reject) => {
        const store = transaction.objectStore(storeName)
        const request = store.clear()
        request.onsuccess = () => resolve()
        request.onerror = () => reject(request.error)
      })
    })

    await Promise.all(promises)
    console.log('All data cleared successfully')
  }

  /**
   * 导出数据
   */
  async exportData(): Promise<any> {
    const data: any = {}
    const storeNames = databaseConfig.stores.map(store => store.name)
    
    for (const storeName of storeNames) {
      const transaction = this.db.getDatabase().transaction([storeName], 'readonly')
      const store = transaction.objectStore(storeName)
      
      data[storeName] = await new Promise((resolve, reject) => {
        const request = store.getAll()
        request.onsuccess = () => resolve(request.result)
        request.onerror = () => reject(request.error)
      })
    }

    return {
      version: databaseConfig.version,
      timestamp: new Date().toISOString(),
      data
    }
  }

  /**
   * 导入数据
   */
  async importData(exportedData: any): Promise<void> {
    if (exportedData.version !== databaseConfig.version) {
      throw new Error('Data version mismatch')
    }

    // 清空现有数据
    await this.clearAllData()

    // 导入新数据
    const storeNames = Object.keys(exportedData.data)
    
    for (const storeName of storeNames) {
      const items = exportedData.data[storeName]
      if (items && items.length > 0) {
        const transaction = this.db.getDatabase().transaction([storeName], 'readwrite')
        const store = transaction.objectStore(storeName)
        
        for (const item of items) {
          await new Promise<void>((resolve, reject) => {
            const request = store.add(item)
            request.onsuccess = () => resolve()
            request.onerror = () => reject(request.error)
          })
        }
      }
    }

    console.log('Data imported successfully')
  }
}

// 导出实例
export const settingsDAO = new SettingsDAO(dbManager)
export const dbInitializer = new DatabaseInitializer(dbManager)
