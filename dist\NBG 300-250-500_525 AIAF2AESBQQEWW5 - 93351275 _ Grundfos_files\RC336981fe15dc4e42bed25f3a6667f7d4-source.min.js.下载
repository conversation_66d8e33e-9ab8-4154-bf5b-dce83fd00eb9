// For license information, see `https://assets.adobedtm.com/5e107551ef5d/89d0c5ed902a/4021f82e766f/RC336981fe15dc4e42bed25f3a6667f7d4-source.js`.
_satellite.__registerScript('https://assets.adobedtm.com/5e107551ef5d/89d0c5ed902a/4021f82e766f/RC336981fe15dc4e42bed25f3a6667f7d4-source.min.js', "window.loadFive9=function(){function e(e){return e.toString(16).padStart(2,\"0\")}function a(a){var o=new Uint8Array((a||40)/2);return window.crypto.getRandomValues(o),Array.from(o,e).join(\"\")}var o=dataLayer.companyCode;if(\"\"!=o&&\"\"!=dataLayer.language){var t=\"Default\",n=\"Default\",i=\"Default\",r=\"Default\",d=\"Default\";null!=dataLayer.user&&(t=dataLayer.user.partnerCompanyId,n=dataLayer.user.partnerCompanyDivision,i=dataLayer.user.partnerCompanyName,r=dataLayer.user.partnerCompanySegment,d=dataLayer.user.partnerCompanySalesOffice);var s=_satellite.getVar(\"public user id\")||\"Default\",c=_satellite.getVar(\"marketoLeadId\")||\"Default\",l=dataLayer.siteCountry;\"GPU\"==o&&(o=\"GCB_GPU\"),\"GPS\"!=o&&\"GBL\"!=o&&\"GCA\"!=o||(o=o+\"_\"+dataLayer.language.toUpperCase()),sessionStorage.getItem(\"chatBot_userSession_id\")||sessionStorage.setItem(\"chatBot_userSession_id\",_satellite.getVar(\"visitorId\")+a(16)),window.chatBot_userSession_id=sessionStorage.getItem(\"chatBot_userSession_id\"),\"\"==_satellite.getVar(\"marketoLeadId\")&&(c=\"Default\"),\"\"==l&&(l=\"Default\"),F9.Chat.Wrapper.init({cdn:\"prod-eu\",useBusinessHours:!1,languages:{enabled:!1,backgroundColor:\"#126af3\"},l10n:{en:{messenger:{customText:{formErrorInvalidEmail:window.CTF.FIVE9.formErrorInvalidEmail,headerText:window.CTF.FIVE9.headerText,inputPlaceholder:window.CTF.FIVE9.inputPlaceholder,inputPlaceholderBlocked:window.CTF.FIVE9.inputPlaceholderBlocked,locationNotSupported:window.CTF.FIVE9.locationNotSupported,locationServicesDenied:window.CTF.FIVE9.locationServicesDenied,messageTimestampFormat:window.CTF.FIVE9.messageTimestampFormat,shareLocation:window.CTF.FIVE9.shareLocation,uploadDocument:window.CTF.FIVE9.uploadDocument,uploadPhoto:window.CTF.FIVE9.uploadPhoto,soundNotification:window.CTF.FIVE9.soundNotification,printTranscipt:window.CTF.FIVE9.printTranscipt,startConversation:window.CTF.FIVE9.startConversation,cancelConversation:window.CTF.FIVE9.cancelConversation,closeConfirmationEndChat:window.CTF.FIVE9.closeConversationConfirmationEndChat,endChatconfirmationHeader:window.CTF.FIVE9.closeConversationConfirmationEndChat,closeConfirmationMessage:window.CTF.FIVE9.closeConversationConfirmationMessage,closeConfirmationCancel:window.CTF.FIVE9.closeConversationConfirmationCancel,welcomeHeader:window.CTF.FIVE9.welcomeHeader,welcomeDescription:window.CTF.FIVE9.welcomeDescription,languageContinue:window.CTF.FIVE9.languageContinue,languageCancel:window.CTF.FIVE9.languageCancel,conversationTerminated:window.CTF.FIVE9.conversationTerminated,chatEndedMessage:window.CTF.FIVE9.chatEndedMessage,startNewChat:window.CTF.FIVE9.startNewChat,closeWindow:window.CTF.FIVE9.closeWindow,downloadTranscript:window.CTF.FIVE9.downloadTranscript}},systemMessages:{conversationCreated:window.CTF.FIVE9.conversationCreated,participantAccepted:window.CTF.FIVE9.participantAccepted,participantJoined:\"\",participantLeft:window.CTF.FIVE9.participantLeft,transferredToParticipant:window.CTF.FIVE9.transferredToParticipant,transferredToGroup:window.CTF.FIVE9.transferredToGroup,conversationTerminated:window.CTF.FIVE9.conversationTerminated},captureFields:[{k:\"name\",l:window.CTF.FIVE9.name,p:\"\"},{k:\"email\",l:window.CTF.FIVE9.emailAddress,p:\"\"}]}},prepopulatedFields:[{k:\"campaign\",v:\"Chat_\"+o},{k:\"chatBot.userSession.id\",v:window.chatBot_userSession_id},{k:\"chatBot.partnerCompanyDivision\",v:n},{k:\"chatBot.partnerCompanyId\",v:t},{k:\"chatBot.partnerCompanyName\",v:i},{k:\"chatBot.partnerCompanySegment\",v:r},{k:\"chatBot.partnerCompanySalesOffice\",v:d},{k:\"chatBot.mcid\",v:_satellite.getVar(\"visitorId\")},{k:\"chatBot.puid\",v:s},{k:\"chatBot.marketoLeadId\",v:c},{k:\"chatBot.Platform\",v:dataLayer.platform},{k:\"chatBot.siteCountry\",v:l},{k:\"chatBot.companyCode\",v:dataLayer.companyCode},{k:\"chatBot.language\",v:dataLayer.language},{k:\"chatBot.websiteURL\",v:window.location.href},{k:\"Question\",v:\"Hello\"}],messenger:{integrationId:\"41f604b8-9a47-4cb6-93be-c380b2dc7052\",soundNotificationEnabled:!0,transcriptPrintingEnabled:!0,menuItems:{imageUpload:!0,fileUpload:!0,shareLocation:!0},embedded:!1,setViewportScale:!1,browserStorage:\"localStorage\",fixedHeader:!1,displayStyle:\"button\",customColors:{brandColor:\"126af3\",conversationColor:\"126AF3\",actionColor:\"126AF3\"},carouselType:\"default\"},clearMessagesTimeout:3})}},window.loadFive9();");