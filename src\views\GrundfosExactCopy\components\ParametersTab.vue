<template>
  <div class="parameters-section">
    <div class="parameters-header">
      <div class="header-left">
        <h3>水泵参数配置</h3>
        <p class="header-subtitle">导入数据点进行曲线拟合，生成专业的水泵性能曲线</p>
      </div>
      <div class="header-actions">
        <button class="btn-action btn-template" @click="downloadTemplate">
          <i class="icon-download"></i>
          下载模板
        </button>
        <button class="btn-action btn-import" @click="triggerImport">
          <i class="icon-upload"></i>
          导入数据
        </button>
        <button class="btn-action btn-export" @click="exportData">
          <i class="icon-export"></i>
          导出数据
        </button>
        <button class="btn-action btn-primary" @click="generateCurves">
          <i class="icon-chart"></i>
          生成曲线
        </button>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      accept=".xlsx,.xls,.csv"
      @change="handleFileImport"
      style="display: none"
    />

    <div class="parameters-content">
      <!-- 数据快捷操作区 -->
      <QuickActions 
        @import-grundfos-data="importGrundfosData"
        @batch-add="batchAddDataPoints"
        @interpolate="interpolateDataPoints"
      />

      <!-- 性能曲线数据表 -->
      <DataTable 
        title="性能曲线数据点"
        :data="dataPoints"
        :columns="performanceColumns"
        @add-point="addDataPoint"
        @remove-point="removeDataPoint"
        @clear-points="clearDataPoints"
        @update-data="updateCurves"
      />

      <!-- NPSH数据表 -->
      <DataTable 
        title="NPSH数据点"
        :data="npshPoints"
        :columns="npshColumns"
        @add-point="addNPSHPoint"
        @remove-point="removeNPSHPoint"
        @clear-points="clearNPSHPoints"
        @update-data="updateCurves"
      />
      
      <!-- 数据可视化预览 -->
      <div class="data-preview-section" v-if="dataPoints.length > 0">
        <div class="section-header">
          <h4>数据点可视化预览</h4>
          <div class="section-actions">
            <el-radio-group v-model="previewType" size="small">
              <el-radio-button value="scatter">散点图</el-radio-button>
              <el-radio-button value="line">线图</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="preview-chart-container" ref="previewChartRef"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
// @ts-ignore
import Papa from 'papaparse'
import QuickActions from './QuickActions.vue'
import DataTable from './DataTable.vue'

// 定义Props
const props = defineProps<{
  dataPoints: any[]
  npshPoints: any[]
}>()

// 定义Emits
const emit = defineEmits<{
  (e: 'update-curves'): void
  (e: 'add-data-point'): void
  (e: 'remove-data-point', index: number): void
  (e: 'clear-data-points'): void
  (e: 'add-npsh-point'): void
  (e: 'remove-npsh-point', index: number): void
  (e: 'clear-npsh-points'): void
  (e: 'import-grundfos-data'): void
  (e: 'batch-add-data-points', count: number): void
  (e: 'interpolate-data-points'): void
  (e: 'download-template'): void
  (e: 'import-data', file: File): void
  (e: 'export-data'): void
  (e: 'generate-curves'): void
}>()

// 表格列定义
const performanceColumns = [
  { prop: 'flow', label: '流量 (m³/h)', width: 120, min: 0, precision: 2, step: 1 },
  { prop: 'head', label: '扬程 (m)', width: 120, min: 0, precision: 2, step: 0.5 },
  { prop: 'efficiency1', label: '单泵效率 (%)', width: 120, min: 0, max: 200, precision: 1, step: 0.5 },
  { prop: 'efficiency2', label: '单泵效率2 (%)', width: 120, min: 0, max: 200, precision: 1, step: 0.5 },
  { prop: 'powerP1', label: '功率P1 (kW)', width: 120, min: 0, precision: 2, step: 0.5 },
  { prop: 'powerP2', label: '功率P2 (kW)', width: 120, min: 0, precision: 2, step: 0.5 }
]

const npshColumns = [
  { prop: 'flow', label: '流量 (m³/h)', width: 180, min: 0, precision: 2, step: 1 },
  { prop: 'npsh', label: 'NPSH (m)', width: 180, min: 0, precision: 2, step: 0.1 }
]

// 预览图表相关
const previewType = ref('scatter')
const previewChartRef = ref<HTMLElement>()
let previewChart: ECharts | null = null
let chartResizeObserver: ResizeObserver | null = null

// 文件输入引用
const fileInput = ref<HTMLInputElement>()

// 方法
const addDataPoint = () => {
  emit('add-data-point')
}

const removeDataPoint = (index: number) => {
  emit('remove-data-point', index)
}

const clearDataPoints = () => {
  emit('clear-data-points')
}

const addNPSHPoint = () => {
  emit('add-npsh-point')
}

const removeNPSHPoint = (index: number) => {
  emit('remove-npsh-point', index)
}

const clearNPSHPoints = () => {
  emit('clear-npsh-points')
}

const importGrundfosData = () => {
  emit('import-grundfos-data')
}

const batchAddDataPoints = (count: number) => {
  emit('batch-add-data-points', count)
}

const interpolateDataPoints = () => {
  emit('interpolate-data-points')
}

const updateCurves = () => {
  emit('update-curves')
  updatePreviewChart()
}

const downloadTemplate = () => {
  emit('download-template')
}

const triggerImport = () => {
  fileInput.value?.click()
}

const handleFileImport = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    emit('import-data', target.files[0])
  }
}

const exportData = () => {
  emit('export-data')
}

const generateCurves = () => {
  emit('generate-curves')
}

// 初始化预览图表
const initPreviewChart = () => {
  // 确保容器已挂载且可见
  if (!previewChartRef.value) {
    console.warn('预览图表容器未挂载，跳过初始化')
    return
  }

  // 检查容器是否可见（父元素是否显示）
  const isVisible = previewChartRef.value.offsetParent !== null
  if (!isVisible) {
    console.log('📊 预览图表容器不可见，跳过初始化')
    return
  }

  // 检查是否已经初始化
  if (previewChart) {
    previewChart.dispose()
  }

  // 使用 requestAnimationFrame 确保DOM完全渲染
  requestAnimationFrame(() => {
    if (!previewChartRef.value) return

    // 强制设置容器尺寸，确保有明确的宽高
    previewChartRef.value.style.width = '100%'
    previewChartRef.value.style.height = '400px'
    previewChartRef.value.style.minWidth = '800px'
    previewChartRef.value.style.minHeight = '400px'

    // 再次使用 requestAnimationFrame 确保样式生效
    requestAnimationFrame(() => {
      if (!previewChartRef.value) return

      // 检查容器大小
      const containerWidth = previewChartRef.value.offsetWidth
      const containerHeight = previewChartRef.value.offsetHeight

      console.log(`📊 预览图表容器尺寸: ${containerWidth}x${containerHeight}`)

      if (containerWidth > 0 && containerHeight > 0) {
        try {
          // 初始化图表
          previewChart = echarts.init(previewChartRef.value)
          updatePreviewChart()

          // 设置尺寸监听器
          setupResizeObserver()

          console.log('✅ 预览图表初始化成功')
        } catch (error) {
          console.error('❌ 初始化预览图表失败:', error)
        }
      } else {
        console.warn('⚠️ 预览图表容器尺寸为0，等待可见时再初始化')
      }
    })
  })
}

// 当组件变为可见时初始化图表
const initChartWhenVisible = () => {
  if (previewChartRef.value && previewChartRef.value.offsetParent !== null && !previewChart) {
    initPreviewChart()
  }
}

// 监听容器尺寸变化
const setupResizeObserver = () => {
  if (typeof ResizeObserver !== 'undefined' && previewChartRef.value) {
    if (chartResizeObserver) {
      chartResizeObserver.disconnect()
    }
    
    chartResizeObserver = new ResizeObserver(() => {
      resizePreviewChart()
    })
    
    chartResizeObserver.observe(previewChartRef.value)
  }
}

// 更新预览图表
const updatePreviewChart = () => {
  if (!previewChart || props.dataPoints.length === 0) return

  // 准备数据
  const flowData = props.dataPoints.map(point => point.flow)
  const headData = props.dataPoints.map(point => point.head)
  const efficiencyData = props.dataPoints.map(point => point.efficiency1)
  const powerData = props.dataPoints.map(point => point.powerP1)
  const npshData = props.npshPoints.map(point => [point.flow, point.npsh])

  // 创建选项
  const option = {
    title: {
      text: '水泵性能数据预览',
      left: 'center',
      textStyle: {
        fontSize: 16
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['扬程', '效率', '功率', 'NPSH'],
      top: 30
    },
    grid: {
      left: 50,
      right: 50,
      bottom: 50,
      top: 80,
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '流量 (m³/h)',
      nameLocation: 'middle',
      nameGap: 30,
      min: 0,
      axisLabel: {
        formatter: '{value}'
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '扬程 (m)',
        position: 'left',
        nameLocation: 'middle',
        nameGap: 40,
        min: 0,
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '效率 (%)',
        position: 'right',
        nameLocation: 'middle',
        nameGap: 40,
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}'
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '扬程',
        type: previewType.value,
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#1f77b4'
        },
        data: flowData.map((flow, index) => [flow, headData[index]])
      },
      {
        name: '效率',
        type: previewType.value,
        yAxisIndex: 1,
        symbol: 'triangle',
        symbolSize: 8,
        itemStyle: {
          color: '#ff7f0e'
        },
        data: flowData.map((flow, index) => [flow, efficiencyData[index]])
      },
      {
        name: '功率',
        type: previewType.value,
        symbol: 'diamond',
        symbolSize: 8,
        itemStyle: {
          color: '#2ca02c'
        },
        data: flowData.map((flow, index) => [flow, powerData[index]])
      },
      {
        name: 'NPSH',
        type: previewType.value,
        symbol: 'rect',
        symbolSize: 8,
        itemStyle: {
          color: '#9467bd'
        },
        data: npshData
      }
    ]
  }

  // 设置图表选项
  previewChart.setOption(option, true)
}

// 调整图表大小
const resizePreviewChart = () => {
  previewChart?.resize()
}

// 监听预览类型变化
watch(previewType, () => {
  updatePreviewChart()
})

// 监听数据变化
watch(() => props.dataPoints, () => {
  updatePreviewChart()
}, { deep: true })

watch(() => props.npshPoints, () => {
  updatePreviewChart()
}, { deep: true })

// 延迟初始化图表，确保DOM已渲染
onMounted(() => {
  // 监听窗口尺寸变化
  window.addEventListener('resize', resizePreviewChart)

  // 使用 Intersection Observer 监听容器可见性
  if (typeof IntersectionObserver !== 'undefined' && previewChartRef.value) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !previewChart) {
          console.log('📊 预览图表容器变为可见，开始初始化')
          setTimeout(() => initPreviewChart(), 100)
        }
      })
    })

    // 延迟观察，确保DOM渲染完成
    setTimeout(() => {
      if (previewChartRef.value) {
        observer.observe(previewChartRef.value)
      }
    }, 500)
  } else {
    // 降级方案：延迟初始化
    setTimeout(() => {
      initChartWhenVisible()
    }, 1000)
  }
})

// 暴露初始化方法供父组件调用
defineExpose({
  initChartWhenVisible
})

// 组件卸载时清理资源
onUnmounted(() => {
  // 清理尺寸监听器
  if (chartResizeObserver) {
    chartResizeObserver.disconnect()
    chartResizeObserver = null
  }
  
  // 清理图表实例
  if (previewChart) {
    previewChart.dispose()
    previewChart = null
  }
  
  // 清理事件监听
  window.removeEventListener('resize', resizePreviewChart)
})
</script>

<style lang="scss" scoped>
.parameters-section {
  .parameters-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32px;
    padding: 24px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid rgba(255,255,255,0.2);

    .header-left h3 {
      margin: 0 0 8px 0;
      color: #2c3e50;
      font-size: 28px;
      font-weight: 700;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .header-subtitle {
      margin: 0;
      color: #6c757d;
      font-size: 14px;
      line-height: 1.5;
    }

    .header-actions {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;

      .btn-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        min-width: 120px;
        justify-content: center;

        &.btn-template {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
          }
        }

        &.btn-import {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          color: white;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 87, 108, 0.3);
          }
        }

        &.btn-export {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          color: white;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
          }
        }

        &.btn-primary {
          background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
          color: white;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(250, 112, 154, 0.3);
          }
        }
      }
    }
  }

  .parameters-content {
    display: flex;
    flex-direction: column;
    gap: 32px;
    
    .data-preview-section {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.08);
      border: 1px solid rgba(255,255,255,0.2);
      
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 2px solid #f8f9fa;

        h4 {
          margin: 0;
          color: #2c3e50;
          font-size: 20px;
          font-weight: 600;
        }
      }
      
      .preview-chart-container {
        height: 400px;
        width: 100%;
        margin-top: 20px;
      }
    }
  }
}
</style> 