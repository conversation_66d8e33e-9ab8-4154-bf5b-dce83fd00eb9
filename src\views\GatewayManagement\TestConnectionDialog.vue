<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useGatewayStore } from '../../stores/gateway';

const props = defineProps<{
  visible: boolean;
  deviceId: string | null;
}>();

const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

const gatewayStore = useGatewayStore();
const loading = ref(false);
const testResult = ref<{ success: boolean; message: string } | null>(null);
const statusMessages = ref<string[]>([]);
const autoClose = ref(false);
const autoCloseDelay = 3000; // 自动关闭延迟（毫秒）
let autoCloseTimeout: ReturnType<typeof setTimeout> | null = null;

// 运行连接测试
const runConnectionTest = async () => {
  if (!props.deviceId) {
    ElMessage.error('缺少设备ID');
    return;
  }
  
  loading.value = true;
  statusMessages.value = ['正在初始化测试...'];
  testResult.value = null;
  
  try {
    // 添加测试步骤消息
    addStatusMessage('正在获取设备信息...');
    await gatewayStore.fetchDeviceById(props.deviceId);
    
    addStatusMessage('正在获取MQTT配置...');
    const mqttConfig = await gatewayStore.fetchMqttConfig(props.deviceId);
    
    addStatusMessage('正在测试MQTT连接...');
    const result = await gatewayStore.testConnection(props.deviceId);
    testResult.value = result;
    
    addStatusMessage(result.success 
      ? `测试成功: ${result.message}` 
      : `测试失败: ${result.message}`);
    
    // 如果连接成功，尝试订阅主题
    if (result.success && mqttConfig) {
      addStatusMessage(`正在尝试订阅主题: ${mqttConfig.topics.join(', ')}`);
      
      // 模拟订阅过程（可以替换为实际的订阅逻辑）
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      addStatusMessage('主题订阅成功');
      addStatusMessage('测试完成');
    }
    
    // 如果设置了自动关闭且测试成功，启动自动关闭倒计时
    if (autoClose.value && result.success) {
      startAutoCloseTimer();
    }
  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : '未知错误';
    addStatusMessage(`测试过程中发生错误: ${errorMsg}`);
    testResult.value = { success: false, message: errorMsg };
  } finally {
    loading.value = false;
  }
};

// 添加状态消息
const addStatusMessage = (message: string) => {
  statusMessages.value.push(message);
  // 滚动到最新消息
  setTimeout(() => {
    const container = document.querySelector('.status-messages');
    if (container) {
      container.scrollTop = container.scrollHeight;
    }
  }, 100);
};

// 关闭对话框
const closeDialog = () => {
  emit('update:visible', false);
  clearAutoCloseTimer();
};

// 开始自动关闭计时器
const startAutoCloseTimer = () => {
  clearAutoCloseTimer(); // 先清除之前的计时器
  
  addStatusMessage(`${autoCloseDelay / 1000} 秒后自动关闭...`);
  
  autoCloseTimeout = setTimeout(() => {
    closeDialog();
  }, autoCloseDelay);
};

// 清除自动关闭计时器
const clearAutoCloseTimer = () => {
  if (autoCloseTimeout) {
    clearTimeout(autoCloseTimeout);
    autoCloseTimeout = null;
  }
};

// 监听可见性变化
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.deviceId) {
    statusMessages.value = [];
    testResult.value = null;
    runConnectionTest();
  } else {
    clearAutoCloseTimer();
  }
});

// 组件挂载时初始化
onMounted(() => {
  if (props.visible && props.deviceId) {
    runConnectionTest();
  }
});
</script>

<template>
  <el-dialog
    title="测试设备连接"
    v-model="visible"
    width="500px"
    @close="closeDialog"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <div class="test-connection-dialog" v-loading="loading">
      <!-- 测试状态消息 -->
      <div class="status-messages">
        <div 
          v-for="(message, index) in statusMessages" 
          :key="index" 
          class="status-message"
        >
          {{ message }}
        </div>
      </div>
      
      <!-- 测试结果 -->
      <div v-if="testResult" class="test-result">
        <el-alert
          :title="testResult.success ? '连接测试成功' : '连接测试失败'"
          :type="testResult.success ? 'success' : 'error'"
          :description="testResult.message"
          show-icon
        />
      </div>
      
      <!-- 自动关闭选项 -->
      <div class="auto-close-option">
        <el-checkbox v-model="autoClose">测试成功后自动关闭</el-checkbox>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="closeDialog">关闭</el-button>
      <el-button type="primary" @click="runConnectionTest" :loading="loading">重新测试</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.test-connection-dialog {
  .status-messages {
    height: 200px;
    overflow-y: auto;
    padding: 10px;
    background-color: #f9f9f9;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    margin-bottom: 15px;
    font-family: monospace;
    
    .status-message {
      margin-bottom: 5px;
      padding: 2px 0;
      line-height: 1.4;
      
      &:last-child {
        margin-bottom: 0;
        color: #409eff;
        font-weight: bold;
      }
    }
  }
  
  .test-result {
    margin-bottom: 15px;
  }
  
  .auto-close-option {
    margin-top: 15px;
    text-align: right;
  }
}
</style> 