import * as echarts from 'echarts'
import type { WarningStatistics } from '@/types'

interface ChartRefs {
  trendChart?: HTMLElement
  categoryChart?: HTMLElement
  healthChart?: HTMLElement
  resolutionChart?: HTMLElement
}

interface ChartData {
  statistics: WarningStatistics
  trendData: Array<{ hour: number; count: number }>
  healthDistribution: {
    excellent: number
    good: number
    fair: number
    poor: number
  }
}

export function initWarningCharts(refs: ChartRefs, data: ChartData) {
  const instances: any = {}
  
  // 今日预警趋势图
  if (refs.trendChart) {
    instances.trendChart = echarts.init(refs.trendChart)
    updateTrendChart(instances.trendChart, data.trendData)
  }
  
  // 预警类别分布图
  if (refs.categoryChart) {
    instances.categoryChart = echarts.init(refs.categoryChart)
    updateCategoryChart(instances.categoryChart, data.statistics)
  }
  
  // 设备健康度分布图
  if (refs.healthChart) {
    instances.healthChart = echarts.init(refs.healthChart)
    updateHealthChart(instances.healthChart, data.healthDistribution)
  }
  
  // 预警处理统计图
  if (refs.resolutionChart) {
    instances.resolutionChart = echarts.init(refs.resolutionChart)
    updateResolutionChart(instances.resolutionChart, data.statistics)
  }
  
  return instances
}

function updateTrendChart(chart: echarts.ECharts, trendData: Array<{ hour: number; count: number }>) {
  const hours = trendData.map(item => `${item.hour}:00`)
  const counts = trendData.map(item => item.count)
  
  const option = {
    title: {
      text: '24小时预警趋势',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        const param = params[0]
        return `${param.name}<br/>预警数量: ${param.value}条`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: hours,
      axisLabel: {
        interval: 3 // 每4小时显示一个标签
      }
    },
    yAxis: {
      type: 'value',
      name: '预警数量',
      nameLocation: 'middle',
      nameGap: 30
    },
    series: [{
      name: '预警数量',
      type: 'line',
      data: counts,
      smooth: true,
      lineStyle: {
        color: '#E6A23C',
        width: 3
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#E6A23C40' },
          { offset: 1, color: '#E6A23C10' }
        ])
      },
      symbol: 'circle',
      symbolSize: 6
    }]
  }
  
  chart.setOption(option)
}

function updateCategoryChart(chart: echarts.ECharts, statistics: WarningStatistics) {
  const categoryNames = {
    equipment_fault: '设备故障',
    performance_anomaly: '性能异常',
    maintenance_due: '维护到期',
    energy_consumption: '能耗异常',
    efficiency_drop: '效率下降',
    vibration_high: '振动过高',
    temperature_abnormal: '温度异常',
    pressure_abnormal: '压力异常',
    flow_abnormal: '流量异常',
    system_error: '系统错误'
  }
  
  const categoryData = Object.entries(statistics.byCategory)
    .filter(([_, count]) => count > 0)
    .map(([category, count]) => ({
      name: categoryNames[category as keyof typeof categoryNames] || category,
      value: count
    }))
    .sort((a, b) => b.value - a.value)
  
  const option = {
    title: {
      text: '预警类别分布',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      textStyle: { fontSize: 12 }
    },
    series: [{
      name: '预警类别',
      type: 'pie',
      radius: ['30%', '60%'],
      center: ['65%', '50%'],
      data: categoryData,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        formatter: '{b}\n{d}%',
        fontSize: 10
      }
    }]
  }
  
  chart.setOption(option)
}

function updateHealthChart(chart: echarts.ECharts, healthDistribution: any) {
  const data = [
    { name: '优秀(90-100)', value: healthDistribution.excellent, itemStyle: { color: '#67C23A' } },
    { name: '良好(80-89)', value: healthDistribution.good, itemStyle: { color: '#409EFF' } },
    { name: '一般(70-79)', value: healthDistribution.fair, itemStyle: { color: '#E6A23C' } },
    { name: '较差(<70)', value: healthDistribution.poor, itemStyle: { color: '#F56C6C' } }
  ]
  
  const option = {
    title: {
      text: '设备健康度分布',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}台 ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: '5%',
      textStyle: { fontSize: 12 }
    },
    series: [{
      name: '设备健康度',
      type: 'pie',
      radius: '60%',
      center: ['50%', '45%'],
      data: data,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        formatter: '{b}\n{c}台',
        fontSize: 10
      }
    }]
  }
  
  chart.setOption(option)
}

function updateResolutionChart(chart: echarts.ECharts, statistics: WarningStatistics) {
  const data = [
    {
      name: '已处理',
      value: statistics.resolved,
      itemStyle: { color: '#67C23A' }
    },
    {
      name: '未处理',
      value: statistics.unresolved,
      itemStyle: { color: '#F56C6C' }
    }
  ]
  
  const option = {
    title: {
      text: '预警处理状态',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params: any) {
        const total = statistics.total
        const percent = ((params.value / total) * 100).toFixed(1)
        return `${params.name}<br/>数量: ${params.value}条<br/>占比: ${percent}%`
      }
    },
    legend: {
      orient: 'horizontal',
      bottom: '5%',
      textStyle: { fontSize: 12 }
    },
    series: [{
      name: '处理状态',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '45%'],
      data: data,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        formatter: function(params: any) {
          const total = statistics.total
          const percent = ((params.value / total) * 100).toFixed(1)
          return `${params.name}\n${params.value}条\n${percent}%`
        },
        fontSize: 10
      }
    }]
  }
  
  // 添加中心文本
  const centerText = {
    graphic: {
      type: 'text',
      left: 'center',
      top: 'middle',
      style: {
        text: `总计\n${statistics.total}条`,
        textAlign: 'center',
        fill: '#333',
        fontSize: 16,
        fontWeight: 'bold'
      }
    }
  }
  
  Object.assign(option, centerText)
  chart.setOption(option)
}
