#!/usr/bin/env node

/**
 * 测试 MCP 工具的简单脚本
 * 这个脚本演示如何使用已安装的 MCP 服务器
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 开始测试 MCP 工具...\n');

// 测试已安装的 MCP 服务器
const mcpServers = [
  {
    name: 'Filesystem Server',
    package: '@modelcontextprotocol/server-filesystem',
    description: '文件系统操作服务器'
  },
  {
    name: 'Sequential Thinking Server', 
    package: '@modelcontextprotocol/server-sequential-thinking',
    description: '逻辑推理和思维链服务器'
  },
  {
    name: 'Brave Search Server',
    package: '@modelcontextprotocol/server-brave-search', 
    description: '搜索引擎集成服务器'
  }
];

console.log('📦 已安装的 MCP 服务器:');
mcpServers.forEach((server, index) => {
  console.log(`${index + 1}. ${server.name}`);
  console.log(`   包名: ${server.package}`);
  console.log(`   描述: ${server.description}\n`);
});

// 测试 MCP Inspector
console.log('🔍 测试 MCP Inspector...');
console.log('你可以使用以下命令启动 MCP Inspector:');
console.log('npx @modelcontextprotocol/inspector\n');

// 显示如何在 Claude Desktop 中配置这些服务器
console.log('⚙️  Claude Desktop 配置示例:');
console.log('将以下配置添加到你的 claude_desktop_config.json 文件中:\n');

const claudeConfig = {
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", process.cwd()]
    },
    "sequential-thinking": {
      "command": "npx", 
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]
    },
    "brave-search": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-brave-search"],
      "env": {
        "BRAVE_SEARCH_API_KEY": "你的_BRAVE_API_密钥"
      }
    }
  }
};

console.log(JSON.stringify(claudeConfig, null, 2));

console.log('\n✅ MCP 工具测试完成！');
console.log('\n📝 下一步:');
console.log('1. 在 Claude Desktop 中配置这些 MCP 服务器');
console.log('2. 重启 Claude Desktop');
console.log('3. 开始使用 MCP 功能！');
console.log('\n🎉 享受你的 MCP 之旅！');
