var st=Object.defineProperty,ot=Object.defineProperties;var nt=Object.getOwnPropertyDescriptors;var Le=Object.getOwnPropertySymbols;var ut=Object.prototype.hasOwnProperty,dt=Object.prototype.propertyIsEnumerable;var Pe=(B,s,w)=>s in B?st(B,s,{enumerable:!0,configurable:!0,writable:!0,value:w}):B[s]=w,W=(B,s)=>{for(var w in s||(s={}))ut.call(s,w)&&Pe(B,w,s[w]);if(Le)for(var w of Le(s))dt.call(s,w)&&Pe(B,w,s[w]);return B},we=(B,s)=>ot(B,nt(s));var R=(B,s,w)=>new Promise((_,r)=>{var v=Y=>{try{D(w.next(Y))}catch(z){r(z)}},m=Y=>{try{D(w.throw(Y))}catch(z){r(z)}},D=Y=>Y.done?_(Y.value):Promise.resolve(Y.value).then(v,m);D((w=w.apply(B,s)).next())});import{_ as G}from"./_plugin-vue_export-helper-C6jw_E77.js";/* empty css                *//* empty css                     *//* empty css                   */import{d as J,aW as Z,r as L,aX as te,b as P,L as S,e,w as t,aa as be,f as l,V as le,Y as ae,S as Q,ab as ye,am as re,ac as me,ad as pe,af as Ne,ag as Be,v as f,ax as $e,y as xe,n as X,h as k,E as K,aD as qe,ap as U,c as ue,U as it,o as O,aq as Te,aY as He,ay as Ye,x as $,aP as Oe,aZ as je,a_ as rt,a$ as Ae,aR as mt,a1 as Se,a2 as Ce,b0 as pt,a9 as _e,aQ as he,C as We,_ as se,au as oe,g as j,F as de,k as ie,av as _t,aw as ct,aA as ft,aU as Ge,b1 as vt,aO as Ue,aS as Me,b2 as Je,b3 as gt,ao as Ie,b4 as bt,b5 as Ke,Z as ke,N as yt,ae as Vt,b6 as wt,Q as kt,ah as Ze,b7 as Qe,aF as Xe,aG as et,aT as Fe,an as ze,b8 as Ut,a4 as $t,b9 as Ee,ba as xt,as as Re,O as St,j as Ct,M as Dt}from"./index-8zz4iTME.js";/* empty css                     *//* empty css                  *//* empty css                  *//* empty css                  *//* empty css                        *//* empty css               *//* empty css                    *//* empty css                 *//* empty css                   *//* empty css                   *//* empty css                    *//* empty css                  *//* empty css                             *//* empty css                 *//* empty css                       */const Et={class:"general-settings"},Tt={class:"card-header"},Yt={class:"form-actions"},At=J({__name:"GeneralSettings",setup(B){const s=Z(),w=L(),_=te({systemName:s.settings.general.systemName,companyName:s.settings.general.companyName,contactEmail:s.settings.general.contactEmail,contactPhone:s.settings.general.contactPhone,timezone:s.settings.general.timezone,dateFormat:s.settings.general.dateFormat,timeFormat:s.settings.general.timeFormat,currency:s.settings.general.currency,dataRetentionDays:s.settings.general.dataRetentionDays,autoRefreshInterval:s.settings.general.autoRefreshInterval,enableDebugMode:s.settings.general.enableDebugMode}),r=L(s.settings.theme.mode),v=L(s.settings.language.locale),m={systemName:[{required:!0,message:"请输入系统名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],companyName:[{required:!0,message:"请输入公司名称",trigger:"blur"}],contactEmail:[{required:!0,message:"请输入联系邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],contactPhone:[{required:!0,message:"请输入联系电话",trigger:"blur"}],timezone:[{required:!0,message:"请选择时区",trigger:"change"}],dateFormat:[{required:!0,message:"请选择日期格式",trigger:"change"}],timeFormat:[{required:!0,message:"请选择时间格式",trigger:"change"}],currency:[{required:!0,message:"请选择货币单位",trigger:"change"}]},D=()=>R(this,null,function*(){var c;if(yield(c=w.value)==null?void 0:c.validate().catch(()=>!1))try{yield s.updateSettings("general",_),yield s.updateSettings("theme",we(W({},s.settings.theme),{mode:r.value})),yield s.updateSettings("language",we(W({},s.settings.language),{locale:v.value})),U.success("设置保存成功")}catch(q){U.error("设置保存失败")}}),Y=()=>{Object.assign(_,s.settings.general),r.value=s.settings.theme.mode,v.value=s.settings.language.locale,U.info("已重置为默认设置")};return(z,c)=>{const q=K,C=re,n=ye,d=Q,g=ae,o=pe,y=me,i=Be,a=Ne,E=le,V=$e,x=xe,I=X,b=be;return S(),P("div",Et,[e(d,null,{header:t(()=>[l("div",Tt,[e(q,null,{default:t(()=>[e(k(qe))]),_:1}),c[13]||(c[13]=l("span",null,"通用设置",-1))])]),default:t(()=>[e(b,{model:_,rules:m,ref_key:"formRef",ref:w,"label-width":"120px"},{default:t(()=>[e(E,{gutter:20},{default:t(()=>[e(g,{span:12},{default:t(()=>[e(d,{class:"setting-section"},{header:t(()=>c[14]||(c[14]=[l("span",null,"基本信息",-1)])),default:t(()=>[e(n,{label:"系统名称",prop:"systemName"},{default:t(()=>[e(C,{modelValue:_.systemName,"onUpdate:modelValue":c[0]||(c[0]=u=>_.systemName=u),placeholder:"请输入系统名称"},null,8,["modelValue"])]),_:1}),e(n,{label:"公司名称",prop:"companyName"},{default:t(()=>[e(C,{modelValue:_.companyName,"onUpdate:modelValue":c[1]||(c[1]=u=>_.companyName=u),placeholder:"请输入公司名称"},null,8,["modelValue"])]),_:1}),e(n,{label:"联系邮箱",prop:"contactEmail"},{default:t(()=>[e(C,{modelValue:_.contactEmail,"onUpdate:modelValue":c[2]||(c[2]=u=>_.contactEmail=u),placeholder:"请输入联系邮箱"},null,8,["modelValue"])]),_:1}),e(n,{label:"联系电话",prop:"contactPhone"},{default:t(()=>[e(C,{modelValue:_.contactPhone,"onUpdate:modelValue":c[3]||(c[3]=u=>_.contactPhone=u),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(g,{span:12},{default:t(()=>[e(d,{class:"setting-section"},{header:t(()=>c[15]||(c[15]=[l("span",null,"区域设置",-1)])),default:t(()=>[e(n,{label:"时区",prop:"timezone"},{default:t(()=>[e(y,{modelValue:_.timezone,"onUpdate:modelValue":c[4]||(c[4]=u=>_.timezone=u),placeholder:"选择时区"},{default:t(()=>[e(o,{label:"北京时间 (UTC+8)",value:"Asia/Shanghai"}),e(o,{label:"东京时间 (UTC+9)",value:"Asia/Tokyo"}),e(o,{label:"纽约时间 (UTC-5)",value:"America/New_York"}),e(o,{label:"伦敦时间 (UTC+0)",value:"Europe/London"})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"日期格式",prop:"dateFormat"},{default:t(()=>[e(y,{modelValue:_.dateFormat,"onUpdate:modelValue":c[5]||(c[5]=u=>_.dateFormat=u),placeholder:"选择日期格式"},{default:t(()=>[e(o,{label:"YYYY-MM-DD",value:"YYYY-MM-DD"}),e(o,{label:"DD/MM/YYYY",value:"DD/MM/YYYY"}),e(o,{label:"MM/DD/YYYY",value:"MM/DD/YYYY"}),e(o,{label:"YYYY年MM月DD日",value:"YYYY年MM月DD日"})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"时间格式",prop:"timeFormat"},{default:t(()=>[e(a,{modelValue:_.timeFormat,"onUpdate:modelValue":c[6]||(c[6]=u=>_.timeFormat=u)},{default:t(()=>[e(i,{value:"24h"},{default:t(()=>c[16]||(c[16]=[f("24小时制")])),_:1,__:[16]}),e(i,{value:"12h"},{default:t(()=>c[17]||(c[17]=[f("12小时制")])),_:1,__:[17]})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"货币单位",prop:"currency"},{default:t(()=>[e(y,{modelValue:_.currency,"onUpdate:modelValue":c[7]||(c[7]=u=>_.currency=u),placeholder:"选择货币单位"},{default:t(()=>[e(o,{label:"人民币 (CNY)",value:"CNY"}),e(o,{label:"美元 (USD)",value:"USD"}),e(o,{label:"欧元 (EUR)",value:"EUR"}),e(o,{label:"日元 (JPY)",value:"JPY"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),e(E,{gutter:20},{default:t(()=>[e(g,{span:12},{default:t(()=>[e(d,{class:"setting-section"},{header:t(()=>c[18]||(c[18]=[l("span",null,"数据设置",-1)])),default:t(()=>[e(n,{label:"数据保留天数",prop:"dataRetentionDays"},{default:t(()=>[e(V,{modelValue:_.dataRetentionDays,"onUpdate:modelValue":c[8]||(c[8]=u=>_.dataRetentionDays=u),min:30,max:3650,placeholder:"数据保留天数"},null,8,["modelValue"]),c[19]||(c[19]=l("div",{class:"form-tip"},"系统将自动清理超过指定天数的历史数据",-1))]),_:1,__:[19]}),e(n,{label:"自动刷新间隔",prop:"autoRefreshInterval"},{default:t(()=>[e(V,{modelValue:_.autoRefreshInterval,"onUpdate:modelValue":c[9]||(c[9]=u=>_.autoRefreshInterval=u),min:10,max:300,placeholder:"刷新间隔(秒)"},null,8,["modelValue"]),c[20]||(c[20]=l("div",{class:"form-tip"},"页面数据自动刷新的时间间隔",-1))]),_:1,__:[20]})]),_:1})]),_:1}),e(g,{span:12},{default:t(()=>[e(d,{class:"setting-section"},{header:t(()=>c[21]||(c[21]=[l("span",null,"系统选项",-1)])),default:t(()=>[e(n,{label:"调试模式"},{default:t(()=>[e(x,{modelValue:_.enableDebugMode,"onUpdate:modelValue":c[10]||(c[10]=u=>_.enableDebugMode=u),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"]),c[22]||(c[22]=l("div",{class:"form-tip"},"开启后将显示详细的调试信息",-1))]),_:1,__:[22]}),e(n,{label:"主题设置"},{default:t(()=>[e(a,{modelValue:r.value,"onUpdate:modelValue":c[11]||(c[11]=u=>r.value=u)},{default:t(()=>[e(i,{value:"light"},{default:t(()=>c[23]||(c[23]=[f("浅色主题")])),_:1,__:[23]}),e(i,{value:"dark"},{default:t(()=>c[24]||(c[24]=[f("深色主题")])),_:1,__:[24]}),e(i,{value:"auto"},{default:t(()=>c[25]||(c[25]=[f("跟随系统")])),_:1,__:[25]})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"语言设置"},{default:t(()=>[e(y,{modelValue:v.value,"onUpdate:modelValue":c[12]||(c[12]=u=>v.value=u),placeholder:"选择语言"},{default:t(()=>[e(o,{label:"简体中文",value:"zh-CN"}),e(o,{label:"繁體中文",value:"zh-TW"}),e(o,{label:"English",value:"en-US"}),e(o,{label:"日本語",value:"ja-JP"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),l("div",Yt,[e(I,{onClick:Y},{default:t(()=>c[26]||(c[26]=[f("重置")])),_:1,__:[26]}),e(I,{type:"primary",onClick:D,loading:k(s).loading},{default:t(()=>c[27]||(c[27]=[f(" 保存设置 ")])),_:1,__:[27]},8,["loading"])])]),_:1},8,["model"])]),_:1})])}}}),Mt=G(At,[["__scopeId","data-v-4d796806"]]),It=["src"],Lt={class:"dialog-footer"},Pt=J({__name:"UserEditDialog",props:{modelValue:{type:Boolean},user:{}},emits:["update:modelValue","save"],setup(B,{emit:s}){const w=B,_=s,r=L(),v=L(!1),m=L({username:"",fullName:"",email:"",phone:"",role:"viewer",department:"",status:"active",avatar:""}),D={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"},{pattern:/^[a-zA-Z0-9_]+$/,message:"只能包含字母、数字和下划线",trigger:"blur"}],fullName:[{required:!0,message:"请输入姓名",trigger:"blur"},{min:2,max:20,message:"长度在 2 到 20 个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{required:!0,message:"请输入电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],role:[{required:!0,message:"请选择角色",trigger:"change"}],department:[{required:!0,message:"请选择部门",trigger:"change"}]},Y=ue({get:()=>w.modelValue,set:g=>_("update:modelValue",g)}),z=ue(()=>!!w.user),c=()=>R(this,null,function*(){var o;if(yield(o=r.value)==null?void 0:o.validate().catch(()=>!1)){v.value=!0;try{const y=we(W({},m.value),{permissions:[]});_("save",y)}finally{v.value=!1}}}),q=()=>{Y.value=!1,C()},C=()=>{m.value={username:"",fullName:"",email:"",phone:"",role:"viewer",department:"",status:"active",avatar:""}},n=g=>{const o=g.type==="image/jpeg"||g.type==="image/png",y=g.size/1024/1024<2;return o?y?!0:(U.error("头像大小不能超过 2MB!"),!1):(U.error("头像只能是 JPG/PNG 格式!"),!1)},d=g=>{const o=new FileReader;o.onload=y=>{var i;m.value.avatar=(i=y.target)==null?void 0:i.result},o.readAsDataURL(g.file)};return it(()=>w.user,g=>{g?m.value={username:g.username,fullName:g.fullName,email:g.email,phone:g.phone,role:g.role,department:g.department,status:g.status,avatar:g.avatar}:C()},{immediate:!0}),(g,o)=>{const y=re,i=ye,a=ae,E=le,V=pe,x=me,I=Be,b=Ne,u=K,M=He,H=be,N=X,T=Te;return S(),O(T,{modelValue:Y.value,"onUpdate:modelValue":o[7]||(o[7]=p=>Y.value=p),title:z.value?"编辑用户":"添加用户",width:"600px","before-close":q},{footer:t(()=>[l("div",Lt,[e(N,{onClick:q},{default:t(()=>o[11]||(o[11]=[f("取消")])),_:1,__:[11]}),e(N,{type:"primary",onClick:c,loading:v.value},{default:t(()=>[f($(z.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:t(()=>[e(H,{model:m.value,rules:D,ref_key:"formRef",ref:r,"label-width":"100px"},{default:t(()=>[e(E,{gutter:20},{default:t(()=>[e(a,{span:12},{default:t(()=>[e(i,{label:"用户名",prop:"username"},{default:t(()=>[e(y,{modelValue:m.value.username,"onUpdate:modelValue":o[0]||(o[0]=p=>m.value.username=p),placeholder:"请输入用户名",disabled:z.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(a,{span:12},{default:t(()=>[e(i,{label:"姓名",prop:"fullName"},{default:t(()=>[e(y,{modelValue:m.value.fullName,"onUpdate:modelValue":o[1]||(o[1]=p=>m.value.fullName=p),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(E,{gutter:20},{default:t(()=>[e(a,{span:12},{default:t(()=>[e(i,{label:"邮箱",prop:"email"},{default:t(()=>[e(y,{modelValue:m.value.email,"onUpdate:modelValue":o[2]||(o[2]=p=>m.value.email=p),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1})]),_:1}),e(a,{span:12},{default:t(()=>[e(i,{label:"电话",prop:"phone"},{default:t(()=>[e(y,{modelValue:m.value.phone,"onUpdate:modelValue":o[3]||(o[3]=p=>m.value.phone=p),placeholder:"请输入电话"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(E,{gutter:20},{default:t(()=>[e(a,{span:12},{default:t(()=>[e(i,{label:"角色",prop:"role"},{default:t(()=>[e(x,{modelValue:m.value.role,"onUpdate:modelValue":o[4]||(o[4]=p=>m.value.role=p),placeholder:"选择角色"},{default:t(()=>[e(V,{label:"管理员",value:"admin"}),e(V,{label:"操作员",value:"operator"}),e(V,{label:"维护人员",value:"maintenance"}),e(V,{label:"查看者",value:"viewer"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(a,{span:12},{default:t(()=>[e(i,{label:"部门",prop:"department"},{default:t(()=>[e(x,{modelValue:m.value.department,"onUpdate:modelValue":o[5]||(o[5]=p=>m.value.department=p),placeholder:"选择部门"},{default:t(()=>[e(V,{label:"信息技术部",value:"信息技术部"}),e(V,{label:"运行部",value:"运行部"}),e(V,{label:"维护部",value:"维护部"}),e(V,{label:"质量部",value:"质量部"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(i,{label:"状态",prop:"status"},{default:t(()=>[e(b,{modelValue:m.value.status,"onUpdate:modelValue":o[6]||(o[6]=p=>m.value.status=p)},{default:t(()=>[e(I,{value:"active"},{default:t(()=>o[8]||(o[8]=[f("活跃")])),_:1,__:[8]}),e(I,{value:"inactive"},{default:t(()=>o[9]||(o[9]=[f("非活跃")])),_:1,__:[9]}),e(I,{value:"locked"},{default:t(()=>o[10]||(o[10]=[f("锁定")])),_:1,__:[10]})]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"头像"},{default:t(()=>[e(M,{class:"avatar-uploader",action:"#","show-file-list":!1,"before-upload":n,"http-request":d},{default:t(()=>[m.value.avatar?(S(),P("img",{key:0,src:m.value.avatar,class:"avatar"},null,8,It)):(S(),O(u,{key:1,class:"avatar-uploader-icon"},{default:t(()=>[e(k(Ye))]),_:1}))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}}}),Ft=G(Pt,[["__scopeId","data-v-63841ed4"]]),zt={class:"user-management"},Rt={class:"card-header"},Nt={class:"header-actions"},Bt={class:"user-stats"},qt={class:"stat-card"},Ht={class:"stat-icon"},Ot={class:"stat-info"},jt={class:"stat-value"},ht={class:"stat-card"},Wt={class:"stat-icon"},Gt={class:"stat-info"},Jt={class:"stat-value"},Kt={class:"stat-card"},Zt={class:"stat-icon"},Qt={class:"stat-info"},Xt={class:"stat-value"},el={class:"stat-card"},tl={class:"stat-icon"},ll={class:"stat-info"},al={class:"stat-value"},sl={class:"user-filters"},ol=J({__name:"UserManagement",setup(B){const s=Z(),w=L({role:null,status:null,department:null}),_=ue(()=>{let g=[...s.users];return w.value.role&&(g=g.filter(o=>o.role===w.value.role)),w.value.status&&(g=g.filter(o=>o.status===w.value.status)),w.value.department&&(g=g.filter(o=>o.department===w.value.department)),g.sort((o,y)=>new Date(y.updatedAt).getTime()-new Date(o.updatedAt).getTime())}),r=g=>({admin:"danger",operator:"primary",maintenance:"warning",viewer:"info"})[g]||"info",v=g=>({admin:"管理员",operator:"操作员",maintenance:"维护人员",viewer:"查看者"})[g]||g,m=g=>({active:"success",inactive:"warning",locked:"danger"})[g]||"info",D=g=>({active:"活跃",inactive:"非活跃",locked:"锁定"})[g]||g,Y=g=>se(g).format("YYYY-MM-DD HH:mm"),z=()=>{s.showUserEditor()},c=g=>{s.showUserEditor(g)},q=g=>R(this,null,function*(){try{const o=yield s.toggleUserStatus(g);U.success(`用户已${o==="active"?"启用":"禁用"}`)}catch(o){U.error("操作失败")}}),C=g=>R(this,null,function*(){try{yield oe.confirm("确定要删除这个用户吗？删除后无法恢复。","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),(yield s.deleteUser(g))?U.success("用户删除成功"):U.error("用户删除失败")}catch(o){}}),n=g=>R(this,null,function*(){try{s.selectedUser?(yield s.updateUser(s.selectedUser.id,g),U.success("用户更新成功")):(yield s.createUser(g),U.success("用户创建成功")),s.hideUserEditor()}catch(o){U.error("操作失败")}}),d=()=>{w.value={role:null,status:null,department:null}};return(g,o)=>{const y=K,i=X,a=ae,E=le,V=pe,x=me,I=pt,b=Ce,u=_e,M=Se,H=Q,N=he;return S(),P("div",zt,[e(H,null,{header:t(()=>[l("div",Rt,[e(y,null,{default:t(()=>[e(k(We))]),_:1}),o[5]||(o[5]=l("span",null,"用户管理",-1)),l("div",Nt,[e(i,{type:"primary",onClick:z},{default:t(()=>[e(y,null,{default:t(()=>[e(k(Ye))]),_:1}),o[4]||(o[4]=f(" 添加用户 "))]),_:1,__:[4]})])])]),default:t(()=>[l("div",Bt,[e(E,{gutter:20},{default:t(()=>[e(a,{span:6},{default:t(()=>[l("div",qt,[l("div",Ht,[e(y,{size:"24",color:"#67C23A"},{default:t(()=>[e(k(je))]),_:1})]),l("div",Ot,[l("div",jt,$(k(s).getActiveUsersCount),1),o[6]||(o[6]=l("div",{class:"stat-label"},"活跃用户",-1))])])]),_:1}),e(a,{span:6},{default:t(()=>[l("div",ht,[l("div",Wt,[e(y,{size:"24",color:"#409EFF"},{default:t(()=>[e(k(rt))]),_:1})]),l("div",Gt,[l("div",Jt,$(k(s).users.length),1),o[7]||(o[7]=l("div",{class:"stat-label"},"总用户数",-1))])])]),_:1}),e(a,{span:6},{default:t(()=>[l("div",Kt,[l("div",Zt,[e(y,{size:"24",color:"#E6A23C"},{default:t(()=>[e(k(Ae))]),_:1})]),l("div",Qt,[l("div",Xt,$(k(s).getRoleStats.admin),1),o[8]||(o[8]=l("div",{class:"stat-label"},"管理员",-1))])])]),_:1}),e(a,{span:6},{default:t(()=>[l("div",el,[l("div",tl,[e(y,{size:"24",color:"#F56C6C"},{default:t(()=>[e(k(mt))]),_:1})]),l("div",ll,[l("div",al,$(k(s).getRoleStats.operator),1),o[9]||(o[9]=l("div",{class:"stat-label"},"操作员",-1))])])]),_:1})]),_:1})]),l("div",sl,[e(E,{gutter:16},{default:t(()=>[e(a,{span:6},{default:t(()=>[e(x,{modelValue:w.value.role,"onUpdate:modelValue":o[0]||(o[0]=T=>w.value.role=T),placeholder:"选择角色",clearable:""},{default:t(()=>[e(V,{label:"管理员",value:"admin"}),e(V,{label:"操作员",value:"operator"}),e(V,{label:"维护人员",value:"maintenance"}),e(V,{label:"查看者",value:"viewer"})]),_:1},8,["modelValue"])]),_:1}),e(a,{span:6},{default:t(()=>[e(x,{modelValue:w.value.status,"onUpdate:modelValue":o[1]||(o[1]=T=>w.value.status=T),placeholder:"选择状态",clearable:""},{default:t(()=>[e(V,{label:"活跃",value:"active"}),e(V,{label:"非活跃",value:"inactive"}),e(V,{label:"锁定",value:"locked"})]),_:1},8,["modelValue"])]),_:1}),e(a,{span:6},{default:t(()=>[e(x,{modelValue:w.value.department,"onUpdate:modelValue":o[2]||(o[2]=T=>w.value.department=T),placeholder:"选择部门",clearable:""},{default:t(()=>[e(V,{label:"信息技术部",value:"信息技术部"}),e(V,{label:"运行部",value:"运行部"}),e(V,{label:"维护部",value:"维护部"}),e(V,{label:"质量部",value:"质量部"})]),_:1},8,["modelValue"])]),_:1}),e(a,{span:6},{default:t(()=>[e(i,{onClick:d},{default:t(()=>o[10]||(o[10]=[f("清除筛选")])),_:1,__:[10]})]),_:1})]),_:1})]),Oe((S(),O(M,{data:_.value,stripe:""},{default:t(()=>[e(b,{prop:"avatar",label:"头像",width:"80"},{default:t(({row:T})=>[e(I,{src:T.avatar,alt:T.fullName},{default:t(()=>[f($(T.fullName.charAt(0)),1)]),_:2},1032,["src","alt"])]),_:1}),e(b,{prop:"username",label:"用户名",width:"120"}),e(b,{prop:"fullName",label:"姓名",width:"120"}),e(b,{prop:"email",label:"邮箱","min-width":"180"}),e(b,{prop:"role",label:"角色",width:"100"},{default:t(({row:T})=>[e(u,{type:r(T.role)},{default:t(()=>[f($(v(T.role)),1)]),_:2},1032,["type"])]),_:1}),e(b,{prop:"department",label:"部门",width:"120"}),e(b,{prop:"status",label:"状态",width:"80"},{default:t(({row:T})=>[e(u,{type:m(T.status),size:"small"},{default:t(()=>[f($(D(T.status)),1)]),_:2},1032,["type"])]),_:1}),e(b,{prop:"lastLogin",label:"最后登录",width:"150"},{default:t(({row:T})=>[f($(Y(T.lastLogin)),1)]),_:1}),e(b,{label:"操作",width:"200"},{default:t(({row:T})=>[e(i,{size:"small",onClick:p=>c(T)},{default:t(()=>o[11]||(o[11]=[f(" 编辑 ")])),_:2,__:[11]},1032,["onClick"]),e(i,{size:"small",type:T.status==="active"?"warning":"success",onClick:p=>q(T.id)},{default:t(()=>[f($(T.status==="active"?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),e(i,{size:"small",type:"danger",onClick:p=>C(T.id),disabled:T.role==="admin"},{default:t(()=>o[12]||(o[12]=[f(" 删除 ")])),_:2,__:[12]},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[N,k(s).loading]])]),_:1}),e(Ft,{modelValue:k(s).showUserDialog,"onUpdate:modelValue":o[3]||(o[3]=T=>k(s).showUserDialog=T),user:k(s).selectedUser,onSave:n},null,8,["modelValue","user"])])}}}),nl=G(ol,[["__scopeId","data-v-87b9ebe4"]]),ul={class:"role-management"},dl={class:"card-header"},il={class:"header-actions"},rl={key:0,class:"permissions-detail"},ml={class:"permissions-grid"},pl={class:"permission-items"},_l=J({__name:"RoleManagement",setup(B){const s=Z(),w=L(!1),_=L(null),r=ue(()=>{const C=new Set(s.permissions.map(n=>n.category));return Array.from(C)}),v=C=>se(C).format("YYYY-MM-DD HH:mm"),m=()=>{s.showRoleEditor()},D=C=>{s.showRoleEditor(C)},Y=C=>{_.value=C,w.value=!0},z=C=>s.permissions.filter(n=>n.category===C),c=C=>{var n;return((n=_.value)==null?void 0:n.permissions.includes(C))||!1},q=C=>R(this,null,function*(){try{yield oe.confirm("确定要删除这个角色吗？删除后无法恢复。","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),(yield s.deleteRole(C))?U.success("角色删除成功"):U.error("无法删除系统角色")}catch(n){}});return(C,n)=>{const d=K,g=X,o=Ce,y=_e,i=Se,a=Q,E=Te;return S(),P("div",ul,[e(a,null,{header:t(()=>[l("div",dl,[e(d,null,{default:t(()=>[e(k(Ae))]),_:1}),n[2]||(n[2]=l("span",null,"角色权限管理",-1)),l("div",il,[e(g,{type:"primary",onClick:m},{default:t(()=>[e(d,null,{default:t(()=>[e(k(Ye))]),_:1}),n[1]||(n[1]=f(" 添加角色 "))]),_:1,__:[1]})])])]),default:t(()=>[e(i,{data:k(s).roles,stripe:""},{default:t(()=>[e(o,{prop:"name",label:"角色名称",width:"150"}),e(o,{prop:"description",label:"描述","min-width":"200"}),e(o,{prop:"permissions",label:"权限数量",width:"100"},{default:t(({row:V})=>[e(y,{type:"info"},{default:t(()=>[f($(V.permissions.length)+"个权限",1)]),_:2},1024)]),_:1}),e(o,{prop:"isSystem",label:"类型",width:"100"},{default:t(({row:V})=>[e(y,{type:V.isSystem?"warning":"success"},{default:t(()=>[f($(V.isSystem?"系统角色":"自定义"),1)]),_:2},1032,["type"])]),_:1}),e(o,{prop:"updatedAt",label:"更新时间",width:"150"},{default:t(({row:V})=>[f($(v(V.updatedAt)),1)]),_:1}),e(o,{label:"操作",width:"200"},{default:t(({row:V})=>[e(g,{size:"small",onClick:x=>Y(V)},{default:t(()=>n[3]||(n[3]=[f(" 查看权限 ")])),_:2,__:[3]},1032,["onClick"]),e(g,{size:"small",onClick:x=>D(V),disabled:V.isSystem},{default:t(()=>n[4]||(n[4]=[f(" 编辑 ")])),_:2,__:[4]},1032,["onClick","disabled"]),e(g,{size:"small",type:"danger",onClick:x=>q(V.id),disabled:V.isSystem},{default:t(()=>n[5]||(n[5]=[f(" 删除 ")])),_:2,__:[5]},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])]),_:1}),e(E,{modelValue:w.value,"onUpdate:modelValue":n[0]||(n[0]=V=>w.value=V),title:"角色权限详情",width:"800px"},{default:t(()=>[_.value?(S(),P("div",rl,[l("h3",null,$(_.value.name)+" - 权限列表",1),l("div",ml,[(S(!0),P(de,null,ie(r.value,V=>(S(),P("div",{key:V,class:"permission-category"},[l("h4",null,$(V),1),l("div",pl,[(S(!0),P(de,null,ie(z(V),x=>(S(),O(y,{key:x.id,type:c(x.id)?"success":"info",class:"permission-tag"},{default:t(()=>[f($(x.name),1)]),_:2},1032,["type"]))),128))])]))),128))])])):j("",!0)]),_:1},8,["modelValue"])])}}}),cl=G(_l,[["__scopeId","data-v-30138457"]]),fl={class:"notification-settings"},vl={class:"card-header"},gl={class:"form-label"},bl={key:0},yl={class:"form-label"},Vl={key:0},wl={class:"form-label"},kl={key:0},Ul={class:"form-label"},$l={key:0},xl={class:"form-actions"},Sl=J({__name:"NotificationSettings",setup(B){const s=Z(),w=L("email"),_=L(!1),r=te(W({},s.settings.notification.email)),v=te(W({},s.settings.notification.sms)),m=te(W({},s.settings.notification.push)),D=te(W({},s.settings.notification.sound)),Y=()=>R(this,null,function*(){_.value=!0;try{yield new Promise(n=>setTimeout(n,2e3)),U.success("测试邮件发送成功")}catch(n){U.error("测试邮件发送失败")}finally{_.value=!1}}),z=()=>R(this,null,function*(){_.value=!0;try{yield new Promise(n=>setTimeout(n,2e3)),U.success("测试短信发送成功")}catch(n){U.error("测试短信发送失败")}finally{_.value=!1}}),c=n=>{U.info(`播放${n}提示音`)},q=()=>R(this,null,function*(){try{yield s.updateSettings("notification",{email:r,sms:v,push:m,sound:D}),U.success("通知设置保存成功")}catch(n){U.error("通知设置保存失败")}}),C=()=>{Object.assign(r,s.settings.notification.email),Object.assign(v,s.settings.notification.sms),Object.assign(m,s.settings.notification.push),Object.assign(D,s.settings.notification.sound),U.info("已重置为默认设置")};return(n,d)=>{const g=K,o=xe,y=ye,i=re,a=ae,E=$e,V=le,x=pe,I=me,b=X,u=be,M=ct,H=ft,N=_t,T=Q;return S(),P("div",fl,[e(T,null,{header:t(()=>[l("div",vl,[e(g,null,{default:t(()=>[e(k(Ge))]),_:1}),d[27]||(d[27]=l("span",null,"通知设置",-1))])]),default:t(()=>[e(N,{modelValue:w.value,"onUpdate:modelValue":d[26]||(d[26]=p=>w.value=p)},{default:t(()=>[e(M,{label:"邮件通知",name:"email"},{default:t(()=>[e(u,{model:r,"label-width":"120px"},{default:t(()=>[e(y,null,{label:t(()=>[l("div",gl,[d[28]||(d[28]=l("span",null,"启用邮件通知",-1)),e(o,{modelValue:r.enabled,"onUpdate:modelValue":d[0]||(d[0]=p=>r.enabled=p)},null,8,["modelValue"])])]),_:1}),r.enabled?(S(),P("div",bl,[e(V,{gutter:20},{default:t(()=>[e(a,{span:12},{default:t(()=>[e(y,{label:"SMTP服务器"},{default:t(()=>[e(i,{modelValue:r.smtpServer,"onUpdate:modelValue":d[1]||(d[1]=p=>r.smtpServer=p),placeholder:"smtp.example.com"},null,8,["modelValue"])]),_:1})]),_:1}),e(a,{span:12},{default:t(()=>[e(y,{label:"端口"},{default:t(()=>[e(E,{modelValue:r.smtpPort,"onUpdate:modelValue":d[2]||(d[2]=p=>r.smtpPort=p),min:1,max:65535},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:20},{default:t(()=>[e(a,{span:12},{default:t(()=>[e(y,{label:"安全类型"},{default:t(()=>[e(I,{modelValue:r.smtpSecurity,"onUpdate:modelValue":d[3]||(d[3]=p=>r.smtpSecurity=p)},{default:t(()=>[e(x,{label:"无",value:"none"}),e(x,{label:"SSL",value:"ssl"}),e(x,{label:"TLS",value:"tls"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(a,{span:12},{default:t(()=>[e(y,{label:"发件人名称"},{default:t(()=>[e(i,{modelValue:r.fromName,"onUpdate:modelValue":d[4]||(d[4]=p=>r.fromName=p),placeholder:"智慧水务平台"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:20},{default:t(()=>[e(a,{span:12},{default:t(()=>[e(y,{label:"用户名"},{default:t(()=>[e(i,{modelValue:r.username,"onUpdate:modelValue":d[5]||(d[5]=p=>r.username=p),placeholder:"邮箱用户名"},null,8,["modelValue"])]),_:1})]),_:1}),e(a,{span:12},{default:t(()=>[e(y,{label:"密码"},{default:t(()=>[e(i,{modelValue:r.password,"onUpdate:modelValue":d[6]||(d[6]=p=>r.password=p),type:"password",placeholder:"邮箱密码"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(y,{label:"发件人地址"},{default:t(()=>[e(i,{modelValue:r.fromAddress,"onUpdate:modelValue":d[7]||(d[7]=p=>r.fromAddress=p),placeholder:"<EMAIL>"},null,8,["modelValue"])]),_:1}),e(y,{label:"默认收件人"},{default:t(()=>[e(I,{modelValue:r.recipients,"onUpdate:modelValue":d[8]||(d[8]=p=>r.recipients=p),multiple:"",placeholder:"选择或输入收件人"},{default:t(()=>[(S(!0),P(de,null,ie(k(s).users,p=>(S(),O(x,{key:p.id,label:p.email,value:p.email},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(y,null,{default:t(()=>[e(b,{onClick:Y,loading:_.value},{default:t(()=>d[29]||(d[29]=[f("测试邮件发送")])),_:1,__:[29]},8,["loading"])]),_:1})])):j("",!0)]),_:1},8,["model"])]),_:1}),e(M,{label:"短信通知",name:"sms"},{default:t(()=>[e(u,{model:v,"label-width":"120px"},{default:t(()=>[e(y,null,{label:t(()=>[l("div",yl,[d[30]||(d[30]=l("span",null,"启用短信通知",-1)),e(o,{modelValue:v.enabled,"onUpdate:modelValue":d[9]||(d[9]=p=>v.enabled=p)},null,8,["modelValue"])])]),_:1}),v.enabled?(S(),P("div",Vl,[e(y,{label:"服务提供商"},{default:t(()=>[e(I,{modelValue:v.provider,"onUpdate:modelValue":d[10]||(d[10]=p=>v.provider=p)},{default:t(()=>[e(x,{label:"阿里云",value:"aliyun"}),e(x,{label:"腾讯云",value:"tencent"}),e(x,{label:"华为云",value:"huawei"})]),_:1},8,["modelValue"])]),_:1}),e(V,{gutter:20},{default:t(()=>[e(a,{span:12},{default:t(()=>[e(y,{label:"API Key"},{default:t(()=>[e(i,{modelValue:v.apiKey,"onUpdate:modelValue":d[11]||(d[11]=p=>v.apiKey=p),placeholder:"API密钥"},null,8,["modelValue"])]),_:1})]),_:1}),e(a,{span:12},{default:t(()=>[e(y,{label:"API Secret"},{default:t(()=>[e(i,{modelValue:v.apiSecret,"onUpdate:modelValue":d[12]||(d[12]=p=>v.apiSecret=p),type:"password",placeholder:"API密钥"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(y,{label:"默认收件人"},{default:t(()=>[e(I,{modelValue:v.defaultRecipients,"onUpdate:modelValue":d[13]||(d[13]=p=>v.defaultRecipients=p),multiple:"",placeholder:"输入手机号"},{default:t(()=>[(S(!0),P(de,null,ie(k(s).users,p=>(S(),O(x,{key:p.id,label:p.phone,value:p.phone},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(y,null,{default:t(()=>[e(b,{onClick:z,loading:_.value},{default:t(()=>d[31]||(d[31]=[f("测试短信发送")])),_:1,__:[31]},8,["loading"])]),_:1})])):j("",!0)]),_:1},8,["model"])]),_:1}),e(M,{label:"推送通知",name:"push"},{default:t(()=>[e(u,{model:m,"label-width":"120px"},{default:t(()=>[e(y,null,{label:t(()=>[l("div",wl,[d[32]||(d[32]=l("span",null,"启用推送通知",-1)),e(o,{modelValue:m.enabled,"onUpdate:modelValue":d[14]||(d[14]=p=>m.enabled=p)},null,8,["modelValue"])])]),_:1}),m.enabled?(S(),P("div",kl,[e(y,{label:"Web推送"},{default:t(()=>[e(o,{modelValue:m.webPush,"onUpdate:modelValue":d[15]||(d[15]=p=>m.webPush=p)},null,8,["modelValue"]),d[33]||(d[33]=l("div",{class:"form-tip"},"在浏览器中显示桌面通知",-1))]),_:1,__:[33]}),e(y,{label:"移动端推送"},{default:t(()=>[e(o,{modelValue:m.mobile,"onUpdate:modelValue":d[16]||(d[16]=p=>m.mobile=p)},null,8,["modelValue"]),d[34]||(d[34]=l("div",{class:"form-tip"},"向移动应用发送推送通知",-1))]),_:1,__:[34]}),e(y,{label:"桌面推送"},{default:t(()=>[e(o,{modelValue:m.desktop,"onUpdate:modelValue":d[17]||(d[17]=p=>m.desktop=p)},null,8,["modelValue"]),d[35]||(d[35]=l("div",{class:"form-tip"},"向桌面应用发送推送通知",-1))]),_:1,__:[35]})])):j("",!0)]),_:1},8,["model"])]),_:1}),e(M,{label:"声音设置",name:"sound"},{default:t(()=>[e(u,{model:D,"label-width":"120px"},{default:t(()=>[e(y,null,{label:t(()=>[l("div",Ul,[d[36]||(d[36]=l("span",null,"启用声音提醒",-1)),e(o,{modelValue:D.enabled,"onUpdate:modelValue":d[18]||(d[18]=p=>D.enabled=p)},null,8,["modelValue"])])]),_:1}),D.enabled?(S(),P("div",$l,[e(y,{label:"音量"},{default:t(()=>[e(H,{modelValue:D.volume,"onUpdate:modelValue":d[19]||(d[19]=p=>D.volume=p),max:100,"show-input":""},null,8,["modelValue"])]),_:1}),e(y,{label:"严重警报音"},{default:t(()=>[e(I,{modelValue:D.criticalAlertSound,"onUpdate:modelValue":d[20]||(d[20]=p=>D.criticalAlertSound=p)},{default:t(()=>[e(x,{label:"警报声1",value:"critical1.mp3"}),e(x,{label:"警报声2",value:"critical2.mp3"}),e(x,{label:"警报声3",value:"critical3.mp3"})]),_:1},8,["modelValue"]),e(b,{onClick:d[21]||(d[21]=p=>c("critical")),style:{"margin-left":"10px"}},{default:t(()=>d[37]||(d[37]=[f("试听")])),_:1,__:[37]})]),_:1}),e(y,{label:"警告提示音"},{default:t(()=>[e(I,{modelValue:D.warningAlertSound,"onUpdate:modelValue":d[22]||(d[22]=p=>D.warningAlertSound=p)},{default:t(()=>[e(x,{label:"提示音1",value:"warning1.mp3"}),e(x,{label:"提示音2",value:"warning2.mp3"}),e(x,{label:"提示音3",value:"warning3.mp3"})]),_:1},8,["modelValue"]),e(b,{onClick:d[23]||(d[23]=p=>c("warning")),style:{"margin-left":"10px"}},{default:t(()=>d[38]||(d[38]=[f("试听")])),_:1,__:[38]})]),_:1}),e(y,{label:"信息提示音"},{default:t(()=>[e(I,{modelValue:D.infoAlertSound,"onUpdate:modelValue":d[24]||(d[24]=p=>D.infoAlertSound=p)},{default:t(()=>[e(x,{label:"轻提示1",value:"info1.mp3"}),e(x,{label:"轻提示2",value:"info2.mp3"}),e(x,{label:"轻提示3",value:"info3.mp3"})]),_:1},8,["modelValue"]),e(b,{onClick:d[25]||(d[25]=p=>c("info")),style:{"margin-left":"10px"}},{default:t(()=>d[39]||(d[39]=[f("试听")])),_:1,__:[39]})]),_:1})])):j("",!0)]),_:1},8,["model"])]),_:1})]),_:1},8,["modelValue"]),l("div",xl,[e(b,{onClick:C},{default:t(()=>d[40]||(d[40]=[f("重置")])),_:1,__:[40]}),e(b,{type:"primary",onClick:q,loading:k(s).loading},{default:t(()=>d[41]||(d[41]=[f(" 保存设置 ")])),_:1,__:[41]},8,["loading"])])]),_:1})])}}}),Cl=G(Sl,[["__scopeId","data-v-56d793dc"]]),Dl={class:"security-settings"},El={class:"card-header"},Tl={class:"ip-whitelist"},Yl={class:"ip-list"},Al={class:"add-ip"},Ml={class:"security-status"},Il={class:"status-item"},Ll={class:"status-icon success"},Pl={class:"status-item"},Fl={class:"status-icon warning"},zl={class:"status-item"},Rl={class:"status-icon success"},Nl={class:"form-actions"},Bl=J({__name:"SecuritySettings",setup(B){const s=Z(),w=L(!1),_=L(""),r=te(W({},s.settings.security.passwordPolicy)),v=te({sessionTimeout:s.settings.security.sessionTimeout,maxLoginAttempts:s.settings.security.maxLoginAttempts,lockoutDuration:s.settings.security.lockoutDuration,twoFactorAuth:s.settings.security.twoFactorAuth,auditLog:s.settings.security.auditLog,encryptionEnabled:s.settings.security.encryptionEnabled}),m=L([...s.settings.security.ipWhitelist]),D=()=>{if(!_.value)return;if(!/^(\d{1,3}\.){3}\d{1,3}(\/\d{1,2})?$/.test(_.value)){U.error("请输入正确的IP地址格式");return}if(m.value.includes(_.value)){U.warning("IP地址已存在");return}m.value.push(_.value),_.value="",U.success("IP地址添加成功")},Y=C=>{m.value.splice(C,1),U.success("IP地址删除成功")},z=()=>R(this,null,function*(){w.value=!0;try{yield new Promise(C=>setTimeout(C,3e3)),U.success("安全扫描完成，未发现安全风险")}catch(C){U.error("安全扫描失败")}finally{w.value=!1}}),c=()=>R(this,null,function*(){try{yield s.updateSettings("security",{passwordPolicy:r,sessionTimeout:v.sessionTimeout,maxLoginAttempts:v.maxLoginAttempts,lockoutDuration:v.lockoutDuration,twoFactorAuth:v.twoFactorAuth,ipWhitelist:m.value,auditLog:v.auditLog,encryptionEnabled:v.encryptionEnabled}),U.success("安全设置保存成功")}catch(C){U.error("安全设置保存失败")}}),q=()=>{Object.assign(r,s.settings.security.passwordPolicy),Object.assign(v,{sessionTimeout:s.settings.security.sessionTimeout,maxLoginAttempts:s.settings.security.maxLoginAttempts,lockoutDuration:s.settings.security.lockoutDuration,twoFactorAuth:s.settings.security.twoFactorAuth,auditLog:s.settings.security.auditLog,encryptionEnabled:s.settings.security.encryptionEnabled}),m.value=[...s.settings.security.ipWhitelist],U.info("已重置为默认设置")};return(C,n)=>{const d=K,g=$e,o=ye,y=xe,i=be,a=Q,E=ae,V=le,x=_e,I=re,b=X;return S(),P("div",Dl,[e(a,null,{header:t(()=>[l("div",El,[e(d,null,{default:t(()=>[e(k(Je))]),_:1}),n[14]||(n[14]=l("span",null,"安全设置",-1))])]),default:t(()=>[e(V,{gutter:20},{default:t(()=>[e(E,{span:12},{default:t(()=>[e(a,{class:"setting-section"},{header:t(()=>n[15]||(n[15]=[l("span",null,"密码策略",-1)])),default:t(()=>[e(i,{model:r,"label-width":"140px"},{default:t(()=>[e(o,{label:"最小长度"},{default:t(()=>[e(g,{modelValue:r.minLength,"onUpdate:modelValue":n[0]||(n[0]=u=>r.minLength=u),min:6,max:32},null,8,["modelValue"])]),_:1}),e(o,{label:"必须包含大写字母"},{default:t(()=>[e(y,{modelValue:r.requireUppercase,"onUpdate:modelValue":n[1]||(n[1]=u=>r.requireUppercase=u)},null,8,["modelValue"])]),_:1}),e(o,{label:"必须包含小写字母"},{default:t(()=>[e(y,{modelValue:r.requireLowercase,"onUpdate:modelValue":n[2]||(n[2]=u=>r.requireLowercase=u)},null,8,["modelValue"])]),_:1}),e(o,{label:"必须包含数字"},{default:t(()=>[e(y,{modelValue:r.requireNumbers,"onUpdate:modelValue":n[3]||(n[3]=u=>r.requireNumbers=u)},null,8,["modelValue"])]),_:1}),e(o,{label:"必须包含特殊字符"},{default:t(()=>[e(y,{modelValue:r.requireSpecialChars,"onUpdate:modelValue":n[4]||(n[4]=u=>r.requireSpecialChars=u)},null,8,["modelValue"])]),_:1}),e(o,{label:"密码有效期(天)"},{default:t(()=>[e(g,{modelValue:r.passwordExpiry,"onUpdate:modelValue":n[5]||(n[5]=u=>r.passwordExpiry=u),min:0,max:365},null,8,["modelValue"]),n[16]||(n[16]=l("div",{class:"form-tip"},"0表示永不过期",-1))]),_:1,__:[16]}),e(o,{label:"禁止重复使用"},{default:t(()=>[e(g,{modelValue:r.preventReuse,"onUpdate:modelValue":n[6]||(n[6]=u=>r.preventReuse=u),min:0,max:10},null,8,["modelValue"]),n[17]||(n[17]=l("div",{class:"form-tip"},"最近几次使用过的密码",-1))]),_:1,__:[17]})]),_:1},8,["model"])]),_:1})]),_:1}),e(E,{span:12},{default:t(()=>[e(a,{class:"setting-section"},{header:t(()=>n[18]||(n[18]=[l("span",null,"会话安全",-1)])),default:t(()=>[e(i,{model:v,"label-width":"140px"},{default:t(()=>[e(o,{label:"会话超时(分钟)"},{default:t(()=>[e(g,{modelValue:v.sessionTimeout,"onUpdate:modelValue":n[7]||(n[7]=u=>v.sessionTimeout=u),min:30,max:1440},null,8,["modelValue"]),n[19]||(n[19]=l("div",{class:"form-tip"},"用户无操作自动退出时间",-1))]),_:1,__:[19]}),e(o,{label:"最大登录尝试"},{default:t(()=>[e(g,{modelValue:v.maxLoginAttempts,"onUpdate:modelValue":n[8]||(n[8]=u=>v.maxLoginAttempts=u),min:3,max:10},null,8,["modelValue"]),n[20]||(n[20]=l("div",{class:"form-tip"},"连续登录失败次数限制",-1))]),_:1,__:[20]}),e(o,{label:"锁定时长(分钟)"},{default:t(()=>[e(g,{modelValue:v.lockoutDuration,"onUpdate:modelValue":n[9]||(n[9]=u=>v.lockoutDuration=u),min:5,max:120},null,8,["modelValue"]),n[21]||(n[21]=l("div",{class:"form-tip"},"账户锁定后的等待时间",-1))]),_:1,__:[21]}),e(o,{label:"双因子认证"},{default:t(()=>[e(y,{modelValue:v.twoFactorAuth,"onUpdate:modelValue":n[10]||(n[10]=u=>v.twoFactorAuth=u)},null,8,["modelValue"]),n[22]||(n[22]=l("div",{class:"form-tip"},"启用短信或邮箱验证码",-1))]),_:1,__:[22]}),e(o,{label:"审计日志"},{default:t(()=>[e(y,{modelValue:v.auditLog,"onUpdate:modelValue":n[11]||(n[11]=u=>v.auditLog=u)},null,8,["modelValue"]),n[23]||(n[23]=l("div",{class:"form-tip"},"记录用户操作日志",-1))]),_:1,__:[23]}),e(o,{label:"数据加密"},{default:t(()=>[e(y,{modelValue:v.encryptionEnabled,"onUpdate:modelValue":n[12]||(n[12]=u=>v.encryptionEnabled=u)},null,8,["modelValue"]),n[24]||(n[24]=l("div",{class:"form-tip"},"启用敏感数据加密存储",-1))]),_:1,__:[24]})]),_:1},8,["model"])]),_:1})]),_:1})]),_:1}),e(a,{class:"setting-section"},{header:t(()=>n[25]||(n[25]=[l("span",null,"IP白名单",-1)])),default:t(()=>[l("div",Tl,[l("div",Yl,[(S(!0),P(de,null,ie(m.value,(u,M)=>(S(),O(x,{key:M,closable:"",onClose:H=>Y(M),class:"ip-tag"},{default:t(()=>[f($(u),1)]),_:2},1032,["onClose"]))),128))]),l("div",Al,[e(I,{modelValue:_.value,"onUpdate:modelValue":n[13]||(n[13]=u=>_.value=u),placeholder:"输入IP地址或网段，如：*********** 或 ***********/24",onKeyup:vt(D,["enter"])},null,8,["modelValue"]),e(b,{onClick:D,disabled:!_.value},{default:t(()=>n[26]||(n[26]=[f("添加")])),_:1,__:[26]},8,["disabled"])]),n[27]||(n[27]=l("div",{class:"form-tip"}," 只有白名单中的IP地址才能访问系统。支持单个IP和CIDR网段格式。 ",-1))])]),_:1}),e(a,{class:"setting-section"},{header:t(()=>n[28]||(n[28]=[l("span",null,"安全状态",-1)])),default:t(()=>[l("div",Ml,[e(V,{gutter:20},{default:t(()=>[e(E,{span:8},{default:t(()=>[l("div",Il,[l("div",Ll,[e(d,null,{default:t(()=>[e(k(Ue))]),_:1})]),n[29]||(n[29]=l("div",{class:"status-info"},[l("div",{class:"status-title"},"SSL证书"),l("div",{class:"status-desc"},"有效期至 2024-12-31")],-1))])]),_:1}),e(E,{span:8},{default:t(()=>[l("div",Pl,[l("div",Fl,[e(d,null,{default:t(()=>[e(k(Me))]),_:1})]),n[30]||(n[30]=l("div",{class:"status-info"},[l("div",{class:"status-title"},"防火墙"),l("div",{class:"status-desc"},"部分端口未关闭")],-1))])]),_:1}),e(E,{span:8},{default:t(()=>[l("div",zl,[l("div",Rl,[e(d,null,{default:t(()=>[e(k(Ue))]),_:1})]),n[31]||(n[31]=l("div",{class:"status-info"},[l("div",{class:"status-title"},"入侵检测"),l("div",{class:"status-desc"},"运行正常")],-1))])]),_:1})]),_:1})])]),_:1}),l("div",Nl,[e(b,{onClick:q},{default:t(()=>n[32]||(n[32]=[f("重置")])),_:1,__:[32]}),e(b,{onClick:z,loading:w.value},{default:t(()=>n[33]||(n[33]=[f("安全扫描")])),_:1,__:[33]},8,["loading"]),e(b,{type:"primary",onClick:c,loading:k(s).loading},{default:t(()=>n[34]||(n[34]=[f(" 保存设置 ")])),_:1,__:[34]},8,["loading"])])]),_:1})])}}}),ql=G(Bl,[["__scopeId","data-v-349f9aca"]]),Hl={class:"backup-restore"},Ol={class:"card-header"},jl={key:0},hl={class:"section-header"},Wl={class:"backup-info"},Gl={class:"info-item"},Jl={class:"info-value"},Kl={class:"info-item"},Zl={class:"info-value"},Ql={class:"info-item"},Xl={class:"info-value"},ea={class:"restore-section"},ta={class:"form-actions"},la=J({__name:"BackupRestore",setup(B){const s=Z(),w=L(!1),_=te(W({},s.settings.backup)),r=L(new Date(`2000-01-01 ${_.backupTime}`)),v=L([{id:"backup_001",filename:"backup_2024-01-15_02-00.zip",size:"125.6 MB",type:"auto",createdAt:"2024-01-15 02:00:00",status:"success"},{id:"backup_002",filename:"backup_2024-01-14_02-00.zip",size:"123.2 MB",type:"auto",createdAt:"2024-01-14 02:00:00",status:"success"},{id:"backup_003",filename:"manual_backup_2024-01-13.zip",size:"128.9 MB",type:"manual",createdAt:"2024-01-13 15:30:00",status:"success"},{id:"backup_004",filename:"backup_2024-01-13_02-00.zip",size:"0 MB",type:"auto",createdAt:"2024-01-13 02:00:00",status:"failed"}]),m=i=>se(i).format("YYYY-MM-DD HH:mm:ss"),D=()=>{if(!_.autoBackup)return"未启用";const i=se();let a=i;switch(_.backupInterval){case"daily":a=i.add(1,"day").hour(2).minute(0).second(0);break;case"weekly":a=i.add(1,"week").day(0).hour(2).minute(0).second(0);break;case"monthly":a=i.add(1,"month").date(1).hour(2).minute(0).second(0);break}return a.format("YYYY-MM-DD HH:mm")},Y=i=>({success:"success",failed:"danger",running:"warning"})[i]||"info",z=i=>({success:"成功",failed:"失败",running:"进行中"})[i]||i,c=()=>R(this,null,function*(){w.value=!0;try{const i=yield s.performBackup();i.success&&(U.success(`备份完成：${i.filename}`),v.value.unshift({id:`backup_${Date.now()}`,filename:i.filename,size:i.size,type:"manual",createdAt:new Date().toISOString(),status:"success"}))}catch(i){U.error("备份失败")}finally{w.value=!1}}),q=i=>{U.success(`开始下载：${i.filename}`)},C=i=>R(this,null,function*(){try{yield oe.confirm(`确定要恢复备份 ${i.filename} 吗？这将覆盖当前数据。`,"确认恢复",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),U.success("备份恢复成功")}catch(a){}}),n=i=>R(this,null,function*(){try{yield oe.confirm("确定要删除这个备份吗？删除后无法恢复。","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const a=v.value.findIndex(E=>E.id===i);a!==-1&&(v.value.splice(a,1),U.success("备份删除成功"))}catch(a){}}),d=i=>{const a=i.type==="application/zip"||i.name.endsWith(".zip"),E=i.size/1024/1024<500;return a?E?!0:(U.error("备份文件大小不能超过 500MB!"),!1):(U.error("只能上传 ZIP 格式的备份文件!"),!1)},g=i=>R(this,null,function*(){try{(yield s.restoreBackup(i.file))&&U.success("备份恢复成功")}catch(a){U.error("备份恢复失败")}}),o=()=>R(this,null,function*(){try{const i=se(r.value).format("HH:mm");_.backupTime=i,yield s.updateSettings("backup",_),U.success("备份设置保存成功")}catch(i){U.error("备份设置保存失败")}}),y=()=>{Object.assign(_,s.settings.backup),r.value=new Date(`2000-01-01 ${_.backupTime}`),U.info("已重置为默认设置")};return(i,a)=>{const E=K,V=xe,x=ye,I=pe,b=me,u=gt,M=$e,H=re,N=be,T=Q,p=ae,ce=le,ee=X,fe=_e,h=Ce,ve=Se,De=He;return S(),P("div",Hl,[e(T,null,{header:t(()=>[l("div",Ol,[e(E,null,{default:t(()=>[e(k(Ke))]),_:1}),a[10]||(a[10]=l("span",null,"备份与恢复",-1))])]),default:t(()=>[e(ce,{gutter:20},{default:t(()=>[e(p,{span:12},{default:t(()=>[e(T,{class:"setting-section"},{header:t(()=>a[11]||(a[11]=[l("span",null,"自动备份设置",-1)])),default:t(()=>[e(N,{model:_,"label-width":"120px"},{default:t(()=>[e(x,{label:"自动备份"},{default:t(()=>[e(V,{modelValue:_.autoBackup,"onUpdate:modelValue":a[0]||(a[0]=A=>_.autoBackup=A)},null,8,["modelValue"])]),_:1}),_.autoBackup?(S(),P("div",jl,[e(x,{label:"备份频率"},{default:t(()=>[e(b,{modelValue:_.backupInterval,"onUpdate:modelValue":a[1]||(a[1]=A=>_.backupInterval=A)},{default:t(()=>[e(I,{label:"每日",value:"daily"}),e(I,{label:"每周",value:"weekly"}),e(I,{label:"每月",value:"monthly"})]),_:1},8,["modelValue"])]),_:1}),e(x,{label:"备份时间"},{default:t(()=>[e(u,{modelValue:r.value,"onUpdate:modelValue":a[2]||(a[2]=A=>r.value=A),format:"HH:mm",placeholder:"选择备份时间"},null,8,["modelValue"])]),_:1}),e(x,{label:"保留期限(天)"},{default:t(()=>[e(M,{modelValue:_.retentionPeriod,"onUpdate:modelValue":a[3]||(a[3]=A=>_.retentionPeriod=A),min:7,max:365},null,8,["modelValue"]),a[12]||(a[12]=l("div",{class:"form-tip"},"超过期限的备份将自动删除",-1))]),_:1,__:[12]}),e(x,{label:"备份位置"},{default:t(()=>[e(H,{modelValue:_.backupLocation,"onUpdate:modelValue":a[4]||(a[4]=A=>_.backupLocation=A),placeholder:"/backup"},null,8,["modelValue"])]),_:1})])):j("",!0)]),_:1},8,["model"])]),_:1})]),_:1}),e(p,{span:12},{default:t(()=>[e(T,{class:"setting-section"},{header:t(()=>a[13]||(a[13]=[l("span",null,"备份内容",-1)])),default:t(()=>[e(N,{model:_,"label-width":"120px"},{default:t(()=>[e(x,{label:"包含文件"},{default:t(()=>[e(V,{modelValue:_.includeFiles,"onUpdate:modelValue":a[5]||(a[5]=A=>_.includeFiles=A)},null,8,["modelValue"]),a[14]||(a[14]=l("div",{class:"form-tip"},"备份上传的文件和附件",-1))]),_:1,__:[14]}),e(x,{label:"包含数据库"},{default:t(()=>[e(V,{modelValue:_.includeDatabase,"onUpdate:modelValue":a[6]||(a[6]=A=>_.includeDatabase=A)},null,8,["modelValue"]),a[15]||(a[15]=l("div",{class:"form-tip"},"备份所有业务数据",-1))]),_:1,__:[15]}),e(x,{label:"包含配置"},{default:t(()=>[e(V,{modelValue:_.includeSettings,"onUpdate:modelValue":a[7]||(a[7]=A=>_.includeSettings=A)},null,8,["modelValue"]),a[16]||(a[16]=l("div",{class:"form-tip"},"备份系统配置信息",-1))]),_:1,__:[16]}),e(x,{label:"压缩备份"},{default:t(()=>[e(V,{modelValue:_.compression,"onUpdate:modelValue":a[8]||(a[8]=A=>_.compression=A)},null,8,["modelValue"]),a[17]||(a[17]=l("div",{class:"form-tip"},"减少备份文件大小",-1))]),_:1,__:[17]}),e(x,{label:"加密备份"},{default:t(()=>[e(V,{modelValue:_.encryption,"onUpdate:modelValue":a[9]||(a[9]=A=>_.encryption=A)},null,8,["modelValue"]),a[18]||(a[18]=l("div",{class:"form-tip"},"保护备份文件安全",-1))]),_:1,__:[18]})]),_:1},8,["model"])]),_:1})]),_:1})]),_:1}),e(T,{class:"setting-section"},{header:t(()=>[l("div",hl,[a[20]||(a[20]=l("span",null,"手动备份",-1)),e(ee,{type:"primary",onClick:c,loading:w.value},{default:t(()=>[e(E,null,{default:t(()=>[e(k(Ie))]),_:1}),a[19]||(a[19]=f(" 立即备份 "))]),_:1,__:[19]},8,["loading"])])]),default:t(()=>[l("div",Wl,[e(ce,{gutter:20},{default:t(()=>[e(p,{span:8},{default:t(()=>[l("div",Gl,[a[21]||(a[21]=l("div",{class:"info-label"},"最后备份时间",-1)),l("div",Jl,$(m(k(s).systemInfo.lastBackup)),1)])]),_:1}),e(p,{span:8},{default:t(()=>[l("div",Kl,[a[23]||(a[23]=l("div",{class:"info-label"},"备份状态",-1)),l("div",Zl,[e(fe,{type:"success"},{default:t(()=>a[22]||(a[22]=[f("正常")])),_:1,__:[22]})])])]),_:1}),e(p,{span:8},{default:t(()=>[l("div",Ql,[a[24]||(a[24]=l("div",{class:"info-label"},"下次自动备份",-1)),l("div",Xl,$(D()),1)])]),_:1})]),_:1})])]),_:1}),e(T,{class:"setting-section"},{header:t(()=>a[25]||(a[25]=[l("span",null,"备份历史",-1)])),default:t(()=>[e(ve,{data:v.value,stripe:""},{default:t(()=>[e(h,{prop:"filename",label:"文件名","min-width":"200"}),e(h,{prop:"size",label:"大小",width:"100"}),e(h,{prop:"type",label:"类型",width:"100"},{default:t(({row:A})=>[e(fe,{type:A.type==="auto"?"primary":"success"},{default:t(()=>[f($(A.type==="auto"?"自动":"手动"),1)]),_:2},1032,["type"])]),_:1}),e(h,{prop:"createdAt",label:"创建时间",width:"150"},{default:t(({row:A})=>[f($(m(A.createdAt)),1)]),_:1}),e(h,{prop:"status",label:"状态",width:"100"},{default:t(({row:A})=>[e(fe,{type:Y(A.status)},{default:t(()=>[f($(z(A.status)),1)]),_:2},1032,["type"])]),_:1}),e(h,{label:"操作",width:"200"},{default:t(({row:A})=>[e(ee,{size:"small",onClick:Ve=>q(A)},{default:t(()=>a[26]||(a[26]=[f(" 下载 ")])),_:2,__:[26]},1032,["onClick"]),e(ee,{size:"small",onClick:Ve=>C(A),disabled:A.status!=="success"},{default:t(()=>a[27]||(a[27]=[f(" 恢复 ")])),_:2,__:[27]},1032,["onClick","disabled"]),e(ee,{size:"small",type:"danger",onClick:Ve=>n(A.id)},{default:t(()=>a[28]||(a[28]=[f(" 删除 ")])),_:2,__:[28]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),e(T,{class:"setting-section"},{header:t(()=>a[29]||(a[29]=[l("span",null,"恢复备份",-1)])),default:t(()=>[l("div",ea,[e(De,{class:"upload-demo",drag:"",action:"#","before-upload":d,"http-request":g,"show-file-list":!1},{tip:t(()=>a[30]||(a[30]=[l("div",{class:"el-upload__tip"}," 只能上传 .zip 格式的备份文件，且不超过 500MB ",-1)])),default:t(()=>[e(E,{class:"el-icon--upload"},{default:t(()=>[e(k(bt))]),_:1}),a[31]||(a[31]=l("div",{class:"el-upload__text"},[f(" 将备份文件拖到此处，或"),l("em",null,"点击上传")],-1))]),_:1,__:[31]})])]),_:1}),l("div",ta,[e(ee,{onClick:y},{default:t(()=>a[32]||(a[32]=[f("重置")])),_:1,__:[32]}),e(ee,{type:"primary",onClick:o,loading:k(s).loading},{default:t(()=>a[33]||(a[33]=[f(" 保存设置 ")])),_:1,__:[33]},8,["loading"])])]),_:1})])}}}),aa=G(la,[["__scopeId","data-v-899eb980"]]),sa={class:"system-logs"},oa={class:"card-header"},na={class:"header-actions"},ua={class:"log-stats"},da={class:"stat-icon"},ia={class:"stat-info"},ra={class:"stat-value"},ma={class:"stat-label"},pa={class:"log-filters"},_a=["title"],ca={key:0},fa={key:1,class:"text-muted"},va={key:0,class:"batch-actions"},ga={key:0,class:"log-detail"},ba={class:"log-message-detail"},ya={key:0,class:"log-details"},Va=J({__name:"SystemLogs",setup(B){const s=Z(),w=L(!1),_=L(!1),r=L(null),v=L([]),m=L(""),D=L(null),Y=L({level:null,category:null}),z=ue(()=>{let b=[...s.systemLogs];if(Y.value.level&&(b=b.filter(u=>u.level===Y.value.level)),Y.value.category&&(b=b.filter(u=>u.category===Y.value.category)),D.value){const[u,M]=D.value;b=b.filter(H=>{const N=new Date(H.timestamp);return N>=new Date(u)&&N<=new Date(M)})}return m.value&&(b=b.filter(u=>u.message.toLowerCase().includes(m.value.toLowerCase()))),b}),c=b=>({debug:Ut,info:ze,warn:Me,error:Fe,fatal:Fe})[b]||ze,q=b=>({debug:"info",info:"success",warn:"warning",error:"danger",fatal:"danger"})[b]||"info",C=b=>({debug:"调试",info:"信息",warn:"警告",error:"错误",fatal:"严重"})[b]||b,n=b=>({system:"系统",user:"用户",device:"设备",warning:"预警",security:"安全"})[b]||b,d=b=>{const u=s.users.find(M=>M.id===b);return(u==null?void 0:u.fullName)||b},g=b=>se(b).format("MM-DD HH:mm:ss"),o=b=>{b?s.setLogFilter("dateRange",b):s.setLogFilter("dateRange",null)},y=b=>{v.value=b},i=b=>{r.value=b,_.value=!0},a=()=>R(this,null,function*(){w.value=!0;try{const b=yield s.exportLogs("csv");b.success&&U.success(`日志导出成功：${b.filename}`)}catch(b){U.error("日志导出失败")}finally{w.value=!1}}),E=()=>R(this,null,function*(){try{yield oe.confirm("确定要清空所有日志吗？此操作不可恢复。","确认清空",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield s.clearLogs(),U.success("日志清空成功")}catch(b){}}),V=()=>R(this,null,function*(){try{U.success(`导出 ${v.value.length} 条日志`)}catch(b){U.error("批量导出失败")}}),x=()=>R(this,null,function*(){try{yield oe.confirm(`确定要删除选中的 ${v.value.length} 条日志吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),U.success(`删除 ${v.value.length} 条日志`),v.value=[]}catch(b){}}),I=()=>{Y.value={level:null,category:null},D.value=null,m.value="",s.clearLogFilters()};return(b,u)=>{const M=K,H=X,N=ae,T=le,p=pe,ce=me,ee=Vt,fe=re,h=Ce,ve=_e,De=Se,A=kt,Ve=Q,ne=et,tt=Xe,lt=Te,at=he;return S(),P("div",sa,[e(Ve,null,{header:t(()=>[l("div",oa,[e(M,null,{default:t(()=>[e(k(Ze))]),_:1}),u[7]||(u[7]=l("span",null,"系统日志",-1)),l("div",na,[e(H,{onClick:a,loading:w.value},{default:t(()=>[e(M,null,{default:t(()=>[e(k(Ie))]),_:1}),u[5]||(u[5]=f(" 导出日志 "))]),_:1,__:[5]},8,["loading"]),e(H,{type:"danger",onClick:E},{default:t(()=>[e(M,null,{default:t(()=>[e(k(Qe))]),_:1}),u[6]||(u[6]=f(" 清空日志 "))]),_:1,__:[6]})])])]),default:t(()=>[l("div",ua,[e(T,{gutter:20},{default:t(()=>[(S(!0),P(de,null,ie(k(s).getLogLevelStats,(F,ge)=>(S(),O(N,{span:4,key:ge},{default:t(()=>[l("div",{class:ke(["stat-card",ge])},[l("div",da,[e(M,{size:"20"},{default:t(()=>[(S(),O(yt(c(ge))))]),_:2},1024)]),l("div",ia,[l("div",ra,$(F),1),l("div",ma,$(C(ge)),1)])],2)]),_:2},1024))),128))]),_:1})]),l("div",pa,[e(T,{gutter:16},{default:t(()=>[e(N,{span:4},{default:t(()=>[e(ce,{modelValue:Y.value.level,"onUpdate:modelValue":u[0]||(u[0]=F=>Y.value.level=F),placeholder:"选择级别",clearable:""},{default:t(()=>[e(p,{label:"调试",value:"debug"}),e(p,{label:"信息",value:"info"}),e(p,{label:"警告",value:"warn"}),e(p,{label:"错误",value:"error"}),e(p,{label:"严重",value:"fatal"})]),_:1},8,["modelValue"])]),_:1}),e(N,{span:4},{default:t(()=>[e(ce,{modelValue:Y.value.category,"onUpdate:modelValue":u[1]||(u[1]=F=>Y.value.category=F),placeholder:"选择类别",clearable:""},{default:t(()=>[e(p,{label:"系统",value:"system"}),e(p,{label:"用户",value:"user"}),e(p,{label:"设备",value:"device"}),e(p,{label:"预警",value:"warning"}),e(p,{label:"安全",value:"security"})]),_:1},8,["modelValue"])]),_:1}),e(N,{span:8},{default:t(()=>[e(ee,{modelValue:D.value,"onUpdate:modelValue":u[2]||(u[2]=F=>D.value=F),type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD HH:mm","value-format":"YYYY-MM-DD HH:mm:ss",onChange:o},null,8,["modelValue"])]),_:1}),e(N,{span:4},{default:t(()=>[e(fe,{modelValue:m.value,"onUpdate:modelValue":u[3]||(u[3]=F=>m.value=F),placeholder:"搜索日志内容",clearable:""},{prefix:t(()=>[e(M,null,{default:t(()=>[e(k(wt))]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(N,{span:4},{default:t(()=>[e(H,{onClick:I},{default:t(()=>u[8]||(u[8]=[f("清除筛选")])),_:1,__:[8]})]),_:1})]),_:1})]),Oe((S(),O(De,{data:z.value,stripe:"",height:"500",onSelectionChange:y},{default:t(()=>[e(h,{type:"selection",width:"55"}),e(h,{prop:"timestamp",label:"时间",width:"150"},{default:t(({row:F})=>[f($(g(F.timestamp)),1)]),_:1}),e(h,{prop:"level",label:"级别",width:"80"},{default:t(({row:F})=>[e(ve,{type:q(F.level),size:"small"},{default:t(()=>[f($(C(F.level)),1)]),_:2},1032,["type"])]),_:1}),e(h,{prop:"category",label:"类别",width:"80"},{default:t(({row:F})=>[e(ve,{type:"info",size:"small"},{default:t(()=>[f($(n(F.category)),1)]),_:2},1024)]),_:1}),e(h,{prop:"message",label:"消息","min-width":"300"},{default:t(({row:F})=>[l("div",{class:"log-message",title:F.message},$(F.message),9,_a)]),_:1}),e(h,{prop:"userId",label:"用户",width:"100"},{default:t(({row:F})=>[F.userId?(S(),P("span",ca,$(d(F.userId)),1)):(S(),P("span",fa,"系统"))]),_:1}),e(h,{prop:"ip",label:"IP地址",width:"120"}),e(h,{label:"操作",width:"100"},{default:t(({row:F})=>[e(H,{size:"small",onClick:ge=>i(F)},{default:t(()=>u[9]||(u[9]=[f(" 详情 ")])),_:2,__:[9]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[at,k(s).loading]]),v.value.length>0?(S(),P("div",va,[e(A,{title:`已选择 ${v.value.length} 条日志`,type:"info",closable:!1},{default:t(()=>[e(H,{size:"small",onClick:V},{default:t(()=>u[10]||(u[10]=[f(" 批量导出 ")])),_:1,__:[10]}),e(H,{size:"small",type:"danger",onClick:x},{default:t(()=>u[11]||(u[11]=[f(" 批量删除 ")])),_:1,__:[11]})]),_:1},8,["title"])])):j("",!0)]),_:1}),e(lt,{modelValue:_.value,"onUpdate:modelValue":u[4]||(u[4]=F=>_.value=F),title:"日志详情",width:"800px"},{default:t(()=>[r.value?(S(),P("div",ga,[e(tt,{column:2,border:""},{default:t(()=>[e(ne,{label:"时间"},{default:t(()=>[f($(g(r.value.timestamp)),1)]),_:1}),e(ne,{label:"级别"},{default:t(()=>[e(ve,{type:q(r.value.level)},{default:t(()=>[f($(C(r.value.level)),1)]),_:1},8,["type"])]),_:1}),e(ne,{label:"类别"},{default:t(()=>[f($(n(r.value.category)),1)]),_:1}),e(ne,{label:"用户"},{default:t(()=>[f($(r.value.userId?d(r.value.userId):"系统"),1)]),_:1}),e(ne,{label:"IP地址"},{default:t(()=>[f($(r.value.ip||"-"),1)]),_:1}),e(ne,{label:"用户代理"},{default:t(()=>[f($(r.value.userAgent||"-"),1)]),_:1})]),_:1}),l("div",ba,[u[12]||(u[12]=l("h4",null,"消息内容",-1)),l("pre",null,$(r.value.message),1)]),r.value.details?(S(),P("div",ya,[u[13]||(u[13]=l("h4",null,"详细信息",-1)),l("pre",null,$(JSON.stringify(r.value.details,null,2)),1)])):j("",!0)])):j("",!0)]),_:1},8,["modelValue"])])}}}),wa=G(Va,[["__scopeId","data-v-e064aeb9"]]),ka={class:"system-info"},Ua={class:"card-header"},$a={class:"header-actions"},xa={class:"service-status"},Sa={class:"status-item"},Ca={class:"status-icon success"},Da={class:"status-info"},Ea={class:"status-desc"},Ta={class:"status-item"},Ya={class:"status-icon success"},Aa={class:"status-info"},Ma={class:"status-desc"},Ia={class:"resource-content"},La={class:"resource-chart"},Pa={class:"resource-info"},Fa={class:"resource-value"},za={class:"resource-content"},Ra={class:"resource-chart"},Na={class:"resource-info"},Ba={class:"resource-value"},qa={class:"resource-content"},Ha={class:"resource-chart"},Oa={class:"resource-info"},ja={class:"resource-value"},ha={class:"business-stats"},Wa={class:"stat-item"},Ga={class:"stat-icon"},Ja={class:"stat-info"},Ka={class:"stat-value"},Za={class:"stat-item"},Qa={class:"stat-icon"},Xa={class:"stat-info"},es={class:"stat-value"},ts={class:"stat-item"},ls={class:"stat-icon"},as={class:"stat-info"},ss={class:"stat-value"},os={class:"system-actions"},ns=J({__name:"SystemInfo",setup(B){const s=Z(),w=L(!1),_=L(!1),r=L(!1),v=L(!1),m=ue(()=>s.systemInfo),D=i=>({development:"开发环境",testing:"测试环境",staging:"预发布环境",production:"生产环境"})[i]||i,Y=i=>i<1?`${Math.floor(i*24)} 小时`:`${i} 天`,z=i=>se(i).format("YYYY-MM-DD HH:mm:ss"),c=i=>i<60?"#67C23A":i<80?"#E6A23C":"#F56C6C",q=i=>i<60?"normal":i<80?"warning":"danger",C=i=>i<60?"正常":i<80?"偏高":"过高",n=()=>R(this,null,function*(){w.value=!0;try{s.refreshSystemInfo(),U.success("系统信息已刷新")}finally{w.value=!1}}),d=()=>R(this,null,function*(){try{yield oe.confirm("确定要重启系统吗？这将中断所有用户的连接。","确认重启",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),_.value=!0,yield new Promise(i=>setTimeout(i,3e3)),U.success("系统重启完成")}catch(i){}finally{_.value=!1}}),g=()=>R(this,null,function*(){r.value=!0;try{yield new Promise(i=>setTimeout(i,2e3)),U.success("缓存清理完成")}finally{r.value=!1}}),o=()=>{U.success("开始下载系统日志")},y=()=>R(this,null,function*(){v.value=!0;try{yield new Promise(i=>setTimeout(i,2e3)),U.info("当前已是最新版本")}finally{v.value=!1}});return(i,a)=>{const E=K,V=X,x=_e,I=et,b=Xe,u=Q,M=ae,H=le,N=$t;return S(),P("div",ka,[e(u,null,{header:t(()=>[l("div",Ua,[e(E,null,{default:t(()=>[e(k(Ee))]),_:1}),a[1]||(a[1]=l("span",null,"系统信息",-1)),l("div",$a,[e(V,{onClick:n,loading:w.value},{default:t(()=>[e(E,null,{default:t(()=>[e(k(Re))]),_:1}),a[0]||(a[0]=f(" 刷新 "))]),_:1,__:[0]},8,["loading"])])])]),default:t(()=>[e(H,{gutter:20},{default:t(()=>[e(M,{span:12},{default:t(()=>[e(u,{class:"info-section"},{header:t(()=>a[2]||(a[2]=[l("span",null,"基本信息",-1)])),default:t(()=>[e(b,{column:1,border:""},{default:t(()=>[e(I,{label:"系统版本"},{default:t(()=>[e(x,{type:"primary"},{default:t(()=>[f("v"+$(m.value.version),1)]),_:1})]),_:1}),e(I,{label:"构建日期"},{default:t(()=>[f($(m.value.buildDate),1)]),_:1}),e(I,{label:"运行环境"},{default:t(()=>[e(x,{type:m.value.environment==="production"?"success":"warning"},{default:t(()=>[f($(D(m.value.environment)),1)]),_:1},8,["type"])]),_:1}),e(I,{label:"运行时间"},{default:t(()=>[f($(Y(m.value.uptime)),1)]),_:1}),e(I,{label:"最后备份"},{default:t(()=>[f($(z(m.value.lastBackup)),1)]),_:1})]),_:1})]),_:1})]),_:1}),e(M,{span:12},{default:t(()=>[e(u,{class:"info-section"},{header:t(()=>a[3]||(a[3]=[l("span",null,"服务状态",-1)])),default:t(()=>[l("div",xa,[l("div",Sa,[l("div",Ca,[e(E,null,{default:t(()=>[e(k(Ue))]),_:1})]),l("div",Da,[a[4]||(a[4]=l("div",{class:"status-title"},"网络连接",-1)),l("div",Ea,$(m.value.networkStatus),1)])]),l("div",Ta,[l("div",Ya,[e(E,null,{default:t(()=>[e(k(Ue))]),_:1})]),l("div",Aa,[a[5]||(a[5]=l("div",{class:"status-title"},"数据库",-1)),l("div",Ma,$(m.value.databaseStatus),1)])])])]),_:1})]),_:1})]),_:1}),e(H,{gutter:20},{default:t(()=>[e(M,{span:8},{default:t(()=>[e(u,{class:"resource-card"},{header:t(()=>a[6]||(a[6]=[l("span",null,"CPU使用率",-1)])),default:t(()=>[l("div",Ia,[l("div",La,[e(N,{type:"circle",percentage:m.value.cpuUsage,color:c(m.value.cpuUsage),width:120},null,8,["percentage","color"])]),l("div",Pa,[l("div",Fa,$(m.value.cpuUsage.toFixed(1))+"%",1),l("div",{class:ke(["resource-status",q(m.value.cpuUsage)])},$(C(m.value.cpuUsage)),3)])])]),_:1})]),_:1}),e(M,{span:8},{default:t(()=>[e(u,{class:"resource-card"},{header:t(()=>a[7]||(a[7]=[l("span",null,"内存使用率",-1)])),default:t(()=>[l("div",za,[l("div",Ra,[e(N,{type:"circle",percentage:m.value.memoryUsage,color:c(m.value.memoryUsage),width:120},null,8,["percentage","color"])]),l("div",Na,[l("div",Ba,$(m.value.memoryUsage.toFixed(1))+"%",1),l("div",{class:ke(["resource-status",q(m.value.memoryUsage)])},$(C(m.value.memoryUsage)),3)])])]),_:1})]),_:1}),e(M,{span:8},{default:t(()=>[e(u,{class:"resource-card"},{header:t(()=>a[8]||(a[8]=[l("span",null,"磁盘使用率",-1)])),default:t(()=>[l("div",qa,[l("div",Ha,[e(N,{type:"circle",percentage:m.value.diskUsage,color:c(m.value.diskUsage),width:120},null,8,["percentage","color"])]),l("div",Oa,[l("div",ja,$(m.value.diskUsage.toFixed(1))+"%",1),l("div",{class:ke(["resource-status",q(m.value.diskUsage)])},$(C(m.value.diskUsage)),3)])])]),_:1})]),_:1})]),_:1}),e(H,{gutter:20},{default:t(()=>[e(M,{span:12},{default:t(()=>[e(u,{class:"info-section"},{header:t(()=>a[9]||(a[9]=[l("span",null,"业务统计",-1)])),default:t(()=>[l("div",ha,[l("div",Wa,[l("div",Ga,[e(E,{size:"24",color:"#67C23A"},{default:t(()=>[e(k(je))]),_:1})]),l("div",Ja,[l("div",Ka,$(m.value.activeUsers),1),a[10]||(a[10]=l("div",{class:"stat-label"},"活跃用户",-1))])]),l("div",Za,[l("div",Qa,[e(E,{size:"24",color:"#409EFF"},{default:t(()=>[e(k(Ee))]),_:1})]),l("div",Xa,[l("div",es,$(m.value.totalDevices),1),a[11]||(a[11]=l("div",{class:"stat-label"},"设备总数",-1))])]),l("div",ts,[l("div",ls,[e(E,{size:"24",color:"#E6A23C"},{default:t(()=>[e(k(Me))]),_:1})]),l("div",as,[l("div",ss,$(m.value.totalWarnings),1),a[12]||(a[12]=l("div",{class:"stat-label"},"预警总数",-1))])])])]),_:1})]),_:1}),e(M,{span:12},{default:t(()=>[e(u,{class:"info-section"},{header:t(()=>a[13]||(a[13]=[l("span",null,"系统操作",-1)])),default:t(()=>[l("div",os,[e(V,{type:"primary",onClick:d,loading:_.value},{default:t(()=>[e(E,null,{default:t(()=>[e(k(xt))]),_:1}),a[14]||(a[14]=f(" 重启系统 "))]),_:1,__:[14]},8,["loading"]),e(V,{type:"warning",onClick:g,loading:r.value},{default:t(()=>[e(E,null,{default:t(()=>[e(k(Qe))]),_:1}),a[15]||(a[15]=f(" 清理缓存 "))]),_:1,__:[15]},8,["loading"]),e(V,{onClick:o},{default:t(()=>[e(E,null,{default:t(()=>[e(k(Ie))]),_:1}),a[16]||(a[16]=f(" 下载日志 "))]),_:1,__:[16]}),e(V,{onClick:y,loading:v.value},{default:t(()=>[e(E,null,{default:t(()=>[e(k(Re))]),_:1}),a[17]||(a[17]=f(" 检查更新 "))]),_:1,__:[17]},8,["loading"])])]),_:1})]),_:1})]),_:1})]),_:1})])}}}),us=G(ns,[["__scopeId","data-v-a6902e8f"]]),ds={class:"settings-system"},is={class:"settings-nav"},rs={class:"settings-content"},ms=J({__name:"index",setup(B){Z();const s=L("general"),w=_=>{s.value=_};return St(()=>{}),(_,r)=>{const v=K,m=Dt,D=Ct,Y=Q;return S(),P("div",ds,[e(Y,{class:"nav-card"},{default:t(()=>[l("div",is,[e(D,{modelValue:s.value,"onUpdate:modelValue":r[0]||(r[0]=z=>s.value=z),mode:"horizontal",onSelect:w},{default:t(()=>[e(m,{index:"general"},{default:t(()=>[e(v,null,{default:t(()=>[e(k(qe))]),_:1}),r[1]||(r[1]=l("span",null,"通用设置",-1))]),_:1,__:[1]}),e(m,{index:"users"},{default:t(()=>[e(v,null,{default:t(()=>[e(k(We))]),_:1}),r[2]||(r[2]=l("span",null,"用户管理",-1))]),_:1,__:[2]}),e(m,{index:"roles"},{default:t(()=>[e(v,null,{default:t(()=>[e(k(Ae))]),_:1}),r[3]||(r[3]=l("span",null,"角色权限",-1))]),_:1,__:[3]}),e(m,{index:"notifications"},{default:t(()=>[e(v,null,{default:t(()=>[e(k(Ge))]),_:1}),r[4]||(r[4]=l("span",null,"通知设置",-1))]),_:1,__:[4]}),e(m,{index:"security"},{default:t(()=>[e(v,null,{default:t(()=>[e(k(Je))]),_:1}),r[5]||(r[5]=l("span",null,"安全设置",-1))]),_:1,__:[5]}),e(m,{index:"backup"},{default:t(()=>[e(v,null,{default:t(()=>[e(k(Ke))]),_:1}),r[6]||(r[6]=l("span",null,"备份恢复",-1))]),_:1,__:[6]}),e(m,{index:"logs"},{default:t(()=>[e(v,null,{default:t(()=>[e(k(Ze))]),_:1}),r[7]||(r[7]=l("span",null,"系统日志",-1))]),_:1,__:[7]}),e(m,{index:"system"},{default:t(()=>[e(v,null,{default:t(()=>[e(k(Ee))]),_:1}),r[8]||(r[8]=l("span",null,"系统信息",-1))]),_:1,__:[8]})]),_:1},8,["modelValue"])])]),_:1}),l("div",rs,[s.value==="general"?(S(),O(Mt,{key:0})):j("",!0),s.value==="users"?(S(),O(nl,{key:1})):j("",!0),s.value==="roles"?(S(),O(cl,{key:2})):j("",!0),s.value==="notifications"?(S(),O(Cl,{key:3})):j("",!0),s.value==="security"?(S(),O(ql,{key:4})):j("",!0),s.value==="backup"?(S(),O(aa,{key:5})):j("",!0),s.value==="logs"?(S(),O(wa,{key:6})):j("",!0),s.value==="system"?(S(),O(us,{key:7})):j("",!0)])])}}}),Ms=G(ms,[["__scopeId","data-v-390f19ec"]]);export{Ms as default};
