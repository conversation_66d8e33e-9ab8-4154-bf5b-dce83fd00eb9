(function () {
  var scheme = (("https:" == document.location.protocol) ? "https" : "http");
  var adnxs_domain = 'secure.adnxs.com';
  var aol_domain = 'secure.leadback.advertising.com';
  window.adroll_seg_eid = "XDG3YBLADRFKFJSCDGKQAR";
  window.adroll_sendrolling_cross_device = true;
  window.adroll_form_fields = {};
  window.adroll_third_party_forms = {};
  window.adroll_third_party_detected = {"MQIH2GT23FAYXPRQCYQCDZ":{"advertisable_eid":"MQIH2GT23FAYXPRQCYQCDZ","has_hubspot":false,"has_mailchimp":false,"has_marketo":false}};
  window.adroll_snippet_errors = [];
  if (typeof __adroll._form_attach != 'undefined') {
    __adroll._form_attach();
  }
  if (typeof __adroll._form_tp_attach != 'undefined') {
    __adroll._form_tp_attach();
  }
  window.adroll_rule_type = "l";
  var rule = ["def09a03", "*/products/*"];
  if (scheme=='http') { adnxs_domain = 'ib.adnxs.com'; aol_domain = 'leadback.advertising.com';}
  var el = document.createElement("div");
  el.style["width"] = "1px";
  el.style["height"] = "1px";
  el.style["display"] = "inline";
  el.style["position"] = "absolute";
  var content = '';

  if (__adroll.consent_allowed(__adroll.consent_networks.facebook)) {
      !function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
      n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
      n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
      t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
      document,'script','//connect.facebook.net/en_US/fbevents.js');
  }

  try {
      try {
          
    (function(){
     if (__adroll.consent_allowed(__adroll.consent_networks.linkedin)) {
        if (typeof window._already_called_lintrk !== "undefined" && !!window._already_called_lintrk) {
            var tag = document.createElement('img');
            tag.setAttribute('height', '1');
            tag.setAttribute('width', '1');
            tag.setAttribute('style', 'display:none;');
            tag.setAttribute('alt', '');
            tag.setAttribute('src', 'https://px.ads.linkedin.com/collect/?pid=2618188&fmt=gif');
            document.body.appendChild(tag);
        }
        else {
            _linkedin_partner_id = "2618188";
            window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || [];
            window._linkedin_data_partner_ids.push(_linkedin_partner_id);
            var s = document.getElementsByTagName("script")[0];
            var b = document.createElement("script");
            b.type = "text/javascript";b.async = true;
            b.src = "https://snap.licdn.com/li.lms-analytics/insight.min.js";
            s.parentNode.insertBefore(b, s);
        }
      }
    })();

      } catch(e) {
          window.adroll_snippet_errors['linkedin'] = e;
      }
      try {
          
(function() {
var rtb = document.createElement("div");
rtb.style["width"] = "1px";
rtb.style["height"] = "1px";
rtb.style["display"] = "inline";
rtb.style["position"] = "absolute";
rtb.innerHTML = ["/cm/g/out?advertisable=MQIH2GT23FAYXPRQCYQCDZ","/cm/x,bombora,b,experian,index,l,o,outbrain,pubmatic,n,taboola,triplelift/out?advertisable=MQIH2GT23FAYXPRQCYQCDZ"].reduce(function (acc, cmURL) {
    return acc + '<img height="1" width="1" style="border-style:none;" alt="" src="' + __adroll._srv(cmURL) + '"/>';
}, '');
__adroll._head().appendChild(rtb);
})();

      } catch(e) {
          window.adroll_snippet_errors['maya_snippet'] = e;
      }
      try {
          
(function(){
  if (__adroll.consent_allowed(__adroll.consent_networks.linkedin)) {
    if (__adroll._has_global('adroll_segments')) {

      if (__adroll._global('adroll_segments') === '88408b3d') {
        var conversion_tag = document.createElement("img");

        conversion_tag.setAttribute('height', '1');
        conversion_tag.setAttribute('width', '1');
        conversion_tag.setAttribute('style', 'display:none;');
        conversion_tag.setAttribute('alt', '');
        conversion_tag.setAttribute('src', 'https://px.ads.linkedin.com/collect/?pid=2618188&conversionId=3095170&fmt=gif');

        document.body.appendChild(conversion_tag);
      }


      if (__adroll._global('adroll_segments') === '2f6f4c9f') {
        var conversion_tag = document.createElement("img");

        conversion_tag.setAttribute('height', '1');
        conversion_tag.setAttribute('width', '1');
        conversion_tag.setAttribute('style', 'display:none;');
        conversion_tag.setAttribute('alt', '');
        conversion_tag.setAttribute('src', 'https://px.ads.linkedin.com/collect/?pid=2618188&conversionId=3036132&fmt=gif');

        document.body.appendChild(conversion_tag);
      }


      if (__adroll._global('adroll_segments') === 'ffbe9121') {
        var conversion_tag = document.createElement("img");

        conversion_tag.setAttribute('height', '1');
        conversion_tag.setAttribute('width', '1');
        conversion_tag.setAttribute('style', 'display:none;');
        conversion_tag.setAttribute('alt', '');
        conversion_tag.setAttribute('src', 'https://px.ads.linkedin.com/collect/?pid=2618188&conversionId=3036084&fmt=gif');

        document.body.appendChild(conversion_tag);
      }


      if (__adroll._global('adroll_segments') === 'de78dfd5') {
        var conversion_tag = document.createElement("img");

        conversion_tag.setAttribute('height', '1');
        conversion_tag.setAttribute('width', '1');
        conversion_tag.setAttribute('style', 'display:none;');
        conversion_tag.setAttribute('alt', '');
        conversion_tag.setAttribute('src', 'https://px.ads.linkedin.com/collect/?pid=2618188&conversionId=3036108&fmt=gif');

        document.body.appendChild(conversion_tag);
      }


      if (__adroll._global('adroll_segments') === '58cb6291') {
        var conversion_tag = document.createElement("img");

        conversion_tag.setAttribute('height', '1');
        conversion_tag.setAttribute('width', '1');
        conversion_tag.setAttribute('style', 'display:none;');
        conversion_tag.setAttribute('alt', '');
        conversion_tag.setAttribute('src', 'https://px.ads.linkedin.com/collect/?pid=2618188&conversionId=3036124&fmt=gif');

        document.body.appendChild(conversion_tag);
      }


      if (__adroll._global('adroll_segments') === 'b70901fc') {
        var conversion_tag = document.createElement("img");

        conversion_tag.setAttribute('height', '1');
        conversion_tag.setAttribute('width', '1');
        conversion_tag.setAttribute('style', 'display:none;');
        conversion_tag.setAttribute('alt', '');
        conversion_tag.setAttribute('src', 'https://px.ads.linkedin.com/collect/?pid=2618188&conversionId=3036148&fmt=gif');

        document.body.appendChild(conversion_tag);
      }


      if (__adroll._global('adroll_segments') === 'de92421e') {
        var conversion_tag = document.createElement("img");

        conversion_tag.setAttribute('height', '1');
        conversion_tag.setAttribute('width', '1');
        conversion_tag.setAttribute('style', 'display:none;');
        conversion_tag.setAttribute('alt', '');
        conversion_tag.setAttribute('src', 'https://px.ads.linkedin.com/collect/?pid=2618188&conversionId=3036140&fmt=gif');

        document.body.appendChild(conversion_tag);
      }

    }
  }
})();

      } catch(e) {
          window.adroll_snippet_errors['linkedinjsconversion'] = e;
      }
      try {
          
(function(){
window.adroll_sendrolling_email_collection = true;
window.adroll_sendrolling_cross_device = true;
    window.adroll_emc_mode = "noload";
    var scr = document.createElement("script");
    scr.type = "text/javascript";
    scr.src = "//s.adroll.com/j/sendrolling.js";
    ((document.getElementsByTagName("head") || [null])[0] ||
    document.getElementsByTagName("script")[0].parentNode).appendChild(scr);
})();

      } catch(e) {
          window.adroll_snippet_errors['sendrolling'] = e;
      }
      try {
          
(function(d) {
    var ca = d.cookie.split(';');
    var cn = "_mkto_trk=";
    for(var i=0;i < ca.length;i++) {
        var c = ca[i];
        while (c.charAt(0)==' ') c = c.substring(1,c.length);
        if (c.indexOf(cn) == 0) {
            l = "<img height=\"1\" width=\"1\" style=\"border-style:none;\" alt=\"\" src=\"https://d.adroll.com/cm/mk/MQIH2GT23FAYXPRQCYQCDZ/in?id=[mk_id]\"/>";
            l = l.replace(/\[protocol\]/gi, ("https:" == d.location.protocol) ? "https" : "http");
            l = l.replace(/\[mk_id\]/gi, escape(c.substring(cn.length,c.length)));
            d.createElement("div").innerHTML = l;
        }
    }
})(document);


      } catch(e) {
          window.adroll_snippet_errors['audience_hostedmatch'] = e;
      }
      try {
          (function() {
  var ua = window.navigator.userAgent.toLowerCase();
  if (window === window.top && ua.indexOf('safari') !== -1 && ua.indexOf('chrome') === -1 && ua.indexOf('crios') === -1) {
    window.document.body.className += ' adroll_safari_light_theme';
    var b = window.document.createElement('script');
    b.language = 'javascript';
    b.src = '//d.adroll.com/bounce/pre/MQIH2GT23FAYXPRQCYQCDZ/MRGRZNNGWBA5XDBC64SOCY/?d=' + encodeURIComponent('//s.adroll.com/j/bounce.js');
    window.__adroll._head().appendChild(b);
  }
})();
      } catch(e) {
          window.adroll_snippet_errors['bouncejs'] = e;
      }
      try {
          if (__adroll.consent_allowed(__adroll.consent_networks.facebook)) {
    var fbLimitedDataUse = true;
    if(typeof __adroll.fb === 'undefined'){
    if (fbLimitedDataUse) {
        fbq('dataProcessingOptions', ['LDU'], 0, 0);
    }
    fbq('init', '536501327204039');
    fbq('set', 'autoConfig', 'false', '536501327204039');
    __adroll.fb=true;

    var __fbcd = {segment_eid: "XDG3YBLADRFKFJSCDGKQAR"};
    for (var prop in __adroll.get_external_data()){
        __fbcd['ar_' + prop] = __adroll.get_external_data()[prop];
    }

    fbq('track', "PageView", __fbcd);
    } else {
    var __fbcd = {event: "EventSegment", segment_eid: "XDG3YBLADRFKFJSCDGKQAR"};
    for (var prop in __adroll.get_external_data()){
        __fbcd['ar_' + prop] = __adroll.get_external_data()[prop];
    }

    fbq('track', "CustomEvent", __fbcd);
    }
}

      } catch(e) {
          window.adroll_snippet_errors['FacebookExternalDataWCASnippet'] = e;
      }
  } catch(e) {}

  var r = Math.random()*10000000000000000;
  content = content.replace(/\[ord\]/gi, r);
  content = content.replace(/\[protocol\]/gi, scheme);
  content = content.replace(/\[adnxs_domain\]/gi, adnxs_domain);
  content = content.replace(/\[aol_domain\]/gi, aol_domain);
  var adroll_tpc = __adroll._global('adroll_tpc');
  if (adroll_tpc) {
    var srv_parts = __adroll._srv().split('?');
    var srv_host = srv_parts[0].substr(srv_parts[0].indexOf(':') + 1);
    var srv_re = new RegExp(srv_host + '([^\?\"\'\>\#\S]+)\\?*', 'gi');
    content = content.replace(srv_re, srv_host + '$1?' + srv_parts[1] + '&');
  }
  content = __adroll.replace_external_data(content);
  el.innerHTML = content;
  __adroll._head().appendChild(el);
  if (typeof __adroll.set_pixel_cookie != 'undefined') {__adroll.set_pixel_cookie(adroll_adv_id, adroll_pix_id, "XDG3YBLADRFKFJSCDGKQAR");}
}());
