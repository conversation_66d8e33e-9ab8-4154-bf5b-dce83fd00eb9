<template>
  <div class="algorithm-params" v-if="selectedAlgorithm">
    <h3 class="section-title">算法参数设置</h3>
    
    <div class="params-container">
      <!-- 多项式拟合参数 -->
      <template v-if="selectedAlgorithm === 'polynomial'">
        <div class="param-item">
          <div class="param-header">
            <div class="param-label">
              <span>多项式阶数</span>
              <el-tooltip content="阶数越高，拟合曲线越精确，但可能过拟合" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
            <div class="param-value">{{ algorithmParams.polynomial.degree }}</div>
          </div>
          <el-slider 
            v-model="polynomialParams.degree" 
            :min="2" 
            :max="8" 
            :step="1"
            :marks="{2:'2', 5:'5', 8:'8'}"
            @change="updateParams"
            show-stops
          />
        </div>
        
        <div class="param-item">
          <div class="param-header">
            <div class="param-label">
              <span>正则化强度</span>
              <el-tooltip content="调节拟合曲线的平滑度，较高的值会使曲线更平滑但可能降低精度" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
            <div class="param-value">{{ algorithmParams.polynomial.regularization }}</div>
          </div>
          <el-slider 
            v-model="polynomialParams.regularization" 
            :min="0" 
            :max="0.5" 
            :step="0.01"
            :marks="{0:'0', 0.25:'0.25', 0.5:'0.5'}"
            @change="updateParams"
          />
        </div>
      </template>
      
      <!-- 神经网络参数 -->
      <template v-if="selectedAlgorithm === 'neural'">
        <div class="param-item">
          <div class="param-header">
            <div class="param-label">
              <span>隐藏层数</span>
              <el-tooltip content="隐藏层越多，网络可以学习更复杂的模式" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
            <div class="param-value">{{ algorithmParams.neural.hiddenLayers }}</div>
          </div>
          <el-slider 
            v-model="neuralParams.hiddenLayers" 
            :min="1" 
            :max="5" 
            :step="1"
            :marks="{1:'1', 3:'3', 5:'5'}"
            @change="updateParams"
            show-stops
          />
        </div>
        
        <div class="param-item">
          <div class="param-header">
            <div class="param-label">
              <span>神经元数量</span>
              <el-tooltip content="每层神经元的数量，影响网络的学习能力" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
            <div class="param-value">{{ algorithmParams.neural.neurons }}</div>
          </div>
          <el-slider 
            v-model="neuralParams.neurons" 
            :min="5" 
            :max="50" 
            :step="5"
            :marks="{5:'5', 25:'25', 50:'50'}"
            @change="updateParams"
            show-stops
          />
        </div>
        
        <div class="param-item">
          <div class="param-header">
            <div class="param-label">
              <span>学习率</span>
              <el-tooltip content="较高的学习率会加快训练速度，但可能不稳定" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
            <div class="param-value">{{ algorithmParams.neural.learningRate }}</div>
          </div>
          <el-slider 
            v-model="neuralParams.learningRate" 
            :min="0.001" 
            :max="0.1" 
            :step="0.001"
            :marks="{0.001:'0.001', 0.05:'0.05', 0.1:'0.1'}"
            @change="updateParams"
          />
        </div>
      </template>
      
      <!-- 线性拟合参数 -->
      <template v-if="selectedAlgorithm === 'linear'">
        <div class="param-item">
          <div class="param-header">
            <div class="param-label">
              <span>拟合类型</span>
              <el-tooltip content="选择不同的拟合函数类型" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </div>
          <el-radio-group v-model="linearParams.type" @change="updateParams" class="fit-type-radio">
            <el-radio value="linear">线性拟合</el-radio>
            <el-radio value="exponential">指数拟合</el-radio>
            <el-radio value="logarithmic">对数拟合</el-radio>
          </el-radio-group>
        </div>
      </template>
      
      <!-- 样条插值参数 -->
      <template v-if="selectedAlgorithm === 'spline'">
        <div class="param-item">
          <div class="param-header">
            <div class="param-label">
              <span>样条类型</span>
              <el-tooltip content="不同类型的样条曲线具有不同的特性" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </div>
          <el-select v-model="splineParams.type" @change="updateParams" class="param-select">
            <el-option label="三次样条" value="cubic" />
            <el-option label="自然样条" value="natural" />
            <el-option label="固定样条" value="clamped" />
          </el-select>
        </div>
        
        <div class="param-item">
          <div class="param-header">
            <div class="param-label">
              <span>平滑系数</span>
              <el-tooltip content="控制曲线的平滑程度，值越大曲线越平滑" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
            <div class="param-value">{{ algorithmParams.spline.smoothing }}</div>
          </div>
          <el-slider 
            v-model="splineParams.smoothing" 
            :min="0" 
            :max="1" 
            :step="0.1"
            :marks="{0:'0', 0.5:'0.5', 1:'1'}"
            @change="updateParams"
          />
        </div>
      </template>
      
      <!-- 贝塞尔曲线参数 -->
      <template v-if="selectedAlgorithm === 'bezier'">
        <div class="param-item">
          <div class="param-header">
            <div class="param-label">
              <span>控制点数量</span>
              <el-tooltip content="控制点越多，曲线可以更精确地拟合数据" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
            <div class="param-value">{{ algorithmParams.bezier.controlPoints }}</div>
          </div>
          <el-slider 
            v-model="bezierParams.controlPoints" 
            :min="3" 
            :max="10" 
            :step="1"
            :marks="{3:'3', 6:'6', 10:'10'}"
            @change="updateParams"
            show-stops
          />
        </div>
        
        <div class="param-item">
          <div class="param-header">
            <div class="param-label">
              <span>张力系数</span>
              <el-tooltip content="控制曲线的弯曲程度，较高的值使曲线更平滑" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
            <div class="param-value">{{ algorithmParams.bezier.tension }}</div>
          </div>
          <el-slider 
            v-model="bezierParams.tension" 
            :min="0" 
            :max="1" 
            :step="0.1"
            :marks="{0:'0', 0.5:'0.5', 1:'1'}"
            @change="updateParams"
          />
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { InfoFilled } from '@element-plus/icons-vue'
import type { AlgorithmParamsType } from './types'

const props = defineProps<{
  selectedAlgorithm: string;
  algorithmParams: AlgorithmParamsType;
}>()

const emit = defineEmits<{
  (e: 'update:algorithmParams', value: AlgorithmParamsType): void;
}>()

// 各算法参数的本地副本
const polynomialParams = ref({
  degree: props.algorithmParams?.polynomial?.degree || 3,
  regularization: props.algorithmParams?.polynomial?.regularization || 0.01
})

const neuralParams = ref({
  hiddenLayers: props.algorithmParams?.neural?.hiddenLayers || 2,
  neurons: props.algorithmParams?.neural?.neurons || 20,
  learningRate: props.algorithmParams?.neural?.learningRate || 0.01,
  epochs: props.algorithmParams?.neural?.epochs || 1000
})

const linearParams = ref({
  type: props.algorithmParams?.linear?.type || 'linear',
  weights: props.algorithmParams?.linear?.weights || 'equal'
})

const splineParams = ref({
  type: props.algorithmParams?.spline?.type || 'cubic',
  smoothing: props.algorithmParams?.spline?.smoothing || 0.5
})

const bezierParams = ref({
  controlPoints: props.algorithmParams?.bezier?.controlPoints || 4,
  tension: props.algorithmParams?.bezier?.tension || 0.5
})

// 当外部算法参数改变时，更新本地参数
watch(() => props.algorithmParams, (newValue) => {
  if (newValue.polynomial) {
    polynomialParams.value = { ...newValue.polynomial }
  }
  if (newValue.neural) {
    neuralParams.value = { ...newValue.neural }
  }
  if (newValue.linear) {
    linearParams.value = { ...newValue.linear }
  }
  if (newValue.spline) {
    splineParams.value = { ...newValue.spline }
  }
  if (newValue.bezier) {
    bezierParams.value = { ...newValue.bezier }
  }
}, { deep: true })

// 更新参数
const updateParams = () => {
  const updatedParams = { ...props.algorithmParams } as AlgorithmParamsType
  
  switch (props.selectedAlgorithm) {
    case 'polynomial':
      updatedParams.polynomial = { ...polynomialParams.value }
      break
    case 'neural':
      updatedParams.neural = { ...neuralParams.value }
      break
    case 'linear':
      updatedParams.linear = { ...linearParams.value }
      break
    case 'spline':
      updatedParams.spline = { ...splineParams.value }
      break
    case 'bezier':
      updatedParams.bezier = { ...bezierParams.value }
      break
  }
  
  emit('update:algorithmParams', updatedParams)
}
</script>

<style lang="scss" scoped>
.algorithm-params {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--el-color-primary-dark-2);
  position: relative;
  padding-left: 0.75rem;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0.25rem;
    bottom: 0.25rem;
    width: 4px;
    background-color: var(--el-color-primary);
    border-radius: 2px;
  }
}

.params-container {
  background-color: var(--el-bg-color);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid var(--el-border-color-light);
}

.param-item {
  margin-bottom: 1.5rem;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.param-label {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--el-text-color-primary);
  
  .info-icon {
    margin-left: 0.375rem;
    font-size: 0.875rem;
    color: var(--el-color-info);
    cursor: help;
  }
}

.param-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.fit-type-radio {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  
  :deep(.el-radio) {
    margin-right: 0;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    background-color: var(--el-fill-color-light);
    transition: all 0.2s ease;
    
    &.is-checked {
      background-color: var(--el-color-primary-light-9);
    }
    
    .el-radio__label {
      font-size: 0.875rem;
    }
  }
}

.param-select {
  width: 100%;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .params-container {
    padding: 1rem;
  }
}
</style> 