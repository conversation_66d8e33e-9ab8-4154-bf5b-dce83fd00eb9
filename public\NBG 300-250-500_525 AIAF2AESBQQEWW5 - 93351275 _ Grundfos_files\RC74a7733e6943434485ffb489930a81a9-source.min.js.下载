// For license information, see `https://assets.adobedtm.com/5e107551ef5d/89d0c5ed902a/4021f82e766f/RC74a7733e6943434485ffb489930a81a9-source.js`.
_satellite.__registerScript('https://assets.adobedtm.com/5e107551ef5d/89d0c5ed902a/4021f82e766f/RC74a7733e6943434485ffb489930a81a9-source.min.js', "!function(e,t,n,a,o,c,f){e.fbq||(o=e.fbq=function(){o.callMethod?o.callMethod.apply(o,arguments):o.queue.push(arguments)},e._fbq||(e._fbq=o),o.push=o,o.loaded=!0,o.version=\"2.0\",o.queue=[],(c=t.createElement(n)).async=!0,c.src=a,(f=t.getElementsByTagName(n)[0]).parentNode.insertBefore(c,f))}(window,document,\"script\",\"https://connect.facebook.net/en_US/fbevents.js\"),fbq(\"init\",_satellite.getVar(\"facebook_id\")),fbq(\"track\",\"PageView\");");