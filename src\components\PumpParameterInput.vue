<template>
  <el-card class="pump-parameter-input">
    <template #header>
      <div class="card-header">
        <el-icon><Setting /></el-icon>
        <span>水泵参数配置</span>
        <div class="header-actions">
          <el-button size="small" @click="resetToDefault">重置默认</el-button>
          <el-button size="small" type="primary" @click="saveParameters">保存配置</el-button>
        </div>
      </div>
    </template>

    <el-tabs v-model="activeTab" type="border-card">
      <!-- 基础参数 -->
      <el-tab-pane label="基础参数" name="basic">
        <el-form :model="pumpParams" label-width="120px" :rules="rules" ref="basicFormRef">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="水泵名称" prop="name">
                <el-input v-model="pumpParams.name" placeholder="请输入水泵名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="型号" prop="model">
                <el-input v-model="pumpParams.model" placeholder="请输入型号" />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="额定流量" prop="ratedFlow">
                <el-input-number
                  v-model="pumpParams.ratedFlow"
                  :min="0"
                  :max="5000"
                  :step="10"
                  controls-position="right"
                />
                <span class="unit">m³/h</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="额定扬程" prop="ratedHead">
                <el-input-number
                  v-model="pumpParams.ratedHead"
                  :min="0"
                  :max="200"
                  :step="1"
                  :precision="1"
                  controls-position="right"
                />
                <span class="unit">m</span>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="额定功率" prop="ratedPower">
                <el-input-number
                  v-model="pumpParams.ratedPower"
                  :min="0"
                  :max="1000"
                  :step="1"
                  :precision="1"
                  controls-position="right"
                />
                <span class="unit">kW</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="额定转速" prop="ratedSpeed">
                <el-input-number
                  v-model="pumpParams.ratedSpeed"
                  :min="0"
                  :max="5000"
                  :step="10"
                  controls-position="right"
                />
                <span class="unit">rpm</span>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="额定效率" prop="efficiency">
                <el-input-number
                  v-model="pumpParams.efficiency"
                  :min="0"
                  :max="100"
                  :step="0.1"
                  :precision="1"
                  controls-position="right"
                />
                <span class="unit">%</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="叶轮直径" prop="impellerDiameter">
                <el-input-number
                  v-model="pumpParams.impellerDiameter"
                  :min="0"
                  :max="1000"
                  :step="1"
                  controls-position="right"
                />
                <span class="unit">mm</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>

      <!-- 曲线数据 -->
      <el-tab-pane label="曲线数据" name="curve">
        <div class="curve-data-section">
          <div class="section-header">
            <h4>性能曲线数据点</h4>
            <div class="actions">
              <el-button size="small" @click="addDataPoint">
                <el-icon><Plus /></el-icon>
                添加数据点
              </el-button>
              <el-button size="small" @click="importData">
                <el-icon><Upload /></el-icon>
                导入数据
              </el-button>
            </div>
          </div>
          
          <el-table :data="curveData" border style="width: 100%">
            <el-table-column prop="Q" label="流量 (m³/h)" width="120">
              <template #default="{ row, $index }">
                <el-input-number
                  v-model="row.Q"
                  :min="0"
                  :step="10"
                  size="small"
                  @change="validateDataPoint($index)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="H" label="扬程 (m)" width="120">
              <template #default="{ row, $index }">
                <el-input-number
                  v-model="row.H"
                  :min="0"
                  :step="0.1"
                  :precision="2"
                  size="small"
                  @change="validateDataPoint($index)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="ETA" label="效率 (%)" width="120">
              <template #default="{ row, $index }">
                <el-input-number
                  v-model="row.ETA"
                  :min="0"
                  :max="100"
                  :step="0.1"
                  :precision="1"
                  size="small"
                  @change="validateDataPoint($index)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="P" label="功率 (kW)" width="120">
              <template #default="{ row, $index }">
                <el-input-number
                  v-model="row.P"
                  :min="0"
                  :step="0.1"
                  :precision="1"
                  size="small"
                  @change="validateDataPoint($index)"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ $index }">
                <el-button
                  size="small"
                  type="danger"
                  @click="removeDataPoint($index)"
                  :disabled="curveData.length <= 3"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- 算法配置 -->
      <el-tab-pane label="算法配置" name="algorithm">
        <el-form :model="algorithmConfig" label-width="120px">
          <el-form-item label="拟合算法">
            <el-select v-model="algorithmConfig.algorithm.type" @change="onAlgorithmChange">
              <el-option label="最小二乘法" value="least-squares" />
              <el-option label="多项式拟合" value="polynomial" />
              <el-option label="神经网络" value="neural-network" />
              <el-option label="样条插值" value="spline" />
            </el-select>
          </el-form-item>
          
          <el-form-item v-if="showPolynomialOrder" label="多项式阶数">
            <el-input-number
              v-model="algorithmConfig.algorithm.order"
              :min="1"
              :max="10"
              :step="1"
            />
          </el-form-item>
          
          <template v-if="algorithmConfig.algorithm.type === 'neural-network'">
            <el-form-item label="训练轮数">
              <el-input-number
                v-model="algorithmConfig.algorithm.epochs"
                :min="100"
                :max="10000"
                :step="100"
              />
            </el-form-item>
            
            <el-form-item label="学习率">
              <el-input-number
                v-model="algorithmConfig.algorithm.learningRate"
                :min="0.001"
                :max="1"
                :step="0.001"
                :precision="3"
              />
            </el-form-item>
            
            <el-form-item label="隐藏层配置">
              <el-input
                v-model="hiddenLayersStr"
                placeholder="例如: 10,5 表示两个隐藏层，分别有10和5个神经元"
                @blur="updateHiddenLayers"
              />
            </el-form-item>
          </template>
          
          <el-form-item label="验证集比例">
            <el-slider
              v-model="algorithmConfig.validationSplit"
              :min="0.1"
              :max="0.5"
              :step="0.05"
              show-tooltip
              :format-tooltip="(val: number) => `${(val * 100).toFixed(0)}%`"
            />
          </el-form-item>
          
          <el-form-item label="交叉验证">
            <el-switch v-model="algorithmConfig.crossValidation" />
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <div class="footer-actions">
      <el-button @click="previewCurve" :loading="previewLoading">
        <el-icon><View /></el-icon>
        预览曲线
      </el-button>
      <el-button type="primary" @click="applyParameters" :loading="applyLoading">
        <el-icon><Check /></el-icon>
        应用参数
      </el-button>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Setting, Plus, Upload, View, Check } from '@element-plus/icons-vue'
import type { PumpParameters, PumpData, AlgorithmConfig, CurveFittingConfig } from '@/types'
import { ElMessage, ElMessageBox } from 'element-plus'

// Props & Emits
interface Props {
  modelValue?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'parameters-updated', params: PumpParameters, data: PumpData, config: CurveFittingConfig): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const activeTab = ref('basic')
const previewLoading = ref(false)
const applyLoading = ref(false)
const basicFormRef = ref()

// 水泵基础参数
const pumpParams = ref<PumpParameters>({
  name: '离心泵-001',
  model: 'IS100-80-160',
  ratedFlow: 1000,
  ratedHead: 40,
  ratedPower: 100,
  ratedSpeed: 2900,
  efficiency: 78,
  impellerDiameter: 160
})

// 曲线数据
const curveData = ref([
  { Q: 0, H: 44.28, ETA: 0, P: 76 },
  { Q: 200, H: 43.58, ETA: 28, P: 84 },
  { Q: 400, H: 42.35, ETA: 48, P: 91 },
  { Q: 600, H: 40.42, ETA: 63, P: 100 },
  { Q: 800, H: 37.25, ETA: 75, P: 105 },
  { Q: 1000, H: 33.56, ETA: 81, P: 108 },
  { Q: 1200, H: 29.17, ETA: 83, P: 109 },
  { Q: 1400, H: 23.72, ETA: 81, P: 107 }
])

// 算法配置
const algorithmConfig = ref<CurveFittingConfig>({
  algorithm: {
    type: 'least-squares',
    order: 2,
    epochs: 100,
    learningRate: 0.01,
    hiddenLayers: [10, 5]
  },
  validationSplit: 0.2,
  crossValidation: false
})

const hiddenLayersStr = ref('10,5')

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入水泵名称', trigger: 'blur' }],
  model: [{ required: true, message: '请输入型号', trigger: 'blur' }],
  ratedFlow: [{ required: true, message: '请输入额定流量', trigger: 'blur' }],
  ratedHead: [{ required: true, message: '请输入额定扬程', trigger: 'blur' }],
  ratedPower: [{ required: true, message: '请输入额定功率', trigger: 'blur' }],
  ratedSpeed: [{ required: true, message: '请输入额定转速', trigger: 'blur' }],
  efficiency: [{ required: true, message: '请输入额定效率', trigger: 'blur' }],
  impellerDiameter: [{ required: true, message: '请输入叶轮直径', trigger: 'blur' }]
}

// 计算属性
const showPolynomialOrder = computed(() => 
  ['least-squares', 'polynomial'].includes(algorithmConfig.value.algorithm.type)
)

// 方法
const addDataPoint = () => {
  const lastPoint = curveData.value[curveData.value.length - 1]
  curveData.value.push({
    Q: lastPoint.Q + 100,
    H: lastPoint.H - 2,
    ETA: Math.max(0, lastPoint.ETA - 5),
    P: lastPoint.P + 5
  })
}

const removeDataPoint = (index: number) => {
  if (curveData.value.length > 3) {
    curveData.value.splice(index, 1)
  }
}

const validateDataPoint = (index: number) => {
  const point = curveData.value[index]
  if (point.Q < 0 || point.H < 0 || point.ETA < 0 || point.ETA > 100 || point.P < 0) {
    ElMessage.warning('数据点参数超出合理范围')
  }
}

const importData = () => {
  ElMessage.info('数据导入功能开发中...')
}

const onAlgorithmChange = () => {
  if (algorithmConfig.value.algorithm.type === 'neural-network') {
    algorithmConfig.value.algorithm.epochs = 1000
    algorithmConfig.value.algorithm.learningRate = 0.01
    algorithmConfig.value.algorithm.hiddenLayers = [10, 5]
  } else if (algorithmConfig.value.algorithm.type === 'polynomial') {
    algorithmConfig.value.algorithm.order = 4
  }
}

const updateHiddenLayers = () => {
  try {
    const layers = hiddenLayersStr.value.split(',').map(s => parseInt(s.trim())).filter(n => !isNaN(n) && n > 0)
    if (layers.length > 0) {
      algorithmConfig.value.algorithm.hiddenLayers = layers
    }
  } catch (error) {
    ElMessage.warning('隐藏层配置格式错误')
  }
}

const resetToDefault = () => {
  ElMessageBox.confirm('确定要重置为默认参数吗？', '确认重置', {
    type: 'warning'
  }).then(() => {
    // 重置为默认值
    Object.assign(pumpParams.value, {
      name: '离心泵-001',
      model: 'IS100-80-160',
      ratedFlow: 1000,
      ratedHead: 40,
      ratedPower: 100,
      ratedSpeed: 2900,
      efficiency: 78,
      impellerDiameter: 160
    })
    
    curveData.value = [
      { Q: 0, H: 44.28, ETA: 0, P: 76 },
      { Q: 200, H: 43.58, ETA: 28, P: 84 },
      { Q: 400, H: 42.35, ETA: 48, P: 91 },
      { Q: 600, H: 40.42, ETA: 63, P: 100 },
      { Q: 800, H: 37.25, ETA: 75, P: 105 },
      { Q: 1000, H: 33.56, ETA: 81, P: 108 },
      { Q: 1200, H: 29.17, ETA: 83, P: 109 },
      { Q: 1400, H: 23.72, ETA: 81, P: 107 }
    ]
    
    ElMessage.success('已重置为默认参数')
  })
}

const saveParameters = () => {
  ElMessage.success('参数配置已保存')
}

const previewCurve = async () => {
  previewLoading.value = true
  try {
    // 模拟预览处理
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('曲线预览生成成功')
  } finally {
    previewLoading.value = false
  }
}

const applyParameters = async () => {
  // 验证基础参数
  const valid = await basicFormRef.value?.validate().catch(() => false)
  if (!valid) {
    activeTab.value = 'basic'
    return
  }
  
  // 验证曲线数据
  if (curveData.value.length < 3) {
    ElMessage.error('至少需要3个数据点')
    activeTab.value = 'curve'
    return
  }
  
  applyLoading.value = true
  try {
    // 转换数据格式
    const pumpData: PumpData = {
      Q: curveData.value.map(p => p.Q),
      H: curveData.value.map(p => p.H),
      ETA: curveData.value.map(p => p.ETA),
      P: curveData.value.map(p => p.P)
    }
    
    const config: CurveFittingConfig = {
      algorithm: algorithmConfig.value.algorithm,
      validationSplit: algorithmConfig.value.validationSplit,
      crossValidation: algorithmConfig.value.crossValidation
    }
    
    // 发送更新事件
    emit('parameters-updated', pumpParams.value, pumpData, config)
    
    ElMessage.success('参数应用成功')
  } finally {
    applyLoading.value = false
  }
}

// 监听隐藏层配置变化
watch(() => algorithmConfig.value.algorithm.hiddenLayers, (newLayers) => {
  hiddenLayersStr.value = newLayers?.join(',') || ''
}, { immediate: true })
</script>

<style lang="scss" scoped>
.pump-parameter-input {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    > div:first-child {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
    }
    
    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .unit {
    margin-left: 8px;
    color: var(--el-text-color-regular);
    font-size: 12px;
  }
  
  .curve-data-section {
    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      
      h4 {
        margin: 0;
        color: var(--el-text-color-primary);
      }
      
      .actions {
        display: flex;
        gap: 8px;
      }
    }
  }
  
  .footer-actions {
    margin-top: 20px;
    text-align: center;
    
    .el-button {
      margin: 0 8px;
    }
  }
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-table .el-input-number) {
  width: 100px;
}
</style>
