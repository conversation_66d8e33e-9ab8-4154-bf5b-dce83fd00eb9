# 多阶段构建
# 第一阶段：构建前端
FROM node:18-alpine AS frontend-builder

WORKDIR /app

# 复制package文件
COPY package*.json pnpm-lock.yaml ./

# 安装pnpm并安装依赖
RUN npm install -g pnpm && pnpm install

# 复制源代码并构建
COPY . .
RUN pnpm run build

# 第二阶段：构建Go服务器
FROM golang:1.21-alpine AS go-builder

WORKDIR /app

# 复制Go源文件
COPY server.go go.mod* go.sum* ./

# 如果没有go.mod，初始化模块
RUN if [ ! -f go.mod ]; then go mod init water-platform; fi

# 构建Go应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o server .

# 第三阶段：运行时镜像
FROM alpine:latest

# 安装必要的包
RUN apk --no-cache add ca-certificates tzdata

# 设置时区
ENV TZ=Asia/Shanghai

WORKDIR /root/

# 从构建阶段复制文件
COPY --from=go-builder /app/server .
COPY --from=frontend-builder /app/dist ./dist

# 暴露端口
EXPOSE 8080

# 设置环境变量
ENV GIN_MODE=release
ENV PORT=8080

# 启动命令
CMD ["./server"]