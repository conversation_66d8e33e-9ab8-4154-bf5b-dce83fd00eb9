!function(){var t={6173:function(t,e,n){var r;t.exports=(r=r||function(t,e){var r;if("undefined"!=typeof window&&window.crypto&&(r=window.crypto),"undefined"!=typeof self&&self.crypto&&(r=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(r=globalThis.crypto),!r&&"undefined"!=typeof window&&window.msCrypto&&(r=window.msCrypto),!r&&void 0!==n.g&&n.g.crypto&&(r=n.g.crypto),!r)try{r=n(2480)}catch(t){}var i=function(){if(r){if("function"==typeof r.getRandomValues)try{return r.getRandomValues(new Uint32Array(1))[0]}catch(t){}if("function"==typeof r.randomBytes)try{return r.randomBytes(4).readInt32LE()}catch(t){}}throw new Error("Native crypto module could not be used to get secure random number.")},o=Object.create||function(){function t(){}return function(e){var n;return t.prototype=e,n=new t,t.prototype=null,n}}(),a={},c=a.lib={},u=c.Base={extend:function(t){var e=o(this);return t&&e.mixIn(t),e.hasOwnProperty("init")&&this.init!==e.init||(e.init=function(){e.$super.init.apply(this,arguments)}),e.init.prototype=e,e.$super=this,e},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}},s=c.WordArray=u.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=null!=e?e:4*t.length},toString:function(t){return(t||f).stringify(this)},concat:function(t){var e=this.words,n=t.words,r=this.sigBytes,i=t.sigBytes;if(this.clamp(),r%4)for(var o=0;o<i;o++){var a=n[o>>>2]>>>24-o%4*8&255;e[r+o>>>2]|=a<<24-(r+o)%4*8}else for(var c=0;c<i;c+=4)e[r+c>>>2]=n[c>>>2];return this.sigBytes+=i,this},clamp:function(){var e=this.words,n=this.sigBytes;e[n>>>2]&=4294967295<<32-n%4*8,e.length=t.ceil(n/4)},clone:function(){var t=u.clone.call(this);return t.words=this.words.slice(0),t},random:function(t){for(var e=[],n=0;n<t;n+=4)e.push(i());return new s.init(e,t)}}),l=a.enc={},f=l.Hex={stringify:function(t){for(var e=t.words,n=t.sigBytes,r=[],i=0;i<n;i++){var o=e[i>>>2]>>>24-i%4*8&255;r.push((o>>>4).toString(16)),r.push((15&o).toString(16))}return r.join("")},parse:function(t){for(var e=t.length,n=[],r=0;r<e;r+=2)n[r>>>3]|=parseInt(t.substr(r,2),16)<<24-r%8*4;return new s.init(n,e/2)}},d=l.Latin1={stringify:function(t){for(var e=t.words,n=t.sigBytes,r=[],i=0;i<n;i++){var o=e[i>>>2]>>>24-i%4*8&255;r.push(String.fromCharCode(o))}return r.join("")},parse:function(t){for(var e=t.length,n=[],r=0;r<e;r++)n[r>>>2]|=(255&t.charCodeAt(r))<<24-r%4*8;return new s.init(n,e)}},h=l.Utf8={stringify:function(t){try{return decodeURIComponent(escape(d.stringify(t)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(t){return d.parse(unescape(encodeURIComponent(t)))}},p=c.BufferedBlockAlgorithm=u.extend({reset:function(){this._data=new s.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=h.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(e){var n,r=this._data,i=r.words,o=r.sigBytes,a=this.blockSize,c=o/(4*a),u=(c=e?t.ceil(c):t.max((0|c)-this._minBufferSize,0))*a,l=t.min(4*u,o);if(u){for(var f=0;f<u;f+=a)this._doProcessBlock(i,f);n=i.splice(0,u),r.sigBytes-=l}return new s.init(n,l)},clone:function(){var t=u.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0}),v=(c.Hasher=p.extend({cfg:u.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function(t){return function(e,n){return new t.init(n).finalize(e)}},_createHmacHelper:function(t){return function(e,n){return new v.HMAC.init(t,n).finalize(e)}}}),a.algo={});return a}(Math),r)},7219:function(t,e,n){var r;t.exports=(r=n(6173),function(t){var e=r,n=e.lib,i=n.WordArray,o=n.Hasher,a=e.algo,c=[],u=[];!function(){function e(e){for(var n=t.sqrt(e),r=2;r<=n;r++)if(!(e%r))return!1;return!0}function n(t){return 4294967296*(t-(0|t))|0}for(var r=2,i=0;i<64;)e(r)&&(i<8&&(c[i]=n(t.pow(r,.5))),u[i]=n(t.pow(r,1/3)),i++),r++}();var s=[],l=a.SHA256=o.extend({_doReset:function(){this._hash=new i.init(c.slice(0))},_doProcessBlock:function(t,e){for(var n=this._hash.words,r=n[0],i=n[1],o=n[2],a=n[3],c=n[4],l=n[5],f=n[6],d=n[7],h=0;h<64;h++){if(h<16)s[h]=0|t[e+h];else{var p=s[h-15],v=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,y=s[h-2],m=(y<<15|y>>>17)^(y<<13|y>>>19)^y>>>10;s[h]=v+s[h-7]+m+s[h-16]}var _=r&i^r&o^i&o,g=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),w=d+((c<<26|c>>>6)^(c<<21|c>>>11)^(c<<7|c>>>25))+(c&l^~c&f)+u[h]+s[h];d=f,f=l,l=c,c=a+w|0,a=o,o=i,i=r,r=w+(g+_)|0}n[0]=n[0]+r|0,n[1]=n[1]+i|0,n[2]=n[2]+o|0,n[3]=n[3]+a|0,n[4]=n[4]+c|0,n[5]=n[5]+l|0,n[6]=n[6]+f|0,n[7]=n[7]+d|0},_doFinalize:function(){var e=this._data,n=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;return n[i>>>5]|=128<<24-i%32,n[14+(i+64>>>9<<4)]=t.floor(r/4294967296),n[15+(i+64>>>9<<4)]=r,e.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var t=o.clone.call(this);return t._hash=this._hash.clone(),t}});e.SHA256=o._createHelper(l),e.HmacSHA256=o._createHmacHelper(l)}(Math),r.SHA256)},2244:function(t,e,n){"use strict";n.r(e);var r=function(t){var e=this.constructor;return this.then((function(n){return e.resolve(t()).then((function(){return n}))}),(function(n){return e.resolve(t()).then((function(){return e.reject(n)}))}))};var i=function(t){return new this((function(e,n){if(!t||void 0===t.length)return n(new TypeError(typeof t+" "+t+" is not iterable(cannot read property Symbol(Symbol.iterator))"));var r=Array.prototype.slice.call(t);if(0===r.length)return e([]);var i=r.length;function o(t,n){if(n&&("object"==typeof n||"function"==typeof n)){var a=n.then;if("function"==typeof a)return void a.call(n,(function(e){o(t,e)}),(function(n){r[t]={status:"rejected",reason:n},0==--i&&e(r)}))}r[t]={status:"fulfilled",value:n},0==--i&&e(r)}for(var a=0;a<r.length;a++)o(a,r[a])}))},o=setTimeout;function a(t){return Boolean(t&&void 0!==t.length)}function c(){}function u(t){if(!(this instanceof u))throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],p(t,this)}function s(t,e){for(;3===t._state;)t=t._value;0!==t._state?(t._handled=!0,u._immediateFn((function(){var n=1===t._state?e.onFulfilled:e.onRejected;if(null!==n){var r;try{r=n(t._value)}catch(t){return void f(e.promise,t)}l(e.promise,r)}else(1===t._state?l:f)(e.promise,t._value)}))):t._deferreds.push(e)}function l(t,e){try{if(e===t)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var n=e.then;if(e instanceof u)return t._state=3,t._value=e,void d(t);if("function"==typeof n)return void p((r=n,i=e,function(){r.apply(i,arguments)}),t)}t._state=1,t._value=e,d(t)}catch(e){f(t,e)}var r,i}function f(t,e){t._state=2,t._value=e,d(t)}function d(t){2===t._state&&0===t._deferreds.length&&u._immediateFn((function(){t._handled||u._unhandledRejectionFn(t._value)}));for(var e=0,n=t._deferreds.length;e<n;e++)s(t,t._deferreds[e]);t._deferreds=null}function h(t,e,n){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof e?e:null,this.promise=n}function p(t,e){var n=!1;try{t((function(t){n||(n=!0,l(e,t))}),(function(t){n||(n=!0,f(e,t))}))}catch(t){if(n)return;n=!0,f(e,t)}}u.prototype.catch=function(t){return this.then(null,t)},u.prototype.then=function(t,e){var n=new this.constructor(c);return s(this,new h(t,e,n)),n},u.prototype.finally=r,u.all=function(t){return new u((function(e,n){if(!a(t))return n(new TypeError("Promise.all accepts an array"));var r=Array.prototype.slice.call(t);if(0===r.length)return e([]);var i=r.length;function o(t,a){try{if(a&&("object"==typeof a||"function"==typeof a)){var c=a.then;if("function"==typeof c)return void c.call(a,(function(e){o(t,e)}),n)}r[t]=a,0==--i&&e(r)}catch(t){n(t)}}for(var c=0;c<r.length;c++)o(c,r[c])}))},u.allSettled=i,u.resolve=function(t){return t&&"object"==typeof t&&t.constructor===u?t:new u((function(e){e(t)}))},u.reject=function(t){return new u((function(e,n){n(t)}))},u.race=function(t){return new u((function(e,n){if(!a(t))return n(new TypeError("Promise.race accepts an array"));for(var r=0,i=t.length;r<i;r++)u.resolve(t[r]).then(e,n)}))},u._immediateFn="function"==typeof setImmediate&&function(t){setImmediate(t)}||function(t){o(t,0)},u._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console};var v=u,y=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==n.g)return n.g;throw new Error("unable to locate global object")}();"function"!=typeof y.Promise?y.Promise=v:(y.Promise.prototype.finally||(y.Promise.prototype.finally=r),y.Promise.allSettled||(y.Promise.allSettled=i))},7658:function(t){var e=function(t){"use strict";var e=Object.prototype,n=e.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",o=r.asyncIterator||"@@asyncIterator",a=r.toStringTag||"@@toStringTag";function c(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,n){return t[e]=n}}function u(t,e,n,r){var i=e&&e.prototype instanceof f?e:f,o=Object.create(i.prototype),a=new E(r||[]);return o._invoke=function(t,e,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return C()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var c=w(a,n);if(c){if(c===l)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var u=s(t,e,n);if("normal"===u.type){if(r=n.done?"completed":"suspendedYield",u.arg===l)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(r="completed",n.method="throw",n.arg=u.arg)}}}(t,n,a),o}function s(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var l={};function f(){}function d(){}function h(){}var p={};c(p,i,(function(){return this}));var v=Object.getPrototypeOf,y=v&&v(v(O([])));y&&y!==e&&n.call(y,i)&&(p=y);var m=h.prototype=f.prototype=Object.create(p);function _(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function g(t,e){var r;this._invoke=function(i,o){function a(){return new e((function(r,a){!function r(i,o,a,c){var u=s(t[i],t,o);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==typeof f&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,c)}),(function(t){r("throw",t,a,c)})):e.resolve(f).then((function(t){l.value=t,a(l)}),(function(t){return r("throw",t,a,c)}))}c(u.arg)}(i,o,r,a)}))}return r=r?r.then(a,a):a()}}function w(t,e){var n=t.iterator[e.method];if(void 0===n){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,w(t,e),"throw"===e.method))return l;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return l}var r=s(n,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,l;var i=r.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,l):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,l)}function b(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(b,this),this.reset(!0)}function O(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,o=function e(){for(;++r<t.length;)if(n.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:C}}function C(){return{value:void 0,done:!0}}return d.prototype=h,c(m,"constructor",h),c(h,"constructor",d),d.displayName=c(h,a,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===d||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,c(t,a,"GeneratorFunction")),t.prototype=Object.create(m),t},t.awrap=function(t){return{__await:t}},_(g.prototype),c(g.prototype,o,(function(){return this})),t.AsyncIterator=g,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var a=new g(u(e,n,r,i),o);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},_(m),c(m,a,"Generator"),c(m,i,(function(){return this})),c(m,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var n in t)e.push(n);return e.reverse(),function n(){for(;e.length;){var r=e.pop();if(r in t)return n.value=r,n.done=!1,n}return n.done=!0,n}},t.values=O,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(P),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(n,r){return a.type="throw",a.arg=t,e.next=n,r&&(e.method="next",e.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),u=n.call(o,"finallyLoc");if(c&&u){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),P(n),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;P(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:O(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),l}},t}(t.exports);try{regeneratorRuntime=e}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=e:Function("r","regeneratorRuntime = r")(e)}},5494:function(t,e,n){"use strict";var r;n.r(e),n.d(e,{NIL:function(){return j},parse:function(){return y},stringify:function(){return d},v1:function(){return v},v3:function(){return C},v4:function(){return A},v5:function(){return I},validate:function(){return c},version:function(){return D}});var i=new Uint8Array(16);function o(){if(!r&&!(r="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return r(i)}var a=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;for(var c=function(t){return"string"==typeof t&&a.test(t)},u=[],s=0;s<256;++s)u.push((s+256).toString(16).substr(1));var l,f,d=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(u[t[e+0]]+u[t[e+1]]+u[t[e+2]]+u[t[e+3]]+"-"+u[t[e+4]]+u[t[e+5]]+"-"+u[t[e+6]]+u[t[e+7]]+"-"+u[t[e+8]]+u[t[e+9]]+"-"+u[t[e+10]]+u[t[e+11]]+u[t[e+12]]+u[t[e+13]]+u[t[e+14]]+u[t[e+15]]).toLowerCase();if(!c(n))throw TypeError("Stringified UUID is invalid");return n},h=0,p=0;var v=function(t,e,n){var r=e&&n||0,i=e||new Array(16),a=(t=t||{}).node||l,c=void 0!==t.clockseq?t.clockseq:f;if(null==a||null==c){var u=t.random||(t.rng||o)();null==a&&(a=l=[1|u[0],u[1],u[2],u[3],u[4],u[5]]),null==c&&(c=f=16383&(u[6]<<8|u[7]))}var s=void 0!==t.msecs?t.msecs:Date.now(),v=void 0!==t.nsecs?t.nsecs:p+1,y=s-h+(v-p)/1e4;if(y<0&&void 0===t.clockseq&&(c=c+1&16383),(y<0||s>h)&&void 0===t.nsecs&&(v=0),v>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");h=s,p=v,f=c;var m=(1e4*(268435455&(s+=122192928e5))+v)%4294967296;i[r++]=m>>>24&255,i[r++]=m>>>16&255,i[r++]=m>>>8&255,i[r++]=255&m;var _=s/4294967296*1e4&268435455;i[r++]=_>>>8&255,i[r++]=255&_,i[r++]=_>>>24&15|16,i[r++]=_>>>16&255,i[r++]=c>>>8|128,i[r++]=255&c;for(var g=0;g<6;++g)i[r+g]=a[g];return e||d(i)};var y=function(t){if(!c(t))throw TypeError("Invalid UUID");var e,n=new Uint8Array(16);return n[0]=(e=parseInt(t.slice(0,8),16))>>>24,n[1]=e>>>16&255,n[2]=e>>>8&255,n[3]=255&e,n[4]=(e=parseInt(t.slice(9,13),16))>>>8,n[5]=255&e,n[6]=(e=parseInt(t.slice(14,18),16))>>>8,n[7]=255&e,n[8]=(e=parseInt(t.slice(19,23),16))>>>8,n[9]=255&e,n[10]=(e=parseInt(t.slice(24,36),16))/1099511627776&255,n[11]=e/4294967296&255,n[12]=e>>>24&255,n[13]=e>>>16&255,n[14]=e>>>8&255,n[15]=255&e,n};function m(t,e,n){function r(t,r,i,o){if("string"==typeof t&&(t=function(t){t=unescape(encodeURIComponent(t));for(var e=[],n=0;n<t.length;++n)e.push(t.charCodeAt(n));return e}(t)),"string"==typeof r&&(r=y(r)),16!==r.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var a=new Uint8Array(16+t.length);if(a.set(r),a.set(t,r.length),(a=n(a))[6]=15&a[6]|e,a[8]=63&a[8]|128,i){o=o||0;for(var c=0;c<16;++c)i[o+c]=a[c];return i}return d(a)}try{r.name=t}catch(t){}return r.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",r.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",r}function _(t){return 14+(t+64>>>9<<4)+1}function g(t,e){var n=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(n>>16)<<16|65535&n}function w(t,e,n,r,i,o){return g((a=g(g(e,t),g(r,o)))<<(c=i)|a>>>32-c,n);var a,c}function b(t,e,n,r,i,o,a){return w(e&n|~e&r,t,e,i,o,a)}function P(t,e,n,r,i,o,a){return w(e&r|n&~r,t,e,i,o,a)}function E(t,e,n,r,i,o,a){return w(e^n^r,t,e,i,o,a)}function O(t,e,n,r,i,o,a){return w(n^(e|~r),t,e,i,o,a)}var C=m("v3",48,(function(t){if("string"==typeof t){var e=unescape(encodeURIComponent(t));t=new Uint8Array(e.length);for(var n=0;n<e.length;++n)t[n]=e.charCodeAt(n)}return function(t){for(var e=[],n=32*t.length,r=0;r<n;r+=8){var i=t[r>>5]>>>r%32&255,o=parseInt("0123456789abcdef".charAt(i>>>4&15)+"0123456789abcdef".charAt(15&i),16);e.push(o)}return e}(function(t,e){t[e>>5]|=128<<e%32,t[_(e)-1]=e;for(var n=1732584193,r=-271733879,i=-1732584194,o=271733878,a=0;a<t.length;a+=16){var c=n,u=r,s=i,l=o;n=b(n,r,i,o,t[a],7,-680876936),o=b(o,n,r,i,t[a+1],12,-389564586),i=b(i,o,n,r,t[a+2],17,606105819),r=b(r,i,o,n,t[a+3],22,-1044525330),n=b(n,r,i,o,t[a+4],7,-176418897),o=b(o,n,r,i,t[a+5],12,1200080426),i=b(i,o,n,r,t[a+6],17,-1473231341),r=b(r,i,o,n,t[a+7],22,-45705983),n=b(n,r,i,o,t[a+8],7,1770035416),o=b(o,n,r,i,t[a+9],12,-1958414417),i=b(i,o,n,r,t[a+10],17,-42063),r=b(r,i,o,n,t[a+11],22,-1990404162),n=b(n,r,i,o,t[a+12],7,1804603682),o=b(o,n,r,i,t[a+13],12,-40341101),i=b(i,o,n,r,t[a+14],17,-1502002290),r=b(r,i,o,n,t[a+15],22,1236535329),n=P(n,r,i,o,t[a+1],5,-165796510),o=P(o,n,r,i,t[a+6],9,-1069501632),i=P(i,o,n,r,t[a+11],14,643717713),r=P(r,i,o,n,t[a],20,-373897302),n=P(n,r,i,o,t[a+5],5,-701558691),o=P(o,n,r,i,t[a+10],9,38016083),i=P(i,o,n,r,t[a+15],14,-660478335),r=P(r,i,o,n,t[a+4],20,-405537848),n=P(n,r,i,o,t[a+9],5,568446438),o=P(o,n,r,i,t[a+14],9,-1019803690),i=P(i,o,n,r,t[a+3],14,-187363961),r=P(r,i,o,n,t[a+8],20,1163531501),n=P(n,r,i,o,t[a+13],5,-1444681467),o=P(o,n,r,i,t[a+2],9,-51403784),i=P(i,o,n,r,t[a+7],14,1735328473),r=P(r,i,o,n,t[a+12],20,-1926607734),n=E(n,r,i,o,t[a+5],4,-378558),o=E(o,n,r,i,t[a+8],11,-2022574463),i=E(i,o,n,r,t[a+11],16,1839030562),r=E(r,i,o,n,t[a+14],23,-35309556),n=E(n,r,i,o,t[a+1],4,-1530992060),o=E(o,n,r,i,t[a+4],11,1272893353),i=E(i,o,n,r,t[a+7],16,-155497632),r=E(r,i,o,n,t[a+10],23,-1094730640),n=E(n,r,i,o,t[a+13],4,681279174),o=E(o,n,r,i,t[a],11,-358537222),i=E(i,o,n,r,t[a+3],16,-722521979),r=E(r,i,o,n,t[a+6],23,76029189),n=E(n,r,i,o,t[a+9],4,-640364487),o=E(o,n,r,i,t[a+12],11,-421815835),i=E(i,o,n,r,t[a+15],16,530742520),r=E(r,i,o,n,t[a+2],23,-995338651),n=O(n,r,i,o,t[a],6,-198630844),o=O(o,n,r,i,t[a+7],10,1126891415),i=O(i,o,n,r,t[a+14],15,-1416354905),r=O(r,i,o,n,t[a+5],21,-57434055),n=O(n,r,i,o,t[a+12],6,1700485571),o=O(o,n,r,i,t[a+3],10,-1894986606),i=O(i,o,n,r,t[a+10],15,-1051523),r=O(r,i,o,n,t[a+1],21,-2054922799),n=O(n,r,i,o,t[a+8],6,1873313359),o=O(o,n,r,i,t[a+15],10,-30611744),i=O(i,o,n,r,t[a+6],15,-1560198380),r=O(r,i,o,n,t[a+13],21,1309151649),n=O(n,r,i,o,t[a+4],6,-145523070),o=O(o,n,r,i,t[a+11],10,-1120210379),i=O(i,o,n,r,t[a+2],15,718787259),r=O(r,i,o,n,t[a+9],21,-343485551),n=g(n,c),r=g(r,u),i=g(i,s),o=g(o,l)}return[n,r,i,o]}(function(t){if(0===t.length)return[];for(var e=8*t.length,n=new Uint32Array(_(e)),r=0;r<e;r+=8)n[r>>5]|=(255&t[r/8])<<r%32;return n}(t),8*t.length))}));var A=function(t,e,n){var r=(t=t||{}).random||(t.rng||o)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,e){n=n||0;for(var i=0;i<16;++i)e[n+i]=r[i];return e}return d(r)};function T(t,e,n,r){switch(t){case 0:return e&n^~e&r;case 1:return e^n^r;case 2:return e&n^e&r^n&r;case 3:return e^n^r}}function x(t,e){return t<<e|t>>>32-e}var I=m("v5",80,(function(t){var e=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"==typeof t){var r=unescape(encodeURIComponent(t));t=[];for(var i=0;i<r.length;++i)t.push(r.charCodeAt(i))}else Array.isArray(t)||(t=Array.prototype.slice.call(t));t.push(128);for(var o=t.length/4+2,a=Math.ceil(o/16),c=new Array(a),u=0;u<a;++u){for(var s=new Uint32Array(16),l=0;l<16;++l)s[l]=t[64*u+4*l]<<24|t[64*u+4*l+1]<<16|t[64*u+4*l+2]<<8|t[64*u+4*l+3];c[u]=s}c[a-1][14]=8*(t.length-1)/Math.pow(2,32),c[a-1][14]=Math.floor(c[a-1][14]),c[a-1][15]=8*(t.length-1)&4294967295;for(var f=0;f<a;++f){for(var d=new Uint32Array(80),h=0;h<16;++h)d[h]=c[f][h];for(var p=16;p<80;++p)d[p]=x(d[p-3]^d[p-8]^d[p-14]^d[p-16],1);for(var v=n[0],y=n[1],m=n[2],_=n[3],g=n[4],w=0;w<80;++w){var b=Math.floor(w/20),P=x(v,5)+T(b,y,m,_)+g+e[b]+d[w]>>>0;g=_,_=m,m=x(y,30)>>>0,y=v,v=P}n[0]=n[0]+v>>>0,n[1]=n[1]+y>>>0,n[2]=n[2]+m>>>0,n[3]=n[3]+_>>>0,n[4]=n[4]+g>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]})),j="00000000-0000-0000-0000-000000000000";var D=function(t){if(!c(t))throw TypeError("Invalid UUID");return parseInt(t.substr(14,1),16)}},6527:function(t,e,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}),i=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)"default"!==n&&Object.prototype.hasOwnProperty.call(t,n)&&r(e,t,n);return i(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.UWT=e.SET=e.OneTag=void 0;var a=o(n(6057)),c=o(n(510));e.OneTag=c;var u=o(n(3324));e.SET=u;var s=o(n(1454));e.UWT=s;var l={track:a.track_DEPRECATED,trackPid:u.trackPid,buildPixel:a.buildPixel_DEPRECATED,buildScript:a.buildScript_DEPRECATED,buildIframe:a.buildIframe_DEPRECATED};e.default=l},6057:function(t,e,n){"use strict";var r=this&&this.__assign||function(){return(r=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},i=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}),o=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),a=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)"default"!==n&&Object.prototype.hasOwnProperty.call(t,n)&&i(e,t,n);return o(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.track_DEPRECATED=e.buildScript_DEPRECATED=e.buildPixel_DEPRECATED=e.buildIframe_DEPRECATED=void 0;var c=a(n(1952)),u=a(n(8352)),s=a(n(3257)),l=n(4654);e.track_DEPRECATED=function(t,e,n){if(!t)throw new Error(l.utilities.LogPrefix+": Cannot track event without pixel ID");if(!e)throw new Error(l.utilities.LogPrefix+": Cannot track event without event name");var i=new c.AccountParams({merch_id:t}),o=new c.EventParams(r({merch_id:t,events:e,eci:l.utilities.EventCodeImpl.DEPRECATED_TRACK},n?{value:n.toString()}:{}));s.track({accountParams:i,eventParams:o,adsApiVersion:l.utilities.AdsApiVersion.v0})};e.buildPixel_DEPRECATED=function(t){u.buildImagePixel(l.utilities.addQueries(t,{bci:c.globalParams.get().bci,eci:l.utilities.EventCodeImpl.DEPRECATED_BUILD_PIXEL}))};e.buildScript_DEPRECATED=function(t){u.buildScriptPixel(l.utilities.addQueries(t,{bci:c.globalParams.get().bci,eci:l.utilities.EventCodeImpl.DEPRECATED_BUILD_SCRIPT}))};e.buildIframe_DEPRECATED=function(t){u.buildIFramePixel(l.utilities.addQueries(t,{bci:c.globalParams.get().bci,eci:l.utilities.EventCodeImpl.DEPRECATED_BUILD_IFRAME}))}},510:function(t,e,n){"use strict";var r=this&&this.__assign||function(){return(r=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},i=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}),o=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),a=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)"default"!==n&&Object.prototype.hasOwnProperty.call(t,n)&&i(e,t,n);return o(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.set=e.event=e.config=void 0;var c=a(n(1952)),u=a(n(3257)),s=n(4654),l=a(n(1454)),f={};e.set=function(t){void 0===t&&(t={}),c.globalParams.set(t||{})};e.config=function(t,e){if(void 0===e&&(e={}),!t||"string"!=typeof t)throw new Error(s.utilities.LogPrefix+": Config is missing required Advertiser Id");e=e&&s.utilities.isObject(e)?e:{};var n=f[t]=new c.AccountParams(r(r({},e),{txn_id:t}));u.init(n),c.globalParams.calledConfig();var i=s.utilities.splitObjectByPropNames(e,c.NonEventParameterKeys),o=i[0],a=i[1],d=new c.EventParams(r(r({},o),{txn_id:t,event:JSON.stringify(a),eci:s.utilities.EventCodeImpl.ONETAG_CONFIG}));u.track({accountParams:n,eventParams:d,adsApiVersion:s.utilities.AdsApiVersion.v1}),l.setDefaultAccountParams(t)};e.event=function(t,e){if(void 0===e&&(e={}),!t)throw new Error(s.utilities.LogPrefix+": Event cannot send event without an Event Code Id");e=e&&s.utilities.isObject(e)?e:{};var n,i=s.utilities.parseEventCodeId(t),o=i[0];i[1];o&&(n=f[o]||new c.AccountParams({txn_id:o}),u.init(n));var a=s.utilities.splitObjectByPropNames(e,c.NonEventParameterKeys),l=a[0],d=a[1],h=new c.EventParams(r(r({},l),{txn_id:t,event:JSON.stringify(d),eci:s.utilities.EventCodeImpl.ONETAG_EVENT}));u.track({accountParams:n,eventParams:h,adsApiVersion:s.utilities.AdsApiVersion.v1})}},3324:function(t,e,n){"use strict";var r=this&&this.__assign||function(){return(r=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},i=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}),o=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),a=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)"default"!==n&&Object.prototype.hasOwnProperty.call(t,n)&&i(e,t,n);return o(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.trackPid=void 0;var c=a(n(1952)),u=a(n(3257)),s=n(4654);e.trackPid=function(t,e){if(void 0===e&&(e={}),!t)throw new Error(s.utilities.LogPrefix+": No Pixel ID Found");e=e||{};var n=new c.AccountParams({txn_id:t}),i=new c.EventParams(r(r(r({},s.utilities.getLegacyParams(e)),e),{txn_id:t,eci:s.utilities.EventCodeImpl.SET_TRACK_PID}));u.track({accountParams:n,eventParams:i,adsApiVersion:s.utilities.AdsApiVersion.v0})}},1454:function(t,e,n){"use strict";var r=this&&this.__assign||function(){return(r=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},i=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}),o=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),a=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)"default"!==n&&Object.prototype.hasOwnProperty.call(t,n)&&i(e,t,n);return o(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.track=e.setDefaultAccountParams=e.init=e.defaultAccountParams=void 0;var c,u=a(n(1952)),s=a(n(3257)),l=n(4654);e.defaultAccountParams=c;var f=function(t){e.defaultAccountParams=c=new u.AccountParams({txn_id:t}),s.init(c)};e.setDefaultAccountParams=f;e.init=function(t){if(!t||"string"!=typeof t)throw new Error(l.utilities.LogPrefix+": Init is missing required Pixel Id");f(t),u.globalParams.calledInit()};e.track=function(t,e){if(void 0===e&&(e={}),!t||"string"!=typeof t)throw new Error(l.utilities.LogPrefix+": Track is missing required event name");if(!c.getPixelId())throw new Error(l.utilities.LogPrefix+": No Pixel ID Found");e=e&&l.utilities.isObject(e)?e:{},t=t.toLowerCase().trim();var n=l.utilities.splitObjectByPropNames(e,u.NonEventParameterKeys),i=n[0],o=n[1],a=new u.EventParams(r(r(r({},i),l.utilities.getLegacyParams(e)),{txn_id:c.getPixelId(),events:JSON.stringify([[t,o]]),eci:l.utilities.EventCodeImpl.UWT_TRACK}));s.track({accountParams:c,eventParams:a,adsApiVersion:l.utilities.AdsApiVersion.v0})}},1952:function(t,e,n){"use strict";var r,i=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),o=this&&this.__assign||function(){return(o=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.setLocation=e.setIframe=e.Parameters=e.NonEventParameterKeys=e.merge=e.globalParams=e.GlobalParams=e.EventParams=e.AccountParams=void 0;var a=n(4654),c=["email_address","phone_number","external_id"],u=["page_location","hide_page_location"],s=u.concat(c).concat(["bci","eci","event_id","event","events","oct_p_id","p_id","p_user_id","pl_id","restricted_data_use","tw_acc_response","tw_clid_src","twclid","tw_document_href","tw_document_referrer","tw_iframe_status","tw_order_quantity","tw_sale_amount","tw_product_id","txn_id","type","version"]);e.NonEventParameterKeys=s;var l=function(){function t(t){this.paramKVs={},this.set(t)}return t.prototype.set=function(t){var e=a.utilities.splitObjectByPropNames(t||{},c),n=e[0],r=e[1],i=Object.keys(n).reduce((function(t,e){return t[e.trim().toLowerCase()]=a.utilities.hashParameter(n[e]),t}),{});a.utilities.mergeObjects(this.paramKVs,i,r)},t.prototype.get=function(){return o({},this.paramKVs)},t.prototype.getPixelId=function(){var t,e,n=(null===(t=this.paramKVs)||void 0===t?void 0:t.txn_id)||(null===(e=this.paramKVs)||void 0===e?void 0:e.merch_id);if(!n)throw new Error(a.utilities.LogPrefix+": Pixel Id doesn't exist.");return n},t}();e.Parameters=l;var f=function(t){function e(){var e,n=t.call(this,{pl_id:a.utilities.generatePageLoadId(),type:"javascript",version:"2.3.33",p_id:"Twitter",p_user_id:"0",integration:(null===(e=window.twq)||void 0===e?void 0:e.integration)||"advertiser"})||this;return n.bciLoader=window.twq?2:1,n.bciCmd=0,n}return i(e,t),e.prototype.calledInit=function(){this.bciCmd|=1},e.prototype.calledConfig=function(){this.bciCmd|=2},e.prototype.get=function(){return o({bci:this.bciLoader+this.bciCmd},this.paramKVs)},e}(l);e.GlobalParams=f;var d=function(t){function e(e){return t.call(this,e)||this}return i(e,t),e}(l);e.AccountParams=d;var h=function(t){function e(e){return t.call(this,o(o({eci:a.utilities.EventCodeImpl.UNKNOWN},e),{event_id:a.utilities.generateEventId()}))||this}return i(e,t),e}(l);e.EventParams=h;var p=new f;function v(t){var e=a.environment.isInIFrame();t.tw_iframe_status=Number(e).toString(),e&&""!==document.referrer&&(t.tw_document_referrer=document.referrer)}function y(t,e){e.tw_document_href=location.href;var n=t.hide_page_location?"":t.page_location;void 0!==n&&(e.tw_document_href=n,e.tw_document_referrer&&(e.tw_document_referrer=n))}e.globalParams=p,e.setIframe=v,e.setLocation=y,e.merge=function(t){var e=t.accountParams,n=t.eventParams,r=a.utilities.mergeObjects({},p.get(),(null==e?void 0:e.get())||{},n.get()),i=a.utilities.splitObjectByPropNames(r,u),o=i[0],c=i[1];return v(c),y(o,c),c}},8352:function(t,e,n){"use strict";var r=this&&this.__assign||function(){return(r=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},i=this&&this.__awaiter||function(t,e,n,r){return new(n||(n=Promise))((function(i,o){function a(t){try{u(r.next(t))}catch(t){o(t)}}function c(t){try{u(r.throw(t))}catch(t){o(t)}}function u(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,c)}u((r=r.apply(t,e||[])).next())}))},o=this&&this.__generator||function(t,e){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function c(o){return function(c){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=e.call(t,a)}catch(t){o=[6,t],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,c])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.TWITTER_BASE=e.trackPid=e.TCO_BASE=e.buildScriptPixel=e.buildImagePixel=e.buildIFramePixel=void 0;var a=n(4654),c=function(t){return void 0===t&&(t=a.utilities.AdsApiVersion.v0),"https://t.co/"+t+"/adsct"};e.TCO_BASE=c;var u=/^(\S+\.)?x\.com/.test(document.location.host)?"analytics.x.com":"analytics.twitter.com",s=function(t){return void 0===t&&(t=a.utilities.AdsApiVersion.v0),"https://"+u+"/"+t+"/adsct"};e.TWITTER_BASE=s;e.trackPid=function(t,e){return e=r({adsApiVersion:a.utilities.AdsApiVersion.v1,needResponse:!1},e),l(c(e.adsApiVersion),t),l(s(e.adsApiVersion),t,e)};var l=function(t,e,n){return void 0===e&&(e={}),void 0===n&&(n={needResponse:!1}),i(void 0,void 0,void 0,(function(){var i,c,u;return o(this,(function(o){switch(o.label){case 0:return i=r({},e),c=Object.keys(i).sort().map((function(t){return t+"="+encodeURIComponent(i[t])})).join("&"),u=t+"?"+c,[4,a.environment.documentVisible()];case 1:return o.sent(),[2,n.needResponse?a.fetch(u):f(u)]}}))}))};e.buildScriptPixel=function(t){return i(void 0,void 0,void 0,(function(){var e;return o(this,(function(n){switch(n.label){case 0:return(e=document.createElement("script")).src=t,e.setAttribute("type","text/javascript"),[4,a.environment.contentLoaded()];case 1:return n.sent(),document.body.appendChild(e),[2]}}))}))};e.buildIFramePixel=function(t){return i(void 0,void 0,void 0,(function(){var e;return o(this,(function(n){switch(n.label){case 0:return(e=document.createElement("iframe")).src=t,e.hidden=!0,[4,a.environment.contentLoaded()];case 1:return n.sent(),document.body.appendChild(e),[2]}}))}))};var f=function(t){return i(void 0,void 0,void 0,(function(){var e;return o(this,(function(n){switch(n.label){case 0:return(e=new Image).src=t,e.height=1,e.width=1,e.style.display="none",[4,a.environment.contentLoaded()];case 1:return n.sent(),document.body.appendChild(e),[2]}}))}))};e.buildImagePixel=f},3257:function(t,e,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}),i=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)"default"!==n&&Object.prototype.hasOwnProperty.call(t,n)&&r(e,t,n);return i(e,t),e},a=this&&this.__awaiter||function(t,e,n,r){return new(n||(n=Promise))((function(i,o){function a(t){try{u(r.next(t))}catch(t){o(t)}}function c(t){try{u(r.throw(t))}catch(t){o(t)}}function u(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,c)}u((r=r.apply(t,e||[])).next())}))},c=this&&this.__generator||function(t,e){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function c(o){return function(c){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=e.call(t,a)}catch(t){o=[6,t],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,c])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.track=e.init=void 0;var u=o(n(1952)),s=o(n(8352)),l=n(4654),f=o(n(1304));e.init=function(t){},e.track=function(t){var e=t.accountParams,n=t.eventParams,r=t.adsApiVersion;return a(this,void 0,void 0,(function(){var t,i,o,a,d;return c(this,(function(c){switch(c.label){case 0:return[4,Promise.resolve()];case 1:return c.sent(),t=!1,e&&(i=l.twclid.getTwclidParams(e.getPixelId()),t=1===i.tw_acc_response,n.set(i)),o=f.getDeviceSettings(),n.set(o),a=u.merge({accountParams:e,eventParams:n}),d=s.trackPid(a,{adsApiVersion:r,needResponse:t}),t&&l.twclid.cacheAccRequestAndSaveTwclid(null==e?void 0:e.getPixelId(),d),[2]}}))}))}},2345:function(t,e,n){"use strict";var r=this&&this.__awaiter||function(t,e,n,r){return new(n||(n=Promise))((function(i,o){function a(t){try{u(r.next(t))}catch(t){o(t)}}function c(t){try{u(r.throw(t))}catch(t){o(t)}}function u(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,c)}u((r=r.apply(t,e||[])).next())}))},i=this&&this.__generator||function(t,e){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function c(o){return function(c){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=e.call(t,a)}catch(t){o=[6,t],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,c])}}};Object.defineProperty(e,"__esModule",{value:!0});var o=n(6527),a=n(4654);!function(){if(window.twq){var t=window.twq;t.exe=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return r(this,void 0,void 0,(function(){var e,n,r,c;return i(this,(function(i){switch(i.label){case 0:switch(e=t[0],n=t.slice(1),e){case"init":r=o.UWT.init;break;case"track":r=o.UWT.track;break;case"config":r=o.OneTag.config;break;case"event":r=o.OneTag.event;break;case"set":r=o.OneTag.set}i.label=1;case 1:return i.trys.push([1,4,,5]),"function"!=typeof r?[3,3]:[4,r.apply(null,n)];case 2:i.sent(),i.label=3;case 3:return[3,5];case 4:return c=i.sent(),a.utilities.logError(c),[3,5];case 5:return[2]}}))}))};for(var e=0;e<t.queue.length;e++)t.exe.apply(null,t.queue[e])}}()},2735:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var i=r(n(6527));window.twttr=window.twttr||{},window.twttr.conversion||(window.twttr.conversion=i.default,n(2345))},6575:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Cookies=void 0;var r=n(3532),i=function(){function t(){}return t.setCookie=function(e,n,i){if(void 0===i&&(i={}),!e)throw new Error(r.LogPrefix+" Missing key for "+e+" cookie");if(!n)throw new Error(r.LogPrefix+" Missing value for "+e+" cookie");var o=e+"="+encodeURIComponent(n)+t.convertCookieOptionsToString(i);document.cookie=o},t.getCookie=function(t){if(!t)throw new Error(r.LogPrefix+": getCookie is missing cookieName");var e=document.cookie.split(";").reduce((function(t,e){var n=e.split("=").map((function(t){return t.trim()})),r=n[0],i=n[1];return t[r]=decodeURIComponent(i),t}),{});return r.hasOwnPropertyCi(e,t)?e[t]:void 0},t.convertCookieOptionsToString=function(t){if(!t)throw new Error(r.LogPrefix+": convertCookieOptionsToString is missing options");var e="";for(var n in t)r.hasOwnPropertyCi(t,n)&&("secure"===n&&t[n]?e+=";"+n:e+=";"+n+"="+t[n]);return r.hasOwnPropertyCi(t,"path")||(e+=";path=/"),e},t}();e.Cookies=i},618:function(t,e){"use strict";var n=this&&this.__awaiter||function(t,e,n,r){return new(n||(n=Promise))((function(i,o){function a(t){try{u(r.next(t))}catch(t){o(t)}}function c(t){try{u(r.throw(t))}catch(t){o(t)}}function u(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,c)}u((r=r.apply(t,e||[])).next())}))},r=this&&this.__generator||function(t,e){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function c(o){return function(c){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=e.call(t,a)}catch(t){o=[6,t],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,c])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.isInIFrame=e.documentVisible=e.contentLoaded=void 0;e.contentLoaded=function(){return n(void 0,void 0,void 0,(function(){return r(this,(function(t){return[2,new Promise((function(t,e){"undefined"==typeof document&&e(),"complete"!==document.readyState&&"interactive"!==document.readyState||t(),document.addEventListener("DOMContentLoaded",(function(){return t()}))}))]}))}))};e.documentVisible=function(){return n(void 0,void 0,void 0,(function(){return r(this,(function(t){return"hidden"!==document.visibilityState?[2,Promise.resolve()]:[2,new Promise((function(t){var e=function(){t(),document.removeEventListener("visibilitychange",e,!1)};document.addEventListener("visibilitychange",e,!1)}))]}))}))};e.isInIFrame=function(){return window.self!==window.top}},7344:function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(t){var e=this;this.json=function(){return new Promise((function(t,n){try{t(JSON.parse(e.xhr.responseText))}catch(t){n(t)}}))},this.xhr=t,this.status=t.status,this.statusText=t.statusText,this.ok=t.status>=200&&t.status<300,this.redirected=t.status>=300&&t.status<400};e.default=function(t){var e=new XMLHttpRequest;e.withCredentials=!0,e.timeout=2e3;var r=new Promise((function(t,r){e.onreadystatechange=function(){if(e.readyState===XMLHttpRequest.DONE){var i=new n(e);i.ok||i.redirected?t(i):r(i)}}}));return e.open("GET",t),e.send(),r}},4654:function(t,e,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}),i=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)"default"!==n&&Object.prototype.hasOwnProperty.call(t,n)&&r(e,t,n);return i(e,t),e},a=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.utilities=e.twclid=e.fetch=e.environment=e.Cookies=void 0;var c=n(6575);Object.defineProperty(e,"Cookies",{enumerable:!0,get:function(){return c.Cookies}});var u=o(n(618));e.environment=u;var s=a(n(7344));e.fetch=s.default;var l=o(n(8974));e.twclid=l;var f=o(n(3532));e.utilities=f},1304:function(t,e){"use strict";function n(){var t=new Float32Array(1),e=new Uint8Array(t.buffer);return t[0]=1/0,t[0]=t[0]-t[0],e[3]}function r(t){var e=parseFloat(String(t));return isNaN(e)?0:e}function i(){var t,e;try{var n=null===(t=window.Intl)||void 0===t?void 0:t.DateTimeFormat;if(n){var i=(new n).resolvedOptions().timeZone;if(i)return i}var o=(e=(new Date).getFullYear(),-Math.max(r(new Date(e,0,1).getTimezoneOffset()),r(new Date(e,6,1).getTimezoneOffset())));return"UTC"+(o>=0?"+":"")+o}catch(t){return""}}function o(t){try{return navigator[t]||"na"}catch(t){return"na"}}Object.defineProperty(e,"__esModule",{value:!0}),e.getDeviceSettings=e.getTimezone=e.getArchitecture=void 0,e.getArchitecture=n,e.getTimezone=i,e.getDeviceSettings=function(){var t=window.screen||{},e=window.navigator||{},r=n(),a=(t.width||0)+"&"+(t.height||0),c=t.colorDepth||0,u=t.availWidth||0,s=t.availHeight||0,l=Array.isArray(e.languages)?e.languages.join(","):e.language||"na",f=e.doNotTrack||"na",d=e.maxTouchPoints||0,h=e.hardwareConcurrency||0,p=o("platform"),v=o("vendor"),y=[i(),l,v,p,r,a,h,c,u,s,d,f].join("&");return{dv:y.length<1e3?y:y.slice(0,1e3)+"&end"}}},8974:function(t,e,n){"use strict";var r=this&&this.__awaiter||function(t,e,n,r){return new(n||(n=Promise))((function(i,o){function a(t){try{u(r.next(t))}catch(t){o(t)}}function c(t){try{u(r.throw(t))}catch(t){o(t)}}function u(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,c)}u((r=r.apply(t,e||[])).next())}))},i=this&&this.__generator||function(t,e){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function c(o){return function(c){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=e.call(t,a)}catch(t){o=[6,t],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,c])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.saveTwclidIntoCookie=e.isAccCached=e.getTwclidParams=e.getTwclid=e.getTwclidFromHref=e.getTwclidFromCookie=e.cacheAccRequestAndSaveTwclid=e.cacheAccRequest=e.AccRequestCache=e.TWCLID_QUERY_PARAM=e.TWCLID_COOKIE_TTL=e.TWCLID_COOKIE_NAME=e.TWCLIDSource=void 0;var o,a=n(6575),c=n(3532);!function(t){t[t.Href=1]="Href",t[t.Cookie=2]="Cookie"}(o=e.TWCLIDSource||(e.TWCLIDSource={})),e.TWCLID_COOKIE_NAME="_twclid",e.TWCLID_COOKIE_TTL=2592e3,e.TWCLID_QUERY_PARAM="twclid",e.AccRequestCache={};e.cacheAccRequest=function(t,n){if(!e.isAccCached(t)){e.AccRequestCache[t]=function(t){return r(void 0,void 0,void 0,(function(){var e,n,r;return i(this,(function(i){switch(i.label){case 0:return i.trys.push([0,3,,4]),[4,t];case 1:return[4,i.sent().json()];case 2:return e=i.sent(),[2,!0===(null===(r=null==e?void 0:e.data)||void 0===r?void 0:r.allow_1p_cookie)];case 3:return n=i.sent(),c.logError(n),[2,!1];case 4:return[2]}}))}))}(n)}};e.cacheAccRequestAndSaveTwclid=function(t,n){e.cacheAccRequest(t,n),e.saveTwclidIntoCookie(t)};e.getTwclidFromCookie=function(){try{return JSON.parse(decodeURIComponent(a.Cookies.getCookie(e.TWCLID_COOKIE_NAME)||"")).twclid}catch(t){return}};e.getTwclidFromHref=function(){var t=location.search.slice(1).split("&").map((function(t){return t.split("=")})).filter((function(t){var n=t[0];t[1];return n===e.TWCLID_QUERY_PARAM}))[0];return t&&t[1]};e.getTwclid=function(t){var n=e.getTwclidFromHref();return n?{twclid:n,source:o.Href}:(n=e.getTwclidFromCookie())?{twclid:n,source:o.Cookie}:void 0};e.getTwclidParams=function(t){var n=e.getTwclid(t);if(!n)return{};var r={twclid:n.twclid,tw_clid_src:n.source};return e.isAccCached(t)||n.source!=o.Href||e.getTwclidFromCookie()||(r.tw_acc_response=1),r};e.isAccCached=function(t){return void 0!==e.AccRequestCache[t]};e.saveTwclidIntoCookie=function(t){return r(void 0,void 0,void 0,(function(){var n,r,u,s,l,f,d;return i(this,(function(i){switch(i.label){case 0:return(n=e.getTwclidFromHref())?[4,e.AccRequestCache[t]]:[2];case 1:if(!i.sent())return[2];if(r=e.getTwclidFromCookie(),n===r)return[2];for(u={pixelVersion:"2.3.33",timestamp:Date.now().toString(),twclid:n,source:o.Href},s=c.getWildcardDomains(),l=0,f=s;l<f.length&&(d=f[l],a.Cookies.setCookie(e.TWCLID_COOKIE_NAME,JSON.stringify(u),{domain:d,"max-age":e.TWCLID_COOKIE_TTL,secure:!0,samesite:"Strict"}),!a.Cookies.getCookie(e.TWCLID_COOKIE_NAME));l++);return[2]}}))}))}},3532:function(t,e,n){"use strict";var r=this&&this.__assign||function(){return(r=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.splitObjectByPropNames=e.splitObjectBy=e.parseEventCodeId=e.mergeObjects=e.logError=e.isObject=e.hashParameter=e.hasOwnPropertyCi=e.generatePageLoadId=e.generateEventId=e.getWildcardDomains=e.getPropertyCi=e.getLegacyParams=e.addQueries=e.LogPrefix=e.EventCodeImpl=e.AdsApiVersion=void 0;var o=i(n(7219)),a=n(5494);!function(t){t.v0="i",t.v1="1/i"}(e.AdsApiVersion||(e.AdsApiVersion={})),function(t){t[t.UNKNOWN=0]="UNKNOWN",t[t.SET_TRACK_PID=1]="SET_TRACK_PID",t[t.UWT_TRACK=2]="UWT_TRACK",t[t.ONETAG_CONFIG=3]="ONETAG_CONFIG",t[t.ONETAG_EVENT=4]="ONETAG_EVENT",t[t.DEPRECATED_TRACK=5]="DEPRECATED_TRACK",t[t.DEPRECATED_BUILD_PIXEL=6]="DEPRECATED_BUILD_PIXEL",t[t.DEPRECATED_BUILD_SCRIPT=7]="DEPRECATED_BUILD_SCRIPT",t[t.DEPRECATED_BUILD_IFRAME=8]="DEPRECATED_BUILD_IFRAME"}(e.EventCodeImpl||(e.EventCodeImpl={})),e.LogPrefix="[Twitter Pixel Tag]",e.addQueries=function(t,e){var n=document.createElement("a");n.href=t;var i=n.origin,o=n.pathname,a=n.search,c=n.hash,u=a.slice(1).split("&").map((function(t){return t.split("=")})).reduce((function(t,e){var n,i=e[0],o=e[1];return r(((n={})[i]=o,n),t)}),r({},e));return""+i+o+"?"+Object.keys(u).sort().map((function(t){return t+"="+(t in e?encodeURIComponent(u[t]):u[t])})).join("&")+c};function c(t,e){var n=e.toLowerCase();for(var r in t)if(r.toLowerCase()===n&&t.hasOwnProperty(r))return t[r]}e.getLegacyParams=function(t){if(!s(t))return{};var e={tw_sale_amount:c(t,"tw_sale_amount")||0,tw_order_quantity:c(t,"tw_order_quantity")||0};return delete t.tw_sale_amount,delete t.tw_order_quantity,u(t,"value")&&(e.tw_sale_amount=c(t,"value")),u(t,"num_items")&&(e.tw_order_quantity=c(t,"num_items")),Array.isArray(c(t,"content_ids"))&&(e.tw_product_id=c(t,"content_ids")[0]),u(t,"partner_id")&&(e.oct_p_id=c(t,"partner_id")),e},e.getPropertyCi=c;e.getWildcardDomains=function(){for(var t=location.hostname.split("."),e=[],n=2;n<=t.length;n++)e.push("."+t.slice(-1*n).join("."));return e};e.generateEventId=function(){return a.v4()};function u(t,e){var n=e.toLowerCase();for(var r in t)if(r.toLowerCase()===n&&t.hasOwnProperty(r))return!0;return!1}function s(t){var e=typeof t;return"function"===e||"object"===e&&!!t}function l(t,e){t=t||{};var n=[{},{}],r=n[0],i=n[1];for(var o in t)e(o,t[o])?r[o]=t[o]:i[o]=t[o];return[r,i]}e.generatePageLoadId=function(){return a.v4()},e.hasOwnPropertyCi=u,e.hashParameter=function(t){return o.default(t).toString()},e.isObject=s,e.logError=function(t){},e.mergeObjects=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=t[0],r=t.slice(1);return r.forEach((function(t){t=t||{},Object.keys(t).forEach((function(e){n[e]=t[e]}))})),n},e.parseEventCodeId=function(t){if("string"!=typeof t)return["",t];var e=t.toLowerCase().split("-");return 3!==e.length||"tw"!==e[0]||""===e[1]||""===e[2]?["",t]:[e[1],e[2]]},e.splitObjectBy=l,e.splitObjectByPropNames=function(t,e){return l(t,(function(t){return e.indexOf(t.trim().toLowerCase())>=0}))}},2480:function(){}},e={};function n(r){var i=e[r];if(void 0!==i)return i.exports;var o=e[r]={exports:{}};return t[r].call(o.exports,o,o.exports,n),o.exports}n.d=function(t,e){for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};!function(){"use strict";n(2244),n(7658),n(2735)}()}();