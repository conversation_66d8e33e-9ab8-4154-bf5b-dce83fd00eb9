var Ye=Object.defineProperty,$e=Object.defineProperties;var Te=Object.getOwnPropertyDescriptors;var _e=Object.getOwnPropertySymbols;var Fe=Object.prototype.hasOwnProperty,Re=Object.prototype.propertyIsEnumerable;var ce=(n,e,l)=>e in n?Ye(n,e,{enumerable:!0,configurable:!0,writable:!0,value:l}):n[e]=l,Z=(n,e)=>{for(var l in e||(e={}))Fe.call(e,l)&&ce(n,l,e[l]);if(_e)for(var l of _e(e))Re.call(e,l)&&ce(n,l,e[l]);return n},ve=(n,e)=>$e(n,Te(e));var J=(n,e,l)=>ce(n,typeof e!="symbol"?e+"":e,l);var q=(n,e,l)=>new Promise((p,h)=>{var m=g=>{try{d(l.next(g))}catch(s){h(s)}},i=g=>{try{d(l.throw(g))}catch(s){h(s)}},d=g=>g.done?p(g.value):Promise.resolve(g.value).then(m,i);d((l=l.apply(n,e)).next())});import{_ as de}from"./_plugin-vue_export-helper-C6jw_E77.js";/* empty css                    *//* empty css                        *//* empty css               *//* empty css                *//* empty css                 *//* empty css                  *//* empty css                       *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                        */import{_ as M,$ as Ve,d as pe,c as V,R as ye,a0 as Ee,W as be,b as $,e as a,f,a1 as Ae,w as r,a2 as Ue,E as fe,o as I,N as ne,x as w,Z as ue,a3 as Ie,a4 as Be,V as me,Y as ge,a5 as ze,h as x,a6 as We,a7 as Le,F as K,k as j,L as b,a8 as Oe,a9 as De,v as D,r as k,U as re,aa as qe,ab as Ne,ac as Ge,ad as He,ae as we,af as Me,ag as Ke,ah as je,ai as Xe,aj as Qe,ak as Ze,al as Je,g as ae,y as xe,am as et,an as tt,n as Se,ao as ke,ap as H,aq as at,O as nt,P as Ce,X as rt,S as ot,ar as lt,as as st,at as it,Q as ct,au as ut}from"./index-8zz4iTME.js";import{g as ee,i as te,L as dt}from"./index-_zIlD_l3.js";/* empty css                          *//* empty css                     *//* empty css                   *//* empty css                     *//* empty css                 */class pt{constructor(){J(this,"baseConsumption",85);J(this,"efficiencyBase",78);J(this,"costPerKwh",.8)}generateEnergyData(e,l,p=15){const h=[],m=M(e),i=M(l);let d=m;for(;d.isBefore(i)||d.isSame(i);){const g=d.hour(),s=d.day(),u=this.getTimeMultiplier(g,s),o=this.getSeasonMultiplier(d.month()),C=.9+Math.random()*.2,S=this.baseConsumption*u*o*C,A=Math.min(95,Math.max(60,this.efficiencyBase+(Math.random()-.5)*20+this.getEfficiencyBonus(S))),T=S*A/100*12,F=40+(Math.random()-.5)*10,R=45+S/this.baseConsumption*10,B=25+(Math.random()-.5)*15,U=1+Math.random()*2,E=S*(p/60),v=E*this.costPerKwh;h.push({timestamp:d.toISOString(),power:Math.round(S*100)/100,energy:Math.round(E*1e3)/1e3,efficiency:Math.round(A*10)/10,cost:Math.round(v*100)/100,flow:Math.round(T*10)/10,head:Math.round(F*10)/10,frequency:Math.round(R*10)/10,temperature:Math.round(B*10)/10,vibration:Math.round(U*100)/100}),d=d.add(p,"minute")}return h}getTimeMultiplier(e,l){return l===0||l===6?e>=8&&e<=18?.7:e>=19&&e<=22?.8:.5:e>=6&&e<=8?.9:e>=9&&e<=17?1:e>=18&&e<=21?1.1:e>=22||e<=5?.6:.8}getSeasonMultiplier(e){return e>=5&&e<=8?1.2:e>=11||e<=2?1.1:1}getEfficiencyBonus(e){const l=e/this.baseConsumption;return l>=.7&&l<=.9?5:l>=.5&&l<=1.2?2:-3}calculateStatistics(e,l){if(e.length===0)throw new Error("数据为空，无法计算统计信息");const p=e.reduce((E,v)=>E+v.energy,0),h=e.reduce((E,v)=>E+v.cost,0),m=e.reduce((E,v)=>E+v.flow,0)*(15/60),i=e.map(E=>E.power),d=e.map(E=>E.efficiency),g=i.reduce((E,v)=>E+v,0)/i.length,s=Math.max(...i),u=Math.min(...i),o=d.reduce((E,v)=>E+v,0)/d.length,C=Math.max(...d),S=Math.min(...d),A=e.length*.25,T=m>0?p/m:0,F=m>0?h/m:0,R=p*.5968,B=s,U=g/s;return{period:l,startDate:e[0].timestamp,endDate:e[e.length-1].timestamp,totalEnergy:Math.round(p*100)/100,totalCost:Math.round(h*100)/100,avgPower:Math.round(g*100)/100,maxPower:Math.round(s*100)/100,minPower:Math.round(u*100)/100,avgEfficiency:Math.round(o*10)/10,maxEfficiency:Math.round(C*10)/10,minEfficiency:Math.round(S*10)/10,runningHours:Math.round(A*10)/10,energyPerUnit:Math.round(T*1e3)/1e3,costPerUnit:Math.round(F*1e3)/1e3,carbonEmission:Math.round(R*100)/100,peakDemand:Math.round(B*100)/100,loadFactor:Math.round(U*1e3)/1e3}}generateTrendData(e,l){const p=[],h=M();for(let m=l-1;m>=0;m--){let i;switch(e){case"day":i=h.subtract(m,"day");break;case"week":i=h.subtract(m,"week");break;case"month":i=h.subtract(m,"month");break;case"quarter":i=h.subtract(m*3,"month");break;case"year":i=h.subtract(m,"year");break;default:i=h.subtract(m,"day")}const d=2e3+Math.random()*500,g=this.getSeasonMultiplier(i.month()),s=1+(Math.random()-.5)*.1,u=d*g*s,o=u*this.costPerKwh,C=75+Math.random()*10,S=u/(24*(e==="day"?1:e==="week"?7:30));p.push({date:i.format("YYYY-MM-DD"),energy:Math.round(u*100)/100,cost:Math.round(o*100)/100,efficiency:Math.round(C*10)/10,power:Math.round(S*100)/100})}return p}generateAlerts(e){const l=[];if(e.length===0)return l;const p=e.reduce((d,g)=>d+g.power,0)/e.length,h=e.reduce((d,g)=>d+g.efficiency,0)/e.length,m=e.reduce((d,g)=>d+g.cost,0);p>this.baseConsumption*1.2&&l.push({id:`alert_${Date.now()}_1`,type:"high_consumption",level:"warning",title:"能耗偏高",message:`当前平均功率 ${p.toFixed(1)} kW 超过正常范围`,value:p,threshold:this.baseConsumption*1.2,timestamp:new Date().toISOString(),resolved:!1}),h<70&&l.push({id:`alert_${Date.now()}_2`,type:"low_efficiency",level:"critical",title:"效率偏低",message:`当前平均效率 ${h.toFixed(1)}% 低于正常水平`,value:h,threshold:70,timestamp:new Date().toISOString(),resolved:!1});const i=1500;return m>i&&l.push({id:`alert_${Date.now()}_3`,type:"cost_overrun",level:"warning",title:"成本超标",message:`当前总成本 ${m.toFixed(2)} 元超过预算`,value:m,threshold:i,timestamp:new Date().toISOString(),resolved:!1}),l}}const G=new pt,ft=Ve("energy",{state:()=>({energyData:[],currentPeriod:"day",dateRange:{start:M().subtract(7,"day").format("YYYY-MM-DD"),end:M().format("YYYY-MM-DD")},currentStatistics:null,previousStatistics:null,trendData:[],comparisonData:null,alerts:[],loading:!1,reportConfig:{period:"day",startDate:M().subtract(7,"day").format("YYYY-MM-DD"),endDate:M().format("YYYY-MM-DD"),includeCharts:!0,includeComparison:!0,includeTrends:!0,format:"pdf"},realTimeEnabled:!1,updateInterval:null}),getters:{getCurrentPeriodData:n=>{if(!n.energyData.length)return[];const e=M(n.dateRange.start),l=M(n.dateRange.end).endOf("day");return n.energyData.filter(p=>{const h=M(p.timestamp);return h.isAfter(e)&&h.isBefore(l)})},getEnergyTrend:n=>n.comparisonData?n.comparisonData.changePercent.totalEnergy:0,getCostTrend:n=>n.comparisonData?n.comparisonData.changePercent.totalCost:0,getEfficiencyTrend:n=>n.comparisonData?n.comparisonData.changePercent.avgEfficiency:0,getUnresolvedAlertsCount:n=>n.alerts.filter(e=>!e.resolved).length,getKPIs:n=>n.currentStatistics?{totalEnergy:n.currentStatistics.totalEnergy,totalCost:n.currentStatistics.totalCost,avgEfficiency:n.currentStatistics.avgEfficiency,energyPerUnit:n.currentStatistics.energyPerUnit,carbonEmission:n.currentStatistics.carbonEmission,loadFactor:n.currentStatistics.loadFactor}:null},actions:{setPeriod(n){this.currentPeriod=n,this.updateDateRange(),this.loadEnergyData()},setDateRange(n,e){this.dateRange.start=n,this.dateRange.end=e,this.loadEnergyData()},updateDateRange(){const n=M();let e;switch(this.currentPeriod){case"day":e=n.subtract(1,"day");break;case"week":e=n.subtract(1,"week");break;case"month":e=n.subtract(1,"month");break;case"quarter":e=n.subtract(3,"month");break;case"year":e=n.subtract(1,"year");break;default:e=n.subtract(1,"day")}this.dateRange.start=e.format("YYYY-MM-DD"),this.dateRange.end=n.format("YYYY-MM-DD")},loadEnergyData(){return q(this,null,function*(){this.loading=!0;try{this.energyData=G.generateEnergyData(this.dateRange.start,this.dateRange.end,15),this.energyData.length>0&&(this.currentStatistics=G.calculateStatistics(this.energyData,this.currentPeriod)),yield this.generateComparisonData(),this.generateTrendData(),this.generateAlerts()}catch(n){console.error("加载能耗数据失败:",n)}finally{this.loading=!1}})},generateComparisonData(){return q(this,null,function*(){if(this.currentStatistics)try{const n=M(this.dateRange.start),l=M(this.dateRange.end).diff(n,"day"),p=n.subtract(l+1,"day"),h=n.subtract(1,"day"),m=G.generateEnergyData(p.format("YYYY-MM-DD"),h.format("YYYY-MM-DD"),15);m.length>0&&(this.previousStatistics=G.calculateStatistics(m,this.currentPeriod),this.comparisonData={current:this.currentStatistics,previous:this.previousStatistics,changePercent:{totalEnergy:this.calculateChangePercent(this.currentStatistics.totalEnergy,this.previousStatistics.totalEnergy),totalCost:this.calculateChangePercent(this.currentStatistics.totalCost,this.previousStatistics.totalCost),avgEfficiency:this.calculateChangePercent(this.currentStatistics.avgEfficiency,this.previousStatistics.avgEfficiency),energyPerUnit:this.calculateChangePercent(this.currentStatistics.energyPerUnit,this.previousStatistics.energyPerUnit)}})}catch(n){console.error("生成对比数据失败:",n)}})},calculateChangePercent(n,e){return e===0?0:Math.round((n-e)/e*1e4)/100},generateTrendData(){const n=this.currentPeriod==="day"?30:this.currentPeriod==="week"||this.currentPeriod==="month"?12:4;this.trendData=G.generateTrendData(this.currentPeriod,n)},generateAlerts(){this.alerts=G.generateAlerts(this.energyData)},resolveAlert(n){const e=this.alerts.find(l=>l.id===n);e&&(e.resolved=!0)},startRealTimeUpdate(){this.updateInterval||(this.realTimeEnabled=!0,this.updateInterval=setInterval(()=>{this.loadEnergyData()},6e4))},stopRealTimeUpdate(){this.updateInterval&&(clearInterval(this.updateInterval),this.updateInterval=null),this.realTimeEnabled=!1},exportReport(n){return q(this,null,function*(){return n&&(this.reportConfig=Z(Z({},this.reportConfig),n)),console.log("导出报表配置:",this.reportConfig),new Promise(e=>{setTimeout(()=>{e({success:!0,filename:`energy_report_${M().format("YYYYMMDD_HHmmss")}.${this.reportConfig.format}`,url:"#"})},2e3)})})},resetData(){this.energyData=[],this.currentStatistics=null,this.previousStatistics=null,this.trendData=[],this.comparisonData=null,this.alerts=[]}}});function mt(n,e){const l={};if(n.trendChart){const p=ee(n.trendChart);p&&p.dispose(),l.trendChart=te(n.trendChart),gt(l.trendChart,e.trendData,e.trendType)}if(n.powerChart){const p=ee(n.powerChart);p&&p.dispose(),l.powerChart=te(n.powerChart),ht(l.powerChart,e.energyData)}if(n.heatmapChart){const p=ee(n.heatmapChart);p&&p.dispose(),l.heatmapChart=te(n.heatmapChart),_t(l.heatmapChart,e.energyData)}if(n.costChart){const p=ee(n.costChart);p&&p.dispose(),l.costChart=te(n.costChart),vt(l.costChart,e.statistics)}return l}function gt(n,e,l){const p=e.map(g=>M(g.date).format("MM-DD"));let h,m,i;switch(l){case"energy":h=e.map(g=>g.energy),m="能耗 (kWh)",i="#409EFF";break;case"cost":h=e.map(g=>g.cost),m="成本 (元)",i="#E6A23C";break;case"efficiency":h=e.map(g=>g.efficiency),m="效率 (%)",i="#67C23A";break;default:h=e.map(g=>g.energy),m="能耗 (kWh)",i="#409EFF"}const d={title:{text:`${l==="energy"?"能耗":l==="cost"?"成本":"效率"}趋势`,left:"center",textStyle:{fontSize:16}},tooltip:{trigger:"axis",formatter:function(g){const s=g[0];return`${s.name}<br/>${s.seriesName}: ${s.value}${l==="efficiency"?"%":l==="cost"?"元":"kWh"}`}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:p,axisLabel:{rotate:45}},yAxis:{type:"value",name:m,nameLocation:"middle",nameGap:50},series:[{name:m,type:"line",data:h,smooth:!0,lineStyle:{color:i,width:3},areaStyle:{color:new dt(0,0,0,1,[{offset:0,color:i+"40"},{offset:1,color:i+"10"}])},symbol:"circle",symbolSize:6}]};n.setOption(d)}function ht(n,e){const p=[{min:0,max:50,label:"0-50kW"},{min:50,max:70,label:"50-70kW"},{min:70,max:90,label:"70-90kW"},{min:90,max:110,label:"90-110kW"},{min:110,max:130,label:"110-130kW"},{min:130,max:999,label:">130kW"}].map(m=>{const i=e.filter(d=>d.power>=m.min&&d.power<m.max).length;return{name:m.label,value:i}}),h={title:{text:"功率分布",left:"center",textStyle:{fontSize:16}},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",top:"middle"},series:[{name:"功率分布",type:"pie",radius:["40%","70%"],center:["60%","50%"],data:p,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},label:{formatter:`{b}
{d}%`}}]};n.setOption(h)}function _t(n,e){const l=Array.from({length:24},(i,d)=>d),p=["周日","周一","周二","周三","周四","周五","周六"],h=[];for(let i=0;i<7;i++)for(let d=0;d<24;d++){const g=e.filter(u=>{const o=M(u.timestamp);return o.day()===i&&o.hour()===d}),s=g.length>0?g.reduce((u,o)=>u+o.efficiency,0)/g.length:0;h.push([d,i,Math.round(s*10)/10])}const m={title:{text:"效率热力图",left:"center",textStyle:{fontSize:16}},tooltip:{position:"top",formatter:function(i){return`${p[i.value[1]]} ${i.value[0]}:00<br/>效率: ${i.value[2]}%`}},grid:{height:"60%",top:"10%"},xAxis:{type:"category",data:l.map(i=>i+":00"),splitArea:{show:!0}},yAxis:{type:"category",data:p,splitArea:{show:!0}},visualMap:{min:60,max:90,calculable:!0,orient:"horizontal",left:"center",bottom:"5%",inRange:{color:["#313695","#4575b4","#74add1","#abd9e9","#e0f3f8","#ffffcc","#fee090","#fdae61","#f46d43","#d73027","#a50026"]}},series:[{name:"效率",type:"heatmap",data:h,label:{show:!1},emphasis:{itemStyle:{shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};n.setOption(m)}function vt(n,e){const l=e.totalCost,p=l*.85,h=l*.1,m=l*.05,d={title:{text:"成本构成",left:"center",textStyle:{fontSize:16}},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: ¥{c} ({d}%)"},legend:{orient:"vertical",left:"left",top:"middle"},series:[{name:"成本构成",type:"pie",radius:"60%",center:["60%","50%"],data:[{name:"电费",value:p,itemStyle:{color:"#409EFF"}},{name:"维护费",value:h,itemStyle:{color:"#67C23A"}},{name:"其他费用",value:m,itemStyle:{color:"#E6A23C"}}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},label:{formatter:`{b}
¥{c}
({d}%)`}}]};n.setOption(d)}const yt={class:"statistics-table"},bt={class:"metric-cell"},Ct={class:"value-cell"},Et={class:"value-cell"},Dt={key:1},wt={class:"description-cell"},Mt={class:"trend-cell"},xt={class:"trend-text"},St={class:"summary-section"},kt={class:"analysis-section"},Pt={class:"suggestion-content"},Yt={class:"suggestion-impact"},$t=pe({__name:"StatisticsTable",props:{currentStats:{},previousStats:{},comparison:{}},setup(n){const e=n,l=V(()=>{if(!e.currentStats)return[];const s=e.currentStats,u=e.previousStats,o=e.comparison;return[{metric:"总能耗",current:`${s.totalEnergy.toLocaleString()}`,previous:u?`${u.totalEnergy.toLocaleString()}`:null,change:(o==null?void 0:o.changePercent.totalEnergy)||null,changeClass:d(o==null?void 0:o.changePercent.totalEnergy,!0),changeIcon:g(o==null?void 0:o.changePercent.totalEnergy),unit:"kWh",description:"统计周期内的总电能消耗",tooltip:"包括所有设备的电能消耗总和，是衡量能源使用效率的重要指标",icon:"Lightning",color:"#409EFF",trendPercentage:Math.min(100,s.totalEnergy/3e3*100),trendColor:"#409EFF",trendText:"正常"},{metric:"总成本",current:`${s.totalCost.toLocaleString()}`,previous:u?`${u.totalCost.toLocaleString()}`:null,change:(o==null?void 0:o.changePercent.totalCost)||null,changeClass:d(o==null?void 0:o.changePercent.totalCost,!0),changeIcon:g(o==null?void 0:o.changePercent.totalCost),unit:"元",description:"统计周期内的总运行成本",tooltip:"包括电费、维护费等所有运行成本",icon:"Money",color:"#E6A23C",trendPercentage:Math.min(100,s.totalCost/2500*100),trendColor:"#E6A23C",trendText:"可控"},{metric:"平均效率",current:`${s.avgEfficiency.toFixed(1)}`,previous:u?`${u.avgEfficiency.toFixed(1)}`:null,change:(o==null?void 0:o.changePercent.avgEfficiency)||null,changeClass:d(o==null?void 0:o.changePercent.avgEfficiency,!1),changeIcon:g(o==null?void 0:o.changePercent.avgEfficiency),unit:"%",description:"设备运行的平均效率",tooltip:"效率越高表示能源利用越充分，是优化的重要目标",icon:"TrendCharts",color:"#67C23A",trendPercentage:s.avgEfficiency,trendColor:s.avgEfficiency>80?"#67C23A":s.avgEfficiency>70?"#E6A23C":"#F56C6C",trendText:s.avgEfficiency>80?"优秀":s.avgEfficiency>70?"良好":"需改进"},{metric:"单位能耗",current:`${s.energyPerUnit.toFixed(3)}`,previous:u?`${u.energyPerUnit.toFixed(3)}`:null,change:(o==null?void 0:o.changePercent.energyPerUnit)||null,changeClass:d(o==null?void 0:o.changePercent.energyPerUnit,!0),changeIcon:g(o==null?void 0:o.changePercent.energyPerUnit),unit:"kWh/m³",description:"单位流量的能耗",tooltip:"反映系统的能效水平，数值越低越好",icon:"DataAnalysis",color:"#909399",trendPercentage:Math.max(0,100-s.energyPerUnit*100),trendColor:"#909399",trendText:"监控中"},{metric:"碳排放",current:`${s.carbonEmission.toFixed(1)}`,previous:u?`${u.carbonEmission.toFixed(1)}`:null,change:null,changeClass:"",changeIcon:"Minus",unit:"kg CO2",description:"统计周期内的碳排放量",tooltip:"基于电网碳排放因子计算的间接碳排放",icon:"Setting",color:"#F56C6C",trendPercentage:Math.min(100,s.carbonEmission/2e3*100),trendColor:"#F56C6C",trendText:"关注"},{metric:"负荷因子",current:`${s.loadFactor.toFixed(3)}`,previous:u?`${u.loadFactor.toFixed(3)}`:null,change:null,changeClass:"",changeIcon:"Minus",unit:"-",description:"平均负荷与峰值负荷的比值",tooltip:"反映负荷的平稳程度，数值越接近1越好",icon:"TrendCharts",color:"#909399",trendPercentage:s.loadFactor*100,trendColor:s.loadFactor>.8?"#67C23A":s.loadFactor>.6?"#E6A23C":"#F56C6C",trendText:s.loadFactor>.8?"优秀":s.loadFactor>.6?"良好":"需优化"}]}),p=V(()=>{if(!e.currentStats)return 0;const s=e.currentStats;let u=0;const o=Math.min(100,s.avgEfficiency/90*100);u+=o*.4;const C=s.loadFactor*100;u+=C*.3;const S=Math.max(0,100-s.energyPerUnit*50);return u+=S*.3,Math.round(u)}),h=V(()=>{if(!e.currentStats)return 0;const s=e.currentStats.avgEfficiency,u=90;return Math.round((u-s)/u*100)}),m=V(()=>{if(!e.currentStats)return 0;const s=e.currentStats.totalCost,u=h.value/100;return Math.round(s*u)}),i=V(()=>{if(!e.currentStats)return[];const s=e.currentStats,u=[];return s.avgEfficiency<75&&u.push({id:1,title:"提升运行效率",description:"当前平均效率偏低，建议检查设备状态，优化运行参数",impact:"可提升效率5-10%",type:"warning",impactType:"warning",icon:ye}),s.loadFactor<.7&&u.push({id:2,title:"优化负荷分配",description:"负荷因子较低，建议平衡各时段用电负荷",impact:"可降低峰值需量10-15%",type:"primary",impactType:"primary",icon:Ee}),s.energyPerUnit>.02&&u.push({id:3,title:"降低单位能耗",description:"单位能耗偏高，建议采用变频调速等节能技术",impact:"可节约能耗15-20%",type:"success",impactType:"success",icon:be}),u});function d(s,u){return s==null?"":u?s>0?"change-negative":s<0?"change-positive":"change-neutral":s>0?"change-positive":s<0?"change-negative":"change-neutral"}function g(s){return s==null?"Minus":s>0?"ArrowUp":s<0?"ArrowDown":"Minus"}return(s,u)=>{const o=fe,C=Ue,S=Ie,A=Be,T=Ae,F=ze,R=ge,B=me,U=De,E=Oe,v=Le;return b(),$("div",yt,[a(T,{data:l.value,border:"",stripe:""},{default:r(()=>[a(C,{prop:"metric",label:"统计指标",width:"150",fixed:"left"},{default:r(({row:t})=>[f("div",bt,[a(o,{color:t.color},{default:r(()=>[(b(),I(ne(t.icon)))]),_:2},1032,["color"]),f("span",null,w(t.metric),1)])]),_:1}),a(C,{prop:"current",label:"当前周期",width:"120",align:"right"},{default:r(({row:t})=>[f("span",Ct,w(t.current),1)]),_:1}),a(C,{prop:"previous",label:"上一周期",width:"120",align:"right"},{default:r(({row:t})=>[f("span",Et,w(t.previous||"-"),1)]),_:1}),a(C,{prop:"change",label:"变化",width:"100",align:"center"},{default:r(({row:t})=>[t.change!==null?(b(),$("div",{key:0,class:ue(["change-cell",t.changeClass])},[a(o,null,{default:r(()=>[(b(),I(ne(t.changeIcon)))]),_:2},1024),f("span",null,w(Math.abs(t.change))+"%",1)],2)):(b(),$("span",Dt,"-"))]),_:1}),a(C,{prop:"unit",label:"单位",width:"80",align:"center"}),a(C,{prop:"description",label:"说明","min-width":"200"},{default:r(({row:t})=>[a(S,{content:t.tooltip,placement:"top"},{default:r(()=>[f("span",wt,w(t.description),1)]),_:2},1032,["content"])]),_:1}),a(C,{label:"趋势",width:"120",align:"center"},{default:r(({row:t})=>[f("div",Mt,[a(A,{percentage:t.trendPercentage,color:t.trendColor,"stroke-width":8,"show-text":!1},null,8,["percentage","color"]),f("span",xt,w(t.trendText),1)])]),_:1})]),_:1},8,["data"]),f("div",St,[a(B,{gutter:20},{default:r(()=>[a(R,{span:8},{default:r(()=>[a(F,{title:"总体评分",value:p.value,suffix:"/100"},{prefix:r(()=>[a(o,{color:"#409EFF"},{default:r(()=>[a(x(ye))]),_:1})]),_:1},8,["value"])]),_:1}),a(R,{span:8},{default:r(()=>[a(F,{title:"节能潜力",value:h.value,suffix:"%"},{prefix:r(()=>[a(o,{color:"#67C23A"},{default:r(()=>[a(x(be))]),_:1})]),_:1},8,["value"])]),_:1}),a(R,{span:8},{default:r(()=>[a(F,{title:"成本优化空间",value:m.value,suffix:"元"},{prefix:r(()=>[a(o,{color:"#E6A23C"},{default:r(()=>[a(x(We))]),_:1})]),_:1},8,["value"])]),_:1})]),_:1})]),f("div",kt,[u[0]||(u[0]=f("h4",null,"智能分析建议",-1)),a(v,null,{default:r(()=>[(b(!0),$(K,null,j(i.value,t=>(b(),I(E,{key:t.id,type:t.type,icon:t.icon},{default:r(()=>[f("div",Pt,[f("h5",null,w(t.title),1),f("p",null,w(t.description),1),f("div",Yt,[a(U,{size:"small",type:t.impactType},{default:r(()=>[D(" 预期影响: "+w(t.impact),1)]),_:2},1032,["type"])])])]),_:2},1032,["type","icon"]))),128))]),_:1})])])}}}),Tt=de($t,[["__scopeId","data-v-c7bdb9bd"]]),Ft={class:"template-option"},Rt={key:0,class:"email-config"},Vt={class:"preview-section"},At={class:"preview-content"},Ut={class:"preview-header"},It={class:"preview-summary"},Bt={class:"preview-item"},zt={class:"preview-value"},Wt={class:"preview-label"},Lt={class:"preview-note"},Ot={class:"dialog-footer"},qt=pe({__name:"ReportExportDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","export"],setup(n,{emit:e}){const l=n,p=e,h=k(),m=k(!1),i=k(!1),d=k(""),g=k("能耗统计报表"),s=k("standard"),u=k(["charts","comparison","trends"]),o=k({period:"month",startDate:M().subtract(1,"month").format("YYYY-MM-DD"),endDate:M().format("YYYY-MM-DD"),includeCharts:!0,includeComparison:!0,includeTrends:!0,format:"pdf"}),C=k([o.value.startDate,o.value.endDate]),S=[{id:"standard",name:"标准报表",description:"常规",type:"primary"},{id:"executive",name:"管理层报表",description:"简洁",type:"success"},{id:"technical",name:"技术报表",description:"详细",type:"warning"},{id:"environmental",name:"环保报表",description:"专业",type:"info"}],A={period:[{required:!0,message:"请选择报表周期",trigger:"change"}],dateRange:[{required:!0,message:"请选择时间范围",trigger:"change"}],format:[{required:!0,message:"请选择导出格式",trigger:"change"}]},T=V({get:()=>l.modelValue,set:v=>p("update:modelValue",v)}),F=V(()=>`能耗统计${{day:"日",week:"周",month:"月",quarter:"季度",year:"年度"}[o.value.period]}报表`),R=V(()=>`统计周期: ${o.value.startDate} 至 ${o.value.endDate}`),B=V(()=>[{label:"总能耗",value:"2,456 kWh"},{label:"总成本",value:"¥1,965"},{label:"平均效率",value:"78.5%"},{label:"碳排放",value:"1,465 kg"}]),U=()=>{T.value=!1},E=()=>q(this,null,function*(){var t;if(yield(t=h.value)==null?void 0:t.validate().catch(()=>!1)){if(i.value&&!d.value){H.warning("请输入邮箱地址");return}m.value=!0;try{o.value.includeCharts=u.value.includes("charts"),o.value.includeComparison=u.value.includes("comparison"),o.value.includeTrends=u.value.includes("trends");const c=ve(Z({},o.value),{emailEnabled:i.value,emailAddress:d.value,emailSubject:g.value,template:s.value,contentOptions:u.value});p("export",c)}finally{m.value=!1}}});return re(C,v=>{v&&(o.value.startDate=v[0],o.value.endDate=v[1])}),re(()=>o.value.period,v=>{const t=M();let c;switch(v){case"day":c=t.subtract(7,"day");break;case"week":c=t.subtract(4,"week");break;case"month":c=t.subtract(6,"month");break;case"quarter":c=t.subtract(4,"quarter");break;case"year":c=t.subtract(3,"year");break;default:c=t.subtract(1,"month")}o.value.startDate=c.format("YYYY-MM-DD"),o.value.endDate=t.format("YYYY-MM-DD"),C.value=[o.value.startDate,o.value.endDate];const P={day:"日",week:"周",month:"月",quarter:"季度",year:"年度"};g.value=`能耗统计${P[v]}报表 - ${M().format("YYYY-MM-DD")}`}),(v,t)=>{const c=He,P=Ge,Y=Ne,oe=we,W=fe,X=Ke,le=Me,L=Je,z=Ze,O=De,Q=xe,N=et,se=qe,_=ge,ie=me,he=Se,Pe=at;return b(),I(Pe,{modelValue:T.value,"onUpdate:modelValue":t[8]||(t[8]=y=>T.value=y),title:"导出能耗报表",width:"600px","before-close":U},{footer:r(()=>[f("div",Ot,[a(he,{onClick:U},{default:r(()=>t[21]||(t[21]=[D("取消")])),_:1,__:[21]}),a(he,{type:"primary",onClick:E,loading:m.value},{default:r(()=>[a(W,null,{default:r(()=>[a(x(ke))]),_:1}),D(" "+w(m.value?"导出中...":"导出报表"),1)]),_:1},8,["loading"])])]),default:r(()=>[a(se,{model:o.value,"label-width":"120px",rules:A,ref_key:"formRef",ref:h},{default:r(()=>[a(Y,{label:"报表周期",prop:"period"},{default:r(()=>[a(P,{modelValue:o.value.period,"onUpdate:modelValue":t[0]||(t[0]=y=>o.value.period=y),placeholder:"请选择统计周期"},{default:r(()=>[a(c,{label:"日报表",value:"day"}),a(c,{label:"周报表",value:"week"}),a(c,{label:"月报表",value:"month"}),a(c,{label:"季度报表",value:"quarter"}),a(c,{label:"年度报表",value:"year"})]),_:1},8,["modelValue"])]),_:1}),a(Y,{label:"时间范围",prop:"dateRange"},{default:r(()=>[a(oe,{modelValue:C.value,"onUpdate:modelValue":t[1]||(t[1]=y=>C.value=y),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(Y,{label:"导出格式",prop:"format"},{default:r(()=>[a(le,{modelValue:o.value.format,"onUpdate:modelValue":t[2]||(t[2]=y=>o.value.format=y)},{default:r(()=>[a(X,{value:"pdf"},{default:r(()=>[a(W,null,{default:r(()=>[a(x(je))]),_:1}),t[9]||(t[9]=D(" PDF报告 "))]),_:1,__:[9]}),a(X,{value:"excel"},{default:r(()=>[a(W,null,{default:r(()=>[a(x(Xe))]),_:1}),t[10]||(t[10]=D(" Excel表格 "))]),_:1,__:[10]}),a(X,{value:"csv"},{default:r(()=>[a(W,null,{default:r(()=>[a(x(Qe))]),_:1}),t[11]||(t[11]=D(" CSV数据 "))]),_:1,__:[11]})]),_:1},8,["modelValue"])]),_:1}),a(Y,{label:"报表内容"},{default:r(()=>[a(z,{modelValue:u.value,"onUpdate:modelValue":t[3]||(t[3]=y=>u.value=y)},{default:r(()=>[a(L,{label:"charts"},{default:r(()=>t[12]||(t[12]=[D("包含图表")])),_:1,__:[12]}),a(L,{label:"comparison"},{default:r(()=>t[13]||(t[13]=[D("包含对比分析")])),_:1,__:[13]}),a(L,{label:"trends"},{default:r(()=>t[14]||(t[14]=[D("包含趋势分析")])),_:1,__:[14]}),a(L,{label:"suggestions"},{default:r(()=>t[15]||(t[15]=[D("包含优化建议")])),_:1,__:[15]}),a(L,{label:"details"},{default:r(()=>t[16]||(t[16]=[D("包含详细数据")])),_:1,__:[16]})]),_:1},8,["modelValue"])]),_:1}),a(Y,{label:"报表模板"},{default:r(()=>[a(P,{modelValue:s.value,"onUpdate:modelValue":t[4]||(t[4]=y=>s.value=y),placeholder:"选择报表模板"},{default:r(()=>[(b(),$(K,null,j(S,y=>a(c,{key:y.id,label:y.name,value:y.id},{default:r(()=>[f("div",Ft,[f("span",null,w(y.name),1),a(O,{size:"small",type:y.type},{default:r(()=>[D(w(y.description),1)]),_:2},1032,["type"])])]),_:2},1032,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),a(Y,{label:"邮件发送"},{default:r(()=>[a(Q,{modelValue:i.value,"onUpdate:modelValue":t[5]||(t[5]=y=>i.value=y)},null,8,["modelValue"]),i.value?(b(),$("div",Rt,[a(N,{modelValue:d.value,"onUpdate:modelValue":t[6]||(t[6]=y=>d.value=y),placeholder:"请输入邮箱地址",style:{"margin-top":"8px"}},{prepend:r(()=>t[17]||(t[17]=[D("收件人")])),_:1},8,["modelValue"]),a(N,{modelValue:g.value,"onUpdate:modelValue":t[7]||(t[7]=y=>g.value=y),placeholder:"邮件主题",style:{"margin-top":"8px"}},{prepend:r(()=>t[18]||(t[18]=[D("主题")])),_:1},8,["modelValue"])])):ae("",!0)]),_:1})]),_:1},8,["model"]),f("div",Vt,[t[20]||(t[20]=f("h4",null,"报表预览",-1)),f("div",At,[f("div",Ut,[f("h3",null,w(F.value),1),f("p",null,w(R.value),1)]),f("div",It,[a(ie,{gutter:16},{default:r(()=>[(b(!0),$(K,null,j(B.value,y=>(b(),I(_,{span:6,key:y.label},{default:r(()=>[f("div",Bt,[f("div",zt,w(y.value),1),f("div",Wt,w(y.label),1)])]),_:2},1024))),128))]),_:1})]),f("div",Lt,[a(W,null,{default:r(()=>[a(x(tt))]),_:1}),t[19]||(t[19]=f("span",null,"这是报表的简化预览，实际报表将包含更详细的数据和图表",-1))])])])]),_:1},8,["modelValue"])}}}),Nt=de(qt,[["__scopeId","data-v-2b184410"]]),Gt={class:"energy-statistics"},Ht={class:"card-header"},Kt={class:"header-actions"},jt={key:0,class:"alerts-section"},Xt={class:"kpi-section"},Qt={class:"kpi-content"},Zt={class:"kpi-icon"},Jt={class:"kpi-data"},ea={class:"kpi-value"},ta={class:"kpi-label"},aa={key:0,class:"kpi-trend"},na={class:"chart-header"},ra={class:"table-header"},oa=pe({__name:"index",setup(n){const e=ft(),l=k(!1),p=k(!1),h=k("energy"),m=k(),i=k(),d=k(),g=k(),s=k([e.dateRange.start,e.dateRange.end]),u=[{label:"日",value:"day"},{label:"周",value:"week"},{label:"月",value:"month"},{label:"季",value:"quarter"},{label:"年",value:"year"}],o=V(()=>e.alerts.filter(t=>!t.resolved)),C=V(()=>{const t=e.getKPIs;if(!t)return[];const c=e.getEnergyTrend,P=e.getCostTrend,Y=e.getEfficiencyTrend;return[{key:"totalEnergy",label:"总能耗",value:`${t.totalEnergy.toLocaleString()} kWh`,icon:"Lightning",color:"#409EFF",trend:c,trendIcon:c>0?"ArrowUp":c<0?"ArrowDown":"Minus",trendClass:c>0?"trend-up":c<0?"trend-down":"trend-stable"},{key:"totalCost",label:"总成本",value:`¥${t.totalCost.toLocaleString()}`,icon:"Money",color:"#E6A23C",trend:P,trendIcon:P>0?"ArrowUp":P<0?"ArrowDown":"Minus",trendClass:P>0?"trend-up":P<0?"trend-down":"trend-stable"},{key:"avgEfficiency",label:"平均效率",value:`${t.avgEfficiency.toFixed(1)}%`,icon:"TrendCharts",color:"#67C23A",trend:Y,trendIcon:Y>0?"ArrowUp":Y<0?"ArrowDown":"Minus",trendClass:Y>0?"trend-down":Y<0?"trend-up":"trend-stable"},{key:"energyPerUnit",label:"单位能耗",value:`${t.energyPerUnit.toFixed(3)} kWh/m³`,icon:"DataAnalysis",color:"#909399",trend:null,trendIcon:"Minus",trendClass:"trend-stable"},{key:"carbonEmission",label:"碳排放",value:`${t.carbonEmission.toFixed(1)} kg`,icon:"Setting",color:"#F56C6C",trend:null,trendIcon:"Minus",trendClass:"trend-stable"},{key:"loadFactor",label:"负荷因子",value:t.loadFactor.toFixed(3),icon:"TrendCharts",color:"#909399",trend:null,trendIcon:"Minus",trendClass:"trend-stable"}]}),S=t=>{e.setPeriod(t)},A=t=>{t&&e.setDateRange(t[0],t[1])},T=t=>{t?(e.startRealTimeUpdate(),H.success("已开启实时数据更新")):(e.stopRealTimeUpdate(),H.info("已关闭实时数据更新"))},F=t=>{e.resolveAlert(t),H.success("预警已处理")},R=()=>{ut.alert(`共有 ${o.value.length} 条未处理的预警信息`,"预警详情",{confirmButtonText:"确定",type:"warning"})},B=()=>{p.value=!0},U=t=>q(this,null,function*(){l.value=!0;try{const c=yield e.exportReport(t);H.success("报表导出成功"),p.value=!1}catch(c){H.error("报表导出失败")}finally{l.value=!1}}),E=()=>{e.loadEnergyData()},v=()=>{e.currentStatistics&&Ce(()=>{mt({trendChart:m.value,powerChart:i.value,heatmapChart:d.value,costChart:g.value},{trendData:e.trendData||[],energyData:e.energyData||[],statistics:e.currentStatistics,trendType:h.value})})};return re(h,()=>{v()}),re(()=>e.currentStatistics,()=>{v()},{deep:!0}),nt(()=>q(this,null,function*(){yield e.loadEnergyData(),yield Ce(),v()})),(t,c)=>{const P=fe,Y=Se,oe=rt,W=we,X=xe,le=ct,L=it,z=ot,O=ge,Q=me,N=lt,se=Me;return b(),$("div",Gt,[a(z,{class:"control-panel"},{header:r(()=>[f("div",Ht,[a(P,null,{default:r(()=>[a(x(Ee))]),_:1}),c[5]||(c[5]=f("span",null,"能耗统计分析",-1)),f("div",Kt,[a(oe,{size:"small"},{default:r(()=>[(b(),$(K,null,j(u,_=>a(Y,{key:_.value,type:x(e).currentPeriod===_.value?"primary":"",onClick:ie=>S(_.value)},{default:r(()=>[D(w(_.label),1)]),_:2},1032,["type","onClick"])),64))]),_:1}),a(W,{modelValue:s.value,"onUpdate:modelValue":c[0]||(c[0]=_=>s.value=_),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:A,size:"small"},null,8,["modelValue"]),a(Y,{size:"small",onClick:B,loading:l.value},{default:r(()=>[a(P,null,{default:r(()=>[a(x(ke))]),_:1}),c[4]||(c[4]=D(" 导出报表 "))]),_:1,__:[4]},8,["loading"]),a(X,{modelValue:x(e).realTimeEnabled,"onUpdate:modelValue":c[1]||(c[1]=_=>x(e).realTimeEnabled=_),onChange:T,"inline-prompt":"","active-text":"实时","inactive-text":"静态",size:"small"},null,8,["modelValue"])])])]),default:r(()=>[o.value.length>0?(b(),$("div",jt,[(b(!0),$(K,null,j(o.value.slice(0,3),_=>(b(),I(le,{key:_.id,title:_.title,description:_.message,type:_.level==="critical"?"error":_.level,"show-icon":"",closable:!0,onClose:ie=>F(_.id)},null,8,["title","description","type","onClose"]))),128)),o.value.length>3?(b(),I(L,{key:0,type:"primary",onClick:R},{default:r(()=>[D(" 查看全部 "+w(o.value.length)+" 条预警 ",1)]),_:1})):ae("",!0)])):ae("",!0)]),_:1}),f("div",Xt,[a(Q,{gutter:20},{default:r(()=>[(b(!0),$(K,null,j(C.value,_=>(b(),I(O,{span:4,key:_.key},{default:r(()=>[a(z,{class:ue(["kpi-card",_.trendClass])},{default:r(()=>[f("div",Qt,[f("div",Zt,[a(P,{size:32,color:_.color},{default:r(()=>[(b(),I(ne(_.icon)))]),_:2},1032,["color"])]),f("div",Jt,[f("div",ea,w(_.value),1),f("div",ta,w(_.label),1),_.trend!==null?(b(),$("div",aa,[a(P,{class:ue(_.trendClass)},{default:r(()=>[(b(),I(ne(_.trendIcon)))]),_:2},1032,["class"]),f("span",null,w(Math.abs(_.trend))+"%",1)])):ae("",!0)])])]),_:2},1032,["class"])]),_:2},1024))),128))]),_:1})]),a(Q,{gutter:20,class:"charts-section"},{default:r(()=>[a(O,{span:12},{default:r(()=>[a(z,null,{header:r(()=>[f("div",na,[c[9]||(c[9]=f("span",null,"能耗趋势分析",-1)),a(se,{modelValue:h.value,"onUpdate:modelValue":c[2]||(c[2]=_=>h.value=_),size:"small"},{default:r(()=>[a(N,{value:"energy"},{default:r(()=>c[6]||(c[6]=[D("能耗")])),_:1,__:[6]}),a(N,{value:"cost"},{default:r(()=>c[7]||(c[7]=[D("成本")])),_:1,__:[7]}),a(N,{value:"efficiency"},{default:r(()=>c[8]||(c[8]=[D("效率")])),_:1,__:[8]})]),_:1},8,["modelValue"])])]),default:r(()=>[f("div",{ref_key:"trendChartRef",ref:m,class:"chart"},null,512)]),_:1})]),_:1}),a(O,{span:12},{default:r(()=>[a(z,null,{header:r(()=>c[10]||(c[10]=[f("span",null,"功率分布分析",-1)])),default:r(()=>[f("div",{ref_key:"powerChartRef",ref:i,class:"chart"},null,512)]),_:1})]),_:1})]),_:1}),a(Q,{gutter:20,class:"charts-section"},{default:r(()=>[a(O,{span:12},{default:r(()=>[a(z,null,{header:r(()=>c[11]||(c[11]=[f("span",null,"效率热力图",-1)])),default:r(()=>[f("div",{ref_key:"heatmapChartRef",ref:d,class:"chart"},null,512)]),_:1})]),_:1}),a(O,{span:12},{default:r(()=>[a(z,null,{header:r(()=>c[12]||(c[12]=[f("span",null,"成本构成分析",-1)])),default:r(()=>[f("div",{ref_key:"costChartRef",ref:g,class:"chart"},null,512)]),_:1})]),_:1})]),_:1}),a(z,{class:"table-section"},{header:r(()=>[f("div",ra,[c[14]||(c[14]=f("span",null,"详细统计数据",-1)),a(Y,{size:"small",onClick:E,loading:x(e).loading},{default:r(()=>[a(P,null,{default:r(()=>[a(x(st))]),_:1}),c[13]||(c[13]=D(" 刷新数据 "))]),_:1,__:[13]},8,["loading"])])]),default:r(()=>[a(Tt,{"current-stats":x(e).currentStatistics,"previous-stats":x(e).previousStatistics,comparison:x(e).comparisonData},null,8,["current-stats","previous-stats","comparison"])]),_:1}),a(Nt,{modelValue:p.value,"onUpdate:modelValue":c[3]||(c[3]=_=>p.value=_),onExport:U},null,8,["modelValue"])])}}}),xa=de(oa,[["__scopeId","data-v-6be7e819"]]);export{xa as default};
