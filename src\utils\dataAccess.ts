import { BaseDAO } from './database'
import { dbManager } from './databaseConfig'
import type { 
  User, 
  Role, 
  Permission, 
  WarningInfo, 
  DeviceStatus, 
  SystemLog,
  PumpCurveData,
  EnergyData,
  OptimizationData,
  UserRole
} from '@/types'

/**
 * 用户数据访问对象
 */
export class UserDAO extends BaseDAO<User> {
  constructor() {
    super(dbManager, 'users')
  }

  /**
   * 根据用户名查找用户
   */
  async getByUsername(username: string): Promise<User | undefined> {
    const users = await this.getByIndex('username', username)
    return users[0]
  }

  /**
   * 根据邮箱查找用户
   */
  async getByEmail(email: string): Promise<User | undefined> {
    const users = await this.getByIndex('email', email)
    return users[0]
  }

  /**
   * 根据角色查找用户
   */
  async getByRole(role: UserRole): Promise<User[]> {
    return this.getByIndex('role', role)
  }

  /**
   * 根据状态查找用户
   */
  async getByStatus(status: User['status']): Promise<User[]> {
    return this.getByIndex('status', status)
  }

  /**
   * 根据部门查找用户
   */
  async getByDepartment(department: string): Promise<User[]> {
    return this.getByIndex('department', department)
  }

  /**
   * 更新最后登录时间
   */
  async updateLastLogin(userId: string): Promise<void> {
    const user = await this.getById(userId)
    if (user) {
      user.lastLogin = new Date().toISOString()
      await this.update(user)
    }
  }

  /**
   * 获取活跃用户
   */
  async getActiveUsers(): Promise<User[]> {
    return this.getByStatus('active')
  }
}

/**
 * 角色数据访问对象
 */
export class RoleDAO extends BaseDAO<Role> {
  constructor() {
    super(dbManager, 'roles')
  }

  /**
   * 获取系统角色
   */
  async getSystemRoles(): Promise<Role[]> {
    return this.getByIndex('isSystem', true)
  }

  /**
   * 获取自定义角色
   */
  async getCustomRoles(): Promise<Role[]> {
    return this.getByIndex('isSystem', false)
  }
}

/**
 * 权限数据访问对象
 */
export class PermissionDAO extends BaseDAO<Permission> {
  constructor() {
    super(dbManager, 'permissions')
  }

  /**
   * 根据类别获取权限
   */
  async getByCategory(category: string): Promise<Permission[]> {
    return this.getByIndex('category', category)
  }

  /**
   * 根据资源获取权限
   */
  async getByResource(resource: string): Promise<Permission[]> {
    return this.getByIndex('resource', resource)
  }
}

/**
 * 预警数据访问对象
 */
export class WarningDAO extends BaseDAO<WarningInfo> {
  constructor() {
    super(dbManager, 'warnings')
  }

  /**
   * 根据类别获取预警
   */
  async getByCategory(category: WarningInfo['category']): Promise<WarningInfo[]> {
    return this.getByIndex('category', category)
  }

  /**
   * 根据严重程度获取预警
   */
  async getBySeverity(severity: WarningInfo['severity']): Promise<WarningInfo[]> {
    return this.getByIndex('severity', severity)
  }

  /**
   * 获取未解决的预警
   */
  async getUnresolved(): Promise<WarningInfo[]> {
    return this.getByIndex('resolved', false)
  }

  /**
   * 获取已解决的预警
   */
  async getResolved(): Promise<WarningInfo[]> {
    return this.getByIndex('resolved', true)
  }

  /**
   * 根据设备获取预警
   */
  async getBySource(source: string): Promise<WarningInfo[]> {
    return this.getByIndex('source', source)
  }

  /**
   * 根据时间范围获取预警
   */
  async getByTimeRange(startTime: string, endTime: string): Promise<WarningInfo[]> {
    return new Promise((resolve, reject) => {
      const transaction = this.db.getDatabase().transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const index = store.index('timestamp')
      const range = IDBKeyRange.bound(startTime, endTime)
      const request = index.getAll(range)

      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 解决预警
   */
  async resolveWarning(warningId: string, resolvedBy: string): Promise<boolean> {
    const warning = await this.getById(warningId)
    if (warning && !warning.resolved) {
      warning.resolved = true
      warning.resolvedBy = resolvedBy
      warning.resolvedAt = new Date().toISOString()
      await this.update(warning)
      return true
    }
    return false
  }
}

/**
 * 设备数据访问对象
 */
export class DeviceDAO extends BaseDAO<DeviceStatus> {
  constructor() {
    super(dbManager, 'devices')
  }

  /**
   * 根据类型获取设备
   */
  async getByType(type: string): Promise<DeviceStatus[]> {
    return this.getByIndex('type', type)
  }

  /**
   * 根据状态获取设备
   */
  async getByStatus(status: DeviceStatus['status']): Promise<DeviceStatus[]> {
    return this.getByIndex('status', status)
  }

  /**
   * 获取在线设备
   */
  async getOnlineDevices(): Promise<DeviceStatus[]> {
    return this.getByStatus('online')
  }

  /**
   * 获取故障设备
   */
  async getFaultDevices(): Promise<DeviceStatus[]> {
    return this.getByStatus('fault')
  }

  /**
   * 根据健康度范围获取设备
   */
  async getByHealthRange(minHealth: number, maxHealth: number): Promise<DeviceStatus[]> {
    return new Promise((resolve, reject) => {
      const transaction = this.db.getDatabase().transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const index = store.index('health')
      const range = IDBKeyRange.bound(minHealth, maxHealth)
      const request = index.getAll(range)

      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 获取需要维护的设备
   */
  async getMaintenanceDue(beforeDate: string): Promise<DeviceStatus[]> {
    return new Promise((resolve, reject) => {
      const transaction = this.db.getDatabase().transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const index = store.index('nextMaintenance')
      const range = IDBKeyRange.upperBound(beforeDate)
      const request = index.getAll(range)

      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }
}

/**
 * 系统日志数据访问对象
 */
export class SystemLogDAO extends BaseDAO<SystemLog> {
  constructor() {
    super(dbManager, 'system_logs')
  }

  /**
   * 根据级别获取日志
   */
  async getByLevel(level: SystemLog['level']): Promise<SystemLog[]> {
    return this.getByIndex('level', level)
  }

  /**
   * 根据类别获取日志
   */
  async getByCategory(category: string): Promise<SystemLog[]> {
    return this.getByIndex('category', category)
  }

  /**
   * 根据用户获取日志
   */
  async getByUser(userId: string): Promise<SystemLog[]> {
    return this.getByIndex('userId', userId)
  }

  /**
   * 根据时间范围获取日志
   */
  async getByTimeRange(startTime: string, endTime: string): Promise<SystemLog[]> {
    return new Promise((resolve, reject) => {
      const transaction = this.db.getDatabase().transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const index = store.index('timestamp')
      const range = IDBKeyRange.bound(startTime, endTime)
      const request = index.getAll(range)

      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 清理旧日志
   */
  async cleanOldLogs(beforeDate: string): Promise<number> {
    const oldLogs = await this.getByTimeRange('1970-01-01', beforeDate)
    
    const operations = oldLogs.map(log => ({
      type: 'delete' as const,
      key: log.id
    }))

    if (operations.length > 0) {
      await this.batchOperation(operations)
    }

    return operations.length
  }
}

/**
 * 泵曲线数据访问对象
 */
export class PumpCurveDAO extends BaseDAO<PumpCurveData> {
  constructor() {
    super(dbManager, 'pump_curves')
  }

  /**
   * 根据类型获取泵曲线
   */
  async getByType(type: string): Promise<PumpCurveData[]> {
    return this.getByIndex('type', type)
  }

  /**
   * 获取活跃的泵曲线
   */
  async getActive(): Promise<PumpCurveData[]> {
    return this.getByIndex('isActive', true)
  }
}

/**
 * 能耗数据访问对象
 */
export class EnergyDataDAO extends BaseDAO<EnergyData> {
  constructor() {
    super(dbManager, 'energy_data')
  }

  /**
   * 根据设备获取能耗数据
   */
  async getByDevice(deviceId: string): Promise<EnergyData[]> {
    return this.getByIndex('deviceId', deviceId)
  }

  /**
   * 根据日期获取能耗数据
   */
  async getByDate(date: string): Promise<EnergyData[]> {
    return this.getByIndex('date', date)
  }

  /**
   * 根据时间范围获取能耗数据
   */
  async getByTimeRange(startTime: string, endTime: string): Promise<EnergyData[]> {
    return new Promise((resolve, reject) => {
      const transaction = this.db.getDatabase().transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const index = store.index('timestamp')
      const range = IDBKeyRange.bound(startTime, endTime)
      const request = index.getAll(range)

      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }
}

/**
 * 优化数据访问对象
 */
export class OptimizationDataDAO extends BaseDAO<OptimizationData> {
  constructor() {
    super(dbManager, 'optimization_data')
  }

  /**
   * 根据类型获取优化数据
   */
  async getByType(type: string): Promise<OptimizationData[]> {
    return this.getByIndex('type', type)
  }

  /**
   * 根据状态获取优化数据
   */
  async getByStatus(status: string): Promise<OptimizationData[]> {
    return this.getByIndex('status', status)
  }
}

// 导出数据访问对象实例
export const userDAO = new UserDAO()
export const roleDAO = new RoleDAO()
export const permissionDAO = new PermissionDAO()
export const warningDAO = new WarningDAO()
export const deviceDAO = new DeviceDAO()
export const systemLogDAO = new SystemLogDAO()
export const pumpCurveDAO = new PumpCurveDAO()
export const energyDataDAO = new EnergyDataDAO()
export const optimizationDataDAO = new OptimizationDataDAO()
