import { defineStore } from 'pinia'
import type { 
  EnergyData, 
  EnergyStatistics, 
  EnergyTrend, 
  EnergyComparison,
  EnergyAlert,
  StatisticsPeriod,
  ReportConfig
} from '@/types'
import { energyDataGenerator } from '@/utils/energyDataGenerator'
import dayjs from 'dayjs'

export const useEnergyStore = defineStore('energy', {
  state: () => ({
    // 原始能耗数据
    energyData: [] as EnergyData[],
    
    // 当前统计周期
    currentPeriod: 'day' as StatisticsPeriod,
    
    // 选择的日期范围
    dateRange: {
      start: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
      end: dayjs().format('YYYY-MM-DD')
    },
    
    // 统计数据
    currentStatistics: null as EnergyStatistics | null,
    previousStatistics: null as EnergyStatistics | null,
    
    // 趋势数据
    trendData: [] as EnergyTrend[],
    
    // 对比数据
    comparisonData: null as EnergyComparison | null,
    
    // 预警信息
    alerts: [] as EnergyAlert[],
    
    // 加载状态
    loading: false,
    
    // 报表配置
    reportConfig: {
      period: 'day' as StatisticsPeriod,
      startDate: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
      endDate: dayjs().format('YYYY-MM-DD'),
      includeCharts: true,
      includeComparison: true,
      includeTrends: true,
      format: 'pdf' as 'pdf' | 'excel' | 'csv'
    } as ReportConfig,
    
    // 实时数据更新
    realTimeEnabled: false,
    updateInterval: null as NodeJS.Timeout | null
  }),

  getters: {
    // 获取当前周期的数据
    getCurrentPeriodData: (state) => {
      if (!state.energyData.length) return []
      
      const start = dayjs(state.dateRange.start)
      const end = dayjs(state.dateRange.end).endOf('day')
      
      return state.energyData.filter(item => {
        const itemDate = dayjs(item.timestamp)
        return itemDate.isAfter(start) && itemDate.isBefore(end)
      })
    },
    
    // 获取能耗变化趋势
    getEnergyTrend: (state) => {
      if (!state.comparisonData) return 0
      return state.comparisonData.changePercent.totalEnergy
    },
    
    // 获取成本变化趋势
    getCostTrend: (state) => {
      if (!state.comparisonData) return 0
      return state.comparisonData.changePercent.totalCost
    },
    
    // 获取效率变化趋势
    getEfficiencyTrend: (state) => {
      if (!state.comparisonData) return 0
      return state.comparisonData.changePercent.avgEfficiency
    },
    
    // 获取未解决的预警数量
    getUnresolvedAlertsCount: (state) => {
      return state.alerts.filter(alert => !alert.resolved).length
    },
    
    // 获取关键性能指标
    getKPIs: (state) => {
      if (!state.currentStatistics) return null
      
      return {
        totalEnergy: state.currentStatistics.totalEnergy,
        totalCost: state.currentStatistics.totalCost,
        avgEfficiency: state.currentStatistics.avgEfficiency,
        energyPerUnit: state.currentStatistics.energyPerUnit,
        carbonEmission: state.currentStatistics.carbonEmission,
        loadFactor: state.currentStatistics.loadFactor
      }
    }
  },

  actions: {
    // 设置统计周期
    setPeriod(period: StatisticsPeriod) {
      this.currentPeriod = period
      this.updateDateRange()
      this.loadEnergyData()
    },
    
    // 设置日期范围
    setDateRange(start: string, end: string) {
      this.dateRange.start = start
      this.dateRange.end = end
      this.loadEnergyData()
    },
    
    // 更新日期范围根据周期
    updateDateRange() {
      const end = dayjs()
      let start: dayjs.Dayjs
      
      switch (this.currentPeriod) {
        case 'day':
          start = end.subtract(1, 'day')
          break
        case 'week':
          start = end.subtract(1, 'week')
          break
        case 'month':
          start = end.subtract(1, 'month')
          break
        case 'quarter':
          start = end.subtract(3, 'month')
          break
        case 'year':
          start = end.subtract(1, 'year')
          break
        default:
          start = end.subtract(1, 'day')
      }
      
      this.dateRange.start = start.format('YYYY-MM-DD')
      this.dateRange.end = end.format('YYYY-MM-DD')
    },
    
    // 加载能耗数据
    async loadEnergyData() {
      this.loading = true
      try {
        // 生成当前周期数据
        this.energyData = energyDataGenerator.generateEnergyData(
          this.dateRange.start,
          this.dateRange.end,
          15 // 15分钟间隔
        )
        
        // 计算当前统计数据
        if (this.energyData.length > 0) {
          this.currentStatistics = energyDataGenerator.calculateStatistics(
            this.energyData,
            this.currentPeriod
          )
        }
        
        // 生成对比数据
        await this.generateComparisonData()
        
        // 生成趋势数据
        this.generateTrendData()
        
        // 生成预警
        this.generateAlerts()
        
      } catch (error) {
        console.error('加载能耗数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 生成对比数据
    async generateComparisonData() {
      if (!this.currentStatistics) return
      
      try {
        // 计算上一个周期的日期范围
        const currentStart = dayjs(this.dateRange.start)
        const currentEnd = dayjs(this.dateRange.end)
        const duration = currentEnd.diff(currentStart, 'day')
        
        const previousStart = currentStart.subtract(duration + 1, 'day')
        const previousEnd = currentStart.subtract(1, 'day')
        
        // 生成上一周期数据
        const previousData = energyDataGenerator.generateEnergyData(
          previousStart.format('YYYY-MM-DD'),
          previousEnd.format('YYYY-MM-DD'),
          15
        )
        
        if (previousData.length > 0) {
          this.previousStatistics = energyDataGenerator.calculateStatistics(
            previousData,
            this.currentPeriod
          )
          
          // 计算变化百分比
          this.comparisonData = {
            current: this.currentStatistics,
            previous: this.previousStatistics,
            changePercent: {
              totalEnergy: this.calculateChangePercent(
                this.currentStatistics.totalEnergy,
                this.previousStatistics.totalEnergy
              ),
              totalCost: this.calculateChangePercent(
                this.currentStatistics.totalCost,
                this.previousStatistics.totalCost
              ),
              avgEfficiency: this.calculateChangePercent(
                this.currentStatistics.avgEfficiency,
                this.previousStatistics.avgEfficiency
              ),
              energyPerUnit: this.calculateChangePercent(
                this.currentStatistics.energyPerUnit,
                this.previousStatistics.energyPerUnit
              )
            }
          }
        }
      } catch (error) {
        console.error('生成对比数据失败:', error)
      }
    },
    
    // 计算变化百分比
    calculateChangePercent(current: number, previous: number): number {
      if (previous === 0) return 0
      return Math.round(((current - previous) / previous) * 10000) / 100
    },
    
    // 生成趋势数据
    generateTrendData() {
      const count = this.currentPeriod === 'day' ? 30 : 
                   this.currentPeriod === 'week' ? 12 : 
                   this.currentPeriod === 'month' ? 12 : 4
      
      this.trendData = energyDataGenerator.generateTrendData(this.currentPeriod, count)
    },
    
    // 生成预警
    generateAlerts() {
      this.alerts = energyDataGenerator.generateAlerts(this.energyData)
    },
    
    // 解决预警
    resolveAlert(alertId: string) {
      const alert = this.alerts.find(a => a.id === alertId)
      if (alert) {
        alert.resolved = true
      }
    },
    
    // 开启实时数据更新
    startRealTimeUpdate() {
      if (this.updateInterval) return
      
      this.realTimeEnabled = true
      this.updateInterval = setInterval(() => {
        this.loadEnergyData()
      }, 60000) // 每分钟更新一次
    },
    
    // 停止实时数据更新
    stopRealTimeUpdate() {
      if (this.updateInterval) {
        clearInterval(this.updateInterval)
        this.updateInterval = null
      }
      this.realTimeEnabled = false
    },
    
    // 导出报表
    async exportReport(config?: Partial<ReportConfig>) {
      if (config) {
        this.reportConfig = { ...this.reportConfig, ...config }
      }
      
      // 这里可以实现实际的报表导出逻辑
      console.log('导出报表配置:', this.reportConfig)
      
      // 模拟导出过程
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            success: true,
            filename: `energy_report_${dayjs().format('YYYYMMDD_HHmmss')}.${this.reportConfig.format}`,
            url: '#' // 实际应用中这里应该是下载链接
          })
        }, 2000)
      })
    },
    
    // 重置数据
    resetData() {
      this.energyData = []
      this.currentStatistics = null
      this.previousStatistics = null
      this.trendData = []
      this.comparisonData = null
      this.alerts = []
    }
  }
})
