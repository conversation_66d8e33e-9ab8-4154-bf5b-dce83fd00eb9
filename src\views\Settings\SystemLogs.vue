<template>
  <div class="system-logs">
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><Document /></el-icon>
          <span>系统日志</span>
          <div class="header-actions">
            <el-button @click="exportLogs" :loading="exporting">
              <el-icon><Download /></el-icon>
              导出日志
            </el-button>
            <el-button type="danger" @click="clearLogs">
              <el-icon><Delete /></el-icon>
              清空日志
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 日志统计 -->
      <div class="log-stats">
        <el-row :gutter="20">
          <el-col :span="4" v-for="(count, level) in settingsStore.getLogLevelStats" :key="level">
            <div class="stat-card" :class="level">
              <div class="stat-icon">
                <el-icon size="20">
                  <component :is="getLevelIcon(level)" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ count }}</div>
                <div class="stat-label">{{ getLevelText(level) }}</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <!-- 筛选条件 -->
      <div class="log-filters">
        <el-row :gutter="16">
          <el-col :span="4">
            <el-select v-model="filters.level" placeholder="选择级别" clearable>
              <el-option label="调试" value="debug" />
              <el-option label="信息" value="info" />
              <el-option label="警告" value="warn" />
              <el-option label="错误" value="error" />
              <el-option label="严重" value="fatal" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="filters.category" placeholder="选择类别" clearable>
              <el-option label="系统" value="system" />
              <el-option label="用户" value="user" />
              <el-option label="设备" value="device" />
              <el-option label="预警" value="warning" />
              <el-option label="安全" value="security" />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleDateChange"
            />
          </el-col>
          <el-col :span="4">
            <el-input v-model="searchText" placeholder="搜索日志内容" clearable>
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-button @click="clearFilters">清除筛选</el-button>
          </el-col>
        </el-row>
      </div>
      
      <!-- 日志表格 -->
      <el-table 
        :data="filteredLogs" 
        stripe 
        v-loading="settingsStore.loading"
        height="500"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="timestamp" label="时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.timestamp) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="level" label="级别" width="80">
          <template #default="{ row }">
            <el-tag :type="getLevelType(row.level)" size="small">
              {{ getLevelText(row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="category" label="类别" width="80">
          <template #default="{ row }">
            <el-tag type="info" size="small">
              {{ getCategoryText(row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="message" label="消息" min-width="300">
          <template #default="{ row }">
            <div class="log-message" :title="row.message">
              {{ row.message }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="userId" label="用户" width="100">
          <template #default="{ row }">
            <span v-if="row.userId">{{ getUserName(row.userId) }}</span>
            <span v-else class="text-muted">系统</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="ip" label="IP地址" width="120" />
        
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button size="small" @click="showLogDetail(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 批量操作 -->
      <div v-if="selectedLogs.length > 0" class="batch-actions">
        <el-alert
          :title="`已选择 ${selectedLogs.length} 条日志`"
          type="info"
          :closable="false"
        >
          <template #default>
            <el-button size="small" @click="batchExport">
              批量导出
            </el-button>
            <el-button size="small" type="danger" @click="batchDelete">
              批量删除
            </el-button>
          </template>
        </el-alert>
      </div>
    </el-card>
    
    <!-- 日志详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="日志详情" width="800px">
      <div v-if="selectedLog" class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="时间">
            {{ formatTime(selectedLog.timestamp) }}
          </el-descriptions-item>
          <el-descriptions-item label="级别">
            <el-tag :type="getLevelType(selectedLog.level)">
              {{ getLevelText(selectedLog.level) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="类别">
            {{ getCategoryText(selectedLog.category) }}
          </el-descriptions-item>
          <el-descriptions-item label="用户">
            {{ selectedLog.userId ? getUserName(selectedLog.userId) : '系统' }}
          </el-descriptions-item>
          <el-descriptions-item label="IP地址">
            {{ selectedLog.ip || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="用户代理">
            {{ selectedLog.userAgent || '-' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="log-message-detail">
          <h4>消息内容</h4>
          <pre>{{ selectedLog.message }}</pre>
        </div>
        
        <div v-if="selectedLog.details" class="log-details">
          <h4>详细信息</h4>
          <pre>{{ JSON.stringify(selectedLog.details, null, 2) }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Document,
  Download,
  Delete,
  Search,
  InfoFilled,
  Warning,
  CircleClose,
  CircleCheck,
  QuestionFilled
} from '@element-plus/icons-vue'
import { useSettingsStore } from '@/stores/settings'
import type { SystemLog } from '@/types'
import dayjs from 'dayjs'
import { ElMessage, ElMessageBox } from 'element-plus'

const settingsStore = useSettingsStore()

// 响应式数据
const exporting = ref(false)
const showDetailDialog = ref(false)
const selectedLog = ref<SystemLog | null>(null)
const selectedLogs = ref<SystemLog[]>([])
const searchText = ref('')
const dateRange = ref<[string, string] | null>(null)

const filters = ref({
  level: null as SystemLog['level'] | null,
  category: null as string | null
})

// 计算属性
const filteredLogs = computed(() => {
  let logs = [...settingsStore.systemLogs]
  
  if (filters.value.level) {
    logs = logs.filter(l => l.level === filters.value.level)
  }
  
  if (filters.value.category) {
    logs = logs.filter(l => l.category === filters.value.category)
  }
  
  if (dateRange.value) {
    const [start, end] = dateRange.value
    logs = logs.filter(l => {
      const logDate = new Date(l.timestamp)
      return logDate >= new Date(start) && logDate <= new Date(end)
    })
  }
  
  if (searchText.value) {
    logs = logs.filter(l => 
      l.message.toLowerCase().includes(searchText.value.toLowerCase())
    )
  }
  
  return logs
})

// 方法
const getLevelIcon = (level: string) => {
  const icons = {
    debug: QuestionFilled,
    info: InfoFilled,
    warn: Warning,
    error: CircleClose,
    fatal: CircleClose
  }
  return icons[level as keyof typeof icons] || InfoFilled
}

const getLevelType = (level: SystemLog['level']) => {
  const types = {
    debug: 'info',
    info: 'success',
    warn: 'warning',
    error: 'danger',
    fatal: 'danger'
  }
  return types[level] || 'info'
}

const getLevelText = (level: SystemLog['level']) => {
  const texts = {
    debug: '调试',
    info: '信息',
    warn: '警告',
    error: '错误',
    fatal: '严重'
  }
  return texts[level] || level
}

const getCategoryText = (category: string) => {
  const texts = {
    system: '系统',
    user: '用户',
    device: '设备',
    warning: '预警',
    security: '安全'
  }
  return texts[category as keyof typeof texts] || category
}

const getUserName = (userId: string) => {
  const user = settingsStore.users.find(u => u.id === userId)
  return user?.fullName || userId
}

const formatTime = (timestamp: string) => {
  return dayjs(timestamp).format('MM-DD HH:mm:ss')
}

const handleDateChange = (dates: [string, string] | null) => {
  if (dates) {
    settingsStore.setLogFilter('dateRange', dates)
  } else {
    settingsStore.setLogFilter('dateRange', null)
  }
}

const handleSelectionChange = (selection: SystemLog[]) => {
  selectedLogs.value = selection
}

const showLogDetail = (log: SystemLog) => {
  selectedLog.value = log
  showDetailDialog.value = true
}

const exportLogs = async () => {
  exporting.value = true
  try {
    const result = await settingsStore.exportLogs('csv')
    if (result.success) {
      ElMessage.success(`日志导出成功：${result.filename}`)
    }
  } catch {
    ElMessage.error('日志导出失败')
  } finally {
    exporting.value = false
  }
}

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有日志吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await settingsStore.clearLogs()
    ElMessage.success('日志清空成功')
  } catch {
    // 用户取消
  }
}

const batchExport = async () => {
  try {
    ElMessage.success(`导出 ${selectedLogs.value.length} 条日志`)
  } catch {
    ElMessage.error('批量导出失败')
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedLogs.value.length} 条日志吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success(`删除 ${selectedLogs.value.length} 条日志`)
    selectedLogs.value = []
  } catch {
    // 用户取消
  }
}

const clearFilters = () => {
  filters.value = {
    level: null,
    category: null
  }
  dateRange.value = null
  searchText.value = ''
  settingsStore.clearLogFilters()
}
</script>

<style lang="scss" scoped>
.system-logs {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    > div:first-child {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
    }
    
    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .log-stats {
    margin-bottom: 20px;
    
    .stat-card {
      display: flex;
      align-items: center;
      padding: 12px;
      border-radius: 8px;
      border: 1px solid var(--el-border-color-light);
      
      .stat-icon {
        margin-right: 8px;
      }
      
      .stat-info {
        .stat-value {
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 2px;
        }
        
        .stat-label {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }
      
      &.debug { border-left: 4px solid #909399; }
      &.info { border-left: 4px solid #67C23A; }
      &.warn { border-left: 4px solid #E6A23C; }
      &.error { border-left: 4px solid #F56C6C; }
      &.fatal { border-left: 4px solid #F56C6C; }
    }
  }
  
  .log-filters {
    margin-bottom: 20px;
  }
  
  .log-message {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .text-muted {
    color: var(--el-text-color-regular);
  }
  
  .batch-actions {
    margin-top: 16px;
  }
}

.log-detail {
  .log-message-detail,
  .log-details {
    margin-top: 20px;
    
    h4 {
      margin-bottom: 10px;
      color: var(--el-text-color-primary);
    }
    
    pre {
      background-color: var(--el-bg-color-page);
      padding: 12px;
      border-radius: 4px;
      font-size: 12px;
      line-height: 1.5;
      overflow-x: auto;
    }
  }
}
</style>
