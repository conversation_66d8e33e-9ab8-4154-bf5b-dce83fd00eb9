function m(i,r,t){const e={type:"value",min:0,splitNumber:11,axisLine:{lineStyle:{color:"#333"}}},{max:s,interval:n,showFormatter:o}=c(i,r);switch(e.max=s,e.interval=n,e.axisLabel={formatter:o},r){case"power":e.name="功率 (kW)";break;case"npsh":e.name="NPSH (m)";break;case"head":e.name="扬程 (m)";break;case"efficiency":e.name="效率 (%)";break;default:e.name=""}return t&&Object.assign(e,t),e}function c(i,r){let t,e;if(r==="efficiency")return t=20,e=220,{max:e,interval:t,showFormatter:function(n){return n<=100?String(n):""}};if(r==="head")return t=5,e=55,{max:e,interval:t,showFormatter:function(n){return String(n)}};if(r==="power")i<=20?(t=5,e=35):i<=50?(t=10,e=70):i<=100?(t=20,e=140):(t=Math.ceil(i/7),e=t*7);else if(r==="npsh")i<=5?(t=1,e=7):i<=10?(t=2,e=14):i<=20?(t=4,e=28):(t=Math.ceil(i/7),e=t*7);else{i<=10?t=1:i<=50?t=5:i<=100?t=10:i<=500?t=50:t=100,e=Math.ceil(i/t)*t+t;const n=10;e=Math.ceil(e/(t*n))*t*n}return{max:e,interval:t,showFormatter:function(n){return String(n)}}}function h(i,r){let e=0,s=0;i<=20?(s=5,e=35):i<=50?(s=10,e=70):i<=100?(s=20,e=140):(s=Math.ceil(i/7),e=s*7);const n={type:"value",name:"功率 (kW)",min:0,max:e,interval:s,splitNumber:7,axisLine:{lineStyle:{color:"#333"}},axisLabel:{formatter:function(f){return String(f)}}};let o=0,l=0;return r<=5?(l=1,o=7):r<=10?(l=2,o=14):r<=20?(l=4,o=28):(l=Math.ceil(r/7),o=l*7),[n,{type:"value",name:"NPSH (m)",min:0,max:o,interval:l,splitNumber:7,position:"right",axisLine:{lineStyle:{color:"#333"}},axisLabel:{formatter:function(f){return String(f)}}}]}export{h as a,m as c};
