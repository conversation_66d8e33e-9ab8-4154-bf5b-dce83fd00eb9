<template>
  <el-drawer
    v-model="visible"
    title="设备详情"
    direction="rtl"
    size="50%"
  >
    <div v-if="device" class="device-detail">
      <el-descriptions title="基本信息" :column="2" border>
        <el-descriptions-item label="设备ID">
          {{ device.id }}
        </el-descriptions-item>
        <el-descriptions-item label="设备名称">
          {{ device.name }}
        </el-descriptions-item>
        <el-descriptions-item label="设备类型">
          {{ device.type }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="statusType">{{ statusText }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="最后更新">
          {{ device.lastUpdate }}
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions title="运行参数" :column="2" border class="mt-4">
        <el-descriptions-item label="温度">
          {{ device.parameters?.temperature }}°C
        </el-descriptions-item>
        <el-descriptions-item label="振动">
          {{ device.parameters?.vibration }} mm/s
        </el-descriptions-item>
        <el-descriptions-item label="压力">
          {{ device.parameters?.pressure }} bar
        </el-descriptions-item>
        <el-descriptions-item label="流量">
          {{ device.parameters?.flow }} m³/h
        </el-descriptions-item>
        <el-descriptions-item label="功率">
          {{ device.parameters?.power }} kW
        </el-descriptions-item>
        <el-descriptions-item label="效率">
          {{ device.parameters?.efficiency }}%
        </el-descriptions-item>
      </el-descriptions>

      <div class="mt-4">
        <h3>健康度</h3>
        <el-progress :percentage="device.health" :color="healthColor" />
      </div>

      <div class="mt-4">
        <h3>下次维护时间</h3>
        <p>{{ device.nextMaintenance }}</p>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { DeviceStatus } from '@/types'

interface Props {
  visible: boolean
  device: DeviceStatus | null
}

const props = defineProps<Props>()

const statusType = computed(() => {
  if (!props.device) return 'info'
  switch (props.device.status) {
    case 'online':
      return 'success'
    case 'offline':
      return 'danger'
    case 'fault':
      return 'danger'
    case 'maintenance':
      return 'warning'
    default:
      return 'info'
  }
})

const statusText = computed(() => {
  if (!props.device) return '未知'
  switch (props.device.status) {
    case 'online':
      return '在线'
    case 'offline':
      return '离线'
    case 'fault':
      return '故障'
    case 'maintenance':
      return '维护中'
    default:
      return '未知'
  }
})

const healthColor = computed(() => {
  if (!props.device) return '#909399'
  const health = props.device.health
  if (health >= 80) return '#67c23a'
  if (health >= 60) return '#e6a23c'
  return '#f56c6c'
})
</script>

<style scoped>
.device-detail {
  padding: 20px;
}

.mt-4 {
  margin-top: 16px;
}
</style>