<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { 
  Refresh, 
  <PERSON><PERSON>hart, 
  Lightning, 
  Cpu, 
  Stopwatch, 
  Opportunity, 
  Monitor,
  DCaret,
  Odometer,
  Histogram,
  List,
  WarningFilled
} from '@element-plus/icons-vue';
import type { InverterData } from '../../types/gateway';
import * as gatewayApi from '../../api/gateway';

// 属性
const props = defineProps<{
  deviceId?: string | null;
}>();

// 状态
const loading = ref(true);
const deviceData = ref<InverterData | null>(null);
const autoRefresh = ref(false);
const refreshInterval = ref(5000);
const lastUpdateTime = ref<number | null>(null);
const refreshTimer = ref<number | null>(null);

// 计算属性
const temperatureClass = computed(() => {
  if (!deviceData.value) return '';
  
  const temp = deviceData.value.temperature;
  if (temp > 80) return 'critical';
  if (temp > 60) return 'warning';
  return 'normal';
});

const dataTable = computed(() => {
  if (!deviceData.value) return [];
  
  const data: { key: string; value: string; unit: string }[] = [
    { key: '状态', value: deviceData.value.runningStatus ? '运行中' : '已停止', unit: '' },
    { key: '频率', value: formatValue(deviceData.value.frequency), unit: 'Hz' },
    { key: '电流', value: formatValue(deviceData.value.current), unit: 'A' },
    { key: '电压', value: formatValue(deviceData.value.voltage), unit: 'V' },
    { key: '功率', value: formatValue(deviceData.value.power), unit: 'kW' },
    { key: '温度', value: formatValue(deviceData.value.temperature), unit: '°C' },
  ];
  
  if (deviceData.value.rotationSpeed !== undefined) {
    data.push({ key: '转速', value: formatValue(deviceData.value.rotationSpeed), unit: 'RPM' });
  }
  
  if (deviceData.value.flowRate !== undefined) {
    data.push({ key: '流量', value: formatValue(deviceData.value.flowRate), unit: 'm³/h' });
  }
  
  if (deviceData.value.pressure !== undefined) {
    data.push({ key: '压力', value: formatValue(deviceData.value.pressure), unit: 'MPa' });
  }
  
  if (deviceData.value.torque !== undefined) {
    data.push({ key: '扭矩', value: formatValue(deviceData.value.torque), unit: 'N·m' });
  }
  
  if (deviceData.value.errorCode) {
    data.push({ key: '错误代码', value: String(deviceData.value.errorCode), unit: '' });
    data.push({ key: '错误信息', value: deviceData.value.errorMessage || '未知错误', unit: '' });
  }
  
  return data;
});

// 生命周期
onMounted(() => {
  loadDeviceData();
});

onUnmounted(() => {
  stopAutoRefresh();
});

// 监听属性变化
watch(() => props.deviceId, () => {
  loadDeviceData();
});

// 监听自动刷新设置
watch([autoRefresh, refreshInterval], () => {
  stopAutoRefresh();
  if (autoRefresh.value) {
    startAutoRefresh();
  }
});

// 加载设备数据
async function loadDeviceData() {
  if (!props.deviceId) {
    deviceData.value = null;
    loading.value = false;
    return;
  }
  
  try {
    loading.value = true;
    const data = await gatewayApi.getDeviceData(props.deviceId);
    
    if (data) {
      deviceData.value = data;
      lastUpdateTime.value = Date.now();
    }
  } catch (error) {
    console.error('加载设备数据失败:', error);
  } finally {
    loading.value = false;
  }
}

// 格式化数值
function formatValue(value: number | undefined): string {
  if (value === undefined || value === null) return '-';
  return value.toFixed(2);
}

// 格式化日期时间
function formatDateTime(timestamp: number): string {
  const date = new Date(timestamp);
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
}

// 开始自动刷新
function startAutoRefresh() {
  refreshTimer.value = window.setInterval(() => {
    loadDeviceData();
  }, refreshInterval.value);
}

// 停止自动刷新
function stopAutoRefresh() {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value);
    refreshTimer.value = null;
  }
}
</script>

<template>
  <div class="device-data-panel">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>
    
    <div v-else-if="!deviceData" class="no-data">
      <el-empty description="暂无设备数据">
        <template #extra>
          <el-button type="primary" @click="loadDeviceData">刷新数据</el-button>
        </template>
      </el-empty>
    </div>
    
    <template v-else>
      <div class="auto-refresh">
        <el-switch
          v-model="autoRefresh"
          inline-prompt
          active-text="自动刷新"
          inactive-text="手动刷新"
        />
        <el-select 
          v-model="refreshInterval" 
          size="small" 
          :disabled="!autoRefresh"
          style="width: 100px; margin-left: 8px;"
        >
          <el-option label="1秒" :value="1000" />
          <el-option label="3秒" :value="3000" />
          <el-option label="5秒" :value="5000" />
          <el-option label="10秒" :value="10000" />
        </el-select>
        <el-button size="small" @click="loadDeviceData" style="margin-left: 8px;">
          <el-icon><Refresh /></el-icon> 刷新
        </el-button>
        <span class="last-update">
          最后更新: {{ lastUpdateTime ? formatDateTime(lastUpdateTime) : '未更新' }}
        </span>
      </div>
      
      <div class="data-grid">
        <el-card class="data-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><PieChart /></el-icon>
              <span>运行状态</span>
            </div>
          </template>
          <div class="data-value" :class="deviceData.runningStatus ? 'running' : 'stopped'">
            {{ deviceData.runningStatus ? '运行中' : '已停止' }}
          </div>
        </el-card>
        
        <el-card class="data-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Lightning /></el-icon>
              <span>电压</span>
            </div>
          </template>
          <div class="data-value">
            {{ formatValue(deviceData.voltage) }} <span class="unit">V</span>
          </div>
        </el-card>
        
        <el-card class="data-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Cpu /></el-icon>
              <span>电流</span>
            </div>
          </template>
          <div class="data-value">
            {{ formatValue(deviceData.current) }} <span class="unit">A</span>
          </div>
        </el-card>
        
        <el-card class="data-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Stopwatch /></el-icon>
              <span>频率</span>
            </div>
          </template>
          <div class="data-value">
            {{ formatValue(deviceData.frequency) }} <span class="unit">Hz</span>
          </div>
        </el-card>
        
        <el-card class="data-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Opportunity /></el-icon>
              <span>功率</span>
            </div>
          </template>
          <div class="data-value">
            {{ formatValue(deviceData.power) }} <span class="unit">kW</span>
          </div>
        </el-card>
        
        <el-card class="data-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Monitor /></el-icon>
              <span>温度</span>
            </div>
          </template>
          <div class="data-value" :class="temperatureClass">
            {{ formatValue(deviceData.temperature) }} <span class="unit">°C</span>
          </div>
        </el-card>
        
        <el-card v-if="deviceData.rotationSpeed !== undefined" class="data-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><DCaret /></el-icon>
              <span>转速</span>
            </div>
          </template>
          <div class="data-value">
            {{ formatValue(deviceData.rotationSpeed) }} <span class="unit">RPM</span>
          </div>
        </el-card>
        
        <el-card v-if="deviceData.flowRate !== undefined" class="data-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Odometer /></el-icon>
              <span>流量</span>
            </div>
          </template>
          <div class="data-value">
            {{ formatValue(deviceData.flowRate) }} <span class="unit">m³/h</span>
          </div>
        </el-card>
        
        <el-card v-if="deviceData.pressure !== undefined" class="data-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Histogram /></el-icon>
              <span>压力</span>
            </div>
          </template>
          <div class="data-value">
            {{ formatValue(deviceData.pressure) }} <span class="unit">MPa</span>
          </div>
        </el-card>
        
        <el-card v-if="deviceData.torque !== undefined" class="data-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><List /></el-icon>
              <span>扭矩</span>
            </div>
          </template>
          <div class="data-value">
            {{ formatValue(deviceData.torque) }} <span class="unit">N·m</span>
          </div>
        </el-card>
      </div>
      
      <el-card class="error-info" v-if="deviceData.errorCode">
        <template #header>
          <div class="card-header error">
            <el-icon><WarningFilled /></el-icon>
            <span>错误信息</span>
          </div>
        </template>
        <div class="error-details">
          <p class="error-code">错误代码: {{ deviceData.errorCode }}</p>
          <p class="error-message">{{ deviceData.errorMessage || '未知错误' }}</p>
        </div>
      </el-card>
      
      <el-divider />
      
      <div class="data-table">
        <h3>原始数据</h3>
        <el-table :data="dataTable" stripe style="width: 100%">
          <el-table-column prop="key" label="参数" width="180" />
          <el-table-column prop="value" label="数值" />
          <el-table-column prop="unit" label="单位" width="100" />
        </el-table>
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
.device-data-panel {
  padding: 10px;
  
  .loading-container {
    padding: 20px;
  }
  
  .no-data {
    padding: 30px 0;
  }
  
  .auto-refresh {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    
    .last-update {
      margin-left: auto;
      color: #909399;
      font-size: 13px;
    }
  }
  
  .data-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 20px;
    
    .data-card {
      .card-header {
        display: flex;
        align-items: center;
        font-size: 14px;
        
        .el-icon {
          margin-right: 8px;
          font-size: 18px;
        }
      }
      
      .data-value {
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        padding: 10px 0;
        
        .unit {
          font-size: 14px;
          font-weight: normal;
          color: #909399;
          margin-left: 4px;
        }
        
        &.running {
          color: #67C23A;
        }
        
        &.stopped {
          color: #909399;
        }
        
        &.warning {
          color: #E6A23C;
        }
        
        &.critical {
          color: #F56C6C;
        }
      }
    }
  }
  
  .error-info {
    margin-bottom: 20px;
    
    .card-header.error {
      color: #F56C6C;
      
      .el-icon {
        color: #F56C6C;
      }
    }
    
    .error-details {
      padding: 10px;
      
      .error-code {
        font-weight: bold;
        margin-bottom: 8px;
      }
      
      .error-message {
        color: #F56C6C;
      }
    }
  }
  
  .data-table {
    margin-top: 20px;
    
    h3 {
      margin-top: 0;
      margin-bottom: 16px;
    }
  }
}

@media (max-width: 1200px) {
  .data-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .data-grid {
    grid-template-columns: 1fr;
  }
  
  .auto-refresh {
    flex-wrap: wrap;
    
    .last-update {
      width: 100%;
      margin-left: 0;
      margin-top: 8px;
    }
  }
}
</style> 