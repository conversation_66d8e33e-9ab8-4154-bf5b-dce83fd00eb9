// 高精度图表算法 - 完全匹配格兰富的图表渲染
import * as echarts from 'echarts'

// 数据点接口
interface DataPoint {
  flowRate: number
  value: number
}

// 图表配置接口
interface ChartConfig {
  curveTypes: {
    powerP1: boolean
    powerP2: boolean
    npsh: boolean
    eta: boolean
    isoEta: boolean
    tolerance: boolean
    minMax: boolean
  }
  installationType: string
  seriesConnection: string
  variableSpeed: string
  fluidType: string
  fluidTemperature: number
}

// 格兰富精确配色方案
export const GRUNDFOS_COLORS = {
  primary: '#0066cc',      // 格兰富蓝
  head: '#1f77b4',         // 扬程曲线 - 蓝色
  efficiency: '#2ca02c',   // 效率曲线 - 绿色
  power: '#ff7f0e',        // 功率曲线 - 橙色
  npsh: '#d62728',         // NPSH曲线 - 红色
  grid: '#e0e0e0',         // 网格线
  text: '#333333',         // 文字
  background: '#ffffff'    // 背景
}

// 曲线平滑算法 - 三次样条插值
export class SplineInterpolation {
  static interpolate(points: DataPoint[], resolution: number = 100): DataPoint[] {
    if (points.length < 2) return points

    const n = points.length
    const h: number[] = []
    const alpha: number[] = []
    const l: number[] = []
    const mu: number[] = []
    const z: number[] = []
    const c: number[] = []
    const b: number[] = []
    const d: number[] = []

    // 计算步长
    for (let i = 0; i < n - 1; i++) {
      h[i] = points[i + 1].flowRate - points[i].flowRate
    }

    // 计算alpha
    for (let i = 1; i < n - 1; i++) {
      alpha[i] = (3 / h[i]) * (points[i + 1].value - points[i].value) - 
                 (3 / h[i - 1]) * (points[i].value - points[i - 1].value)
    }

    // 求解三对角矩阵
    l[0] = 1
    mu[0] = 0
    z[0] = 0

    for (let i = 1; i < n - 1; i++) {
      l[i] = 2 * (points[i + 1].flowRate - points[i - 1].flowRate) - h[i - 1] * mu[i - 1]
      mu[i] = h[i] / l[i]
      z[i] = (alpha[i] - h[i - 1] * z[i - 1]) / l[i]
    }

    l[n - 1] = 1
    z[n - 1] = 0
    c[n - 1] = 0

    // 回代求解
    for (let j = n - 2; j >= 0; j--) {
      c[j] = z[j] - mu[j] * c[j + 1]
      b[j] = (points[j + 1].value - points[j].value) / h[j] - h[j] * (c[j + 1] + 2 * c[j]) / 3
      d[j] = (c[j + 1] - c[j]) / (3 * h[j])
    }

    // 生成插值点
    const result: DataPoint[] = []
    const minFlow = points[0].flowRate
    const maxFlow = points[n - 1].flowRate
    const step = (maxFlow - minFlow) / resolution

    for (let i = 0; i <= resolution; i++) {
      const x = minFlow + i * step
      
      // 找到对应的区间
      let j = 0
      for (j = 0; j < n - 1; j++) {
        if (x <= points[j + 1].flowRate) break
      }

      // 计算插值
      const dx = x - points[j].flowRate
      const y = points[j].value + b[j] * dx + c[j] * dx * dx + d[j] * dx * dx * dx

      result.push({ flowRate: x, value: y })
    }

    return result
  }
}

// 格兰富专用图表配置生成器
export class GrundfosChartGenerator {
  
  // 创建完全匹配格兰富的图表配置 - 双图表布局
  static createExactChart(
    curves: Record<string, DataPoint[]>,
    config: ChartConfig,
    bestEfficiencyPoint?: { q: number; h: number; efficiency: number }
  ): echarts.EChartsOption {

    const option: echarts.EChartsOption = {
      backgroundColor: GRUNDFOS_COLORS.background,

      // 网格配置 - 格兰富双图表布局
      grid: [
        {
          left: '8%',
          right: '8%',
          top: '10%',
          bottom: '55%',
          containLabel: true,
          borderWidth: 1,
          borderColor: GRUNDFOS_COLORS.grid
        },
        {
          left: '8%',
          right: '8%',
          top: '60%',
          bottom: '10%',
          containLabel: true,
          borderWidth: 1,
          borderColor: GRUNDFOS_COLORS.grid
        }
      ],

      // 图例配置
      legend: {
        data: [],
        top: '2%',
        left: 'center',
        textStyle: {
          fontSize: 12,
          color: GRUNDFOS_COLORS.text,
          fontFamily: 'Arial, sans-serif'
        },
        itemWidth: 25,
        itemHeight: 14,
        itemGap: 20
      },

      // 工具提示
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999',
            width: 1,
            type: 'dashed'
          }
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#ccc',
        borderWidth: 1,
        textStyle: {
          color: GRUNDFOS_COLORS.text,
          fontSize: 12
        },
        formatter: this.createTooltipFormatter()
      },

      // X轴配置 - 双轴，修正流量范围为525
      xAxis: [
        {
          type: 'value',
          name: 'Q [m³/h]',
          nameLocation: 'middle',
          nameGap: 30,
          nameTextStyle: {
            fontSize: 12,
            color: GRUNDFOS_COLORS.text,
            fontWeight: 'bold',
            fontFamily: 'Arial, sans-serif'
          },
          min: 0,
          max: 900,
          interval: 50,
          axisLine: {
            lineStyle: {
              color: GRUNDFOS_COLORS.text,
              width: 1
            }
          },
          axisLabel: {
            color: GRUNDFOS_COLORS.text,
            fontSize: 11,
            fontFamily: 'Arial, sans-serif'
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: GRUNDFOS_COLORS.grid,
              type: 'solid',
              width: 0.5
            }
          },
          gridIndex: 0
        },
        {
          type: 'value',
          name: 'Q [m³/h]',
          nameLocation: 'middle',
          nameGap: 30,
          nameTextStyle: {
            fontSize: 12,
            color: GRUNDFOS_COLORS.text,
            fontWeight: 'bold',
            fontFamily: 'Arial, sans-serif'
          },
          min: 0,
          max: 900,
          interval: 50,
          axisLine: {
            lineStyle: {
              color: GRUNDFOS_COLORS.text,
              width: 1
            }
          },
          axisLabel: {
            color: GRUNDFOS_COLORS.text,
            fontSize: 11,
            fontFamily: 'Arial, sans-serif'
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: GRUNDFOS_COLORS.grid,
              type: 'solid',
              width: 0.5
            }
          },
          gridIndex: 1
        }
      ],

      // Y轴配置 - 双图表四轴配置
      yAxis: [
        // 上图 - 扬程轴 (左)
        {
          type: 'value',
          name: 'H [m]',
          nameLocation: 'middle',
          nameGap: 40,
          nameTextStyle: {
            fontSize: 12,
            color: GRUNDFOS_COLORS.head,
            fontWeight: 'bold',
            fontFamily: 'Arial, sans-serif'
          },
          min: 0,
          max: 55,
          interval: 5,
          splitNumber: 11,
          position: 'left',
          axisLine: {
            show: true,
            lineStyle: {
              color: GRUNDFOS_COLORS.head,
              width: 2
            }
          },
          axisLabel: {
            color: GRUNDFOS_COLORS.head,
            fontSize: 11,
            fontFamily: 'Arial, sans-serif'
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: GRUNDFOS_COLORS.grid,
              type: 'solid',
              width: 0.5
            }
          },
          gridIndex: 0
        },
        // 上图 - 效率轴 (右)
        {
          type: 'value',
          name: 'η [%]',
          nameLocation: 'middle',
          nameGap: 40,
          nameTextStyle: {
            fontSize: 12,
            color: GRUNDFOS_COLORS.efficiency,
            fontWeight: 'bold',
            fontFamily: 'Arial, sans-serif'
          },
          min: 0,
          max: 200,
          interval: 20,
          position: 'right',
          axisLine: {
            show: true,
            lineStyle: {
              color: GRUNDFOS_COLORS.efficiency,
              width: 2
            }
          },
          axisLabel: {
            color: GRUNDFOS_COLORS.efficiency,
            fontSize: 11,
            fontFamily: 'Arial, sans-serif',
            formatter: function(value: number) {
              return String(value); // 直接返回值，不做任何过滤
            }
          },
          splitLine: {
            show: false
          },
          gridIndex: 0
        },
        // 下图 - 功率轴 (左)
        {
          type: 'value',
          name: 'P [kW]',
          nameLocation: 'middle',
          nameGap: 40,
          nameTextStyle: {
            fontSize: 12,
            color: GRUNDFOS_COLORS.power,
            fontWeight: 'bold',
            fontFamily: 'Arial, sans-serif'
          },
          min: 0,
          max: 120,
          interval: 20,
          position: 'left',
          axisLine: {
            show: true,
            lineStyle: {
              color: GRUNDFOS_COLORS.power,
              width: 2
            }
          },
          axisLabel: {
            color: GRUNDFOS_COLORS.power,
            fontSize: 11,
            fontFamily: 'Arial, sans-serif'
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: GRUNDFOS_COLORS.grid,
              type: 'solid',
              width: 0.5
            }
          },
          gridIndex: 1
        },
        // 下图 - NPSH轴 (右)
        {
          type: 'value',
          name: 'NPSH [m]',
          nameLocation: 'middle',
          nameGap: 40,
          nameTextStyle: {
            fontSize: 12,
            color: GRUNDFOS_COLORS.npsh,
            fontWeight: 'bold',
            fontFamily: 'Arial, sans-serif'
          },
          min: 0,
          max: 20,
          interval: 5,
          position: 'right',
          axisLine: {
            show: true,
            lineStyle: {
              color: GRUNDFOS_COLORS.npsh,
              width: 2
            }
          },
          axisLabel: {
            color: GRUNDFOS_COLORS.npsh,
            fontSize: 11,
            fontFamily: 'Arial, sans-serif'
          },
          splitLine: {
            show: false
          },
          gridIndex: 1
        }
      ],

      series: []
    }

    // 生成系列数据
    const { series, legendData } = this.generateSeries(curves, config)
    option.series = series
    if (option.legend && !Array.isArray(option.legend)) {
      option.legend.data = legendData
    }

    // 添加最佳效率点标记
    if (bestEfficiencyPoint && series.length > 0) {
      const efficiencySeriesIndex = series.findIndex(s => s.name.includes('η'));
      if (efficiencySeriesIndex !== -1) {
        series[efficiencySeriesIndex].markPoint = {
          data: [
            {
              name: '系统最佳效率点',
              coord: [bestEfficiencyPoint.q, bestEfficiencyPoint.efficiency],
              symbol: 'circle',
              symbolSize: 12,
              itemStyle: {
                color: '#ff00ff' // 紫色标记
              },
              label: {
                show: true,
                position: 'top',
                formatter: `{b}: {c}%`,
                color: '#333'
              }
            }
          ]
        };
      }
    }

    return option
  }

  // 生成系列数据
  private static generateSeries(
    curves: Record<string, DataPoint[]>, 
    config: ChartConfig
  ): { series: any[], legendData: string[] } {
    const series: any[] = []
    const legendData: string[] = []

    // Q-H曲线 (始终显示) - 上图
    if (curves.QH) {
      const smoothedData = SplineInterpolation.interpolate(curves.QH, 200)
      legendData.push('H')
      series.push({
        name: 'H',
        type: 'line',
        xAxisIndex: 0,
        yAxisIndex: 0,
        data: smoothedData.map(p => [p.flowRate, p.value]),
        lineStyle: {
          color: GRUNDFOS_COLORS.head,
          width: 3,
          shadowColor: 'rgba(31, 119, 180, 0.3)',
          shadowBlur: 3,
          shadowOffsetY: 2
        },
        symbol: 'none',
        smooth: false,
        emphasis: {
          lineStyle: {
            width: 4
          }
        }
      })
    }

    // 效率曲线1 - 上图
    if (config.curveTypes.eta && curves.QETA1) {
      const smoothedData = SplineInterpolation.interpolate(curves.QETA1, 200)
      legendData.push('η1')
      series.push({
        name: 'η1',
        type: 'line',
        xAxisIndex: 0,
        yAxisIndex: 1,
        data: smoothedData.map(p => [p.flowRate, p.value]),
        lineStyle: {
          color: '#2ca02c',
          width: 2,
          shadowColor: 'rgba(44, 160, 44, 0.3)',
          shadowBlur: 2,
          shadowOffsetY: 1
        },
        symbol: 'none',
        smooth: false
      })
    }

    // 效率曲线2 - 上图
    if (config.curveTypes.eta && curves.QETA2) {
      const smoothedData = SplineInterpolation.interpolate(curves.QETA2, 200)
      legendData.push('η2')
      series.push({
        name: 'η2',
        type: 'line',
        xAxisIndex: 0,
        yAxisIndex: 1,
        data: smoothedData.map(p => [p.flowRate, p.value]),
        lineStyle: {
          color: '#1f77b4',
          width: 2,
          shadowColor: 'rgba(31, 119, 180, 0.3)',
          shadowBlur: 2,
          shadowOffsetY: 1
        },
        symbol: 'none',
        smooth: false
      })
    }

    // 兼容单效率曲线
    if (config.curveTypes.eta && curves.QETA && !curves.QETA1 && !curves.QETA2) {
      const smoothedData = SplineInterpolation.interpolate(curves.QETA, 200)
      legendData.push('η')
      series.push({
        name: 'η',
        type: 'line',
        xAxisIndex: 0,
        yAxisIndex: 1,
        data: smoothedData.map(p => [p.flowRate, p.value]),
        lineStyle: {
          color: GRUNDFOS_COLORS.efficiency,
          width: 2,
          shadowColor: 'rgba(44, 160, 44, 0.3)',
          shadowBlur: 2,
          shadowOffsetY: 1
        },
        symbol: 'none',
        smooth: false
      })
    }

    // 功率曲线P1 - 下图
    if (config.curveTypes.powerP1 && curves.QP1) {
      const smoothedData = SplineInterpolation.interpolate(curves.QP1, 200)
      legendData.push('P1')
      series.push({
        name: 'P1',
        type: 'line',
        xAxisIndex: 1,
        yAxisIndex: 2,
        data: smoothedData.map(p => [p.flowRate, p.value]),
        lineStyle: {
          color: GRUNDFOS_COLORS.power,
          width: 2,
          shadowColor: 'rgba(255, 127, 14, 0.3)',
          shadowBlur: 2,
          shadowOffsetY: 1
        },
        symbol: 'none',
        smooth: false
      })
    }

    // 功率曲线P2 - 下图
    if (config.curveTypes.powerP2 && curves.QP2) {
      const smoothedData = SplineInterpolation.interpolate(curves.QP2, 200)
      legendData.push('P2')
      series.push({
        name: 'P2',
        type: 'line',
        xAxisIndex: 1,
        yAxisIndex: 2,
        data: smoothedData.map(p => [p.flowRate, p.value]),
        lineStyle: {
          color: GRUNDFOS_COLORS.power,
          width: 2,
          type: 'dashed',
          dashArray: [5, 5],
          shadowColor: 'rgba(255, 127, 14, 0.3)',
          shadowBlur: 2,
          shadowOffsetY: 1
        },
        symbol: 'none',
        smooth: false
      })
    }

    // NPSH曲线 - 下图
    if (config.curveTypes.npsh && curves.QNPSH) {
      const smoothedData = SplineInterpolation.interpolate(curves.QNPSH, 200)
      legendData.push('NPSH')
      series.push({
        name: 'NPSH',
        type: 'line',
        xAxisIndex: 1,
        yAxisIndex: 3,
        data: smoothedData.map(p => [p.flowRate, p.value]),
        lineStyle: {
          color: GRUNDFOS_COLORS.npsh,
          width: 2,
          shadowColor: 'rgba(214, 39, 40, 0.3)',
          shadowBlur: 2,
          shadowOffsetY: 1
        },
        symbol: 'none',
        smooth: false
      })
    }

    return { series, legendData }
  }

  // 创建工具提示格式化器
  private static createTooltipFormatter() {
    return function(params: any) {
      if (!Array.isArray(params)) params = [params]
      
      let result = `<div style="font-size: 12px; line-height: 1.5;">
        <div style="font-weight: bold; margin-bottom: 5px;">
          Q = ${params[0].value[0].toFixed(1)} m³/h
        </div>`
      
      params.forEach((param: any) => {
        const unit = param.seriesName === 'H' ? 'm' : 
                    param.seriesName === 'η' ? '%' : 
                    param.seriesName.includes('P') ? 'kW' : 'm'
        
        result += `<div style="color: ${param.color};">
          <span style="display: inline-block; width: 10px; height: 10px; 
                       background-color: ${param.color}; margin-right: 5px;"></span>
          ${param.seriesName}: ${param.value[1].toFixed(1)} ${unit}
        </div>`
      })
      
      result += '</div>'
      return result
    }
  }
}

// 性能计算算法
export class PerformanceCalculator {
  
  // 计算最佳效率点
  static findBestEfficiencyPoint(etaCurve: DataPoint[]): DataPoint | null {
    if (!etaCurve || etaCurve.length === 0) return null
    
    return etaCurve.reduce((best, current) => 
      current.value > best.value ? current : best
    )
  }

  // 计算工作点性能
  static calculateOperatingPoint(
    curves: Record<string, DataPoint[]>, 
    targetFlow: number
  ): Record<string, number> {
    const result: Record<string, number> = {}
    
    Object.keys(curves).forEach(curveType => {
      const curve = curves[curveType]
      const interpolatedValue = this.interpolateValue(curve, targetFlow)
      if (interpolatedValue !== null) {
        result[curveType] = interpolatedValue
      }
    })
    
    return result
  }

  // 线性插值
  private static interpolateValue(curve: DataPoint[], targetFlow: number): number | null {
    if (!curve || curve.length < 2) return null
    
    // 找到目标流量的区间
    for (let i = 0; i < curve.length - 1; i++) {
      const p1 = curve[i]
      const p2 = curve[i + 1]
      
      if (targetFlow >= p1.flowRate && targetFlow <= p2.flowRate) {
        // 线性插值
        const ratio = (targetFlow - p1.flowRate) / (p2.flowRate - p1.flowRate)
        return p1.value + ratio * (p2.value - p1.value)
      }
    }
    
    return null
  }
}
