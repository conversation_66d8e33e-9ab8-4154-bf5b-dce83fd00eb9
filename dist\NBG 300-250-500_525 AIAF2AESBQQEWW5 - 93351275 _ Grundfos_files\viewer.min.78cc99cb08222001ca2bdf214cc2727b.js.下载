var S7dmUtils=S7dmUtils||{};
(function(b){b=b||{};b.Viewer=function(){};b.Viewer.RETRY_JS_MAX=50;b.Viewer.prototype.retryCount=0;b.Viewer.prototype.viewerList=null;b.Viewer.prototype.load=function(c,a){a:if(a&&0===a.lastIndexOf("/etc/dam/viewers",0)||a&&0===a.lastIndexOf("/libs/dam/viewers",0))var d=!0;else{try{var e=new URL(a);d="http:"===e.protocol||"https:"===e.protocol;break a}catch(g){console.error("Invalid viewer path ",a)}d=!1}if(d&&!this.isAllLoaded(c)){this.viewerList=c;for(var f in c)$("head").append('\x3cscript type\x3d"text/javascript" src\x3d"'+a+
c[f]+'"\x3e\x3c/script\x3e')}return this};b.Viewer.prototype.ready=function(c){if("undefined"==typeof s7viewers||null!=this.viewerList&&!this.isAllLoaded(this.viewerList)){var a=this;this.retryCount++;this.retryCount<b.Viewer.RETRY_JS_MAX?setTimeout(function(){a.ready(c)},100):c.fail.call()}else c.success.call()};b.Viewer.prototype.isAllLoaded=function(c){if("undefined"==typeof s7viewers)return!1;for(var a in c)if("Responsive"==a){if("undefined"==typeof s7responsiveImage)return!1}else if("undefined"==
typeof s7viewers[a])return!1;return!0}})(S7dmUtils);