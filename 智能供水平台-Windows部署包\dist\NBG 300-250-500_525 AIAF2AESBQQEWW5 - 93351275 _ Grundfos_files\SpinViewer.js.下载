/*!************************************************************************
*
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2013 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
if(typeof s7viewers=="undefined"){s7viewers={}}else{if(typeof s7viewers!="object"){throw new Error("Cannot initialize a root 's7viewers' package. s7viewers is not an object")}}if(!s7viewers.SpinViewer){(function(){var a;s7viewers.SpinViewer=function(b){this.sdkBasePath="../../s7viewersdk/2025.5/SpinViewer/";this.containerId=null;this.params={};this.handlers=[];this.onInitComplete=null;this.onInitFail=null;this.initializationComplete=false;this.initCalled=false;this.firstMediasetParsed=false;this.isDisposed=false;this.utilsScriptElm=null;this.fixinputmarker=null;this.sdkProvided=false;this.lockurldomains=true;this.defaultCSS="SpinViewer_light.css";if(typeof b=="object"){if(b.containerId){this.setContainerId(b.containerId)}if(b.params){for(var c in b.params){if(b.params.hasOwnProperty(c)&&b.params.propertyIsEnumerable(c)){this.setParam(c,b.params[c])}}}if(b.handlers){this.setHandlers(b.handlers)}if(b.localizedTexts){this.setLocalizedTexts(b.localizedTexts)}}};s7viewers.SpinViewer.cssClassName="s7spinviewer";s7viewers.SpinViewer.prototype.modifiers={};s7viewers.SpinViewer.prototype.setContainerId=function(b){if(this.isDisposed){return}this.containerId=b||null};s7viewers.SpinViewer.getCodeBase=function(){var h="";var c="";var f=null;if(document.scripts){f=document.scripts}else{f=document.getElementsByTagName("script")}for(var e=0;e<f.length;e++){var g=f[e].src;var b=/^\s*(http[s]?:\/\/[^\/]*)?(.*)(\/(js|js_orig)\/SpinViewer\.js)/.exec(g);if(b&&b.length==5){if(typeof b[1]!=="undefined"){h=b[1]}h+=b[2];c=g;break}}if((h!="")&&(h.lastIndexOf("/")!=h.length-1)){h+="/"}var d=/\/etc\/dam\/viewers\//;s7viewers.SpinViewer.codebase={contentUrl:h,isDAM:d.test(c)}};s7viewers.SpinViewer.getCodeBase();s7viewers.SpinViewer.prototype.getContentUrl=function(){return s7viewers.SpinViewer.codebase.contentUrl};s7viewers.SpinViewer.prototype.symbols={"Container.LABEL":"Spin viewer","PanRightButton.TOOLTIP":"Spin East","PanLeftButton.TOOLTIP":"Spin West"};s7viewers.SpinViewer.prototype.includeViewer=function(){a.Util.lib.include("s7sdk.set.MediaSet");a.Util.lib.include("s7sdk.set.SpinView");a.Util.lib.include("s7sdk.common.Button");a.Util.lib.include("s7sdk.common.Container");this.trackingManager=new a.TrackingManager();var f={en:this.symbols,defaultLocale:"en"};this.s7params=new a.ParameterManager(null,null,{asset:"MediaSet.asset"},this.getContentUrl()+this.defaultCSS,this.lockurldomains);var e="";if(this.s7params.params.config&&(typeof(this.s7params.params.config)=="string")){e=",";if(this.s7params.params.config.indexOf("/")>-1){e+=this.s7params.params.config.split("/")[1]}else{e+=this.s7params.params.config}}this.s7params.setViewer("503,2025.5.0"+e);this.s7params.setDefaultLocalizedTexts(f);for(var b in this.params){if(b!="localizedtexts"){this.s7params.push(b,this.params[b])}else{this.s7params.setLocalizedTexts(this.params[b])}}this.s7params.push("OOTBPresetCSSFileToClassMap",{html5_spinviewer_dark:"s7spinviewer_dark",html5_spinviewer_light:""});this.container=null;this.spinView=null;this.zoomInButton=null;this.zoomOutButton=null;this.zoomResetButton=null;this.closeButton=null;this.mediaSet=null;this.fullScreenButton=null;this.spinLeftButton=null;this.spinRightButton=null;this.isOrientationMarkerForcedChanged=false;this.visibilityManagerZoom=null;var c=this;function g(){c.s7params.params.aemmode=s7viewers.SpinViewer.codebase.isDAM?"1":"0";if(a.browser.device.name=="desktop"){c.s7params.push("SpinView.singleclick","zoomReset")}if(a.browser.device.name=="desktop"){c.s7params.push("SpinView.doubleclick","reset")}var j=c.getParam("fixinputmarker");if(j){c.fixinputmarker=(j=="s7touchinput"||j=="s7mouseinput")?c.fixinputmarker=j:null}var h=c.getURLParameter("fixinputmarker");if(h){c.fixinputmarker=(h=="s7touchinput"||h=="s7mouseinput")?c.fixinputmarker=h:null}if(c.fixinputmarker){if(c.fixinputmarker==="s7mouseinput"){c.addClass(c.containerId,"s7mouseinput")}else{if(c.fixinputmarker==="s7touchinput"){c.addClass(c.containerId,"s7touchinput")}}}else{if(a.browser.supportsTouch()){c.addClass(c.containerId,"s7touchinput")}else{c.addClass(c.containerId,"s7mouseinput")}}var i=c.s7params.get("presetClasses");if(i&&i.length>0){i.forEach(function(k){c.addClass(c.containerId,k)})}c.parseMods();c.container=new a.Container(c.containerId,c.s7params,c.containerId+"_container");if(c.container.isInLayout()){d()}else{c.container.addEventListener(a.event.ResizeEvent.ADDED_TO_LAYOUT,d,false)}}function d(){c.container.removeEventListener(a.event.ResizeEvent.ADDED_TO_LAYOUT,d,false);var s=document.getElementById(c.containerId);var l=s.style.minHeight;s.style.minHeight="1px";var t=document.createElement("div");t.style.position="relative";t.style.width="100%";t.style.height="100%";s.appendChild(t);var k=t.offsetHeight;if(t.offsetHeight<=1){s.style.height="100%";k=t.offsetHeight}s.removeChild(t);s.style.minHeight=l;var m=false;switch(c.s7params.get("responsive","auto")){case"fit":m=false;break;case"constrain":m=true;break;default:m=k==0;break}c.updateCSSMarkers();c.updateOrientationMarkers();if(c.container.isFixedSize()){c.viewerMode="fixed"}else{if(m){c.viewerMode="ratio"}else{c.viewerMode="free"}}c.trackingManager.setCallback(q);if((typeof(AppMeasurementBridge)=="function")&&(c.isConfig2Exist==true)){c.appMeasurementBridge=new AppMeasurementBridge(c.trackingParams)}c.mediaSet=new a.MediaSet(null,c.s7params,c.containerId+"_mediaSet");c.trackingManager.attach(c.mediaSet);c.mediaSet.addEventListener(a.AssetEvent.NOTF_SET_PARSED,u,false);c.container.addEventListener(a.event.ResizeEvent.COMPONENT_RESIZE,p,false);c.container.addEventListener(a.event.ResizeEvent.FULLSCREEN_RESIZE,o,false);c.container.addEventListener(a.event.ResizeEvent.SIZE_MARKER_CHANGE,h,false);c.spinView=new a.SpinView(c.container,c.s7params,c.containerId+"_spinView");c.trackingManager.attach(c.spinView);c.toolsButtons=document.createElement("div");c.toolsButtons.setAttribute("id",c.containerId+"_divToolsButtons");var j=document.getElementById(c.container.getInnerContainerId());j.appendChild(c.toolsButtons);if(((c.s7params.get("closeButton","0")=="1")||(c.s7params.get("closeButton","0").toLowerCase()=="true"))){c.closeButton=new a.common.CloseButton(c.containerId+"_divToolsButtons",c.s7params,c.containerId+"_closeButton");c.closeButton.addEventListener("click",n)}c.zoomInButton=new a.ZoomInButton(c.containerId+"_divToolsButtons",c.s7params,c.containerId+"_zoomInButton");c.zoomOutButton=new a.ZoomOutButton(c.containerId+"_divToolsButtons",c.s7params,c.containerId+"_zoomOutButton");c.zoomResetButton=new a.ZoomResetButton(c.containerId+"_divToolsButtons",c.s7params,c.containerId+"_zoomResetButton");c.zoomInButton.deactivate();c.zoomOutButton.deactivate();c.zoomResetButton.deactivate();if(a.browser.device.name=="desktop"){c.divSpinButtons=document.createElement("div");c.divSpinButtons.setAttribute("id",c.containerId+"_divSpinButtons");c.divSpinButtons.className="s7spinbuttons";j.appendChild(c.divSpinButtons);c.spinLeftButton=new a.PanLeftButton(c.containerId+"_divSpinButtons",c.s7params,c.containerId+"_spinLeftButton");c.spinRightButton=new a.PanRightButton(c.containerId+"_divSpinButtons",c.s7params,c.containerId+"_spinRightButton");c.spinLeftButton.addEventListener("click",function(){c.spinView.moveFrame(a.Enum.SPIN_DIRECTION.WEST)},false);c.spinRightButton.addEventListener("click",function(){c.spinView.moveFrame(a.Enum.SPIN_DIRECTION.EAST)},false)}c.fullScreenButton=new a.common.FullScreenButton(c.container,c.s7params,c.containerId+"_fullScreenButton");if(c.container.isPopup()&&!c.container.isFixedSize()&&!c.container.supportsNativeFullScreen()){c.fullScreenButton.setCSS(".s7fullscreenbutton","display","none")}if(c.viewerMode=="ratio"){s.style.height="auto"}r(c.container.getWidth(),c.container.getHeight());if(a.browser.supportsTouch()){c.visibilityManagerZoom=new a.VisibilityManager();c.visibilityManagerZoom.reference(c.spinView);c.visibilityManagerZoom.attach(c.closeButton);c.visibilityManagerZoom.attach(c.zoomInButton);c.visibilityManagerZoom.attach(c.zoomOutButton);c.visibilityManagerZoom.attach(c.zoomResetButton);if(!c.notCustomSize||c.container.supportsNativeFullScreen()){c.visibilityManagerZoom.attach(c.fullScreenButton)}}c.zoomInButton.addEventListener("click",function(){c.spinView.zoomIn()});c.zoomOutButton.addEventListener("click",function(){c.spinView.zoomOut()});c.fullScreenButton.addEventListener("click",i);c.spinView.addEventListener(a.event.CapabilityStateEvent.NOTF_SPIN_CAPABILITY_STATE,function(v){if(v.s7event.state.hasCapability(a.SpinCapabilityState.ZOOM_IN)){c.zoomInButton.activate()}else{c.zoomInButton.deactivate()}if(v.s7event.state.hasCapability(a.SpinCapabilityState.ZOOM_OUT)){c.zoomOutButton.activate()}else{c.zoomOutButton.deactivate()}if(v.s7event.state.hasCapability(a.SpinCapabilityState.ZOOM_RESET)){c.zoomResetButton.activate()}else{c.zoomResetButton.deactivate()}});c.zoomResetButton.addEventListener("click",function(){c.spinView.zoomReset()});function u(x){var v=x.s7event.asset;c.spinView.setMediaSet(v);if(a.browser.device.name=="desktop"){if(v.items[0] instanceof a.MediaSetDesc){c.spinLeftButton.setCSS(".s7panleftbutton","visibility","hidden");c.spinRightButton.setCSS(".s7panrightbutton","visibility","hidden")}else{c.spinLeftButton.setCSS(".s7panleftbutton","visibility","inherit");c.spinRightButton.setCSS(".s7panrightbutton","visibility","inherit")}if(v.items.length==1){c.spinLeftButton.deactivate();c.spinRightButton.deactivate()}else{c.spinLeftButton.activate();c.spinRightButton.activate()}}if(c.viewerMode=="ratio"){var y=v.items[0];y=y instanceof a.MediaSetDesc?y.items[0]:y;var w=y.width/y.height;c.container.setModifier({aspect:w})}r(c.container.getWidth(),c.container.getHeight());if((c.onInitComplete!=null)&&(typeof c.onInitComplete=="function")){c.onInitComplete()}if((c.handlers.initComplete!=null)&&(typeof c.handlers.initComplete=="function")&&!c.firstMediasetParsed){if(typeof window.s7sdk=="undefined"){window.s7sdk=a}c.handlers.initComplete()}c.firstMediasetParsed=true}function q(x,w,A,v,y){if(!c.handlers.trackEvent&&c.isConfig2Exist!=true&&a.Modifier.parse(c.s7params.get("launch","true"),[true]).values[0]){if(typeof(_satellite)!="undefined"&&_satellite._dmviewers_v001){c.handlers.trackEvent=_satellite._dmviewers_v001().trackingFn}}if(c.appMeasurementBridge){c.appMeasurementBridge.track(x,w,A,v,y)}if(c.handlers.trackEvent){if(typeof window.s7sdk=="undefined"){window.s7sdk=a}var z=c.containerId;c.handlers.trackEvent(z,w,A,v,y)}if("s7ComponentEvent" in window){s7ComponentEvent(x,w,A,v,y)}}function i(){if(!c.container.isFullScreen()){if(c.closeButton){c.closeButton.setCSS(".s7closebutton","display","none")}c.container.requestFullScreen()}else{if(c.closeButton){c.closeButton.setCSS(".s7closebutton","display","block")}c.container.cancelFullScreen()}}function p(v){if((typeof(v.target)=="undefined")||(v.target==document.getElementById(c.containerId+"_container"))){if(!c.container.isInLayout()){return}if(c.closeButton){if(c.container.isFullScreen()){c.closeButton.setCSS(".s7closebutton","display","none")}else{c.closeButton.setCSS(".s7closebutton","display","block")}}r(v.s7event.w,v.s7event.h)}}function o(v){if(c.closeButton){if(c.container.isFullScreen()){c.closeButton.setCSS(".s7closebutton","display","none")}else{c.closeButton.setCSS(".s7closebutton","display","block")}}r(v.s7event.w,v.s7event.h);c.fullScreenButton.setSelected(c.container.isFullScreen())}function h(v){c.updateCSSMarkers()}function r(v,x){c.updateOrientationMarkers();c.spinView.resize(v,x)}function n(){try{if(a.browser.name!="firefox"&&a.browser.name!="safari"){window.open(c.getContentUrl()+"s7sdkclose.html","_self")}else{if(a.browser.name=="safari"){window.focus();window.close()}else{window.close()}}}catch(v){a.Logger.log(a.Logger.WARN,"Cannot close the window")}}}this.s7params.addEventListener(a.Event.SDK_READY,function(){c.initSiteCatalyst(c.s7params,g)},false);this.s7params.setProvidedSdk(this.sdkProvided);this.s7params.init()};s7viewers.SpinViewer.prototype.setParam=function(b,c){if(this.isDisposed){return}this.params[b]=c};s7viewers.SpinViewer.prototype.getParam=function(c){var d=c.toLowerCase();for(var b in this.params){if(b.toLowerCase()==d){return this.params[b]}}return null};s7viewers.SpinViewer.prototype.setParams=function(b){if(this.isDisposed){return}var e=b.split("&");for(var c=0;c<e.length;c++){var d=e[c].split("=");if(d.length>1){this.setParam(d[0],decodeURIComponent(e[c].split("=")[1]))}}};s7viewers.SpinViewer.prototype.s7sdkUtilsAvailable=function(){if(s7viewers.SpinViewer.codebase.isDAM){return typeof(s7viewers.s7sdk)!="undefined"}else{return(typeof(s7classic)!="undefined")&&(typeof(s7classic.s7sdk)!="undefined")}};s7viewers.SpinViewer.prototype.init=function(){if(this.isDisposed){return}if(this.initCalled){return}this.initCalled=true;if(this.initializationComplete){return this}this.lockurldomains=(Boolean(Number(this.params.lockurldomains))||typeof this.params.lockurldomains=="undefined")?1:0;var i=document.getElementById(this.containerId);if(i.className!=""){if(i.className.indexOf(s7viewers.SpinViewer.cssClassName)!=-1){}else{i.className+=" "+s7viewers.SpinViewer.cssClassName}}else{i.className=s7viewers.SpinViewer.cssClassName}this.s7sdkNamespace=s7viewers.SpinViewer.codebase.isDAM?"s7viewers":"s7classic";var d=this.getContentUrl()+this.sdkBasePath+"js/s7sdk/utils/Utils.js?namespace="+this.s7sdkNamespace;var f=null;if(document.scripts){f=document.scripts}else{f=document.getElementsByTagName("script")}if(this.s7sdkUtilsAvailable()){a=(s7viewers.SpinViewer.codebase.isDAM?s7viewers.s7sdk:s7classic.s7sdk);this.sdkProvided=true;if(this.isDisposed){return}a.Util.init();this.includeViewer();this.initializationComplete=true}else{if(!this.s7sdkUtilsAvailable()&&(s7viewers.SpinViewer.codebase.isDAM?s7viewers.S7SDK_S7VIEWERS_LOAD_STARTED:s7viewers.S7SDK_S7CLASSIC_LOAD_STARTED)){this.sdkProvided=true;var h=this;var g=setInterval(function(){if(h.s7sdkUtilsAvailable()){clearInterval(g);a=(s7viewers.SpinViewer.codebase.isDAM?s7viewers.s7sdk:s7classic.s7sdk);if(h.isDisposed){return}a.Util.init();h.includeViewer();h.initializationComplete=true}},100)}else{this.utilsScriptElm=document.createElement("script");this.utilsScriptElm.setAttribute("language","javascript");this.utilsScriptElm.setAttribute("type","text/javascript");var e=document.getElementsByTagName("head")[0];var c=this;function b(){if(!c.utilsScriptElm.executed){c.utilsScriptElm.executed=true;a=(s7viewers.SpinViewer.codebase.isDAM?s7viewers.s7sdk:s7classic.s7sdk);if(c.s7sdkUtilsAvailable()&&a.Util){if(c.isDisposed){return}a.Util.init();c.includeViewer();c.initializationComplete=true;c.utilsScriptElm.onreadystatechange=null;c.utilsScriptElm.onload=null;c.utilsScriptElm.onerror=null}}}if(typeof(c.utilsScriptElm.readyState)!="undefined"){c.utilsScriptElm.onreadystatechange=function(){if(c.utilsScriptElm.readyState=="loaded"){e.appendChild(c.utilsScriptElm)}else{if(c.utilsScriptElm.readyState=="complete"){b()}}};c.utilsScriptElm.setAttribute("src",d)}else{c.utilsScriptElm.onload=function(){b()};c.utilsScriptElm.onerror=function(){};c.utilsScriptElm.setAttribute("src",d);e.appendChild(c.utilsScriptElm);c.utilsScriptElm.setAttribute("data-src",c.utilsScriptElm.getAttribute("src"));c.utilsScriptElm.setAttribute("src","?namespace="+this.s7sdkNamespace)}if(s7viewers.SpinViewer.codebase.isDAM){s7viewers.S7SDK_S7VIEWERS_LOAD_STARTED=true}else{s7viewers.S7SDK_S7CLASSIC_LOAD_STARTED=true}}}return this};s7viewers.SpinViewer.prototype.getDomain=function(b){var c=/(^http[s]?:\/\/[^\/]+)/i.exec(b);if(c==null){return""}else{return c[1]}};s7viewers.SpinViewer.prototype.setAsset=function(b){if(this.isDisposed){return}if(this.mediaSet){this.mediaSet.setAsset(b)}else{this.setParam("asset",b)}};s7viewers.SpinViewer.prototype.setLocalizedTexts=function(b){if(this.isDisposed){return}if(this.s7params){this.s7params.setLocalizedTexts(b)}else{this.setParam("localizedtexts",b)}};s7viewers.SpinViewer.prototype.initSiteCatalyst=function(i,c){var f=i.get("asset",null,"MediaSet").split(",")[0].split(":")[0];this.isConfig2Exist=false;if(f.indexOf("/")!=-1){var d=a.MediaSetParser.findCompanyNameInAsset(f);var h=i.get("config2");this.isConfig2Exist=(h!=""&&typeof h!="undefined");if(this.isConfig2Exist){this.trackingParams={siteCatalystCompany:d,config2:h,isRoot:i.get("serverurl")};var b=this.getContentUrl()+"../AppMeasurementBridge.jsp?company="+d+(h==""?"":"&preset="+h);if(i.get("serverurl",null)){b+="&isRoot="+i.get("serverurl")}var g=document.createElement("script");g.setAttribute("language","javascript");g.setAttribute("type","text/javascript");g.setAttribute("src",b);var e=document.getElementsByTagName("head");g.onload=g.onerror=function(){if(!g.executed){g.executed=true;if(typeof c=="function"){c()}g.onreadystatechange=null;g.onload=null;g.onerror=null}};g.onreadystatechange=function(){if(g.readyState=="complete"||g.readyState=="loaded"){setTimeout(function(){if(!g.executed){g.executed=true;if(typeof c=="function"){c()}}g.onreadystatechange=null;g.onload=null;g.onerror=null},0)}};e[0].appendChild(g)}else{if(typeof c=="function"){c()}}}};s7viewers.SpinViewer.prototype.getComponent=function(b){if(this.isDisposed){return null}switch(b){case"container":return this.container||null;case"spinView":return this.spinView||null;case"mediaSet":return this.mediaSet||null;case"zoomInButton":return this.zoomInButton||null;case"zoomOutButton":return this.zoomOutButton||null;case"zoomResetButton":return this.zoomResetButton||null;case"closeButton":return this.closeButton||null;case"spinLeftButton":return this.spinLeftButton||null;case"spinRightButton":return this.spinRightButton||null;case"parameterManager":return this.s7params||null;default:return null}};s7viewers.SpinViewer.prototype.setHandlers=function(c){if(this.isDisposed){return}if(this.initCalled){return}this.handlers=[];for(var b in c){if(!c.hasOwnProperty(b)){continue}if(typeof c[b]!="function"){continue}this.handlers[b]=c[b]}};s7viewers.SpinViewer.prototype.getModifiers=function(){return this.modifiers};s7viewers.SpinViewer.prototype.setModifier=function(f){if(this.isDisposed){return}var h,c,j,b,g,e;for(h in f){if(!this.modifiers.hasOwnProperty(h)){continue}c=this.modifiers[h];try{b=f[h];if(c.parseParams===false){g=new a.Modifier([b!=""?b:c.defaults[0]])}else{g=a.Modifier.parse(b,c.defaults,c.ranges)}if(g.values.length==1){this[h]=g.values[0];this.setModifierInternal(h)}else{if(g.values.length>1){j={};for(e=0;e<g.values.length;e++){j[c.params[e]]=g.values[e]}this[h]=j;this.setModifierInternal(h)}}}catch(d){throw new Error("Unable to process modifier: '"+h+"'. "+d)}}};s7viewers.SpinViewer.prototype.setModifierInternal=function(b){switch(b){default:break}};s7viewers.SpinViewer.prototype.parseMods=function(){var g,c,h,b,f,e;for(g in this.modifiers){if(!this.modifiers.hasOwnProperty(g)){continue}c=this.modifiers[g];try{b=this.s7params.get(g,"");if(c.parseParams===false){f=new a.Modifier([b!=""?b:c.defaults[0]])}else{f=a.Modifier.parse(b,c.defaults,c.ranges)}if(f.values.length==1){this[g]=f.values[0]}else{if(f.values.length>1){h={};for(e=0;e<f.values.length;e++){h[c.params[e]]=f.values[e]}this[g]=h}}}catch(d){throw new Error("Unable to process modifier: '"+g+"'. "+d)}}};s7viewers.SpinViewer.prototype.updateCSSMarkers=function(){var c=this.container.getSizeMarker();var b;if(c==a.common.Container.SIZE_MARKER_NONE){return}if(c==a.common.Container.SIZE_MARKER_LARGE){b="s7size_large"}else{if(c==a.common.Container.SIZE_MARKER_SMALL){b="s7size_small"}else{if(c==a.common.Container.SIZE_MARKER_MEDIUM){b="s7size_medium"}}}if(this.containerId){this.setNewSizeMarker(this.containerId,b)}this.reloadInnerComponents()};s7viewers.SpinViewer.prototype.reloadInnerComponents=function(){var c=this.s7params.getRegisteredComponents();for(var b=0;b<c.length;b++){if(c[b]&&c[b].restrictedStylesInvalidated()){c[b].reload()}}};s7viewers.SpinViewer.prototype.setNewSizeMarker=function(f,c){var b=document.getElementById(f).className;var d=/^(.*)(s7size_small|s7size_medium|s7size_large)(.*)$/gi;var e;if(b.match(d)){e=b.replace(d,"$1"+c+"$3")}else{e=b+" "+c}if(b!=e){document.getElementById(f).className=e}};s7viewers.SpinViewer.prototype.dispose=function(){if(this.appMeasurementBridge){this.appMeasurementBridge.dispose();this.appMeasurementBridge=null}if(this.trackingManager){this.trackingManager.dispose();this.trackingManager=null}if(this.spinView){this.spinView.dispose();this.spinView=null}if(this.spinRightButton){this.spinRightButton.dispose();this.spinRightButton=null}if(this.spinLeftButton){this.spinLeftButton.dispose();this.spinLeftButton=null}if(this.zoomInButton){this.zoomInButton.dispose();this.zoomInButton=null}if(this.zoomOutButton){this.zoomOutButton.dispose();this.zoomOutButton=null}if(this.zoomResetButton){this.zoomResetButton.dispose();this.zoomResetButton=null}if(this.fullScreenButton){this.fullScreenButton.dispose();this.fullScreenButton=null}if(this.closeButton){this.closeButton.dispose();this.closeButton=null}if(this.mediaSet){this.mediaSet.dispose();this.mediaSet=null}if(this.s7params){this.s7params.dispose();this.s7params=null}if(this.toolsButtons){this.toolsButtons.parentNode.removeChild(this.toolsButtons);delete this.toolsButtons}if(this.divSpinButtons){this.divSpinButtons.parentNode.removeChild(this.divSpinButtons);delete this.divSpinButtons}if(this.container){var e=[s7viewers.SpinViewer.cssClassName,"s7touchinput","s7mouseinput","s7size_large","s7size_small","s7size_medium"];var c=document.getElementById(this.containerId).className.split(" ");for(var d=0;d<e.length;d++){var b=c.indexOf(e[d]);if(b!=-1){c.splice(b,1)}}document.getElementById(this.containerId).className=c.join(" ");this.container.dispose();this.container=null}this.handlers=[];this.isDisposed=true};s7viewers.SpinViewer.prototype.updateOrientationMarkers=function(){if(!this.isOrientationMarkerForcedChanged){var b;if(window.innerWidth>window.innerHeight){b="s7device_landscape"}else{b="s7device_portrait"}if(document.getElementById(this.containerId).className.indexOf(b)==-1){this.setNewOrientationMarker(this.containerId,b);this.reloadInnerComponents()}}};s7viewers.SpinViewer.prototype.setNewOrientationMarker=function(f,c){var b=document.getElementById(f).className;var d=/^(.*)(s7device_landscape|s7device_portrait)(.*)$/gi;var e;if(b.match(d)){e=b.replace(d,"$1"+c+"$3")}else{e=b+" "+c}if(b!=e){document.getElementById(f).className=e}};s7viewers.SpinViewer.prototype.forceDeviceOrientationMarker=function(b){switch(b){case"s7device_portrait":case"s7device_landscape":this.isOrientationMarkerForcedChanged=true;if(this.containerId){this.setNewOrientationMarker(this.containerId,b)}this.reloadInnerComponents();break;case null:this.isOrientationMarkerForcedChanged=false;this.updateOrientationMarkers();break;default:break}};s7viewers.SpinViewer.prototype.getURLParameter=function(c){var b=a.ParameterManager.getSanitizedParameters(a.query.params,this.lockurldomains);return b[c]};s7viewers.SpinViewer.prototype.addClass=function(d,c){var b=document.getElementById(d).className.split(" ");if(b.indexOf(c)==-1){b[b.length]=c;document.getElementById(d).className=b.join(" ")}}})()};