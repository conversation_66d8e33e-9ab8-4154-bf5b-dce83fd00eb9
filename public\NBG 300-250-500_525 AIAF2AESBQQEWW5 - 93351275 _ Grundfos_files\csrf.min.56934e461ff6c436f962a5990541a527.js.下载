(function(d){d.Granite=d.Granite||{};d.Granite.HTTP=d.Granite.HTTP||{};var f=null;d.Granite.HTTP.externalize=d.Granite.HTTP.externalize||function(g){if(null===f)a:{var n=/^(?:http|https):\/\/[^/]+(\/.*)\/(?:etc\.clientlibs|etc(\/.*)*\/clientlibs|libs(\/.*)*\/clientlibs|apps(\/.*)*\/clientlibs|etc\/designs).*\.js(\?.*)?$/;try{if(d.CQURLInfo)f=CQURLInfo.contextPath||"";else{for(var h=document.getElementsByTagName("script"),k=0;k<h.length;k++){var l=n.exec(h[k].src);if(l){f=l[1];break a}}f=""}}catch(p){}}try{0===
g.indexOf("/")&&f&&0!==g.indexOf(f+"/")&&(g=f+g)}catch(p){}return g}})(this);
(function(d){window.Granite.csrf||(window.Granite.csrf=d(window.Granite.HTTP))})(function(d){function f(){this._handler=[]}function g(a){var b="//"+document.location.host,c=document.location.protocol+b;return a===c||a.slice(0,c.length+1)===c+"/"||a===b||a.slice(0,b.length+1)===b+"/"||!/^(\/\/|http:|https:).*/.test(a)}function n(a){window.console&&console.warn("CSRF data not available;The data may be unavailable by design, such as during non-authenticated requests: "+a)}function h(){var a=new f;q=
a;var b=new XMLHttpRequest;b.onreadystatechange=function(){if(4===b.readyState)try{e=JSON.parse(b.responseText).token,a.resolve(e)}catch(c){n(c),a.reject(b.responseText)}};b.open("GET",r,!0);b.send();return a}function k(){var a=new XMLHttpRequest;a.open("GET",r,!1);a.send();try{return e=JSON.parse(a.responseText).token}catch(b){n(b)}}function l(a){var b=a.getAttribute("action");"GET"===a.method.toUpperCase()||b&&!g(b)||(e||k(),e&&(b=a.querySelector('input[name\x3d":cq_csrf_token"]'),b||(b=document.createElement("input"),
b.setAttribute("type","hidden"),b.setAttribute("name",":cq_csrf_token"),a.appendChild(b)),b.setAttribute("value",e)))}function p(a){var b=function(c){c=c.target;"FORM"===c.nodeName&&l(c)};a.addEventListener?a.addEventListener("submit",b,!0):a.attachEvent&&a.attachEvent("submit",b)}f.prototype={then:function(a,b){this._handler.push({resolve:a,reject:b})},resolve:function(){this._execute("resolve",arguments)},reject:function(){this._execute("reject",arguments)},_execute:function(a,b){if(null===this._handler)throw Error("Promise already completed.");
for(var c=0,t=this._handler.length;c<t;c++)this._handler[c][a].apply(window,b);this.then=function(u,v){("resolve"===a?u:v).apply(window,b)};this._handler=null}};var r=d.externalize("/libs/granite/csrf/token.json"),q,e;p(document);var w=XMLHttpRequest.prototype.open;XMLHttpRequest.prototype.open=function(a,b,c){"get"!==a.toLowerCase()&&g(b)&&(this._csrf=!0,this._async=c);return w.apply(this,arguments)};var m=XMLHttpRequest.prototype.send;XMLHttpRequest.prototype.send=function(){if(this._csrf)if(e)this.setRequestHeader("CSRF-Token",
e),m.apply(this,arguments);else if(!1===this._async)k(),e&&this.setRequestHeader("CSRF-Token",e),m.apply(this,arguments);else{var a=this,b=Array.prototype.slice.call(arguments);q.then(function(c){a.setRequestHeader("CSRF-Token",c);m.apply(a,b)},function(){m.apply(a,b)})}else m.apply(this,arguments)};var x=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){l(this);return x.apply(this,arguments)};if(window.Node){var y=Node.prototype.appendChild;Node.prototype.appendChild=function(){var a=
y.apply(this,arguments);if("IFRAME"===a.nodeName)try{a.contentWindow&&!a._csrf&&(a._csrf=!0,p(a.contentWindow.document))}catch(b){a.src&&a.src.length&&g(a.src)&&window.console&&console.error("Unable to attach CSRF token to an iframe element on the same origin")}return a}}h();setInterval(function(){h()},3E5);return{initialised:!1,refreshToken:h,_clearToken:function(){e=void 0;h()}}});