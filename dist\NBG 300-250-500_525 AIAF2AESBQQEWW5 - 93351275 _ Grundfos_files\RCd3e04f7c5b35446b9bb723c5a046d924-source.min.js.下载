// For license information, see `https://assets.adobedtm.com/5e107551ef5d/89d0c5ed902a/4021f82e766f/RCd3e04f7c5b35446b9bb723c5a046d924-source.js`.
_satellite.__registerScript('https://assets.adobedtm.com/5e107551ef5d/89d0c5ed902a/4021f82e766f/RCd3e04f7c5b35446b9bb723c5a046d924-source.min.js', "function formPrefillTracking(){s.helper.trackEvent(\"event2\");var e=document.querySelector('form[id*=\"mktoForm\"]');if(e){var r=e.querySelectorAll(\".mktoField\"),t=e.querySelectorAll('.mktoField:not([type=\"hidden\"])').length,l=[];Array.prototype.forEach.call(r,(function(e){e.value&&\"hidden\"!==e.type&&\"gfSubscriptionFlowTrigger\"!=e.name&&\"radio\"!=e.type&&\"gfmarketingpermission\"!=e.name&&-1===e.name.indexOf(\"Undefined\")&&l.push(e.name)}));var i=l.length;(l.join(\",\")||0)&&(s.helper.trackVariable(\"eVar199\"),s.eVar199=l.join(\",\"),s.helper.trackVariable(\"eVar13\"),s.eVar13=i+\"|\"+t,s.helper.trackEvent(\"event1\"),_satellite.logger.log(\"Prefilled Fields from Marketo : \"+l.join(\",\")))}s.tl(this,\"o\",\"form viewed & prefill tracking\")}\"viewed\"==_satellite.getVar(\"form.event\")&&setTimeout(formPrefillTracking,2e3);");