import mqtt from 'mqtt';
import type { MqttClient, IClientOptions } from 'mqtt';
import { ref, reactive } from 'vue';
import type { MQTTConfig, InverterData, GatewayError } from '../types/gateway';

export class MqttService {
  private client: MqttClient | null = null;
  private config: MQTTConfig | null = null;

  // 响应式状态
  public connected = ref(false);
  public connecting = ref(false);
  public connectionError = ref<Error | null>(null);
  public lastMessage = ref<any>(null);
  public messages = reactive<Record<string, any>>({});
  public errors = reactive<GatewayError[]>([]);
  
  // 设备数据收集
  public deviceData = reactive<Record<string, InverterData>>({});

  // 连接到MQTT代理
  public connect(config: MQTTConfig): Promise<boolean> {
    this.config = config;
    this.connecting.value = true;
    this.connectionError.value = null;
    
    const options: IClientOptions = {
      clientId: config.clientId,
      keepalive: config.keepAlive,
      reconnectPeriod: config.reconnectPeriod,
      clean: true
    };
    
    if (config.username) {
      options.username = config.username;
      options.password = config.password;
    }
    
    return new Promise((resolve, reject) => {
      try {
        this.client = mqtt.connect(config.brokerUrl, options);
        
        this.client.on('connect', () => {
          console.log('MQTT连接成功');
          this.connected.value = true;
          this.connecting.value = false;
          
          // 连接成功后自动订阅配置中的主题
          if (config.subscribeTopic) {
            this.subscribe(config.subscribeTopic);
          }
          
          resolve(true);
        });
        
        this.client.on('error', (err) => {
          console.error('MQTT连接错误:', err);
          this.connectionError.value = err;
          this.connecting.value = false;
          reject(err);
        });
        
        this.client.on('message', (topic, message) => {
          try {
            const payload = JSON.parse(message.toString());
            this.lastMessage.value = payload;
            this.messages[topic] = payload;
            
            // 处理设备数据
            this.handleDeviceData(topic, payload);
          } catch (err) {
            console.error('MQTT消息解析错误:', err);
            this.recordError({
              id: Date.now().toString(),
              deviceId: this.extractDeviceId(topic),
              timestamp: Date.now(),
              errorCode: 1001,
              errorMessage: '消息解析错误: ' + (err as Error).message,
              severity: 'warning',
              resolved: false
            });
          }
        });
        
        this.client.on('disconnect', () => {
          console.log('MQTT断开连接');
          this.connected.value = false;
        });
        
        this.client.on('offline', () => {
          console.log('MQTT客户端离线');
          this.connected.value = false;
        });
        
        this.client.on('reconnect', () => {
          console.log('MQTT尝试重新连接');
          this.connecting.value = true;
        });
      } catch (err) {
        console.error('MQTT创建客户端错误:', err);
        this.connectionError.value = err as Error;
        this.connecting.value = false;
        reject(err);
      }
    });
  }
  
  // 断开MQTT连接
  public disconnect(): Promise<void> {
    return new Promise((resolve) => {
      if (!this.client) {
        resolve();
        return;
      }
      
      this.client.end(false, {}, () => {
        this.client = null;
        this.connected.value = false;
        resolve();
      });
    });
  }
  
  // 订阅主题
  public subscribe(topic: string, qos: 0 | 1 | 2 = 1): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.client || !this.connected.value) {
        reject(new Error('MQTT客户端未连接'));
        return;
      }
      
      this.client.subscribe(topic, { qos }, (err) => {
        if (err) {
          console.error(`订阅主题 ${topic} 失败:`, err);
          reject(err);
          return;
        }
        
        console.log(`成功订阅主题: ${topic}`);
        resolve();
      });
    });
  }
  
  // 发布消息
  public publish(topic: string, message: any, qos: 0 | 1 | 2 = 1, retain: boolean = false): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.client || !this.connected.value) {
        reject(new Error('MQTT客户端未连接'));
        return;
      }
      
      let payload: string;
      
      if (typeof message === 'object') {
        payload = JSON.stringify(message);
      } else {
        payload = String(message);
      }
      
      this.client.publish(topic, payload, { qos, retain }, (err) => {
        if (err) {
          console.error(`发布消息到主题 ${topic} 失败:`, err);
          reject(err);
          return;
        }
        
        console.log(`成功发布消息到主题: ${topic}`);
        resolve();
      });
    });
  }
  
  // 处理设备数据
  private handleDeviceData(topic: string, payload: any): void {
    try {
      if (!payload || typeof payload !== 'object') return;
      
      const deviceId = this.extractDeviceId(topic);
      if (!deviceId) return;
      
      // 尝试将数据转换为 InverterData 格式
      if (this.isInverterData(payload)) {
        const inverterData: InverterData = {
          deviceId,
          timestamp: payload.timestamp || Date.now(),
          frequency: parseFloat(payload.frequency || 0),
          current: parseFloat(payload.current || 0),
          voltage: parseFloat(payload.voltage || 0),
          power: parseFloat(payload.power || 0),
          temperature: parseFloat(payload.temperature || 0),
          runningStatus: Boolean(payload.runningStatus),
          errorCode: payload.errorCode,
          errorMessage: payload.errorMessage,
          rotationSpeed: payload.rotationSpeed,
          flowRate: payload.flowRate,
          pressure: payload.pressure,
          torque: payload.torque
        };
        
        // 更新设备数据
        this.deviceData[deviceId] = inverterData;
        
        // 检查错误
        if (inverterData.errorCode && inverterData.errorMessage) {
          this.recordError({
            id: `${deviceId}-${Date.now()}`,
            deviceId: deviceId,
            timestamp: Date.now(),
            errorCode: inverterData.errorCode,
            errorMessage: inverterData.errorMessage,
            severity: this.determineSeverity(inverterData.errorCode),
            resolved: false
          });
        }
      }
    } catch (err) {
      console.error('处理设备数据错误:', err);
    }
  }
  
  // 从主题中提取设备ID
  private extractDeviceId(topic: string): string {
    if (!topic || !this.config?.topicPrefix) return '';
    
    const parts = topic.split('/');
    // 假设主题格式为 topicPrefix/deviceId/data
    const prefixParts = this.config.topicPrefix.split('/');
    const prefixLength = prefixParts.filter(p => p.length > 0).length;
    
    if (parts.length > prefixLength) {
      return parts[prefixLength];
    }
    
    return '';
  }
  
  // 判断数据是否符合变频器数据格式
  private isInverterData(data: any): boolean {
    return typeof data === 'object' && 
          (data.frequency !== undefined || 
           data.current !== undefined || 
           data.voltage !== undefined || 
           data.power !== undefined);
  }
  
  // 记录错误
  private recordError(error: GatewayError): void {
    // 检查是否已存在相同错误
    const existingIndex = this.errors.findIndex(e => 
      e.deviceId === error.deviceId && 
      e.errorCode === error.errorCode && 
      !e.resolved
    );
    
    if (existingIndex >= 0) {
      // 更新现有错误
      this.errors[existingIndex].timestamp = error.timestamp;
    } else {
      // 添加新错误
      this.errors.push(error);
    }
  }
  
  // 确定错误严重程度
  private determineSeverity(errorCode: number): 'critical' | 'warning' | 'info' {
    // 按错误代码确定严重程度，这里简单示例
    if (errorCode >= 9000) return 'critical';
    if (errorCode >= 5000) return 'warning';
    return 'info';
  }
  
  // 获取设备数据
  public getDeviceData(deviceId: string): InverterData | null {
    return this.deviceData[deviceId] || null;
  }
  
  // 获取所有设备数据
  public getAllDeviceData(): Record<string, InverterData> {
    return this.deviceData;
  }
  
  // 获取设备错误
  public getDeviceErrors(deviceId: string): GatewayError[] {
    return this.errors.filter(error => error.deviceId === deviceId);
  }
  
  // 获取所有错误
  public getAllErrors(): GatewayError[] {
    return this.errors;
  }
  
  // 解决错误
  public resolveError(errorId: string): boolean {
    const index = this.errors.findIndex(e => e.id === errorId);
    if (index >= 0) {
      this.errors[index].resolved = true;
      this.errors[index].resolvedTime = Date.now();
      return true;
    }
    return false;
  }
  
  // 清除所有已解决的错误
  public clearResolvedErrors(): number {
    const initialLength = this.errors.length;
    const unresolvedErrors = this.errors.filter(e => !e.resolved);
    this.errors.splice(0, this.errors.length, ...unresolvedErrors);
    return initialLength - this.errors.length;
  }
  
  // 测试连接
  public async testConnection(config: MQTTConfig): Promise<boolean> {
    const tempClient = mqtt.connect(config.brokerUrl, {
      clientId: `${config.clientId}-test-${Date.now()}`,
      keepalive: config.keepAlive,
      reconnectPeriod: 0, // 不重连，测试一次性
      connectTimeout: 5000, // 5秒超时
      username: config.username,
      password: config.password,
    });
    
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        tempClient.end(true);
        resolve(false);
      }, 5000);
      
      tempClient.on('connect', () => {
        clearTimeout(timeout);
        tempClient.end(true);
        resolve(true);
      });
      
      tempClient.on('error', () => {
        clearTimeout(timeout);
        tempClient.end(true);
        resolve(false);
      });
    });
  }
}

// 创建一个单例实例
export const mqttService = new MqttService(); 