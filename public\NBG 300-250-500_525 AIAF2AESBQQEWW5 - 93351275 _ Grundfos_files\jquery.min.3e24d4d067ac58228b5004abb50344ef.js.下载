/*
 jQuery JavaScript Library v1.12.4-aem
 http://jquery.com/

 Includes Sizzle.js
 http://sizzlejs.com/

 Copyright jQuery Foundation and other contributors
 Released under the MIT license
 http://jquery.org/license

 Date: 2016-05-20T17:17Z
 Sizzle CSS Selector Engine v2.2.1
 http://sizzlejs.com/

 Copyright jQuery Foundation and other contributors
 Released under the MIT license
 http://jquery.org/license

 Date: 2015-10-17
*/
(function(B,ya){"object"===typeof module&&"object"===typeof module.exports?module.exports=B.document?ya(B,!0):function(za){if(!za.document)throw Error("jQuery requires a window with a document");return ya(za)}:ya(B)})("undefined"!==typeof window?window:this,function(B,ya){function za(a){var b=!!a&&"length"in a&&a.length,d=c.type(a);return"function"===d||c.isWindow(a)?!1:"array"===d||0===b||"number"===typeof b&&0<b&&b-1 in a}function Wa(a,b,d){if(c.isFunction(b))return c.grep(a,function(e,f){return!!b.call(e,
f,e)!==d});if(b.nodeType)return c.grep(a,function(e){return e===b!==d});if("string"===typeof b){if(Rc.test(b))return c.filter(b,a,d);b=c.filter(b,a)}return c.grep(a,function(e){return-1<c.inArray(e,b)!==d})}function pa(a,b){do a=a[b];while(a&&1!==a.nodeType);return a}function Aa(a){var b={};c.each(a.match(qa)||[],function(d,e){b[e]=!0});return b}function Qb(){C.addEventListener?(C.removeEventListener("DOMContentLoaded",Ba),B.removeEventListener("load",Ba)):(C.detachEvent("onreadystatechange",Ba),
B.detachEvent("onload",Ba))}function Ba(){if(C.addEventListener||"load"===B.event.type||"complete"===C.readyState)Qb(),c.ready()}function Rb(a,b,d){if(void 0===d&&1===a.nodeType)if(d="data-"+b.replace(Sc,"-$1").toLowerCase(),d=a.getAttribute(d),"string"===typeof d){try{d="true"===d?!0:"false"===d?!1:"null"===d?null:+d+""===d?+d:Tc.test(d)?c.parseJSON(d):d}catch(e){}c.data(a,b,d)}else d=void 0;return d}function qb(a){for(var b in a)if(("data"!==b||!c.isEmptyObject(a[b]))&&"toJSON"!==b)return!1;return!0}
function Sb(a,b,d,e){if(Xa(a)){var f=c.expando,g=a.nodeType,k=g?c.cache:a,m=g?a[f]:a[f]&&f;if(m&&k[m]&&(e||k[m].data)||void 0!==d||"string"!==typeof b){m||(m=g?a[f]=ta.pop()||c.guid++:f);k[m]||(k[m]=g?{}:{toJSON:c.noop});if("object"===typeof b||"function"===typeof b)e?k[m]=c.extend(k[m],b):k[m].data=c.extend(k[m].data,b);a=k[m];e||(a.data||(a.data={}),a=a.data);void 0!==d&&(a[c.camelCase(b)]=d);"string"===typeof b?(d=a[b],null==d&&(d=a[c.camelCase(b)])):d=a;return d}}}function Tb(a,b,d){if(Xa(a)){var e,
f,g=a.nodeType,k=g?c.cache:a,m=g?a[c.expando]:c.expando;if(k[m]){if(b&&(e=d?k[m]:k[m].data)){c.isArray(b)?b=b.concat(c.map(b,c.camelCase)):b in e?b=[b]:(b=c.camelCase(b),b=b in e?[b]:b.split(" "));for(f=b.length;f--;)delete e[b[f]];if(d?!qb(e):!c.isEmptyObject(e))return}if(!d&&(delete k[m].data,!qb(k[m])))return;g?c.cleanData([a],!0):y.deleteExpando||k!=k.window?delete k[m]:k[m]=void 0}}}function Ub(a,b,d,e){var f=1,g=20,k=e?function(){return e.cur()}:function(){return c.css(a,b,"")},m=k(),p=d&&d[3]||
(c.cssNumber[b]?"":"px"),q=(c.cssNumber[b]||"px"!==p&&+m)&&rb.exec(c.css(a,b));if(q&&q[3]!==p){p=p||q[3];d=d||[];q=+m||1;do f=f||".5",q/=f,c.style(a,b,q+p);while(f!==(f=k()/m)&&1!==f&&--g)}if(d){q=+q||+m||0;var t=d[1]?q+(d[1]+1)*d[2]:+d[2];e&&(e.unit=p,e.start=q,e.end=t)}return t}function Vb(a){var b="abbr article aside audio bdi canvas data datalist details dialog figcaption figure footer header hgroup main mark meter nav output picture progress section summary template time video".split(" ");a=
a.createDocumentFragment();if(a.createElement)for(;b.length;)a.createElement(b.pop());return a}function ea(a,b){var d,e,f=0,g="undefined"!==typeof a.getElementsByTagName?a.getElementsByTagName(b||"*"):"undefined"!==typeof a.querySelectorAll?a.querySelectorAll(b||"*"):void 0;if(!g)for(g=[],d=a.childNodes||a;null!=(e=d[f]);f++)!b||c.nodeName(e,b)?g.push(e):c.merge(g,ea(e,b));return void 0===b||b&&c.nodeName(a,b)?c.merge([a],g):g}function sb(a,b){for(var d,e=0;null!=(d=a[e]);e++)c._data(d,"globalEval",
!b||c._data(b[e],"globalEval"))}function Uc(a){tb.test(a.type)&&(a.defaultChecked=a.checked)}function Wb(a,b,d,e,f){for(var g,k,m,p,q,t,v=a.length,A=Vb(b),Q=[],L=0;L<v;L++)if((k=a[L])||0===k)if("object"===c.type(k))c.merge(Q,k.nodeType?[k]:k);else if(Vc.test(k)){m=m||A.appendChild(b.createElement("div"));p=(Xb.exec(k)||["",""])[1].toLowerCase();t=ka[p]||ka._default;m.innerHTML=t[1]+c.htmlPrefilter(k)+t[2];for(g=t[0];g--;)m=m.lastChild;!y.leadingWhitespace&&ub.test(k)&&Q.push(b.createTextNode(ub.exec(k)[0]));
if(!y.tbody)for(g=(k="table"!==p||Yb.test(k)?"\x3ctable\x3e"!==t[1]||Yb.test(k)?0:m:m.firstChild)&&k.childNodes.length;g--;)c.nodeName(q=k.childNodes[g],"tbody")&&!q.childNodes.length&&k.removeChild(q);c.merge(Q,m.childNodes);for(m.textContent="";m.firstChild;)m.removeChild(m.firstChild);m=A.lastChild}else Q.push(b.createTextNode(k));m&&A.removeChild(m);y.appendChecked||c.grep(ea(Q,"input"),Uc);for(L=0;k=Q[L++];)if(e&&-1<c.inArray(k,e))f&&f.push(k);else if(a=c.contains(k.ownerDocument,k),m=ea(A.appendChild(k),
"script"),a&&sb(m),d)for(g=0;k=m[g++];)Zb.test(k.type||"")&&d.push(k);return A}function bb(){return!0}function Na(){return!1}function $b(){try{return C.activeElement}catch(a){}}function vb(a,b,d,e,f,g){var k;if("object"===typeof b){"string"!==typeof d&&(e=e||d,d=void 0);for(k in b)vb(a,k,d,e,b[k],g);return a}null==e&&null==f?(f=d,e=d=void 0):null==f&&("string"===typeof d?(f=e,e=void 0):(f=e,e=d,d=void 0));if(!1===f)f=Na;else if(!f)return a;if(1===g){var m=f;f=function(p){c().off(p);return m.apply(this,
arguments)};f.guid=m.guid||(m.guid=c.guid++)}return a.each(function(){c.event.add(this,b,f,e,d)})}function ac(a,b){return c.nodeName(a,"table")&&c.nodeName(11!==b.nodeType?b:b.firstChild,"tr")?a.getElementsByTagName("tbody")[0]||a.appendChild(a.ownerDocument.createElement("tbody")):a}function bc(a){a.type=(null!==c.find.attr(a,"type"))+"/"+a.type;return a}function cc(a){var b=Wc.exec(a.type);b?a.type=b[1]:a.removeAttribute("type");return a}function dc(a,b){if(1===b.nodeType&&c.hasData(a)){var d,e;
var f=c._data(a);a=c._data(b,f);var g=f.events;if(g)for(d in delete a.handle,a.events={},g)for(f=0,e=g[d].length;f<e;f++)c.event.add(b,d,g[d][f]);a.data&&(a.data=c.extend({},a.data))}}function Fa(a,b,d,e){b=ec.apply([],b);var f,g=0,k=a.length,m=k-1,p=b[0],q=c.isFunction(p);if(q||1<k&&"string"===typeof p&&!y.checkClone&&Xc.test(p))return a.each(function(Q){var L=a.eq(Q);q&&(b[0]=p.call(this,Q,L.html()));Fa(L,b,d,e)});if(k){var t=Wb(b,a[0].ownerDocument,!1,a,e);var v=t.firstChild;1===t.childNodes.length&&
(t=v);if(v||e){var A=c.map(ea(t,"script"),bc);for(f=A.length;g<k;g++)v=t,g!==m&&(v=c.clone(v,!0,!0),f&&c.merge(A,ea(v,"script"))),d.call(a[g],v,g);if(f)for(t=A[A.length-1].ownerDocument,c.map(A,cc),g=0;g<f;g++)v=A[g],Zb.test(v.type||"")&&!c._data(v,"globalEval")&&c.contains(t,v)&&(v.src?c._evalUrl&&c._evalUrl(v.src):c.globalEval((v.text||v.textContent||v.innerHTML||"").replace(Yc,"")));t=v=null}}return a}function fc(a,b,d){for(var e=b?c.filter(b,a):a,f=0;null!=(b=e[f]);f++)d||1!==b.nodeType||c.cleanData(ea(b)),
b.parentNode&&(d&&c.contains(b.ownerDocument,b)&&sb(ea(b,"script")),b.parentNode.removeChild(b));return a}function gc(a,b){a=c(b.createElement(a)).appendTo(b.body);b=c.css(a[0],"display");a.detach();return b}function cb(a){var b=C,d=hc[a];d||(d=gc(a,b),"none"!==d&&d||(Ya=(Ya||c("\x3ciframe frameborder\x3d'0' width\x3d'0' height\x3d'0'/\x3e")).appendTo(b.documentElement),b=(Ya[0].contentWindow||Ya[0].contentDocument).document,b.write(),b.close(),d=gc(a,b),Ya.detach()),hc[a]=d);return d}function wb(a,
b){return{get:function(){if(a())delete this.get;else return(this.get=b).apply(this,arguments)}}}function ic(a){if(a in jc)return a;for(var b=a.charAt(0).toUpperCase()+a.slice(1),d=kc.length;d--;)if(a=kc[d]+b,a in jc)return a}function lc(a,b){for(var d,e,f,g=[],k=0,m=a.length;k<m;k++)e=a[k],e.style&&(g[k]=c._data(e,"olddisplay"),d=e.style.display,b?(g[k]||"none"!==d||(e.style.display=""),""===e.style.display&&Za(e)&&(g[k]=c._data(e,"olddisplay",cb(e.nodeName)))):(f=Za(e),(d&&"none"!==d||!f)&&c._data(e,
"olddisplay",f?d:c.css(e,"display"))));for(k=0;k<m;k++)e=a[k],!e.style||b&&"none"!==e.style.display&&""!==e.style.display||(e.style.display=b?g[k]||"":"none");return a}function mc(a,b,d){return(a=Zc.exec(b))?Math.max(0,a[1]-(d||0))+(a[2]||"px"):b}function nc(a,b,d,e,f){b=d===(e?"border":"content")?4:"width"===b?1:0;for(var g=0;4>b;b+=2)"margin"===d&&(g+=c.css(a,d+Ga[b],!0,f)),e?("content"===d&&(g-=c.css(a,"padding"+Ga[b],!0,f)),"margin"!==d&&(g-=c.css(a,"border"+Ga[b]+"Width",!0,f))):(g+=c.css(a,
"padding"+Ga[b],!0,f),"padding"!==d&&(g+=c.css(a,"border"+Ga[b]+"Width",!0,f)));return g}function oc(a,b,d){var e=!0,f="width"===b?a.offsetWidth:a.offsetHeight,g=Oa(a),k=y.boxSizing&&"border-box"===c.css(a,"boxSizing",!1,g);if(0>=f||null==f){f=Ha(a,b,g);if(0>f||null==f)f=a.style[b];if(db.test(f))return f;e=k&&(y.boxSizingReliable()||f===a.style[b]);f=parseFloat(f)||0}return f+nc(a,b,d||(k?"border":"content"),e,g)+"px"}function ha(a,b,d,e,f){return new ha.prototype.init(a,b,d,e,f)}function pc(){B.setTimeout(function(){Pa=
void 0});return Pa=c.now()}function eb(a,b){var d={height:a},e=0;for(b=b?1:0;4>e;e+=2-b){var f=Ga[e];d["margin"+f]=d["padding"+f]=a}b&&(d.opacity=d.width=a);return d}function qc(a,b,d){for(var e,f=(ma.tweeners[b]||[]).concat(ma.tweeners["*"]),g=0,k=f.length;g<k;g++)if(e=f[g].call(d,b,a))return e}function $c(a,b){var d,e;for(d in a){var f=c.camelCase(d);var g=b[f];var k=a[d];c.isArray(k)&&(g=k[1],k=a[d]=k[0]);d!==f&&(a[f]=k,delete a[d]);if((e=c.cssHooks[f])&&"expand"in e)for(d in k=e.expand(k),delete a[f],
k)d in a||(a[d]=k[d],b[d]=g);else b[f]=g}}function ma(a,b,d){var e,f=0,g=ma.prefilters.length,k=c.Deferred().always(function(){delete m.elem}),m=function(){if(e)return!1;var q=Pa||pc();q=Math.max(0,p.startTime+p.duration-q);for(var t=1-(q/p.duration||0),v=0,A=p.tweens.length;v<A;v++)p.tweens[v].run(t);k.notifyWith(a,[p,t,q]);if(1>t&&A)return q;k.resolveWith(a,[p]);return!1},p=k.promise({elem:a,props:c.extend({},b),opts:c.extend(!0,{specialEasing:{},easing:c.easing._default},d),originalProperties:b,
originalOptions:d,startTime:Pa||pc(),duration:d.duration,tweens:[],createTween:function(q,t){q=c.Tween(a,p.opts,q,t,p.opts.specialEasing[q]||p.opts.easing);p.tweens.push(q);return q},stop:function(q){var t=0,v=q?p.tweens.length:0;if(e)return this;for(e=!0;t<v;t++)p.tweens[t].run(1);q?(k.notifyWith(a,[p,1,0]),k.resolveWith(a,[p,q])):k.rejectWith(a,[p,q]);return this}});d=p.props;for($c(d,p.opts.specialEasing);f<g;f++)if(b=ma.prefilters[f].call(p,a,d,p.opts))return c.isFunction(b.stop)&&(c._queueHooks(p.elem,
p.opts.queue).stop=c.proxy(b.stop,b)),b;c.map(d,qc,p);c.isFunction(p.opts.start)&&p.opts.start.call(a,p);c.fx.timer(c.extend(m,{elem:a,anim:p,queue:p.opts.queue}));return p.progress(p.opts.progress).done(p.opts.done,p.opts.complete).fail(p.opts.fail).always(p.opts.always)}function Ia(a){return c.attr(a,"class")||""}function rc(a){return function(b,d){"string"!==typeof b&&(d=b,b="*");var e=0,f=b.toLowerCase().match(qa)||[];if(c.isFunction(d))for(;b=f[e++];)"+"===b.charAt(0)?(b=b.slice(1)||"*",(a[b]=
a[b]||[]).unshift(d)):(a[b]=a[b]||[]).push(d)}}function sc(a,b,d,e){function f(m){var p;g[m]=!0;c.each(a[m]||[],function(q,t){q=t(b,d,e);if("string"===typeof q&&!k&&!g[q])return b.dataTypes.unshift(q),f(q),!1;if(k)return!(p=q)});return p}var g={},k=a===xb;return f(b.dataTypes[0])||!g["*"]&&f("*")}function yb(a,b){var d,e,f=c.ajaxSettings.flatOptions||{};for(e in b)void 0!==b[e]&&((f[e]?a:d||(d={}))[e]=b[e]);d&&c.extend(!0,a,d);return a}function ad(a){if(!c.contains(a.ownerDocument||C,a))return!0;
for(;a&&1===a.nodeType;){if("none"===(a.style&&a.style.display||c.css(a,"display"))||"hidden"===a.type)return!0;a=a.parentNode}return!1}function zb(a,b,d,e){var f;if(c.isArray(b))c.each(b,function(g,k){d||bd.test(a)?e(a,k):zb(a+"["+("object"===typeof k&&null!=k?g:"")+"]",k,d,e)});else if(d||"object"!==c.type(b))e(a,b);else for(f in b)zb(a+"["+f+"]",b[f],d,e)}function Ab(){try{return new B.XMLHttpRequest}catch(a){}}function tc(){try{return new B.ActiveXObject("Microsoft.XMLHTTP")}catch(a){}}function uc(a){return c.isWindow(a)?
a:9===a.nodeType?a.defaultView||a.parentWindow:!1}var ta=[],C=B.document,Ca=ta.slice,ec=ta.concat,Bb=ta.push,vc=ta.indexOf,fb={},cd=fb.toString,Qa=fb.hasOwnProperty,y={},c=function(a,b){return new c.fn.init(a,b)},dd=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,ed=/^-ms-/,fd=/-([\da-z])/gi,gd=function(a,b){return b.toUpperCase()};c.fn=c.prototype={jquery:"1.12.4-aem",constructor:c,selector:"",length:0,toArray:function(){return Ca.call(this)},get:function(a){return null!=a?0>a?this[a+this.length]:this[a]:Ca.call(this)},
pushStack:function(a){a=c.merge(this.constructor(),a);a.prevObject=this;a.context=this.context;return a},each:function(a){return c.each(this,a)},map:function(a){return this.pushStack(c.map(this,function(b,d){return a.call(b,d,b)}))},slice:function(){return this.pushStack(Ca.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(a){var b=this.length;a=+a+(0>a?b:0);return this.pushStack(0<=a&&a<b?[this[a]]:[])},end:function(){return this.prevObject||
this.constructor()},push:Bb,sort:ta.sort,splice:ta.splice};c.extend=c.fn.extend=function(){var a,b,d,e=arguments[0]||{},f=1,g=arguments.length,k=!1;"boolean"===typeof e&&(k=e,e=arguments[f]||{},f++);"object"===typeof e||c.isFunction(e)||(e={});f===g&&(e=this,f--);for(;f<g;f++)if(null!=(d=arguments[f]))for(b in d){var m=e[b];var p=d[b];"__proto__"!==b&&e!==p&&(k&&p&&(c.isPlainObject(p)||(a=c.isArray(p)))?(a?(a=!1,m=m&&c.isArray(m)?m:[]):m=m&&c.isPlainObject(m)?m:{},e[b]=c.extend(k,m,p)):void 0!==p&&
(e[b]=p))}return e};c.extend({expando:"jQuery"+("1.12.4-aem"+Math.random()).replace(/\D/g,""),isReady:!0,error:function(a){throw Error(a);},noop:function(){},isFunction:function(a){return"function"===c.type(a)},isArray:Array.isArray||function(a){return"array"===c.type(a)},isWindow:function(a){return null!=a&&a==a.window},isNumeric:function(a){var b=a&&a.toString();return!c.isArray(a)&&0<=b-parseFloat(b)+1},isEmptyObject:function(a){for(var b in a)return!1;return!0},isPlainObject:function(a){var b;
if(!a||"object"!==c.type(a)||a.nodeType||c.isWindow(a))return!1;try{if(a.constructor&&!Qa.call(a,"constructor")&&!Qa.call(a.constructor.prototype,"isPrototypeOf"))return!1}catch(d){return!1}if(!y.ownFirst)for(b in a)return Qa.call(a,b);for(b in a);return void 0===b||Qa.call(a,b)},type:function(a){return null==a?a+"":"object"===typeof a||"function"===typeof a?fb[cd.call(a)]||"object":typeof a},globalEval:function(a){a&&c.trim(a)&&(B.execScript||function(b){B.eval.call(B,b)})(a)},camelCase:function(a){return a.replace(ed,
"ms-").replace(fd,gd)},nodeName:function(a,b){return a.nodeName&&a.nodeName.toLowerCase()===b.toLowerCase()},each:function(a,b){var d,e=0;if(za(a))for(d=a.length;e<d&&!1!==b.call(a[e],e,a[e]);e++);else for(e in a)if(!1===b.call(a[e],e,a[e]))break;return a},trim:function(a){return null==a?"":(a+"").replace(dd,"")},makeArray:function(a,b){b=b||[];null!=a&&(za(Object(a))?c.merge(b,"string"===typeof a?[a]:a):Bb.call(b,a));return b},inArray:function(a,b,d){if(b){if(vc)return vc.call(b,a,d);var e=b.length;
for(d=d?0>d?Math.max(0,e+d):d:0;d<e;d++)if(d in b&&b[d]===a)return d}return-1},merge:function(a,b){for(var d=+b.length,e=0,f=a.length;e<d;)a[f++]=b[e++];if(d!==d)for(;void 0!==b[e];)a[f++]=b[e++];a.length=f;return a},grep:function(a,b,d){for(var e=[],f=0,g=a.length,k=!d;f<g;f++)d=!b(a[f],f),d!==k&&e.push(a[f]);return e},map:function(a,b,d){var e,f=0,g=[];if(za(a))for(e=a.length;f<e;f++){var k=b(a[f],f,d);null!=k&&g.push(k)}else for(f in a)k=b(a[f],f,d),null!=k&&g.push(k);return ec.apply([],g)},guid:1,
proxy:function(a,b){if("string"===typeof b){var d=a[b];b=a;a=d}if(c.isFunction(a)){var e=Ca.call(arguments,2);d=function(){return a.apply(b||this,e.concat(Ca.call(arguments)))};d.guid=a.guid=a.guid||c.guid++;return d}},now:function(){return+new Date},support:y});"function"===typeof Symbol&&(c.fn[Symbol.iterator]=ta[Symbol.iterator]);c.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(a,b){fb["[object "+b+"]"]=b.toLowerCase()});var Ta=function(a){function b(h,
n,l,r){var u,x,w,D,z=n&&n.ownerDocument,F=n?n.nodeType:9;l=l||[];if("string"!==typeof h||!h||1!==F&&9!==F&&11!==F)return l;if(!r&&((n?n.ownerDocument||n:O)!==G&&Ja(n),n=n||G,R)){if(11!==F&&(D=hd.exec(h)))if(u=D[1])if(9===F)if(x=n.getElementById(u)){if(x.id===u)return l.push(x),l}else return l;else{if(z&&(x=z.getElementById(u))&&la(n,x)&&x.id===u)return l.push(x),l}else{if(D[2])return Da.apply(l,n.getElementsByTagName(h)),l;if((u=D[3])&&S.getElementsByClassName&&n.getElementsByClassName)return Da.apply(l,
n.getElementsByClassName(u)),l}if(!(!S.qsa||ba[h+" "]||N&&N.test(h))){if(1!==F){z=n;var K=h}else if("object"!==n.nodeName.toLowerCase()){(w=n.getAttribute("id"))?w=w.replace(id,"\\$\x26"):n.setAttribute("id",w=M);D=gb(h);u=D.length;for(x=wc.test(w)?"#"+w:"[id\x3d'"+w+"']";u--;)D[u]=x+" "+A(D[u]);K=D.join(",");z=Cb.test(h)&&t(n.parentNode)||n}if(K)try{return Da.apply(l,z.querySelectorAll(K)),l}catch(E){}finally{w===M&&n.removeAttribute("id")}}}return jd(h.replace(hb,"$1"),n,l,r)}function d(){function h(l,
r){n.push(l+" ")>P.cacheLength&&delete h[n.shift()];return h[l+" "]=r}var n=[];return h}function e(h){h[M]=!0;return h}function f(h){var n=G.createElement("div");try{return!!h(n)}catch(l){return!1}finally{n.parentNode&&n.parentNode.removeChild(n)}}function g(h,n){h=h.split("|");for(var l=h.length;l--;)P.attrHandle[h[l]]=n}function k(h,n){var l=n&&h,r=l&&1===h.nodeType&&1===n.nodeType&&(~n.sourceIndex||-2147483648)-(~h.sourceIndex||-2147483648);if(r)return r;if(l)for(;l=l.nextSibling;)if(l===n)return-1;
return h?1:-1}function m(h){return function(n){return"input"===n.nodeName.toLowerCase()&&n.type===h}}function p(h){return function(n){var l=n.nodeName.toLowerCase();return("input"===l||"button"===l)&&n.type===h}}function q(h){return e(function(n){n=+n;return e(function(l,r){for(var u,x=h([],l.length,n),w=x.length;w--;)l[u=x[w]]&&(l[u]=!(r[u]=l[u]))})})}function t(h){return h&&"undefined"!==typeof h.getElementsByTagName&&h}function v(){}function A(h){for(var n=0,l=h.length,r="";n<l;n++)r+=h[n].value;
return r}function Q(h,n,l){var r=n.dir,u=l&&"parentNode"===r,x=na++;return n.first?function(w,D,z){for(;w=w[r];)if(1===w.nodeType||u)return h(w,D,z)}:function(w,D,z){var F,K=[Y,x];if(z)for(;w=w[r];){if((1===w.nodeType||u)&&h(w,D,z))return!0}else for(;w=w[r];)if(1===w.nodeType||u){var E=w[M]||(w[M]={});E=E[w.uniqueID]||(E[w.uniqueID]={});if((F=E[r])&&F[0]===Y&&F[1]===x)return K[2]=F[2];E[r]=K;if(K[2]=h(w,D,z))return!0}}}function L(h){return 1<h.length?function(n,l,r){for(var u=h.length;u--;)if(!h[u](n,
l,r))return!1;return!0}:h[0]}function ja(h,n,l,r,u){for(var x,w=[],D=0,z=h.length,F=null!=n;D<z;D++)if(x=h[D])if(!l||l(x,r,u))w.push(x),F&&n.push(D);return w}function ia(h,n,l,r,u,x){r&&!r[M]&&(r=ia(r));u&&!u[M]&&(u=ia(u,x));return e(function(w,D,z,F){var K,E=[],T=[],Z=D.length,X;if(!(X=w)){X=n||"*";for(var H=z.nodeType?[z]:z,ra=[],V=0,ib=H.length;V<ib;V++)b(X,H[V],ra);X=ra}X=!h||!w&&n?X:ja(X,E,h,z,F);H=l?u||(w?h:Z||r)?[]:D:X;l&&l(X,H,z,F);if(r){var oa=ja(H,T);r(oa,[],z,F);for(z=oa.length;z--;)if(K=
oa[z])H[T[z]]=!(X[T[z]]=K)}if(w){if(u||h){if(u){oa=[];for(z=H.length;z--;)(K=H[z])&&oa.push(X[z]=K);u(null,H=[],oa,F)}for(z=H.length;z--;)(K=H[z])&&-1<(oa=u?Ka(w,K):E[z])&&(w[oa]=!(D[oa]=K))}}else H=ja(H===D?H.splice(Z,H.length):H),u?u(null,D,H,F):Da.apply(D,H)})}function fa(h){var n,l,r=h.length,u=P.relative[h[0].type];var x=u||P.relative[" "];for(var w=u?1:0,D=Q(function(K){return K===n},x,!0),z=Q(function(K){return-1<Ka(n,K)},x,!0),F=[function(K,E,T){K=!u&&(T||E!==ca)||((n=E).nodeType?D(K,E,T):
z(K,E,T));n=null;return K}];w<r;w++)if(x=P.relative[h[w].type])F=[Q(L(F),x)];else{x=P.filter[h[w].type].apply(null,h[w].matches);if(x[M]){for(l=++w;l<r&&!P.relative[h[l].type];l++);return ia(1<w&&L(F),1<w&&A(h.slice(0,w-1).concat({value:" "===h[w-2].type?"*":""})).replace(hb,"$1"),x,w<l&&fa(h.slice(w,l)),l<r&&fa(h=h.slice(l)),l<r&&A(h))}F.push(x)}return L(F)}function jb(h,n){var l=0<n.length,r=0<h.length,u=function(x,w,D,z,F){var K,E,T=0,Z="0",X=x&&[],H=[],ra=ca,V=x||r&&P.find.TAG("*",F),ib=Y+=null==
ra?1:Math.random()||.1,oa=V.length;for(F&&(ca=w===G||w||F);Z!==oa&&null!=(K=V[Z]);Z++){if(r&&K){var Db=0;w||K.ownerDocument===G||(Ja(K),D=!R);for(;E=h[Db++];)if(E(K,w||G,D)){z.push(K);break}F&&(Y=ib)}l&&((K=!E&&K)&&T--,x&&X.push(K))}T+=Z;if(l&&Z!==T){for(Db=0;E=n[Db++];)E(X,H,w,D);if(x){if(0<T)for(;Z--;)X[Z]||H[Z]||(H[Z]=kd.call(z));H=ja(H)}Da.apply(z,H);F&&!x&&0<H.length&&1<T+n.length&&b.uniqueSort(z)}F&&(Y=ib,ca=ra);return X};return l?e(u):u}var I,ca,J,U,G,W,R,N,aa,da,la,M="sizzle"+1*new Date,O=
a.document,Y=0,na=0,La=d(),sa=d(),ba=d(),Ra=function(h,n){h===n&&(U=!0);return 0},Sa={}.hasOwnProperty,ua=[],kd=ua.pop,ld=ua.push,Da=ua.push,xc=ua.slice,Ka=function(h,n){for(var l=0,r=h.length;l<r;l++)if(h[l]===n)return l;return-1},md=/[\x20\t\r\n\f]+/g,hb=/^[\x20\t\r\n\f]+|((?:^|[^\\])(?:\\.)*)[\x20\t\r\n\f]+$/g,nd=/^[\x20\t\r\n\f]*,[\x20\t\r\n\f]*/,od=/^[\x20\t\r\n\f]*([>+~]|[\x20\t\r\n\f])[\x20\t\r\n\f]*/,pd=/=[\x20\t\r\n\f]*([^\]'"]*?)[\x20\t\r\n\f]*\]/g,qd=/:((?:\\.|[\w-]|[^\x00-\xa0])+)(?:\((('((?:\\.|[^\\'])*)'|"((?:\\.|[^\\"])*)")|((?:\\.|[^\\()[\]]|\[[\x20\t\r\n\f]*((?:\\.|[\w-]|[^\x00-\xa0])+)(?:[\x20\t\r\n\f]*([*^$|!~]?=)[\x20\t\r\n\f]*(?:'((?:\\.|[^\\'])*)'|"((?:\\.|[^\\"])*)"|((?:\\.|[\w-]|[^\x00-\xa0])+))|)[\x20\t\r\n\f]*\])*)|.*)\)|)/,
wc=/^(?:\\.|[\w-]|[^\x00-\xa0])+$/,kb={ID:/^#((?:\\.|[\w-]|[^\x00-\xa0])+)/,CLASS:/^\.((?:\\.|[\w-]|[^\x00-\xa0])+)/,TAG:/^((?:\\.|[\w-]|[^\x00-\xa0])+|[*])/,ATTR:/^\[[\x20\t\r\n\f]*((?:\\.|[\w-]|[^\x00-\xa0])+)(?:[\x20\t\r\n\f]*([*^$|!~]?=)[\x20\t\r\n\f]*(?:'((?:\\.|[^\\'])*)'|"((?:\\.|[^\\"])*)"|((?:\\.|[\w-]|[^\x00-\xa0])+))|)[\x20\t\r\n\f]*\]/,PSEUDO:/^:((?:\\.|[\w-]|[^\x00-\xa0])+)(?:\((('((?:\\.|[^\\'])*)'|"((?:\\.|[^\\"])*)")|((?:\\.|[^\\()[\]]|\[[\x20\t\r\n\f]*((?:\\.|[\w-]|[^\x00-\xa0])+)(?:[\x20\t\r\n\f]*([*^$|!~]?=)[\x20\t\r\n\f]*(?:'((?:\\.|[^\\'])*)'|"((?:\\.|[^\\"])*)"|((?:\\.|[\w-]|[^\x00-\xa0])+))|)[\x20\t\r\n\f]*\])*)|.*)\)|)/,
CHILD:/^:(only|first|last|nth|nth-last)-(child|of-type)(?:\([\x20\t\r\n\f]*(even|odd|(([+-]|)(\d*)n|)[\x20\t\r\n\f]*(?:([+-]|)[\x20\t\r\n\f]*(\d+)|))[\x20\t\r\n\f]*\)|)/i,bool:/^(?:checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped)$/i,needsContext:/^[\x20\t\r\n\f]*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\([\x20\t\r\n\f]*((?:-\d)?\d*)[\x20\t\r\n\f]*\)|)(?=[^-]|$)/i},rd=/^(?:input|select|textarea|button)$/i,sd=/^h\d$/i,$a=
/^[^{]+\{\s*\[native \w/,hd=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Cb=/[+~]/,id=/'|\\/g,va=/\\([\da-f]{1,6}[\x20\t\r\n\f]?|([\x20\t\r\n\f])|.)/ig,wa=function(h,n,l){h="0x"+n-65536;return h!==h||l?n:0>h?String.fromCharCode(h+65536):String.fromCharCode(h>>10|55296,h&1023|56320)},yc=function(){Ja()};try{Da.apply(ua=xc.call(O.childNodes),O.childNodes),ua[O.childNodes.length].nodeType}catch(h){Da={apply:ua.length?function(n,l){ld.apply(n,xc.call(l))}:function(n,l){for(var r=n.length,u=0;n[r++]=l[u++];);n.length=
r-1}}}var S=b.support={};var td=b.isXML=function(h){return(h=h&&(h.ownerDocument||h).documentElement)?"HTML"!==h.nodeName:!1};var Ja=b.setDocument=function(h){var n;h=h?h.ownerDocument||h:O;if(h===G||9!==h.nodeType||!h.documentElement)return G;G=h;W=G.documentElement;R=!td(G);(n=G.defaultView)&&n.top!==n&&(n.addEventListener?n.addEventListener("unload",yc,!1):n.attachEvent&&n.attachEvent("onunload",yc));S.attributes=f(function(l){l.className="i";return!l.getAttribute("className")});S.getElementsByTagName=
f(function(l){l.appendChild(G.createComment(""));return!l.getElementsByTagName("*").length});S.getElementsByClassName=$a.test(G.getElementsByClassName);S.getById=f(function(l){W.appendChild(l).id=M;return!G.getElementsByName||!G.getElementsByName(M).length});S.getById?(P.find.ID=function(l,r){if("undefined"!==typeof r.getElementById&&R)return(l=r.getElementById(l))?[l]:[]},P.filter.ID=function(l){var r=l.replace(va,wa);return function(u){return u.getAttribute("id")===r}}):(delete P.find.ID,P.filter.ID=
function(l){var r=l.replace(va,wa);return function(u){return(u="undefined"!==typeof u.getAttributeNode&&u.getAttributeNode("id"))&&u.value===r}});P.find.TAG=S.getElementsByTagName?function(l,r){if("undefined"!==typeof r.getElementsByTagName)return r.getElementsByTagName(l);if(S.qsa)return r.querySelectorAll(l)}:function(l,r){var u=[],x=0;r=r.getElementsByTagName(l);if("*"===l){for(;l=r[x++];)1===l.nodeType&&u.push(l);return u}return r};P.find.CLASS=S.getElementsByClassName&&function(l,r){if("undefined"!==
typeof r.getElementsByClassName&&R)return r.getElementsByClassName(l)};aa=[];N=[];if(S.qsa=$a.test(G.querySelectorAll))f(function(l){W.appendChild(l).innerHTML="\x3ca id\x3d'"+M+"'\x3e\x3c/a\x3e\x3cselect id\x3d'"+M+"-\r\\' msallowcapture\x3d''\x3e\x3coption selected\x3d''\x3e\x3c/option\x3e\x3c/select\x3e";l.querySelectorAll("[msallowcapture^\x3d'']").length&&N.push("[*^$]\x3d[\\x20\\t\\r\\n\\f]*(?:''|\"\")");l.querySelectorAll("[selected]").length||N.push("\\[[\\x20\\t\\r\\n\\f]*(?:value|checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped)");
l.querySelectorAll("[id~\x3d"+M+"-]").length||N.push("~\x3d");l.querySelectorAll(":checked").length||N.push(":checked");l.querySelectorAll("a#"+M+"+*").length||N.push(".#.+[+~]")}),f(function(l){var r=G.createElement("input");r.setAttribute("type","hidden");l.appendChild(r).setAttribute("name","D");l.querySelectorAll("[name\x3dd]").length&&N.push("name[\\x20\\t\\r\\n\\f]*[*^$|!~]?\x3d");l.querySelectorAll(":enabled").length||N.push(":enabled",":disabled");l.querySelectorAll("*,:x");N.push(",.*:")});
(S.matchesSelector=$a.test(da=W.matches||W.webkitMatchesSelector||W.mozMatchesSelector||W.oMatchesSelector||W.msMatchesSelector))&&f(function(l){S.disconnectedMatch=da.call(l,"div");da.call(l,"[s!\x3d'']:x");aa.push("!\x3d",":((?:\\\\.|[\\w-]|[^\\x00-\\xa0])+)(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|\\[[\\x20\\t\\r\\n\\f]*((?:\\\\.|[\\w-]|[^\\x00-\\xa0])+)(?:[\\x20\\t\\r\\n\\f]*([*^$|!~]?\x3d)[\\x20\\t\\r\\n\\f]*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|((?:\\\\.|[\\w-]|[^\\x00-\\xa0])+))|)[\\x20\\t\\r\\n\\f]*\\])*)|.*)\\)|)")});
N=N.length&&new RegExp(N.join("|"));aa=aa.length&&new RegExp(aa.join("|"));la=(n=$a.test(W.compareDocumentPosition))||$a.test(W.contains)?function(l,r){var u=9===l.nodeType?l.documentElement:l;r=r&&r.parentNode;return l===r||!!(r&&1===r.nodeType&&(u.contains?u.contains(r):l.compareDocumentPosition&&l.compareDocumentPosition(r)&16))}:function(l,r){if(r)for(;r=r.parentNode;)if(r===l)return!0;return!1};Ra=n?function(l,r){if(l===r)return U=!0,0;var u=!l.compareDocumentPosition-!r.compareDocumentPosition;
if(u)return u;u=(l.ownerDocument||l)===(r.ownerDocument||r)?l.compareDocumentPosition(r):1;return u&1||!S.sortDetached&&r.compareDocumentPosition(l)===u?l===G||l.ownerDocument===O&&la(O,l)?-1:r===G||r.ownerDocument===O&&la(O,r)?1:J?Ka(J,l)-Ka(J,r):0:u&4?-1:1}:function(l,r){if(l===r)return U=!0,0;var u=0,x=l.parentNode,w=r.parentNode,D=[l],z=[r];if(!x||!w)return l===G?-1:r===G?1:x?-1:w?1:J?Ka(J,l)-Ka(J,r):0;if(x===w)return k(l,r);for(;l=l.parentNode;)D.unshift(l);for(l=r;l=l.parentNode;)z.unshift(l);
for(;D[u]===z[u];)u++;return u?k(D[u],z[u]):D[u]===O?-1:z[u]===O?1:0};return G};b.matches=function(h,n){return b(h,null,null,n)};b.matchesSelector=function(h,n){(h.ownerDocument||h)!==G&&Ja(h);n=n.replace(pd,"\x3d'$1']");if(!(!S.matchesSelector||!R||ba[n+" "]||aa&&aa.test(n)||N&&N.test(n)))try{var l=da.call(h,n);if(l||S.disconnectedMatch||h.document&&11!==h.document.nodeType)return l}catch(r){}return 0<b(n,G,null,[h]).length};b.contains=function(h,n){(h.ownerDocument||h)!==G&&Ja(h);return la(h,n)};
b.attr=function(h,n){(h.ownerDocument||h)!==G&&Ja(h);var l=P.attrHandle[n.toLowerCase()];l=l&&Sa.call(P.attrHandle,n.toLowerCase())?l(h,n,!R):void 0;return void 0!==l?l:S.attributes||!R?h.getAttribute(n):(l=h.getAttributeNode(n))&&l.specified?l.value:null};b.error=function(h){throw Error("Syntax error, unrecognized expression: "+h);};b.uniqueSort=function(h){var n,l=[],r=0,u=0;U=!S.detectDuplicates;J=!S.sortStable&&h.slice(0);h.sort(Ra);if(U){for(;n=h[u++];)n===h[u]&&(r=l.push(u));for(;r--;)h.splice(l[r],
1)}J=null;return h};var Eb=b.getText=function(h){var n="",l=0;var r=h.nodeType;if(!r)for(;r=h[l++];)n+=Eb(r);else if(1===r||9===r||11===r){if("string"===typeof h.textContent)return h.textContent;for(h=h.firstChild;h;h=h.nextSibling)n+=Eb(h)}else if(3===r||4===r)return h.nodeValue;return n};var P=b.selectors={cacheLength:50,createPseudo:e,match:kb,attrHandle:{},find:{},relative:{"\x3e":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},
preFilter:{ATTR:function(h){h[1]=h[1].replace(va,wa);h[3]=(h[3]||h[4]||h[5]||"").replace(va,wa);"~\x3d"===h[2]&&(h[3]=" "+h[3]+" ");return h.slice(0,4)},CHILD:function(h){h[1]=h[1].toLowerCase();"nth"===h[1].slice(0,3)?(h[3]||b.error(h[0]),h[4]=+(h[4]?h[5]+(h[6]||1):2*("even"===h[3]||"odd"===h[3])),h[5]=+(h[7]+h[8]||"odd"===h[3])):h[3]&&b.error(h[0]);return h},PSEUDO:function(h){var n,l=!h[6]&&h[2];if(kb.CHILD.test(h[0]))return null;h[3]?h[2]=h[4]||h[5]||"":l&&qd.test(l)&&(n=gb(l,!0))&&(n=l.indexOf(")",
l.length-n)-l.length)&&(h[0]=h[0].slice(0,n),h[2]=l.slice(0,n));return h.slice(0,3)}},filter:{TAG:function(h){var n=h.replace(va,wa).toLowerCase();return"*"===h?function(){return!0}:function(l){return l.nodeName&&l.nodeName.toLowerCase()===n}},CLASS:function(h){var n=La[h+" "];return n||(n=new RegExp("(^|[\\x20\\t\\r\\n\\f])"+h+"([\\x20\\t\\r\\n\\f]|$)"),La(h,function(l){return n.test("string"===typeof l.className&&l.className||"undefined"!==typeof l.getAttribute&&l.getAttribute("class")||"")}))},
ATTR:function(h,n,l){return function(r){r=b.attr(r,h);if(null==r)return"!\x3d"===n;if(!n)return!0;r+="";return"\x3d"===n?r===l:"!\x3d"===n?r!==l:"^\x3d"===n?l&&0===r.indexOf(l):"*\x3d"===n?l&&-1<r.indexOf(l):"$\x3d"===n?l&&r.slice(-l.length)===l:"~\x3d"===n?-1<(" "+r.replace(md," ")+" ").indexOf(l):"|\x3d"===n?r===l||r.slice(0,l.length+1)===l+"-":!1}},CHILD:function(h,n,l,r,u){var x="nth"!==h.slice(0,3),w="last"!==h.slice(-4),D="of-type"===n;return 1===r&&0===u?function(z){return!!z.parentNode}:function(z,
F,K){var E,T;F=x!==w?"nextSibling":"previousSibling";var Z=z.parentNode,X=D&&z.nodeName.toLowerCase();K=!K&&!D;var H=!1;if(Z){if(x){for(;F;){for(E=z;E=E[F];)if(D?E.nodeName.toLowerCase()===X:1===E.nodeType)return!1;var ra=F="only"===h&&!ra&&"nextSibling"}return!0}ra=[w?Z.firstChild:Z.lastChild];if(w&&K){E=Z;var V=E[M]||(E[M]={});V=V[E.uniqueID]||(V[E.uniqueID]={});H=V[h]||[];H=(T=H[0]===Y&&H[1])&&H[2];for(E=T&&Z.childNodes[T];E=++T&&E&&E[F]||(H=T=0)||ra.pop();)if(1===E.nodeType&&++H&&E===z){V[h]=
[Y,T,H];break}}else if(K&&(E=z,V=E[M]||(E[M]={}),V=V[E.uniqueID]||(V[E.uniqueID]={}),H=V[h]||[],H=T=H[0]===Y&&H[1]),!1===H)for(;(E=++T&&E&&E[F]||(H=T=0)||ra.pop())&&((D?E.nodeName.toLowerCase()!==X:1!==E.nodeType)||!++H||(K&&(V=E[M]||(E[M]={}),V=V[E.uniqueID]||(V[E.uniqueID]={}),V[h]=[Y,H]),E!==z)););H-=u;return H===r||0===H%r&&0<=H/r}}},PSEUDO:function(h,n){var l=P.pseudos[h]||P.setFilters[h.toLowerCase()]||b.error("unsupported pseudo: "+h);if(l[M])return l(n);if(1<l.length){var r=[h,h,"",n];return P.setFilters.hasOwnProperty(h.toLowerCase())?
e(function(u,x){for(var w,D=l(u,n),z=D.length;z--;)w=Ka(u,D[z]),u[w]=!(x[w]=D[z])}):function(u){return l(u,0,r)}}return l}},pseudos:{not:e(function(h){var n=[],l=[],r=zc(h.replace(hb,"$1"));return r[M]?e(function(u,x,w,D){D=r(u,null,D,[]);for(var z=u.length;z--;)if(w=D[z])u[z]=!(x[z]=w)}):function(u,x,w){n[0]=u;r(n,null,w,l);n[0]=null;return!l.pop()}}),has:e(function(h){return function(n){return 0<b(h,n).length}}),contains:e(function(h){h=h.replace(va,wa);return function(n){return-1<(n.textContent||
n.innerText||Eb(n)).indexOf(h)}}),lang:e(function(h){wc.test(h||"")||b.error("unsupported lang: "+h);h=h.replace(va,wa).toLowerCase();return function(n){var l;do if(l=R?n.lang:n.getAttribute("xml:lang")||n.getAttribute("lang"))return l=l.toLowerCase(),l===h||0===l.indexOf(h+"-");while((n=n.parentNode)&&1===n.nodeType);return!1}}),target:function(h){var n=a.location&&a.location.hash;return n&&n.slice(1)===h.id},root:function(h){return h===W},focus:function(h){return h===G.activeElement&&(!G.hasFocus||
G.hasFocus())&&!!(h.type||h.href||~h.tabIndex)},enabled:function(h){return!1===h.disabled},disabled:function(h){return!0===h.disabled},checked:function(h){var n=h.nodeName.toLowerCase();return"input"===n&&!!h.checked||"option"===n&&!!h.selected},selected:function(h){h.parentNode&&h.parentNode.selectedIndex;return!0===h.selected},empty:function(h){for(h=h.firstChild;h;h=h.nextSibling)if(6>h.nodeType)return!1;return!0},parent:function(h){return!P.pseudos.empty(h)},header:function(h){return sd.test(h.nodeName)},
input:function(h){return rd.test(h.nodeName)},button:function(h){var n=h.nodeName.toLowerCase();return"input"===n&&"button"===h.type||"button"===n},text:function(h){var n;return"input"===h.nodeName.toLowerCase()&&"text"===h.type&&(null==(n=h.getAttribute("type"))||"text"===n.toLowerCase())},first:q(function(){return[0]}),last:q(function(h,n){return[n-1]}),eq:q(function(h,n,l){return[0>l?l+n:l]}),even:q(function(h,n){for(var l=0;l<n;l+=2)h.push(l);return h}),odd:q(function(h,n){for(var l=1;l<n;l+=
2)h.push(l);return h}),lt:q(function(h,n,l){for(n=0>l?l+n:l;0<=--n;)h.push(n);return h}),gt:q(function(h,n,l){for(l=0>l?l+n:l;++l<n;)h.push(l);return h})}};P.pseudos.nth=P.pseudos.eq;for(I in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})P.pseudos[I]=m(I);for(I in{submit:!0,reset:!0})P.pseudos[I]=p(I);v.prototype=P.filters=P.pseudos;P.setFilters=new v;var gb=b.tokenize=function(h,n){var l,r,u,x,w;if(x=sa[h+" "])return n?0:x.slice(0);x=h;var D=[];for(w=P.preFilter;x;){if(!z||(l=nd.exec(x)))l&&
(x=x.slice(l[0].length)||x),D.push(r=[]);var z=!1;if(l=od.exec(x))z=l.shift(),r.push({value:z,type:l[0].replace(hb," ")}),x=x.slice(z.length);for(u in P.filter)!(l=kb[u].exec(x))||w[u]&&!(l=w[u](l))||(z=l.shift(),r.push({value:z,type:u,matches:l}),x=x.slice(z.length));if(!z)break}return n?x.length:x?b.error(h):sa(h,D).slice(0)};var zc=b.compile=function(h,n){var l,r=[],u=[],x=ba[h+" "];if(!x){n||(n=gb(h));for(l=n.length;l--;)x=fa(n[l]),x[M]?r.push(x):u.push(x);x=ba(h,jb(u,r));x.selector=h}return x};
var jd=b.select=function(h,n,l,r){var u,x,w,D="function"===typeof h&&h,z=!r&&gb(h=D.selector||h);l=l||[];if(1===z.length){var F=z[0]=z[0].slice(0);if(2<F.length&&"ID"===(x=F[0]).type&&S.getById&&9===n.nodeType&&R&&P.relative[F[1].type]){n=(P.find.ID(x.matches[0].replace(va,wa),n)||[])[0];if(!n)return l;D&&(n=n.parentNode);h=h.slice(F.shift().value.length)}for(u=kb.needsContext.test(h)?0:F.length;u--;){x=F[u];if(P.relative[w=x.type])break;if(w=P.find[w])if(r=w(x.matches[0].replace(va,wa),Cb.test(F[0].type)&&
t(n.parentNode)||n)){F.splice(u,1);h=r.length&&A(F);if(!h)return Da.apply(l,r),l;break}}}(D||zc(h,z))(r,n,!R,l,!n||Cb.test(h)&&t(n.parentNode)||n);return l};S.sortStable=M.split("").sort(Ra).join("")===M;S.detectDuplicates=!!U;Ja();S.sortDetached=f(function(h){return h.compareDocumentPosition(G.createElement("div"))&1});f(function(h){h.innerHTML="\x3ca href\x3d'#'\x3e\x3c/a\x3e";return"#"===h.firstChild.getAttribute("href")})||g("type|href|height|width",function(h,n,l){if(!l)return h.getAttribute(n,
"type"===n.toLowerCase()?1:2)});S.attributes&&f(function(h){h.innerHTML="\x3cinput/\x3e";h.firstChild.setAttribute("value","");return""===h.firstChild.getAttribute("value")})||g("value",function(h,n,l){if(!l&&"input"===h.nodeName.toLowerCase())return h.defaultValue});f(function(h){return null==h.getAttribute("disabled")})||g("checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",function(h,n,l){var r;if(!l)return!0===h[n]?n.toLowerCase():
(r=h.getAttributeNode(n))&&r.specified?r.value:null});return b}(B);c.find=Ta;c.expr=Ta.selectors;c.expr[":"]=c.expr.pseudos;c.uniqueSort=c.unique=Ta.uniqueSort;c.text=Ta.getText;c.isXMLDoc=Ta.isXML;c.contains=Ta.contains;var Ua=function(a,b,d){for(var e=[],f=void 0!==d;(a=a[b])&&9!==a.nodeType;)if(1===a.nodeType){if(f&&c(a).is(d))break;e.push(a)}return e},Ac=function(a,b){for(var d=[];a;a=a.nextSibling)1===a.nodeType&&a!==b&&d.push(a);return d},Bc=c.expr.match.needsContext,Cc=/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/,
Rc=/^.[^:#\[\.,]*$/;c.filter=function(a,b,d){var e=b[0];d&&(a=":not("+a+")");return 1===b.length&&1===e.nodeType?c.find.matchesSelector(e,a)?[e]:[]:c.find.matches(a,c.grep(b,function(f){return 1===f.nodeType}))};c.fn.extend({find:function(a){var b,d=[],e=this,f=e.length;if("string"!==typeof a)return this.pushStack(c(a).filter(function(){for(b=0;b<f;b++)if(c.contains(e[b],this))return!0}));for(b=0;b<f;b++)c.find(a,e[b],d);d=this.pushStack(1<f?c.unique(d):d);d.selector=this.selector?this.selector+" "+
a:a;return d},filter:function(a){return this.pushStack(Wa(this,a||[],!1))},not:function(a){return this.pushStack(Wa(this,a||[],!0))},is:function(a){return!!Wa(this,"string"===typeof a&&Bc.test(a)?c(a):a||[],!1).length}});var ud=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/;(c.fn.init=function(a,b,d){if(!a)return this;d=d||Dc;if("string"===typeof a){var e="\x3c"===a.charAt(0)&&"\x3e"===a.charAt(a.length-1)&&3<=a.length?[null,a,null]:ud.exec(a);if(!e||!e[1]&&b)return!b||b.jquery?(b||d).find(a):this.constructor(b).find(a);
if(e[1]){if(b=b instanceof c?b[0]:b,c.merge(this,c.parseHTML(e[1],b&&b.nodeType?b.ownerDocument||b:C,!0)),Cc.test(e[1])&&c.isPlainObject(b))for(e in b)if(c.isFunction(this[e]))this[e](b[e]);else this.attr(e,b[e])}else{if((b=C.getElementById(e[2]))&&b.parentNode){if(b.id!==e[2])return Dc.find(a);this.length=1;this[0]=b}this.context=C;this.selector=a}return this}if(a.nodeType)return this.context=this[0]=a,this.length=1,this;if(c.isFunction(a))return"undefined"!==typeof d.ready?d.ready(a):a(c);void 0!==
a.selector&&(this.selector=a.selector,this.context=a.context);return c.makeArray(a,this)}).prototype=c.fn;var Dc=c(C);var vd=/^(?:parents|prev(?:Until|All))/,wd={children:!0,contents:!0,next:!0,prev:!0};c.fn.extend({has:function(a){var b,d=c(a,this),e=d.length;return this.filter(function(){for(b=0;b<e;b++)if(c.contains(this,d[b]))return!0})},closest:function(a,b){for(var d,e=0,f=this.length,g=[],k=Bc.test(a)||"string"!==typeof a?c(a,b||this.context):0;e<f;e++)for(d=this[e];d&&d!==b;d=d.parentNode)if(11>
d.nodeType&&(k?-1<k.index(d):1===d.nodeType&&c.find.matchesSelector(d,a))){g.push(d);break}return this.pushStack(1<g.length?c.uniqueSort(g):g)},index:function(a){return a?"string"===typeof a?c.inArray(this[0],c(a)):c.inArray(a.jquery?a[0]:a,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(a,b){return this.pushStack(c.uniqueSort(c.merge(this.get(),c(a,b))))},addBack:function(a){return this.add(null==a?this.prevObject:this.prevObject.filter(a))}});c.each({parent:function(a){return(a=
a.parentNode)&&11!==a.nodeType?a:null},parents:function(a){return Ua(a,"parentNode")},parentsUntil:function(a,b,d){return Ua(a,"parentNode",d)},next:function(a){return pa(a,"nextSibling")},prev:function(a){return pa(a,"previousSibling")},nextAll:function(a){return Ua(a,"nextSibling")},prevAll:function(a){return Ua(a,"previousSibling")},nextUntil:function(a,b,d){return Ua(a,"nextSibling",d)},prevUntil:function(a,b,d){return Ua(a,"previousSibling",d)},siblings:function(a){return Ac((a.parentNode||{}).firstChild,
a)},children:function(a){return Ac(a.firstChild)},contents:function(a){return c.nodeName(a,"iframe")?a.contentDocument||a.contentWindow.document:c.merge([],a.childNodes)}},function(a,b){c.fn[a]=function(d,e){var f=c.map(this,b,d);"Until"!==a.slice(-5)&&(e=d);e&&"string"===typeof e&&(f=c.filter(e,f));1<this.length&&(wd[a]||(f=c.uniqueSort(f)),vd.test(a)&&(f=f.reverse()));return this.pushStack(f)}});var qa=/\S+/g;c.Callbacks=function(a){a="string"===typeof a?Aa(a):c.extend({},a);var b,d,e,f,g=[],k=
[],m=-1,p=function(){f=a.once;for(e=b=!0;k.length;m=-1)for(d=k.shift();++m<g.length;)!1===g[m].apply(d[0],d[1])&&a.stopOnFalse&&(m=g.length,d=!1);a.memory||(d=!1);b=!1;f&&(g=d?[]:"")},q={add:function(){g&&(d&&!b&&(m=g.length-1,k.push(d)),function A(v){c.each(v,function(Q,L){c.isFunction(L)?a.unique&&q.has(L)||g.push(L):L&&L.length&&"string"!==c.type(L)&&A(L)})}(arguments),d&&!b&&p());return this},remove:function(){c.each(arguments,function(t,v){for(var A;-1<(A=c.inArray(v,g,A));)g.splice(A,1),A<=
m&&m--});return this},has:function(t){return t?-1<c.inArray(t,g):0<g.length},empty:function(){g&&(g=[]);return this},disable:function(){f=k=[];g=d="";return this},disabled:function(){return!g},lock:function(){f=!0;d||q.disable();return this},locked:function(){return!!f},fireWith:function(t,v){f||(v=v||[],v=[t,v.slice?v.slice():v],k.push(v),b||p());return this},fire:function(){q.fireWith(this,arguments);return this},fired:function(){return!!e}};return q};c.extend({Deferred:function(a){var b=[["resolve",
"done",c.Callbacks("once memory"),"resolved"],["reject","fail",c.Callbacks("once memory"),"rejected"],["notify","progress",c.Callbacks("memory")]],d="pending",e={state:function(){return d},always:function(){f.done(arguments).fail(arguments);return this},then:function(){var g=arguments;return c.Deferred(function(k){c.each(b,function(m,p){var q=c.isFunction(g[m])&&g[m];f[p[1]](function(){var t=q&&q.apply(this,arguments);if(t&&c.isFunction(t.promise))t.promise().progress(k.notify).done(k.resolve).fail(k.reject);
else k[p[0]+"With"](this===e?k.promise():this,q?[t]:arguments)})});g=null}).promise()},promise:function(g){return null!=g?c.extend(g,e):e}},f={};e.pipe=e.then;c.each(b,function(g,k){var m=k[2],p=k[3];e[k[1]]=m.add;p&&m.add(function(){d=p},b[g^1][2].disable,b[2][2].lock);f[k[0]]=function(){f[k[0]+"With"](this===f?e:this,arguments);return this};f[k[0]+"With"]=m.fireWith});e.promise(f);a&&a.call(f,f);return f},when:function(a){var b=0,d=Ca.call(arguments),e=d.length,f=1!==e||a&&c.isFunction(a.promise)?
e:0,g=1===f?a:c.Deferred(),k=function(t,v,A){return function(Q){v[t]=this;A[t]=1<arguments.length?Ca.call(arguments):Q;A===p?g.notifyWith(v,A):--f||g.resolveWith(v,A)}},m;if(1<e){var p=Array(e);var q=Array(e);for(m=Array(e);b<e;b++)d[b]&&c.isFunction(d[b].promise)?d[b].promise().progress(k(b,q,p)).done(k(b,m,d)).fail(g.reject):--f}f||g.resolveWith(m,d);return g.promise()}});var lb;c.fn.ready=function(a){c.ready.promise().done(a);return this};c.extend({isReady:!1,readyWait:1,holdReady:function(a){a?
c.readyWait++:c.ready(!0)},ready:function(a){(!0===a?--c.readyWait:c.isReady)||(c.isReady=!0,!0!==a&&0<--c.readyWait||(lb.resolveWith(C,[c]),c.fn.triggerHandler&&(c(C).triggerHandler("ready"),c(C).off("ready"))))}});c.ready.promise=function(a){if(!lb)if(lb=c.Deferred(),"complete"===C.readyState||"loading"!==C.readyState&&!C.documentElement.doScroll)B.setTimeout(c.ready);else if(C.addEventListener)C.addEventListener("DOMContentLoaded",Ba),B.addEventListener("load",Ba);else{C.attachEvent("onreadystatechange",
Ba);B.attachEvent("onload",Ba);var b=!1;try{b=null==B.frameElement&&C.documentElement}catch(d){}b&&b.doScroll&&function e(){if(!c.isReady){try{b.doScroll("left")}catch(f){return B.setTimeout(e,50)}Qb();c.ready()}}()}return lb.promise(a)};c.ready.promise();for(var xd in c(y))break;y.ownFirst="0"===xd;y.inlineBlockNeedsLayout=!1;c(function(){var a;if((a=C.getElementsByTagName("body")[0])&&a.style){var b=C.createElement("div");var d=C.createElement("div");d.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px";
a.appendChild(d).appendChild(b);"undefined"!==typeof b.style.zoom&&(b.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",y.inlineBlockNeedsLayout=b=3===b.offsetWidth)&&(a.style.zoom=1);a.removeChild(d)}});(function(){var a=C.createElement("div");y.deleteExpando=!0;try{delete a.test}catch(b){y.deleteExpando=!1}})();var Xa=function(a){var b=c.noData[(a.nodeName+" ").toLowerCase()],d=+a.nodeType||1;return 1!==d&&9!==d?!1:!b||!0!==b&&a.getAttribute("classid")===b},Tc=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,
Sc=/([A-Z])/g;c.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"},hasData:function(a){a=a.nodeType?c.cache[a[c.expando]]:a[c.expando];return!!a&&!qb(a)},data:function(a,b,d){return Sb(a,b,d)},removeData:function(a,b){return Tb(a,b)},_data:function(a,b,d){return Sb(a,b,d,!0)},_removeData:function(a,b){return Tb(a,b,!0)}});c.fn.extend({data:function(a,b){var d,e=this[0],f=e&&e.attributes;if(void 0===a){if(this.length){var g=c.data(e);if(1===e.nodeType&&
!c._data(e,"parsedAttrs")){for(d=f.length;d--;)if(f[d]){var k=f[d].name;0===k.indexOf("data-")&&(k=c.camelCase(k.slice(5)),Rb(e,k,g[k]))}c._data(e,"parsedAttrs",!0)}}return g}return"object"===typeof a?this.each(function(){c.data(this,a)}):1<arguments.length?this.each(function(){c.data(this,a,b)}):e?Rb(e,a,c.data(e,a)):void 0},removeData:function(a){return this.each(function(){c.removeData(this,a)})}});c.extend({queue:function(a,b,d){if(a){b=(b||"fx")+"queue";var e=c._data(a,b);d&&(!e||c.isArray(d)?
e=c._data(a,b,c.makeArray(d)):e.push(d));return e||[]}},dequeue:function(a,b){b=b||"fx";var d=c.queue(a,b),e=d.length,f=d.shift(),g=c._queueHooks(a,b),k=function(){c.dequeue(a,b)};"inprogress"===f&&(f=d.shift(),e--);f&&("fx"===b&&d.unshift("inprogress"),delete g.stop,f.call(a,k,g));!e&&g&&g.empty.fire()},_queueHooks:function(a,b){var d=b+"queueHooks";return c._data(a,d)||c._data(a,d,{empty:c.Callbacks("once memory").add(function(){c._removeData(a,b+"queue");c._removeData(a,d)})})}});c.fn.extend({queue:function(a,
b){var d=2;"string"!==typeof a&&(b=a,a="fx",d--);return arguments.length<d?c.queue(this[0],a):void 0===b?this:this.each(function(){var e=c.queue(this,a,b);c._queueHooks(this,a);"fx"===a&&"inprogress"!==e[0]&&c.dequeue(this,a)})},dequeue:function(a){return this.each(function(){c.dequeue(this,a)})},clearQueue:function(a){return this.queue(a||"fx",[])},promise:function(a,b){var d,e=1,f=c.Deferred(),g=this,k=this.length,m=function(){--e||f.resolveWith(g,[g])};"string"!==typeof a&&(b=a,a=void 0);for(a=
a||"fx";k--;)(d=c._data(g[k],a+"queueHooks"))&&d.empty&&(e++,d.empty.add(m));m();return f.promise(b)}});(function(){var a;y.shrinkWrapBlocks=function(){if(null!=a)return a;a=!1;var b;if((b=C.getElementsByTagName("body")[0])&&b.style){var d=C.createElement("div");var e=C.createElement("div");e.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px";b.appendChild(e).appendChild(d);"undefined"!==typeof d.style.zoom&&(d.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:1px;width:1px;zoom:1",
d.appendChild(C.createElement("div")).style.width="5px",a=3!==d.offsetWidth);b.removeChild(e);return a}}})();var Fb=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,rb=new RegExp("^(?:([+-])\x3d|)("+Fb+")([a-z%]*)$","i"),Ga=["Top","Right","Bottom","Left"],Za=function(a,b){a=b||a;return"none"===c.css(a,"display")||!c.contains(a.ownerDocument,a)},Ea=function(a,b,d,e,f,g,k){var m=0,p=a.length,q=null==d;if("object"===c.type(d))for(m in f=!0,d)Ea(a,b,m,d[m],!0,g,k);else if(void 0!==e&&(f=!0,c.isFunction(e)||
(k=!0),q&&(k?(b.call(a,e),b=null):(q=b,b=function(t,v,A){return q.call(c(t),A)})),b))for(;m<p;m++)b(a[m],d,k?e:e.call(a[m],m,b(a[m],d)));return f?a:q?b.call(a):p?b(a[0],d):g},tb=/^(?:checkbox|radio)$/i,Xb=/<([\w:-]+)/,Zb=/^$|\/(?:java|ecma)script/i,ub=/^\s+/;(function(){var a=C.createElement("div"),b=C.createDocumentFragment(),d=C.createElement("input");a.innerHTML="  \x3clink/\x3e\x3ctable\x3e\x3c/table\x3e\x3ca href\x3d'/a'\x3ea\x3c/a\x3e\x3cinput type\x3d'checkbox'/\x3e";y.leadingWhitespace=3===
a.firstChild.nodeType;y.tbody=!a.getElementsByTagName("tbody").length;y.htmlSerialize=!!a.getElementsByTagName("link").length;y.html5Clone="\x3c:nav\x3e\x3c/:nav\x3e"!==C.createElement("nav").cloneNode(!0).outerHTML;d.type="checkbox";d.checked=!0;b.appendChild(d);y.appendChecked=d.checked;a.innerHTML="\x3ctextarea\x3ex\x3c/textarea\x3e";y.noCloneChecked=!!a.cloneNode(!0).lastChild.defaultValue;a.innerHTML="\x3coption\x3e\x3c/option\x3e";y.option=!!a.lastChild;b.appendChild(a);d=C.createElement("input");
d.setAttribute("type","radio");d.setAttribute("checked","checked");d.setAttribute("name","t");a.appendChild(d);y.checkClone=a.cloneNode(!0).cloneNode(!0).lastChild.checked;y.noCloneEvent=!!a.addEventListener;a[c.expando]=1;y.attributes=!a.getAttribute(c.expando)})();var ka={legend:[1,"\x3cfieldset\x3e","\x3c/fieldset\x3e"],area:[1,"\x3cmap\x3e","\x3c/map\x3e"],param:[1,"\x3cobject\x3e","\x3c/object\x3e"],thead:[1,"\x3ctable\x3e","\x3c/table\x3e"],tr:[2,"\x3ctable\x3e\x3ctbody\x3e","\x3c/tbody\x3e\x3c/table\x3e"],
col:[2,"\x3ctable\x3e\x3ctbody\x3e\x3c/tbody\x3e\x3ccolgroup\x3e","\x3c/colgroup\x3e\x3c/table\x3e"],td:[3,"\x3ctable\x3e\x3ctbody\x3e\x3ctr\x3e","\x3c/tr\x3e\x3c/tbody\x3e\x3c/table\x3e"],_default:y.htmlSerialize?[0,"",""]:[1,"X\x3cdiv\x3e","\x3c/div\x3e"]};ka.tbody=ka.tfoot=ka.colgroup=ka.caption=ka.thead;ka.th=ka.td;y.option||(ka.optgroup=ka.option=[1,"\x3cselect multiple\x3d'multiple'\x3e","\x3c/select\x3e"]);var Vc=/<|&#?\w+;/,Yb=/<tbody/i;(function(){var a,b=C.createElement("div");for(a in{submit:!0,
change:!0,focusin:!0}){var d="on"+a;(y[a]=d in B)||(b.setAttribute(d,"t"),y[a]=!1===b.attributes[d].expando)}})();var Gb=/^(?:input|select|textarea)$/i,yd=/^key/,zd=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Ec=/^(?:focusinfocus|focusoutblur)$/,Fc=/^([^.]*)(?:\.(.+)|)/;c.event={global:{},add:function(a,b,d,e,f){var g,k,m,p,q;if(k=c._data(a)){if(d.handler){var t=d;d=t.handler;f=t.selector}d.guid||(d.guid=c.guid++);(g=k.events)||(g=k.events={});(m=k.handle)||(m=k.handle=function(L){return"undefined"===
typeof c||L&&c.event.triggered===L.type?void 0:c.event.dispatch.apply(m.elem,arguments)},m.elem=a);b=(b||"").match(qa)||[""];for(k=b.length;k--;){var v=Fc.exec(b[k])||[];var A=p=v[1];var Q=(v[2]||"").split(".").sort();A&&(v=c.event.special[A]||{},A=(f?v.delegateType:v.bindType)||A,v=c.event.special[A]||{},p=c.extend({type:A,origType:p,data:e,handler:d,guid:d.guid,selector:f,needsContext:f&&c.expr.match.needsContext.test(f),namespace:Q.join(".")},t),(q=g[A])||(q=g[A]=[],q.delegateCount=0,v.setup&&
!1!==v.setup.call(a,e,Q,m)||(a.addEventListener?a.addEventListener(A,m,!1):a.attachEvent&&a.attachEvent("on"+A,m))),v.add&&(v.add.call(a,p),p.handler.guid||(p.handler.guid=d.guid)),f?q.splice(q.delegateCount++,0,p):q.push(p),c.event.global[A]=!0)}a=null}},remove:function(a,b,d,e,f){var g,k,m,p,q,t=c.hasData(a)&&c._data(a);if(t&&(p=t.events)){b=(b||"").match(qa)||[""];for(m=b.length;m--;){var v=Fc.exec(b[m])||[];var A=q=v[1];var Q=(v[2]||"").split(".").sort();if(A){var L=c.event.special[A]||{};A=(e?
L.delegateType:L.bindType)||A;var ja=p[A]||[];v=v[2]&&new RegExp("(^|\\.)"+Q.join("\\.(?:.*\\.|)")+"(\\.|$)");for(k=g=ja.length;g--;){var ia=ja[g];!f&&q!==ia.origType||d&&d.guid!==ia.guid||v&&!v.test(ia.namespace)||e&&e!==ia.selector&&("**"!==e||!ia.selector)||(ja.splice(g,1),ia.selector&&ja.delegateCount--,L.remove&&L.remove.call(a,ia))}k&&!ja.length&&(L.teardown&&!1!==L.teardown.call(a,Q,t.handle)||c.removeEvent(a,A,t.handle),delete p[A])}else for(A in p)c.event.remove(a,A+b[m],d,e,!0)}c.isEmptyObject(p)&&
(delete t.handle,c._removeData(a,"events"))}},trigger:function(a,b,d,e){var f,g,k=[d||C],m=Qa.call(a,"type")?a.type:a;var p=Qa.call(a,"namespace")?a.namespace.split("."):[];var q=f=d=d||C;if(3!==d.nodeType&&8!==d.nodeType&&!Ec.test(m+c.event.triggered)){-1<m.indexOf(".")&&(p=m.split("."),m=p.shift(),p.sort());var t=0>m.indexOf(":")&&"on"+m;a=a[c.expando]?a:new c.Event(m,"object"===typeof a&&a);a.isTrigger=e?2:3;a.namespace=p.join(".");a.rnamespace=a.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+
"(\\.|$)"):null;a.result=void 0;a.target||(a.target=d);b=null==b?[a]:c.makeArray(b,[a]);p=c.event.special[m]||{};if(e||!p.trigger||!1!==p.trigger.apply(d,b)){if(!e&&!p.noBubble&&!c.isWindow(d)){var v=p.delegateType||m;Ec.test(v+m)||(q=q.parentNode);for(;q;q=q.parentNode)k.push(q),f=q;f===(d.ownerDocument||C)&&k.push(f.defaultView||f.parentWindow||B)}for(g=0;(q=k[g++])&&!a.isPropagationStopped();)a.type=1<g?v:p.bindType||m,(f=(c._data(q,"events")||{})[a.type]&&c._data(q,"handle"))&&f.apply(q,b),(f=
t&&q[t])&&f.apply&&Xa(q)&&(a.result=f.apply(q,b),!1===a.result&&a.preventDefault());a.type=m;if(!(e||a.isDefaultPrevented()||p._default&&!1!==p._default.apply(k.pop(),b))&&Xa(d)&&t&&d[m]&&!c.isWindow(d)){(f=d[t])&&(d[t]=null);c.event.triggered=m;try{d[m]()}catch(A){}c.event.triggered=void 0;f&&(d[t]=f)}return a.result}}},dispatch:function(a){a=c.event.fix(a);var b,d,e,f=Ca.call(arguments);var g=(c._data(this,"events")||{})[a.type]||[];var k=c.event.special[a.type]||{};f[0]=a;a.delegateTarget=this;
if(!k.preDispatch||!1!==k.preDispatch.call(this,a)){var m=c.event.handlers.call(this,a,g);for(g=0;(e=m[g++])&&!a.isPropagationStopped();)for(a.currentTarget=e.elem,b=0;(d=e.handlers[b++])&&!a.isImmediatePropagationStopped();)if(!a.rnamespace||a.rnamespace.test(d.namespace))a.handleObj=d,a.data=d.data,d=((c.event.special[d.origType]||{}).handle||d.handler).apply(e.elem,f),void 0!==d&&!1===(a.result=d)&&(a.preventDefault(),a.stopPropagation());k.postDispatch&&k.postDispatch.call(this,a);return a.result}},
handlers:function(a,b){var d,e=[],f=b.delegateCount,g=a.target;if(f&&g.nodeType&&("click"!==a.type||isNaN(a.button)||1>a.button))for(;g!=this;g=g.parentNode||this)if(1===g.nodeType&&(!0!==g.disabled||"click"!==a.type)){var k=[];for(d=0;d<f;d++){var m=b[d];var p=m.selector+" ";void 0===k[p]&&(k[p]=m.needsContext?-1<c(p,this).index(g):c.find(p,this,null,[g]).length);k[p]&&k.push(m)}k.length&&e.push({elem:g,handlers:k})}f<b.length&&e.push({elem:this,handlers:b.slice(f)});return e},fix:function(a){if(a[c.expando])return a;
var b=a.type;var d=a,e=this.fixHooks[b];e||(this.fixHooks[b]=e=zd.test(b)?this.mouseHooks:yd.test(b)?this.keyHooks:{});var f=e.props?this.props.concat(e.props):this.props;a=new c.Event(d);for(b=f.length;b--;){var g=f[b];a[g]=d[g]}a.target||(a.target=d.srcElement||C);3===a.target.nodeType&&(a.target=a.target.parentNode);a.metaKey=!!a.metaKey;return e.filter?e.filter(a,d):a},props:"altKey bubbles cancelable ctrlKey currentTarget detail eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),
fixHooks:{},keyHooks:{props:["char","charCode","key","keyCode"],filter:function(a,b){null==a.which&&(a.which=null!=b.charCode?b.charCode:b.keyCode);return a}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(a,b){var d=b.button,e=b.fromElement;if(null==a.pageX&&null!=b.clientX){var f=a.target.ownerDocument||C;var g=f.documentElement;f=f.body;a.pageX=b.clientX+(g&&g.scrollLeft||f&&f.scrollLeft||0)-(g&&g.clientLeft||
f&&f.clientLeft||0);a.pageY=b.clientY+(g&&g.scrollTop||f&&f.scrollTop||0)-(g&&g.clientTop||f&&f.clientTop||0)}!a.relatedTarget&&e&&(a.relatedTarget=e===a.target?b.toElement:e);a.which||void 0===d||(a.which=d&1?1:d&2?3:d&4?2:0);return a}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==$b()&&this.focus)try{return this.focus(),!1}catch(a){}},delegateType:"focusin"},blur:{trigger:function(){if(this===$b()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if(c.nodeName(this,
"input")&&"checkbox"===this.type&&this.click)return this.click(),!1},_default:function(a){return c.nodeName(a.target,"a")}},beforeunload:{postDispatch:function(a){void 0!==a.result&&a.originalEvent&&(a.originalEvent.returnValue=a.result)}}},simulate:function(a,b,d){a=c.extend(new c.Event,d,{type:a,isSimulated:!0});c.event.trigger(a,null,b);a.isDefaultPrevented()&&d.preventDefault()}};c.removeEvent=C.removeEventListener?function(a,b,d){a.removeEventListener&&a.removeEventListener(b,d)}:function(a,
b,d){b="on"+b;a.detachEvent&&("undefined"===typeof a[b]&&(a[b]=null),a.detachEvent(b,d))};c.Event=function(a,b){if(!(this instanceof c.Event))return new c.Event(a,b);a&&a.type?(this.originalEvent=a,this.type=a.type,this.isDefaultPrevented=a.defaultPrevented||void 0===a.defaultPrevented&&!1===a.returnValue?bb:Na):this.type=a;b&&c.extend(this,b);this.timeStamp=a&&a.timeStamp||c.now();this[c.expando]=!0};c.Event.prototype={constructor:c.Event,isDefaultPrevented:Na,isPropagationStopped:Na,isImmediatePropagationStopped:Na,
preventDefault:function(){var a=this.originalEvent;this.isDefaultPrevented=bb;a&&(a.preventDefault?a.preventDefault():a.returnValue=!1)},stopPropagation:function(){var a=this.originalEvent;this.isPropagationStopped=bb;a&&!this.isSimulated&&(a.stopPropagation&&a.stopPropagation(),a.cancelBubble=!0)},stopImmediatePropagation:function(){var a=this.originalEvent;this.isImmediatePropagationStopped=bb;a&&a.stopImmediatePropagation&&a.stopImmediatePropagation();this.stopPropagation()}};c.each({mouseenter:"mouseover",
mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(a,b){c.event.special[a]={delegateType:b,bindType:b,handle:function(d){var e=d.relatedTarget,f=d.handleObj;if(!e||e!==this&&!c.contains(this,e)){d.type=f.origType;var g=f.handler.apply(this,arguments);d.type=b}return g}}});y.submit||(c.event.special.submit={setup:function(){if(c.nodeName(this,"form"))return!1;c.event.add(this,"click._submit keypress._submit",function(a){a=a.target;(a=c.nodeName(a,"input")||c.nodeName(a,
"button")?c.prop(a,"form"):void 0)&&!c._data(a,"submit")&&(c.event.add(a,"submit._submit",function(b){b._submitBubble=!0}),c._data(a,"submit",!0))})},postDispatch:function(a){a._submitBubble&&(delete a._submitBubble,this.parentNode&&!a.isTrigger&&c.event.simulate("submit",this.parentNode,a))},teardown:function(){if(c.nodeName(this,"form"))return!1;c.event.remove(this,"._submit")}});y.change||(c.event.special.change={setup:function(){if(Gb.test(this.nodeName)){if("checkbox"===this.type||"radio"===
this.type)c.event.add(this,"propertychange._change",function(a){"checked"===a.originalEvent.propertyName&&(this._justChanged=!0)}),c.event.add(this,"click._change",function(a){this._justChanged&&!a.isTrigger&&(this._justChanged=!1);c.event.simulate("change",this,a)});return!1}c.event.add(this,"beforeactivate._change",function(a){a=a.target;Gb.test(a.nodeName)&&!c._data(a,"change")&&(c.event.add(a,"change._change",function(b){!this.parentNode||b.isSimulated||b.isTrigger||c.event.simulate("change",
this.parentNode,b)}),c._data(a,"change",!0))})},handle:function(a){var b=a.target;if(this!==b||a.isSimulated||a.isTrigger||"radio"!==b.type&&"checkbox"!==b.type)return a.handleObj.handler.apply(this,arguments)},teardown:function(){c.event.remove(this,"._change");return!Gb.test(this.nodeName)}});y.focusin||c.each({focus:"focusin",blur:"focusout"},function(a,b){var d=function(e){c.event.simulate(b,e.target,c.event.fix(e))};c.event.special[b]={setup:function(){var e=this.ownerDocument||this,f=c._data(e,
b);f||e.addEventListener(a,d,!0);c._data(e,b,(f||0)+1)},teardown:function(){var e=this.ownerDocument||this,f=c._data(e,b)-1;f?c._data(e,b,f):(e.removeEventListener(a,d,!0),c._removeData(e,b))}}});c.fn.extend({on:function(a,b,d,e){return vb(this,a,b,d,e)},one:function(a,b,d,e){return vb(this,a,b,d,e,1)},off:function(a,b,d){if(a&&a.preventDefault&&a.handleObj){var e=a.handleObj;c(a.delegateTarget).off(e.namespace?e.origType+"."+e.namespace:e.origType,e.selector,e.handler);return this}if("object"===
typeof a){for(e in a)this.off(e,b,a[e]);return this}if(!1===b||"function"===typeof b)d=b,b=void 0;!1===d&&(d=Na);return this.each(function(){c.event.remove(this,a,d,b)})},trigger:function(a,b){return this.each(function(){c.event.trigger(a,b,this)})},triggerHandler:function(a,b){var d=this[0];if(d)return c.event.trigger(a,b,d,!0)}});var Ad=/ jQuery\d+="(?:null|\d+)"/g,Gc=/<(?:abbr|article|aside|audio|bdi|canvas|data|datalist|details|dialog|figcaption|figure|footer|header|hgroup|main|mark|meter|nav|output|picture|progress|section|summary|template|time|video)[\s/>]/i,
Bd=/<script|<style|<link/i,Xc=/checked\s*(?:[^=]|=\s*.checked.)/i,Wc=/^true\/(.*)/,Yc=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,Hb=Vb(C).appendChild(C.createElement("div"));c.extend({htmlPrefilter:function(a){return a},clone:function(a,b,d){var e,f,g=c.contains(a.ownerDocument,a);if(y.html5Clone||c.isXMLDoc(a)||!Gc.test("\x3c"+a.nodeName+"\x3e"))var k=a.cloneNode(!0);else Hb.innerHTML=a.outerHTML,Hb.removeChild(k=Hb.firstChild);if(!(y.noCloneEvent&&y.noCloneChecked||1!==a.nodeType&&11!==a.nodeType||
c.isXMLDoc(a))){var m=ea(k);var p=ea(a);for(f=0;null!=(e=p[f]);++f)if(m[f]){var q=void 0,t=e,v=m[f];if(1===v.nodeType){var A=v.nodeName.toLowerCase();if(!y.noCloneEvent&&v[c.expando]){e=c._data(v);for(q in e.events)c.removeEvent(v,q,e.handle);v.removeAttribute(c.expando)}if("script"===A&&v.text!==t.text)bc(v).text=t.text,cc(v);else if("object"===A)v.parentNode&&(v.outerHTML=t.outerHTML),y.html5Clone&&t.innerHTML&&!c.trim(v.innerHTML)&&(v.innerHTML=t.innerHTML);else if("input"===A&&tb.test(t.type))v.defaultChecked=
v.checked=t.checked,v.value!==t.value&&(v.value=t.value);else if("option"===A)v.defaultSelected=v.selected=t.defaultSelected;else if("input"===A||"textarea"===A)v.defaultValue=t.defaultValue}}}if(b)if(d)for(p=p||ea(a),m=m||ea(k),f=0;null!=(e=p[f]);f++)dc(e,m[f]);else dc(a,k);m=ea(k,"script");0<m.length&&sb(m,!g&&ea(a,"script"));return k},cleanData:function(a,b){for(var d,e,f,g,k=0,m=c.expando,p=c.cache,q=y.attributes,t=c.event.special;null!=(d=a[k]);k++)if(b||Xa(d))if(g=(f=d[m])&&p[f]){if(g.events)for(e in g.events)t[e]?
c.event.remove(d,e):c.removeEvent(d,e,g.handle);p[f]&&(delete p[f],q||"undefined"===typeof d.removeAttribute?d[m]=void 0:d.removeAttribute(m),ta.push(f))}}});c.fn.extend({domManip:Fa,detach:function(a){return fc(this,a,!0)},remove:function(a){return fc(this,a)},text:function(a){return Ea(this,function(b){return void 0===b?c.text(this):this.empty().append((this[0]&&this[0].ownerDocument||C).createTextNode(b))},null,a,arguments.length)},append:function(){return Fa(this,arguments,function(a){1!==this.nodeType&&
11!==this.nodeType&&9!==this.nodeType||ac(this,a).appendChild(a)})},prepend:function(){return Fa(this,arguments,function(a){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var b=ac(this,a);b.insertBefore(a,b.firstChild)}})},before:function(){return Fa(this,arguments,function(a){this.parentNode&&this.parentNode.insertBefore(a,this)})},after:function(){return Fa(this,arguments,function(a){this.parentNode&&this.parentNode.insertBefore(a,this.nextSibling)})},empty:function(){for(var a,b=
0;null!=(a=this[b]);b++){for(1===a.nodeType&&c.cleanData(ea(a,!1));a.firstChild;)a.removeChild(a.firstChild);a.options&&c.nodeName(a,"select")&&(a.options.length=0)}return this},clone:function(a,b){a=null==a?!1:a;b=null==b?a:b;return this.map(function(){return c.clone(this,a,b)})},html:function(a){return Ea(this,function(b){var d=this[0]||{},e=0,f=this.length;if(void 0===b)return 1===d.nodeType?d.innerHTML.replace(Ad,""):void 0;if(!("string"!==typeof b||Bd.test(b)||!y.htmlSerialize&&Gc.test(b)||!y.leadingWhitespace&&
ub.test(b)||ka[(Xb.exec(b)||["",""])[1].toLowerCase()])){b=c.htmlPrefilter(b);try{for(;e<f;e++)d=this[e]||{},1===d.nodeType&&(c.cleanData(ea(d,!1)),d.innerHTML=b);d=0}catch(g){}}d&&this.empty().append(b)},null,a,arguments.length)},replaceWith:function(){var a=[];return Fa(this,arguments,function(b){var d=this.parentNode;0>c.inArray(this,a)&&(c.cleanData(ea(this)),d&&d.replaceChild(b,this))},a)}});c.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},
function(a,b){c.fn[a]=function(d){for(var e=0,f=[],g=c(d),k=g.length-1;e<=k;e++)d=e===k?this:this.clone(!0),c(g[e])[b](d),Bb.apply(f,d.get());return this.pushStack(f)}});var Ya,hc={HTML:"block",BODY:"block"},Hc=/^margin/,db=new RegExp("^("+Fb+")(?!px)[a-z%]+$","i"),Ib=function(a,b,d,e){var f,g={};for(f in b)g[f]=a.style[f],a.style[f]=b[f];d=d.apply(a,e||[]);for(f in b)a.style[f]=g[f];return d},Ic=C.documentElement;(function(){function a(){var q=C.documentElement;q.appendChild(m);p.style.cssText="-webkit-box-sizing:border-box;box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%";
b=e=k=!1;d=g=!0;if(B.getComputedStyle){var t=B.getComputedStyle(p);b="1%"!==(t||{}).top;k="2px"===(t||{}).marginLeft;e="4px"===(t||{width:"4px"}).width;p.style.marginRight="50%";d="4px"===(t||{marginRight:"4px"}).marginRight;t=p.appendChild(C.createElement("div"));t.style.cssText=p.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0";t.style.marginRight=t.style.width="0";p.style.width="1px";g=!parseFloat((B.getComputedStyle(t)||
{}).marginRight);p.removeChild(t)}p.style.display="none";if(f=0===p.getClientRects().length)if(p.style.display="",p.innerHTML="\x3ctable\x3e\x3ctr\x3e\x3ctd\x3e\x3c/td\x3e\x3ctd\x3et\x3c/td\x3e\x3c/tr\x3e\x3c/table\x3e",p.childNodes[0].style.borderCollapse="separate",t=p.getElementsByTagName("td"),t[0].style.cssText="margin:0;border:0;padding:0;display:none",f=0===t[0].offsetHeight)t[0].style.display="",t[1].style.display="none",f=0===t[0].offsetHeight;q.removeChild(m)}var b,d,e,f,g,k,m=C.createElement("div"),
p=C.createElement("div");p.style&&(p.style.cssText="float:left;opacity:.5",y.opacity="0.5"===p.style.opacity,y.cssFloat=!!p.style.cssFloat,p.style.backgroundClip="content-box",p.cloneNode(!0).style.backgroundClip="",y.clearCloneStyle="content-box"===p.style.backgroundClip,m=C.createElement("div"),m.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",p.innerHTML="",m.appendChild(p),y.boxSizing=""===p.style.boxSizing||""===p.style.MozBoxSizing||
""===p.style.WebkitBoxSizing,c.extend(y,{reliableHiddenOffsets:function(){null==b&&a();return f},boxSizingReliable:function(){null==b&&a();return e},pixelMarginRight:function(){null==b&&a();return d},pixelPosition:function(){null==b&&a();return b},reliableMarginRight:function(){null==b&&a();return g},reliableMarginLeft:function(){null==b&&a();return k}}))})();var Cd=/^(top|right|bottom|left)$/;if(B.getComputedStyle){var Oa=function(a){var b=a.ownerDocument.defaultView;b&&b.opener||(b=B);return b.getComputedStyle(a)};
var Ha=function(a,b,d){var e=a.style;var f=(d=d||Oa(a))?d.getPropertyValue(b)||d[b]:void 0;""!==f&&void 0!==f||c.contains(a.ownerDocument,a)||(f=c.style(a,b));if(d&&!y.pixelMarginRight()&&db.test(f)&&Hc.test(b)){a=e.width;b=e.minWidth;var g=e.maxWidth;e.minWidth=e.maxWidth=e.width=f;f=d.width;e.width=a;e.minWidth=b;e.maxWidth=g}return void 0===f?f:f+""}}else Ic.currentStyle&&(Oa=function(a){return a.currentStyle},Ha=function(a,b,d){var e,f,g=a.style;var k=(d=d||Oa(a))?d[b]:void 0;null==k&&g&&g[b]&&
(k=g[b]);if(db.test(k)&&!Cd.test(b)){d=g.left;if(f=(e=a.runtimeStyle)&&e.left)e.left=a.currentStyle.left;g.left="fontSize"===b?"1em":k;k=g.pixelLeft+"px";g.left=d;f&&(e.left=f)}return void 0===k?k:k+""||"auto"});var Jb=/alpha\([^)]*\)/i,Dd=/opacity\s*=\s*([^)]*)/i,Ed=/^(none|table(?!-c[ea]).+)/,Zc=new RegExp("^("+Fb+")(.*)$","i"),Fd={position:"absolute",visibility:"hidden",display:"block"},Jc={letterSpacing:"0",fontWeight:"400"},kc=["Webkit","O","Moz","ms"],jc=C.createElement("div").style;c.extend({cssHooks:{opacity:{get:function(a,
b){if(b)return a=Ha(a,"opacity"),""===a?"1":a}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{"float":y.cssFloat?"cssFloat":"styleFloat"},style:function(a,b,d,e){if(a&&3!==a.nodeType&&8!==a.nodeType&&a.style){var f,g=c.camelCase(b),k=a.style;b=c.cssProps[g]||(c.cssProps[g]=ic(g)||g);var m=c.cssHooks[b]||c.cssHooks[g];if(void 0!==d){var p=typeof d;"string"===
p&&(f=rb.exec(d))&&f[1]&&(d=Ub(a,b,f),p="number");if(null!=d&&d===d&&("number"===p&&(d+=f&&f[3]||(c.cssNumber[g]?"":"px")),y.clearCloneStyle||""!==d||0!==b.indexOf("background")||(k[b]="inherit"),!(m&&"set"in m)||void 0!==(d=m.set(a,d,e))))try{k[b]=d}catch(q){}}else return m&&"get"in m&&void 0!==(f=m.get(a,!1,e))?f:k[b]}},css:function(a,b,d,e){var f;var g=c.camelCase(b);b=c.cssProps[g]||(c.cssProps[g]=ic(g)||g);(g=c.cssHooks[b]||c.cssHooks[g])&&"get"in g&&(f=g.get(a,!0,d));void 0===f&&(f=Ha(a,b,e));
"normal"===f&&b in Jc&&(f=Jc[b]);return""===d||d?(a=parseFloat(f),!0===d||isFinite(a)?a||0:f):f}});c.each(["height","width"],function(a,b){c.cssHooks[b]={get:function(d,e,f){if(e)return Ed.test(c.css(d,"display"))&&0===d.offsetWidth?Ib(d,Fd,function(){return oc(d,b,f)}):oc(d,b,f)},set:function(d,e,f){var g=f&&Oa(d);return mc(d,e,f?nc(d,b,f,y.boxSizing&&"border-box"===c.css(d,"boxSizing",!1,g),g):0)}}});y.opacity||(c.cssHooks.opacity={get:function(a,b){return Dd.test((b&&a.currentStyle?a.currentStyle.filter:
a.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":b?"1":""},set:function(a,b){var d=a.style;a=a.currentStyle;var e=c.isNumeric(b)?"alpha(opacity\x3d"+100*b+")":"",f=a&&a.filter||d.filter||"";d.zoom=1;if((1<=b||""===b)&&""===c.trim(f.replace(Jb,""))&&d.removeAttribute&&(d.removeAttribute("filter"),""===b||a&&!a.filter))return;d.filter=Jb.test(f)?f.replace(Jb,e):f+" "+e}});c.cssHooks.marginRight=wb(y.reliableMarginRight,function(a,b){if(b)return Ib(a,{display:"inline-block"},Ha,[a,"marginRight"])});
c.cssHooks.marginLeft=wb(y.reliableMarginLeft,function(a,b){if(b)return(parseFloat(Ha(a,"marginLeft"))||(c.contains(a.ownerDocument,a)?a.getBoundingClientRect().left-Ib(a,{marginLeft:0},function(){return a.getBoundingClientRect().left}):0))+"px"});c.each({margin:"",padding:"",border:"Width"},function(a,b){c.cssHooks[a+b]={expand:function(d){var e=0,f={};for(d="string"===typeof d?d.split(" "):[d];4>e;e++)f[a+Ga[e]+b]=d[e]||d[e-2]||d[0];return f}};Hc.test(a)||(c.cssHooks[a+b].set=mc)});c.fn.extend({css:function(a,
b){return Ea(this,function(d,e,f){var g,k={},m=0;if(c.isArray(e)){f=Oa(d);for(g=e.length;m<g;m++)k[e[m]]=c.css(d,e[m],!1,f);return k}return void 0!==f?c.style(d,e,f):c.css(d,e)},a,b,1<arguments.length)},show:function(){return lc(this,!0)},hide:function(){return lc(this)},toggle:function(a){return"boolean"===typeof a?a?this.show():this.hide():this.each(function(){Za(this)?c(this).show():c(this).hide()})}});c.Tween=ha;ha.prototype={constructor:ha,init:function(a,b,d,e,f,g){this.elem=a;this.prop=d;this.easing=
f||c.easing._default;this.options=b;this.start=this.now=this.cur();this.end=e;this.unit=g||(c.cssNumber[d]?"":"px")},cur:function(){var a=ha.propHooks[this.prop];return a&&a.get?a.get(this):ha.propHooks._default.get(this)},run:function(a){var b,d=ha.propHooks[this.prop];this.pos=this.options.duration?b=c.easing[this.easing](a,this.options.duration*a,0,1,this.options.duration):b=a;this.now=(this.end-this.start)*b+this.start;this.options.step&&this.options.step.call(this.elem,this.now,this);d&&d.set?
d.set(this):ha.propHooks._default.set(this);return this}};ha.prototype.init.prototype=ha.prototype;ha.propHooks={_default:{get:function(a){return 1!==a.elem.nodeType||null!=a.elem[a.prop]&&null==a.elem.style[a.prop]?a.elem[a.prop]:(a=c.css(a.elem,a.prop,""))&&"auto"!==a?a:0},set:function(a){if(c.fx.step[a.prop])c.fx.step[a.prop](a);else 1!==a.elem.nodeType||null==a.elem.style[c.cssProps[a.prop]]&&!c.cssHooks[a.prop]?a.elem[a.prop]=a.now:c.style(a.elem,a.prop,a.now+a.unit)}}};ha.propHooks.scrollTop=
ha.propHooks.scrollLeft={set:function(a){a.elem.nodeType&&a.elem.parentNode&&(a.elem[a.prop]=a.now)}};c.easing={linear:function(a){return a},swing:function(a){return.5-Math.cos(a*Math.PI)/2},_default:"swing"};c.fx=ha.prototype.init;c.fx.step={};var Pa,mb,Gd=/^(?:toggle|show|hide)$/,Hd=/queueHooks$/;c.Animation=c.extend(ma,{tweeners:{"*":[function(a,b){var d=this.createTween(a,b);Ub(d.elem,a,rb.exec(b),d);return d}]},tweener:function(a,b){c.isFunction(a)?(b=a,a=["*"]):a=a.match(qa);for(var d,e=0,f=
a.length;e<f;e++)d=a[e],ma.tweeners[d]=ma.tweeners[d]||[],ma.tweeners[d].unshift(b)},prefilters:[function(a,b,d){var e,f=this,g={},k=a.style,m=a.nodeType&&Za(a),p=c._data(a,"fxshow");if(!d.queue){var q=c._queueHooks(a,"fx");if(null==q.unqueued){q.unqueued=0;var t=q.empty.fire;q.empty.fire=function(){q.unqueued||t()}}q.unqueued++;f.always(function(){f.always(function(){q.unqueued--;c.queue(a,"fx").length||q.empty.fire()})})}if(1===a.nodeType&&("height"in b||"width"in b)){d.overflow=[k.overflow,k.overflowX,
k.overflowY];var v=c.css(a,"display");var A="none"===v?c._data(a,"olddisplay")||cb(a.nodeName):v;"inline"===A&&"none"===c.css(a,"float")&&(y.inlineBlockNeedsLayout&&"inline"!==cb(a.nodeName)?k.zoom=1:k.display="inline-block")}d.overflow&&(k.overflow="hidden",y.shrinkWrapBlocks()||f.always(function(){k.overflow=d.overflow[0];k.overflowX=d.overflow[1];k.overflowY=d.overflow[2]}));for(e in b)if(A=b[e],Gd.exec(A)){delete b[e];var Q=Q||"toggle"===A;if(A===(m?"hide":"show"))if("show"===A&&p&&void 0!==p[e])m=
!0;else continue;g[e]=p&&p[e]||c.style(a,e)}else v=void 0;if(c.isEmptyObject(g))"inline"===("none"===v?cb(a.nodeName):v)&&(k.display=v);else for(e in p?"hidden"in p&&(m=p.hidden):p=c._data(a,"fxshow",{}),Q&&(p.hidden=!m),m?c(a).show():f.done(function(){c(a).hide()}),f.done(function(){var L;c._removeData(a,"fxshow");for(L in g)c.style(a,L,g[L])}),g)b=qc(m?p[e]:0,e,f),e in p||(p[e]=b.start,m&&(b.end=b.start,b.start="width"===e||"height"===e?1:0))}],prefilter:function(a,b){b?ma.prefilters.unshift(a):
ma.prefilters.push(a)}});c.speed=function(a,b,d){var e=a&&"object"===typeof a?c.extend({},a):{complete:d||!d&&b||c.isFunction(a)&&a,duration:a,easing:d&&b||b&&!c.isFunction(b)&&b};e.duration=c.fx.off?0:"number"===typeof e.duration?e.duration:e.duration in c.fx.speeds?c.fx.speeds[e.duration]:c.fx.speeds._default;if(null==e.queue||!0===e.queue)e.queue="fx";e.old=e.complete;e.complete=function(){c.isFunction(e.old)&&e.old.call(this);e.queue&&c.dequeue(this,e.queue)};return e};c.fn.extend({fadeTo:function(a,
b,d,e){return this.filter(Za).css("opacity",0).show().end().animate({opacity:b},a,d,e)},animate:function(a,b,d,e){var f=c.isEmptyObject(a),g=c.speed(b,d,e);b=function(){var k=ma(this,c.extend({},a),g);(f||c._data(this,"finish"))&&k.stop(!0)};b.finish=b;return f||!1===g.queue?this.each(b):this.queue(g.queue,b)},stop:function(a,b,d){var e=function(f){var g=f.stop;delete f.stop;g(d)};"string"!==typeof a&&(d=b,b=a,a=void 0);b&&!1!==a&&this.queue(a||"fx",[]);return this.each(function(){var f=!0,g=null!=
a&&a+"queueHooks",k=c.timers,m=c._data(this);if(g)m[g]&&m[g].stop&&e(m[g]);else for(g in m)m[g]&&m[g].stop&&Hd.test(g)&&e(m[g]);for(g=k.length;g--;)k[g].elem!==this||null!=a&&k[g].queue!==a||(k[g].anim.stop(d),f=!1,k.splice(g,1));!f&&d||c.dequeue(this,a)})},finish:function(a){!1!==a&&(a=a||"fx");return this.each(function(){var b=c._data(this),d=b[a+"queue"];var e=b[a+"queueHooks"];var f=c.timers,g=d?d.length:0;b.finish=!0;c.queue(this,a,[]);e&&e.stop&&e.stop.call(this,!0);for(e=f.length;e--;)f[e].elem===
this&&f[e].queue===a&&(f[e].anim.stop(!0),f.splice(e,1));for(e=0;e<g;e++)d[e]&&d[e].finish&&d[e].finish.call(this);delete b.finish})}});c.each(["toggle","show","hide"],function(a,b){var d=c.fn[b];c.fn[b]=function(e,f,g){return null==e||"boolean"===typeof e?d.apply(this,arguments):this.animate(eb(b,!0),e,f,g)}});c.each({slideDown:eb("show"),slideUp:eb("hide"),slideToggle:eb("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(a,b){c.fn[a]=function(d,e,
f){return this.animate(b,d,e,f)}});c.timers=[];c.fx.tick=function(){var a=c.timers,b=0;for(Pa=c.now();b<a.length;b++){var d=a[b];d()||a[b]!==d||a.splice(b--,1)}a.length||c.fx.stop();Pa=void 0};c.fx.timer=function(a){c.timers.push(a);a()?c.fx.start():c.timers.pop()};c.fx.interval=13;c.fx.start=function(){mb||(mb=B.setInterval(c.fx.tick,c.fx.interval))};c.fx.stop=function(){B.clearInterval(mb);mb=null};c.fx.speeds={slow:600,fast:200,_default:400};c.fn.delay=function(a,b){a=c.fx?c.fx.speeds[a]||a:a;
return this.queue(b||"fx",function(d,e){var f=B.setTimeout(d,a);e.stop=function(){B.clearTimeout(f)}})};(function(){var a=C.createElement("input");C.createElement("div");var b=C.createElement("select"),d=b.appendChild(C.createElement("option"));var e=C.createElement("div");e.setAttribute("className","t");e.innerHTML="  \x3clink/\x3e\x3ctable\x3e\x3c/table\x3e\x3ca href\x3d'/a'\x3ea\x3c/a\x3e\x3cinput type\x3d'checkbox'/\x3e";e.getElementsByTagName("a");a.setAttribute("type","checkbox");e.appendChild(a);
var f=e.getElementsByTagName("a")[0];f.style.cssText="top:1px";y.getSetAttribute="t"!==e.className;y.style=/top/.test(f.getAttribute("style"));y.hrefNormalized="/a"===f.getAttribute("href");y.checkOn=!!a.value;y.optSelected=d.selected;y.enctype=!!C.createElement("form").enctype;b.disabled=!0;y.optDisabled=!d.disabled;a=C.createElement("input");a.setAttribute("value","");y.input=""===a.getAttribute("value");a.value="t";a.setAttribute("type","radio");y.radioValue="t"===a.value})();var Id=/\r/g,Jd=/[\x20\t\r\n\f]+/g;
c.fn.extend({val:function(a){var b,d,e=this[0];if(arguments.length){var f=c.isFunction(a);return this.each(function(g){1===this.nodeType&&(g=f?a.call(this,g,c(this).val()):a,null==g?g="":"number"===typeof g?g+="":c.isArray(g)&&(g=c.map(g,function(k){return null==k?"":k+""})),b=c.valHooks[this.type]||c.valHooks[this.nodeName.toLowerCase()],b&&"set"in b&&void 0!==b.set(this,g,"value")||(this.value=g))})}if(e){if((b=c.valHooks[e.type]||c.valHooks[e.nodeName.toLowerCase()])&&"get"in b&&void 0!==(d=b.get(e,
"value")))return d;d=e.value;return"string"===typeof d?d.replace(Id,""):null==d?"":d}}});c.extend({valHooks:{option:{get:function(a){var b=c.find.attr(a,"value");return null!=b?b:c.trim(c.text(a)).replace(Jd," ")}},select:{get:function(a){for(var b,d=a.options,e=a.selectedIndex,f=(a="select-one"===a.type||0>e)?null:[],g=a?e+1:d.length,k=0>e?g:a?e:0;k<g;k++)if(b=d[k],!(!b.selected&&k!==e||(y.optDisabled?b.disabled:null!==b.getAttribute("disabled"))||b.parentNode.disabled&&c.nodeName(b.parentNode,"optgroup"))){b=
c(b).val();if(a)return b;f.push(b)}return f},set:function(a,b){for(var d,e=a.options,f=c.makeArray(b),g=e.length;g--;)if(b=e[g],-1<c.inArray(c.valHooks.option.get(b),f))try{b.selected=d=!0}catch(k){b.scrollHeight}else b.selected=!1;d||(a.selectedIndex=-1);return e}}}});c.each(["radio","checkbox"],function(){c.valHooks[this]={set:function(a,b){if(c.isArray(b))return a.checked=-1<c.inArray(c(a).val(),b)}};y.checkOn||(c.valHooks[this].get=function(a){return null===a.getAttribute("value")?"on":a.value})});
var xa=c.expr.attrHandle,Kb=/^(?:checked|selected)$/i,Ma=y.getSetAttribute,nb=y.input;c.fn.extend({attr:function(a,b){return Ea(this,c.attr,a,b,1<arguments.length)},removeAttr:function(a){return this.each(function(){c.removeAttr(this,a)})}});c.extend({attr:function(a,b,d){var e,f=a.nodeType;if(3!==f&&8!==f&&2!==f){if("undefined"===typeof a.getAttribute)return c.prop(a,b,d);if(1!==f||!c.isXMLDoc(a)){b=b.toLowerCase();var g=c.attrHooks[b]||(c.expr.match.bool.test(b)?Kd:ab)}if(void 0!==d){if(null===
d){c.removeAttr(a,b);return}if(g&&"set"in g&&void 0!==(e=g.set(a,d,b)))return e;a.setAttribute(b,d+"");return d}if(g&&"get"in g&&null!==(e=g.get(a,b)))return e;e=c.find.attr(a,b);return null==e?void 0:e}},attrHooks:{type:{set:function(a,b){if(!y.radioValue&&"radio"===b&&c.nodeName(a,"input")){var d=a.value;a.setAttribute("type",b);d&&(a.value=d);return b}}}},removeAttr:function(a,b){var d=0,e=b&&b.match(qa);if(e&&1===a.nodeType)for(;b=e[d++];){var f=c.propFix[b]||b;c.expr.match.bool.test(b)?nb&&Ma||
!Kb.test(b)?a[f]=!1:a[c.camelCase("default-"+b)]=a[f]=!1:c.attr(a,b,"");a.removeAttribute(Ma?b:f)}}});var Kd={set:function(a,b,d){!1===b?c.removeAttr(a,d):nb&&Ma||!Kb.test(d)?a.setAttribute(!Ma&&c.propFix[d]||d,d):a[c.camelCase("default-"+d)]=a[d]=!0;return d}};c.each(c.expr.match.bool.source.match(/\w+/g),function(a,b){var d=xa[b]||c.find.attr;nb&&Ma||!Kb.test(b)?xa[b]=function(e,f,g){if(!g){var k=xa[f];xa[f]=m;var m=null!=d(e,f,g)?f.toLowerCase():null;xa[f]=k}return m}:xa[b]=function(e,f,g){if(!g)return e[c.camelCase("default-"+
f)]?f.toLowerCase():null}});nb&&Ma||(c.attrHooks.value={set:function(a,b,d){if(c.nodeName(a,"input"))a.defaultValue=b;else return ab&&ab.set(a,b,d)}});if(!Ma){var ab={set:function(a,b,d){var e=a.getAttributeNode(d);e||a.setAttributeNode(e=a.ownerDocument.createAttribute(d));e.value=b+="";if("value"===d||b===a.getAttribute(d))return b}};xa.id=xa.name=xa.coords=function(a,b,d){var e;if(!d)return(e=a.getAttributeNode(b))&&""!==e.value?e.value:null};c.valHooks.button={get:function(a,b){if((a=a.getAttributeNode(b))&&
a.specified)return a.value},set:ab.set};c.attrHooks.contenteditable={set:function(a,b,d){ab.set(a,""===b?!1:b,d)}};c.each(["width","height"],function(a,b){c.attrHooks[b]={set:function(d,e){if(""===e)return d.setAttribute(b,"auto"),e}}})}y.style||(c.attrHooks.style={get:function(a){return a.style.cssText||void 0},set:function(a,b){return a.style.cssText=b+""}});var Ld=/^(?:input|select|textarea|button|object)$/i,Md=/^(?:a|area)$/i;c.fn.extend({prop:function(a,b){return Ea(this,c.prop,a,b,1<arguments.length)},
removeProp:function(a){a=c.propFix[a]||a;return this.each(function(){try{this[a]=void 0,delete this[a]}catch(b){}})}});c.extend({prop:function(a,b,d){var e,f=a.nodeType;if(3!==f&&8!==f&&2!==f){if(1!==f||!c.isXMLDoc(a)){b=c.propFix[b]||b;var g=c.propHooks[b]}return void 0!==d?g&&"set"in g&&void 0!==(e=g.set(a,d,b))?e:a[b]=d:g&&"get"in g&&null!==(e=g.get(a,b))?e:a[b]}},propHooks:{tabIndex:{get:function(a){var b=c.find.attr(a,"tabindex");return b?parseInt(b,10):Ld.test(a.nodeName)||Md.test(a.nodeName)&&
a.href?0:-1}}},propFix:{"for":"htmlFor","class":"className"}});y.hrefNormalized||c.each(["href","src"],function(a,b){c.propHooks[b]={get:function(d){return d.getAttribute(b,4)}}});y.optSelected||(c.propHooks.selected={get:function(a){if(a=a.parentNode)a.selectedIndex,a.parentNode&&a.parentNode.selectedIndex;return null},set:function(a){if(a=a.parentNode)a.selectedIndex,a.parentNode&&a.parentNode.selectedIndex}});c.each("tabIndex readOnly maxLength cellSpacing cellPadding rowSpan colSpan useMap frameBorder contentEditable".split(" "),
function(){c.propFix[this.toLowerCase()]=this});y.enctype||(c.propFix.enctype="encoding");var Lb=/[\t\r\n\f]/g;c.fn.extend({addClass:function(a){var b,d,e,f,g,k=0;if(c.isFunction(a))return this.each(function(p){c(this).addClass(a.call(this,p,Ia(this)))});if("string"===typeof a&&a)for(b=a.match(qa)||[];d=this[k++];){var m=Ia(d);if(e=1===d.nodeType&&(" "+m+" ").replace(Lb," ")){for(g=0;f=b[g++];)0>e.indexOf(" "+f+" ")&&(e+=f+" ");e=c.trim(e);m!==e&&c.attr(d,"class",e)}}return this},removeClass:function(a){var b,
d,e,f,g,k=0;if(c.isFunction(a))return this.each(function(p){c(this).removeClass(a.call(this,p,Ia(this)))});if(!arguments.length)return this.attr("class","");if("string"===typeof a&&a)for(b=a.match(qa)||[];d=this[k++];){var m=Ia(d);if(e=1===d.nodeType&&(" "+m+" ").replace(Lb," ")){for(g=0;f=b[g++];)for(;-1<e.indexOf(" "+f+" ");)e=e.replace(" "+f+" "," ");e=c.trim(e);m!==e&&c.attr(d,"class",e)}}return this},toggleClass:function(a,b){var d=typeof a;return"boolean"===typeof b&&"string"===d?b?this.addClass(a):
this.removeClass(a):c.isFunction(a)?this.each(function(e){c(this).toggleClass(a.call(this,e,Ia(this),b),b)}):this.each(function(){var e,f;if("string"===d){var g=0;var k=c(this);for(f=a.match(qa)||[];e=f[g++];)k.hasClass(e)?k.removeClass(e):k.addClass(e)}else if(void 0===a||"boolean"===d)(e=Ia(this))&&c._data(this,"__className__",e),c.attr(this,"class",e||!1===a?"":c._data(this,"__className__")||"")})},hasClass:function(a){var b,d=0;for(a=" "+a+" ";b=this[d++];)if(1===b.nodeType&&-1<(" "+Ia(b)+" ").replace(Lb,
" ").indexOf(a))return!0;return!1}});c.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(a,b){c.fn[b]=function(d,e){return 0<arguments.length?this.on(b,null,d,e):this.trigger(b)}});c.fn.extend({hover:function(a,b){return this.mouseenter(a).mouseleave(b||a)}});var Nd=B.location,Mb=c.now(),Nb=/\?/,Od=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;
c.parseJSON=function(a){if(B.JSON&&B.JSON.parse)return B.JSON.parse(a+"");var b,d=null,e=c.trim(a+"");return e&&!c.trim(e.replace(Od,function(f,g,k,m){b&&g&&(d=0);if(0===d)return f;b=k||g;d+=!m-!k;return""}))?Function("return "+e)():c.error("Invalid JSON: "+a)};c.parseXML=function(a){if(!a||"string"!==typeof a)return null;try{if(B.DOMParser){var b=new B.DOMParser;var d=b.parseFromString(a,"text/xml")}else d=new B.ActiveXObject("Microsoft.XMLDOM"),d.async="false",d.loadXML(a)}catch(e){d=void 0}d&&
d.documentElement&&!d.getElementsByTagName("parsererror").length||c.error("Invalid XML: "+a);return d};var Pd=/#.*$/,Kc=/([?&])_=[^&]*/,Qd=/^(.*?):[ \t]*([^\r\n]*)\r?$/mg,Rd=/^(?:GET|HEAD)$/,Sd=/^\/\//,Lc=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Mc={},xb={},Nc="*/".concat("*"),Ob=Nd.href,Va=Lc.exec(Ob.toLowerCase())||[];c.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ob,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Va[1]),global:!0,
processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset\x3dUTF-8",accepts:{"*":Nc,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":c.parseJSON,"text xml":c.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(a,b){return b?
yb(yb(a,c.ajaxSettings),b):yb(c.ajaxSettings,a)},ajaxPrefilter:rc(Mc),ajaxTransport:rc(xb),ajax:function(a,b){function d(J,U,G,W){var R=U;if(2!==fa){fa=2;g&&B.clearTimeout(g);m=void 0;f=W||"";I.readyState=0<J?4:0;W=200<=J&&300>J||304===J;if(G){var N=q;for(var aa=I,da,la,M,O,Y=N.contents,na=N.dataTypes;"*"===na[0];)na.shift(),void 0===la&&(la=N.mimeType||aa.getResponseHeader("Content-Type"));if(la)for(O in Y)if(Y[O]&&Y[O].test(la)){na.unshift(O);break}if(na[0]in G)M=na[0];else{for(O in G){if(!na[0]||
N.converters[O+" "+na[0]]){M=O;break}da||(da=O)}M=M||da}M?(M!==na[0]&&na.unshift(M),N=G[M]):N=void 0}a:{G=q;da=N;la=I;M=W;var La;aa={};Y=G.dataTypes.slice();if(Y[1])for(ba in G.converters)aa[ba.toLowerCase()]=G.converters[ba];for(O=Y.shift();O;){G.responseFields[O]&&(la[G.responseFields[O]]=da);!sa&&M&&G.dataFilter&&(da=G.dataFilter(da,G.dataType));var sa=O;if(O=Y.shift())if("*"===O)O=sa;else if("*"!==sa&&sa!==O){var ba=aa[sa+" "+O]||aa["* "+O];if(!ba)for(La in aa)if(N=La.split(" "),N[1]===O&&(ba=
aa[sa+" "+N[0]]||aa["* "+N[0]])){!0===ba?ba=aa[La]:!0!==aa[La]&&(O=N[0],Y.unshift(N[1]));break}if(!0!==ba)if(ba&&G["throws"])da=ba(da);else try{da=ba(da)}catch(ua){N={state:"parsererror",error:ba?ua:"No conversion from "+sa+" to "+O};break a}}}N={state:"success",data:da}}if(W)if(q.ifModified&&((R=I.getResponseHeader("Last-Modified"))&&(c.lastModified[ca]=R),(R=I.getResponseHeader("etag"))&&(c.etag[ca]=R)),204===J||"HEAD"===q.type)R="nocontent";else if(304===J)R="notmodified";else{R=N.state;var Ra=
N.data;var Sa=N.error;W=!Sa}else if(Sa=R,J||!R)R="error",0>J&&(J=0);I.status=J;I.statusText=(U||R)+"";W?A.resolveWith(t,[Ra,R,I]):A.rejectWith(t,[I,R,Sa]);I.statusCode(L);L=void 0;k&&v.trigger(W?"ajaxSuccess":"ajaxError",[I,q,W?Ra:Sa]);Q.fireWith(t,[I,R]);k&&(v.trigger("ajaxComplete",[I,q]),--c.active||c.event.trigger("ajaxStop"))}}"object"===typeof a&&(b=a,a=void 0);b=b||{};var e,f,g,k,m,p,q=c.ajaxSetup({},b),t=q.context||q,v=q.context&&(t.nodeType||t.jquery)?c(t):c.event,A=c.Deferred(),Q=c.Callbacks("once memory"),
L=q.statusCode||{},ja={},ia={},fa=0,jb="canceled",I={readyState:0,getResponseHeader:function(J){var U;if(2===fa){if(!p)for(p={};U=Qd.exec(f);)p[U[1].toLowerCase()]=U[2];U=p[J.toLowerCase()]}return null==U?null:U},getAllResponseHeaders:function(){return 2===fa?f:null},setRequestHeader:function(J,U){var G=J.toLowerCase();fa||(J=ia[G]=ia[G]||J,ja[J]=U);return this},overrideMimeType:function(J){fa||(q.mimeType=J);return this},statusCode:function(J){var U;if(J)if(2>fa)for(U in J)L[U]=[L[U],J[U]];else I.always(J[I.status]);
return this},abort:function(J){J=J||jb;m&&m.abort(J);d(0,J);return this}};A.promise(I).complete=Q.add;I.success=I.done;I.error=I.fail;q.url=((a||q.url||Ob)+"").replace(Pd,"").replace(Sd,Va[1]+"//");q.type=b.method||b.type||q.method||q.type;q.dataTypes=c.trim(q.dataType||"*").toLowerCase().match(qa)||[""];null==q.crossDomain&&(a=Lc.exec(q.url.toLowerCase()),q.crossDomain=!(!a||a[1]===Va[1]&&a[2]===Va[2]&&(a[3]||("http:"===a[1]?"80":"443"))===(Va[3]||("http:"===Va[1]?"80":"443"))));q.data&&q.processData&&
"string"!==typeof q.data&&(q.data=c.param(q.data,q.traditional));sc(Mc,q,b,I);if(2===fa)return I;(k=c.event&&q.global)&&0===c.active++&&c.event.trigger("ajaxStart");q.type=q.type.toUpperCase();q.hasContent=!Rd.test(q.type);var ca=q.url;q.hasContent||(q.data&&(ca=q.url+=(Nb.test(ca)?"\x26":"?")+q.data,delete q.data),!1===q.cache&&(q.url=Kc.test(ca)?ca.replace(Kc,"$1_\x3d"+Mb++):ca+(Nb.test(ca)?"\x26":"?")+"_\x3d"+Mb++));q.ifModified&&(c.lastModified[ca]&&I.setRequestHeader("If-Modified-Since",c.lastModified[ca]),
c.etag[ca]&&I.setRequestHeader("If-None-Match",c.etag[ca]));(q.data&&q.hasContent&&!1!==q.contentType||b.contentType)&&I.setRequestHeader("Content-Type",q.contentType);I.setRequestHeader("Accept",q.dataTypes[0]&&q.accepts[q.dataTypes[0]]?q.accepts[q.dataTypes[0]]+("*"!==q.dataTypes[0]?", "+Nc+"; q\x3d0.01":""):q.accepts["*"]);for(e in q.headers)I.setRequestHeader(e,q.headers[e]);if(q.beforeSend&&(!1===q.beforeSend.call(t,I,q)||2===fa))return I.abort();jb="abort";for(e in{success:1,error:1,complete:1})I[e](q[e]);
if(m=sc(xb,q,b,I)){I.readyState=1;k&&v.trigger("ajaxSend",[I,q]);if(2===fa)return I;q.async&&0<q.timeout&&(g=B.setTimeout(function(){I.abort("timeout")},q.timeout));try{fa=1,m.send(ja,d)}catch(J){if(2>fa)d(-1,J);else throw J;}}else d(-1,"No Transport");return I},getJSON:function(a,b,d){return c.get(a,b,d,"json")},getScript:function(a,b){return c.get(a,void 0,b,"script")}});c.each(["get","post"],function(a,b){c[b]=function(d,e,f,g){c.isFunction(e)&&(g=g||f,f=e,e=void 0);return c.ajax(c.extend({url:d,
type:b,dataType:g,data:e,success:f},c.isPlainObject(d)&&d))}});c._evalUrl=function(a){return c.ajax({url:a,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,"throws":!0})};c.fn.extend({wrapAll:function(a){if(c.isFunction(a))return this.each(function(d){c(this).wrapAll(a.call(this,d))});if(this[0]){var b=c(a,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&b.insertBefore(this[0]);b.map(function(){for(var d=this;d.firstChild&&1===d.firstChild.nodeType;)d=d.firstChild;return d}).append(this)}return this},
wrapInner:function(a){return c.isFunction(a)?this.each(function(b){c(this).wrapInner(a.call(this,b))}):this.each(function(){var b=c(this),d=b.contents();d.length?d.wrapAll(a):b.append(a)})},wrap:function(a){var b=c.isFunction(a);return this.each(function(d){c(this).wrapAll(b?a.call(this,d):a)})},unwrap:function(){return this.parent().each(function(){c.nodeName(this,"body")||c(this).replaceWith(this.childNodes)}).end()}});c.expr.filters.hidden=function(a){return y.reliableHiddenOffsets()?0>=a.offsetWidth&&
0>=a.offsetHeight&&!a.getClientRects().length:ad(a)};c.expr.filters.visible=function(a){return!c.expr.filters.hidden(a)};var Td=/%20/g,bd=/\[\]$/,Oc=/\r?\n/g,Ud=/^(?:submit|button|image|reset|file)$/i,Vd=/^(?:input|select|textarea|keygen)/i;c.param=function(a,b){var d,e=[],f=function(g,k){k=c.isFunction(k)?k():null==k?"":k;e[e.length]=encodeURIComponent(g)+"\x3d"+encodeURIComponent(k)};void 0===b&&(b=c.ajaxSettings&&c.ajaxSettings.traditional);if(c.isArray(a)||a.jquery&&!c.isPlainObject(a))c.each(a,
function(){f(this.name,this.value)});else for(d in a)zb(d,a[d],b,f);return e.join("\x26").replace(Td,"+")};c.fn.extend({serialize:function(){return c.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var a=c.prop(this,"elements");return a?c.makeArray(a):this}).filter(function(){var a=this.type;return this.name&&!c(this).is(":disabled")&&Vd.test(this.nodeName)&&!Ud.test(a)&&(this.checked||!tb.test(a))}).map(function(a,b){a=c(this).val();return null==a?null:c.isArray(a)?
c.map(a,function(d){return{name:b.name,value:d.replace(Oc,"\r\n")}}):{name:b.name,value:a.replace(Oc,"\r\n")}}).get()}});c.ajaxSettings.xhr=void 0!==B.ActiveXObject?function(){return this.isLocal?tc():8<C.documentMode?Ab():/^(get|post|head|put|delete|options)$/i.test(this.type)&&Ab()||tc()}:Ab;var Wd=0,ob={},pb=c.ajaxSettings.xhr();B.attachEvent&&B.attachEvent("onunload",function(){for(var a in ob)ob[a](void 0,!0)});y.cors=!!pb&&"withCredentials"in pb;(pb=y.ajax=!!pb)&&c.ajaxTransport(function(a){if(!a.crossDomain||
y.cors){var b;return{send:function(d,e){var f,g=a.xhr(),k=++Wd;g.open(a.type,a.url,a.async,a.username,a.password);if(a.xhrFields)for(f in a.xhrFields)g[f]=a.xhrFields[f];a.mimeType&&g.overrideMimeType&&g.overrideMimeType(a.mimeType);a.crossDomain||d["X-Requested-With"]||(d["X-Requested-With"]="XMLHttpRequest");for(f in d)void 0!==d[f]&&g.setRequestHeader(f,d[f]+"");g.send(a.hasContent&&a.data||null);b=function(m,p){if(b&&(p||4===g.readyState))if(delete ob[k],b=void 0,g.onreadystatechange=c.noop,p)4!==
g.readyState&&g.abort();else{var q={};var t=g.status;"string"===typeof g.responseText&&(q.text=g.responseText);try{var v=g.statusText}catch(A){v=""}t||!a.isLocal||a.crossDomain?1223===t&&(t=204):t=q.text?200:404}q&&e(t,v,q,g.getAllResponseHeaders())};a.async?4===g.readyState?B.setTimeout(b):g.onreadystatechange=ob[k]=b:b()},abort:function(){b&&b(void 0,!0)}}}});c.ajaxPrefilter(function(a){a.crossDomain&&(a.contents.script=!1)});c.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},
contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(a){c.globalEval(a);return a}}});c.ajaxPrefilter("script",function(a){void 0===a.cache&&(a.cache=!1);a.crossDomain&&(a.type="GET",a.global=!1)});c.ajaxTransport("script",function(a){if(a.crossDomain){var b,d=C.head||c("head")[0]||C.documentElement;return{send:function(e,f){b=C.createElement("script");b.async=!0;a.scriptCharset&&(b.charset=a.scriptCharset);b.src=a.url;b.onload=b.onreadystatechange=function(g,k){if(k||!b.readyState||
/loaded|complete/.test(b.readyState))b.onload=b.onreadystatechange=null,b.parentNode&&b.parentNode.removeChild(b),b=null,k||f(200,"success")};d.insertBefore(b,d.firstChild)},abort:function(){if(b)b.onload(void 0,!0)}}}});var Pc=[],Pb=/(=)\?(?=&|$)|\?\?/;c.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var a=Pc.pop()||c.expando+"_"+Mb++;this[a]=!0;return a}});c.ajaxPrefilter("json jsonp",function(a,b,d){var e,f=!1!==a.jsonp&&(Pb.test(a.url)?"url":"string"===typeof a.data&&0===(a.contentType||
"").indexOf("application/x-www-form-urlencoded")&&Pb.test(a.data)&&"data");if(f||"jsonp"===a.dataTypes[0]){var g=a.jsonpCallback=c.isFunction(a.jsonpCallback)?a.jsonpCallback():a.jsonpCallback;f?a[f]=a[f].replace(Pb,"$1"+g):!1!==a.jsonp&&(a.url+=(Nb.test(a.url)?"\x26":"?")+a.jsonp+"\x3d"+g);a.converters["script json"]=function(){e||c.error(g+" was not called");return e[0]};a.dataTypes[0]="json";var k=B[g];B[g]=function(){e=arguments};d.always(function(){void 0===k?c(B).removeProp(g):B[g]=k;a[g]&&
(a.jsonpCallback=b.jsonpCallback,Pc.push(g));e&&c.isFunction(k)&&k(e[0]);e=k=void 0});return"script"}});c.parseHTML=function(a,b,d){if(!a||"string"!==typeof a)return null;"boolean"===typeof b&&(d=b,b=!1);b=b||C;var e=Cc.exec(a);d=!d&&[];if(e)return[b.createElement(e[1])];e=Wb([a],b,d);d&&d.length&&c(d).remove();return c.merge([],e.childNodes)};var Qc=c.fn.load;c.fn.load=function(a,b,d){if("string"!==typeof a&&Qc)return Qc.apply(this,arguments);var e,f,g=this,k=a.indexOf(" ");if(-1<k){var m=c.trim(a.slice(k,
a.length));a=a.slice(0,k)}c.isFunction(b)?(d=b,b=void 0):b&&"object"===typeof b&&(e="POST");0<g.length&&c.ajax({url:a,type:e||"GET",dataType:"html",data:b}).done(function(p){f=arguments;g.html(m?c("\x3cdiv\x3e").append(c.parseHTML(p)).find(m):p)}).always(d&&function(p,q){g.each(function(){d.apply(this,f||[p.responseText,q,p])})});return this};c.each("ajaxStart ajaxStop ajaxComplete ajaxError ajaxSuccess ajaxSend".split(" "),function(a,b){c.fn[b]=function(d){return this.on(b,d)}});c.expr.filters.animated=
function(a){return c.grep(c.timers,function(b){return a===b.elem}).length};c.offset={setOffset:function(a,b,d){var e=c.css(a,"position"),f=c(a),g={};"static"===e&&(a.style.position="relative");var k=f.offset();var m=c.css(a,"top");var p=c.css(a,"left");("absolute"===e||"fixed"===e)&&-1<c.inArray("auto",[m,p])?(p=f.position(),m=p.top,p=p.left):(m=parseFloat(m)||0,p=parseFloat(p)||0);c.isFunction(b)&&(b=b.call(a,d,c.extend({},k)));null!=b.top&&(g.top=b.top-k.top+m);null!=b.left&&(g.left=b.left-k.left+
p);"using"in b?b.using.call(a,g):f.css(g)}};c.fn.extend({offset:function(a){if(arguments.length)return void 0===a?this:this.each(function(g){c.offset.setOffset(this,a,g)});var b,d={top:0,left:0},e=(b=this[0])&&b.ownerDocument;if(e){var f=e.documentElement;if(!c.contains(f,b))return d;"undefined"!==typeof b.getBoundingClientRect&&(d=b.getBoundingClientRect());b=uc(e);return{top:d.top+(b.pageYOffset||f.scrollTop)-(f.clientTop||0),left:d.left+(b.pageXOffset||f.scrollLeft)-(f.clientLeft||0)}}},position:function(){if(this[0]){var a=
{top:0,left:0},b=this[0];if("fixed"===c.css(b,"position"))var d=b.getBoundingClientRect();else{var e=this.offsetParent();d=this.offset();c.nodeName(e[0],"html")||(a=e.offset());a.top+=c.css(e[0],"borderTopWidth",!0);a.left+=c.css(e[0],"borderLeftWidth",!0)}return{top:d.top-a.top-c.css(b,"marginTop",!0),left:d.left-a.left-c.css(b,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var a=this.offsetParent;a&&!c.nodeName(a,"html")&&"static"===c.css(a,"position");)a=a.offsetParent;
return a||Ic})}});c.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(a,b){var d=/Y/.test(b);c.fn[a]=function(e){return Ea(this,function(f,g,k){var m=uc(f);if(void 0===k)return m?b in m?m[b]:m.document.documentElement[g]:f[g];m?m.scrollTo(d?c(m).scrollLeft():k,d?k:c(m).scrollTop()):f[g]=k},a,e,arguments.length,null)}});c.each(["top","left"],function(a,b){c.cssHooks[b]=wb(y.pixelPosition,function(d,e){if(e)return e=Ha(d,b),db.test(e)?c(d).position()[b]+"px":e})});c.each({Height:"height",
Width:"width"},function(a,b){c.each({padding:"inner"+a,content:b,"":"outer"+a},function(d,e){c.fn[e]=function(f,g){var k=arguments.length&&(d||"boolean"!==typeof f),m=d||(!0===f||!0===g?"margin":"border");return Ea(this,function(p,q,t){return c.isWindow(p)?p.document.documentElement["client"+a]:9===p.nodeType?(q=p.documentElement,Math.max(p.body["scroll"+a],q["scroll"+a],p.body["offset"+a],q["offset"+a],q["client"+a])):void 0===t?c.css(p,q,m):c.style(p,q,t,m)},b,k?f:void 0,k,null)}})});c.fn.extend({bind:function(a,
b,d){return this.on(a,null,b,d)},unbind:function(a,b){return this.off(a,null,b)},delegate:function(a,b,d,e){return this.on(b,a,d,e)},undelegate:function(a,b,d){return 1===arguments.length?this.off(a,"**"):this.off(b,a||"**",d)}});c.fn.size=function(){return this.length};c.fn.andSelf=c.fn.addBack;"function"===typeof define&&define.amd&&define("jquery",[],function(){return c});var Xd=B.jQuery,Yd=B.$;c.noConflict=function(a){B.$===c&&(B.$=Yd);a&&B.jQuery===c&&(B.jQuery=Xd);return c};ya||(B.jQuery=B.$=
c);return c});jQuery.uaMatch=function(B){B=B.toLowerCase();B=/(chrome)[ \/]([\w.]+)/.exec(B)||/(webkit)[ \/]([\w.]+)/.exec(B)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(B)||/(msie) ([\w.]+)/.exec(B)||0>B.indexOf("compatible")&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(B)||[];return{browser:B[1]||"",version:B[2]||"0"}};
jQuery.browser||(matched=jQuery.uaMatch(navigator.userAgent),browser={},matched.browser&&(browser[matched.browser]=!0,browser.version=matched.version),browser.chrome?browser.webkit=!0:browser.webkit&&(browser.safari=!0),jQuery.browser=browser);
(function(B){function ya(pa){var Aa=window.document.implementation.createHTMLDocument("");Aa.body.innerHTML=pa;return Aa.body&&Aa.body.innerHTML}var za=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi,Wa=B.htmlPrefilter;B.htmlPrefilter=function(pa){var Aa=pa.replace(za,"\x3c$1\x3e\x3c/$2\x3e");Aa!==pa&&ya(pa)!==ya(Aa)&&console.error("HTML tags must be properly nested and closed: "+pa);return Wa(pa)}})(window.jQuery);