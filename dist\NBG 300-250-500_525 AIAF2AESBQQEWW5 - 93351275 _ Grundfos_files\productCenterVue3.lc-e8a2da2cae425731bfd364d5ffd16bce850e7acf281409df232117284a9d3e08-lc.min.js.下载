var Va=Object.defineProperty,Ba=Object.defineProperties;var Ya=Object.getOwnPropertyDescriptors;var bt=Object.getOwnPropertySymbols;var la=Object.prototype.hasOwnProperty,ia=Object.prototype.propertyIsEnumerable;var oa=(a,e,t)=>e in a?Va(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t,ne=(a,e)=>{for(var t in e||(e={}))la.call(e,t)&&oa(a,t,e[t]);if(bt)for(var t of bt(e))ia.call(e,t)&&oa(a,t,e[t]);return a},fe=(a,e)=>Ba(a,Ya(e));var ca=(a,e)=>{var t={};for(var s in a)la.call(a,s)&&e.indexOf(s)<0&&(t[s]=a[s]);if(a!=null&&bt)for(var s of bt(a))e.indexOf(s)<0&&ia.call(a,s)&&(t[s]=a[s]);return t};var Wa=(a,e)=>()=>(e||a((e={exports:{}}).exports,e),e.exports);var ve=(a,e,t)=>new Promise((s,n)=>{var l=u=>{try{i(t.next(u))}catch(p){n(p)}},o=u=>{try{i(t.throw(u))}catch(p){n(p)}},i=u=>u.done?s(u.value):Promise.resolve(u.value).then(l,o);i((t=t.apply(a,e)).next())});import{t as we,c as Yt,g as ke,a as A,G as oe,P as De,r as qt,u as ot,_ as ja,i as pt,S as qe,b as Xe,d as Ue,e as Ke,f as at,h as Ja,j as $t,k as Wt,l as Se,m as jt,n as Za,s as Xa,q as Ca,o as Ka,p as xa,v as es,w as ts,x as as,y as Ct,z as ya,A as ss,B as ns,N as mt,C as qa,D as ra,E as os,F as ua,H as $a,I as ls,J as Jt,K as Zt,L as zt,M as da,O as pa,Q as Ft,R as Xt,T as Kt,U as St,V as Ia,W as Pa,X as It,Y as ma,Z as is,$ as ka,a0 as Sa,a1 as cs,a2 as rs,a3 as us,a4 as Aa,a5 as Dt,a6 as Ea,a7 as ds,a8 as ps,a9 as ms,aa as vs,ab as gs,ac as _s,ad as Ot,ae as bs,af as At,ag as hs,ah as fs,ai as Cs}from"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/grundfos-aem-vue3.jzWaqUtf.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/vue.iHqVIOfa.js";import{d as Ne,s as Ve}from"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/pinia.Ctu0cYgx.js";import{Q as Be}from"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/qs.DSza7awe.js";import{d as G,c as r,D as Ee,I as B,R as F,H as d,M as be,J as c,G as f,S as te,T as le,Q as X,O as q,r as j,Y as Q,u as v,L as Te,V as st,f as ze,g as gt,k as xt,P as Pt,w as me,o as ys,q as Nt,a as qs,X as Oe,a3 as $s,ad as Ra,ae as Et,N as Ye,_ as Is,af as Ps,F as ks,ag as vt,$ as nt,Z as Ss}from"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/@vue.DVFa8qY-.js";import{a as Ua,b as Je,c as As,d as Es,e as Rs,f as Us}from"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/@vueuse.B28xn-6N.js";import{a as Ls,b as Ts,c as ws}from"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/@tanstack.DxIcY3kU.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/hash-it.CEjHYFI-.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/vue-dompurify-html.DOaXFJI-.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/dompurify.CzNiSsDN.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/@formkit.CeV5sp5o.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/vue3-touch-events.BW7lqoOL.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/leaflet.BrDwVF98.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/call-bind-apply-helpers.BOU9z0XM.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/function-bind.QECIgn5J.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/es-errors.33T_b48r.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/dayjs.BIIKh1wf.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/cookie.seWKNMeD.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/axios.3eK-A0Ov.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/side-channel.Cs6SUPBp.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/object-inspect.CjERQP-r.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/side-channel-list.CLkyKXwN.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/side-channel-map.C9Ic4EF0.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/get-intrinsic.BMthiaA2.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/es-object-atoms.B8jHrylh.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/math-intrinsics.O0UcqvTV.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/gopd.DFHXJQvh.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/es-define-property.BFRwQPUt.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/has-symbols.mWE3_vNO.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/get-proto.DSY1OQeS.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/dunder-proto.J-oDIVZm.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/hasown.B1E8BENs.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/call-bound.C2OEcMaM.js";import"/etc.clientlibs/settings/wcm/design/aem-productcenter/productCenterVue3/resources/js/chunks/side-channel-weakmap.TERH4U0a.js";var Nd=Wa($e=>{const Gt={pumpedLiquidSelect:"PumpedFluid_PLG",pumpDesignLinkRel:"pumpdesignlist"},Ds={comments:!0,liquidinformation:!0,lqcid:null,userselection:!0,warnings:!0},Rt={pumpDesigns:"pump designs",pumpRange:"pump range",pumpDetails:"pump details",result:"result"},Os="liquids",Ut=(a,e,t)=>{we({product:t,userJourney:{data:e,stepType:a}},Os)},xe=Ne("liquidGuideStore",{state:()=>({config:{navListApiUrl:null},labels:{},navigationList:null,configuration:{configQcid:null,liquidInfo:null,productFamilyQcResults:null},productTypeCode:null,qcSummary:null,pumpDesigns:null,liquidId:null,qcResults:null,selectLiquidQcId:null,translatedQcSummary:null,translatedLabels:{},visibleProductFamilies:null}),actions:fe(ne({},Yt()),{getNavigationList(){const a=_=>{this.navigationList=A(_,this.navigationList)},{qcid:e,lQcid:t,lid:s,lPumpdesignid:n,pumpdesignid:l,familyCode:o,typecode:i,lTypecode:u,configQcid:p}=ke(window.location.search),g={qcid:t||e,lid:s,pumpdesignid:n||l,familyCode:o,typecode:u||i,configQcid:p};return this.productTypeCode=g.typecode,oe(this.config.navListApiUrl,{params:g}).then(_=>{a(_)}).catch(_=>{a(_)})},getTranslatedLabels(a){const e=s=>{this.translatedLabels=A(s)},t={settingid:"liquids",languageCode:a};return oe(this.config.printTranslateApiUrl,{params:t}).then(e).catch(e)},getQcSummary(a=!1,e=""){this.configuration.productFamilyQcResults=A(null,this.configuration.productFamilyQcResults);const{configQcid:t}=ke(window.location.search);if(a){const l=i=>{this.translatedQcSummary=A(i)},o={qcid:t,languageCode:e};return oe(this.config.qCsummaryApiUrl,{params:o}).then(l).catch(l)}const s=l=>{this.qcSummary=A(l)},n={qcid:t};return oe(this.config.qCsummaryApiUrl,{params:n}).then(s).catch(s)},setConfigQcResults(a){var t,s;this.configuration.productFamilyQcResults=A(a);const e=(t=a==null?void 0:a.data)==null?void 0:t.id;this.configuration.configQcid=e!=null?e:this.configuration.configQcid,qt({configQcid:(s=this.configuration.configQcid)!=null?s:void 0})},getLiquidConfiguration(a=!1){const{qcid:e,lQcid:t,lTypecode:s,typecode:n,configQcid:l}=ke(window.location.search),o=s||n,p=a?l?{qcid:l}:{qcid:t||e,typecode:o}:{qcid:l,typecode:o};return this.productTypeCode=o,oe(this.config.productFamilyQcApiUrl,{params:p}).then(this.setConfigQcResults).catch(this.setConfigQcResults)},getLiquidInfo(){const{qcid:a,lQcid:e,lid:t}=ke(window.location.search),n={qcid:e||a,lid:t},l=o=>{this.configuration.liquidInfo=A(o)};return oe(this.config.liquidsInfoApiUrl,{params:n}).then(l).catch(l)},updateLiquidsProductFamilyQc(a){this.configuration.productFamilyQcResults=A(null,this.configuration.productFamilyQcResults);const e=a?[a]:[],t={qcid:this.configuration.configQcid};return De(this.config.productFamilyQcApiUrl,Be.stringify({body:JSON.stringify(e)},{encode:!0}),{params:t}).then(s=>{s.data&&this.setConfigQcResults(s)}).catch(this.setConfigQcResults)},getLiquidSizingQc(){const a={qcid:this.configuration.configQcid,typecode:this.productTypeCode};return oe(this.config.liquidsSizingApiUrl,{params:a})},setLiquidData(){ot().setTopLevelData("plgreport",fe(ne({},Ds),{lqcid:this.configuration.configQcid}))},getPumpDesigns(a){const{qcid:e,lQcid:t}=ke(window.location.search),s={qcid:t||e},n=o=>{this.pumpDesigns=A(o)},l=a!=null?a:this.config.liquidsPumpDesignsApiUrl;oe(l,{params:s}).then(n).catch(n)},setQcResults(a){var e,t,s;return this.qcResults=A(a),this.selectLiquidQcId=(t=(e=a==null?void 0:a.data)==null?void 0:e.id)!=null?t:this.selectLiquidQcId,this.liquidId=(s=this.qcResultsLiquidQuestion)==null?void 0:s.selectedoptionkey,qt({lQcid:this.selectLiquidQcId,lid:this.liquidId}),Promise.resolve(a)},updateLiquidsQc(a){const e={qcid:this.selectLiquidQcId};this.qcResults=A(null,this.qcResults);const t=a?[a]:[];return De(this.config.liquidsQCApiUrl,Be.stringify({body:JSON.stringify(t)},{encode:!0}),{params:e})},selectFirstLiquidIfEmpty(a){var e,t,s,n;if(!this.liquidId){const l=(n=(s=(t=(e=this.qcResultsLiquidQuestion)==null?void 0:e.options)==null?void 0:t[1])==null?void 0:s.key)!=null?n:"";return this.updateLiquidsQc({label:Gt.pumpedLiquidSelect,convalue:l})}return Promise.resolve(a)},getLiquidsQc(){const{qcid:a,lQcid:e}=ke(window.location.search);this.selectLiquidQcId=e||a;const t={qcid:this.selectLiquidQcId};return oe(this.config.liquidsQCApiUrl,{params:t}).then(this.setQcResults)},loadLiquidsQc(){return this.getLiquidsQc().then(this.selectFirstLiquidIfEmpty).then(this.setQcResults).then(this.getLiquidInfo).catch(this.setQcResults)},getPumpDesignProductFamilies(){const{qcid:a,lQcid:e,pumpdesignid:t,lPumpdesignid:s}=ke(window.location.search),n={qcid:e||a,pumpdesign:s||t};return oe(this.config.liquidsPumpDesignProductFamiliesApiUrl,{params:n}).then(this.setVisibleProductFamilies).catch(this.setVisibleProductFamilies)},setVisibleProductFamilies(a){var t,s;const e=(s=(t=a==null?void 0:a.data)==null?void 0:t.productfamilies)!=null?s:[];this.populateTechnicalData(e).then(n=>{this.visibleProductFamilies=A({data:n})})},populateTechnicalData(a){const e=a.map(t=>this.getTechnicalData(t));return Promise.all(e)},getTechnicalData(a){const{typecode:e}=a,t={typecode:e};return oe(this.config.catalogueProductFamilyApiUrl,{params:t}).then(s=>{var l;const n=(l=s==null?void 0:s.data)==null?void 0:l.technicaldata;return Promise.resolve(fe(ne({},a),{technicaldata:n}))})},getProductSubfamilies(a){const{lQcid:e,qcid:t}=ke(window.location.search),s={qcid:e||t,typecode:a};return this.visibleProductFamilies=A(null,this.visibleProductFamilies),oe(this.config.productFamiliesApiUrl,{params:s}).then(this.setVisibleProductFamilies).catch(this.setVisibleProductFamilies)}}),getters:{breadcrumbs(a){var e,t;return(t=(e=a==null?void 0:a.navigationList)==null?void 0:e.data)!=null?t:[]},configQcResults(a){var e;return(e=a.configuration.productFamilyQcResults)==null?void 0:e.data},hasSummaryLink(){var e,t;const a="summary";return(t=(e=this.configQcResults)==null?void 0:e.links)==null?void 0:t.find(s=>s.rel===a)},configQuestions(){var a,e,t;return(t=(e=(a=this.configQcResults)==null?void 0:a.groups[0])==null?void 0:e.questions)!=null?t:[]},configResultsQty(){var e;const a=(e=this.configQuestions)==null?void 0:e.find(t=>t.label==="NoOfHits");return a?Number(a.value):0},liquidInfoData(a){var e,t,s,n;return(n=(s=(t=(e=a.configuration.liquidInfo)==null?void 0:e.data)==null?void 0:t.liquidinfo)==null?void 0:s[0])!=null?n:null},resultsLiquidInfo(a){var s,n,l,o,i,u;const e=(l=(n=(s=a.qcSummary)==null?void 0:s.data)==null?void 0:n.liquidname)!=null?l:null,t=(u=(i=(o=a.qcSummary)==null?void 0:o.data)==null?void 0:i.info)!=null?u:null;return{name:e,infotext:t}},qcSummaryDataInputs(a){var e,t,s;return(s=(t=(e=a.qcSummary)==null?void 0:e.data)==null?void 0:t.input)!=null?s:[]},translatedQcSummaryData(a){var e,t;return(t=(e=a.translatedQcSummary)==null?void 0:e.data)!=null?t:{}},translatedLabelsData(a){var e,t;return(t=(e=a.translatedLabels)==null?void 0:e.data)!=null?t:{}},translatedResultsLiquidInfo(a){var s,n,l,o,i,u;const e=(l=(n=(s=a.translatedQcSummary)==null?void 0:s.data)==null?void 0:n.liquidname)!=null?l:null,t=(u=(i=(o=a.translatedQcSummary)==null?void 0:o.data)==null?void 0:i.info)!=null?u:null;return{name:e,infotext:t}},documentationApiUrl(a){return`${a.config.documentationApiUrl}/${a.productTypeCode}`},qcResultsQuestions(a){var e,t,s,n,l;return(l=(n=(s=(t=(e=a.qcResults)==null?void 0:e.data)==null?void 0:t.groups)==null?void 0:s[0])==null?void 0:n.questions)!=null?l:[]},qcResultsLiquidQuestion(){return this.qcResultsQuestions.find(a=>a.label===Gt.pumpedLiquidSelect)}}}),Ns={class:"cmp-breadcrumb__list",itemscope:"",itemtype:"https://schema.org/BreadcrumbList"},Ms=["data-qa"],zs=["title","href"],Fs=["content"],Gs={key:1,itemprop:"name"},Qs=["href"],Hs=["content"],Vs=G({__name:"CmpLiquidGuideBreadCrumb",props:{labels:{},config:{}},setup(a){const e=a,t=xe(),s=r(()=>{var n;return(n=t.navigationList)==null?void 0:n.data});return Ee(()=>{t.setConfigApis({navListApiUrl:e.config.apiUrl}),t.getNavigationList()}),(n,l)=>s.value?(d(),B(ja,{key:0,"data-qa":"cmp-liquid-guide-breadcrumb",class:"cmp-breadcrumb",labels:n.labels},{default:be(()=>[c("ol",Ns,[(d(!0),f(te,null,le(s.value,(o,i)=>(d(),f("li",{key:o.title,"data-qa":`cmp-liquid-guide-breadcrumb-item-${o.title}`,class:X(["cmp-breadcrumb__list-item",i>0&&"cmp-breadcrumb__list-item--truncate"]),itemprop:"itemListElement",itemscope:"",itemtype:"https://schema.org/ListItem"},[i!==s.value.length-1?(d(),f("a",{key:0,class:"cmp-breadcrumb__link",title:o.title,href:o.path,itemtype:"https://schema.org/Thing",itemprop:"item"},[c("meta",{itemprop:"url",content:o.path},null,8,Fs),c("span",{itemprop:"name",class:X([i==0&&"cmp-breadcrumb__home-icon"])},q(o.navigationTitle),3)],8,zs)):(d(),f("span",Gs,q(o.navigationTitle),1)),i!==s.value.length-1?(d(),f("link",{key:2,itemtype:"https://schema.org/Thing",itemprop:"item",href:n.config.currentPagePath},null,8,Qs)):F("",!0),c("meta",{itemprop:"position",content:o.index+1},null,8,Hs)],10,Ms))),128))])]),_:1},8,["labels"])):F("",!0)}}),ea=()=>{const a=j([]),e=j(!1);return{errors:a,hasErrors:e,handleValidation:(n,l)=>{!l||l.length===0||pt(l)?a.value=a.value.filter(i=>i.name!==n):a.value.some(u=>u.name===n)||a.value.push({name:n,message:l}),e.value=a.value.length>0},hasMissingFields:n=>n.some(l=>{const{mandatory:o,value:i,selectedoptionkey:u}=l;return o&&i===""&&!u})}},Bs={key:0,"data-qa":"liquids-info",class:"cmp-liquids-info"},Ys={class:"cmp-liquids-info__title"},Ws=["innerHTML"],Lt=G({__name:"CmpLiquidGuideInfo",props:{liquidInfo:{}},setup(a){const e=a,t=r(()=>{var l,o;const s="<br>",n=(o=(l=e.liquidInfo)==null?void 0:l.infotext)!=null?o:null;return n?`${n}`.replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g,`$1${s}$2`):null});return(s,n)=>s.liquidInfo&&t.value?(d(),f("div",Bs,[c("div",Ys,q(s.liquidInfo.name),1),c("div",{"data-qa":"liquids-info-text",innerHTML:t.value},null,8,Ws)])):F("",!0)}}),js={"data-qa":"cmp-liquid-guide-configuration",class:"cmp-liquid-details"},Js={class:"b-deck__heading"},Zs={class:"cmp-action-buttons cmp-action-buttons--align-right"},Xs=["disabled"],Ks=G({__name:"CmpLiquidGuideConfiguration",props:{config:{},labels:{},liquidResultsPageUrl:{}},setup(a){const e=a,t=xe(),{hasErrors:s,handleValidation:n,hasMissingFields:l}=ea(),o=r(()=>t.configQuestions),i=r(()=>t.hasSummaryLink),u=r(()=>l(o.value)),p=r(()=>{var I,y;return(y=(I=t.configQcResults)==null?void 0:I.comments)!=null?y:[]}),g=r(()=>t.liquidInfoData),_=r(()=>{var I,y,$;return($=(y=(I=t.configuration)==null?void 0:I.productFamilyQcResults)==null?void 0:y.loading)!=null?$:!0}),m=r(()=>{var I,y;return(y=(I=t.configQcResults)==null?void 0:I.warnings)!=null?y:[]}),b=r(()=>t.configuration.configQcid),h=()=>{const I=ke(window.location.search);I.configQcid=b.value;const y={resultsDisplayed:t.configResultsQty};Ut(Rt.result,y),window.location.href=Ue(e.liquidResultsPageUrl,I)};return Ee(()=>{t.setConfigApis(e.config),t.getLiquidConfiguration(!0),t.getLiquidInfo()}),(I,y)=>(d(),f("div",js,[c("h2",Js,q(I.labels.selectPump),1),o.value?(d(),f("div",{key:0,class:X([`cmp-liquid-details-questions b-layout-grid__item b-layout-grid__item--12 
        b-layout-grid__item--large-7`,_.value&&v(qe).LOADING])},[(d(!0),f(te,null,le(o.value,$=>(d(),B(Ke,Te({ref_for:!0},$,{key:b.value+$.label,"qc-id":b.value,"input-modifier":"inline","update-handler":v(t).updateLiquidsProductFamilyQc,"handle-field-validation":v(n),value:$.value,"qc-api-url":I.config.liquidsQCApiUrl,unitoptions:$.unitoptions,"force-default-tooltip":"","hide-input-if-disabled":""}),null,16,["qc-id","update-handler","handle-field-validation","value","qc-api-url","unitoptions"]))),128))],2)):F("",!0),c("div",Zs,[c("button",{"data-qa":"cmp-liquid-guide-configuration-next-button",type:"button",disabled:v(s)||u.value||!i.value,class:X(["cmp-liquid-guide-configuration__next elm-button","elm-button--positive",_.value&&"elm-button--disabled"]),onClick:h},q(I.labels.nextPage),11,Xs)]),_.value?F("",!0):(d(),B(Xe,{key:1,"data-qa":"cmp-liquid-guide-configuration-alert",messages:p.value,classes:"mod-liquids__alert","show-icon":""},null,8,["messages"])),_.value?F("",!0):(d(),B(Xe,{key:2,"data-qa":"cmp-liquid-guide-configuration-warning",messages:m.value,classes:"mod-liquids__alert","show-icon":"","is-warning":""},null,8,["messages"])),Q(Lt,{"liquid-info":g.value},null,8,["liquid-info"])]))}}),xs={class:"b-deck__inner"},en={class:"b-deck__header"},tn={class:"b-deck__heading"},an={class:"b-layout-grid b-layout-grid--spaced"},sn={id:"pump_designs_list-2",class:"b-layout-grid__group"},nn=["data-qa","onClick"],on={class:"cmp-category-card cmp-category-card--category"},ln={class:"cmp-category-card__content"},cn={class:"cmp-category-card__image"},rn={class:"elm-img elm-img--16-9"},un=["src","alt"],dn={class:"cmp-category-card__meta"},pn={class:"cmp-category-card__info"},mn=["data-qa"],vn=G({__name:"CmpLiquidGuidePumpDesigns",props:{config:{},labels:{},liquidPumpPageUrl:{}},setup(a){const e=a,t=xe(),s=r(()=>{var i;return(i=t.pumpDesigns)==null?void 0:i.loading}),n=r(()=>{var i,u,p;return(p=(u=(i=t.pumpDesigns)==null?void 0:i.data)==null?void 0:u.pumpdesigns)!=null?p:[]}),l=i=>{var u,p,g;return(g=(p=(u=i==null?void 0:i.links)==null?void 0:u[0])==null?void 0:p.href)!=null?g:null},o=i=>{var h;const{lid:u,qcid:p,lQcid:g,wcmmode:_}=ke(window.location.search),m={lid:u,lQcid:g||p,wcmmode:_,lPumpdesignid:i.id},b=(h=i==null?void 0:i.englishname)==null?void 0:h.toLowerCase();Ut(Rt.pumpRange,{liquid:u,pumpDesign:b}),window.location.href=Ue(e.liquidPumpPageUrl,m)};return Ee(()=>{t.getPumpDesigns(e.config.liquidsPumpDesignsApiUrl)}),(i,u)=>(d(),f("div",{"data-qa":"cmp-liquid-guide-pump-designs","aria-live":"polite",class:X(["cmp-liquid-guide-pump-designs b-deck b-deck--full-width",s.value&&v(qe).LOADING])},[c("div",xs,[c("header",en,[c("h2",tn,q(i.labels.pumpDesign),1)]),c("div",an,[c("div",sn,[(d(!0),f(te,null,le(n.value,p=>(d(),f("div",{key:p.displayname+p.productcount,"data-qa":`liquid-pump-designs-item-${p.displayname}`,class:"cmp-liquid-guide-pump-designs__item b-layout-grid__item b-layout-grid__item--12 b-layout-grid__item--medium-6 b-layout-grid__item--large-4",onClick:g=>o(p)},[c("div",on,[c("div",ln,[c("div",cn,[c("div",rn,[c("img",{src:l(p),alt:p.displayname,class:"elm-img__asset"},null,8,un)])])]),c("div",dn,[c("div",pn,[c("h3",{"data-qa":`liquid-pump-designs-item-heading-${p.displayname}`,class:"cmp-category-card__heading"},q(p.displayname),9,mn)])])])],8,nn))),128))])])])],2))}}),gn={class:"b-deck__inner"},_n={class:"b-deck__header"},bn={class:"b-deck__heading"},hn={class:"b-layout-grid b-layout-grid--spaced"},fn={class:"b-layout-grid__group"},Cn=["data-qa"],yn={class:"cmp-liquid-guide-pump-families__card cmp-catalogue-card cmp-catalogue-card--range"},qn=["title","onClick"],$n={class:"cmp-catalogue-card__content"},In={class:"cmp-catalogue-card__image cmp-catalogue-card__image--inset"},Pn={"data-component-root":""},kn={class:"elm-img elm-img--4-3"},Sn=["src"],An={class:"cmp-catalogue-card__meta"},En=["data-qa"],Rn={class:"cmp-catalogue-card__heading"},Un={class:"cmp-catalogue-card__description"},Ln={class:"cmp-catalogue-card__spec"},Tn=["aria-label"],wn=["data-qa"],Dn={class:"cmp-catalogue-card__spec-cell cmp-catalogue-card__spec-cell--key"},On={class:"cmp-catalogue-card__spec-cell cmp-catalogue-card__spec-cell--value"},Nn={class:"cmp-catalogue-card__actions"},Mn={"data-qa":"liquid-pump-families-item-action",class:"cmp-catalogue-card__action-item"},zn={class:"cmp-catalogue-card__action-link elm-link elm-link--block"},Fn=G({__name:"CmpLiquidGuidePumpFamilies",props:{config:{},labels:{},liquidPumpConfigurationPageUrl:{}},setup(a){const e=a,t=xe(),s=j(null),n=j({}),l=j({}),o=r(()=>{var m;return(m=t.visibleProductFamilies)==null?void 0:m.loading}),i=r(()=>{var m,b;return(b=(m=t.visibleProductFamilies)==null?void 0:m.data)!=null?b:[]}),u=m=>{const b=ne({lTypecode:m},ke(window.location.search));s.value&&(b.familyCode=s.value),n.value={product:m},Ut(Rt.pumpDetails,l.value,n.value),window.location.href=Ue(e.liquidPumpConfigurationPageUrl,b)},p=(m,b)=>{l.value.viewBy=b===1?"range":"product",n.value={range:m},s.value=m,qt({familyCode:s.value}),t.getProductSubfamilies(m)},g=m=>{var b,h,I;return(I=(h=(b=m==null?void 0:m.links)==null?void 0:b[0])==null?void 0:h.href)!=null?I:null},_=m=>{var I,y;const b=!(m!=null&&m.isleaf),h=(I=m==null?void 0:m.typecode)!=null?I:null;if(b){const $=(y=m==null?void 0:m.level)!=null?y:null;p(h,$)}else u(h)};return Ee(()=>{t.setConfigApis(e.config),t.getPumpDesignProductFamilies()}),(m,b)=>(d(),f("div",{class:X(["cmp-liquid-guide-pump-families b-deck b-deck--full-width b-theme b-theme--subtle",o.value&&v(qe).LOADING]),"data-qa":"cmp-liquid-guide-pump-families"},[c("div",gn,[c("header",_n,[c("h2",bn,q(m.labels.pumps),1)]),c("div",hn,[c("div",fn,[(d(!0),f(te,null,le(i.value,h=>(d(),f("div",{key:h.name,"data-qa":`liquid-pump-families-item-${h.name}`,class:"b-layout-grid__item b-layout-grid__item--12 b-layout-grid__item--large-4"},[c("div",yn,[c("a",{title:h.name,class:"cmp-catalogue-card__link",onClick:I=>_(h)},[c("div",$n,[c("div",In,[c("div",Pn,[c("div",kn,[c("img",{src:g(h),alt:"",class:"elm-img__asset"},null,8,Sn)])])])]),c("div",An,[c("div",{"data-qa":`liquid-pump-families-item-${h.name}-info`,class:"cmp-catalogue-card__info"},[c("h3",Rn,q(h.name),1),c("p",Un,q(h.description),1)],8,En),c("div",Ln,[c("table",{class:"cmp-catalogue-card__spec-table","aria-label":m.labels.pumps},[c("tbody",null,[(d(!0),f(te,null,le(h.technicaldata,I=>(d(),f("tr",{key:I.label,"data-qa":`liquid-pump-families-item-technical-data-${I.label}`,class:"cmp-catalogue-card__spec-row"},[c("th",Dn,q(I.label),1),c("td",On,q(I.convvalue)+" "+q(I.convunit),1)],8,wn))),128))])],8,Tn)])]),c("div",Nn,[c("div",Mn,[c("div",zn,q(m.labels.select),1)])])],8,qn)])],8,Cn))),128))])])])],2))}}),Gn={class:"cmp-form-options__legend"},Qn=G({__name:"CmpPrintCommonLiquidsContent",props:{labels:{},setPrintData:{type:Function},printData:{}},setup(a){return(e,t)=>{var s,n,l,o,i,u,p,g;return d(),f(te,null,[c("legend",Gn,q(e.labels.liquid),1),Q(at,{id:"printinput-userselection",label:e.labels.userSelection,value:(n=(s=e.printData)==null?void 0:s.plgreport)==null?void 0:n.userselection,onInput:t[0]||(t[0]=_=>e.setPrintData("plgreport","userselection",_.target.checked))},null,8,["label","value"]),Q(at,{id:"printinput-liquidinformation",label:e.labels.liquidInformation,value:(o=(l=e.printData)==null?void 0:l.plgreport)==null?void 0:o.liquidinformation,onInput:t[1]||(t[1]=_=>e.setPrintData("plgreport","liquidinformation",_.target.checked))},null,8,["label","value"]),Q(at,{id:"printinput-comments",label:e.labels.comments,value:(u=(i=e.printData)==null?void 0:i.plgreport)==null?void 0:u.comments,onInput:t[2]||(t[2]=_=>e.setPrintData("plgreport","comments",_.target.checked))},null,8,["label","value"]),Q(at,{id:"printinput-warnings",label:e.labels.warnings,value:(g=(p=e.printData)==null?void 0:p.plgreport)==null?void 0:g.warnings,onInput:t[3]||(t[3]=_=>e.setPrintData("plgreport","warnings",_.target.checked))},null,8,["label","value"])],64)}}}),Hn={class:"b-layout-grid__group"},Vn={class:"b-layout-grid__item b-layout-grid__item--12 b-layout-grid__item--medium-6"},Bn={class:"b-layout-grid__item b-layout-grid__item--12 b-layout-grid__item--medium-6 cmp-print-second-column"},Yn={class:"cmp-form-options__legend"},Wn=G({__name:"CmpPrintLiquidsContent",props:{labels:{}},setup(a){const e=ot(),t=s=>{const n=s.target;e.updateLanguageCode(n.value)};return(s,n)=>{var l,o,i;return d(),f("div",Hn,[c("div",Vn,[Q(Qn,{labels:s.labels,"set-print-data":v(e).setPrintData,"print-data":v(e).printSettings},null,8,["labels","set-print-data","print-data"])]),c("div",Bn,[c("legend",Yn,q(s.labels.language),1),Q(Ja,{id:"cmp-print-language-select",label:(l=s.labels)==null?void 0:l.language,value:(o=v(e))==null?void 0:o.languageCode,options:(i=v(e))==null?void 0:i.userLanguages,"value-update-handler":t},null,8,["label","value","options"])])])}}}),jn=G({__name:"CmpPrintLiquids",props:{labels:{},config:{},toggleHandler:{type:Function},liquidName:{}},setup(a){const e=a,t=xe(),s=fe(ne({},e.labels),{subHeading:e.labels.liquidsGeneralInfo});return Ee(()=>{t.setLiquidData()}),(n,l)=>{var o;return d(),B(Wt,{"toggle-handler":n.toggleHandler,labels:s,config:n.config,"document-name":`PLG - ${n.liquidName}`,"print-type":v($t).liquids,"content-component":Wn,"qc-id":(o=v(t).configQcResults)==null?void 0:o.id},null,8,["toggle-handler","config","document-name","print-type","qc-id"])}}}),Jn={class:"elm-button__text"},Zn=G({__name:"ElmPrintLiquidsButton",props:{labels:{},config:{},liquidName:{}},setup(a,{expose:e}){const t=a,s=Se(),n=l=>{var o;s.setOverlayComponent({component:jn,heading:(o=t.labels)==null?void 0:o.heading,props:{class:"cmp-print print-modal",labels:t.labels,liquidName:t.liquidName,config:{languagesApiUrl:t.config.languagesApiUrl,preparePdfApiUrl:t.config.preparePdfApiUrl,printGetPdfApiUrl:t.config.printGetPdfApiUrl,userLogoApiUrl:t.config.userLogoApiUrl}},origin:{target:l.target,x:l.clientX,y:l.clientY}})};return e({openPrintLiquidsOverlay:n}),(l,o)=>(d(),f("button",{type:"button",class:"elm-button","data-qa":"cmp-liquid-guide-results-print",onClick:st(n,["prevent"])},[c("span",Jn,q(l.labels.openPrint),1)]))}}),Xn={class:"cmp-liquid-guide-results__data"},Kn={class:"b-deck__heading"},xn={"data-qa":"liquid-guide-results-info",class:"cmp-liquid-guide-results__data-info"},eo=["data-qa"],to=["data-qa"],ao=["data-qa"],so={key:0,"data-qa":"cmp-liquid-guide-results-no-matching-product",class:"cmp-liquid-guide-results__data-no-matching-product"},no={class:"cmp-action-buttons cmp-action-buttons--align-right"},oo=["disabled"],lo={"data-qa":"cmp-liquid-guide-results-documentation",class:"cmp-liquid-guide-results__documentation"},io={class:"b-deck__header"},co={class:"b-deck__heading"},ro={class:"cmp-alert"},uo={"data-qa":"cmp-liquid-guide-results-documentation-alert",class:"cmp-text cmp-alert__message cmp-liquid-guide-results-documentation-alert"},po={class:"b-layout-grid"},mo={class:"b-layout-grid__group"},vo={class:"b-layout-grid__item b-layout-grid__item--12"},go=G({__name:"CmpLiquidGuideResults",props:{config:{},labels:{},sizePageUrl:{}},setup(a,{expose:e}){const t=a,s=j(!1),n=xe(),l=["language-liquids-group"],o=r(()=>{var C,k,U;return(U=(k=(C=n.qcSummary)==null?void 0:C.data)==null?void 0:k.comments)!=null?U:[]}),i=r(()=>{var C,k,U;return((C=n.qcSummary)==null?void 0:C.loading)||((U=(k=n.configuration)==null?void 0:k.productFamilyQcResults)==null?void 0:U.loading)||s.value}),u=r(()=>{var C,k,U;return(U=(k=(C=n.qcSummary)==null?void 0:C.data)==null?void 0:k.warnings)!=null?U:[]}),p=r(()=>{const{languagesApiUrl:C,preparePdfApiUrl:k,printGetPdfApiUrl:U,userLogoApiUrl:P,printSettingsApiUrl:O,liquidsPrintSettingsEnabled:z}=t.config;return{languagesApiUrl:C,preparePdfApiUrl:k,printGetPdfApiUrl:U,userLogoApiUrl:P,printSettingsApiUrl:O,liquidsPrintSettingsEnabled:z}}),g=r(()=>n.configResultsQty===0),_=r(()=>n.qcSummaryDataInputs),m=r(()=>n.translatedQcSummaryData),b=r(()=>n.resultsLiquidInfo),h=r(()=>n.translatedLabelsData),I=r(()=>n.translatedResultsLiquidInfo),y=r(()=>n.configResultsQty),$=r(()=>`${t.config.documentationApiUrl}/${n.productTypeCode}`),M=r(()=>n.qcResultsQuestions),H=r(()=>n.configQuestions),R=()=>{g.value||(s.value=!0,n.getLiquidSizingQc().then(C=>{var U;const k=(U=C==null?void 0:C.data)==null?void 0:U.id;window.location.href=`${t.sizePageUrl}?sQcid=${k}&plg=true`}).finally(()=>{s.value=!1}))},L=C=>(C||[]).filter(k=>!k.hidden).map(k=>Ca(k,!0)),D=C=>ve(this,null,function*(){var P,O,z,V,J,w,E,N,ee,se;const k={};k.printSettings=C!=null?C:{},yield n.getQcSummary(!0,C.languageCode),yield n.getTranslatedLabels(C.languageCode);const U=(O=(P=m.value)==null?void 0:P.input)==null?void 0:O.map((Z,ie)=>({title:Z.title,values:L(ie===0?M==null?void 0:M.value:H==null?void 0:H.value)}));return k.selectedCharacteristics=U,k.liquidInfo=fe(ne({},I.value),{infotext:Xa((z=I.value)==null?void 0:z.infotext)}),k.comments=(J=(V=m.value)==null?void 0:V.comments)!=null?J:o.value,k.warnings=(E=(w=m.value)==null?void 0:w.warnings)!=null?E:u.value,k.labels=(se=h.value)!=null?se:(ee=(N=t.labels)==null?void 0:N.print)==null?void 0:ee.pdf,Promise.resolve(k)});return Ee(()=>{n.setConfigApis(t.config),n.getQcSummary(),n.getLiquidsQc(),n.getLiquidConfiguration()}),e({mapPrintRequestBody:D}),(C,k)=>{var U;return d(),f("section",{class:X(["cmp-liquid-guide-results",i.value&&v(qe).LOADING]),"data-qa":"cmp-liquid-guide-results"},[c("div",Xn,[c("h2",Kn,q(C.labels.result),1),c("div",xn,[(d(!0),f(te,null,le(_.value,P=>(d(),f("div",{key:P.title,"data-qa":`liquid-guide-results-info-${P.title}`,class:"cmp-liquid-guide-results__data-info-item"},[c("h3",null,q(P.title),1),c("ul",{"data-qa":`liquid-guide-results-info-${P.title}-values`},[(d(!0),f(te,null,le(P.values||[],O=>(d(),f("li",{key:O.value+O.name,"data-qa":`liquid-guide-results-info-${P.title}-value-${O.name}`},q(O.name)+": "+q(O.value),9,ao))),128))],8,to)],8,eo))),128))]),g.value?(d(),f("div",so,q(C.labels.noMatchingProduct),1)):F("",!0),c("div",no,[c("button",{type:"button","data-qa":"cmp-liquid-guide-results-find-products",disabled:g.value||s.value,class:X(["cmp-liquid-guide-results__find-products elm-button","elm-button--positive"]),onClick:R},[ze(q(C.labels.findProduct)+" ( ",1),c("span",null,q(y.value),1),k[0]||(k[0]=ze(" ) "))],8,oo),Q(Zn,{"data-qa":"cmp-liquid-guide-results-print",config:p.value,labels:C.labels.print,"liquid-name":b.value.name},null,8,["config","labels","liquid-name"]),C.config.liquidsPrintSettingsEnabled?(d(),B(jt,{key:0,disabled:g.value||s.value,"template-id":"Liquids","setting-id":"liquids","settings-right-column":l,"button-text-override":`${((U=C.labels.print)==null?void 0:U.text)||""} - Print Jasper`,"map-print-request-body":D,config:C.config,labels:C.labels.print},null,8,["disabled","button-text-override","config","labels"])):F("",!0)]),o.value.length>0?(d(),B(Xe,{key:1,class:"cmp-liquid-guide-results-comments","data-qa":"cmp-liquid-guide-results-alert",messages:o.value,classes:"mod-liquids__alert","show-icon":""},null,8,["messages"])):F("",!0),u.value.length>0?(d(),B(Xe,{key:2,class:"cmp-liquid-guide-results-warnings","data-qa":"cmp-liquid-guide-results-warning",messages:u.value,classes:"mod-liquids__alert","show-icon":"","is-warning":""},null,8,["messages"])):F("",!0),Q(Lt,{"liquid-info":b.value},null,8,["liquid-info"])]),c("div",lo,[c("header",io,[c("h2",co,q(C.labels.documentation),1)]),c("div",ro,[c("div",uo,[c("p",null,[c("strong",null,q(C.labels.documentNote)+"!",1),ze(" "+q(C.labels.documentWarning),1)])])]),c("div",po,[c("div",mo,[c("div",vo,[Q(Za,{id:"documentation-list","data-qa":"cmp-liquid-guide-results-documentation-list",labels:C.labels,config:{apiUrl:$.value},class:"mod-result-list","results-component":Ka},null,8,["labels","config","results-component"])])])])])],2)}}}),_o={"data-qa":"cmp-liquid-guide-select-liquid","aria-live":"polite",class:"cmp-liquid-guide-select-liquid"},bo={class:"b-deck__heading"},ho={class:"cmp-action-buttons cmp-action-buttons--align-right"},fo=["disabled"],Co=G({__name:"CmpLiquidGuideSelectLiquid",props:{config:{},labels:{},liquidPumpDesignPageUrl:{}},setup(a){const e=a,t=xe(),s=r(()=>t.qcResultsQuestions),{hasErrors:n,handleValidation:l,hasMissingFields:o}=ea(),i=j(!1),u=j(!1),p=r(()=>{var R,L,D;return(D=(L=(R=t.qcResults)==null?void 0:R.data)==null?void 0:L.comments)!=null?D:[]}),g=r(()=>{var R,L,D;return(D=(L=(R=t.qcResults)==null?void 0:R.data)==null?void 0:L.warnings)!=null?D:[]}),_=r(()=>o(s.value)),m=r(()=>{var R,L,D;return(D=(L=(R=t.qcResults)==null?void 0:R.data)==null?void 0:L.links)==null?void 0:D.find(C=>C.rel===Gt.pumpDesignLinkRel)}),b=r(()=>{var R,L;return(L=(R=t.qcResults)==null?void 0:R.loading)!=null?L:!1}),h=r(()=>t.liquidInfoData),I=r(()=>t.selectLiquidQcId),y=()=>{const R=I.value,L=t.liquidId,{wcmmode:D}=ke(window.location.search);Ut(Rt.pumpDesigns,{liquid:L});const C={lQcid:R,lid:L,wcmmode:D};window.location.href=Ue(e.liquidPumpDesignPageUrl,C)},$=()=>{u.value&&(u.value=!1,_.value||y())},M=R=>{i.value=!1,t.updateLiquidsQc(R).then(t.setQcResults).then($).then(t.getLiquidInfo).catch(t.setQcResults)},H=()=>{i.value=!0,b.value?u.value=!0:m.value&&y()};return Ee(()=>{t.setConfigApis(e.config),t.loadLiquidsQc()}),(R,L)=>(d(),f("div",_o,[c("h2",bo,q(R.labels.selectLiquid),1),s.value?(d(),f("div",{key:0,class:X(["cmp-liquid-guide-select-liquid__questions b-layout-grid__item b-layout-grid__item--12 b-layout-grid__item--large-7",b.value&&v(qe).LOADING])},[(d(!0),f(te,null,le(s.value,D=>(d(),B(Ke,Te({ref_for:!0},D,{key:I.value+D.label,"data-qa":`liquid-guide-select-liquid-question-${D.label}`,"qc-id":I.value,"input-modifier":"inline","update-handler":M,"handle-field-validation":v(l),value:D.value,"qc-api-url":R.config.liquidsQCApiUrl,unitoptions:D.unitoptions,"show-missing-inputs":i.value,"force-default-tooltip":""}),null,16,["data-qa","qc-id","handle-field-validation","value","qc-api-url","unitoptions","show-missing-inputs"]))),128))],2)):F("",!0),c("div",ho,[c("button",{type:"button","data-qa":"cmp-liquid-guide-select-liquid-next-button",disabled:v(n)||_.value||!m.value,class:X(["cmp-liquid-guide-select-liquid__next elm-button","elm-button--positive",b.value&&"elm-button--disabled"]),onClick:H},q(R.labels.nextPage),11,fo)]),Q(Xe,{"data-qa":"cmp-liquid-guide-select-liquid-alert",messages:p.value,classes:"mod-liquids__alert","show-icon":"","is-warning":""},null,8,["messages"]),Q(Xe,{"data-qa":"cmp-liquid-guide-select-liquid-warning",messages:g.value,classes:"mod-liquids__alert","show-icon":"","is-warning":""},null,8,["messages"]),Q(Lt,{"liquid-info":h.value},null,8,["liquid-info"])]))}}),La=G({__name:"CmpPrintCompareProductOptions",props:{checkboxLabel:{},selectableProductsOption:{}},setup(a){const e=a,t=r(()=>{var n;const s=[];return(n=e.selectableProductsOption)==null||n.forEach(l=>{const{value:o,label:i}=l;s.push({value:o,label:i})}),s});return(s,n)=>{const l=gt("CmpFormFieldMultipleCheckbox");return d(),B(l,{id:"cmp-print-compare-product-options",name:"selectedProducts",required:"",label:s.checkboxLabel,options:t.value},null,8,["label","options"])}}}),yo={"data-qa":"compare-products-table-group",class:"cmp-compare-products-table__group"},qo={key:0,"data-qa":"compare-products-table-group-headers"},$o=["id"],Io=["id","data-qa","headers"],Po=["data-qa","aria-expanded"],ko={class:"cmp-compare-products-table__group-toggle-inner"},So=["data-qa","aria-hidden"],Ao=["id","data-qa","headers"],Eo=["data-qa","headers"],Ro=["data-header"],va=G({__name:"CmpCompareProductsTableGroup",props:{collapsable:{type:Boolean},comparisonlist:{},id:{},itemCount:{},name:{}},setup(a){const e=a,t=j(!1),s=r(()=>`${e.id}-blank`),n=r(()=>e.collapsable?!t.value:!1),l=()=>{t.value=!t.value},o=u=>`${e.id}-col-${u}`,i=u=>`${e.id}-row-${u}`;return(u,p)=>(d(),f("tbody",yo,[u.collapsable?(d(),f("tr",qo,[c("td",{id:s.value,class:"h-hidden"}," ",8,$o),(d(!0),f(te,null,le(u.itemCount,g=>(d(),f("th",{id:o(g-1),key:`col-${g}`,"data-qa":`compare-products-table-group-header-${g}`,headers:s.value,class:"cmp-compare-products-table__cell cmp-compare-products-table__cell--toggle"},[c("button",{"data-qa":`compare-products-table-group-header-button-${g}`,"aria-expanded":t.value?"true":"false",class:"cmp-compare-products-table__group-toggle",type:"button",onClick:l},[c("span",ko,q(u.name),1)],8,Po)],8,Io))),128))])):F("",!0),(d(!0),f(te,null,le(u.comparisonlist,(g,_)=>(d(),f("tr",{key:`row-${_}`,"data-qa":`compare-products-table-group-content-${_}`,"aria-hidden":n.value?"true":void 0,class:"cmp-compare-products-table__group-content"},[c("th",{id:i(_),"data-qa":`compare-products-table-group-content-header${_}`,headers:s.value,class:"h-hidden"},q(g.text),9,Ao),(d(!0),f(te,null,le(g.values,(m,b)=>(d(),f("td",{key:`cell-${_}-${b}`,"data-qa":`compare-products-table-group-content-cell-${_}`,headers:`${i(_)} ${o(b)}`,class:X(["cmp-compare-products-table__cell","cmp-compare-products-table__cell--body",u.itemCount>1&&g.allequal&&"cmp-compare-products-table__cell--equal",u.itemCount>1&&!g.allequal&&"cmp-compare-products-table__cell--not-equal"])},[c("span",{"data-header":g.text,class:"cmp-compare-products-table__cell-inner"},q(m||"-"),9,Ro)],10,Eo))),128))],8,So))),128))]))}}),Uo={"data-qa":"cmp-compare-info-row-container"},Lo={"data-qa":"cmp-compare-info-row"},To=["data-qa"],wo={class:"mod-compare-products__product-actions"},Do=["data-qa","onClick"],Oo=["data-qa","onClick"],No=G({__name:"CmpCompareProductInfoRow",props:{labels:{},items:{}},setup(a){const e=xt("onEdit"),{dutyPoints:t,lcc:s}=e||{};return(n,l)=>(d(),f("tbody",Uo,[c("tr",Lo,[l[0]||(l[0]=c("td",{class:"h-hidden"}," ",-1)),(d(!0),f(te,null,le(n.items,o=>(d(),f("td",{key:o.id,"data-qa":`cmp-compare-info-row-cell-${o.id}`,class:"cmp-compare-products-table__cell cmp-compare-products-table__cell--body"},[c("div",wo,[c("button",{"data-qa":`cmp-compare-info-row-edit-duty-points-${o.id}`,class:"elm-link",type:"button",onClick:i=>{var u;return(u=v(t))==null?void 0:u(i,[o])}},q(n.labels.editDutyPoint),9,Do),c("button",{"data-qa":`cmp-compare-info-row-edit-lcc-${o.id}`,class:"elm-link",type:"button",onClick:i=>{var u;return(u=v(s))==null?void 0:u(i,o)}},q(n.labels.editLcc),9,Oo)])],8,To))),128))])]))}}),Mo={"data-qa":"cmp-compare-product-actions-row"},zo=["data-qa"],Fo={class:"cmp-compare-products-table__actions"},Go={class:"cmp-action-buttons cmp-action-buttons--inline"},Qo=G({__name:"CmpCompareProductActionsRow",props:{labels:{},items:{}},setup(a){return(e,t)=>(d(),f("tr",Mo,[t[0]||(t[0]=c("td",{class:"h-hidden"}," ",-1)),(d(!0),f(te,null,le(e.items,s=>(d(),f("td",{key:s.id,"data-qa":`cmp-compare-product-actions-row__cell-${s.id}`,class:"cmp-compare-products-table__cell cmp-compare-products-table__cell"},[c("div",Fo,[c("div",Go,[Q(xa,{id:`compare-action-add-to-project-${s.id}`,"data-qa":`cmp-compare-add-to-project-button-${s.id}`,labels:e.labels.addToProject,"pump-system-id":s.pumpsystemid,class:"elm-action-button elm-action-button--subtle elm-action-button--add-project cmp-action-buttons__action"},null,8,["id","data-qa","labels","pump-system-id"]),Q(es,{id:`compare-action-add-to-quotation-${s.id}`,"data-qa":`cmp-compare-add-to-quotation-button-${s.id}`,labels:e.labels.addToQuotation,"pump-system-ids":[s.pumpsystemid],class:"elm-action-button elm-action-button--subtle elm-action-button--add-quotation cmp-action-buttons__action"},null,8,["id","data-qa","labels","pump-system-ids"]),Q(ts,{id:`compare-action-add-to-saved-products-${s.id}`,"data-qa":`cmp-compare-add-to-saved-products-button-${s.id}`,labels:e.labels.save,"pump-system-ids":[s.pumpsystemid],class:"elm-action-button elm-action-button--subtle elm-action-button--save cmp-action-buttons__action"},null,8,["id","data-qa","labels","pump-system-ids"])]),Q(as,{id:`compare-action-delete-from-compare-${s.id}`,"data-qa":`cmp-compare-delete-button-${s.id}`,labels:e.labels.delete,ids:[s.id],class:"elm-action-button elm-action-button--subtle elm-action-button--trash-can_outline cmp-compare-products-table__action"},null,8,["id","data-qa","labels","ids"])])],8,zo))),128))]))}}),Ho={key:0,"data-qa":"cmp-compare-products-table-curves-row"},Vo={"data-qa":"cmp-compare-products-table-curves-row__cell-hidden",class:"h-hidden",scope:"row"},Bo=["data-qa"],Yo=G({__name:"CmpCompareProductCurvesRow",props:{labels:{}},setup(a){const e=xt("curves"),{pixelRatio:t}=Ua(),s=r(()=>Math.round(t.value*96));return(n,l)=>{var o;return(o=v(e))!=null&&o.length?(d(),f("tr",Ho,[c("th",Vo,q(n.labels.curve),1),(d(!0),f(te,null,le(v(e),(i,u)=>(d(),f("td",{key:u+v(Ct)(),"data-qa":`cmp-compare-products-table-curves-row__cell-${u}`,class:"cmp-compare-products-table__cell"},[i.exist?(d(),B(ya,{key:0,"data-qa":`cmp-compare-products-table-curves-row__cell-${u}-img`,src:{src:`${i.src}&dpi=${s.value}&w=600&h=450`,width:600,height:450},alt:i.src,class:"elm-img elm-img--4-3"},null,8,["data-qa","src","alt"])):F("",!0)],8,Bo))),128))])):F("",!0)}}}),Wo={"data-qa":"cmp-compare-details-names"},jo={"data-qa":"cmp-compare-details-names--cell-hidden",class:"h-hidden",scope:"row"},Jo=["data-qa"],Zo={"data-qa":"cmp-compare-details-numbers"},Xo={"data-qa":"cmp-compare-details-numbers__cell-hidden",class:"h-hidden",scope:"row"},Ko=["data-qa","data-header"],xo={"data-qa":"cmp-compare-details-actions"},el=["data-qa"],tl=["data-qa","href"],al={class:"elm-button__text"},sl=G({__name:"CmpCompareProductDetailsRows",props:{labels:{}},setup(a){const e=xt("productDetails"),{productUrl:t,productNumbers:s,productNames:n,pumpSystemIds:l}=e.value,o=i=>Ue(t,{pumpsystemid:i});return(i,u)=>(d(),f(te,null,[c("tr",Wo,[c("th",jo,q(i.labels.headers.name),1),(d(!0),f(te,null,le(v(n),p=>(d(),f("th",{key:p.name+v(Ct)(),"data-qa":`cmp-compare-details-names__cell-${p.name}`,class:"cmp-compare-products-table__cell cmp-compare-products-table__cell--head",scope:"col"},q(p.name),9,Jo))),128))]),c("tr",Zo,[c("th",Xo,q(i.labels.headers.productNumber),1),(d(!0),f(te,null,le(v(s),p=>(d(),f("td",{key:p+v(Ct)(),"data-qa":`cmp-compare-details-numbers__cell-${p}`,"data-header":`${i.labels.headers.productNumber} `,class:"cmp-compare-products-table__cell cmp-compare-products-table__cell--subhead"},q(p),9,Ko))),128))]),c("tr",xo,[u[0]||(u[0]=c("td",{"data-qa":"cmp-compare-details-actions__cell-hidden",class:"h-hidden"}," ",-1)),(d(!0),f(te,null,le(v(l),p=>(d(),f("td",{key:p+v(Ct)(),"data-qa":`cmp-compare-details-actions__cell-${p}`,class:"cmp-compare-products-table__cell cmp-compare-products-table__cell--body"},[c("a",{"data-qa":`cmp-compare-details-actions__cell-${p}-button`,href:o(p),class:"elm-button elm-button--small cmp-compare-products-table__cell-button"},[c("span",al,q(i.labels.viewProduct),1)],8,tl)],8,el))),128))])],64))}}),nl={"data-qa":"cmp-compare-products-table-container"},ol=["aria-label"],ll={"data-qa":"cmp-compare-products-table-colgroup"},il=["data-qa"],cl={"data-qa":"cmp-compare-products-table-col-hidden-row",class:"hidden"},rl=["data-qa"],ul=G({__name:"CmpCompareProductsTable",props:{labels:{},showCurves:{type:Boolean},items:{},compareData:{},compareLccData:{},colWidth:{}},setup(a){return(e,t)=>(d(),f("div",nl,[e.items.length?(d(),B(ss,{key:0,"data-qa":"cmp-compare-products-table-scroll",class:"cmp-horizontal-scroll--large","use-offset":"","shift-factor":".cmp-compare-products-table__cell"},{content:be(()=>{var s;return[c("table",{"data-qa":"cmp-compare-products-table",class:X(["cmp-compare-products-table__table","cmp-compare-products-table__table--padded"]),"aria-label":e.labels.compare.compareProducts,style:Pt({width:`${((s=e.colWidth)!=null?s:320)*e.items.length}px`})},[c("colgroup",ll,[t[0]||(t[0]=c("col",null,null,-1)),(d(!0),f(te,null,le(e.items,n=>{var l;return d(),f("col",{key:n.id,"data-qa":`cmp-compare-products-table-col-${n.id}`,style:Pt({width:`${(l=e.colWidth)!=null?l:320}px`}),class:"cmp-compare-products-table__col"},null,12,il)}),128))]),c("tr",cl,[(d(!0),f(te,null,le(e.items,n=>(d(),f("th",{key:n.id,"data-qa":`cmp-compare-products-table-col-th-${n.id}--hidden`,scope:"col",class:"h-hidden"},q(n.id),9,rl))),128))]),c("thead",null,[Q(Qo,{labels:e.labels.actions,items:e.items},null,8,["labels","items"]),e.showCurves?(d(),B(Yo,{key:0,labels:e.labels.compare.headers},null,8,["labels"])):F("",!0),Q(sl,{labels:e.labels.compare},null,8,["labels"])]),e.compareLccData?(d(),B(va,Te({key:0},e.compareLccData,{"item-count":e.items.length}),null,16,["item-count"])):F("",!0),Q(No,{labels:e.labels.actions,items:e.items},null,8,["labels","items"]),(d(!0),f(te,null,le(e.compareData,n=>(d(),B(va,Te({ref_for:!0},n,{key:n.id,"item-count":e.items.length,collapsable:""}),null,16,["item-count"]))),128))],12,ol)]}),_:1})):F("",!0)]))}});var Ta=(a=>(a.PRODUCTNUMBER="productnumber",a.PUMPSYSTEMID="pumpsystemid",a.COMPARISON_GRAPH="comparisongraph",a))(Ta||{}),wa=(a=>(a.COMPARISON_PRINT="Comparison Print",a))(wa||{}),Fe=(a=>(a.COMPLETE_CONFIG="gpcChangeConfigurationComplete",a.CANCEL_CONFIG="gpcChangeConfigurationCancel",a.ADD_CANVAS="gpcCurveCanvasAddCanvas",a.DELETE_CANVAS="gpcCurveCanvasDeleteCanvas",a.ADD_PRODUCT_TO_CANVAS="gpcCurveCanvasAddProduct",a.CURVE_CANVAS_USER_PRICE="gpcCurveCanvasUserDefinedPrice",a.CURVE_CANVAS_CUE="gpcCurveCanvasCUE",a.GPC_PRINT_FILE="gpcPrintFile",a))(Fe||{}),Qt=(a=>(a.PRODUCT="product",a.PROFILE="profile",a))(Qt||{}),Da=(a=>(a.GENERAL_TAB_ID="General",a))(Da||{}),ct=(a=>(a.TEXT="text",a.OPTIONS="options",a.CONVERTED="convertedUnit",a.REGULAR="unitOfMeasure",a))(ct||{}),rt=(a=>(a.UxHero="UxHero",a.UxRequired="UxRequired",a.UxParameters="UxParameters",a.Flow="Flow",a.Head="Head",a.RequiredFlow="DimFlow1",a.RequiredHead="TotalHead1",a.Requirements="Requirements",a))(rt||{}),Ht=(a=>(a[a.ImgHeight=560]="ImgHeight",a[a.ImgWidth=560]="ImgWidth",a))(Ht||{}),ut=(a=>(a.Price="Price",a.Cue="CUE",a.HasUserPrice="HasUserDefinedPrice",a.CueProduct="CueProduct",a))(ut||{}),Me=(a=>(a.P2KW="P2KW",a.PHASE="PHASE",a.Energy="Energy",a.LCC="LCC",a.PRODNAME="PRODNAME",a.Price="Price",a.SPEED_RPM="SPEED_RPM",a.U="U",a))(Me||{}),Vt=(a=>(a.COMPARISON_CURVE="comparisoncurve",a.CAPACITY_RANGE="sizing-compare-load-profile",a))(Vt||{});const dl=G({__name:"CmpComparePrint",props:{labels:{},config:{},toggleHandler:{type:Function},selectableProductsForPrint:{}},setup(a){return(e,t)=>(d(),B(Wt,{"data-qa":"cmp-compare-print-container","toggle-handler":e.toggleHandler,labels:e.labels,config:e.config,"document-name":v(wa).COMPARISON_PRINT,"print-type":v($t).compare,"saved-property-list":[v(Ta).COMPARISON_GRAPH],"content-component":ns,"selectable-products-for-print":e.selectableProductsForPrint},null,8,["toggle-handler","labels","config","document-name","print-type","saved-property-list","selectable-products-for-print"]))}}),it={DPI:96,RATIOS:{DESKTOP:[21,9],MOBILE:[4,3]},IMG_WIDTHS:[360,480,768,1024,1200]},pl={"data-qa":"cmp-compare-products-graph-image-container"},ml=G({__name:"CmpCompareProductsGraph",props:{labels:{},pumpSystemIds:{},src:{}},setup(a){const e=a,t=Se(),s=r(()=>t.isMobile),n=r(()=>s.value?it.RATIOS.MOBILE:it.RATIOS.DESKTOP),l=(p,g=[])=>{const _=Ue(e.src,{fmt:"png",dpi:Math.round(window.devicePixelRatio*it.DPI),pumpsystemid:p.join(",")}),m=n.value[1]/n.value[0];return g.map(b=>({src:_,height:Math.round(b*m),width:b}))},o=r(()=>l(e.pumpSystemIds,[it.IMG_WIDTHS[0]])[0]),i=r(()=>l(e.pumpSystemIds,it.IMG_WIDTHS)),u=()=>{t.setNotification({type:mt.ERROR,message:e.labels.consolidatedGraphError})};return(p,g)=>(d(),f("div",pl,[Q(ya,{"data-qa":"cmp-compare-products-graph-image",class:X(["elm-img",`elm-img--${n.value[0]}-${n.value[1]}`]),alt:p.labels.imageAlt,"image-load-error-handler":u,src:o.value,srcset:i.value,"src-format":"w={width}&h={height}","fill-mode":"contain"},null,8,["class","alt","src","srcset"])]))}}),vl=()=>{const a={dpi:180,w:600,h:900},e=qa(),t=r(()=>e==null?void 0:e.curves),s=r(()=>e==null?void 0:e.pumpSystemIds),n=r(()=>e==null?void 0:e.productNumbers),l=r(()=>e==null?void 0:e.productNames),o=r(()=>e==null?void 0:e.items),i=j({data:[]}),u=j({data:[]}),p=R=>{var L;return[{id:"include-comparison-group",customComponent:La,props:{checkboxLabel:(L=R.print)==null?void 0:L.includeInPrint,selectableProductsOption:e.selectableProductsOptions}}]},g=(R,L)=>{const{id:D,name:C}=L,k=(L.comparisonlist||[]).map(U=>{const{allequal:P,label:O,text:z}=U,V=U.values[R];return{allequal:P,label:O,text:z,value:V}});return{id:D,name:C,comparisonList:k}},_=R=>{var U,P,O,z,V;const L={},D=(U=s.value)==null?void 0:U.indexOf(R);L.pumpSystemId=R,L.productName=(P=l.value[D])==null?void 0:P.name,L.productNumber=n.value[D];const C=(O=i.value)==null?void 0:O.data.find(J=>J.id==="_QM_LCC");L.lccData=g(D,C);const k=(z=i.value)==null?void 0:z.data.filter(({comparisonlist:J,name:w,id:E})=>!pt(J)&&!pt(w)&&E!=="_QM_LCC");return L.compareData=(k||[]).map(J=>g(D,J)),L.curveImageLink=Ue((V=t.value[D])==null?void 0:V.src,a),L.productNumberLink=ra(R),L},m=R=>{const L=R<=3||R===6;return{dpi:L?250:240,w:L?1400:2200,h:L?1650:1200}},b=R=>R.replace(/<table>|<tbody>|<\/table><\/tbody>/g,"").replace(/tr>/g,"div>").replace(/>(.{0,8})<\/td>(\s*)(.*)&nbsp;&nbsp;/g,">$1</span>$2$3&#9;&#9;&#9;&#9;&#9;").replace(/>(.{9,16})<\/td>(\s*)(.*)&nbsp;&nbsp;/g,">$1</span>$2$3&#9;&#9;&#9;&#9;").replace(/>(.{17,25})<\/td>(\s*)(.*)&nbsp;&nbsp;/g,">$1</span>$2$3&#9;&#9;&#9;").replace(/>(.{26,32})<\/td>(\s*)(.*)&nbsp;&nbsp;/g,">$1</span>$2$3&#9;&#9;").replace(/>(.{33,40})<\/td>(\s*)(.*)&nbsp;&nbsp;/g,">$1</span>$2$3&#9;").replace(/<td/g,"<span").replace(/<\/td/g,"</span"),h=(R,L,D)=>ve($e,null,function*(){const C=[];R.forEach(U=>{C.push(oe(L.quotationTextApiUrl,{params:{pumpsystemid:U,languageCode:D}}))});const k=yield Promise.all(C);return R.forEach((U,P)=>{var V,J,w,E,N;const O=(V=s.value)==null?void 0:V.indexOf(U);k[P].productName=(J=l.value[O])==null?void 0:J.name,k[P].productNumber=n.value[O],k[P].pictureLink=(N=(E=(w=o.value[O])==null?void 0:w.picture)==null?void 0:E.link)==null?void 0:N.href;const z=ra(U);k[P].productInfoLink=z,k[P].data=b(k[P].data)}),Promise.resolve(k)}),I=R=>{i.value=A(R)},y=R=>{u.value=A(R)},$=(R,L,D)=>oe(R,{params:{pumpsystemid:D,languageCode:L}}).then(C=>(I(C),C)).catch(C=>(I(C),C)),M=(R,L,D)=>oe(R,{params:{settingid:D,languageCode:L}}).then(C=>(y(C),C)).catch(C=>(y(C),C));return{comparisonCustomGroups:p,mapPrintRequestBody:(R,L,D)=>ve($e,null,function*(){var O,z;const C={};C.printSettings=R!=null?R:{};const k=((O=R.selectedProducts)==null?void 0:O.split(","))||[];C.selectedProductsCount=k.length,R.comparisonGraph&&(D!=null&&D.comparisonCurveApiUrl)&&(C.comparisonCurveUrl=Ue(`${D==null?void 0:D.comparisonCurveApiUrl}?pumpsystemid=${R.selectedProducts}`,m(k.length)));const U=R==null?void 0:R.languageCode;yield M(D.printTranslateApiUrl,U,"comparison"),yield $(D==null?void 0:D.compareApiUrl,U,R.selectedProducts);const P=k.map(V=>_(V));if(P.length>5){const V=P.length===6?3:5;C.comparisonData=P.slice(0,V),C.comparisonDataOverFlow=P.slice(V)}else C.comparisonData=P;return C.quotationTextData=yield h(k,D,U),C.labels=(z=u.value)==null?void 0:z.data,Promise.resolve(C)}),getTranslatedCompareData:$}},gl={"data-qa":"cmp-compare-products-actions",class:"mod-compare-products__actions"},_l=["disabled"],bl={class:"elm-button__text"},hl=["disabled"],fl={class:"elm-button__text"},Cl=["disabled"],yl={class:"elm-button__text"},ql=["disabled"],$l={class:"elm-button__text"},Il=G({__name:"CmpCompareProductsActions",props:{config:{},labels:{},isLoading:{type:Boolean},toggleView:{type:Function},openPrintCompareModal:{type:Function},deleteAllProducts:{type:Function},editDutyPoints:{type:Function},graphIsVisible:{type:Boolean},productUrl:{}},setup(a,{expose:e}){const t=a,s=vl(),n=r(()=>s.comparisonCustomGroups(t.labels)),l=o=>ve(this,null,function*(){return s.mapPrintRequestBody(o,t.labels,t.config)});return e({mapPrintRequestBody:l}),(o,i)=>{var u;return d(),f("div",gl,[c("button",{"data-qa":"cmp-compare-products-actions-print",disabled:o.isLoading,type:"button",class:"elm-button mod-compare-products__action",onClick:i[0]||(i[0]=(...p)=>o.openPrintCompareModal&&o.openPrintCompareModal(...p))},[c("span",bl,q(o.labels.print.text),1)],8,_l),(u=o.config)!=null&&u.comparePrintJasperEnabled?(d(),B(jt,{key:0,disabled:o.isLoading,"template-id":"Comparison",class:"mod-compare-products__action","setting-id":"comparison","settings-right-column":["language-comparison-group","comparison-group"],"custom-groups":n.value,"button-text-override":`${o.labels.print.text||""} - Print Jasper`,"map-print-request-body":l,config:o.config,labels:o.labels.print},null,8,["disabled","custom-groups","button-text-override","config","labels"])):F("",!0),c("button",{"data-qa":"cmp-compare-products-actions-toggle",disabled:o.isLoading,type:"button",class:"elm-button elm-button--ghost mod-compare-products__action",onClick:i[1]||(i[1]=(...p)=>o.toggleView&&o.toggleView(...p))},[c("span",fl,q(o.graphIsVisible?o.labels.hideGraph:o.labels.showGraph),1)],8,hl),c("button",{"data-qa":"cmp-compare-products-actions-delete",disabled:o.isLoading,type:"button",class:"elm-button elm-button--ghost mod-compare-products__action",onClick:i[2]||(i[2]=(...p)=>o.deleteAllProducts&&o.deleteAllProducts(...p))},[c("span",yl,q(o.labels.deleteAll),1)],8,Cl),c("button",{"data-qa":"cmp-compare-products-actions-edit",disabled:o.isLoading,type:"button",class:"elm-button elm-button--ghost mod-compare-products__action",onClick:i[3]||(i[3]=p=>o.editDutyPoints(p))},[c("span",$l,q(o.labels.editAllDutyPoints),1)],8,ql)])}}}),Pl={"data-qa":"mod-compare-products","aria-live":"polite"},kl=G({__name:"ModCompareProducts",props:{labels:{},productUrl:{},config:{}},setup(a){const e=a,t=qa(),s=Se(),n=j(!1),l=r(()=>s.isMobile),o=r(()=>t==null?void 0:t.isLoading),i=r(()=>t==null?void 0:t.compareLccData),u=r(()=>t==null?void 0:t.curves),p=r(()=>t==null?void 0:t.compareData),g=r(()=>t==null?void 0:t.items),_=r(()=>t==null?void 0:t.productCount),m=r(()=>{var C,k;return _.value>((k=(C=g.value)==null?void 0:C.length)!=null?k:0)}),b=r(()=>t==null?void 0:t.pumpSystemIds),h=r(()=>t==null?void 0:t.productNumbers),I=r(()=>t==null?void 0:t.productNames),y=r(()=>{var C;return e.labels.hasMoreProducts.replace("[0]",(C=g.value)==null?void 0:C.length).replace("[1]",_.value)}),$=r(()=>({productUrl:e.productUrl,productNumbers:h,productNames:I,pumpSystemIds:b}));me(b,(C,k)=>{os(C,k)||t==null||t.getProductComparison()});const M=()=>{n.value=!n.value},H=(C,k)=>{const U=k!=null?k:g.value;s.setOverlayComponent({component:ua,heading:e.labels.actions.editDutyPoint,showToggle:!0,props:{labels:e.labels.questionCatalogue,apiUrl:e.config.dutyPointApiUrl,pumpSystemIds:U.map(P=>P.pumpsystemid),onSubmitData:P=>t.setDutyPoints(U,P)},origin:{target:C.target,x:C.clientX,y:C.clientY}})},R=(C,k)=>{s.setOverlayComponent({component:ua,heading:e.labels.actions.editLcc,showToggle:!0,props:{labels:e.labels.questionCatalogue,apiUrl:e.config.lccApiUrl,pumpSystemIds:k.pumpsystemid,onSubmitData:U=>t.setLccData(k,U)},origin:{target:C.target,x:C.clientX,y:C.clientY}})},L=C=>{var k,U,P,O,z,V,J;s.setOverlayComponent({component:dl,heading:(U=(k=e.labels)==null?void 0:k.print)==null?void 0:U.heading,showToggle:!0,props:{labels:(P=e.labels)==null?void 0:P.print,config:{languagesApiUrl:(O=e.config)==null?void 0:O.languagesApiUrl,preparePdfApiUrl:(z=e.config)==null?void 0:z.preparePdfApiUrl,printGetPdfApiUrl:(V=e.config)==null?void 0:V.printGetPdfApiUrl,userLogoApiUrl:(J=e.config)==null?void 0:J.userLogoApiUrl},selectableProductsForPrint:t.selectableProductsOptions},origin:{target:C.target,x:C.clientX,y:C.clientY},noContentAttrs:!l.value,contentClassName:l.value?"":"cmp-print__overlay-top-screen"})},D=()=>{t.getProducts(!0)};return Nt("productDetails",$),Nt("curves",u),Nt("onEdit",{dutyPoints:H,lcc:R}),Ee(()=>{t.setConfigApis(e.config),t.setLabels(e.labels),t.getProducts(!0),document.addEventListener("vueVuexCompareProductsUpdated",D)}),ys(()=>{document.removeEventListener("vueVuexCompareProductsUpdated",D)}),(C,k)=>{var P;const U=gt("CmpAlert");return d(),f("div",Pl,[m.value?(d(),B(U,{key:0,"data-qa":"cmp-compare-products-products-alert",class:"cmp-alert",messages:[y.value]},null,8,["messages"])):F("",!0),g.value.length?(d(),f(te,{key:1},[Q(Il,{labels:fe(ne({},C.labels.actions),{print:C.labels.print}),config:C.config,"product-url":C.productUrl,"is-loading":o.value,"toggle-view":M,"open-print-compare-modal":L,"edit-duty-points":H,"graph-is-visible":n.value,"delete-all-products":v(t).deleteProducts},null,8,["labels","config","product-url","is-loading","graph-is-visible","delete-all-products"]),n.value?(d(),B(ml,{key:0,labels:C.labels.compare,"pump-system-ids":b.value,src:C.config.consolidatedGraphApiUrl},null,8,["labels","pump-system-ids","src"])):F("",!0),Q(ul,{class:X(["cmp-compare-products-table",o.value&&v(qe).LOADING]),labels:C.labels,"show-curves":!n.value,items:g.value,"is-loading":o.value,"compare-data":p.value,"compare-lcc-data":i.value},null,8,["class","labels","show-curves","items","is-loading","compare-data","compare-lcc-data"])],64)):!o.value&&!g.value.length?(d(),B(U,{key:2,"data-qa":"cmp-compare-products-no-results-alert",class:"cmp-alert",messages:[(P=C.labels.compare)==null?void 0:P.noResults]},null,8,["messages"])):F("",!0)])}}}),_t=Ne("configToolStore",{state:()=>({config:{navListApiUrl:null},labels:{},autoCompleteError:null,isCurveEnabled:!1,changedCharacteristics:{data:{}},configHistory:{data:{}},configurationId:null,configuratorEnabled:!1,configType:null,curveViewerImg:{data:{}},hasProductGenerationError:!1,hiddenTabsOverride:[],initialPumpSystemId:null,initialRequestId:null,leadtime:{},newPumpSystemId:null,productConfig:{},pumpSystemId:null,selectedInstanceId:null,selectedCsticGroupId:null,visibleTabsOverride:null,autoCompleteEnabled:!1,isFetchingNewProduct:!1}),actions:fe(ne({},Yt()),{startConfig(a){const{pumpSystemId:e,configType:t,profileName:s,configDocumentId:n}=a,l=e;this.autoCompleteEnabled=this.config.autoCompleteEnabled==="true"&&t!==Qt.PROFILE,this.pumpSystemId=A(null,this.pumpSystemId),this.initialPumpSystemId=l,this.productConfig=A(null,this.productConfig),this.isCurveEnabled=!1;const o={configType:t,profileName:s,pumpSystemId:e,initialPumpSystemId:l};return n&&n.length>0&&(o.configDocumentId=n),oe(this.config.startConfigApiUrl,{params:o}).then(i=>{var H;const{visibleCharacteristicGroups:u,rootItem:p,configurationId:g,initialConfigData:_,pumpSystemId:m}=(H=i==null?void 0:i.data)!=null?H:{},{itemId:b,id:h}=p!=null?p:{},{initialRequestId:I,visibleTabsOverride:y,hiddenTabsOverride:$}=_!=null?_:{},M=u==null?void 0:u.find(R=>!!R.selected).id;this.selectedCsticGroupId=M,this.selectedInstanceId=b||h,this.productConfig=A(i),this.configurationId=g,this.initialRequestId=I,this.visibleTabsOverride=y,this.hiddenTabsOverride=$,this.pumpSystemId=A(m),this.updateConfigDataChain()}).catch(i=>{this.productConfig=A(i,this.productConfig)})},fetchInitialData(){var n;const{configPumpsystemid:a,pumpsystemid:e,profileName:t}=(n=ke(window.location.search))!=null?n:{},s={pumpsystemid:a||e,profileName:t};return oe(this.config.getInitialDataApiUrl,{params:s}).then(({data:l})=>{this.configType=l.configType,this.configuratorEnabled=l.configuratorEnabled,this.startConfig(l)})},updateConfigDataChain(){this.isCurveEnabled&&this.updatePumpsystemId(),this.updateLeadTime()},updateLeadTime(){var t,s,n,l,o;const a=(s=(t=$a())==null?void 0:t.userSettings)==null?void 0:s.productrange;if(a==="gma"||a==="general")return Promise.resolve();const e={configurationId:this.configurationId};return(l=(n=this.productConfig)==null?void 0:n.data)!=null&&l.complete&&((o=this.config)!=null&&o.leadtimeEnabled)?(this.leadtime=A(null,this.leadtime),oe(this.config.leadTimeApiUrl,{params:e}).then(i=>{this.leadtime=A(i)}).catch(i=>{this.leadtime=A(i,this.leadtime)})):Promise.resolve()},updatePumpsystemId(){this.pumpSystemId=A(null,this.pumpSystemId),this.curveViewerImg=A(null,this.curveViewerImg);const{configurationId:a,initialPumpSystemId:e}=this;return oe(this.config.updatePumpsystemIdApiUrl,{params:{configurationId:a,initialPumpSystemId:e}}).then(t=>{this.pumpSystemId=A(t),this.getCurveViewerImage()}).catch(t=>{this.pumpSystemId=A(t,this.pumpSystemId),this.curveViewerImg=A(t,this.curveViewerImg)})},getCurveViewerImage(){var e,t;const{pumpSystemId:a}=(t=(e=this.pumpSystemId)==null?void 0:e.data)!=null?t:{};return oe(this.config.curveViewerApiUrl,{params:{pumpSystemId:a}}).then(s=>{this.curveViewerImg=A(s)}).catch(s=>{this.curveViewerImg=A(s,this.curveViewerImg)})},emptyConfiguration(){const a={pumpSystemId:this.initialPumpSystemId,profileName:this.productConfig.data.kbKey.name};return this.productConfig=A(null,this.productConfig),oe(this.config.emptyConfigurationApiUrl,{params:a}).then(e=>{var s,n,l,o;const t=(s=e.data.visibleCharacteristicGroups.find(i=>!!(i!=null&&i.selected)))==null?void 0:s.id;this.selectedCsticGroupId=t,this.productConfig=A(e),this.configurationId=(n=e==null?void 0:e.data)==null?void 0:n.configurationId,this.initialRequestId=(o=(l=e==null?void 0:e.data)==null?void 0:l.initialConfigData)==null?void 0:o.initialRequestId,this.selectedInstanceId=e.data.rootItem.itemId||e.data.rootItem.id,this.updateConfigDataChain()}).catch(e=>{this.productConfig=A(e,this.productConfig)})},setProductGroupHandler(a){const{configurationId:e,initialPumpSystemId:t}=this;return this.productConfig=A(null,this.productConfig),oe(this.config.getConfigApiUrl,{params:{configurationId:e,selectedInstanceId:a,initialPumpSystemId:t}}).then(s=>{this.productConfig=A(s),this.selectedInstanceId=a}).catch(s=>{this.productConfig=A(s,this.productConfig)})},setCsticGroupId(a){const{configurationId:e,selectedInstanceId:t,initialPumpSystemId:s}=this;return this.selectedCsticGroupId!==a&&(this.selectedCsticGroupId=a,this.productConfig=A(null,this.productConfig),a)?oe(this.config.getConfigApiUrl,{params:{configurationId:e,selectedInstanceId:t,selectedCsticGroupId:a,initialPumpSystemId:s}}).then(n=>{n.data&&(this.productConfig=A(n))}).catch(n=>{this.productConfig=A(n,this.productConfig)}):null},setCharacteristic(a){var g;const{csticId:e,value:t,unit:s,selected:n}=a,{configurationId:l,selectedInstanceId:o,selectedCsticGroupId:i,initialPumpSystemId:u}=this,p={configurationId:l,selectedInstanceId:o,selectedCsticGroupId:i,csticId:e,value:t,selected:n,unit:s,initialPumpSystemId:u};return this.autoCompleteError=null,(g=this.productConfig)!=null&&g.loading?null:(this.productConfig=A(null,this.productConfig),De(this.config.setCharacteristicApiUrl,Be.stringify({body:JSON.stringify(p)}),{}).then(_=>{this.productConfig=A(_),this.updateConfigDataChain()}).catch(_=>{this.productConfig=A(_,this.productConfig)}))},getConfigHistory(){var n;const{configurationId:a,initialRequestId:e,productConfig:t}=this!=null?this:{},{kbId:s}=(n=t==null?void 0:t.data)!=null?n:{};return this.configHistory=A(null,this.configHistory),oe(this.config.getConfigHistoryApiUrl,{params:{configurationId:a,initialRequestId:e,kbId:s}}).then(l=>{l.data&&(this.configHistory=A(l))}).catch(l=>{this.configHistory=A(l,this.configHistory)})},onUndo(a){const{configurationId:e,selectedInstanceId:t,selectedCsticGroupId:s,initialPumpSystemId:n}=this;return this.productConfig=A(null,this.productConfig),a&&(this.configHistory=A(null,this.configHistory)),oe(this.config.undoApiUrl,{params:{configurationId:e,requestId:a,selectedInstanceId:t,selectedCsticGroupId:s,initialPumpSystemId:n}}).then(l=>{this.productConfig=A(l),this.updateConfigDataChain(),a&&this.getConfigHistory()}).catch(l=>{this.productConfig=A(l,this.productConfig)})},onAutoComplete(){this.productConfig=A(null,this.productConfig);const{configurationId:a,pumpSystemId:e,selectedInstanceId:t,selectedCsticGroupId:s,initialPumpSystemId:n}=this;return oe(this.config.autoCompleteApiUrl,{params:{configurationId:a,pumpSystemId:e,selectedInstanceId:t,selectedCsticGroupId:s,initialPumpSystemId:n}}).then(l=>{var _,m;const{failed:o,errorMsg:i,focusCsticGroupId:u,focusItemId:p,focusField:g}=(m=(_=l.data)==null?void 0:_.autoCompleteData)!=null?m:{};this.productConfig=A(l),this.autoCompleteError=null,this.updateConfigDataChain(),o&&(this.autoCompleteError={errorMsg:i,focusField:g},this.selectedInstanceId=p,this.selectedCsticGroupId=u)}).catch(l=>{this.productConfig=A(l,this.productConfig)})},switchProducts(){var n;const{configurationId:a,initialRequestId:e,productConfig:t}=this,{kbId:s}=(n=t==null?void 0:t.data)!=null?n:{};return this.changedCharacteristics=A(null,this.changedCharacteristics),this.hasProductGenerationError=!1,oe(this.config.getChangedCharacteristicsApiUrl,{params:{configurationId:a,initialRequestId:e,kbId:s}}).then(l=>{this.changedCharacteristics=A(l)}).catch(l=>{this.changedCharacteristics=A(l,this.changedCharacteristics)})},onGoToProduct(){const{configurationId:a,initialPumpSystemId:e,initialRequestId:t}=this;return this.hasProductGenerationError=!1,oe(this.config.switchProductApiUrl,{params:{configurationId:a,initialPumpSystemId:e,initialRequestId:t}}).then(s=>{var l,o;const{pumpSystemId:n}=(l=s==null?void 0:s.data)!=null?l:{};n?window.location.href=this.config.goToProductApiUrl+(((o=s.data)==null?void 0:o.pumpSystemId)||""):this.hasProductGenerationError=!0}).catch(()=>{this.hasProductGenerationError=!0})},applyChanges(){this.isFetchingNewProduct=!0,this.onGoToProduct().then(()=>{this.hasProductGenerationError||we(null,Fe.COMPLETE_CONFIG)}).finally(()=>{this.isFetchingNewProduct=!1})},cancelConfig(){var s;const{buildYourOwnPumpUrl:a,goToProductApiUrl:e}=(s=this.config)!=null?s:{},t=this.configType===Qt.PROFILE;window.location.href=t?`${a}?configureCancel=true`:`${e}${this.initialPumpSystemId}&configureCancel=true`},setCurveEnabled(a){this.isCurveEnabled=a}}),getters:{configDataIsLoading(a){var e,t;return((e=a.productConfig)==null?void 0:e.loading)||((t=a.pumpSystemId)==null?void 0:t.loading)},group(a){var e,t;return(t=(e=a.productConfig)==null?void 0:e.data)==null?void 0:t.rootItem},tabs(a){var l,o;const e=(o=(l=a.productConfig)==null?void 0:l.data)==null?void 0:o.visibleCharacteristicGroups,t=e?e.filter(i=>i.visible&&i.id!==Da.GENERAL_TAB_ID&&!a.hiddenTabsOverride.includes(i.id)):[];return(this.visibleTabsOverride?t.slice(0,this.visibleTabsOverride):t).map(i=>fe(ne({},i),{label:i.description,caption:i.complete?null:" ",subCaption:i.consistent?null:" "}))},tabContent(a){var t,s;const e=(s=(t=a.productConfig)==null?void 0:t.data)==null?void 0:s.visibleCharacteristics;return e?e.filter(n=>n.visible):[]},kbDetails(a){var l;const e=(l=a.productConfig)==null?void 0:l.data,{profileName:t,kbKey:s,kbBuild:n}=e!=null?e:{};return t?`${t} - ${s==null?void 0:s.version} - ${n}`:""}}}),Sl={key:0,class:"cmp-product-group","data-qa":"cmp-product-group"},Al={class:"cmp-product-group__inner"},El=["aria-describedby"],Rl=["aria-expanded"],Ul={class:"cmp-product-group__tooltip-inner"},Ll=["aria-expanded"],Tl={"data-qa":"cmp-product-group-list-item",class:"cmp-product-group__list-item"},wl=["data-qa"],Dl=["data-qa","onClick"],Ol=G({__name:"CmpConfigToolProductGroup",props:{labels:{},group:{},setProductGroupHandler:{type:Function}},setup(a){const e=a,t=Se(),s=j(!1),n=j(!0),l=r(()=>t.isMobile),o=r(()=>{var g,_;return(_=(g=e.group)==null?void 0:g.subItems)!=null?_:[]}),i=r(()=>{const g=o.value.find(_=>_.selected);return e.group.selected?e.group:g}),u=()=>{s.value=!s.value},p=()=>{n.value=!1};return(g,_)=>o.value.length>0?(d(),f("div",Sl,[c("div",Al,[c("button",{"data-qa":"cmp-product-group-toggle-button",class:"cmp-product-group__button",type:"button",onClick:u},[c("span",{"aria-describedby":`${i.value.key}-tip`,class:X(`cmp-product-group__button-title${!i.value.complete&&"--warning"}`)},[ze(q(i.value.key)+" ",1),n.value&&!l.value?(d(),f("div",{key:0,"aria-expanded":n.value,role:"tooltip",class:"cmp-product-group__tooltip"},[c("div",Ul,[c("button",{"data-qa":"cmp-product-group-tooltip-close-button",class:"elm-button elm-button--close cmp-config-tool__action-overlay-close-btn",type:"button",onClick:p}),c("h4",null,q(g.labels.multilevelTooltipTitle),1),c("p",null,q(g.labels.multilevelTooltipText),1)])],8,Rl)):F("",!0)],10,El)]),s.value?(d(),f("ul",{key:0,"data-qa":"cmp-product-group-sub-items","aria-expanded":s.value,class:"cmp-product-group__sub-items",onClick:u},[c("li",Tl,[c("button",{"data-qa":"cmp-product-group-list-item-button",type:"button",class:X(["cmp-product-group__list-item-button",!g.group.complete&&"cmp-product-group__list-item-warning"]),onClick:_[0]||(_[0]=st(m=>g.setProductGroupHandler(g.group.itemId),["prevent"]))},q(g.group.key),3)]),(d(!0),f(te,null,le(o.value,m=>(d(),f("li",{key:m.itemId,"data-qa":`cmp-product-group-list-item-${m.itemId}`,class:"cmp-product-group__list-item cmp-product-group__list-item--sub-item"},[c("button",{"data-qa":`cmp-product-group-list-item-button-${m.itemId}`,type:"button",class:X(["cmp-product-group__list-item-button",!m.complete&&"cmp-product-group__list-item-warning"]),onClick:st(b=>g.setProductGroupHandler(m.itemId),["prevent"])},q(m.key),11,Dl)],8,wl))),128))],8,Ll)):F("",!0)])])):F("",!0)}}),Nl=["id","data-qa","disabled"],Ml=["id","data-qa","name","disabled"],zl=["value","data-qa"],Fl=G({__name:"CmpConfigToolInputFieldValue",props:{item:{},inputTypeText:{type:Boolean},isConvertedUnit:{type:Boolean},validateInputs:{type:Function},inputValueHandler:{type:Function},autoCompleteError:{}},setup(a){const e=a,t=qs({}),s=j(null),n=r(()=>{var m,b;return(b=(m=e.item)==null?void 0:m.possibleValues)==null?void 0:b.filter(h=>h.selectable)}),l=()=>{if(s.value&&n.value.length>0){const m=n.value.find(y=>e.isConvertedUnit?y.valueLow===s.value:y.convertedValue===s.value),{valueLow:b,convertedValue:h}=m!=null?m:{},I=e.isConvertedUnit?h:b;s.value=I}},o=m=>{const{valueLow:b,convertedValue:h}=m;return e.isConvertedUnit?h:b},i=(m,b)=>{const{label:h,convertedValue:I}=m,y=e.isConvertedUnit?I:h;return b==="FLOAT"?parseFloat(y):y},u=()=>{const{required:m,id:b}=e.item,h=!s.value||s.value.length===0;m&&e.validateInputs(b,h)},p=()=>{u(),e.inputValueHandler(s.value)},g=ls(()=>{p()},500),_=()=>{var h,I;const{values:m}=(h=e.item)!=null?h:{},b=(I=m==null?void 0:m[0])==null?void 0:I.value;s.value=b!=null?b:"",u()};return Ee(()=>{var m,b,h,I;((m=e.item)==null?void 0:m.id)===((b=e.autoCompleteError)==null?void 0:b.focusField)&&((I=t[(h=e.item)==null?void 0:h.id])==null||I.focus()),_()}),me(()=>e.isConvertedUnit,()=>{l()}),me(()=>e.item,()=>{_()}),(m,b)=>m.inputTypeText?Oe((d(),f("input",{key:0,id:m.item.id,ref:h=>t[m.item.id]=h,"onUpdate:modelValue":b[0]||(b[0]=h=>s.value=h),"data-qa":`cmp-config-tool-input-field-value-${m.item.id}`,disabled:m.item.readOnly,type:"text",class:"cmp-form-text__text",onInput:b[1]||(b[1]=(...h)=>v(g)&&v(g)(...h))},null,40,Nl)),[[$s,s.value]]):Oe((d(),f("select",{key:1,id:m.item.id,ref:h=>t[m.item.id]=h,"onUpdate:modelValue":b[2]||(b[2]=h=>s.value=h),"data-qa":`cmp-config-tool-input-field-select-${m.item.id}`,name:m.item.id,disabled:m.item.readOnly,class:"cmp-form-options__field cmp-form-options__field--drop-down",onChange:p},[b[3]||(b[3]=c("option",{value:""},null,-1)),(d(!0),f(te,null,le(n.value,h=>(d(),f("option",{key:o(h),value:o(h),"data-qa":`cmp-config-tool-input-field-option-${m.item.id}`},q(i(h,m.item.valueType)),9,zl))),128))],40,Ml)),[[Ra,s.value]])}}),Gl=["data-qa"],Ql=["for"],Hl={key:0,class:"cmp-config-tool-unit__form-item"},Vl=["id","data-qa","name","disabled"],Bl=["value"],Yl=["value"],Wl=G({__name:"CmpConfigToolInputFieldUnit",props:{item:{},switchUnitHandler:{type:Function}},setup(a){const e=a,t=j(null),s=r(()=>{var u;return(u=e.item)==null?void 0:u.id}),n=r(()=>{var u;return(u=e.item)==null?void 0:u.unitOfMeasure}),l=r(()=>{var u;return(u=e.item)==null?void 0:u.convertedUnit}),o=r(()=>{var u;return n.value&&!((u=e.item)!=null&&u.convertedUnit)}),i=u=>{const{value:p}=u.target;t.value=p,e.switchUnitHandler(p)};return Et(()=>{var u;t.value=(u=n.value)!=null?u:null}),me(n,u=>{t.value=u}),(u,p)=>(d(),f("div",{"data-qa":`cmp-config-tool-input-field-unit-${s.value}`,class:X(`${o.value?"cmp-config-tool-unit":""}`)},[c("label",{for:`${s.value}-${n.value}`,class:"cmp-config-tool-input__form-item-label h-hidden"},q(n.value),9,Ql),o.value?(d(),f("span",Hl,q(n.value),1)):Oe((d(),f("select",{key:1,id:`${s.value}-${n.value}`,"onUpdate:modelValue":p[0]||(p[0]=g=>t.value=g),"data-qa":`cmp-config-tool-input-field-unit-select-${s.value}`,name:s.value,disabled:u.item.readOnly,class:"cmp-form-options__field cmp-form-options__field--drop-down cmp-config-tool-unit__form-item",onChange:i},[c("option",{value:n.value},q(n.value),9,Bl),c("option",{value:l.value},q(l.value),9,Yl)],40,Vl)),[[Ra,t.value]])],10,Gl))}}),jl=["data-qa"],Jl=["data-qa","for"],Zl=["data-qa"],Xl=G({__name:"CmpConfigToolInputField",props:{item:{},validateInputs:{type:Function},setCharacteristic:{type:Function},autoCompleteError:{}},setup(a){var m;const e=a,t=j(!1),s=j(null),n=j((m=e.item)==null?void 0:m.unitOfMeasure),l=j(null),o=r(()=>{var b;return((b=e.item)==null?void 0:b.possibleValues.length)===0}),i=r(()=>o.value?ct.TEXT:ct.OPTIONS),u=r(()=>{var b;return!((b=e.item)!=null&&b.consistent)}),p=b=>{t.value=b===ct.CONVERTED,n.value=e.item[t.value?b:ct.REGULAR]},g=()=>{const b=s.value==="",h=b?l.value:s.value;e.setCharacteristic({csticId:e.item.id,value:h,unit:n.value,selected:!b}),l.value=h},_=b=>{s.value=b,g()};return Ee(()=>{var h;const{values:b}=e.item;(h=b==null?void 0:b[0])!=null&&h.value&&(l.value=b[0].value)}),(b,h)=>(d(),f("div",{"data-qa":`cmp-config-tool-input-field-${b.item.id}`,class:X(["cmp-config-tool-input__form-item",`cmp-form-${i.value}`,u.value&&`cmp-form-${i.value}--error`])},[c("label",{"data-qa":`cmp-config-tool-input-field-label-${b.item.id}`,for:b.item.id,class:X(["cmp-config-tool-input__form-item-label",b.item.required&&"cmp-config-tool-input__form-item-label--required"])},q(b.item.label),11,Jl),c("div",{"data-qa":`cmp-config-tool-input-field-group-${b.item.id}`,class:"cmp-config-tool-input__form-item-group"},[Q(Fl,{item:b.item,"input-type-text":o.value,"is-converted-unit":t.value,"validate-inputs":b.validateInputs,"input-value-handler":_,"auto-complete-error":b.autoCompleteError},null,8,["item","input-type-text","is-converted-unit","validate-inputs","auto-complete-error"]),b.item.unitOfMeasure?(d(),B(Wl,{key:0,item:b.item,"switch-unit-handler":p},null,8,["item"])):F("",!0)],8,Zl)],10,jl))}}),Kl=(a,e)=>{const t=a.__vccOpts||a;for(const[s,n]of e)t[s]=n;return t},xl={},ei={class:"cmp-config-tool-nav b-theme b-deck b-deck--full-width"},ti={class:"cmp-config-tool-nav__inner b-deck__inner b-layout-grid__group"},ai={class:"cmp-config-tool-nav__actions"};function si(a,e){return d(),f("div",ei,[c("div",ti,[Ye(a.$slots,"info"),c("div",ai,[Ye(a.$slots,"actions")])])])}const Oa=Kl(xl,[["render",si]]),ni=["id","data-qa","disabled"],oi={class:"elm-button__text"},Ze=G({__name:"ElmConfigToolNavButton",props:{id:{},label:{},isGhost:{type:Boolean},classes:{},isLoading:{type:Boolean},commitAction:{type:Function},isSideNav:{type:Boolean}},setup(a){return(e,t)=>(d(),f("button",{id:e.id,"data-qa":e.id,class:X(["elm-button","elm-button--small",e.isSideNav?"cmp-config-tool-side-nav__button":`cmp-config-tool-nav__actions-item
           cmp-config-tool-nav__actions-button`,e.isGhost&&"elm-button--ghost",e.classes,e.isLoading&&v(qe).LOADING]),disabled:e.isLoading,type:"button",onClick:t[0]||(t[0]=(...s)=>e.commitAction&&e.commitAction(...s))},[c("span",oi,q(e.label),1)],10,ni))}}),li=G({__name:"CmpConfigToolTabsMobileNav",props:{labels:{},switchProducts:{type:Function},cancelConfig:{type:Function},isLoading:{type:Boolean}},setup(a){return(e,t)=>(d(),B(Oa,{"data-qa":"cmp-config-tool-tabs-mobile-nav"},{actions:be(()=>[Q(Ze,{id:"elm-config-tool-button-apply",class:"elm-button--positive",label:e.labels.applyChanges,"is-loading":e.isLoading,"commit-action":e.switchProducts},null,8,["label","is-loading","commit-action"]),Q(Ze,{id:"elm-config-tool-button-cancel",label:e.labels.cancel,"is-loading":e.isLoading,"commit-action":e.cancelConfig,"is-ghost":""},null,8,["label","is-loading","commit-action"])]),_:1}))}}),ii={"data-qa":"cmp-config-tool-alert-product-notice",class:"cmp-product-notice"},ci={class:"cmp-product-notice__inner"},ri={class:"b-layout-grid b-layout-grid--spaced"},ui={class:"b-layout-grid__group"},di={class:"b-layout-grid__item b-layout-grid__item--12 b-layout-grid__item--large-6"},pi={class:"cmp-alert cmp-product-notice__alert"},mi={"data-qa":"cmp-config-tool-alert-product-notice-message",class:"cmp-text cmp-alert__message"},vi=G({__name:"CmpConfigToolAlert",props:{alertMessage:{}},setup(a){return(e,t)=>(d(),f("div",ii,[c("div",ci,[c("div",ri,[c("div",ui,[c("div",di,[c("div",pi,[c("div",mi,[c("p",null,[c("strong",null,q(e.alertMessage),1)])])])])])])])]))}}),gi=["data-qa"],ga=G({__name:"CmpConfigCurveControls",props:{id:{},labels:{},isCurveEnabled:{type:Boolean},setCurveEnabled:{type:Function},updatePumpsystemId:{type:Function}},setup(a){const e=a,t=n=>{const l=n.target.checked;e.setCurveEnabled(l),l&&e.updatePumpsystemId()},s=r(()=>{const n="cmp-config-tool-curve-controls";return e.id?`${n}-${e.id}`:n});return(n,l)=>(d(),f("div",{"data-qa":s.value,class:"cmp-config-tool-curve-controls"},[n.isCurveEnabled?F("",!0):(d(),B(Ze,{key:0,id:"cmp-config-tool-curve-controls-button","is-side-nav":"","is-ghost":"","commit-action":n.updatePumpsystemId,label:n.labels.updateCurve},null,8,["commit-action","label"])),Q(at,{id:"auto-update-curve-enabled",modifier:"inline","data-qa":"cmp-config-tool-curve-controls-checkbox",class:"cmp-config-tool-side-nav__auto-update",value:n.isCurveEnabled,label:n.labels.autoUpdateCurve,"value-update-handler":t},null,8,["value","label"])],8,gi))}}),_i=["id","data-qa"],dt=G({__name:"ElmConfigToolNavLink",props:{id:{},label:{},classes:{},isLoading:{type:Boolean},commitAction:{type:Function},isSideNav:{type:Boolean}},setup(a){return(e,t)=>(d(),f("div",{class:X(["elm-config-tool-nav-link",e.isSideNav?"cmp-config-tool-side-nav__link":"cmp-config-tool-nav__actions-item"])},[c("button",{id:e.id,"data-qa":e.id,class:X(["elm-link","elm-link--block",!e.isSideNav&&"cmp-config-tool-nav__actions-link",e.classes,e.isLoading&&"elm-link--disabled"]),type:"button",onClick:t[0]||(t[0]=(...s)=>e.commitAction&&e.commitAction(...s))},q(e.label),11,_i)],2))}}),bi={class:"b-layout-grid"},hi={class:"b-layout-grid__group"},fi={class:"b-layout-grid__item b-layout-grid__item--large-5 b-layout-grid__item--small-12 cmp-config-tool-form-column"},Ci=["data-qa"],yi={key:0,class:"cmp-config-tool__help-text"},qi=["title","href","data-qa"],$i={class:"b-layout-grid__item b-layout-grid__item--small-12 b-layout-grid__item--large-6 b-layout-grid__item--large-offset-1 cmp-config-tool-curve-column"},Ii={class:"cmp-curves__section"},Pi=["data-qa"],ki=["data-qa"],Si=["data-qa","src","alt"],Ai=G({__name:"CmpConfigToolTabs",props:{labels:{},configToolGuide:{},openModal:{}},setup(a){const e=a,t=_t(),s=Se(),n=j([]),l=j(0),o=r(()=>s==null?void 0:s.isMobile),i=r(()=>t==null?void 0:t.tabs),u=r(()=>t==null?void 0:t.tabContent),p=r(()=>{var $;return($=t.productConfig)==null?void 0:$.loading}),g=r(()=>{var $;return($=t.configHistory)==null?void 0:$.loading}),_=r(()=>t.configDataIsLoading),m=r(()=>t.autoCompleteError),b=r(()=>{var $,M;return(M=($=m==null?void 0:m.value)==null?void 0:$.errorMsg)!=null?M:e.labels.configConflictWarning}),h=r(()=>{var $,M;return(M=($=t.curveViewerImg)==null?void 0:$.data)==null?void 0:M.link}),I=r(()=>{var $;return($=t.curveViewerImg)==null?void 0:$.loading}),y=($,M)=>{const H=n.value.findIndex(L=>L.id===$),R=H!==-1;R&&!M?n.value.splice(H,1):!R&&M&&n.value.push({id:$})};return me(()=>o.value,()=>{o.value&&(l.value+=1)}),($,M)=>{var H;return(H=i.value)!=null&&H.length?(d(),B(Zt,{key:l.value,"data-qa":"cmp-config-tool-tabs",tabs:i.value,"on-tab-click":v(t).setCsticGroupId,class:"cmp-tabs",sticky:"","sticky-heading":"","use-query":""},{tabContent:be(R=>[(d(!0),f(te,null,le(i.value,L=>(d(),B(Jt,{id:L.id,key:L.id,"data-qa":`cmp-config-tool-tab-${L.id}`,"tab-props":R,class:"cmp-tab"},{tab:be(()=>{var D,C,k;return[c("section",null,[m.value||!L.consistent?(d(),B(vi,{key:0,"alert-message":b.value,"data-qa":`cmp-config-tool-alert-${L.id}`},null,8,["alert-message","data-qa"])):F("",!0),c("div",{class:X(["b-theme","b-theme--subtle","cmp-config-tool-tab",p.value&&v(qe).LOADING])},[c("div",{class:X(["cmp-config-tool-tab__content",_.value&&v(qe).LOADING])},[c("div",bi,[c("div",hi,[c("div",fi,[c("header",{"data-qa":`cmp-config-tool-tab-${L.id}-header`,class:"cmp-config-tool-tab__header"},[c("h3",null,q($.labels.selectParameters),1),o.value?F("",!0):(d(),f("p",yi,[ze(q($.labels.needHelp)+" ",1),c("a",{title:$.labels.downloadGuide,href:$.configToolGuide,class:"elm-link","data-qa":`cmp-config-tool-tab-${L.id}-guide-link`},q($.labels.downloadGuide),9,qi)]))],8,Ci),o.value?(d(),f(te,{key:0},[v(t).autoCompleteEnabled?(d(),B(dt,{key:0,id:"elm-config-side-nav-link__autocomplete","is-side-nav":"",classes:"cmp-config-tool-side-nav__auto-complete",label:$.labels.autoComplete,"commit-action":$.openModal.autoComplete,"is-loading":p.value},null,8,["label","commit-action","is-loading"])):F("",!0),Q(dt,{id:"elm-config-side-nav-link__history",classes:"cmp-config-tool-side-nav__history","is-side-nav":"",label:$.labels.viewConfigHistory,"commit-action":v(t).getConfigHistory,"is-loading":g.value},null,8,["label","commit-action","is-loading"])],64)):F("",!0),(d(!0),f(te,null,le(u.value,U=>(d(),B(Xl,{key:U.id,item:U,"validate-inputs":y,"set-characteristic":v(t).setCharacteristic,"auto-complete-error":m.value},null,8,["item","set-characteristic","auto-complete-error"]))),128)),o.value?(d(),f(te,{key:1},[Q(li,{"is-loading":p.value,labels:$.labels,"switch-products":$.openModal.applyConfig,"cancel-config":$.openModal.cancelConfig},null,8,["is-loading","labels","switch-products","cancel-config"]),Q(ga,{id:"form-column",labels:$.labels,"is-curve-enabled":v(t).isCurveEnabled,"set-curve-enabled":v(t).setCurveEnabled,"update-pumpsystem-id":v(t).updatePumpsystemId},null,8,["labels","is-curve-enabled","set-curve-enabled","update-pumpsystem-id"])],64)):F("",!0)]),c("div",$i,[c("section",Ii,[o.value?F("",!0):(d(),f("div",{key:0,"data-qa":`cmp-config-tool-side-nav-${L.id}`,class:"cmp-config-tool-side-nav"},[Q(Ze,{id:"elm-config-side-nav-button__history",classes:"cmp-config-tool-side-nav__history-btn","is-side-nav":"","is-ghost":"","commit-action":v(t).getConfigHistory,"is-loading":g.value,label:$.labels.viewConfigHistory},null,8,["commit-action","is-loading","label"]),Q(ga,{id:"curve-column",labels:$.labels,"is-curve-enabled":v(t).isCurveEnabled,"set-curve-enabled":v(t).setCurveEnabled,"update-pumpsystem-id":v(t).updatePumpsystemId},null,8,["labels","is-curve-enabled","set-curve-enabled","update-pumpsystem-id"])],8,Pi)),c("div",{class:X(["cmp-config-tool-curve-column__image",I.value&&v(qe).LOADING]),"data-qa":`cmp-config-tool-curve-image-column-${L.id}`},[(D=h.value)!=null&&D.href?(d(),f("img",{key:0,id:"performance-curves","data-qa":`cmp-config-tool-curve-image-${L.id}`,class:"cmp-config-tool-curve-column__curve-image",src:(C=h.value)==null?void 0:C.href,alt:(k=h.value)==null?void 0:k.rel,width:"100%"},null,8,Si)):F("",!0)],10,ki)])])])])],2)],2)])]}),_:2},1032,["id","data-qa","tab-props"]))),128))]),_:1},8,["tabs","on-tab-click"])):F("",!0)}}}),Ei={class:"cmp-overlay-form__body"},Ri={class:"cmp-overlay-form__footer"},Ui={class:"elm-button__text"},Li=G({__name:"CmpConfigToolLeadtimeErrorsOverlay",props:{confirmLabel:{},errorMessages:{},toggleHandler:{type:Function}},setup(a){return(e,t)=>(d(),f("div",null,[c("div",Ei,[c("ul",null,[(d(!0),f(te,null,le(e.errorMessages,(s,n)=>(d(),f("li",{key:`leadtime-warning-${n}`},q(s),1))),128))])]),c("div",Ri,[c("button",{class:"elm-button elm-button--ghost elm-button--medium cmp-overlay-form__footer-button",type:"button",onClick:t[0]||(t[0]=(...s)=>e.toggleHandler&&e.toggleHandler(...s))},[c("span",Ui,q(e.confirmLabel),1)])])]))}}),Ti={"data-qa":"cmp-config-tool-nav-info",class:"cmp-config-tool-nav__info"},wi={"data-qa":"cmp-config-tool-nav-info-item",class:"cmp-config-tool-nav__info-item"},Di={class:"cmp-config-tool-nav__info-item-title"},Oi={class:"cmp-config-tool-nav__info-item-desc cmp-config-tool-nav__info-item-price"},Ni={"data-qa":"cmp-config-tool-nav-info-item-lead-time",class:"cmp-config-tool-nav__info-item-lead-time"},Mi={class:"cmp-config-tool-nav__info-item-title"},zi=G({__name:"CmpConfigToolNavInfo",props:{labels:{},netTotal:{},leadTime:{},setOverlayComponent:{type:Function}},setup(a){const e=a,t=r(()=>{var _,m,b;const{netPriceValue:i}=(_=e.netTotal)!=null?_:{},u=(m=e.netTotal)==null?void 0:m["Price Currency"],p=(b=i==null?void 0:i.toString())==null?void 0:b.replace(/\B(?=(\d{3})+(?!\d))/g,","),g=`${u} ${p}`;return i>0?g:e.labels.onRequest}),s=r(()=>{var i;return(i=e.leadTime)==null?void 0:i.loading}),n=r(()=>{var i,u,p;return(p=(u=(i=e.leadTime)==null?void 0:i.data)==null?void 0:u.errorMessages)!=null?p:[]}),l=r(()=>{var b,h,I;const{leadTime:i,success:u}=(h=(b=e.leadTime)==null?void 0:b.data)!=null?h:{},{days:p,onRequest:g}=(I=e.labels)!=null?I:{},_=parseInt(i,10),m=Number.isNaN(_)?"":_;return u?`${m} ${p}`:g}),o=()=>{e.setOverlayComponent({component:Li,heading:e.labels.leadtimeErrorOverlayHeading,props:{confirmLabel:e.labels.ok,errorMessages:n.value}})};return(i,u)=>(d(),f("dl",Ti,[c("div",wi,[c("dt",Di,q(i.labels.totalListPrice),1),u[0]||(u[0]=ze(" : ")),c("dd",Oi,q(t.value),1)]),c("div",Ni,[c("dt",Mi,q(i.labels.netEstimatedLeadTime),1),u[1]||(u[1]=ze(" : ")),c("dd",{class:X(["cmp-config-tool-nav__info-item-desc",s.value&&"cmp-config-tool-nav__info-item-lead-time--loading"])},q(s.value?i.labels.loading:l.value),3),n.value.length>0?(d(),f("button",{key:0,"data-qa":"cmp-config-tool-nav-info-lead-time-warning",type:"button",class:"elm-link cmp-config-tool-nav__warning-icon",onClick:o})):F("",!0)])]))}}),Fi=G({__name:"CmpConfigToolMainNav",props:{labels:{},openModal:{}},setup(a){const e=_t(),t=Se(),s=r(()=>t==null?void 0:t.isMobile),n=r(()=>e==null?void 0:e.leadtime),l=r(()=>e==null?void 0:e.productConfig),o=r(()=>e==null?void 0:e.autoCompleteEnabled),i=r(()=>l.value.loading),u=r(()=>{var g;return(g=l.value.data)==null?void 0:g.netTotal}),p=()=>{e.onUndo()};return(g,_)=>(d(),B(Oa,{class:X([s.value&&"cmp-config-tool-nav--mobile"]),"data-qa":"cmp-config-tool-main-nav"},Is({actions:be(()=>[s.value?(d(),B(Ze,{key:0,id:"elm-config-tool-button-undo",label:g.labels.undo,"is-loading":i.value,"commit-action":p},null,8,["label","is-loading"])):(d(),f(te,{key:1},[Q(dt,{id:"elm-config-tool-link-empty",label:g.labels.emptyConfiguration,"is-loading":i.value,"commit-action":v(e).emptyConfiguration},null,8,["label","is-loading","commit-action"]),Q(dt,{id:"elm-config-tool-link-undo",classes:"elm-link--icon-reset_outline",label:g.labels.undo,"is-loading":i.value,"commit-action":p},null,8,["label","is-loading"]),o.value?(d(),B(dt,{key:0,id:"elm-config-tool-link-auto-complete",label:g.labels.autoComplete,"is-loading":i.value,"commit-action":g.openModal.autoComplete},null,8,["label","is-loading","commit-action"])):F("",!0),Q(Ze,{id:"elm-config-tool-button-cancel",label:g.labels.cancel,"is-loading":i.value,"commit-action":g.openModal.cancelConfig,"is-ghost":""},null,8,["label","is-loading","commit-action"]),Q(Ze,{id:"elm-config-tool-button-apply",classes:"elm-button--positive",label:g.labels.applyChanges,"is-loading":i.value,"commit-action":g.openModal.applyConfig},null,8,["label","is-loading","commit-action"])],64))]),_:2},[s.value?void 0:{name:"info",fn:be(()=>[Q(zi,{labels:g.labels,"net-total":u.value,"lead-time":n.value,"set-overlay-component":v(t).setOverlayComponent},null,8,["labels","net-total","lead-time","set-overlay-component"])]),key:"0"}]),1032,["class"]))}}),Gi=["data-qa"],Qi=["data-qa"],Hi=["id","data-qa","disabled"],Vi={class:"elm-button__text"},Tt=G({__name:"CmpConfigToolActionOverlay",props:{id:{},buttonLabel:{},commitAction:{type:Function},btnPositive:{type:Boolean},isDisabled:{type:Boolean},isLoading:{type:Boolean}},setup(a){return(e,t)=>(d(),f("div",{"data-qa":`cmp-config-tool-action-overlay-${e.id}`,class:"cmp-config-tool__action-overlay"},[c("div",{"data-qa":`cmp-config-tool-action-overlay-content-${e.id}`,class:"cmp-config-tool__action-overlay-content"},[Ye(e.$slots,"default")],8,Qi),c("div",null,[c("button",{id:e.id,"data-qa":`cmp-config-tool-action-overlay-btn-${e.id}`,class:X(["elm-button","cmp-config-tool__action-overlay-btn",e.btnPositive?"elm-button--positive":"elm-button--ghost",e.isLoading&&v(qe).LOADING]),disabled:e.isDisabled||e.isLoading,type:"button",onClick:t[0]||(t[0]=(...s)=>e.commitAction&&e.commitAction(...s))},[c("span",Vi,q(e.buttonLabel),1)],10,Hi)])],8,Gi))}}),Bi={class:"cmp-text"},Yi=["aria-label"],Wi={class:"elm-table__head"},ji={class:"elm-table__row"},Ji={class:"elm-table__body"},Zi={class:"elm-table__cell elm-table__cell--body"},Xi={class:"cmp-form-option"},Ki=["id","name","value"],xi=["for"],ec=G({__name:"CmpConfigToolHistoryOverlay",props:{labels:{},toggleHandler:{type:Function}},setup(a){const e=a,t=_t(),s=j([]),n=r(()=>{const{selectItem:m,itemName:b,itemValue:h,actionType:I}=e.labels;return[m,b,h,I]}),l=r(()=>t.configHistory),o=r(()=>{var m;return(m=l.value)==null?void 0:m.loading}),i=r(()=>{var m;return(m=l.value)==null?void 0:m.data.history}),u=r(()=>!i.value||i.value.length===0||o.value),p=r(()=>i.value.length>0),g=()=>{var h;const m=i.value.filter(I=>s.value.includes(I.requestId)),b=((h=m==null?void 0:m[0])==null?void 0:h.requestId)||null;t.onUndo(b)},_=m=>{const{label:b,value:h,actionType:I}=m;return Object.values({label:b,value:h,actionType:I})};return(m,b)=>(d(),B(Tt,{id:"history-modal","button-label":m.labels.deleteActions,"commit-action":g,"is-disabled":u.value,"is-loading":o.value,"btn-positive":""},{default:be(()=>[c("div",Bi,[c("p",null,q(m.labels.configHistoryModalText),1)]),p.value?(d(),f("table",{key:0,"aria-label":m.labels.configHistoryModalTitle,class:X(["elm-table","cmp-config-tool__delete-actions-table",o.value&&v(qe).LOADING])},[c("thead",Wi,[c("tr",ji,[(d(!0),f(te,null,le(n.value,h=>(d(),f("th",{key:h,scope:"col",class:"elm-table__cell elm-table__cell--head"},q(h),1))),128))])]),c("tbody",Ji,[(d(!0),f(te,null,le(i.value,h=>(d(),f("tr",{key:h.requestId,class:"elm-table__row"},[c("td",Zi,[c("span",Xi,[Oe(c("input",{id:h.requestId,"onUpdate:modelValue":b[0]||(b[0]=I=>s.value=I),name:h.requestId,value:h.requestId,class:"cmp-form-option__field cmp-form-option__field--checkbox",type:"checkbox"},null,8,Ki),[[Ps,s.value]]),c("label",{for:h.requestId,class:"cmp-form-option__label cmp-form-option__label--checkbox"},null,8,xi)])]),(d(!0),f(te,null,le(_(h),I=>(d(),f("td",{key:I,class:"elm-table__cell elm-table__cell--body cmp-config-tool-history-overlay__cell-value"},q(I),1))),128))]))),128))])],10,Yi)):F("",!0)]),_:1},8,["button-label","is-disabled","is-loading"]))}}),tc=G({__name:"CmpConfigToolAutoCompleteOverlay",props:{labels:{},commitAction:{type:Function},toggleHandler:{type:Function}},setup(a){const e=a,t=()=>{e.commitAction(),e.toggleHandler()};return(s,n)=>(d(),B(Tt,{id:"complete-modal","button-label":s.labels.continue,"commit-action":t},{default:be(()=>[c("p",null,q(s.labels.autoCompleteConfirmation),1)]),_:1},8,["button-label"]))}}),ac={class:"cmp-text"},sc=G({__name:"CmpConfigToolCancelOverlay",props:{labels:{},commitAction:{type:Function},toggleHandler:{type:Function}},setup(a){const e=a,t=()=>{we(null,Fe.CANCEL_CONFIG),e.commitAction()};return(s,n)=>(d(),B(Tt,{id:"cancel-modal","button-label":s.labels.continue,"commit-action":t},{default:be(()=>[c("div",ac,[c("p",null,q(s.labels.cancelModalText),1)])]),_:1},8,["button-label"]))}}),nc={key:1,class:"cmp-alert"},oc={class:"cmp-text cmp-alert__message cmp-config-tool-action-overlay__conflict-error"},lc={key:2},ic=G({__name:"CmpConfigToolApplyOverlay",props:{labels:{},toggleHandler:{type:Function}},setup(a){const e=_t(),t=r(()=>{var i,u;return(u=(i=e.changedCharacteristics)==null?void 0:i.data)==null?void 0:u.characteristics}),s=r(()=>{var i;return(i=e.changedCharacteristics)==null?void 0:i.loading}),n=r(()=>e.isFetchingNewProduct),l=r(()=>e.hasProductGenerationError),o=r(()=>{var p,g;const{consistent:i,complete:u}=(g=(p=e.productConfig)==null?void 0:p.data)!=null?g:{};return!i||!u});return(i,u)=>(d(),B(Tt,{id:"apply-modal","button-label":i.labels.continue,"commit-action":v(e).applyChanges,"is-loading":s.value||n.value},{default:be(()=>[l.value?(d(),B(Xe,{key:0,messages:[i.labels.generateProductErrorMsg],classes:"cmp-config-tool-action-overlay__product-generation-error","is-warning":""},null,8,["messages"])):F("",!0),o.value?(d(),f("div",nc,[c("div",oc,[c("p",null,q(i.labels.conflictErrorMsg),1)])])):F("",!0),c("p",null,q(i.labels.applyModalText),1),s.value?F("",!0):(d(),f("ul",lc,[(d(!0),f(te,null,le(t.value,p=>(d(),f("li",{key:p.id},q(p.label)+" ( "+q(p.value)+" ) ",1))),128))]))]),_:1},8,["button-label","commit-action","is-loading"]))}}),cc={"data-qa":"cmp-config-tool__kb-details",class:"cmp-config-tool__kbdetails"},rc=G({__name:"ModConfigTool",props:{labels:{},config:{},configToolGuide:{}},setup(a){const e=a,t=_t(),s=Se(),n=r(()=>t.configuratorEnabled),l=r(()=>{var y;return(y=t.productConfig)==null?void 0:y.data}),o=r(()=>{var y,$;return($=(y=t.productConfig)==null?void 0:y.error)==null?void 0:$.message}),i=r(()=>{var y;return!((y=t.productConfig)!=null&&y.loading)&&!!o.value}),u=r(()=>t.configHistory),p=r(()=>t.kbDetails),g=r(()=>t.group),_=()=>{s.setOverlayComponent({component:ec,heading:e.labels.configHistoryModalTitle,props:{class:"cmp-config-tool__history-modal",labels:e.labels}})},I={autoComplete:()=>{s.setOverlayComponent({component:tc,heading:e.labels.autoComplete,props:{class:"cmp-config-tool__auto-complete-modal",labels:e.labels,commitAction:t.onAutoComplete}})},cancelConfig:()=>{s.setOverlayComponent({component:sc,heading:e.labels.cancelModalTitle,props:{class:"cmp-config-tool__cancel-modal",labels:e.labels,commitAction:t.cancelConfig}})},applyConfig:()=>{t.switchProducts(),s.setOverlayComponent({component:ic,heading:e.labels.applyModalTitle,props:{class:"cmp-config-tool__apply-modal",labels:e.labels}})}};return me(u,y=>{y!=null&&y.data&&!(y!=null&&y.loading)&&_()},{deep:!0}),Et(()=>{t.setConfigApis(e.config),t.fetchInitialData()}),(y,$)=>{const M=gt("cmp-config-tool-alert");return n.value?(d(),f("section",{key:0,"data-qa":"mod-config-tool",class:X(["mod-config-tool","mod-config-tool-vue3",!l.value&&v(qe).LOADING])},[l.value?(d(),f(te,{key:0},[c("div",cc,q(p.value),1),Q(Ol,{labels:y.labels,"set-product-group-handler":v(t).setProductGroupHandler,group:g.value},null,8,["labels","set-product-group-handler","group"]),Q(Ai,{class:"cmp-config-tool-tabs",labels:y.labels,"config-tool-guide":y.configToolGuide,"open-modal":I},null,8,["labels","config-tool-guide"]),Q(Fi,{labels:y.labels,"open-modal":I},null,8,["labels"])],64)):F("",!0),i.value?(d(),B(M,{key:1,"data-qa":"cmp-config-tool-alert","alert-message":o.value},null,8,["alert-message"])):F("",!0)],2)):F("",!0)}}}),et=Ne("curveCanvasGraphs",()=>{const a=j({}),e=j([]),t=j({}),s=j({data:{}}),n=j({data:{}}),l=j({data:{}}),{pixelRatio:o}=Ua(),i=r(()=>y(!1,o.value)),u=r(()=>y(!0,o.value)),p=r(()=>{var C;return(C=a.value.comparisonCurveDataUrl)!=null?C:zt(Vt.COMPARISON_CURVE,!0)}),g=r(()=>{var C;return(C=a.value.compareLoadProfileUrl)!=null?C:zt(Vt.CAPACITY_RANGE,!0)}),_=r(()=>{var U,P;const{h:C,q:k}=(P=(U=s.value)==null?void 0:U.data)!=null?P:{};return{h:C,q:k}}),m=r(()=>{var U,P;const{h:C,q:k}=(P=(U=l.value)==null?void 0:U.data)!=null?P:{};return{h:C,q:k}}),b=C=>{e.value=C},h=C=>{t.value=C},I=C=>{a.value=C},y=(C=!1,k=1)=>{var E,N,ee;const U=(N=(E=e.value)==null?void 0:E.toString())!=null?N:"",{comparisoncurve:P,co2ImageLink:O,energychart:z,comparisonroi:V}=(ee=t.value)!=null?ee:{},J=se=>Math.round(k*se),w=se=>se!=null&&se.length&&(U!=null&&U.length)?Ue(se,{pumpsystemid:U,DPI:J(C?144:96),h:C?800:560,w:C?1200:560}):null;return{comparisonUrl:w(P),co2Url:w(O),energyUrl:w(z),lccUrl:w(V)}},$=({dpx:C,dpy:k})=>{var z;s.value=A(null,s.value);const U=C&&k,P=(z=e.value)==null?void 0:z.join(","),O=ne({dpi:Math.round(o.value*pa.DPI),fmt:da.PNG,h:560,w:560,pumpsystemid:P},U&&{dpx:C,dpy:k});return oe(p.value,{params:O}).then(V=>{s.value=A(V)}).catch(V=>{s.value=A(V,s.value)})},M=C=>ve($e,null,function*(){const{coords:k,dimensions:U,addParams:P,apiUrl:O}=C!=null?C:{},{h:z,w:V}=U!=null?U:{};if(!O){console.warn("API URL for Capacity Range Image Data is not provided");return}l.value=A(null,l.value);const J=ne(ne({dpi:Math.round(o.value*pa.DPI),fmt:da.PNG,h:z!=null?z:600,w:V!=null?V:600},P!=null?P:{}),k!=null?k:{});oe(O,{params:J}).then(w=>{l.value=A(w)}).catch(w=>{l.value=A(w,l.value)})}),H=new Map,R=j(null),L=j([]);return{setConfig:I,selectedPumpSystemIds:e,setSelectedPumpSystemIds:b,setResultsLinks:h,curveImagesUrls:i,curveImagesPrintUrls:u,compareCurveImgData:s,compareCurveLoadProfile:n,getCompareCurveImgData:$,getCompareCurveLoadProfile:C=>ve($e,null,function*(){var O;const k=(O=e.value)!=null?O:[],U=`${k==null?void 0:k.join(",")}#${C}`;R.value!==U&&(R.value=U,n.value=A(null));const P=L.value.join(",")!==(k==null?void 0:k.join(","));if(H.has(U)&&!P)n.value=A(H.get(U));else{L.value=k;try{const z={pumpsystemid:k==null?void 0:k.join(","),infotext:C},V=yield oe(g.value,{params:z});H.set(U,V),n.value=A(V)}catch(z){n.value=A(z,n.value)}}}),getCapacityRangeImgData:M,capacityRangeImgData:l,compareCurveImgDataItems:_,capacityRangeImgDataItems:m}}),We=Ne("curveCanvasStoreSetup",()=>{const a=j(!1),e=j([]),t=j({}),s=j({}),n=j({data:{}}),l=j({data:{}}),o=j({data:{}}),i=j({data:{}}),u=j(""),p=Se(),g=$a(),_=et(),m=r(()=>{var S;return(S=p.user)==null?void 0:S.isConfirmed}),b=r(()=>{var S;return(S=p.user)==null?void 0:S.isLoggedIn}),h=r(()=>m.value&&b.value),I=r(()=>g.userSessionInitialized),y=r(()=>_.compareCurveImgDataItems),$=r(()=>{var S;return(S=n.value)==null?void 0:S.loading}),M=r(()=>{var S;return(S=l.value)==null?void 0:S.data}),H=r(()=>{var S;return(S=M.value)==null?void 0:S.curveCanvasItem}),R=r(()=>{var S,W;return(W=(S=o.value)==null?void 0:S.data)==null?void 0:W.links}),L=r(()=>{var S;return(S=H.value)==null?void 0:S.qcid}),D=r(()=>{var S,W,x,ue,de;return(de=(ue=(x=(W=(S=i.value)==null?void 0:S.data)==null?void 0:W.groups)==null?void 0:x[0])==null?void 0:ue.questions)!=null?de:[]}),C=r(()=>{var S,W;return(W=(S=i.value)==null?void 0:S.data)==null?void 0:W.id}),k=r(()=>D.value.find(S=>S.label===rt.Flow)),U=r(()=>D.value.find(S=>S.label===rt.Head)),P=r(()=>{var S,W,x;return(x=(W=(S=o.value)==null?void 0:S.data)==null?void 0:W.fixedColumns)!=null?x:[]}),O=r(()=>{var S,W,x;return(x=(W=(S=o.value)==null?void 0:S.data)==null?void 0:W.headerlist)!=null?x:[]}),z=r(()=>{var S,W,x;return(x=(W=(S=o.value)==null?void 0:S.data)==null?void 0:W.pumpSystemIds)!=null?x:[]}),V=r(()=>{var S,W,x;return(x=(W=(S=o.value)==null?void 0:S.data)==null?void 0:W.items)!=null?x:[]}),J=r(()=>{var S;return((S=e.value)==null?void 0:S.length)>0?V.value.filter(W=>{var x;return e.value.includes((x=W==null?void 0:W.id)==null?void 0:x.value)}).map(W=>{var x;return(x=W.PUMPSYSTEMID)==null?void 0:x.value}):z.value}),w=S=>{e.value=S},E=(S,W)=>{var x;S&&(t.value=ne(ne({},t.value),S),_.setConfig(t.value)),W&&(s.value=ne(ne({},s.value),W)),I.value&&!a.value&&h.value&&(t.value.curveCanvasApiUrl&&N(),t.value.curveCanvasItemApiUrl&&((x=Z())==null||x.then(()=>{L.value&&(C.value||Ce(),ge())})),a.value=!0)},N=S=>{const{pageNumber:W,sortId:x,sortOrder:ue}=S!=null?S:{};return n.value=A(null,n.value),oe(t.value.curveCanvasApiUrl,{params:{sortId:x,sortOrder:ue,pageNumber:W}}).then(de=>{n.value=A(de)}).catch(de=>{n.value=A(de,n.value)})},ee=S=>(n.value=A(null,n.value),De(t.value.curveCanvasApiUrl,S).then(W=>(we(null,Fe.ADD_CANVAS),N(),W)).catch(W=>{var x,ue;return n.value=A(W,n.value),Promise.reject(new Error((ue=(x=W==null?void 0:W.error)==null?void 0:x.message)!=null?ue:"Failed to add curve canvas."))})),se=()=>{var x;const{canvasId:S,id:W}=(x=ke(window.location.search))!=null?x:{};return{id:S!=null?S:W}},Z=()=>{l.value=A(null,l.value);const S=se();return oe(t.value.curveCanvasItemApiUrl,{params:S}).then(W=>{l.value=A(W)}).catch(W=>{l.value=A(W,l.value)})},ie=({qcid:S})=>{const W=fe(ne({},H.value),{qcid:S}),x=se();return Ft(t.value.curveCanvasItemApiUrl,W,{params:x}).then(ue=>{l.value=A(ue)}).catch(ue=>{l.value=A(ue,l.value)})},pe=()=>{l.value=A(null,l.value);const S={id:H.value.id};return Xt(t.value.curveCanvasItemApiUrl,{params:S}).then(()=>{we(null,Fe.DELETE_CANVAS),l.value=A({data:{}})}).catch(W=>{var x,ue;return l.value=A(W,l.value),Promise.reject(new Error((ue=(x=W==null?void 0:W.error)==null?void 0:x.message)!=null?ue:"Failed to delete canvas."))})},ge=()=>{o.value=A(null,o.value);const S=se();return oe(t.value.curveCanvasResultGridApiUrl,{params:S}).then(W=>{o.value=A(W)}).catch(W=>{o.value=A(W,o.value)})},he=({name:S=H.value.name,comment:W=H.value.comment})=>{const{id:x,qcid:ue}=H.value,de={id:x,qcid:ue,name:S,comment:W};return Ft(t.value.curveCanvasApiUrl,ne({},de)).catch(()=>{Z()}).then(()=>{Z()})},Ce=S=>{i.value=A(null,i.value);const W=Kt(S==null?void 0:S[0]),x=S?Be.stringify({body:JSON.stringify(S)},{encode:!0}):{},ue=ne({qcid:L.value},W);return De(t.value.curveCanvasQuestionCatalogueUrl,x,{params:ue}).then(de=>{var T;return i.value=A(de),(T=de.data)==null?void 0:T.id}).then(de=>S&&de?ie({qcid:de}):Promise.resolve()).catch(de=>{i.value=A(de,i.value)})},Re=()=>{var ue,de,T,K;const{h:S,q:W}=y.value;if(S==null||W==null)return;const x=[{baseunit:(ue=U.value)==null?void 0:ue.baseunit,conunitkey:(de=U.value)==null?void 0:de.selectedunitoptionkey,convalue:`${S}`,label:rt.Head},{baseunit:(T=k.value)==null?void 0:T.baseunit,conunitkey:(K=k.value)==null?void 0:K.selectedunitoptionkey,convalue:`${W}`,label:rt.Flow}];Ce(x)};return me(I,()=>{E()}),me(()=>{var S;return(S=M.value)==null?void 0:S.overviewPageUrl},S=>{S!=null&&S.length&&(u.value=S)}),me(J,S=>{S&&_.setSelectedPumpSystemIds(S)}),me(R,S=>{S&&_.setResultsLinks(S)}),me(y,S=>{S&&Re()}),{addCurveCanvas:ee,curveCanvasConfig:t,curveCanvasLabels:s,curveCanvas:n,curveCanvasItem:H,curveCanvasItemDetails:l,curveCanvasQc:i,curveCanvasQcId:C,curveCanvasResultGrid:o,curveCanvasResultGridPumpSystemIds:z,curveCanvasResultGridItems:V,selectedPumpSystemIds:J,curveCanvasItemQcId:L,activeSelectedPumpSystemIds:e,deleteCurveCanvasItem:pe,getCanvasId:se,getCurveCanvas:N,getCurveCanvasItemDetails:Z,getCurveCanvasQc:Ce,getCurveCanvasResultGrid:ge,resultsLinks:R,initialize:E,isActive:h,isConfirmed:m,isCreated:a,isLoading:$,isLoggedIn:b,overviewPageUrl:u,putCurveCanvas:he,putCurveCanvasItem:ie,userSessionInitialized:I,updateCurveInput:Re,curveCanvasQuestions:D,curveCanvasResultGridHeaderlist:O,curveCanvasResultGridFixedColumns:P,setActiveSelectedValues:w,flow:k,head:U}}),uc={class:"b-deck b-deck--full-width b-theme--subtle"},dc=G({__name:"CmpCurveCanvasUserAlert",props:{labels:{},isLoggedIn:{type:Boolean}},setup(a){const e=a,t=r(()=>{const{isLoggedOut:s,isUnconfirmedUser:n}=e.labels;return e.isLoggedIn?n:s});return(s,n)=>{const l=gt("CmpAlert");return d(),f("div",uc,[Q(l,{class:"cmp-alert b-deck__inner",messages:[t.value]},null,8,["messages"])])}}}),pc={"data-qa":"cmp-curve-canvas-result-list",class:"cmp-result-list cmp-result-list--shadow"},mc={class:"cmp-result-list__body"},vc=G({__name:"CmpCurveCanvasResultList",props:{labels:{},id:{},headerlist:{},items:{},isLoading:{type:Boolean},sortHandler:{type:Function},sortId:{},sortOrder:{}},setup(a){const e=a,t=r(()=>e.headerlist.map(s=>fe(ne({},s),{align:"left"})));return(s,n)=>{var o;const l=gt("CmpAlert");return d(),f("section",pc,[c("div",mc,[(o=s.items)!=null&&o.length?(d(),B(St,{key:0,id:s.id,"data-qa":"cmp-curve-canvas-result-list-table",headerlist:t.value,items:s.items,class:"cmp-results-table",labels:s.labels,"sort-handler":s.sortHandler,"sort-id":s.sortId,"sort-order":s.sortOrder},{default:be(i=>[c("td",null,[c("span",null,q(i.value),1)])]),_:1},8,["id","headerlist","items","labels","sort-handler","sort-id","sort-order"])):s.isLoading?F("",!0):(d(),B(l,{key:1,"data-qa":"cmp-curve-canvas-result-list-alert",class:"cmp-alert",messages:[s.labels.noResults]},null,8,["messages"]))])])}}}),gc={key:0,"data-qa":"mod-curve-canvas-overview-wrapper",class:"mod-curve-canvas-overview-wrapper"},_c={class:"b-theme b-theme--underline cmp-standard-hero cmp-standard-hero--small"},bc={class:"cmp-standard-hero__inner"},hc={class:"cmp-standard-hero__content"},fc={class:"cmp-standard-hero__content-inner"},Cc={class:"elm-button__text"},yc={class:"b-deck b-deck--full-width b-theme b-theme--subtle"},qc={class:"b-deck__inner"},$c=G({__name:"ModCurveCanvasOverview",props:{labels:{},config:{}},setup(a){const e=a,t=We(),s=Se(),n=r(()=>t.curveCanvas),l=r(()=>{var u;return(u=s.user)==null?void 0:u.isLoggedIn}),o=r(()=>t.isActive&&t.userSessionInitialized),i=u=>{var g;const p={id:"name",label:"name",required:!0,maxlength:40,type:"text"};s.setOverlayComponent({component:Pa,heading:(g=e.labels)==null?void 0:g.legend,props:{labels:e.labels,submitHandler:t.addCurveCanvas,fields:[p]},origin:{target:u.target,x:u.clientX,y:u.clientY}})};return Ee(()=>{t.initialize(e.config)}),(u,p)=>o.value?(d(),f("div",gc,[c("div",_c,[c("div",bc,[c("div",hc,[c("div",fc,[Ye(u.$slots,"hero-content",{dataQa:"curve-canvas-overview-hero"}),c("button",{"data-qa":"curve-canvas-add-button",type:"button",class:"elm-button elm-curve-canvas-add-button__mobile",onClick:i},[c("span",Cc,q(u.labels.text),1)])])])])]),c("div",yc,[c("div",qc,[Q(Ia,Te(n.value,{id:"curvecanvas-list-results","data-qa":"curve-canvas-result-list",labels:u.labels,config:u.config,"filter-handler":v(t).getCurveCanvas,"update-handler":v(t).getCurveCanvas,"results-component":vc,class:"mod-result-list"}),null,16,["labels","config","filter-handler","update-handler"])])])])):(d(),B(dc,{key:1,labels:u.labels,"is-logged-in":l.value,"data-qa":"curve-canvas-overview-user-alert"},null,8,["labels","is-logged-in"]))}}),Ic={class:"cmp-curve-canvas-hero__section-inner"},Pc={class:"cmp-curve-canvas-hero__definition-title"},kc={class:"cmp-curve-canvas-hero__definition-value"},ht=G({__name:"CmpCurveCanvasHeroItem",props:{title:{},value:{}},setup(a){return(e,t)=>(d(),f("dl",Ic,[c("dt",Pc,q(e.title),1),c("dd",kc,q(e.value),1)]))}}),Sc={"data-qa":"curve-canvas-compare-hero",class:"cmp-curve-canvas-compare-hero"},Ac={class:"cmp-curve-canvas-hero__actions cmp-action-buttons cmp-action-buttons--align-right cmp-action-buttons--inline"},Ec={class:"b-layout-grid"},Rc={class:"b-layout-grid__item b-layout-grid__item--12 b-layout-grid__item--large-6 cmp-curve-canvas-hero__detail-container"},Uc={"data-qa":"curve-canvas-hero-title",class:"cmp-title cmp-title--1"},Lc=G({__name:"CmpCurveCanvasCompareHero",props:{labels:{}},setup(a){const e=a,t={group:"b-layout-grid__group",item:"b-layout-grid__item"},s=`${t.item} ${t.item}--12 ${t.item}--large-6 
${t.item}--xlarge-3 cmp-curve-canvas-hero__section`,n=We(),l=Se(),o=r(()=>n.curveCanvasItem),i=p=>{const{confirmation:g,failed:_}=e.labels.actions.delete;l.setConfirmation({message:g,origin:{target:p==null?void 0:p.target},confirmHandler:()=>n.deleteCurveCanvasItem().then(()=>{window.location.href=n.overviewPageUrl}).catch(()=>{l.setNotification({message:_,type:qe.ERROR})})})},u=p=>{var m;const g={id:"newName",label:"newName",required:!0,maxlength:40,type:"text",value:(m=o.value)==null?void 0:m.name},{editCanvas:_}=e.labels;l.setOverlayComponent({component:Pa,heading:_.legend,props:{labels:_,submitHandler:n.putCurveCanvas,fields:[g]},event:p})};return(p,g)=>(d(),f("div",Sc,[c("div",Ac,[Q(It,{id:"curve-canvas-action-edit",text:p.labels.actions.edit.text,icon:"edit_outline","on-click":u,"show-tooltip":""},null,8,["text"]),Q(It,{id:"curve-canvas-action-delete",text:p.labels.actions.delete.text,icon:"trash-can_outline","on-click":i,"show-tooltip":""},null,8,["text"])]),c("div",Ec,[c("div",{"data-qa":"curve-canvas-hero-title-container",class:X(`${t.group} h-vsb--xxlarge`)},[c("div",Rc,[c("h1",Uc,q(o.value.name),1)])],2),c("div",{class:X(`${t.group} h-vsb--large`)},[c("div",{class:X(s)},[Q(ht,{"data-qa":"curve-canvas-hero-created-by",title:p.labels.createdBy,value:o.value.createdby},null,8,["title","value"]),Q(ht,{"data-qa":"curve-canvas-hero-created-at",title:p.labels.created,value:v(ma)(o.value.createdat,!1)},null,8,["title","value"])]),c("div",{class:X(s)},[Q(ht,{"data-qa":"curve-canvas-hero-modified-by",title:p.labels.modifiedBy,value:o.value.modifiedby},null,8,["title","value"]),Q(ht,{"data-qa":"curve-canvas-hero-modified-at",title:p.labels.modifiedAt,value:v(ma)(o.value.modifiedat,!1)},null,8,["title","value"])])],2)])]))}}),Tc=Ne("productDataStore",{state:()=>({config:{},labels:{},productData:{data:{}}}),actions:fe(ne({},Yt()),{getProductData(a){const e=a==null?void 0:a.trim();if(!e||(e==null?void 0:e.length)<5)return this.productData={loading:!1,data:null},null;const t={productNumber:e};return this.productData=A(null,this.productData),oe(this.productApiUrl,{params:t}).then(({data:s=[]})=>{this.productData=A({data:s[0]})}).catch(s=>{this.productData=A(s,this.productData)})}}),getters:{productApiUrl(a){var e,t;return(t=(e=a==null?void 0:a.config)==null?void 0:e.productApiUrl)!=null?t:zt(is.PRODUCT_API_SELECTOR,!0)},productPumpSystemId(a){var e,t;return(t=(e=a.productData)==null?void 0:e.data)==null?void 0:t.pumpsystemid},productName(a){var e,t,s;return(s=(t=(e=a.productData)==null?void 0:e.data)==null?void 0:t.name)!=null?s:""},productExists(a){var e,t,s;return(s=(t=(e=a.productData)==null?void 0:e.data)==null?void 0:t.exist)!=null?s:!1},productCodes(a){var o,i,u,p;const e=(u=(i=(o=a.productData)==null?void 0:o.data)==null?void 0:i.productgroups)!=null?u:[],t=(p=e==null?void 0:e.length)!=null?p:0,s=g=>{var _,m;return(m=(_=e==null?void 0:e[t-g])==null?void 0:_.code)!=null?m:""},n=s(2),l=s(1);return{productFamily:n,productCode:l}},productNumber(a){var e,t,s;return(s=(t=(e=a.productData)==null?void 0:e.data)==null?void 0:t.productnumber)!=null?s:null},productCurveExists(a){var e,t,s,n;return(n=(s=(t=(e=a.productData)==null?void 0:e.data)==null?void 0:t.curve)==null?void 0:s.exist)!=null?n:!1},isUnknownProduct(){return!!this.productNumber&&!this.productExists},isInvalidCurveData(){return!!this.productExists&&!this.productCurveExists},isCorrectCurveData(){return!!this.productPumpSystemId&&!!this.productCurveExists}}}),ta=Ne("curveCanvasProductStore",()=>{const a=Se(),e=We(),t=j({data:{}}),s=j({data:{}}),n=j(!1),l=j(""),o=r(()=>e.curveCanvasConfig),i=r(()=>e.curveCanvasLabels),u=r(()=>e.curveCanvasItem),p=r(()=>e.activeSelectedPumpSystemIds),g=r(()=>{var P;return(P=t.value)==null?void 0:P.loading}),_=r(()=>{var P;return(P=t.value)==null?void 0:P.data}),m=r(()=>{var P;return(P=_.value)==null?void 0:P.pumpsystemid}),b=r(()=>{var P;return(P=s.value)==null?void 0:P.loading}),h=r(()=>{var P,O,z;return(z=(O=(P=s.value)==null?void 0:P.data)==null?void 0:O.groups)!=null?z:[]}),I=r(()=>h.value.map(P=>({id:P.label,label:P.text}))),y=r(()=>{var P,O;return(O=(P=s.value)==null?void 0:P.data)==null?void 0:O.id}),$=r(()=>{var z,V;const P=(z=h.value.find(J=>(J==null?void 0:J.label)===ut.Price))==null?void 0:z.questions,O=(V=P==null?void 0:P.find(J=>(J==null?void 0:J.label)===ut.HasUserPrice))==null?void 0:V.value;return typeof O=="string"?O==="true"?!0:O==="false"?!1:!!O:!!O}),M=r(()=>{var z,V;const P=(z=h.value.find(J=>(J==null?void 0:J.label)===ut.Cue))==null?void 0:z.questions,O=(V=P==null?void 0:P.find(J=>(J==null?void 0:J.label)===ut.CueProduct))==null?void 0:V.selectedoptionkey;return O!=null?O:""}),H=()=>ve($e,null,function*(){var O,z;t.value=A(null,t.value);const P={id:(O=u.value)==null?void 0:O.id,itemId:(z=p.value)==null?void 0:z[0]};return oe(o.value.curveCanvasItemsUrl,{params:P}).then(V=>{t.value=A(V)}).catch(V=>{t.value=A(V,t.value)})}),R=()=>ve($e,null,function*(){var O,z,V;const P={id:(O=u.value)==null?void 0:O.id,itemId:(z=p.value)==null?void 0:z[0],qcid:y.value};return Ft(o.value.curveCanvasItemsUrl,(V=t.value)==null?void 0:V.data,{params:P}).then(()=>ve($e,null,function*(){var J;!n.value&&$.value&&we(null,Fe.CURVE_CANVAS_USER_PRICE),(J=l.value)!=null&&J.length&&l.value!==M.value&&we(null,Fe.CURVE_CANVAS_CUE),yield e.getCurveCanvasResultGrid()}))}),L=()=>ve($e,null,function*(){var O;s.value=A(null,s.value);const P={pumpsystemid:m.value};return(O=oe(o.value.curveCanvasProductQuestionCatalogueUrl,{params:P}))==null?void 0:O.then(z=>{z.data&&(s.value=A(z),n.value=$.value,l.value=M.value)}).catch(z=>{s.value=A(z,s.value)})}),D=P=>ve($e,null,function*(){s.value=A(null,s.value);const O=ne({qcid:y.value,pumpsystemid:m.value},Kt(P)),z=Be.stringify({body:JSON.stringify([P])},{encode:!0});return De(o.value.curveCanvasProductQuestionCatalogueUrl,z,{params:O}).then(V=>{s.value=A(V)}).catch(V=>{s.value=A(V,s.value)})}),C=P=>{const{id:O}=u.value;if(O&&(P==null?void 0:P.length)>0){const z={id:O,pumpSystemIds:P};return De(o.value.curveCanvasItemsUrl,{},{params:z}).then(()=>Promise.resolve()).catch(V=>{var w,E,N,ee;const J=(E=(w=V==null?void 0:V.error)==null?void 0:w.message)!=null?E:"Failed to add product(s) to canvas.";return a.setNotification({message:((ee=(N=i.value)==null?void 0:N.errors)==null?void 0:ee.addProductFailed)||J,type:mt.ERROR}),Promise.reject(new Error(J))})}return Promise.reject(new Error("Invalid parameters."))},k=()=>ve($e,null,function*(){var z;const P={id:(z=u.value)==null?void 0:z.id,itemIds:p.value},O=Be.stringify(P,{arrayFormat:"repeat"});return Xt(`${o.value.curveCanvasItemsUrl}?${O}`,{}).then(()=>{e.getCurveCanvasResultGrid()}).catch(()=>{var V,J,w;a.setNotification({message:(w=(J=(V=i.value)==null?void 0:V.actions)==null?void 0:J.delete)==null?void 0:w.failed,type:mt.ERROR}),e.getCurveCanvasResultGrid()})}),U=P=>ve($e,null,function*(){var O,z,V,J;a.setConfirmation({message:(J=(V=(z=(O=i.value)==null?void 0:O.resultsTable)==null?void 0:z.actions)==null?void 0:V.delete)==null?void 0:J.confirmation,origin:{target:P==null?void 0:P.target},confirmHandler:()=>ve($e,null,function*(){yield k()})})});return me(m,()=>{L()}),{curveCanvasItem:u,selectedCurveCanvasItem:t,curveCanvasProductQc:s,selectedCurveItemPumpSystemId:m,getSelectedCurveCanvasItem:H,getCurveCanvasProductQc:L,updateCurveCanvasProductQc:D,curveCanvasQcLoading:b,selectedCanvasItemLoading:g,saveSelectedCurveCanvasItem:R,selectedCurveCanvasData:_,qcGroupTabs:I,qcGroups:h,curveCanvasQcId:y,addProductsToCanvas:C,deleteSelectedProducts:U,onDeleteSelectedProducts:k}}),wc={class:"cmp-curve-canvas-product-selector"},Dc={class:"message"},Oc={key:0,class:"warn"},Nc={key:1,class:"warn"},Mc=["disabled"],zc=G({__name:"CmpCurveCanvasProductSelector",props:{labels:{},toggleHandler:{type:Function}},setup(a){const e=a,t=Tc(),s=We(),n=ta(),l=r(()=>{var p;return(p=t.productPumpSystemId)!=null?p:""}),o=r(()=>t.isCorrectCurveData),i=()=>{t.getProductData(""),s.getCurveCanvasResultGrid(),e.toggleHandler()},u=()=>{o.value&&n.addProductsToCanvas([l.value]).then(()=>{var b;const{productFamily:p,productCode:g}=(b=t.productCodes)!=null?b:{},{productNumber:_}=t!=null?t:{};we({product:{variant:_,product:g,range:p}},Fe.ADD_PRODUCT_TO_CANVAS),i()}).catch(()=>{i()})};return(p,g)=>(d(),f("div",wc,[Q(ka,{id:"pumpselector-select-product-number",maxlength:"25",modifier:"inline",label:p.labels.productNumber,"handle-raw-value-update":v(t).getProductData,"use-debounce":""},null,8,["label","handle-raw-value-update"]),c("div",Dc,[ze(q(v(t).productName)+" ",1),v(t).isInvalidCurveData?(d(),f("div",Oc,q(p.labels.warnings.invalidCurveData),1)):F("",!0),v(t).isUnknownProduct?(d(),f("div",Nc,q(p.labels.warnings.unknownProduct),1)):F("",!0)]),c("button",{disabled:!o.value,type:"button",class:"elm-button",onClick:u},q(p.labels.actions.addProductNumber),9,Mc)]))}}),Fc=["data-qa"],Gc={class:"elm-link__text"},Bt=G({__name:"ElmCurveCanvasAddPumpButton",props:{id:{},labels:{}},setup(a){const e=a,t=s=>{Se().setOverlayComponent({component:zc,heading:e.labels.productSelector.header,props:{labels:e.labels.productSelector},origin:{target:s.target,x:s.clientX,y:s.clientY}})};return(s,n)=>(d(),f("button",{class:"elm-button cmp-action-buttons__action","data-qa":`${s.id}-add-pump-button`,"aria-controls":"overlay",type:"button",onClick:t},[c("span",Gc,q(s.labels.text),1)],8,Fc))}}),Qc={key:0,class:"cmp-curve-canvas-catalogue-question","data-qa":"curve-canvas-catalogue-question"},Hc=G({__name:"CmpCurveCanvasCatalogueQuestion",props:{config:{}},setup(a){const e=We(),t=r(()=>e.curveCanvasItemQcId),s=r(()=>e.curveCanvasQuestions),n=l=>{e.getCurveCanvasQc([l])};return(l,o)=>{var i;return((i=s.value)==null?void 0:i.length)>0?(d(),f("div",Qc,[(d(!0),f(te,null,le(s.value,u=>(d(),B(Ke,Te({ref_for:!0},u,{key:u==null?void 0:u.label,"data-qa":"curve-canvas-catalogue-question"+(u==null?void 0:u.label),"qc-id":t.value,"update-handler":n,"qc-api-url":l.config.curveCanvasQuestionCatalogue,class:"tooltip-middle","input-modifier":"inline","hide-input-if-disabled":""}),null,16,["data-qa","qc-id","qc-api-url"]))),128))])):F("",!0)}}}),Vc={class:X(["elm-tooltip__inner"])},Bc={key:0,class:"elm-title elm-title--3"},Yc=["aria-label"],Wc={"data-qa":"cmp-load_profile-comparison-table-header"},jc={class:"elm-table__row"},Jc=["data-qa"],Zc=["data-qa"],Xc=["data-qa"],Kc=["data-qa"],xc=G({__name:"CmpCurveCanvasComparisonTable",props:{loadProfile:{},activeInterestPoint:{},tooltipHeader:{}},setup(a){const e=a,t=j(null),s=j(0),n=r(()=>{var g;return(g=e.loadProfile.data)==null?void 0:g.columns}),l=r(()=>{var g;return(g=e.loadProfile.data)==null?void 0:g.rows}),o=r(()=>e.loadProfile.loading),i=r(()=>{const{positionLeft:g}=e.activeInterestPoint;return{top:s.value&&g?"-40px":"0px",left:`${g?g-s.value-20:0}px`,display:g?"table":"none"}}),u=()=>{var g,_;s.value=(_=(g=t.value)==null?void 0:g.getBoundingClientRect().width)!=null?_:0},p=new ResizeObserver(()=>{requestAnimationFrame(()=>{u()})});return Ee(()=>{u(),p.observe(t.value)}),ks(()=>{p.disconnect()}),(g,_)=>(d(),f("div",{ref_key:"root",ref:t,"data-qa":"cmp-load_profile-comparison-tooltip",style:Pt(i.value),class:X(["elm-tooltip","cmp-load_profile-comparison__tooltip",o.value&&v(qe).LOADING])},[c("div",Vc,[o.value?F("",!0):(d(),f("h3",Bc,q(g.tooltipHeader),1)),n.value&&l.value?(d(),f("table",{key:1,"data-qa":"cmp-load_profile-comparison-table",class:"elm-table elm-table--compact cmp-load-profile-comparison-table","aria-label":g.tooltipHeader},[c("thead",Wc,[c("tr",jc,[_[0]||(_[0]=c("th",{id:"cmpLoadProfileComparisonFirstCell",class:"elm-table__cell elm-table__cell--head","data-qa":"cmp-load_profile-comparison-table-header-first-cell"},null,-1)),(d(!0),f(te,null,le(n.value,(m,b)=>(d(),f("th",{key:m.pumpsystemid+"#ind#"+b+"LoadProfileComparison-header",class:"elm-table__cell elm-table__cell--head",scope:"col","data-qa":`cmp-load_profile-comparison-table-header-${b}`},q(m.displayname),9,Jc))),128))])]),c("tbody",null,[(d(!0),f(te,null,le(l.value,m=>(d(),f("tr",{key:m.label+"LoadProfileComparison-Row",class:"elm-table__row cmp-load-profile-table-body-row","data-qa":`cmp-load_profile-comparison-table-row-${m.label}`},[c("th",{class:"elm-table__cell elm-table__cell--head",scope:"row","data-qa":`cmp-load_profile-comparison-table-row-${m.label}-header`},q(m.text)+" ("+q(m.unit)+") ",9,Xc),(d(!0),f(te,null,le(m.values,(b,h)=>(d(),f("td",{key:b+"#rowValueInd#"+h,"data-qa":`cmp-load_profile-comparison-table-row-${m.label}-value-${h}`,class:"elm-table__cell"},q(b),9,Kc))),128))],8,Zc))),128))])],8,Yc)):F("",!0)])],6))}}),er={class:"cmp-curve-comparison","data-qa":"cmp-curve-comparison"},tr=G({__name:"CmpCurveCanvasComparison",props:{labels:{}},setup(a){const e=et(),t=Se(),s=j({}),n=j(!1),l=r(()=>t.isMobile),o=r(()=>e.compareCurveImgData),i=r(()=>e.compareCurveLoadProfile),u=r(()=>e.selectedPumpSystemIds),p=r(()=>{var h;return!!((h=u.value)!=null&&h.length)}),g=(h,I,y)=>{l.value||(h&&I&&y?(s.value={positionLeft:I,positionTop:y},e.getCompareCurveLoadProfile(h.infotext)):s.value={})},_=()=>{n.value=!0},m=()=>{n.value=!1},b=(h,I)=>{e.getCompareCurveImgData({dpx:h,dpy:I})};return Ee(()=>{b()}),me(u,()=>{b()}),(h,I)=>{var y;return d(),f("div",er,[Q(Sa,{"data-qa":"cmp-curves-img",class:"cmp-curves-img",labels:{heading:h.labels.tooltipHeader},"capacity-curve":o.value.data,"is-loading":o.value.loading,"on-set-point":b,"curve-height":v(Ht).ImgHeight,"curve-width":v(Ht).ImgWidth,"is-curve-editable":p.value,"interest-point-handler":g,"mouse-enter-curve":_,"mouse-leave-curve":m,"clear-classes":""},null,8,["labels","capacity-curve","is-loading","curve-height","curve-width","is-curve-editable"]),!l.value&&n.value&&((y=u.value)!=null&&y.length)?(d(),B(xc,{key:0,"load-profile":i.value,"active-interest-point":s.value,"tooltip-header":h.labels.tooltipHeader},null,8,["load-profile","active-interest-point","tooltip-header"])):F("",!0)])}}}),ar={ref:"stickyContainer"},sr={class:"cmp-curve-canvas-curve-tabs__graph"},nr=["src","alt"],or=.5,_a=0,Na=G({__name:"CmpCurveCanvasTabs",props:{labels:{},noSticky:{type:Boolean}},setup(a){const e=et(),t=Se(),s=a,n=j(0),l=vt("stickyStart"),o=vt("stickyPart"),i=Je(l),u=Je(o),{width:p,height:g}=As(),_=r(()=>t.isMobile),m=r(()=>{if(_.value||s.noSticky)return!1;const{bottom:$,height:M}=u,{bottom:H}=i,R=$.value+or>=H.value,L=H.value-M.value<=_a;return R&&L}),b=r(()=>_.value||m.value||s.noSticky?!1:i.top.value<=_a),h=r(()=>e.selectedPumpSystemIds),I=r(()=>e.curveImagesUrls),y=r(()=>{const{energyView:$,co2View:M,lccView:H,performanceView:R}=s.labels,{energyUrl:L,co2Url:D,lccUrl:C}=I.value;return[{label:$,src:L,id:"energy-view"},{label:M,src:D,id:"co2-view"},{label:H,src:C,id:"lcc-view"},{label:R,id:"performance-view",src:"1"}].filter(k=>!!k.src)});return me([p,g],()=>{n.value+=1}),($,M)=>{var H,R;return(H=h.value)!=null&&H.length?(d(),f("div",{key:0,ref_key:"stickyStart",ref:l,class:X(["cmp-curve-canvas-curve-tabs",m.value&&"cmp-curve-canvas-curve-tabs--pinned"])},[c("div",ar,[c("div",{ref_key:"stickyPart",ref:o,class:X([b.value&&"cmp-curve-canvas-curve-tabs__inner--sticky",m.value&&"cmp-curve-canvas-curve-tabs__inner--pinned"])},[(R=y.value)!=null&&R.length?(d(),B(Zt,{key:n.value,"data-qa":"cmp-curve-canvas-tabs",tabs:y.value,"default-tab-id":"performance-view",class:"cmp-tabs"},{tabContent:be(L=>[(d(!0),f(te,null,le(y.value,D=>(d(),B(Jt,{id:D.id,key:D.id,"data-qa":`cmp-curve-canvas-tab-${D.id}`,"tab-props":L,class:"cmp-tab",style:{maxHeight:"580px"}},{tab:be(()=>[c("div",sr,[D.src&&D.id!=="performance-view"?(d(),f("img",{key:0,class:"cmp-curve-canvas-roi-img",src:D.src,alt:D.label},null,8,nr)):D.id==="performance-view"?(d(),B(tr,{key:1,labels:$.labels.loadProfile},null,8,["labels"])):F("",!0)])]),_:2},1032,["id","data-qa","tab-props"]))),128))]),_:1},8,["tabs"])):F("",!0)],2)],512)],2)):F("",!0)}}}),lr=["data-qa"],ir={class:"h-hidden"},cr=G({__name:"CmpPrintSizingContent",props:{id:{},labels:{}},setup(a){const e=ot(),t=r(()=>{var i;return(i=e==null?void 0:e.printSettings)==null?void 0:i.curveCanvas}),s=r(()=>t.value?Object.entries(t.value).filter(([i,u])=>typeof u=="boolean").map(([i])=>i):[]),n=(i,u)=>{e.setPrintData("curveCanvas",i,u)},l=(i,u)=>{const{checked:p}=u.target;n(i,p)},o=i=>{const{value:u}=i.target;n("comment",u)};return(i,u)=>{var p,g;return d(),f("div",{"data-qa":i.id,class:"cmp-overlay-form__body cmp-print-curve-canvas__modal-content"},[c("fieldset",null,[c("legend",ir,q(i.labels.legend),1),(d(!0),f(te,null,le(s.value,_=>{var m,b,h;return d(),B(at,{id:`${i.id}__${_}`,key:_,"data-qa":`${i.id}__${_}`,value:(m=t.value)==null?void 0:m[_],label:(h=(b=i.labels)==null?void 0:b.fields)==null?void 0:h[_],onInput:I=>l(_,I)},null,8,["id","data-qa","value","label","onInput"])}),128)),(p=t.value)!=null&&p.useComment?(d(),B(cs,{key:0,id:`${i.id}__comment`,"data-qa":`${i.id}__comment`,value:(g=t.value)==null?void 0:g.comment,"value-update-handler":o},null,8,["id","data-qa","value"])):F("",!0)])],8,lr)}}}),rr=G({__name:"CmpPrintSizingModal",props:{id:{},labels:{},toggleHandler:{type:Function},onSubmitPrint:{type:Function}},setup(a){const e=a,t=ot(),s=r(()=>{var i;return(i=t==null?void 0:t.printSettings)==null?void 0:i.curveCanvas}),n=()=>{var u,p;const i=(p=Object.entries((u=s.value)!=null?u:{}))==null?void 0:p.reduce((g,[_,m])=>{const b=_==="comment"||_==="useComment";return g[_]=b?m:!0,g},{});t.setTopLevelData($t.curveCanvas,i)},l=()=>{n(),e.onSubmitPrint()},o=r(()=>{const{printDescription:i,fields:u}=e.labels;return fe(ne({},e.labels),{subHeading:i,fields:fe(ne({},u),{useComment:u.comments})})});return(i,u)=>(d(),B(Wt,{id:i.id,"toggle-handler":i.toggleHandler,labels:o.value,"print-type":v($t).curveCanvas,"content-component":cr,"on-submit-print":l,"on-cancel":n},null,8,["id","toggle-handler","labels","print-type"]))}}),ur=G({__name:"CmpPrintCurveCanvasModal",props:{labels:{},toggleHandler:{type:Function},onSubmitPrint:{type:Function}},setup(a){const e=ot(),t=We(),s=r(()=>t.resultsLinks),n=(l,o)=>{e.setPrintData("curveCanvas",l,o)};return Ee(()=>{var p,g,_;const{comparisonroi:l,co2ImageLink:o,energychart:i}=(p=s.value)!=null?p:{},u=m=>m?!0:null;n("lifecycleCostCurve",u(l)),n("co2Curve",u(o)),n("energyChart",u(i)),n("comment",(_=(g=t==null?void 0:t.curveCanvasItem)==null?void 0:g.comment)!=null?_:"")}),(l,o)=>(d(),B(rr,{id:"cmp-print-curve-canvas-content",labels:l.labels,"toggle-handler":l.toggleHandler,"on-submit-print":l.onSubmitPrint},null,8,["labels","toggle-handler","on-submit-print"]))}}),dr=["disabled","data-qa"],ba=G({__name:"ElmActionButtonPrintCurveCanvas",props:{id:{},labels:{},disabled:{type:Boolean},onSubmitPrint:{type:Function}},setup(a){const e=a,t=Se(),s=()=>{var n;t.setOverlayComponent({component:ur,heading:(n=e.labels)==null?void 0:n.text,props:{labels:e.labels,onSubmitPrint:e.onSubmitPrint}})};return(n,l)=>(d(),f("button",{disabled:n.disabled,"data-qa":`${n.id}-print-button`,type:"button",class:"elm-button cmp-action-buttons__action",onClick:s},[c("span",null,q(n.labels.text),1)],8,dr))}}),pr={key:0,class:"cmp-print-curve-canvas-graphs","data-qa":"cmp-print-curve-canvas-graphs"},mr=["data-qa"],vr={class:"cmp-print-curve-canvas-content__title"},gr=["src","alt"],_r=G({__name:"CmpPrintCurveCanvasGraphs",props:{setGraphsReady:{type:Function},images:{}},setup(a){const e=a,t=j(0),s=r(()=>e.images.length),n=()=>{t.value+=1};return me(t,l=>{l>=s.value&&e.setGraphsReady()}),(l,o)=>{var i;return(i=l.images)!=null&&i.length?(d(),f("div",pr,[(d(!0),f(te,null,le(l.images,u=>(d(),f("div",{key:u.src,class:"cmp-print-curve-canvas-content__graph","data-qa":`cmp-print-curve-canvas-content-graph-${u.label}`},[c("h3",vr,q(u.label),1),c("img",{class:"cmp-print-curve-canvas-content__img",src:u.src,alt:u.label,onLoad:n},null,40,gr)],8,mr))),128))])):F("",!0)}}}),br={"data-qa":"cmp-print-curve-canvas-content",class:"cmp-print-curve-canvas-content"},hr={class:"cmp-print-curve-canvas-content-page-setup"},fr={class:"cmp-print-curve-canvas-content__page-header"},Cr={class:"cmp-print-curve-canvas-content__header-logo"},yr=["src"],qr={class:"cmp-print-curve-canvas-content__header-info"},$r={"aria-describedby":"Curve Canvas Print Layout"},Ir={class:"cmp-print-curve-canvas-content__page"},Pr={key:0,class:"cmp-print-curve-canvas-content__comments"},kr={class:"cmp-print-curve-canvas-content__title"},Sr={class:"cmp-print-curve-canvas-content__sizing-input-fields"},Ar={class:"cmp-print-curve-canvas-content__page cmp-print-curve-canvas-content__results"},Er={class:"cmp-print-curve-canvas-content__title"},Rr=G({__name:"CmpPrintCurveCanvasIframeContent",props:{headerlist:{},labels:{},selectedItems:{},printData:{},curveImagesPrintUrls:{},companyPrintSettings:{},logoSrc:{},questions:{},disableIframe:{type:Function}},setup(a){const e=a,t=j(null),s=j(null),n=j(!1),{print:l}=rs(t,s,e.disableIframe),o=r(()=>{var _;return(_=e.curveImagesPrintUrls)!=null?_:[]}),i=r(()=>{const{comparisonUrl:_,lccUrl:m,co2Url:b,energyUrl:h}=o.value;return[{url:_,label:"performanceCurve"},{url:m,label:"lifecycleCostCurve"},{url:b,label:"co2Curve"},{url:h,label:"energyChart"}].map(y=>{var $,M;return{label:($=e.labels)==null?void 0:$[y.label],src:(M=e.printData)!=null&&M[y.label]?y.url:null}}).filter(y=>y.src)}),u=r(()=>{var H,R;const{companyName:_,createdBy:m,phone:b,date:h}=(H=e.labels)!=null?H:{},{companyname:I,createdby:y,companyphone:$,dateinheader:M}=(R=e.companyPrintSettings)!=null?R:{};return[{label:_,value:I},{label:m,value:y},{label:b,value:$},{label:h,value:M}]}),p=()=>{n.value=!0},g=()=>{setTimeout(()=>{l()},0)};return Ee(()=>{var _;(!((_=i.value)!=null&&_.length)||n.value)&&g()}),me(n,_=>{_&&g()}),(_,m)=>{var b,h,I,y,$,M;return Oe((d(),f("div",br,[c("div",{ref_key:"iframeContent",ref:s,"data-qa":"cmp-print-curve-canvas-iframe-content"},[c("section",hr,[c("div",fr,[c("div",Cr,[c("img",{src:_.logoSrc,alt:"Grundfos",class:"cmp-print-curve-canvas-content__header-logo-image"},null,8,yr)]),c("div",qr,[(d(!0),f(te,null,le(u.value,H=>(d(),B(us,{key:H.label,modifier:"inline",value:H.value,label:H.label,"show-empty-value":""},null,8,["value","label"]))),128))])]),c("table",$r,[m[0]||(m[0]=c("thead",{class:"cmp-print-curve-canvas-content__page-header-placeholder"},[c("tr",null,[c("th",null,[c("div",{class:"cmp-print-curve-canvas-content__page-header-space"})])])],-1)),c("tbody",null,[c("tr",null,[c("td",null,[c("div",Ir,[(h=(b=_.printData)==null?void 0:b.comment)!=null&&h.length&&((I=_.printData)!=null&&I.useComment)?(d(),f("div",Pr,q((y=_.printData)==null?void 0:y.comment),1)):F("",!0),c("h3",kr,q(_.labels.sizingInput),1),c("div",null,[c("div",Sr,[(d(!0),f(te,null,le(_.questions,H=>(d(),B(Ke,Te({ref_for:!0},H,{key:H.label,labels:_.labels,"is-print":!0,modifier:"inline",class:"cmp-print-curve-canvas-content__cmp-input"}),null,16,["labels"]))),128))])])]),c("div",Ar,[c("h3",Er,q(_.labels.sizingResult),1),($=_.selectedItems)!=null&&$.length?(d(),B(St,{key:0,id:"cmp-print-curve-canvas-content-results",headerlist:_.headerlist,items:_.selectedItems,class:"cmp-results-table cmp-results-table--shadow","use-forced-resize":""},null,8,["headerlist","items"])):F("",!0)]),(M=i.value)!=null&&M.length?(d(),B(_r,{key:0,id:"cmp-print-curve-canvas-graphs","set-graphs-ready":p,images:i.value},null,8,["images"])):F("",!0)])])])])])],512),c("iframe",{ref_key:"iframe",ref:t,"data-qa":"cmp-print-curve-canvas-iframe",title:"cmp-print-curve-canvas-iframe"},null,512)],512)),[[nt,!1]])}}}),Ur={class:"cmp-result-list__header cmp-result-list__header--compact cmp-result-list__header--fill"},Lr={class:"cmp-result-list__header-inner"},Tr={class:"cmp-result-list__actions"},wr={class:"cmp-form-option cmp-result-list__selector"},Dr=["id","checked","disabled","name"],Or=["for"],Nr={class:"cmp-results-table__label"},Mr={"data-qa":"result-list-actions",class:"cmp-action-buttons cmp-action-buttons--inline"},zr=G({__name:"CmpResultListHeader",props:{labels:{},selectAllId:{},items:{},allSelected:{type:Boolean},activeSelectedValues:{},selectAll:{type:Function}},setup(a){return(e,t)=>{var s,n;return d(),f("header",Ur,[c("div",Lr,[c("div",Tr,[c("div",wr,[c("input",{id:e.selectAllId,checked:e.allSelected,disabled:!((s=e.items)!=null&&s.length),name:e.selectAllId,"data-qa":"result-list-select-all-checkbox",class:"cmp-form-option__field cmp-form-option__field--checkbox",type:"checkbox",value:"all",onChange:t[0]||(t[0]=(...l)=>e.selectAll&&e.selectAll(...l))},null,40,Dr),c("label",{for:e.selectAllId,"data-qa":"result-list-select-all-label",class:"cmp-form-option__label cmp-form-option__label--checkbox"},[c("span",Nr,q(e.labels.selectAll),1),c("span",null,"("+q((n=e.activeSelectedValues)==null?void 0:n.length)+")",1)],8,Or)]),c("div",Mr,[Ye(e.$slots,"actions")])])])])}}}),Fr={class:"cmp-curve-canvas-item-edit-overlay__product"},Gr={class:"cmp-curve-canvas-item-edit-overlay__product-name"},Qr={class:"cmp-curve-canvas-item-edit-overlay__tab-content"},Hr=G({__name:"CmpCurveCanvasItemEditOverlay",props:{labels:{},toggleHandler:{type:Function}},setup(a){const e=a,t=j(0),s=ta(),n=r(()=>s.qcGroupTabs),l=r(()=>s.qcGroups),o=r(()=>s.curveCanvasQcId),i=r(()=>s.selectedCurveCanvasData),u=r(()=>s.curveCanvasQcLoading),p=r(()=>s.selectedCanvasItemLoading),g=()=>{s.saveSelectedCurveCanvasItem(),e.toggleHandler()};return Ee(()=>ve(this,null,function*(){yield s.getSelectedCurveCanvasItem()})),(_,m)=>{var b;return d(),f("div",{class:X(["cmp-curve-canvas-item-edit-overlay",p.value&&v(qe).LOADING])},[c("div",Fr,[c("div",Gr,[c("strong",null,q(i.value.productname),1)]),ze(" "+q(_.labels.productNumber)+" "+q(i.value.productno),1)]),(b=n.value)!=null&&b.length?(d(),B(Zt,{id:"curve-canvas-item-edit-overlay-curve-tabs",key:t.value,tabs:n.value,class:X(["cmp-tabs cmp-curve-canvas-item-edit-overlay__inner",u.value&&v(qe).LOADING]),"data-qa":"cmp-curve-canvas-item-edit-overlay-tabs"},{tabContent:be(h=>[(d(!0),f(te,null,le(l.value,I=>(d(),B(Jt,{id:I.label,key:I.label,"tab-props":h,"data-qa":`cmp-curve-canvas-item-edit-tab-${I.label}`,class:"cmp-tab"},{tab:be(()=>[c("div",Qr,[(d(!0),f(te,null,le(I.questions,y=>(d(),B(Ke,Te({ref_for:!0},y,{key:y.label,"data-qa":"curve-canvas-item-edit-qc"+y.label,"qc-id":o.value,"update-handler":v(s).updateCurveCanvasProductQc,"input-modifier":"inline",class:"tooltip-middle"}),null,16,["data-qa","qc-id","update-handler"]))),128))])]),_:2},1032,["id","tab-props","data-qa"]))),128))]),_:1},8,["tabs","class"])):F("",!0),Q(Aa,{"is-loading":u.value||p.value,"confirm-label":_.labels.submit,"cancel-label":_.labels.cancel,"on-cancel":_.toggleHandler,"on-confirm":g,"data-qa":"cmp-curve-canvas-item-edit-overlay-footer"},null,8,["is-loading","confirm-label","cancel-label","on-cancel"])],2)}}}),Vr=G({__name:"ElmCurveCanvasEditButton",props:{id:{},labels:{},isDisabled:{type:Boolean}},setup(a){const e=a,t=Se(),s=r(()=>t.isMobile),n=l=>{t.setOverlayComponent({component:Hr,heading:e.labels.header,props:{labels:e.labels},origin:{target:l==null?void 0:l.target,x:l==null?void 0:l.clientX,y:l==null?void 0:l.clientY},noContentAttrs:!s.value,contentClassName:s.value?"":"cmp-curve-canvas-item-edit-overlay__overlay-top-screen"})};return(l,o)=>{var i;return d(),B(It,{id:`action-edit-${l.id}`,text:(i=l.labels)==null?void 0:i.text,"is-disabled":l.isDisabled,"on-click":n,icon:"edit_outline","show-tooltip":""},null,8,["id","text","is-disabled"])}}}),Br={class:"cmp-curve-canvas-results-table"},Yr={class:"b-deck--full-width"},Wr="id.value",jr=G({__name:"CmpCurveCanvasResultsTable",props:{id:{},labels:{},logoSrc:{},config:{}},setup(a){const e=a,t=ot(),s=We(),n=et(),l=ta(),o=j(!1),i=j(Me.PRODNAME),u=j(Dt.ascending),p=r(()=>s.curveCanvasResultGridItems),g=r(()=>s.curveCanvasResultGridHeaderlist),_=r(()=>s.curveCanvasResultGridFixedColumns),m=r(()=>s.selectedPumpSystemIds),b=r(()=>{var w,E;return{text:(E=(w=e.labels.actions)==null?void 0:w.addProduct)==null?void 0:E.text,productSelector:e.labels.productSelector}}),h=r(()=>{var w;return(w=t==null?void 0:t.printSettings)==null?void 0:w.curveCanvas}),I=r(()=>s.curveCanvasQuestions),{selectedValues:y,activeSelectedValues:$,allSelected:M,selectAll:H,toggleSelectValue:R}=Ea(Wr,p),L=w=>{var ie;const E=i.value,N=(ie=w[E])==null?void 0:ie.value;if(!N)return N;const ee=pe=>{const ge=Number(pe);return Number.isNaN(ge)?Number.MIN_SAFE_INTEGER:ge},se={[Me.Price]:" ",[Me.SPEED_RPM]:"-",[Me.U]:"-"};return se[E]?ee(N.split(se[E])[0]):[Me.P2KW,Me.PHASE,Me.Energy,Me.LCC].includes(E)?ee(N):N},D=r(()=>{var w;return(w=g.value)==null?void 0:w.map(E=>fe(ne({},E),{sortable:!0,align:"left"}))}),C=r(()=>ds(p.value,[L],[u.value])),k=r(()=>{var w,E;return(w=$.value)!=null&&w.length?(E=C.value)==null?void 0:E.filter(N=>{var ee;return $.value.includes((ee=N==null?void 0:N.id)==null?void 0:ee.value)}):C.value}),U=w=>{const E=Dt.ascending,N=Dt.descending,ee=u.value===N?E:N;i.value===w?u.value=ee:i.value=w},P=()=>{var w;we(null,Fe.GPC_PRINT_FILE),o.value=!0,s.putCurveCanvas({comment:(w=h.value)==null?void 0:w.comment})},O=()=>{o.value=!1},z=w=>{const{curveImagesUrls:E}=n,N={};return w.co2Curve&&(N.co2ImageUrl=E.co2Url),w.energyChart&&(N.energyChartImageUrl=E.energyUrl),w.lifecycleCostCurve&&(N.lccImageUrl=E.lccUrl),w.performanceCurve&&(N.performanceCurveUrl=E.comparisonUrl),N},V=w=>(w||[]).filter(E=>!E.hidden).map(E=>Ca(E)),J=w=>{var ee,se,Z;const E={};E.canvasInputs=V(I.value),E.compareListTable=ps(D.value,k.value,"PRODNO"),E.printSettings=w!=null?w:{};const N=z(w);return E.imageLinksCount=Object.keys(N).length,E.imageLinks=ms(N),E.labels=(Z=(se=(ee=e.labels.actions)==null?void 0:ee.print)==null?void 0:se.pdf)!=null?Z:{},Promise.resolve(E)};return me($,w=>{s.setActiveSelectedValues(w)}),(w,E)=>{var N,ee,se,Z,ie,pe,ge,he;return d(),f("div",Br,[c("section",null,[Q(ba,{id:w.id,class:"cmp-curve-canvas-results-table__mobile-button",labels:(N=w.labels.actions)==null?void 0:N.print,disabled:!((ee=m.value)!=null&&ee.length),"on-submit-print":P},null,8,["id","labels","disabled"]),Q(Bt,{id:w.id,class:"cmp-curve-canvas-results-table__mobile-button",labels:b.value},null,8,["id","labels"]),c("div",Yr,[Q(zr,{"data-qa":"curve-canvas-results-table",labels:{selectAll:w.labels.selectAll},"select-all-id":"curve-canvas-results-table-select-all",items:p.value,"all-selected":v(M),"active-selected-values":v($),"select-all":v(H)},{actions:be(()=>{var Ce,Re,S,W,x,ue,de;return[Q(Vr,{id:w.id,labels:(Ce=w.labels.actions)==null?void 0:Ce.edit,"is-disabled":((Re=v($))==null?void 0:Re.length)!==1},null,8,["id","labels","is-disabled"]),Q(It,{id:`action-delete-${w.id}`,text:(W=(S=w.labels.actions)==null?void 0:S.delete)==null?void 0:W.text,"is-disabled":!((x=v($))!=null&&x.length),"on-click":v(l).deleteSelectedProducts,icon:"trash-can_outline","show-tooltip":""},null,8,["id","text","is-disabled","on-click"]),Q(ba,{id:w.id,class:"cmp-curve-canvas-results-table__desktop-button",labels:w.labels.actions.print,disabled:!((ue=m.value)!=null&&ue.length),"on-submit-print":P},null,8,["id","labels","disabled"]),w.config.enablePrintCurveCanvasViaJasper?(d(),B(jt,{key:0,class:"cmp-action-buttons__action cmp-curve-canvas-results-table__desktop-button","template-id":"CurveCanvas","setting-id":"curvecanvas",disabled:!((de=m.value)!=null&&de.length),"settings-right-column":["language-optimizedpumpcomparison-group"],"button-text-override":`${w.labels.actions.print.text||""} - Print Jasper`,"map-print-request-body":J,config:w.config,labels:w.labels.actions.print},null,8,["disabled","button-text-override","config","labels"])):F("",!0),Q(Bt,{id:w.id,class:"cmp-curve-canvas-results-table__desktop-button",labels:b.value},null,8,["id","labels"])]}),_:1},8,["labels","items","all-selected","active-selected-values","select-all"]),p.value?(d(),B(St,{key:0,id:`table-${w.id}`,"fixed-columns":_.value,headerlist:D.value,items:C.value,"select-id":"id","selected-values":v(y),"select-handler":v(R),"sort-handler":U,"sort-id":i.value,"sort-order":u.value,class:"cmp-results-table"},null,8,["id","fixed-columns","headerlist","items","selected-values","select-handler","sort-id","sort-order"])):F("",!0),o.value?(d(),B(Rr,{key:1,headerlist:g.value,labels:fe(ne({},(Z=(se=w.labels.actions)==null?void 0:se.print)==null?void 0:Z.pdf),{dataTableLabel:(pe=(ie=w.labels.actions)==null?void 0:ie.print)==null?void 0:pe.dataTableLabel}),"selected-items":k.value,"print-data":h.value,"curve-images-print-urls":v(n).curveImagesPrintUrls,"company-print-settings":(he=(ge=v(t))==null?void 0:ge.printSettings)==null?void 0:he.company,"logo-src":w.logoSrc,questions:v(s).curveCanvasQuestions,"disable-iframe":O},null,8,["headerlist","labels","selected-items","print-data","curve-images-print-urls","company-print-settings","logo-src","questions"])):F("",!0)])])])}}}),Jr={key:0},Zr={class:"b-layout-grid__group b-deck"},Xr={key:1,class:"b-deck b-deck--full-width b-theme--subtle"},Kr={class:"cmp-alert b-deck__inner"},xr={class:"cmp-text cmp-alert__message"},eu={key:0},tu={key:1},ha="b-layout-grid__item b-layout-grid__item--12 b-layout-grid__item--large-6",au=G({__name:"ModCurveCanvasCompare",props:{id:{},labels:{},config:{},logoSrc:{}},setup(a){const e=a,t=We(),s=r(()=>{var o;return(o=t.curveCanvasItemDetails)==null?void 0:o.loading}),n=r(()=>{var o,i;return{text:(i=(o=e.labels.actions)==null?void 0:o.addProduct)==null?void 0:i.text,productSelector:e.labels.productSelector}}),l=r(()=>t.isActive&&t.userSessionInitialized);return Et(()=>{t.initialize(e.config,e.labels)}),me(()=>t.curveCanvasItemQcId,o=>{o&&t.getCurveCanvasResultGrid()}),(o,i)=>{var u;return l.value?(d(),f("div",Jr,[(u=v(t).curveCanvasItem)!=null&&u.id?(d(),f("div",{key:0,class:X(["cmp-curve-canvas-comparison-detail",s.value&&v(qe).LOADING])},[Q(Lc,{labels:o.labels.hero},null,8,["labels"]),Q(Bt,{id:o.id,class:"cmp-curve-canvas-comparison-detail__button-mobile",labels:n.value},null,8,["id","labels"]),c("div",Zr,[Q(Hc,{class:X(ha),config:o.config},null,8,["config"]),Q(Na,{class:X(ha),labels:o.labels.curves},null,8,["labels"])]),Q(jr,{id:`cmp-curve-canvas-results-table-${o.id}`,labels:fe(ne({},o.labels.resultsTable),{productSelector:o.labels.productSelector}),config:o.config,"logo-src":o.logoSrc},null,8,["id","labels","config","logo-src"])],2)):F("",!0)])):(d(),f("div",Xr,[c("div",Kr,[c("div",xr,[v(t).isLoggedIn?v(t).isConfirmed?F("",!0):(d(),f("p",tu,q(o.labels.details.isUnconfirmedUser),1)):(d(),f("p",eu,q(o.labels.details.isLoggedOut),1))])])]))}}});var ce=(a=>(a.HERO="UxHero",a.JOURNEY="UxJourney",a.REQUIRED="UxRequired",a.EDIT_LOAD_PROFILE="EditLoadProfile",a.TYPE="RESTSelectHowToSize",a.RANGE="RESTProductFamily",a.PRODUCT="RESTProductGroup",a.APPLICATION_NAME="RESTApplicationName",a.PUMP_DESIGN="RESTPumpDesign",a.APP_AREA="UxArea",a.APPLICATION="UxApplication",a.INSTALLATION="UxInstallation",a.LOCATION="LocationMap",a.LONGITUDE="SLocLon",a.LATITUDE="SLocLat",a.SHOW_SIZING_RESULT="ShowSizingResult",a.VIEW="View",a.SIZE_BY="SizeBy",a.MAINS_VOLTAGE="MainsVoltage",a.PUMPS_WITH_EXTERNAL_FREQUENCY_DRIVE="IncludeFixedSpeedWithVfd",a.FLOW="Flow",a.HEAD="Head",a.NO_OF_HITS="NoOfHits",a.PUMP_FAMILY="PumpFamily",a))(ce||{}),He=(a=>(a.PARAMS_UPDATED="Sizing parameters updated",a.LOAD_PARAMS="gpcLoadParameters",a.SAVE_PARAMS="gpcSaveParameters",a.RESET="gpcResetDefaults",a.PAGE_LOAD="Sizing page load",a.NO_RESULTS="gpcNoResults",a.FIRST_LOAD="Sizing result grid shown first time",a.JOURNEY_CHANGED="Sizing journey selector changed",a.PRINT="gpcPrintFile",a))(He||{}),Ae=(a=>(a.SELECTED_OPTION="selectedoptionkey",a.HOT_WATER_RECIRC="DBDHW_RE",a.ADVANCED="Advanced",a.STANDARD="Standard",a.ONE_PAGE_SIZING="SizingOnePage",a.GUIDED_SELECTION="GuidedSelection",a.QUICK_SIZING="QuickSizing",a.WASTE_WATER="SEWAGE",a.SIZE_BY_PUMP_FAMILY="SizeByPumpFamily",a.YES="1Yes",a))(Ae||{}),yt=(a=>(a.RANGE="range",a.CATEGORY="category",a.CODE_PUMP_DESIGN="PUMPDESIGNID-",a))(yt||{}),Qe=(a=>(a.PRODUCT_NUMBER_ID="PRODNO",a.PUMP_SYSTEM_ID="PUMPSYSTEMID",a.COMMENT="#COMMENT",a.PRODUCT_NAME="PRODNAME",a.COMMENTS="comments",a.SALES_PRICE="SalesPrice",a))(Qe||{}),tt=(a=>(a.CURVE="optimizedpumpcomparisoncurve",a.CO2="co2ImageLink",a.ENERGY_CHART="energychart",a.ROI="optimizedpumpcomparisonroi",a))(tt||{}),kt=(a=>(a.Sizing="sizing",a.Variants="variants",a))(kt||{}),je=(a=>(a.SIZING_CAPACITY_RANGE="fetchsizingcapacityrange",a.CAPACITY_RANGE="fetchcapacityrange",a.OPT_COMP_RESULT_GRID="optimizedPumpComparisonresultgrid",a.RESULT_GRID="resultgrid",a))(je||{}),Ma=(a=>(a[a.TIMEOUT=600]="TIMEOUT",a))(Ma||{});const su=(a,e,t)=>{const{hasMissingFields:s}=ea(),n=Se(),l=r(()=>n.isMobile),o=r(()=>n.isEditMode),i=r(()=>{var T,K;return(K=(T=a.value)==null?void 0:T.groups)!=null?K:[]}),u=r(()=>{var T,K;return(K=(T=a.value)==null?void 0:T.links)!=null?K:[]}),p=r(()=>m(ce.REQUIRED)),g=r(()=>{var T,K;return(K=(T=p.value)==null?void 0:T.questions)!=null?K:[]}),_=T=>{var K;return(K=u.value)==null?void 0:K.find(ae=>ae.rel===T)},m=T=>{var K;return(K=i.value)==null?void 0:K.find(ae=>(ae==null?void 0:ae.label)===T)},b=T=>{var K;return(K=g.value)==null?void 0:K.find(ae=>(ae==null?void 0:ae.label)===T)},h=T=>{var K,ae;return(ae=(K=L.value)==null?void 0:K.find(Ie=>(Ie==null?void 0:Ie.label)===T))==null?void 0:ae[Ae.SELECTED_OPTION]},I=r(()=>{const T=i.value.reduce((K,ae)=>K.concat(ae.questions),[]);return s(T)}),y=r(()=>{var T,K;return(K=(T=m(ce.HERO))==null?void 0:T.questions)!=null?K:[]}),$=r(()=>y.value.find(({label:T})=>T===ce.JOURNEY)),M=r(()=>{const T=_(je.SIZING_CAPACITY_RANGE),{sizingCapacityRangeApiUrl:K,capacityRangeApiUrl:ae}=e.value;return T?K:ae}),H=r(()=>{var ae,Ie;const T=(ae=_(je.SIZING_CAPACITY_RANGE))==null?void 0:ae.href,K=(Ie=_(je.CAPACITY_RANGE))==null?void 0:Ie.href;return T!=null?T:K}),R=r(()=>vs(ke(H.value),["pgcode","qcid"])),L=r(()=>y.value.filter(({label:T})=>T!==ce.FLOW&&T!==ce.HEAD&&T!==ce.JOURNEY)),D=r(()=>b(ce.FLOW)),C=r(()=>b(ce.HEAD)),k=[ce.LOCATION,ce.LATITUDE,ce.LONGITUDE],U=r(()=>{const[T,K,ae]=k.map(b);return{location:T,latitude:K,longitude:ae}}),P=r(()=>{const{location:T,latitude:K,longitude:ae}=U.value;return T&&K&&ae?[...k]:[]}),O=r(()=>!!_(je.CAPACITY_RANGE)),z=r(()=>!!_(je.RESULT_GRID)),V=r(()=>{var T;return((T=$.value)==null?void 0:T[Ae.SELECTED_OPTION])===Ae.ONE_PAGE_SIZING}),J=r(()=>{var T;return((T=$.value)==null?void 0:T[Ae.SELECTED_OPTION])===Ae.GUIDED_SELECTION}),w=r(()=>h(ce.SIZE_BY)===Ae.QUICK_SIZING),E=r(()=>w.value&&!l.value),N=r(()=>{var T;return((T=b(ce.APPLICATION_NAME))==null?void 0:T[Ae.SELECTED_OPTION])===Ae.WASTE_WATER}),ee=r(()=>h(ce.TYPE)===Ae.SIZE_BY_PUMP_FAMILY),se=r(()=>ee.value?yt.RANGE:yt.CATEGORY),Z=r(()=>{var ae;const T=ee.value?ce.RANGE:ce.PUMP_DESIGN,K=(ae=b(T))==null?void 0:ae[Ae.SELECTED_OPTION];return ee.value?K:`${yt.CODE_PUMP_DESIGN}${K}`}),ie=r(()=>i.value.filter(({label:T})=>T!==ce.HERO&&T!==ce.REQUIRED)),pe=r(()=>{var T;return!!((T=ie.value)!=null&&T.length)||n.isEditMode}),ge=r(()=>!!p.value||n.isEditMode),he=r(()=>{const T=g.value.find(({label:ae})=>ae===ce.NO_OF_HITS),K=`${T==null?void 0:T.text}: ${T==null?void 0:T.value}`;return T?K:""}),Ce=r(()=>{var Ie,ye,Le,Y;const T=Number((ye=(Ie=C.value)==null?void 0:Ie.value)!=null?ye:0),K=Number((Y=(Le=D.value)==null?void 0:Le.value)!=null?Y:0),ae=T!==0&&K!==0;return!(!H.value||V.value&&ae)}),Re=r(()=>i.value.filter(({label:T})=>T===ce.EDIT_LOAD_PROFILE||T===ce.REQUIRED)),S=r(()=>{var T;return((T=n==null?void 0:n.user)==null?void 0:T.isLoggedIn)||!!e.value.enableAnonymousAccess}),W=r(()=>{var T;return((T=$.value)==null?void 0:T[Ae.SELECTED_OPTION])===Ae.ADVANCED}),x=r(()=>{const T=y.value.find(({label:K})=>K===ce.INSTALLATION);return(T==null?void 0:T.selectedoptionkey)===Ae.HOT_WATER_RECIRC&&t.value.hotWaterRecirculationLink}),ue=r(()=>{var ye,Le,Y;const{value:T,selectedunitoptionkey:K}=(ye=D.value)!=null?ye:{},{value:ae,selectedunitoptionkey:Ie}=(Le=C.value)!=null?Le:{};return{userJourney:{journey:"sizing",subjourney:(Y=$.value)==null?void 0:Y[Ae.SELECTED_OPTION],stepType:"sizing",data:{type:h(ce.TYPE),application:h(ce.APPLICATION),applicationArea:h(ce.APP_AREA),pumpDesign:h(ce.PUMP_DESIGN),range:h(ce.RANGE),product:h(ce.PRODUCT)},flowCurrent:T&&`${T} ${K}`,headCurrent:ae&&`${ae} ${Ie}`}}}),de=r(()=>{var ye;const{locationMapUpdateApiUrl:T,mapboxUrl:K,mapboxGeocodeUrl:ae,mapboxAccessToken:Ie}=(ye=e.value)!=null?ye:{};return{locationMapUpdateApiUrl:T,mapboxUrl:K,mapboxGeocodeUrl:ae,mapboxAccessToken:Ie}});return{groups:i,links:u,hasMissingInputs:I,basicQuestions:y,capacityRangeApiUrl:M,capacityRangeLink:H,capacityRangeParams:R,criteria:L,paramsContent:p,paramsQuestions:g,flow:D,head:C,mapLabels:P,journey:$,isCurveClickable:O,isOnePageSizing:V,isQuickSizing:w,isSplitQuestionFields:E,isWasteWater:N,isSizeByPumpFamily:ee,getResourceType:se,getTypeCode:Z,moreContent:ie,moreIsVisible:pe,noOfHits:he,showCapacityRangeCurve:Ce,onePageSizingPrintContent:Re,enabledAdvancedOption:S,isAdvancedOptionSelected:W,showHwrLink:x,trackingData:ue,isGuidedSelection:J,mapConfigUrls:de,isMobile:l,isEditMode:o,paramsAreVisible:ge,sizingMapQuestions:U,hasResultGrid:z,setOverlayComponent:n.setOverlayComponent,setConfirmation:n.setConfirmation,setNotification:n.setNotification}},nu=s=>ve($e,[s],function*({updatedInputs:a,url:e,qcId:t}){const n=Array.isArray(a)?a:[a];let l={qcid:t};n.forEach(u=>{u.baseunit!==u.conunitkey&&(l=ne(ne({},l),Kt(u)))});const o=Be.stringify({body:JSON.stringify(n)},{encode:!0}),{data:i}=yield De(e,o,{params:l});return i}),ou=s=>ve($e,[s],function*({ctx:a,url:e,qcId:t}){const{signal:n}=a,l=ne({},t&&{qcid:t}),{data:o}=yield oe(e,{params:l,signal:n});return o}),za=Ne("useOnePageSizing",()=>{const a="PUMPSYSTEMID.value",e=et(),t=Ge();let s=!0;const n=j({data:{}}),{qcId:l,config:o,appUrls:i,links:u}=Ve(t),p=r(()=>{var E,N;return(N=(E=n.value)==null?void 0:E.data)!=null?N:{}}),g=r(()=>{var E,N;return(N=(E=p.value)==null?void 0:E.headerlist)!=null?N:[]}),_=r(()=>{var E,N;return(N=(E=p.value)==null?void 0:E.rows)!=null?N:[]}),m=r(()=>{var Z;const E=(Z=window==null?void 0:window.location)==null?void 0:Z.origin,N=g.value,ee=_.value;if(!(N!=null&&N.length))return{headerlist:[],items:[],totalhits:0};const se=ee.map(ie=>{var de,T,K;const pe=ie.tag,ge=(de=ie.links)==null?void 0:de.find(ae=>ae.rel===kt.Variants),he=(T=ie==null?void 0:ie[Qe.COMMENTS])!=null?T:null,Ce=`${E}?pumpsystemid=${pe}`,{key:Re,value:S}=(K=ie.cells[0])!=null?K:{},W=Re!=null?Re:S,x=(ae,Ie)=>{var Y;const ye=(Y=N[Ie])==null?void 0:Y.label,{value:Le}=ae;if(ye===Qe.PRODUCT_NUMBER_ID)return{[ye]:{href:Ce,value:Le}};if(ye===Qe.PRODUCT_NAME){const re=ge?V(ie):Ce;return{[ye]:{href:re,value:Le,sizingCriteria:W}}}return ye===Qe.COMMENT&&he!=null?{[ye]:{comments:he,value:Le}}:{[ye]:fe(ne({},ae),{editable:ye===Qe.SALES_PRICE})}},ue=ie.cells.reduce((ae,Ie,ye)=>ne(ne({},ae),x(Ie,ye)),{});return ue[Qe.PUMP_SYSTEM_ID]={value:pe},ue});return{headerlist:N,items:se,totalhits:se.length}}),b=r(()=>{var E,N;return(N=(E=m.value)==null?void 0:E.items)!=null?N:[]}),{selectedValues:h,selectableValues:I,activeSelectedValues:y,allSelected:$,selectAll:M,toggleSelectValue:H}=Ea(a,b),R=r(()=>{var ee,se,Z;const E=(ee=u.value)==null?void 0:ee.find(ie=>ie.rel===je.OPT_COMP_RESULT_GRID),N=(Z=(se=m.value)==null?void 0:se.totalhits)!=null?Z:0;return!!E&&N>0}),L=r(()=>{var E,N;return(N=(E=p.value)==null?void 0:E.links)!=null?N:[]}),D=r(()=>P(tt.CURVE)),C=r(()=>{const E=h.value.length?h.value:I.value;return D.value?E:[]}),k=()=>{s&&(t.setTracking(He.FIRST_LOAD),s=!1)},U=()=>{M({target:{checked:!0}})},P=E=>{var ee;const N=L.value.find(se=>se.rel===E);return(ee=N==null?void 0:N.href)!=null?ee:null},O=()=>{const E={comparisoncurve:P(tt.CURVE),co2ImageLink:P(tt.CO2),energychart:P(tt.ENERGY_CHART),comparisonroi:P(tt.ROI)};e.setResultsLinks(E)},z=E=>{n.value=A(E)},V=E=>{const N={sQcid:l.value,pumpsystemid:E.tag},{resultsPageUrl:ee}=i.value;return Ue(ee,N)},J=()=>ve($e,null,function*(){var N;k(),z(null);const E={qcid:l.value};return oe((N=o.value)==null?void 0:N.sizingResultsGridApiUrl,{params:E}).then(ee=>{z(ee)})}),w=E=>{var ie,pe,ge;const N=(ie=E==null?void 0:E.PUMPSYSTEMID)==null?void 0:ie.value,ee=g.value.findIndex(he=>(he==null?void 0:he.label)===Qe.SALES_PRICE),se=_.value.find(he=>he.tag===N);if((pe=se==null?void 0:se.cells)==null?void 0:pe[ee]){z(null);const he=Be.stringify({body:JSON.stringify(p.value)},{encode:!0});De((ge=o.value)==null?void 0:ge.sizingResultsGridApiUrl,he,{}).then(Ce=>{z(Ce)}).catch(Ce=>{z(Ce)})}};return me(C,(E,N)=>{E!=null&&E.length&&E!==N&&e.setSelectedPumpSystemIds(E)}),me(L,(E,N)=>{!pt(E)&&E!==N&&O()}),{onePageSizingOriginalResults:n,onePageSizingResultsData:m,hasOnePageSizingResults:R,performanceCurveUrl:D,pumpSystemIds:C,startOnePageSizing:J,selectAllOnePageSizingResults:U,updateOnePageSizingResults:w,activeSelectedValues:y,selectableValues:I,allSelected:$,selectAll:M,toggleSelectValue:H}}),Ge=Ne("useSizingQc",()=>{const a=gs(),e=et();let t={};const s=j({}),n=j({}),l=j({}),o=j({data:{}}),i=j(void 0),u=j([]),p=j([]),g=Ls(),_=r(()=>["qcData",n.value.qcApiUrl]),m=Ts({queryKey:_,queryFn:Y=>ou({ctx:Y,url:n.value.qcApiUrl,qcId:i.value}),enabled:r(()=>!!n.value.qcApiUrl),staleTime:1/0,refetchOnWindowFocus:!1}),b=Y=>{g.setQueryData(_.value,Y)},h=ws({mutationFn:Y=>nu({updatedInputs:Y,url:n.value.qcApiUrl,qcId:k.value}),onSuccess:b}),I=Es(()=>{p.value.length&&(h.mutate([...p.value]),p.value=[])},500,{maxWait:1e3}),y=Y=>{p.value.push(...Array.isArray(Y)?Y:[Y]),I()},$=Y=>ve($e,null,function*(){const re=[...p.value,...Array.isArray(Y)?Y:[Y]];p.value=[],yield h.mutateAsync(re)}),M=Y=>{var _e;const re=(_e=Y==null?void 0:Y.data)!=null?_e:{};b(re)},H=()=>{const{plg:Y}=ke(window.location.search),re=Y&&i.value;i.value=re,m.refetch(),S(He.RESET)},R=r(()=>m.error),L=r(()=>m.isError.value),D=r(()=>m.isLoading.value),C=r(()=>m.isFetched.value),k=r(()=>{var Y,re;return(re=(Y=m.data)==null?void 0:Y.value)==null?void 0:re.id}),U=r(()=>e.compareCurveImgDataItems),P=r(()=>e.capacityRangeImgDataItems),O=su(m.data,n,s),{journey:z,isAdvancedOptionSelected:V,enabledAdvancedOption:J,isOnePageSizing:w,trackingData:E,getResourceType:N,getTypeCode:ee,head:se,flow:Z,isQuickSizing:ie,isWasteWater:pe,setConfirmation:ge}=O,he=r(()=>{var Y;return(Y=z==null?void 0:z.value)==null?void 0:Y.selectedoptionkey}),Ce=r(()=>{var Y;return(Y=u.value)==null?void 0:Y.length}),Re=r(()=>{var Y;return m.isLoading.value||m.isFetching.value||((Y=h.isPending)==null?void 0:Y.value)||a.isCalculatorStackLoading}),S=Y=>{we(E.value,Y)},W=(Y,re,_e)=>ve($e,null,function*(){const{sQcid:Pe,qcId:lt}=ke(window.location.search);i.value=Pe!=null?Pe:lt,s.value=Y,n.value=re,l.value=_e,yield Rs(m.isSuccess).toBe(!0),e.setConfig(fe(ne({},n.value),{comparisonCurveDataUrl:re.sizingOnePageCurveDataApiUrl,compareLoadProfileUrl:re.sizingOnePageCompareLoadProfileUrl})),t=za(),V.value&&ue(),w.value&&t.startOnePageSizing(),S(He.PAGE_LOAD)}),x=(Y,re)=>{var sa,na;if(Y==null||re==null||!Y||!re)return;const{baseunit:_e,selectedunitoptionkey:Pe}=(sa=se.value)!=null?sa:{},{baseunit:lt,selectedunitoptionkey:wt}=(na=Z.value)!=null?na:{},Ha=[{baseunit:_e,conunitkey:Pe,convalue:`${Y}`,label:ce.HEAD},{baseunit:lt,conunitkey:wt,convalue:`${re}`,label:ce.FLOW}];y(Ha)},ue=()=>{const Y={baseunit:"",label:ce.JOURNEY,conunitkey:"",convalue:Ae.STANDARD};y(Y)},de=()=>{var Pe;const{advancedSizingPageUrl:Y}=(Pe=s.value)!=null?Pe:{},_e=Y.length?Y:"/content/grundfos/www/en/solutions/products/advanced-sizing";window.location.href=Ue(_e,{sQcid:k.value})},T=()=>ve($e,null,function*(){const Y={baseunit:"",label:ce.JOURNEY,conunitkey:"",convalue:Ae.ADVANCED},re={baseunit:"",label:ce.SIZE_BY,conunitkey:"",convalue:ce.PUMP_FAMILY},_e=z.value?[Y]:[re,Y];yield $(_e),J.value&&V.value&&de()}),K=()=>{var Pe;const{goToSizingUrl:Y}=(Pe=n.value)!=null?Pe:{},re={resourcetype:N==null?void 0:N.value,typecode:ee==null?void 0:ee.value},_e=Ue(Y,re);window.location.href=_e},ae=()=>{ge({message:"confirmationMessage",confirmHandler:K})},Ie=(Y,re)=>{pt(re)?u.value=u.value.filter(_e=>_e.label!==Y):u.value.some(Pe=>Pe.label===Y)||u.value.push({label:Y,error:re})},ye=()=>ve($e,null,function*(){const Y=()=>Promise.reject(new Error);return Ce.value?Y():ie.value&&pe.value?(ae(),Y()):Promise.resolve()}),Le=(Y,re)=>{const{h:_e,q:Pe}=Y,{h:lt,q:wt}=re;_e===lt&&Pe===wt||_e===0&&Pe===0||!_e||!Pe||x(_e,Pe)};return me(U,Le),me(P,Le),me(he,(Y,re)=>{re&&Y!==re&&S(He.JOURNEY_CHANGED)}),me(k,(Y,re)=>{var _e;Y&&Y!==re&&(qt({sQcid:k.value}),w.value&&((_e=t==null?void 0:t.startOnePageSizing)==null||_e.call(t)))}),fe(ne({qcId:k,appUrls:s,config:n,updateBuffer:p,labels:l,qcContent:o,initStore:W,onReset:H,hasQcError:L,setQcContent:M,setTracking:S,onSelectAdvancedSizing:T,compareCurveImgDataItems:U,goToQuickSizing:K,updateSizing:y,syncCapacityRange:x,updateSizingImmediate:$,hasErrors:Ce,handleFieldValidation:Ie,onStartQcSizing:ye,isFirstLoad:D,isFetched:C},O),{qcIsLoading:Re,qcError:R})}),lu={class:"cmp-sizing-params__deck-header"},iu={class:"cmp-title cmp-title--1"},cu={key:0,class:"cmp-sizing-params__explanation"},ru=["href"],Fa=G({__name:"CmpSizingParamsHeader",props:{label:{},link:{},tooltip:{}},setup(a){return(e,t)=>(d(),f("div",lu,[c("h2",iu,q(e.label),1),e.link?(d(),f("div",cu,[c("a",{href:e.link,target:"_blank",rel:"noopener noreferrer",class:"elm-link"},q(e.tooltip),9,ru)])):F("",!0)]))}}),uu=Ne("useSizingResults",()=>{const a=Ge(),{config:e,qcId:t,appUrls:s,isGuidedSelection:n}=Ve(a),l=j({data:{}}),o=r(()=>{var y,$,M;return(M=($=(y=l.value)==null?void 0:y.data)==null?void 0:$.links)!=null?M:[]}),i=r(()=>{var y,$,M,H;return(H=(M=($=(y=l.value)==null?void 0:y.data)==null?void 0:$.result)==null?void 0:M.totalhits)!=null?H:0}),u=r(()=>{var y,$,M,H;return(H=(M=($=(y=l.value)==null?void 0:y.data)==null?void 0:$.result)==null?void 0:M.message)!=null?H:null}),p=r(()=>{var y;return(y=l.value)==null?void 0:y.loading}),g=y=>{l.value=A(y)},_=y=>{const $={qcid:t.value};return oe(y,{params:$}).then(M=>(g(M),M)).catch(M=>(g(M),M))},m=()=>{const y={qcid:t.value};return _s({baseUrl:e.value.sizingResultsBaseApiUrl,totalTimeout:Ma.TIMEOUT,params:y}).then($=>{const M=$!=null?$:{data:{}};return g(M),$}).catch($=>(g($),$))},b=()=>{g(null);const{guidedSelectionResultsApiUrl:y,isJobManagerForSizingEnabled:$,sizingResultsApiUrl:M}=e.value;return n.value?_(y):$?m():_(M)},h=()=>{var C,k,U,P;const{resultsPageUrl:y}=s.value,{plg:$}=ke(window.location.search),{isJobManagerForSizingEnabled:M}=e.value,H=o.value.find(O=>O.rel==="self"),L=((P=(U=(k=(C=H==null?void 0:H.href)==null?void 0:C.split)==null?void 0:k.call(C,"?"))==null?void 0:U[0])!=null?P:"").split("/").pop(),D=ne(ne({sQcid:t.value},M&&{sJobId:L}),$&&{plg:$});return Ue(y,D)},I=()=>{window.location.href=h()};return me(t,y=>{y&&(l.value=null)}),{sizingResults:l,totalResults:i,resultsLoading:p,getSizingResults:b,getResultsPageUrl:h,goToResultsPage:I,setSizingResults:g,getResultsByApiUrl:_,getPooljobSizingResults:m,resultMessage:u}}),Ga=Ne("useSizingParams",()=>{var u,p,g,_,m;const a=(m=(_=(g=(p=(u=window==null?void 0:window.grundfos)==null?void 0:u.apiUrls)==null?void 0:p.productCenter)==null?void 0:g.userSavedItems)==null?void 0:_.sizingParams)!=null?m:"",t={isLoggedIn:!0,isConfirmed:!0,warningMessage:Ot("savedItems.unauthorizedWarning","productCenter")},s=Se(),n=j({data:{}}),l=b=>(n.value=A(null,n.value),oe(a,{params:b,user:t}).then(h=>{n.value=A(h)}).catch(h=>{n.value=A(h,n.value)}));return{sizingParams:n,getSizingParams:l,addSizingParams:b=>(n.value=A(null,n.value),De(a,b,{user:t}).then(()=>(s.setNotification({message:Ot("savedItems.addedToSavedSizingParams","productCenter"),type:mt.INFO}),l())).catch(h=>{var y,$;n.value=A(h,n.value);const I=($=(y=h==null?void 0:h.error)==null?void 0:y.message)!=null?$:"Failed to add sizing params.";return Promise.reject(new Error(I))})),deleteSizingParams:(b=[])=>(n.value=A(null,n.value),Xt(a,{params:{id:b.join(",")},user:t}).then(()=>(s.setNotification({message:Ot("savedItems.removedFromSavedSizingParams","productCenter",b.length),type:mt.INFO}),l())).catch(h=>{var y,$;n.value=A(h,n.value);const I=($=(y=h==null?void 0:h.error)==null?void 0:y.message)!=null?$:"Failed to delete sizing params.";return Promise.reject(new Error(I))}))}}),du=["data-qa"],pu={class:"cmp-result-list__body"},mu=["onClick"],vu={key:1},gu=G({__name:"CmpSizingParamsResultList",props:{labels:{},id:{},headerlist:{},items:{},sortId:{},sortOrder:{},updateHandler:{type:Function},resultHandler:{type:Function}},setup(a){const e=a,t=r(()=>e.headerlist.map(n=>fe(ne({},n),{align:"left"}))),s=r(()=>e.items.map(i=>{var u=i,{qcid:n,title:l}=u,o=ca(u,["qcid","title"]);return ne({title:{rel:kt.Sizing,value:l.value,qcid:n.value,href:l.href}},o)}));return(n,l)=>{var o;return d(),f("section",{"data-qa":`cmp-sizing-params-result-list-${n.id}`,class:"cmp-result-list"},[c("div",pu,[(o=s.value)!=null&&o.length?(d(),B(St,{key:0,id:`${n.id}-results-table`,"data-qa":`cmp-sizing-params-result-list-table-${n.id}`,headerlist:t.value,items:s.value,labels:n.labels,"sort-handler":n.updateHandler,"sort-id":n.sortId,"sort-order":n.sortOrder,class:"cmp-results-table cmp-results-table--shadow"},{default:be(i=>[c("td",null,[i.rel===v(kt).Sizing?(d(),f("button",{key:0,type:"button",class:"elm-link",onClick:u=>n.resultHandler(i)},q(i.value),9,mu)):(d(),f("span",vu,q(i.value),1))])]),_:1},8,["id","data-qa","headerlist","items","labels","sort-handler","sort-id","sort-order"])):F("",!0)])],8,du)}}}),_u={class:"cmp-sizing-load-params"},bu=G({__name:"CmpSizingLoadParamsModal",props:{labels:{},loadHandler:{type:Function},toggleHandler:{type:Function}},setup(a){const e=a,t=Ga(),s=r(()=>t.sizingParams),n=l=>{e.loadHandler(l==null?void 0:l.href),e.toggleHandler()};return(l,o)=>(d(),f("div",_u,[Q(Ia,Te(s.value,{id:"saved-items-sizing-params","data-qa":"sizing-params-result-list",labels:l.labels,"result-handler":n,"update-handler":v(t).getSizingParams,"results-component":gu,class:"mod-result-list"}),null,16,["labels","update-handler"])]))}}),hu={class:"cmp-overlay-form__body"},fu={class:"h-hidden"},Cu=G({__name:"CmpSizingSaveParamsForm",props:{qcId:{},labels:{},submitHandler:{type:Function},toggleHandler:{type:Function}},setup(a){const e=a,t=Ga(),s=j(null),n=()=>{t.addSizingParams({qcid:e.qcId,title:s.value}).then(()=>{e.submitHandler(),e.toggleHandler()})};return(l,o)=>(d(),f("form",{"data-qa":"cmp-sizing-save-params-form",autocomplete:"off",class:"cmp-overlay-form",onSubmit:st(n,["prevent"])},[c("div",hu,[c("fieldset",null,[c("legend",fu,q(l.labels.legend),1),Q(ka,{id:"params-save-name",modelValue:s.value,"onUpdate:modelValue":o[0]||(o[0]=i=>s.value=i),"data-qa":"params-save-name",label:l.labels.fields.title.label,modifier:"inline",required:""},null,8,["modelValue","label"])])]),Q(Aa,{"confirm-label":l.labels.actions.submit,"cancel-label":l.labels.actions.cancel,"on-confirm":n,"on-cancel":l.toggleHandler},null,8,["confirm-label","cancel-label","on-cancel"])],32))}}),yu={class:"cmp-confirmation-dialog__body"},qu={class:"cmp-confirmation-dialog__text"},$u={class:"cmp-confirmation-dialog__footer"},Iu=G({__name:"CmpNoSizingResultsOverlay",props:{labels:{},message:{},toggleHandler:{type:Function},navigateToWhereToBuy:{type:Function}},setup(a){return(e,t)=>(d(),f("div",null,[c("div",yu,[c("p",qu,q(e.message),1)]),c("div",$u,[c("p",null,[c("button",{ref:"cancel",type:"button",class:"elm-button elm-button--ghost cmp-confirmation-dialog__footer-button",onClick:t[0]||(t[0]=(...s)=>e.navigateToWhereToBuy&&e.navigateToWhereToBuy(...s))},q(e.labels.contactUs),513),c("button",{type:"button",class:"elm-button cmp-confirmation-dialog__footer-button",onClick:t[1]||(t[1]=(...s)=>e.toggleHandler&&e.toggleHandler(...s))},q(e.labels.goBackToSizing),1)])])]))}}),Pu={id:"sizing-params-actions",class:"cmp-sizing-params-actions__inner"},ku={class:"cmp-sizing-params-actions__label",for:"sizing-params-actions"},Su={class:"elm-button__text"},Au={class:"elm-button__text"},Eu={class:"cmp-sizing-params-actions__button"},Ru=["title"],Uu=["disabled"],Lu={class:"elm-button__text"},Tu=G({__name:"CmpSizingParamsActions",setup(a){const e=Ge(),t=uu(),{loginChecker:s}=bs(),{qcId:n,appUrls:l,labels:o,qcIsLoading:i,hasErrors:u,hasResultGrid:p}=Ve(e),{resultsLoading:g,totalResults:_,resultMessage:m}=Ve(t),b=vt("sizing-container"),{noResults:h,results:I,actions:y,loadParams:$,couldNotFindTheProduct:M,noSizingResultsModal:H}=o.value,R=r(()=>i.value||g.value),L=r(()=>p.value?!1:_.value===0),D=r(()=>L.value?h:y.start),C=r(()=>!!u.value||!!L.value),k=r(()=>{var Z,ie;return(ie=(Z=b.value)==null?void 0:Z.parentElement)!=null?ie:null}),U=Z=>{s({action:()=>{e.setOverlayComponent({component:bu,showToggle:!0,heading:$==null?void 0:$.legend,props:{labels:$,loadHandler:pe=>{e.setTracking(He.LOAD_PARAMS),window.location&&(window.location.href=pe)}},origin:{target:Z==null?void 0:Z.target,x:Z==null?void 0:Z.clientX,y:Z==null?void 0:Z.clientY}})},checkEmployee:!1,evt:Z})},P=Z=>{s({action:()=>{e.setOverlayComponent({component:Cu,heading:o.value.saveParams.legend,props:{labels:o.value.saveParams,qcId:n.value,submitHandler:()=>{e.setTracking(He.SAVE_PARAMS)}},origin:{target:Z.target,x:Z.clientX,y:Z.clientY}})},checkEmployee:!1,evt:Z})},O=Z=>{var pe;const ie=(pe=m.value)!=null?pe:y==null?void 0:y.sizingNoResultPrompt;e.setOverlayComponent({component:Iu,showToggle:!1,heading:M,props:{labels:H,message:ie,navigateToWhereToBuy:()=>{var ge;window.location.href=(ge=l.value)==null?void 0:ge.whereToBuyUrl}},isTransparent:!0,contentClassName:"cmp-alert-confirmation-dialogue",origin:{target:Z==null?void 0:Z.target,x:Z==null?void 0:Z.clientX,y:Z==null?void 0:Z.clientY}})},z=()=>{e.setNotification({message:I.noResultsForInput,type:"error"})},V=Z=>ve(this,null,function*(){e.onStartQcSizing().then(()=>{t.getSizingResults().then(()=>{L.value?(e.setTracking(He.NO_RESULTS),O(Z)):t.goToResultsPage()}).catch(()=>{t.setSizingResults({data:{}}),z()})}).catch(()=>{})}),{top:J,height:w}=Je(k),{top:E}=Je(document.body),{height:N}=Je(b),{y:ee}=Us(),se=r(()=>{if(!J.value)return!1;const Z=J.value-E.value,pe=Z+w.value-N.value;return ee.value>=Z&&ee.value<=pe});return(Z,ie)=>{var pe,ge,he,Ce,Re;return d(),f("div",{ref:"sizing-container","data-qa":"sizing-params-actions",class:X(["cmp-sizing-params-actions",se.value&&"is-stuck"])},[c("div",Pu,[c("label",ku,q((pe=v(y))==null?void 0:pe.chooseAction),1),c("button",{"data-qa":"load-params",class:"elm-button elm-button--ghost cmp-sizing-params-actions__button",type:"button",onClick:U},[c("span",Su,q((ge=v(y))==null?void 0:ge.load),1)]),c("button",{"data-qa":"save-params",class:"elm-button elm-button--ghost cmp-sizing-params-actions__button",type:"button",onClick:P},[c("span",Au,q((he=v(y))==null?void 0:he.save),1)]),c("div",Eu,[c("button",{"data-qa":"reset-params",title:(Ce=v(y))==null?void 0:Ce.reset,type:"button",class:"elm-link elm-link--icon-reset_outline",onClick:ie[0]||(ie[0]=(...S)=>v(e).onReset&&v(e).onReset(...S))},q((Re=v(y))==null?void 0:Re.reset),9,Ru)]),c("button",{"data-qa":"start-sizing",class:X(["elm-button","elm-button--positive","cmp-sizing-params-actions__start-sizing",R.value&&v(qe).LOADING]),disabled:C.value,type:"submit",onClick:V},[c("span",Lu,q(D.value),1)],10,Uu)])],2)}}}),wu={class:"cmp-sizing-params-wrapper"},Du={id:"sizing-params",class:"cmp-sizing-params__deck b-theme b-theme--underline b-deck"},Ou={class:"b-deck__inner"},aa=G({__name:"CmpSizingParamsWrapper",props:{showParams:{type:Boolean},showActions:{type:Boolean}},setup(a){return(e,t)=>(d(),f("form",{ref:"uxRequiredForm",class:"cmp-sizing-params-wrapper-form",autocomplete:"off",onSubmit:t[0]||(t[0]=st(()=>{},["prevent"])),onKeydown:t[1]||(t[1]=Ss(st(()=>{},["prevent"]),["enter"]))},[Oe(c("div",wu,[e.showActions?(d(),B(Tu,{key:0})):F("",!0),c("div",Du,[c("div",Ou,[Ye(e.$slots,"params")])])],512),[[nt,e.showParams]])],544))}}),Nu={class:"b-layout-grid"},Mu={class:"b-layout-grid__group"},zu={class:X(["b-layout-grid__item b-layout-grid__item--12"])},Fu=G({__name:"CmpSizingParamsQuickSizing",setup(a){const e=["Flow","Head","NumberPumps","NumberPumpsBackup","MainsVoltage"],t=Ge(),{isMobile:s,isEditMode:n,qcId:l,paramsContent:o,mapLabels:i,config:u,qcIsLoading:p,labels:g,mapConfigUrls:_}=Ve(t);return(m,b)=>(d(),B(aa,{"show-actions":"","show-params":v(o)||v(n)},{params:be(()=>{var h,I,y;return[c("div",Nu,[c("div",Mu,[c("div",zu,[Q(Fa,{label:v(g).questionCatalogue.selectParameters,link:(h=v(o))==null?void 0:h.explanationlink,tooltip:(I=v(o))==null?void 0:I.explanationtooltip},null,8,["label","link","tooltip"]),Q(At,{groups:[v(o)],"hidden-question-labels":v(i),"input-modifier":"inline",labels:v(g).questionCatalogue,"is-loading":v(p),"qc-api-url":(y=v(u))==null?void 0:y.qcApiUrl,"qc-id":v(l),"set-qc-content":v(t).setQcContent,"update-handler":v(t).updateSizing,"map-config-urls":v(_),"show-group-descriptions":!1,"is-split-question-fields":!v(s),"split-column-labels":v(s)?[]:e,"handle-field-validation":v(t).handleFieldValidation,"show-missing-inputs":!1},null,8,["groups","hidden-question-labels","labels","is-loading","qc-api-url","qc-id","set-qc-content","update-handler","map-config-urls","is-split-question-fields","split-column-labels","handle-field-validation"])])])])]}),_:1},8,["show-params"]))}}),Gu={class:"cmp-capacity-curve-img","data-qa":"cmp-capacity-curve-img"},Qu=G({__name:"CmpCapacityCurveImg",props:{labels:{},isCurveEditable:{type:Boolean},apiUrl:{},params:{},dimensions:{}},setup(a){const e=a,t=et(),s=r(()=>t.capacityRangeImgData),n=(l,o)=>{const i={dpx:l,dpy:o},{apiUrl:u,params:p,dimensions:g}=e!=null?e:{};t.getCapacityRangeImgData({apiUrl:u,addParams:p,dimensions:g,coords:i})};return Ee(()=>{n()}),me(()=>e.params,()=>{n()}),(l,o)=>{var i;return d(),f("div",Gu,[Q(Sa,{"data-qa":"cmp-curves-img",class:"cmp-curves-img",labels:{heading:(i=l.labels.capacityCurveHeader)!=null?i:"Capacity Curve Image"},"capacity-curve":s.value.data,"is-loading":s.value.loading,"on-set-point":n,"is-curve-editable":l.isCurveEditable,"clear-classes":""},null,8,["labels","capacity-curve","is-loading","is-curve-editable"])])}}}),Hu={class:"cmp-selection-tool-link b-theme--subtle b-layout-grid","data-qa":"cmp-selection-tool-link"},Vu={class:"cmp-selection-tool-link__info"},Bu={class:"cmp-selection-tool-link__name","data-qa":"link-name"},Yu={key:0,"data-qa":"link-description"},Wu=["href"],fa=G({__name:"CmpSizingSelectionToolLink",props:{labels:{},linkUrl:{}},setup(a){return(e,t)=>(d(),f("div",Hu,[t[0]||(t[0]=c("i",{class:"mod-sizing__hwr-link-icon","data-qa":"icon"},null,-1)),c("div",Vu,[c("div",Bu,q(e.labels.linkName),1),e.labels.linkDescription?(d(),f("div",Yu,q(e.labels.linkDescription),1)):F("",!0)]),c("a",{class:"elm-button elm-button--small elm-button--ghost elm-button--icon-arrow-right_outline cmp-selection-tool-link__link","data-qa":"link",href:e.linkUrl},[c("span",null,q(e.labels.linkDescription),1)],8,Wu)]))}}),ju={class:"b-layout-grid"},Ju={class:"b-layout-grid__group"},Zu={key:1,class:"cmp-text cmp-sizing-params__text"},Xu={key:3,class:"cmp-sizing-params__hit-counter"},Ku={class:"elm-tag"},xu=.5,Mt=133,ft="b-layout-grid__item b-layout-grid__item--12",Qa=G({__name:"CmpSizingParams",setup(a){const e=Ge(),t=vt("stickyStart"),s=vt("stickyPart"),n=Je(t),l=Je(s),{appUrls:o,config:i,paramsContent:u,labels:p,qcIsLoading:g,isMobile:_,noOfHits:m,sizingMapQuestions:b,capacityRangeLink:h,capacityRangeApiUrl:I,mapLabels:y,qcId:$,isCurveClickable:M,capacityRangeParams:H,showCapacityRangeCurve:R,showHwrLink:L}=Ve(e),D=r(()=>{if(_.value)return!1;const U=l.bottom.value+xu>=n.bottom.value,P=n.bottom.value-l.height.value<=Mt;return U&&P}),C=r(()=>_.value||D.value?!1:n.top.value<=Mt),k=r(()=>C.value?`top: ${Mt}px`:"");return(U,P)=>{var O,z,V,J,w;return d(),f("div",ju,[c("div",Ju,[c("div",{ref_key:"stickyStart",ref:t,style:{},class:X([ft,"cmp-sizing-params__fields","b-layout-grid__item--large-6"])},[Q(Fa,{label:v(p).questionCatalogue.selectParameters,link:(O=v(u))==null?void 0:O.explanationlink,tooltip:(z=v(u))==null?void 0:z.explanationtooltip},null,8,["label","link","tooltip"]),(V=v(y))!=null&&V.length?(d(),B(hs,{key:0,config:v(i),"qc-update":v(e).updateSizing,"params-questions":v(b)},null,8,["config","qc-update","params-questions"])):F("",!0),Q(At,{groups:[v(u)],"hidden-question-labels":v(y),"input-modifier":"inline",labels:v(p).questionCatalogue,"is-loading":v(g),"qc-api-url":(J=v(i))==null?void 0:J.qcApiUrl,"qc-id":v($),"set-qc-content":v(e).setQcContent,"update-handler":v(e).updateSizing,"map-config-urls":v(e).mapConfigUrls,"show-group-descriptions":!1,"handle-field-validation":v(e).handleFieldValidation,"show-missing-inputs":!0},null,8,["groups","hidden-question-labels","labels","is-loading","qc-api-url","qc-id","set-qc-content","update-handler","map-config-urls","handle-field-validation"]),v(h)?(d(),f("div",Zu,[c("p",null,q(v(p).dutyCurveText),1)])):F("",!0),Ye(U.$slots,"fields")],2),c("div",{class:X([ft,"b-layout-grid__item--large-6","cmp-sizing-params__container",D.value&&"cmp-sizing-params__container--pinned"])},[c("div",{ref_key:"stickyPart",ref:s,class:X(["cmp-sizing-params__curve-container",ft,C.value&&"cmp-sizing-params__curve-container--sticky",D.value&&"cmp-sizing-params__curve-container--pinned"]),style:Pt(k.value)},[c("div",{class:X(["cmp-sizing-params__curve-container-inner",(C.value||D.value)&&ft])},[v(L)&&!v(_)?(d(),B(fa,{key:0,labels:v(p).hotWaterRecirculation,"link-url":v(o).hotWaterRecirculationLink},null,8,["labels","link-url"])):F("",!0),v(R)?(d(),B(Qu,{key:1,labels:v(p),"is-curve-editable":v(M),"api-url":v(I),params:v(H)},null,8,["labels","is-curve-editable","api-url","params"])):F("",!0),Ye(U.$slots,"figure"),v(L)&&v(_)?(d(),B(fa,{key:2,labels:v(p).hotWaterRecirculation,"link-url":v(o).hotWaterRecirculationLink},null,8,["labels","link-url"])):F("",!0),(w=v(m))!=null&&w.length?(d(),f("div",Xu,[c("p",Ku,q(v(m)),1)])):F("",!0)],2)],6)],2)])])}}}),ed={class:"cmp-sizing-actions cmp-sizing-actions--underline"},td={class:"cmp-title cmp-title--3"},ad={class:"cmp-sizing-actions__group"},sd=["disabled"],nd=["disabled"],od=G({__name:"CmpSizingParamsPumpSizing",setup(a){const e=Ge(),{isEditMode:t,paramsContent:s,isMobile:n,labels:l,moreContent:o,mapLabels:i,qcIsLoading:u,config:p,qcId:g,mapConfigUrls:_,moreIsVisible:m}=Ve(e),b=j(!1),h=j(!1),I=()=>{h.value=!h.value},y=($=!1)=>{b.value=$};return($,M)=>{var H;return d(),f(te,null,[Q(aa,{"show-actions":"","show-params":v(s)||v(t)},{params:be(()=>[Q(Qa)]),_:1},8,["show-params"]),Oe(c("div",null,[c("div",ed,[c("h3",td,q(v(l).more.heading),1),c("div",ad,[c("button",{disabled:!v(n)&&h.value,class:"elm-link elm-link--block",type:"button",onClick:M[0]||(M[0]=R=>y(!0))},q(v(l).more.expandAll),9,sd),c("button",{disabled:!v(n)&&h.value,class:"elm-link elm-link--block",type:"button",onClick:M[1]||(M[1]=R=>y(!1))},q(v(l).more.collapseAll),9,nd),v(n)?F("",!0):(d(),f("button",{key:0,class:"elm-link elm-link--block",type:"button",onClick:I},q(v(l).more[h.value?"disableFullWidth":"enableFullWidth"]),1))])]),c("div",{ref:"more",class:X(["cmp-sizing-more",!v(n)&&h.value&&"cmp-sizing-more--full-width"])},[Q(At,{groups:v(o),"hidden-question-labels":v(i),"input-modifier":"inline","expand-all":b.value,"collapse-all":!b.value,labels:v(l).questionCatalogue,"is-loading":v(u),"qc-api-url":(H=v(p))==null?void 0:H.qcApiUrl,"qc-id":v(g),"set-qc-content":v(e).setQcContent,"update-handler":v(e).updateSizing,"map-config-urls":v(_),"show-group-descriptions":!0,"use-accordion":!h.value,"handle-field-validation":v(e).handleFieldValidation,"show-group-headings":!0,"show-missing-inputs":!0},null,8,["groups","hidden-question-labels","expand-all","collapse-all","labels","is-loading","qc-api-url","qc-id","set-qc-content","update-handler","map-config-urls","use-accordion","handle-field-validation"])],2)],512),[[nt,v(m)]])],64)}}}),ld={class:"cmp-title cmp-title--3 cmp-one-page-sizing-title"},id=G({__name:"CmpSizingOnePageSizing",setup(a){const e=Ge(),t=za(),{isEditMode:s,paramsContent:n,labels:l,moreContent:o,mapLabels:i,qcIsLoading:u,config:p,qcId:g,mapConfigUrls:_,moreIsVisible:m}=Ve(e);return(b,h)=>(d(),B(aa,{"show-params":v(n)||v(s)},{params:be(()=>[Q(Qa,null,{fields:be(()=>{var I;return[Oe(c("div",null,[c("h3",ld,q(v(l).more.heading),1),Q(At,{groups:v(o),"hidden-question-labels":v(i),"input-modifier":"inline",labels:v(l).questionCatalogue,"is-loading":v(u),"qc-api-url":(I=v(p))==null?void 0:I.qcApiUrl,"qc-id":v(g),"set-qc-content":v(e).setQcContent,"update-handler":v(e).updateSizing,"map-config-urls":v(_),"expand-all":!0,"is-active":!0,"show-group-descriptions":!1,"use-accordion":!1,"handle-field-validation":v(e).handleFieldValidation,"show-missing-inputs":!1},null,8,["groups","hidden-question-labels","labels","is-loading","qc-api-url","qc-id","set-qc-content","update-handler","map-config-urls","handle-field-validation"])],512),[[nt,v(m)]])]}),figure:be(()=>[v(t).hasOnePageSizingResults?(d(),B(Na,{key:0,class:"cmp-curve-canvas-sizing-tabs",labels:v(l).curves,"no-sticky":""},null,8,["labels"])):F("",!0)]),_:1})]),_:1},8,["show-params"]))}}),cd={"data-qa":"cmp-sizing-basic-header",class:"cmp-sizing-basic__header"},rd={class:"cmp-title cmp-title--1"},ud={class:"cmp-sizing-basic__fieldset"},dd={class:"cmp-sizing-basic__legend"},pd={"data-qa":"cmp-sizing-basic-field-group",class:"cmp-sizing-basic__field-group cmp-sizing-basic__field-group--criteria"},md=["disabled"],vd={class:"elm-square-button__text"},gd={"data-qa":"cmp-sizing-basic-journey-selector",class:"cmp-sizing-actions__group cmp-sizing-basic__journey-selector"},_d={key:0,class:"cmp-sizing-actions__group"},bd={class:"cmp-sizing-actions__group"},hd=["disabled"],fd={class:"elm-button__text"},Cd=G({__name:"CmpSizingBasic",props:{labels:{},criteria:{},uxJourney:{},isFirstLoad:{type:Boolean},isLoading:{type:Boolean},buttonClass:{},hideButton:{type:Boolean},enabledAdvancedOption:{type:Boolean},onSelectAdvancedSizing:{type:Function},buttonHandler:{type:Function},updateHandler:{type:Function}},setup(a){const e=a,t=r(()=>{var o,i;return`${Math.floor(100/((i=(o=e.criteria)==null?void 0:o.length)!=null?i:0))}w`}),s=r(()=>{var o;return(o=e.criteria)!=null&&o.length?e.criteria.some(i=>{if(!fs(i))return!0;const{readonly:u,selectedoptionkey:p,value:g}=i;return!u&&!p&&!g}):!0}),n=r(()=>{var i;if(!e.uxJourney)return null;const o=(i=e.uxJourney.options)==null?void 0:i.filter(u=>u.key!=="Advanced");return fe(ne({},e.uxJourney),{options:o!=null?o:[]})}),l=r(()=>!e.hideButton&&!s.value);return(o,i)=>{var u;return d(),f("div",null,[c("div",cd,[c("h1",rd,q(o.labels.hero.sizeProduct),1)]),c("div",ud,[c("legend",dd,q(o.labels.legend),1),(u=o.criteria)!=null&&u.length?(d(),f("div",{key:0,class:X(["cmp-sizing-basic__fields",o.hideButton&&"cmp-sizing-basic__fields--no-button"])},[c("div",pd,[(d(!0),f(te,null,le(o.criteria,p=>(d(),B(Ke,Te({ref_for:!0},p,{key:p==null?void 0:p.label,disabled:o.isLoading,class:["cmp-sizing-basic__field",`cmp-sizing-basic__field--${t.value}`],"update-handler":o.updateHandler,"input-modifier":"underline"}),null,16,["disabled","class","update-handler"]))),128))]),l.value?(d(),f("button",{key:0,"data-qa":"cmp-sizing-basic-start-button",disabled:s.value,class:X(["elm-button","elm-button--small","cmp-sizing-basic__button",o.buttonClass,o.isLoading&&v(qe).LOADING]),type:"button",onClick:i[0]||(i[0]=(...p)=>o.buttonHandler&&o.buttonHandler(...p))},[c("span",vd,q(o.labels.actions.start),1)],10,md)):F("",!0)],2)):F("",!0),c("div",{class:X(["cmp-sizing-actions",o.enabledAdvancedOption&&"cmp-sizing-actions--align-right"])},[Oe(c("div",gd,[n.value?(d(),B(Ke,Te({key:0},n.value,{disabled:o.isLoading,type:6,"update-handler":o.updateHandler,"input-modifier":"inline"}),null,16,["disabled","update-handler"])):F("",!0)],512),[[nt,o.criteria]]),o.enabledAdvancedOption?(d(),f("div",_d,[c("button",{type:"button",class:"elm-link elm-link--block mod-sizing__advanced-sizing",onClick:i[1]||(i[1]=(...p)=>o.onSelectAdvancedSizing&&o.onSelectAdvancedSizing(...p))},q(o.labels.hero.openAdvancedSizing),1)])):F("",!0),c("div",bd,[o.criteria&&l.value?(d(),f("button",{key:0,"data-qa":"cmp-sizing-basic-mobile-button",type:"button",class:X(["elm-button","cmp-sizing-basic__mobile-button",o.isLoading&&v(qe).LOADING]),disabled:s.value,onClick:i[2]||(i[2]=(...p)=>o.buttonHandler&&o.buttonHandler(...p))},[c("span",fd,q(o.labels.actions.start),1)],10,hd)):F("",!0)])],2)]),Oe(c("div",{"data-qa":"cmp-sizing-basic-loader",class:"cmp-sizing-basic__loader"},q(o.labels.loading),513),[[nt,o.isFirstLoad]])])}}}),yd={id:"sizing-hero",class:"mod-sizing__hero b-theme b-theme--dark-blue cmp-standard-hero"},qd={class:"cmp-standard-hero__inner"},$d=G({__name:"CmpSizingHero",setup(a){const e=Ge();return(t,s)=>(d(),f("div",yd,[c("div",qd,[Q(Cd,{labels:v(e).labels,criteria:v(e).criteria,"is-first-load":v(e).isFirstLoad,"ux-journey":v(e).journey,"is-loading":v(e).qcIsLoading,"hide-button":!0,"enabled-advanced-option":v(e).enabledAdvancedOption,"on-select-advanced-sizing":v(e).onSelectAdvancedSizing,"update-handler":v(e).updateSizingImmediate,"button-class":"elm-square-button--icon-scroll-down",class:"cmp-sizing-basic keep"},null,8,["labels","criteria","is-first-load","ux-journey","is-loading","enabled-advanced-option","on-select-advanced-sizing","update-handler"])])]))}}),Id={class:"cmp-standard-hero__inner"},Pd={class:"cmp-standard-hero__content"},kd={class:"cmp-text"},Sd={class:"cmp-title cmp-title--2 cmp-text text-center"},Ad=["href"],Ed={class:"elm-button__text"},Rd=G({__name:"CmpSizingExpired",props:{labels:{},productPageUrl:{}},setup(a){return(e,t)=>(d(),f("div",Id,[c("div",Pd,[c("div",kd,[c("h2",Sd,q(e.labels.pageExpiredText),1)]),c("div",null,[c("a",{href:e.productPageUrl,class:"elm-button"},[c("span",Ed,q(e.labels.pageExpiredButtonText),1)],8,Ad)])])]))}}),Ud={key:0,class:"mod-sizing__details"},Ld={key:1,"aria-live":"polite"},Td=G({__name:"ModSizing",props:{appUrls:{},config:{},labels:{}},setup(a){const e=a,t=Ge();return Et(()=>{t.initStore(e.appUrls,e.config,e.labels)}),(s,n)=>v(t).hasQcError?(d(),f("section",Ld,[Q(Rd,{labels:s.labels.expired,"product-page-url":s.config.productPageUrl},null,8,["labels","product-page-url"])])):(d(),f("section",{key:0,class:X(`mod-sizing ${v(t).moreIsVisible&&"mod-sizing--underline"}`),"aria-live":"polite"},[Q($d),v(t).isFetched&&v(t).paramsContent?(d(),f("section",Ud,[v(t).isQuickSizing?(d(),B(Fu,{key:0})):v(t).isOnePageSizing?(d(),B(id,{key:1})):(d(),B(od,{key:2}))])):F("",!0)],2))}}),wd={CmpLiquidGuideBreadCrumb:Vs,CmpLiquidGuideConfiguration:Ks,CmpLiquidGuideInfo:Lt,CmpLiquidGuidePumpDesigns:vn,CmpLiquidGuidePumpFamilies:Fn,CmpLiquidGuideResults:go,CmpLiquidGuideSelectLiquid:Co,CmpPrintCompareProductOptions:La,ModCompareProducts:kl,ModConfigTool:rc,ModCurveCanvasOverview:$c,ModCurveCanvasCompare:au,ModSizing:Td},Dd="data-gpc-vue3-component-root",Od={rootComponents:wd,rootNode:Dd};Cs(Od)});export default Nd();
