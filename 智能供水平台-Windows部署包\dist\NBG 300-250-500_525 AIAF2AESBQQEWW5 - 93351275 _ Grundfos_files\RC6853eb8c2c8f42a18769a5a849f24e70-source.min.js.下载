// For license information, see `https://assets.adobedtm.com/5e107551ef5d/89d0c5ed902a/4021f82e766f/RC6853eb8c2c8f42a18769a5a849f24e70-source.js`.
_satellite.__registerScript('https://assets.adobedtm.com/5e107551ef5d/89d0c5ed902a/4021f82e766f/RC6853eb8c2c8f42a18769a5a849f24e70-source.min.js', "try{if(void 0!==dataLayer.product&&void 0!==dataLayer.product.variant&&void 0!==dataLayer.product.product&&void 0!==dataLayer.product.range&&\"undefined\"!=dataLayer.companyCode){var productStock,imgUrl=document.querySelector(\".elm-img__asset\").src,descriptionInnerText=document.querySelector(\".cmp-catalogue-hero__description\").innerText,productStockElm=document.querySelector(\".elm-availability-tag--available\");productStockElm&&(productStock=10),adobe.target.getOffer({mbox:\"data-collection-mbox\",params:{\"entity.id\":dataLayer.companyCode+\"_\"+dataLayer.product.variant+\"_oob\",\"entity.name\":dataLayer.product.product,\"entity.variant\":dataLayer.product.variant,\"entity.productRange\":dataLayer.product.range,\"entity.pageUrl\":window.location.href,\"entity.thumbnailUrl\":imgUrl,\"entity.inventory\":productStock,\"entity.message\":descriptionInnerText,\"profile.salesCompanyCode\":dataLayer.companyCode},success:function(t){adobe.target.applyOffer({mbox:\"data-collection-mbox\",offer:t})},error:function(t,a){console.log(\"Error \",t,a)}})}}catch(t){}");