<template>
  <div class="role-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><Lock /></el-icon>
          <span>角色权限管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="showAddRole">
              <el-icon><Plus /></el-icon>
              添加角色
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 角色列表 -->
      <el-table :data="settingsStore.roles" stripe>
        <el-table-column prop="name" label="角色名称" width="150" />
        
        <el-table-column prop="description" label="描述" min-width="200" />
        
        <el-table-column prop="permissions" label="权限数量" width="100">
          <template #default="{ row }">
            <el-tag type="info">{{ row.permissions.length }}个权限</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="isSystem" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isSystem ? 'warning' : 'success'">
              {{ row.isSystem ? '系统角色' : '自定义' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="updatedAt" label="更新时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.updatedAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="viewPermissions(row)">
              查看权限
            </el-button>
            <el-button size="small" @click="editRole(row)" :disabled="row.isSystem">
              编辑
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteRole(row.id)"
              :disabled="row.isSystem"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 权限详情对话框 -->
    <el-dialog v-model="showPermissionsDialog" title="角色权限详情" width="800px">
      <div v-if="selectedRole" class="permissions-detail">
        <h3>{{ selectedRole.name }} - 权限列表</h3>
        <div class="permissions-grid">
          <div v-for="category in permissionCategories" :key="category" class="permission-category">
            <h4>{{ category }}</h4>
            <div class="permission-items">
              <el-tag 
                v-for="permission in getCategoryPermissions(category)" 
                :key="permission.id"
                :type="hasPermission(permission.id) ? 'success' : 'info'"
                class="permission-tag"
              >
                {{ permission.name }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Lock, Plus } from '@element-plus/icons-vue'
import { useSettingsStore } from '@/stores/settings'
import type { Role } from '@/types'
import dayjs from 'dayjs'
import { ElMessage, ElMessageBox } from 'element-plus'

const settingsStore = useSettingsStore()

// 响应式数据
const showPermissionsDialog = ref(false)
const selectedRole = ref<Role | null>(null)

// 计算属性
const permissionCategories = computed(() => {
  const categories = new Set(settingsStore.permissions.map(p => p.category))
  return Array.from(categories)
})

// 方法
const formatTime = (timestamp: string) => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm')
}

const showAddRole = () => {
  settingsStore.showRoleEditor()
}

const editRole = (role: Role) => {
  settingsStore.showRoleEditor(role)
}

const viewPermissions = (role: Role) => {
  selectedRole.value = role
  showPermissionsDialog.value = true
}

const getCategoryPermissions = (category: string) => {
  return settingsStore.permissions.filter(p => p.category === category)
}

const hasPermission = (permissionId: string) => {
  return selectedRole.value?.permissions.includes(permissionId) || false
}

const deleteRole = async (roleId: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个角色吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const success = await settingsStore.deleteRole(roleId)
    if (success) {
      ElMessage.success('角色删除成功')
    } else {
      ElMessage.error('无法删除系统角色')
    }
  } catch {
    // 用户取消
  }
}
</script>

<style lang="scss" scoped>
.role-management {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    > div:first-child {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
    }
  }
}

.permissions-detail {
  h3 {
    margin-bottom: 20px;
    color: var(--el-text-color-primary);
  }
  
  .permissions-grid {
    .permission-category {
      margin-bottom: 20px;
      
      h4 {
        margin-bottom: 10px;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }
      
      .permission-items {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        
        .permission-tag {
          margin: 0;
        }
      }
    }
  }
}
</style>
