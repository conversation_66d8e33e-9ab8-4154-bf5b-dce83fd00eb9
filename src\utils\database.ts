/**
 * IndexedDB 数据库管理器
 * 提供完整的数据持久化存储方案
 */

export interface DatabaseConfig {
  name: string
  version: number
  stores: StoreConfig[]
}

export interface StoreConfig {
  name: string
  keyPath: string
  autoIncrement?: boolean
  indexes?: IndexConfig[]
}

export interface IndexConfig {
  name: string
  keyPath: string | string[]
  unique?: boolean
}

export class DatabaseManager {
  private db: IDBDatabase | null = null
  private config: DatabaseConfig

  constructor(config: DatabaseConfig) {
    this.config = config
  }

  /**
   * 初始化数据库
   */
  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.config.name, this.config.version)

      request.onerror = () => {
        reject(new Error(`Failed to open database: ${request.error}`))
      }

      request.onsuccess = () => {
        this.db = request.result
        console.log(`Database ${this.config.name} opened successfully`)
        resolve()
      }

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        this.createStores(db)
      }
    })
  }

  /**
   * 创建数据表
   */
  private createStores(db: IDBDatabase): void {
    this.config.stores.forEach(storeConfig => {
      // 删除已存在的表
      if (db.objectStoreNames.contains(storeConfig.name)) {
        db.deleteObjectStore(storeConfig.name)
      }

      // 创建新表
      const store = db.createObjectStore(storeConfig.name, {
        keyPath: storeConfig.keyPath,
        autoIncrement: storeConfig.autoIncrement || false
      })

      // 创建索引
      if (storeConfig.indexes) {
        storeConfig.indexes.forEach(indexConfig => {
          store.createIndex(indexConfig.name, indexConfig.keyPath, {
            unique: indexConfig.unique || false
          })
        })
      }

      console.log(`Created store: ${storeConfig.name}`)
    })
  }

  /**
   * 获取数据库实例
   */
  getDatabase(): IDBDatabase {
    if (!this.db) {
      throw new Error('Database not initialized. Call init() first.')
    }
    return this.db
  }

  /**
   * 关闭数据库
   */
  close(): void {
    if (this.db) {
      this.db.close()
      this.db = null
    }
  }
}

/**
 * 数据访问对象基类
 */
export class BaseDAO<T> {
  protected db: DatabaseManager
  protected storeName: string

  constructor(db: DatabaseManager, storeName: string) {
    this.db = db
    this.storeName = storeName
  }

  /**
   * 添加数据
   */
  async add(data: T): Promise<string | number> {
    return new Promise((resolve, reject) => {
      const transaction = this.db.getDatabase().transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      const request = store.add(data)

      request.onsuccess = () => resolve(request.result as string | number)
      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 更新数据
   */
  async update(data: T): Promise<void> {
    return new Promise((resolve, reject) => {
      const transaction = this.db.getDatabase().transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      const request = store.put(data)

      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 删除数据
   */
  async delete(key: string | number): Promise<void> {
    return new Promise((resolve, reject) => {
      const transaction = this.db.getDatabase().transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      const request = store.delete(key)

      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 根据主键获取数据
   */
  async getById(key: string | number): Promise<T | undefined> {
    return new Promise((resolve, reject) => {
      const transaction = this.db.getDatabase().transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const request = store.get(key)

      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 获取所有数据
   */
  async getAll(): Promise<T[]> {
    return new Promise((resolve, reject) => {
      const transaction = this.db.getDatabase().transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const request = store.getAll()

      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 根据索引查询数据
   */
  async getByIndex(indexName: string, value: any): Promise<T[]> {
    return new Promise((resolve, reject) => {
      const transaction = this.db.getDatabase().transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const index = store.index(indexName)
      const request = index.getAll(value)

      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 分页查询
   */
  async getPage(offset: number, limit: number): Promise<T[]> {
    return new Promise((resolve, reject) => {
      const transaction = this.db.getDatabase().transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const request = store.openCursor()
      const results: T[] = []
      let currentIndex = 0

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result
        if (cursor) {
          if (currentIndex >= offset && results.length < limit) {
            results.push(cursor.value)
          }
          currentIndex++
          if (results.length < limit) {
            cursor.continue()
          } else {
            resolve(results)
          }
        } else {
          resolve(results)
        }
      }

      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 计数
   */
  async count(): Promise<number> {
    return new Promise((resolve, reject) => {
      const transaction = this.db.getDatabase().transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const request = store.count()

      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 清空表
   */
  async clear(): Promise<void> {
    return new Promise((resolve, reject) => {
      const transaction = this.db.getDatabase().transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      const request = store.clear()

      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 批量操作
   */
  async batchOperation(operations: Array<{
    type: 'add' | 'update' | 'delete'
    data?: T
    key?: string | number
  }>): Promise<void> {
    return new Promise((resolve, reject) => {
      const transaction = this.db.getDatabase().transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      let completed = 0

      const checkComplete = () => {
        completed++
        if (completed === operations.length) {
          resolve()
        }
      }

      operations.forEach(operation => {
        let request: IDBRequest

        switch (operation.type) {
          case 'add':
            request = store.add(operation.data!)
            break
          case 'update':
            request = store.put(operation.data!)
            break
          case 'delete':
            request = store.delete(operation.key!)
            break
          default:
            throw new Error(`Unknown operation type: ${operation.type}`)
        }

        request.onsuccess = checkComplete
        request.onerror = () => reject(request.error)
      })
    })
  }
}
