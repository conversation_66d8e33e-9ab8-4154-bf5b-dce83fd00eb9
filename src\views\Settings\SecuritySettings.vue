<template>
  <div class="security-settings">
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><Key /></el-icon>
          <span>安全设置</span>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="setting-section">
            <template #header>
              <span>密码策略</span>
            </template>
            
            <el-form :model="passwordForm" label-width="140px">
              <el-form-item label="最小长度">
                <el-input-number v-model="passwordForm.minLength" :min="6" :max="32" />
              </el-form-item>
              
              <el-form-item label="必须包含大写字母">
                <el-switch v-model="passwordForm.requireUppercase" />
              </el-form-item>
              
              <el-form-item label="必须包含小写字母">
                <el-switch v-model="passwordForm.requireLowercase" />
              </el-form-item>
              
              <el-form-item label="必须包含数字">
                <el-switch v-model="passwordForm.requireNumbers" />
              </el-form-item>
              
              <el-form-item label="必须包含特殊字符">
                <el-switch v-model="passwordForm.requireSpecialChars" />
              </el-form-item>
              
              <el-form-item label="密码有效期(天)">
                <el-input-number v-model="passwordForm.passwordExpiry" :min="0" :max="365" />
                <div class="form-tip">0表示永不过期</div>
              </el-form-item>
              
              <el-form-item label="禁止重复使用">
                <el-input-number v-model="passwordForm.preventReuse" :min="0" :max="10" />
                <div class="form-tip">最近几次使用过的密码</div>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card class="setting-section">
            <template #header>
              <span>会话安全</span>
            </template>
            
            <el-form :model="sessionForm" label-width="140px">
              <el-form-item label="会话超时(分钟)">
                <el-input-number v-model="sessionForm.sessionTimeout" :min="30" :max="1440" />
                <div class="form-tip">用户无操作自动退出时间</div>
              </el-form-item>
              
              <el-form-item label="最大登录尝试">
                <el-input-number v-model="sessionForm.maxLoginAttempts" :min="3" :max="10" />
                <div class="form-tip">连续登录失败次数限制</div>
              </el-form-item>
              
              <el-form-item label="锁定时长(分钟)">
                <el-input-number v-model="sessionForm.lockoutDuration" :min="5" :max="120" />
                <div class="form-tip">账户锁定后的等待时间</div>
              </el-form-item>
              
              <el-form-item label="双因子认证">
                <el-switch v-model="sessionForm.twoFactorAuth" />
                <div class="form-tip">启用短信或邮箱验证码</div>
              </el-form-item>
              
              <el-form-item label="审计日志">
                <el-switch v-model="sessionForm.auditLog" />
                <div class="form-tip">记录用户操作日志</div>
              </el-form-item>
              
              <el-form-item label="数据加密">
                <el-switch v-model="sessionForm.encryptionEnabled" />
                <div class="form-tip">启用敏感数据加密存储</div>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
      
      <el-card class="setting-section">
        <template #header>
          <span>IP白名单</span>
        </template>
        
        <div class="ip-whitelist">
          <div class="ip-list">
            <el-tag 
              v-for="(ip, index) in ipWhitelist" 
              :key="index"
              closable
              @close="removeIP(index)"
              class="ip-tag"
            >
              {{ ip }}
            </el-tag>
          </div>
          
          <div class="add-ip">
            <el-input 
              v-model="newIP" 
              placeholder="输入IP地址或网段，如：*********** 或 ***********/24"
              @keyup.enter="addIP"
            />
            <el-button @click="addIP" :disabled="!newIP">添加</el-button>
          </div>
          
          <div class="form-tip">
            只有白名单中的IP地址才能访问系统。支持单个IP和CIDR网段格式。
          </div>
        </div>
      </el-card>
      
      <el-card class="setting-section">
        <template #header>
          <span>安全状态</span>
        </template>
        
        <div class="security-status">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="status-item">
                <div class="status-icon success">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="status-info">
                  <div class="status-title">SSL证书</div>
                  <div class="status-desc">有效期至 2024-12-31</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="8">
              <div class="status-item">
                <div class="status-icon warning">
                  <el-icon><Warning /></el-icon>
                </div>
                <div class="status-info">
                  <div class="status-title">防火墙</div>
                  <div class="status-desc">部分端口未关闭</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="8">
              <div class="status-item">
                <div class="status-icon success">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="status-info">
                  <div class="status-title">入侵检测</div>
                  <div class="status-desc">运行正常</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
      
      <div class="form-actions">
        <el-button @click="resetSettings">重置</el-button>
        <el-button @click="runSecurityScan" :loading="scanning">安全扫描</el-button>
        <el-button type="primary" @click="saveSettings" :loading="settingsStore.loading">
          保存设置
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Key, CircleCheck, Warning } from '@element-plus/icons-vue'
import { useSettingsStore } from '@/stores/settings'
import { ElMessage } from 'element-plus'

const settingsStore = useSettingsStore()
const scanning = ref(false)
const newIP = ref('')

// 表单数据
const passwordForm = reactive({ ...settingsStore.settings.security.passwordPolicy })
const sessionForm = reactive({
  sessionTimeout: settingsStore.settings.security.sessionTimeout,
  maxLoginAttempts: settingsStore.settings.security.maxLoginAttempts,
  lockoutDuration: settingsStore.settings.security.lockoutDuration,
  twoFactorAuth: settingsStore.settings.security.twoFactorAuth,
  auditLog: settingsStore.settings.security.auditLog,
  encryptionEnabled: settingsStore.settings.security.encryptionEnabled
})

const ipWhitelist = ref([...settingsStore.settings.security.ipWhitelist])

// 方法
const addIP = () => {
  if (!newIP.value) return
  
  // 简单的IP格式验证
  const ipRegex = /^(\d{1,3}\.){3}\d{1,3}(\/\d{1,2})?$/
  if (!ipRegex.test(newIP.value)) {
    ElMessage.error('请输入正确的IP地址格式')
    return
  }
  
  if (ipWhitelist.value.includes(newIP.value)) {
    ElMessage.warning('IP地址已存在')
    return
  }
  
  ipWhitelist.value.push(newIP.value)
  newIP.value = ''
  ElMessage.success('IP地址添加成功')
}

const removeIP = (index: number) => {
  ipWhitelist.value.splice(index, 1)
  ElMessage.success('IP地址删除成功')
}

const runSecurityScan = async () => {
  scanning.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 3000))
    ElMessage.success('安全扫描完成，未发现安全风险')
  } catch {
    ElMessage.error('安全扫描失败')
  } finally {
    scanning.value = false
  }
}

const saveSettings = async () => {
  try {
    await settingsStore.updateSettings('security', {
      passwordPolicy: passwordForm,
      sessionTimeout: sessionForm.sessionTimeout,
      maxLoginAttempts: sessionForm.maxLoginAttempts,
      lockoutDuration: sessionForm.lockoutDuration,
      twoFactorAuth: sessionForm.twoFactorAuth,
      ipWhitelist: ipWhitelist.value,
      auditLog: sessionForm.auditLog,
      encryptionEnabled: sessionForm.encryptionEnabled
    })
    ElMessage.success('安全设置保存成功')
  } catch {
    ElMessage.error('安全设置保存失败')
  }
}

const resetSettings = () => {
  Object.assign(passwordForm, settingsStore.settings.security.passwordPolicy)
  Object.assign(sessionForm, {
    sessionTimeout: settingsStore.settings.security.sessionTimeout,
    maxLoginAttempts: settingsStore.settings.security.maxLoginAttempts,
    lockoutDuration: settingsStore.settings.security.lockoutDuration,
    twoFactorAuth: settingsStore.settings.security.twoFactorAuth,
    auditLog: settingsStore.settings.security.auditLog,
    encryptionEnabled: settingsStore.settings.security.encryptionEnabled
  })
  ipWhitelist.value = [...settingsStore.settings.security.ipWhitelist]
  ElMessage.info('已重置为默认设置')
}
</script>

<style lang="scss" scoped>
.security-settings {
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
  }
  
  .setting-section {
    margin-bottom: 20px;
    
    :deep(.el-card__header) {
      padding: 12px 20px;
      background-color: var(--el-bg-color-page);
      font-weight: 600;
    }
  }
  
  .form-tip {
    font-size: 12px;
    color: var(--el-text-color-regular);
    margin-top: 4px;
  }
  
  .ip-whitelist {
    .ip-list {
      margin-bottom: 16px;
      
      .ip-tag {
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }
    
    .add-ip {
      display: flex;
      gap: 8px;
      margin-bottom: 8px;
    }
  }
  
  .security-status {
    .status-item {
      display: flex;
      align-items: center;
      padding: 16px;
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      
      .status-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        
        &.success {
          background-color: #f0f9ff;
          color: #67C23A;
        }
        
        &.warning {
          background-color: #fdf6ec;
          color: #E6A23C;
        }
        
        &.danger {
          background-color: #fef0f0;
          color: #F56C6C;
        }
      }
      
      .status-info {
        .status-title {
          font-weight: 600;
          margin-bottom: 4px;
        }
        
        .status-desc {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--el-border-color-light);
  }
}
</style>
