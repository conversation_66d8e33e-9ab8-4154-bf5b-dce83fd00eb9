import type { AlgorithmConfig, FittingResult } from '@/types'

/**
 * 简化的神经网络实现
 */
export class SimpleNeuralNetwork {
  private weights: number[][][]
  private biases: number[][]
  private layers: number[]
  private learningRate: number

  constructor(layers: number[], learningRate = 0.01) {
    this.layers = layers
    this.learningRate = learningRate
    this.weights = []
    this.biases = []
    
    // 初始化权重和偏置
    for (let i = 0; i < layers.length - 1; i++) {
      const weightMatrix: number[][] = []
      const biasVector: number[] = []
      
      for (let j = 0; j < layers[i + 1]; j++) {
        const neuronWeights: number[] = []
        for (let k = 0; k < layers[i]; k++) {
          neuronWeights.push(Math.random() * 2 - 1) // [-1, 1]
        }
        weightMatrix.push(neuronWeights)
        biasVector.push(Math.random() * 2 - 1)
      }
      
      this.weights.push(weightMatrix)
      this.biases.push(biasVector)
    }
  }

  private sigmoid(x: number): number {
    return 1 / (1 + Math.exp(-x))
  }

  private sigmoidDerivative(x: number): number {
    return x * (1 - x)
  }

  private forward(input: number[]): number[][] {
    const activations: number[][] = [input]
    let currentActivation: number[] = input

    for (let i = 0; i < this.weights.length; i++) {
      const nextActivation: number[] = []
      
      for (let j = 0; j < this.weights[i].length; j++) {
        let sum = this.biases[i][j]
        for (let k = 0; k < currentActivation.length; k++) {
          sum += currentActivation[k] * this.weights[i][j][k]
        }
        nextActivation.push(this.sigmoid(sum))
      }
      
      currentActivation = nextActivation
      activations.push(currentActivation)
    }

    return activations
  }

  train(inputs: number[][], outputs: number[][], epochs: number): void {
    for (let epoch = 0; epoch < epochs; epoch++) {
      for (let i = 0; i < inputs.length; i++) {
        const activations = this.forward(inputs[i])
        
        // 反向传播
        let errors: number[] = []
        const output = activations[activations.length - 1]
        
        // 计算输出层误差
        for (let j = 0; j < output.length; j++) {
          errors.push((outputs[i][j] - output[j]) * this.sigmoidDerivative(output[j]))
        }

        // 反向传播误差
        for (let layer = this.weights.length - 1; layer >= 0; layer--) {
          const layerErrors: number[] = []
          
          if (layer > 0) {
            for (let j = 0; j < this.weights[layer - 1].length; j++) {
              let error = 0
              for (let k = 0; k < errors.length; k++) {
                error += errors[k] * this.weights[layer][k][j]
              }
              layerErrors.push(error * this.sigmoidDerivative(activations[layer][j]))
            }
          }

          // 更新权重和偏置
          for (let j = 0; j < this.weights[layer].length; j++) {
            for (let k = 0; k < this.weights[layer][j].length; k++) {
              this.weights[layer][j][k] += this.learningRate * errors[j] * activations[layer][k]
            }
            this.biases[layer][j] += this.learningRate * errors[j]
          }

          errors = layerErrors
        }
      }
    }
  }

  predict(input: number[]): number[] {
    const activations = this.forward(input)
    return activations[activations.length - 1]
  }
}

/**
 * 多项式拟合
 */
export function polynomialFitting(x: number[], y: number[], degree: number): FittingResult {
  const startTime = performance.now()
  
  // 构建范德蒙德矩阵
  const n = x.length
  const A: number[][] = []
  
  for (let i = 0; i < n; i++) {
    const row: number[] = []
    for (let j = 0; j <= degree; j++) {
      row.push(Math.pow(x[i], j))
    }
    A.push(row)
  }
  
  // 使用正规方程求解: (A^T * A) * coeffs = A^T * y
  const AT = transpose(A)
  const ATA = matrixMultiply(AT, A)
  const ATy = matrixVectorMultiply(AT, y)
  
  const coefficients = gaussianElimination(ATA, ATy)
  
  // 计算评估指标
  const predictions = x.map(xi => {
    let pred = 0
    for (let j = 0; j <= degree; j++) {
      pred += coefficients[j] * Math.pow(xi, j)
    }
    return pred
  })
  
  const r2Score = calculateR2(y, predictions)
  const mse = calculateMSE(y, predictions)
  const mae = calculateMAE(y, predictions)
  
  const endTime = performance.now()
  
  return {
    algorithm: 'polynomial',
    parameters: coefficients,
    r2Score,
    mse,
    mae,
    trainingTime: endTime - startTime
  }
}

/**
 * 神经网络拟合
 */
export function neuralNetworkFitting(
  x: number[], 
  y: number[], 
  config: AlgorithmConfig
): FittingResult {
  const startTime = performance.now()
  
  // 数据归一化
  const xMin = Math.min(...x)
  const xMax = Math.max(...x)
  const yMin = Math.min(...y)
  const yMax = Math.max(...y)
  
  const normalizedX = x.map(val => (val - xMin) / (xMax - xMin))
  const normalizedY = y.map(val => (val - yMin) / (yMax - yMin))
  
  // 准备训练数据
  const inputs = normalizedX.map(val => [val])
  const outputs = normalizedY.map(val => [val])
  
  // 创建神经网络
  const hiddenLayers = config.hiddenLayers || [10, 5]
  const layers = [1, ...hiddenLayers, 1]
  const nn = new SimpleNeuralNetwork(layers, config.learningRate || 0.01)
  
  // 训练
  nn.train(inputs, outputs, config.epochs || 1000)
  
  // 预测
  const predictions = normalizedX.map(val => {
    const pred = nn.predict([val])[0]
    return pred * (yMax - yMin) + yMin // 反归一化
  })
  
  // 计算评估指标
  const r2Score = calculateR2(y, predictions)
  const mse = calculateMSE(y, predictions)
  const mae = calculateMAE(y, predictions)
  
  const endTime = performance.now()
  
  return {
    algorithm: 'neural-network',
    parameters: [], // 神经网络参数复杂，这里简化
    r2Score,
    mse,
    mae,
    trainingTime: endTime - startTime
  }
}

/**
 * 样条插值拟合
 */
export function splineFitting(x: number[], y: number[]): FittingResult {
  const startTime = performance.now()
  
  // 简化的三次样条插值实现
  const n = x.length
  const h: number[] = []
  const alpha: number[] = []
  
  for (let i = 0; i < n - 1; i++) {
    h[i] = x[i + 1] - x[i]
  }
  
  for (let i = 1; i < n - 1; i++) {
    alpha[i] = (3 / h[i]) * (y[i + 1] - y[i]) - (3 / h[i - 1]) * (y[i] - y[i - 1])
  }
  
  // 求解三对角矩阵系统
  const l = new Array(n).fill(0)
  const mu = new Array(n).fill(0)
  const z = new Array(n).fill(0)
  const c = new Array(n).fill(0)
  const b = new Array(n).fill(0)
  const d = new Array(n).fill(0)
  
  l[0] = 1
  mu[0] = 0
  z[0] = 0
  
  for (let i = 1; i < n - 1; i++) {
    l[i] = 2 * (x[i + 1] - x[i - 1]) - h[i - 1] * mu[i - 1]
    mu[i] = h[i] / l[i]
    z[i] = (alpha[i] - h[i - 1] * z[i - 1]) / l[i]
  }
  
  l[n - 1] = 1
  z[n - 1] = 0
  c[n - 1] = 0
  
  for (let j = n - 2; j >= 0; j--) {
    c[j] = z[j] - mu[j] * c[j + 1]
    b[j] = (y[j + 1] - y[j]) / h[j] - h[j] * (c[j + 1] + 2 * c[j]) / 3
    d[j] = (c[j + 1] - c[j]) / (3 * h[j])
  }
  
  // 计算预测值
  const predictions = x.map((xi, i) => {
    if (i === n - 1) return y[i]
    const dx = xi - x[i]
    return y[i] + b[i] * dx + c[i] * dx * dx + d[i] * dx * dx * dx
  })
  
  const r2Score = calculateR2(y, predictions)
  const mse = calculateMSE(y, predictions)
  const mae = calculateMAE(y, predictions)
  
  const endTime = performance.now()
  
  return {
    algorithm: 'spline',
    parameters: [...b, ...c, ...d], // 样条参数
    r2Score,
    mse,
    mae,
    trainingTime: endTime - startTime
  }
}

// 辅助函数
function transpose(matrix: number[][]): number[][] {
  return matrix[0].map((_, colIndex) => matrix.map(row => row[colIndex]))
}

function matrixMultiply(a: number[][], b: number[][]): number[][] {
  const result: number[][] = []
  for (let i = 0; i < a.length; i++) {
    result[i] = []
    for (let j = 0; j < b[0].length; j++) {
      let sum = 0
      for (let k = 0; k < b.length; k++) {
        sum += a[i][k] * b[k][j]
      }
      result[i][j] = sum
    }
  }
  return result
}

function matrixVectorMultiply(matrix: number[][], vector: number[]): number[] {
  return matrix.map(row => 
    row.reduce((sum, val, i) => sum + val * vector[i], 0)
  )
}

function gaussianElimination(A: number[][], b: number[]): number[] {
  const n = A.length
  const augmented = A.map((row, i) => [...row, b[i]])
  
  // 前向消元
  for (let i = 0; i < n; i++) {
    // 选主元
    let maxRow = i
    for (let k = i + 1; k < n; k++) {
      if (Math.abs(augmented[k][i]) > Math.abs(augmented[maxRow][i])) {
        maxRow = k
      }
    }
    [augmented[i], augmented[maxRow]] = [augmented[maxRow], augmented[i]]
    
    // 消元
    for (let k = i + 1; k < n; k++) {
      const factor = augmented[k][i] / augmented[i][i]
      for (let j = i; j <= n; j++) {
        augmented[k][j] -= factor * augmented[i][j]
      }
    }
  }
  
  // 回代
  const x = new Array(n)
  for (let i = n - 1; i >= 0; i--) {
    x[i] = augmented[i][n]
    for (let j = i + 1; j < n; j++) {
      x[i] -= augmented[i][j] * x[j]
    }
    x[i] /= augmented[i][i]
  }
  
  return x
}

function calculateR2(actual: number[], predicted: number[]): number {
  const actualMean = actual.reduce((sum, val) => sum + val, 0) / actual.length
  const totalSumSquares = actual.reduce((sum, val) => sum + Math.pow(val - actualMean, 2), 0)
  const residualSumSquares = actual.reduce((sum, val, i) => sum + Math.pow(val - predicted[i], 2), 0)
  return 1 - (residualSumSquares / totalSumSquares)
}

function calculateMSE(actual: number[], predicted: number[]): number {
  const sumSquaredErrors = actual.reduce((sum, val, i) => sum + Math.pow(val - predicted[i], 2), 0)
  return sumSquaredErrors / actual.length
}

function calculateMAE(actual: number[], predicted: number[]): number {
  const sumAbsoluteErrors = actual.reduce((sum, val, i) => sum + Math.abs(val - predicted[i]), 0)
  return sumAbsoluteErrors / actual.length
}