<template>
  <div class="grundfos-echarts-version">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">格兰富泵曲线 - ECharts专业版</h1>
      <p class="page-subtitle">基于格兰富源码分析的高保真复制版本</p>
    </div>

    <!-- 曲线设置面板 -->
    <CmpCurveSettings 
      v-model:pumpCount="pumpCount"
      v-model:frequency="frequency"
      v-model:workingFlow="workingFlow"
      v-model:workingHead="workingHead"
      v-model:showCurveTypes="curveSettings"
      @settings-changed="handleSettingsChange"
    />

    <!-- 上方图表：扬程和效率 -->
    <div class="chart-container">
      <div class="chart-header">
        <h3>扬程特性曲线和效率曲线</h3>
        <div class="chart-legend">
          <span class="legend-item head">
            <span class="legend-color" style="background: #1f77b4"></span>
            扬程 H (m)
          </span>
          <span class="legend-item efficiency">
            <span class="legend-color" style="background: #2c2c2c"></span>
            效率 η (%)
          </span>
        </div>
      </div>
      <div ref="topChartRef" class="chart-area"></div>
    </div>

    <!-- 下方图表：功率和NPSH -->
    <div class="chart-container">
      <div class="chart-header">
        <h3>功率特性曲线和NPSH曲线</h3>
        <div class="chart-legend">
          <span class="legend-item power-p1">
            <span class="legend-color" style="background: #d62728"></span>
            P1功率 (kW)
          </span>
          <span class="legend-item power-p2">
            <span class="legend-color" style="background: #b91c1c; border-style: dashed"></span>
            P2功率 (kW)
          </span>
          <span class="legend-item npsh">
            <span class="legend-color" style="background: #2ca02c"></span>
            NPSH (m)
          </span>
        </div>
      </div>
      <div ref="bottomChartRef" class="chart-area"></div>
    </div>

    <!-- 技术参数显示 -->
    <div class="technical-data">
      <h4>当前工作点参数</h4>
      <div class="parameters-grid">
        <div class="parameter-item">
          <label>流量 Q:</label>
          <span>{{ workingFlow.toFixed(1) }} m³/h</span>
        </div>
        <div class="parameter-item">
          <label>扬程 H:</label>
          <span>{{ workingHead.toFixed(1) }} m</span>
        </div>
        <div class="parameter-item">
          <label>效率 η:</label>
          <span>{{ workingEfficiency.toFixed(1) }}%</span>
        </div>
        <div class="parameter-item">
          <label>P1功率:</label>
          <span>{{ workingP1Power.toFixed(2) }} kW</span>
        </div>
        <div class="parameter-item">
          <label>P2功率:</label>
          <span>{{ workingP2Power.toFixed(2) }} kW</span>
        </div>
        <div class="parameter-item">
          <label>NPSH:</label>
          <span>{{ workingNPSH.toFixed(2) }} m</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue'
import * as echarts from 'echarts'
import CmpCurveSettings from './components/CmpCurveSettings.vue'

// 响应式数据
const pumpCount = ref(1)
const frequency = ref(50)
const workingFlow = ref(400)
const workingHead = ref(25)

// 曲线显示设置
const curveSettings = ref({
  showHead: true,
  showEfficiency: true,
  showP1Power: true,
  showP2Power: true,
  showNPSH: true
})

// 图表引用
const topChartRef = ref<HTMLElement>()
const bottomChartRef = ref<HTMLElement>()
let topChart: echarts.ECharts | null = null
let bottomChart: echarts.ECharts | null = null

// 基础性能数据 (NBG 300-250-500/525)
const basePerformanceData = [
  // [流量, 扬程, 效率1, 效率2, P1功率, P2功率, NPSH]
  [0, 50.0, 0, 0, 15.0, 18.5, 0.5],
  [50, 49.8, 25, 30, 16.2, 19.8, 0.8],
  [100, 49.2, 45, 50, 17.8, 21.5, 1.2],
  [150, 48.3, 58, 62, 19.8, 23.8, 1.8],
  [200, 47.0, 68, 71, 22.2, 26.5, 2.5],
  [250, 45.2, 75, 78, 25.0, 29.8, 3.4],
  [300, 43.0, 80, 82, 28.2, 33.5, 4.5],
  [350, 40.2, 83, 84, 31.8, 37.8, 5.8],
  [400, 36.8, 85, 85, 35.8, 42.5, 7.3],
  [450, 32.8, 86, 84, 40.2, 47.8, 9.0],
  [500, 28.2, 85, 82, 45.0, 53.5, 11.0],
  [550, 23.0, 82, 78, 50.2, 59.8, 13.2],
  [600, 17.2, 77, 72, 55.8, 66.5, 15.7],
  [650, 10.8, 70, 64, 61.8, 73.8, 18.5],
  [700, 3.8, 60, 54, 68.2, 81.5, 21.6],
  [750, 0, 45, 40, 75.0, 89.8, 25.0],
  [800, 0, 25, 22, 82.2, 98.5, 28.7],
  [850, 0, 0, 0, 89.8, 107.8, 32.7]
]

// 相似定律计算
const applyAffinityLaws = (baseValue: number, type: 'flow' | 'head' | 'power') => {
  const frequencyRatio = frequency.value / 50
  switch (type) {
    case 'flow':
      return baseValue * frequencyRatio
    case 'head':
      return baseValue * Math.pow(frequencyRatio, 2)
    case 'power':
      return baseValue * Math.pow(frequencyRatio, 3)
    default:
      return baseValue
  }
}

// 插值计算
const interpolate = (targetFlow: number, dataIndex: number): number => {
  const singlePumpFlow = targetFlow / pumpCount.value
  const baseFlow = singlePumpFlow / applyAffinityLaws(1, 'flow')
  
  if (baseFlow <= 0) return basePerformanceData[0][dataIndex]
  if (baseFlow >= 850) return basePerformanceData[basePerformanceData.length - 1][dataIndex]
  
  for (let i = 0; i < basePerformanceData.length - 1; i++) {
    const [flow1] = basePerformanceData[i]
    const [flow2] = basePerformanceData[i + 1]
    
    if (baseFlow >= flow1 && baseFlow <= flow2) {
      const value1 = basePerformanceData[i][dataIndex]
      const value2 = basePerformanceData[i + 1][dataIndex]
      const ratio = (baseFlow - flow1) / (flow2 - flow1)
      return value1 + (value2 - value1) * ratio
    }
  }
  
  return 0
}

// 计算当前工作点参数
const workingEfficiency = computed(() => {
  const baseEff1 = interpolate(workingFlow.value, 2)
  return applyAffinityLaws(baseEff1, 'head') // 效率受频率影响较小
})

const workingP1Power = computed(() => {
  const baseP1 = interpolate(workingFlow.value, 4)
  return applyAffinityLaws(baseP1, 'power') * pumpCount.value
})

const workingP2Power = computed(() => {
  const baseP2 = interpolate(workingFlow.value, 5)
  return applyAffinityLaws(baseP2, 'power') * pumpCount.value
})

const workingNPSH = computed(() => {
  const baseNPSH = interpolate(workingFlow.value, 6)
  return applyAffinityLaws(baseNPSH, 'head')
})

// 生成曲线数据
const generateCurveData = () => {
  const data = {
    flow: [] as number[],
    head: [] as number[],
    efficiency1: [] as number[],
    efficiency2: [] as number[],
    p1Power: [] as number[],
    p2Power: [] as number[],
    npsh: [] as number[]
  }
  
  for (let i = 0; i < basePerformanceData.length; i++) {
    const [baseFlow, baseHead, baseEff1, baseEff2, baseP1, baseP2, baseNPSH] = basePerformanceData[i]
    
    const actualFlow = applyAffinityLaws(baseFlow, 'flow') * pumpCount.value
    const actualHead = applyAffinityLaws(baseHead, 'head')
    const actualEff1 = Math.min(baseEff1, 100) // 限制效率最大值
    const actualEff2 = Math.min(baseEff2, 100)
    const actualP1 = applyAffinityLaws(baseP1, 'power') * pumpCount.value
    const actualP2 = applyAffinityLaws(baseP2, 'power') * pumpCount.value
    const actualNPSH = applyAffinityLaws(baseNPSH, 'head')
    
    data.flow.push(actualFlow)
    data.head.push(actualHead)
    data.efficiency1.push(actualEff1)
    data.efficiency2.push(actualEff2)
    data.p1Power.push(actualP1)
    data.p2Power.push(actualP2)
    data.npsh.push(actualNPSH)
  }
  
  return data
}

// 设置变化处理
const handleSettingsChange = () => {
  updateCharts()
}

// 更新上方图表（扬程和效率）
const updateTopChart = (data: any) => {
  const option: any = {
    title: {
      text: '扬程特性曲线和效率曲线',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: function(params: any) {
        let result = `流量: ${params[0].axisValue} m³/h<br/>`
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value} ${param.seriesName.includes('扬程') ? 'm' : '%'}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['扬程曲线', '效率曲线1', '效率曲线2'],
      top: 30,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '10%',
      right: '10%',
      top: '20%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '流量 Q (m³/h)',
      nameLocation: 'middle',
      nameGap: 30,
      min: 0,
      max: 900,
      interval: 50,
      axisLine: {
        lineStyle: {
          color: '#333'
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '扬程 H (m)',
        nameLocation: 'middle',
        nameGap: 50,
        min: 0,
        max: 55,
        interval: 5,
        splitNumber: 11,
        axisLine: {
          lineStyle: {
            color: '#1f77b4'
          }
        },
        axisLabel: {
          color: '#1f77b4'
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0'
          }
        }
      },
      {
        type: 'value',
        name: '效率 η (%)',
        nameLocation: 'middle',
        nameGap: 50,
        min: 0,
        max: 220,
        interval: 20,
        axisLine: {
          lineStyle: {
            color: '#2c2c2c'
          }
        },
        axisLabel: {
          color: '#2c2c2c',
          formatter: function(value: number) {
            // 只显示0-100%范围内的刻度值
            if (value <= 100) {
              return String(value);
            }
            return '';  // 100%以上不显示刻度值
          }
        }
      }
    ],
    series: [
      {
        name: '扬程曲线',
        type: 'line',
        yAxisIndex: 0,
        data: data.flow.map((flow: number, index: number) => [flow, data.head[index]]),
        lineStyle: {
          color: '#1f77b4',
          width: 3
        },
        symbol: 'none',
        smooth: true
      },
      {
        name: '效率曲线1',
        type: 'line',
        yAxisIndex: 1,
        data: data.flow.map((flow: number, index: number) => [flow, data.efficiency1[index]]),
        lineStyle: {
          color: '#000',
          width: 2
        },
        symbol: 'none',
        smooth: true
      },
      {
        name: '效率曲线2',
        type: 'line',
        yAxisIndex: 1,
        data: data.flow.map((flow: number, index: number) => [flow, data.efficiency2[index]]),
        lineStyle: {
          color: '#666',
          width: 2,
          type: 'dashed'
        },
        symbol: 'none',
        smooth: true
      }
    ]
  }

  // 添加525毫米标注
  option.graphic = [
    {
      type: 'text',
      left: '10%',
      top: '20%',
      style: {
        text: '525 毫米',
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    {
      type: 'text',
      left: '10%',
      bottom: '5%',
      style: {
        text: '输送的液体 = 水\n运行期间的液体温度 = 20 °C\n密度 = 998.2 kg/m³',
        fontSize: 12,
        lineHeight: 20
      }
    }
  ]

  // 添加工作点标记
  if (workingFlow.value > 0) {
    option.series.push({
      name: '工作点',
      type: 'scatter',
      yAxisIndex: 0,
      data: [[workingFlow.value, workingHead.value]],
      symbolSize: 12,
      itemStyle: {
        color: '#ff6b35',
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: true,
        position: 'top',
        formatter: '工作点',
        color: '#ff6b35',
        fontSize: 12,
        fontWeight: 'bold'
      }
    } as any)
  }

  topChart?.setOption(option, true)
}

// 更新下方图表（功率和NPSH）
const updateBottomChart = (data: any) => {
  const option: any = {
    title: {
      text: '功率特性曲线和NPSH曲线',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: function(params: any) {
        let result = `流量: ${params[0].axisValue} m³/h<br/>`
        params.forEach((param: any) => {
          const unit = param.seriesName.includes('NPSH') ? 'm' : 'kW'
          result += `${param.seriesName}: ${param.value} ${unit}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['P1功率', 'P2功率', 'NPSH'],
      top: 30,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '10%',
      right: '10%',
      top: '20%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '流量 Q (m³/h)',
      nameLocation: 'middle',
      nameGap: 30,
      min: 0,
      max: Math.max(...data.flow) * 1.1,
      axisLine: {
        lineStyle: {
          color: '#333'
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '功率 P (kW)',
        nameLocation: 'middle',
        nameGap: 50,
        min: 0,
        max: Math.max(...data.p1Power, ...data.p2Power) * 1.1,
        axisLine: {
          lineStyle: {
            color: '#d62728'
          }
        },
        axisLabel: {
          color: '#d62728'
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0'
          }
        }
      },
      {
        type: 'value',
        name: 'NPSH (m)',
        nameLocation: 'middle',
        nameGap: 50,
        min: 0,
        max: Math.max(...data.npsh) * 1.2,
        axisLine: {
          lineStyle: {
            color: '#2ca02c'
          }
        },
        axisLabel: {
          color: '#2ca02c'
        }
      }
    ],
    series: [
      {
        name: 'P1功率',
        type: 'line',
        yAxisIndex: 0,
        data: data.flow.map((flow: number, index: number) => [flow, data.p1Power[index]]),
        lineStyle: {
          color: '#d62728',
          width: 3
        },
        symbol: 'none',
        smooth: true
      },
      {
        name: 'P2功率',
        type: 'line',
        yAxisIndex: 0,
        data: data.flow.map((flow: number, index: number) => [flow, data.p2Power[index]]),
        lineStyle: {
          color: '#b91c1c',
          width: 2,
          type: 'dashed'
        },
        symbol: 'none',
        smooth: true
      },
      {
        name: 'NPSH',
        type: 'line',
        yAxisIndex: 1,
        data: data.flow.map((flow: number, index: number) => [flow, data.npsh[index]]),
        lineStyle: {
          color: '#2ca02c',
          width: 3
        },
        symbol: 'none',
        smooth: true
      }
    ]
  }

  // 添加工作点标记
  if (workingFlow.value > 0) {
    option.series.push({
      name: '功率工作点',
      type: 'scatter',
      yAxisIndex: 0,
      data: [[workingFlow.value, workingP1Power.value]],
      symbolSize: 12,
      itemStyle: {
        color: '#ff6b35',
        borderColor: '#fff',
        borderWidth: 2
      }
    } as any)

    option.series.push({
      name: 'NPSH工作点',
      type: 'scatter',
      yAxisIndex: 1,
      data: [[workingFlow.value, workingNPSH.value]],
      symbolSize: 12,
      itemStyle: {
        color: '#ff6b35',
        borderColor: '#fff',
        borderWidth: 2
      }
    } as any)
  }

  bottomChart?.setOption(option, true)
}

// 更新图表
const updateCharts = () => {
  if (!topChart || !bottomChart) return

  const data = generateCurveData()

  // 更新上方图表
  updateTopChart(data)

  // 更新下方图表
  updateBottomChart(data)
}

// 初始化图表
const initCharts = () => {
  if (topChartRef.value && bottomChartRef.value) {
    // 初始化上方图表
    topChart = echarts.init(topChartRef.value)

    // 初始化下方图表
    bottomChart = echarts.init(bottomChartRef.value)

    // 首次更新图表
    updateCharts()

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      topChart?.resize()
      bottomChart?.resize()
    })
  }
}

// 组件挂载
onMounted(() => {
  nextTick(() => {
    initCharts()
  })
})

// 监听数据变化
watch([pumpCount, frequency, workingFlow, workingHead], () => {
  updateCharts()
}, { deep: true })

watch(curveSettings, () => {
  updateCharts()
}, { deep: true })

// 组件卸载时清理
onUnmounted(() => {
  topChart?.dispose()
  bottomChart?.dispose()
  window.removeEventListener('resize', () => {
    topChart?.resize()
    bottomChart?.resize()
  })
})
</script>

<style scoped>
.grundfos-echarts-version {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 28px;
  font-weight: bold;
  color: #1a365d;
  margin: 0 0 10px 0;
}

.page-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.chart-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.chart-legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.legend-color {
  width: 20px;
  height: 3px;
  border-radius: 2px;
}

.chart-area {
  width: 100%;
  height: 400px;
  padding: 10px;
}

.technical-data {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.technical-data h4 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.parameters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.parameter-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.parameter-item label {
  font-weight: 600;
  color: #333;
}

.parameter-item span {
  font-weight: bold;
  color: #007bff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grundfos-echarts-version {
    padding: 10px;
  }

  .chart-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .chart-legend {
    flex-wrap: wrap;
    gap: 10px;
  }

  .chart-area {
    height: 300px;
  }

  .parameters-grid {
    grid-template-columns: 1fr;
  }
}
</style>
