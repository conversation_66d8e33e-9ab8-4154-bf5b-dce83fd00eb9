<template>
  <div class="backup-restore">
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><FolderOpened /></el-icon>
          <span>备份与恢复</span>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="setting-section">
            <template #header>
              <span>自动备份设置</span>
            </template>
            
            <el-form :model="backupForm" label-width="120px">
              <el-form-item label="自动备份">
                <el-switch v-model="backupForm.autoBackup" />
              </el-form-item>
              
              <div v-if="backupForm.autoBackup">
                <el-form-item label="备份频率">
                  <el-select v-model="backupForm.backupInterval">
                    <el-option label="每日" value="daily" />
                    <el-option label="每周" value="weekly" />
                    <el-option label="每月" value="monthly" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="备份时间">
                  <el-time-picker 
                    v-model="backupTime" 
                    format="HH:mm"
                    placeholder="选择备份时间"
                  />
                </el-form-item>
                
                <el-form-item label="保留期限(天)">
                  <el-input-number v-model="backupForm.retentionPeriod" :min="7" :max="365" />
                  <div class="form-tip">超过期限的备份将自动删除</div>
                </el-form-item>
                
                <el-form-item label="备份位置">
                  <el-input v-model="backupForm.backupLocation" placeholder="/backup" />
                </el-form-item>
              </div>
            </el-form>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card class="setting-section">
            <template #header>
              <span>备份内容</span>
            </template>
            
            <el-form :model="backupForm" label-width="120px">
              <el-form-item label="包含文件">
                <el-switch v-model="backupForm.includeFiles" />
                <div class="form-tip">备份上传的文件和附件</div>
              </el-form-item>
              
              <el-form-item label="包含数据库">
                <el-switch v-model="backupForm.includeDatabase" />
                <div class="form-tip">备份所有业务数据</div>
              </el-form-item>
              
              <el-form-item label="包含配置">
                <el-switch v-model="backupForm.includeSettings" />
                <div class="form-tip">备份系统配置信息</div>
              </el-form-item>
              
              <el-form-item label="压缩备份">
                <el-switch v-model="backupForm.compression" />
                <div class="form-tip">减少备份文件大小</div>
              </el-form-item>
              
              <el-form-item label="加密备份">
                <el-switch v-model="backupForm.encryption" />
                <div class="form-tip">保护备份文件安全</div>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
      
      <el-card class="setting-section">
        <template #header>
          <div class="section-header">
            <span>手动备份</span>
            <el-button type="primary" @click="performBackup" :loading="backupLoading">
              <el-icon><Download /></el-icon>
              立即备份
            </el-button>
          </div>
        </template>
        
        <div class="backup-info">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <div class="info-label">最后备份时间</div>
                <div class="info-value">{{ formatTime(settingsStore.systemInfo.lastBackup) }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <div class="info-label">备份状态</div>
                <div class="info-value">
                  <el-tag type="success">正常</el-tag>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <div class="info-label">下次自动备份</div>
                <div class="info-value">{{ getNextBackupTime() }}</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
      
      <el-card class="setting-section">
        <template #header>
          <span>备份历史</span>
        </template>
        
        <el-table :data="backupHistory" stripe>
          <el-table-column prop="filename" label="文件名" min-width="200" />
          
          <el-table-column prop="size" label="大小" width="100" />
          
          <el-table-column prop="type" label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="row.type === 'auto' ? 'primary' : 'success'">
                {{ row.type === 'auto' ? '自动' : '手动' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="createdAt" label="创建时间" width="150">
            <template #default="{ row }">
              {{ formatTime(row.createdAt) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button size="small" @click="downloadBackup(row)">
                下载
              </el-button>
              <el-button size="small" @click="restoreBackup(row)" :disabled="row.status !== 'success'">
                恢复
              </el-button>
              <el-button size="small" type="danger" @click="deleteBackup(row.id)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      
      <el-card class="setting-section">
        <template #header>
          <span>恢复备份</span>
        </template>
        
        <div class="restore-section">
          <el-upload
            class="upload-demo"
            drag
            action="#"
            :before-upload="beforeUpload"
            :http-request="handleUpload"
            :show-file-list="false"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将备份文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 .zip 格式的备份文件，且不超过 500MB
              </div>
            </template>
          </el-upload>
        </div>
      </el-card>
      
      <div class="form-actions">
        <el-button @click="resetSettings">重置</el-button>
        <el-button type="primary" @click="saveSettings" :loading="settingsStore.loading">
          保存设置
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { FolderOpened, Download, UploadFilled } from '@element-plus/icons-vue'
import { useSettingsStore } from '@/stores/settings'
import dayjs from 'dayjs'
import { ElMessage, ElMessageBox } from 'element-plus'

const settingsStore = useSettingsStore()
const backupLoading = ref(false)

// 表单数据
const backupForm = reactive({ ...settingsStore.settings.backup })
const backupTime = ref(new Date(`2000-01-01 ${backupForm.backupTime}`))

// 模拟备份历史数据
const backupHistory = ref([
  {
    id: 'backup_001',
    filename: 'backup_2024-01-15_02-00.zip',
    size: '125.6 MB',
    type: 'auto',
    createdAt: '2024-01-15 02:00:00',
    status: 'success'
  },
  {
    id: 'backup_002',
    filename: 'backup_2024-01-14_02-00.zip',
    size: '123.2 MB',
    type: 'auto',
    createdAt: '2024-01-14 02:00:00',
    status: 'success'
  },
  {
    id: 'backup_003',
    filename: 'manual_backup_2024-01-13.zip',
    size: '128.9 MB',
    type: 'manual',
    createdAt: '2024-01-13 15:30:00',
    status: 'success'
  },
  {
    id: 'backup_004',
    filename: 'backup_2024-01-13_02-00.zip',
    size: '0 MB',
    type: 'auto',
    createdAt: '2024-01-13 02:00:00',
    status: 'failed'
  }
])

// 方法
const formatTime = (timestamp: string) => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

const getNextBackupTime = () => {
  if (!backupForm.autoBackup) return '未启用'
  
  const now = dayjs()
  let next = now
  
  switch (backupForm.backupInterval) {
    case 'daily':
      next = now.add(1, 'day').hour(2).minute(0).second(0)
      break
    case 'weekly':
      next = now.add(1, 'week').day(0).hour(2).minute(0).second(0)
      break
    case 'monthly':
      next = now.add(1, 'month').date(1).hour(2).minute(0).second(0)
      break
  }
  
  return next.format('YYYY-MM-DD HH:mm')
}

const getStatusType = (status: string) => {
  const types = {
    success: 'success',
    failed: 'danger',
    running: 'warning'
  }
  return types[status as keyof typeof types] || 'info'
}

const getStatusText = (status: string) => {
  const texts = {
    success: '成功',
    failed: '失败',
    running: '进行中'
  }
  return texts[status as keyof typeof texts] || status
}

const performBackup = async () => {
  backupLoading.value = true
  try {
    const result = await settingsStore.performBackup()
    if (result.success) {
      ElMessage.success(`备份完成：${result.filename}`)
      // 添加到备份历史
      backupHistory.value.unshift({
        id: `backup_${Date.now()}`,
        filename: result.filename,
        size: result.size,
        type: 'manual',
        createdAt: new Date().toISOString(),
        status: 'success'
      })
    }
  } catch {
    ElMessage.error('备份失败')
  } finally {
    backupLoading.value = false
  }
}

const downloadBackup = (backup: any) => {
  ElMessage.success(`开始下载：${backup.filename}`)
}

const restoreBackup = async (backup: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要恢复备份 ${backup.filename} 吗？这将覆盖当前数据。`,
      '确认恢复',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('备份恢复成功')
  } catch {
    // 用户取消
  }
}

const deleteBackup = async (backupId: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个备份吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = backupHistory.value.findIndex(b => b.id === backupId)
    if (index !== -1) {
      backupHistory.value.splice(index, 1)
      ElMessage.success('备份删除成功')
    }
  } catch {
    // 用户取消
  }
}

const beforeUpload = (file: File) => {
  const isZip = file.type === 'application/zip' || file.name.endsWith('.zip')
  const isLt500M = file.size / 1024 / 1024 < 500

  if (!isZip) {
    ElMessage.error('只能上传 ZIP 格式的备份文件!')
    return false
  }
  if (!isLt500M) {
    ElMessage.error('备份文件大小不能超过 500MB!')
    return false
  }
  return true
}

const handleUpload = async (options: any) => {
  try {
    const success = await settingsStore.restoreBackup(options.file)
    if (success) {
      ElMessage.success('备份恢复成功')
    }
  } catch {
    ElMessage.error('备份恢复失败')
  }
}

const saveSettings = async () => {
  try {
    // 更新备份时间
    const timeStr = dayjs(backupTime.value).format('HH:mm')
    backupForm.backupTime = timeStr
    
    await settingsStore.updateSettings('backup', backupForm)
    ElMessage.success('备份设置保存成功')
  } catch {
    ElMessage.error('备份设置保存失败')
  }
}

const resetSettings = () => {
  Object.assign(backupForm, settingsStore.settings.backup)
  backupTime.value = new Date(`2000-01-01 ${backupForm.backupTime}`)
  ElMessage.info('已重置为默认设置')
}
</script>

<style lang="scss" scoped>
.backup-restore {
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
  }
  
  .setting-section {
    margin-bottom: 20px;
    
    :deep(.el-card__header) {
      padding: 12px 20px;
      background-color: var(--el-bg-color-page);
      font-weight: 600;
    }
    
    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }
  }
  
  .form-tip {
    font-size: 12px;
    color: var(--el-text-color-regular);
    margin-top: 4px;
  }
  
  .backup-info {
    .info-item {
      text-align: center;
      padding: 16px;
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      
      .info-label {
        font-size: 12px;
        color: var(--el-text-color-regular);
        margin-bottom: 8px;
      }
      
      .info-value {
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
  }
  
  .restore-section {
    padding: 20px 0;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--el-border-color-light);
  }
}
</style>
