{"name": "smart-water-platform", "version": "2.0.0", "description": "深圳铂今智慧水务平台 - 现代化版本", "type": "module", "scripts": {"dev": "cross-env SASS_SILENCE_DEPRECATIONS=legacy-js-api vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.0", "@modelcontextprotocol/inspector": "^0.14.0", "@modelcontextprotocol/sdk": "^1.12.1", "@modelcontextprotocol/server-brave-search": "^0.6.2", "@modelcontextprotocol/server-filesystem": "^2025.3.28", "@modelcontextprotocol/server-sequential-thinking": "^0.6.2", "@types/mqtt": "^2.5.0", "@vueuse/core": "^13.3.0", "axios": "^1.6.0", "dayjs": "^1.11.0", "echarts": "^5.6.0", "element-plus": "^2.4.0", "file-saver": "^2.0.5", "lodash-es": "^4.17.0", "ml-matrix": "^6.12.1", "ml-regression": "^6.3.0", "mqtt": "^5.13.1", "papaparse": "^5.5.3", "pinia": "^2.1.0", "simple-statistics": "^7.8.8", "vue": "^3.4.0", "vue-router": "^4.2.0", "xlsx": "^0.18.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.6.0", "@types/file-saver": "^2.0.7", "@types/node": "^24.0.0", "@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.0", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.0", "prettier": "^3.1.0", "sass": "^1.89.2", "typescript": "~5.3.0", "unplugin-auto-import": "^0.17.0", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.0", "vue-tsc": "^1.8.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0"}