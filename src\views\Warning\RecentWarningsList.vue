<template>
  <div class="recent-warnings-list">
    <el-table :data="warnings" stripe>
      <el-table-column prop="severity" label="级别" width="80">
        <template #default="{ row }">
          <el-tag :type="getSeverityType(row.severity)" size="small">
            {{ getSeverityText(row.severity) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="title" label="预警信息" min-width="200">
        <template #default="{ row }">
          <div class="warning-info">
            <div class="warning-title">{{ row.title }}</div>
            <div class="warning-message">{{ row.message }}</div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="category" label="类别" width="100">
        <template #default="{ row }">
          <el-tag :type="getCategoryType(row.category)" size="small">
            {{ getCategoryText(row.category) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="timestamp" label="发生时间" width="150">
        <template #default="{ row }">
          <div class="timestamp">
            <el-icon><Clock /></el-icon>
            <span>{{ formatTime(row.timestamp) }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="resolved" label="状态" width="80">
        <template #default="{ row }">
          <el-tag :type="row.resolved ? 'success' : 'danger'" size="small">
            {{ row.resolved ? '已处理' : '待处理' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="120">
        <template #default="{ row }">
          <el-button
            v-if="!row.resolved"
            size="small"
            type="primary"
            @click="handleResolve(row.id)"
          >
            处理
          </el-button>
          <el-button
            size="small"
            @click="showDetails(row)"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div v-if="warnings.length === 0" class="empty-state">
      <el-empty description="暂无预警信息">
        <el-icon size="64" color="#C0C4CC">
          <CircleCheck />
        </el-icon>
        <div style="margin-top: 16px; color: #67C23A;">
          系统运行正常
        </div>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Clock, CircleCheck } from '@element-plus/icons-vue'
import type { WarningInfo, WarningCategory, WarningSeverity } from '@/types'
import dayjs from 'dayjs'
import { ElMessageBox } from 'element-plus'

interface Props {
  warnings: WarningInfo[]
}

interface Emits {
  (e: 'resolve', warningId: string): void
  (e: 'showDetails', warning: WarningInfo): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 方法
const getSeverityType = (severity: WarningSeverity) => {
  const types = {
    critical: 'danger',
    high: 'warning',
    medium: 'primary',
    low: 'info'
  }
  return types[severity] || 'info'
}

const getSeverityText = (severity: WarningSeverity) => {
  const texts = {
    critical: '严重',
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[severity] || severity
}

const getCategoryType = (category: WarningCategory) => {
  const types = {
    equipment_fault: 'danger',
    performance_anomaly: 'warning',
    maintenance_due: 'info',
    energy_consumption: 'primary',
    efficiency_drop: 'warning',
    vibration_high: 'danger',
    temperature_abnormal: 'danger',
    pressure_abnormal: 'warning',
    flow_abnormal: 'warning',
    system_error: 'danger'
  }
  return types[category] || 'info'
}

const getCategoryText = (category: WarningCategory) => {
  const texts = {
    equipment_fault: '设备故障',
    performance_anomaly: '性能异常',
    maintenance_due: '维护到期',
    energy_consumption: '能耗异常',
    efficiency_drop: '效率下降',
    vibration_high: '振动过高',
    temperature_abnormal: '温度异常',
    pressure_abnormal: '压力异常',
    flow_abnormal: '流量异常',
    system_error: '系统错误'
  }
  return texts[category] || category
}

const formatTime = (timestamp: string) => {
  return dayjs(timestamp).format('MM-DD HH:mm')
}

const handleResolve = async (warningId: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要处理这个预警吗？',
      '确认处理',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    emit('resolve', warningId)
  } catch {
    // 用户取消
  }
}

const showDetails = (warning: WarningInfo) => {
  emit('showDetails', warning)
}
</script>

<style lang="scss" scoped>
.recent-warnings-list {
  .warning-info {
    .warning-title {
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 4px;
    }
    
    .warning-message {
      font-size: 12px;
      color: var(--el-text-color-regular);
      line-height: 1.4;
    }
  }
  
  .timestamp {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: var(--el-text-color-regular);
  }
  
  .empty-state {
    padding: 40px;
    text-align: center;
  }
}

:deep(.el-table) {
  .el-table__row {
    &:hover {
      background-color: var(--el-table-row-hover-bg-color);
    }
  }
}
</style>
