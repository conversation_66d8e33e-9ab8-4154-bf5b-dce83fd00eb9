var M=Object.defineProperty;var C=Object.getOwnPropertySymbols;var L=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable;var _=(t,o,e)=>o in t?M(t,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[o]=e,y=(t,o)=>{for(var e in o||(o={}))L.call(o,e)&&_(t,e,o[e]);if(C)for(var e of C(o))k.call(o,e)&&_(t,e,o[e]);return t};var h=(t,o,e)=>new Promise((s,r)=>{var n=c=>{try{a(e.next(c))}catch(d){r(d)}},i=c=>{try{a(e.throw(c))}catch(d){r(d)}},a=c=>c.done?s(c.value):Promise.resolve(c.value).then(n,i);a((e=e.apply(t,o)).next())});import{u as A,N as m}from"/etc.clientlibs/settings/wcm/design/aem-selectiontools/dbs/resources/js/chunks/vue.be4e63a4.js";import{d as N,c as P}from"/etc.clientlibs/settings/wcm/design/aem-selectiontools/dbs/resources/js/chunks/pinia.8c97c576.js";import{y as $}from"/etc.clientlibs/settings/wcm/design/aem-selectiontools/dbs/resources/js/chunks/vue-dompurify-html.e9de19ba.js";import{p as S,d as D}from"/etc.clientlibs/settings/wcm/design/aem-selectiontools/dbs/resources/js/chunks/@formkit.e4fea57f.js";import{c as B}from"/etc.clientlibs/settings/wcm/design/aem-selectiontools/dbs/resources/js/chunks/cookie.de0d2061.js";import"/etc.clientlibs/settings/wcm/design/aem-selectiontools/dbs/resources/js/chunks/vue-demi.738a00cf.js";import"/etc.clientlibs/settings/wcm/design/aem-selectiontools/dbs/resources/js/chunks/dompurify.a27cc898.js";const G="modulepreload",K=function(t){return"/etc.clientlibs/settings/wcm/design/aem-selectiontools/"+t},T={},f=function(o,e,s){if(!e||e.length===0)return o();const r=document.getElementsByTagName("link");return Promise.all(e.map(n=>{if(n=K(n),n in T)return;T[n]=!0;const i=n.endsWith(".css"),a=i?'[rel="stylesheet"]':"";if(!!s)for(let l=r.length-1;l>=0;l--){const u=r[l];if(u.href===n&&(!i||u.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${n}"]${a}`))return;const d=document.createElement("link");if(d.rel=i?"stylesheet":G,i||(d.as="script",d.crossOrigin=""),d.href=n,document.head.appendChild(d),i)return new Promise((l,u)=>{d.addEventListener("load",l),d.addEventListener("error",()=>u(new Error(`Unable to preload CSS for ${n}`)))})})).then(()=>o()).catch(n=>{const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=n,window.dispatchEvent(i),!i.defaultPrevented)throw n})},U={tooltipAttr:"data-v-tooltip",addClass(t,o=""){t.classList.add(`${this.tooltipAttr}${o}`)},updateTooltip(t,{value:o,modifiers:e}){if(!o)return null;if(typeof o=="string")t.setAttribute(this.tooltipAttr,o),e.arrow&&this.addClass(t,"__arrow");else{const{text:s,displayArrow:r,position:n}=o;s&&t.setAttribute(this.tooltipAttr,s),(r||e.arrow)&&this.addClass(t,"__arrow"),n&&typeof n=="string"&&this.addClass(t,`--position-${n}`)}},mounted(t,{value:o,dir:e,modifiers:s}){(typeof o=="object"&&(o!=null&&o.text)||typeof o=="string")&&e.addClass(t),e.updateTooltip(t,{value:o,modifiers:s})},updated(t,{value:o,dir:e,modifiers:s}){e.updateTooltip(t,{value:o,modifiers:s})}},W={beforeMount(t,o){t.clickOutsideEvent=e=>h(this,null,function*(){t===e.target||t.contains(e.target)||o.value(e,t)}),document.body.addEventListener("click",t.clickOutsideEvent)},unmounted(t){document.body.removeEventListener("click",t.clickOutsideEvent)}};var R,v;const q=!!((v=(R=window==null?void 0:window.CQ)==null?void 0:R.WCM)!=null&&v.isEditMode()),p=B.parse(document.cookie),x="crm-token",Q="login-state",V="partnerId",g="user-type",j=N("baseStore",{state:()=>({confirmation:null,notification:null,tooltipComponent:null,overlayComponent:null,dbsOverlayComponent:null,isEditMode:q,user:{isConfirmed:g in p&&p[g]==="2",isCrm:x in p,isLoggedIn:Q in p,isGrundfosEmployee:!1,isPartner:Object.keys(p).some(t=>t.includes(V))}}),actions:{setConfirmation(t){this.confirmation=t},setNotification(t){this.notification=t},setIsEditMode(t){this.isEditMode=t},setIsLoggedIn(t){this.user.isLoggedIn=t},setIsGrundfosEmployee(t){this.user.isGrundfosEmployee=t},setTooltipComponent(t){this.tooltipComponent=t},setOverlayComponent(t){this.overlayComponent=t},setDbsOverlayComponent(t){this.dbsOverlayComponent=t}},getters:{isGrundfosEmployee(t){return t.user.isLoggedIn&&t.user.isGrundfosEmployee}}}),F=P(),Y=t=>A(y({},t)).use(S,D).use(F).use($).directive("tooltip",U).directive("clickOutside",W);let E="data-vue3-component-root",O=`[${E}]`;const z=(t,o)=>{E=t,O=o},w=document.querySelector("body"),b=new Map;function H(t){return!!t.parentElement&&!t.parentElement.closest(O)}function J(t,o,e){const s=`${E}-${e}`;if(b.has(s))return;const r=Y(t).mount(o);b.set(s,r)}const X=t=>{window.addEventListener("load",()=>{var o,e,s,r;if(w){Array.from(w.querySelectorAll(O)).filter(H).forEach((i,a)=>J(t,i,a));const n=()=>{var a,c;const i=!!((c=(a=window==null?void 0:window.CQ)==null?void 0:a.WCM)!=null&&c.isEditMode(!0));document.body.classList.toggle("b-edit-mode",i),j().setIsEditMode(i)};(e=(o=window==null?void 0:window.CQ)==null?void 0:o.WCM)!=null&&e.isEditMode&&((s=window==null?void 0:window.parent)!=null&&s.$)&&(n(),window==null||window.parent.$((r=window==null?void 0:window.parent)==null?void 0:r.document).on("editor-frame-mode-changed",n))}})},Z=m(()=>f(()=>import("/etc.clientlibs/settings/wcm/design/aem-selectiontools/dbs/resources/js/chunks/ModDbs.3d3f92fa.js"),["dbs/resources/js/chunks/ModDbs.3d3f92fa.js","dbs/resources/js/chunks/ModDbs@vue@vue@type@script@setup@<EMAIL>","dbs/resources/js/chunks/vue.be4e63a4.js","dbs/resources/js/chunks/grundfos-aem-base.a59268ce.js","dbs/resources/js/chunks/lodash.0170f7da.js","dbs/resources/js/chunks/call-bind.e74be82a.js","dbs/resources/js/chunks/get-intrinsic.2ef8b220.js","dbs/resources/js/chunks/es-errors.65fb74fb.js","dbs/resources/js/chunks/has-symbols.01c00318.js","dbs/resources/js/chunks/has-proto.5a33d920.js","dbs/resources/js/chunks/function-bind.cd3c6dd0.js","dbs/resources/js/chunks/hasown.1cf6b71d.js","dbs/resources/js/chunks/set-function-length.9b2a3166.js","dbs/resources/js/chunks/define-data-property.6ef3de54.js","dbs/resources/js/chunks/es-define-property.f96e0c73.js","dbs/resources/js/chunks/gopd.600ab611.js","dbs/resources/js/chunks/has-property-descriptors.fd6f3c91.js","dbs/resources/js/chunks/qs.5ab0e8f3.js","dbs/resources/js/chunks/side-channel.a5d871cd.js","dbs/resources/js/chunks/object-inspect.a65e5157.js","dbs/resources/js/chunks/pinia.8c97c576.js","dbs/resources/js/chunks/vue-demi.738a00cf.js","dbs/resources/js/chunks/axios.539e5d75.js","dbs/resources/js/chunks/dompurify.a27cc898.js","dbs/resources/js/chunks/vue-dompurify-html.e9de19ba.js","dbs/resources/js/chunks/@formkit.e4fea57f.js","dbs/resources/js/chunks/cookie.de0d2061.js"])),tt=m(()=>f(()=>import("/etc.clientlibs/settings/wcm/design/aem-selectiontools/dbs/resources/js/chunks/ModDbsButton.6bab0f34.js"),["dbs/resources/js/chunks/ModDbsButton.6bab0f34.js","dbs/resources/js/chunks/vue.be4e63a4.js","dbs/resources/js/chunks/pinia.8c97c576.js","dbs/resources/js/chunks/vue-demi.738a00cf.js","dbs/resources/js/chunks/vue-dompurify-html.e9de19ba.js","dbs/resources/js/chunks/dompurify.a27cc898.js","dbs/resources/js/chunks/@formkit.e4fea57f.js","dbs/resources/js/chunks/cookie.de0d2061.js"])),ot=m(()=>f(()=>import("/etc.clientlibs/settings/wcm/design/aem-selectiontools/dbs/resources/js/chunks/CmpDbsOverlay.f4fffde9.js"),["dbs/resources/js/chunks/CmpDbsOverlay.f4fffde9.js","dbs/resources/js/chunks/vue.be4e63a4.js","dbs/resources/js/chunks/ModDbs@vue@vue@type@script@setup@<EMAIL>","dbs/resources/js/chunks/grundfos-aem-base.a59268ce.js","dbs/resources/js/chunks/lodash.0170f7da.js","dbs/resources/js/chunks/call-bind.e74be82a.js","dbs/resources/js/chunks/get-intrinsic.2ef8b220.js","dbs/resources/js/chunks/es-errors.65fb74fb.js","dbs/resources/js/chunks/has-symbols.01c00318.js","dbs/resources/js/chunks/has-proto.5a33d920.js","dbs/resources/js/chunks/function-bind.cd3c6dd0.js","dbs/resources/js/chunks/hasown.1cf6b71d.js","dbs/resources/js/chunks/set-function-length.9b2a3166.js","dbs/resources/js/chunks/define-data-property.6ef3de54.js","dbs/resources/js/chunks/es-define-property.f96e0c73.js","dbs/resources/js/chunks/gopd.600ab611.js","dbs/resources/js/chunks/has-property-descriptors.fd6f3c91.js","dbs/resources/js/chunks/qs.5ab0e8f3.js","dbs/resources/js/chunks/side-channel.a5d871cd.js","dbs/resources/js/chunks/object-inspect.a65e5157.js","dbs/resources/js/chunks/pinia.8c97c576.js","dbs/resources/js/chunks/vue-demi.738a00cf.js","dbs/resources/js/chunks/axios.539e5d75.js","dbs/resources/js/chunks/dompurify.a27cc898.js","dbs/resources/js/chunks/vue-dompurify-html.e9de19ba.js","dbs/resources/js/chunks/@formkit.e4fea57f.js","dbs/resources/js/chunks/cookie.de0d2061.js"])),et={components:{ModDbs:Z,ModDbsButton:tt,CmpDbsOverlay:ot}},I="data-vue3-component-dbs",nt=`[${I}]`;z(I,nt);X(et);export{j as b};
