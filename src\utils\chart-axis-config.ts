/**
 * 图表坐标轴配置工具
 * 根据数据最大值自动计算Y轴范围、步长和显示刻度的通用算法
 */

/**
 * 创建Y轴配置
 * @param maxValue 数据最大值
 * @param axisType 轴类型：'power'(功率)、'npsh'(汽蚀余量)、'head'(扬程)、'efficiency'(效率)等
 * @param options 附加选项
 * @returns Y轴配置对象
 */
export function createYAxisConfig(maxValue: number, axisType: string, options?: any) {
  // 默认配置
  const defaultConfig: any = {
    type: 'value',
    min: 0,
    splitNumber: 11, // 默认分割为11条线
    axisLine: {
      lineStyle: {
        color: '#333'
      }
    }
  };

  // 根据数据最大值计算合适的步长和最大值
  const { max, interval, showFormatter } = calculateAxisRange(maxValue, axisType);
  
  // 设置计算出的最大值和步长
  defaultConfig.max = max;
  defaultConfig.interval = interval;
  
  // 设置轴标签格式化函数
  defaultConfig.axisLabel = {
    formatter: showFormatter
  };

  // 根据轴类型设置名称
  switch (axisType) {
    case 'power':
      defaultConfig.name = '功率 (kW)';
      break;
    case 'npsh':
      defaultConfig.name = 'NPSH (m)';
      break;
    case 'head':
      defaultConfig.name = '扬程 (m)';
      break;
    case 'efficiency':
      defaultConfig.name = '效率 (%)';
      break;
    default:
      defaultConfig.name = '';
  }

  // 合并自定义选项
  if (options) {
    Object.assign(defaultConfig, options);
  }

  return defaultConfig;
}

/**
 * 计算坐标轴范围和步长
 * @param maxValue 数据最大值
 * @param axisType 轴类型
 * @returns 坐标轴配置对象
 */
export function calculateAxisRange(maxValue: number, axisType: string) {
  let interval: number;
  let max: number;
  
  // 根据轴类型和数据最大值确定步长和最大值
  if (axisType === 'efficiency') {
    // 效率Y轴特殊处理：范围0-220%，步长20，只显示0-100%范围内的刻度值
    interval = 20;
    max = 220;
    
    // 效率轴的格式化函数：只显示0-100%范围内的刻度值
    const showFormatter = function(value: number) {
      if (value <= 100) {
        return String(value);
      }
      return '';  // 100%以上不显示刻度值
    };
    
    return { max, interval, showFormatter };
  } else if (axisType === 'head') {
    // 扬程Y轴：范围0-55，步长5，共11个刻度
    interval = 5;
    max = 55;
    
    // 标准格式化函数
    const showFormatter = function(value: number) {
      return String(value);
    };
    
    return { max, interval, showFormatter };
  } else {
    // 其他类型轴的处理
    
    // 根据数据最大值确定合适的步长
    if (axisType === 'power') {
      // 功率根据最大值动态设置范围和步长
      if (maxValue <= 20) {
        interval = 5;
        max = 35;
      } else if (maxValue <= 50) {
        interval = 10;
        max = 70;
      } else if (maxValue <= 100) {
        interval = 20;
        max = 140;
      } else {
        interval = Math.ceil(maxValue / 7); // 确保有7个刻度
        max = interval * 7;
      }
    } else if (axisType === 'npsh') {
      // NPSH根据最大值动态设置范围和步长
      if (maxValue <= 5) {
        interval = 1;
        max = 7;
      } else if (maxValue <= 10) {
        interval = 2;
        max = 14;
      } else if (maxValue <= 20) {
        interval = 4;
        max = 28;
      } else {
        interval = Math.ceil(maxValue / 7); // 确保有7个刻度
        max = interval * 7;
      }
    } else {
      // 动态计算合适的步长
      if (maxValue <= 10) {
        interval = 1;
      } else if (maxValue <= 50) {
        interval = 5;
      } else if (maxValue <= 100) {
        interval = 10;
      } else if (maxValue <= 500) {
        interval = 50;
      } else {
        interval = 100;
      }
      
      // 计算最大值：向上取整到步长的整数倍，再加一个步长
      max = Math.ceil(maxValue / interval) * interval + interval;
      
      // 确保刻度线数量为11个（即分成10等份）
      const splitCount = 10;
      max = Math.ceil(max / (interval * splitCount)) * interval * splitCount;
    }
    
    // 标准格式化函数：显示数据范围内的值
    const showFormatter = function(value: number) {
      return String(value);
    };
    
    return { max, interval, showFormatter };
  }
}

/**
 * 为功率和NPSH创建对齐的Y轴配置
 * @param powerMaxValue 功率最大值
 * @param npshMaxValue NPSH最大值
 * @returns 包含两个Y轴配置的数组
 */
export function createAlignedPowerNpshYAxis(powerMaxValue: number, npshMaxValue: number) {
  // 固定分割线数量为7条
  const splitNumber = 7;
  
  // 根据实际数据动态计算功率的最大值和步长
  let powerMax = 0;
  let powerInterval = 0;
  
  // 根据最大功率值确定合适的步长和最大值
  if (powerMaxValue <= 20) {
    powerInterval = 5;
    powerMax = 35;
  } else if (powerMaxValue <= 50) {
    powerInterval = 10;
    powerMax = 70;
  } else if (powerMaxValue <= 100) {
    powerInterval = 20;
    powerMax = 140;
  } else {
    powerInterval = Math.ceil(powerMaxValue / splitNumber);
    powerMax = powerInterval * splitNumber;
  }
  
  // 功率Y轴配置
  const powerConfig = {
    type: 'value',
    name: '功率 (kW)',
    min: 0,
    max: powerMax,
    interval: powerInterval,
    splitNumber: splitNumber,
    axisLine: {
      lineStyle: {
        color: '#333'
      }
    },
    axisLabel: {
      formatter: function(value: number) {
        return String(value);
      }
    }
  };
  
  // 根据NPSH最大值确定合适的步长和最大值
  let npshMax = 0;
  let npshInterval = 0;
  
  if (npshMaxValue <= 5) {
    npshInterval = 1;
    npshMax = 7;
  } else if (npshMaxValue <= 10) {
    npshInterval = 2;
    npshMax = 14;
  } else if (npshMaxValue <= 20) {
    npshInterval = 4;
    npshMax = 28;
  } else {
    npshInterval = Math.ceil(npshMaxValue / splitNumber);
    npshMax = npshInterval * splitNumber;
  }
  
  // NPSH Y轴配置
  const npshConfig = {
    type: 'value',
    name: 'NPSH (m)',
    min: 0,
    max: npshMax,
    interval: npshInterval,
    splitNumber: splitNumber, // 与功率轴保持一致的分割数量
    position: 'right',
    axisLine: {
      lineStyle: {
        color: '#333'
      }
    },
    axisLabel: {
      formatter: function(value: number) {
        return String(value);
      }
    }
  };
  
  return [powerConfig, npshConfig];
} 