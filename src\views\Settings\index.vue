<template>
  <div class="settings-system">
    <!-- 设置导航 -->
    <el-card class="nav-card">
      <div class="settings-nav">
        <el-menu
          v-model="activeTab"
          mode="horizontal"
          @select="handleTabSelect"
        >
          <el-menu-item index="general">
            <el-icon><Setting /></el-icon>
            <span>通用设置</span>
          </el-menu-item>
          <el-menu-item index="users">
            <el-icon><User /></el-icon>
            <span>用户管理</span>
          </el-menu-item>
          <el-menu-item index="roles">
            <el-icon><Lock /></el-icon>
            <span>角色权限</span>
          </el-menu-item>
          <el-menu-item index="notifications">
            <el-icon><Bell /></el-icon>
            <span>通知设置</span>
          </el-menu-item>
          <el-menu-item index="security">
            <el-icon><Key /></el-icon>
            <span>安全设置</span>
          </el-menu-item>
          <el-menu-item index="backup">
            <el-icon><FolderOpened /></el-icon>
            <span>备份恢复</span>
          </el-menu-item>
          <el-menu-item index="logs">
            <el-icon><Document /></el-icon>
            <span>系统日志</span>
          </el-menu-item>
          <el-menu-item index="system">
            <el-icon><Monitor /></el-icon>
            <span>系统信息</span>
          </el-menu-item>
        </el-menu>
      </div>
    </el-card>

    <!-- 设置内容 -->
    <div class="settings-content">
      <!-- 通用设置 -->
      <GeneralSettings v-if="activeTab === 'general'" />

      <!-- 用户管理 -->
      <UserManagement v-if="activeTab === 'users'" />

      <!-- 角色权限 -->
      <RoleManagement v-if="activeTab === 'roles'" />

      <!-- 通知设置 -->
      <NotificationSettings v-if="activeTab === 'notifications'" />

      <!-- 安全设置 -->
      <SecuritySettings v-if="activeTab === 'security'" />

      <!-- 备份恢复 -->
      <BackupRestore v-if="activeTab === 'backup'" />

      <!-- 系统日志 -->
      <SystemLogs v-if="activeTab === 'logs'" />

      <!-- 系统信息 -->
      <SystemInfo v-if="activeTab === 'system'" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  Setting,
  User,
  Lock,
  Bell,
  Key,
  FolderOpened,
  Document,
  Monitor
} from '@element-plus/icons-vue'
import { useSettingsStore } from '@/stores/settings'
import GeneralSettings from './GeneralSettings.vue'
import UserManagement from './UserManagement.vue'
import RoleManagement from './RoleManagement.vue'
import NotificationSettings from './NotificationSettings.vue'
import SecuritySettings from './SecuritySettings.vue'
import BackupRestore from './BackupRestore.vue'
import SystemLogs from './SystemLogs.vue'
import SystemInfo from './SystemInfo.vue'

// 响应式数据
const settingsStore = useSettingsStore()
const activeTab = ref('general')

// 方法
const handleTabSelect = (key: string) => {
  activeTab.value = key
}

// 生命周期
onMounted(() => {
  // 初始化设置数据
})
</script>

<style lang="scss" scoped>
.settings-system {
  .nav-card {
    margin-bottom: 20px;

    .settings-nav {
      :deep(.el-menu) {
        border-bottom: none;

        .el-menu-item {
          display: flex;
          align-items: center;
          gap: 8px;

          &.is-active {
            color: var(--el-color-primary);
            border-bottom-color: var(--el-color-primary);
          }
        }
      }
    }
  }

  .settings-content {
    min-height: 600px;
  }
}
</style>
