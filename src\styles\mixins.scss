@use './variables.scss' as *;

// 响应式断点混合器
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{$breakpoint-xs - 1px}) {
      @content;
    }
  }
  @else if $breakpoint == sm {
    @media (min-width: #{$breakpoint-xs}) and (max-width: #{$breakpoint-sm - 1px}) {
      @content;
    }
  }
  @else if $breakpoint == md {
    @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-md - 1px}) {
      @content;
    }
  }
  @else if $breakpoint == lg {
    @media (min-width: #{$breakpoint-md}) and (max-width: #{$breakpoint-lg - 1px}) {
      @content;
    }
  }
  @else if $breakpoint == xl {
    @media (min-width: #{$breakpoint-lg}) {
      @content;
    }
  }
  @else if $breakpoint == mobile {
    @media (max-width: #{$breakpoint-sm - 1px}) {
      @content;
    }
  }
  @else if $breakpoint == tablet {
    @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-md - 1px}) {
      @content;
    }
  }
  @else if $breakpoint == desktop {
    @media (min-width: #{$breakpoint-md}) {
      @content;
    }
  }
}

// 最小宽度断点
@mixin min-width($breakpoint) {
  @if $breakpoint == xs {
    @media (min-width: #{$breakpoint-xs}) {
      @content;
    }
  }
  @else if $breakpoint == sm {
    @media (min-width: #{$breakpoint-sm}) {
      @content;
    }
  }
  @else if $breakpoint == md {
    @media (min-width: #{$breakpoint-md}) {
      @content;
    }
  }
  @else if $breakpoint == lg {
    @media (min-width: #{$breakpoint-lg}) {
      @content;
    }
  }
  @else if $breakpoint == xl {
    @media (min-width: #{$breakpoint-xl}) {
      @content;
    }
  }
}

// 最大宽度断点
@mixin max-width($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{$breakpoint-xs - 1px}) {
      @content;
    }
  }
  @else if $breakpoint == sm {
    @media (max-width: #{$breakpoint-sm - 1px}) {
      @content;
    }
  }
  @else if $breakpoint == md {
    @media (max-width: #{$breakpoint-md - 1px}) {
      @content;
    }
  }
  @else if $breakpoint == lg {
    @media (max-width: #{$breakpoint-lg - 1px}) {
      @content;
    }
  }
  @else if $breakpoint == xl {
    @media (max-width: #{$breakpoint-xl - 1px}) {
      @content;
    }
  }
}

// 弹性布局混合器
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

@mixin flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

// 文本省略混合器
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 绝对居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 响应式字体大小
@mixin responsive-font-size($desktop-size, $mobile-size: null) {
  font-size: $desktop-size;
  
  @if $mobile-size {
    @include respond-to(mobile) {
      font-size: $mobile-size;
    }
  } @else {
    @include respond-to(mobile) {
      font-size: $desktop-size * 0.875; // 默认移动端字体缩小12.5%
    }
  }
}

// 响应式间距
@mixin responsive-spacing($property, $desktop-value, $mobile-value: null) {
  #{$property}: $desktop-value;
  
  @if $mobile-value {
    @include respond-to(mobile) {
      #{$property}: $mobile-value;
    }
  } @else {
    @include respond-to(mobile) {
      #{$property}: $desktop-value * 0.75; // 默认移动端间距缩小25%
    }
  }
}

// 触摸友好的按钮
@mixin touch-friendly {
  min-height: 44px; // iOS推荐的最小触摸目标
  min-width: 44px;
  
  @include respond-to(mobile) {
    padding: $spacing-mobile-md $spacing-mobile-lg;
  }
}

// 卡片样式
@mixin card-style {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: $border-radius-md;
  box-shadow: $box-shadow-light;
  
  @include respond-to(mobile) {
    border-radius: $border-radius-sm;
    margin: $spacing-mobile-sm;
  }
}

// 滚动条样式
@mixin custom-scrollbar($width: 8px) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }

  &::-webkit-scrollbar-track {
    background: var(--el-bg-color-page);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-darker);
    border-radius: $width / 2;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--el-border-color-dark);
  }
  
  // 移动端隐藏滚动条
  @include respond-to(mobile) {
    scrollbar-width: none;
    -ms-overflow-style: none;
    
    &::-webkit-scrollbar {
      display: none;
    }
  }
}
