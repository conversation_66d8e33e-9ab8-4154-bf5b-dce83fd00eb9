<template>
  <div class="data-section">
    <div class="section-header">
      <h4>{{ title }}</h4>
      <div class="section-actions">
        <el-tooltip :content="`添加${title}`" placement="top">
          <button class="btn-small btn-add" @click="$emit('add-point')">
            <i class="icon-plus"></i>
            添加数据点
          </button>
        </el-tooltip>
        <el-tooltip :content="`清空所有${title}`" placement="top">
          <button class="btn-small btn-danger" @click="$emit('clear-points')" v-if="data.length > 0">
            <i class="icon-trash"></i>
            清空
          </button>
        </el-tooltip>
        <div class="data-stats">
          <span class="data-count">共 {{ data.length }} 个数据点</span>
          <el-progress 
            :percentage="Math.min(data.length * (title.includes('NPSH') ? 20 : 10), 100)" 
            :stroke-width="8" 
            :show-text="false"
            :color="data.length >= (title.includes('NPSH') ? 3 : 5) ? '#67c23a' : '#e6a23c'"
          ></el-progress>
        </div>
      </div>
    </div>

    <div class="table-container">
      <el-table
        :data="data"
        border
        stripe
        size="small"
        highlight-current-row
        :max-height="title.includes('NPSH') ? 300 : 400"
        :header-cell-style="{background:'#f0f6ff', color:'#333', fontWeight:'600'}"
      >
        <el-table-column type="index" width="60" align="center" label="#" />
        <el-table-column 
          v-for="column in columns" 
          :key="column.prop" 
          :prop="column.prop" 
          :label="column.label" 
          :width="column.width"
        >
          <template #default="{row, $index}">
            <el-input-number 
              v-model="row[column.prop]" 
              :min="column.min" 
              :max="column.max" 
              :precision="column.precision" 
              :step="column.step" 
              size="small"
              @change="$emit('update-data')"
              controls-position="right"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template #default="{$index}">
            <el-button 
              type="danger" 
              size="small" 
              circle
              @click="$emit('remove-point', $index)"
              icon="Delete"
            />
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  title: string
  data: any[]
  columns: {
    prop: string
    label: string
    width?: number | string
    min?: number
    max?: number
    precision?: number
    step?: number
  }[]
}>()

defineEmits<{
  (e: 'add-point'): void
  (e: 'remove-point', index: number): void
  (e: 'clear-points'): void
  (e: 'update-data'): void
}>()
</script>

<style lang="scss" scoped>
.data-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.08);
  border: 1px solid rgba(255,255,255,0.2);
  backdrop-filter: blur(10px);

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f8f9fa;

    h4 {
      margin: 0;
      color: #2c3e50;
      font-size: 20px;
      font-weight: 600;
    }

    .section-actions {
      display: flex;
      align-items: center;
      gap: 16px;

      .btn-small {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 16px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 13px;
        font-weight: 500;
        transition: all 0.2s ease;

        &.btn-add {
          background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
          color: #2c3e50;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(132, 250, 176, 0.3);
          }
        }

        &.btn-danger {
          background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
          color: #2c3e50;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 154, 158, 0.3);
          }
        }
      }

      .data-stats {
        display: flex;
        flex-direction: column;
        gap: 6px;
        min-width: 120px;
        
        .data-count {
          color: #6c757d;
          font-size: 13px;
          font-weight: 500;
        }
      }
    }
  }

  .table-container {
    overflow-x: auto;
    border-radius: 12px;
  }
}
</style> 