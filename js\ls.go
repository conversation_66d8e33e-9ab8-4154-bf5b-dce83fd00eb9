package main

import (
	"fmt"
	"math"
)

func LeastSquares(x []float64, y []float64, m int) (a float64, b float64) {
	/*** 多项式最小二乘 ***/

	if len(x) != len(y) {
		fmt.Println("最小二乘时，两数组长度不一致!")
	} else {

		var ATA []float64 // A^T A
		var ATy []float64 // A^T y

		// \sum (x_i)^m_
		for m_ := 0; m_ < 2*m+1; m_++ {
			var sum float64
			for _, j := range x {
				sum += math.Pow(j, float64(m_))
			}
			ATA = append(ATA, sum)
		}

		// \sum (x_i)^m_ * y[i]
		for m_ := 0; m_ < m+1; m_++ {
			var sum float64
			for i, j := range x {
				sum += math.Pow(j, float64(m_)) * y[i]
			}
			ATy = append(ATy, sum)
		}

		fmt.Println("系数数组 ", ATA)
		fmt.Println("偏置 ", ATy)

		// 创建并赋值a
		a := make([][]float64, m+1)
		for i := range a {
			a[i] = ATA[i : (m+1)+i]
		}
		fmt.Println("系数矩阵 ", a)

		gaussSeidel(a, ATy, m)

	}
	return
}

func gaussSeidel(a [][]float64, b []float64, m int) (r []float64) {
	/*** 高斯-赛德尔迭代 求方程组的近似解***/

	// 创建并初始化参数x
	x := make([]float64, m+1)
	// 用于收敛比较
	xCopy := make([]float64, m+1)

	// 迭代，迭代次数随m增加而增加
	for k := 1; k < (m-1)*1000; k++ {

		// 存储第 K 次迭代的解
		copy(xCopy, x)

		// 解
		for i := 0; i < m+1; i++ {
			// 非对角线加权求和
			var sumk1 float64
			for j := 0; j < m+1; j++ {
				if j == i {
					continue
				}
				sumk1 += a[i][j] * x[j]
			}

			// 第 i 个解(参数)
			// x[i] = (b[i] - sumk1) / a[i][i]
			x[i] = (1-1.7)*x[i] + 1.7/a[i][i]*(b[i]-sumk1)
		}

		// 判断是否收敛（ 如果有m+1个误差小于1e-8，则终止迭代）
		sum := 0
		for i := 0; i < m+1; i++ {
			if x[i]-xCopy[i] < 1e-8 {
				sum += 1
			}
		}

		if sum == m+1 {
			fmt.Printf("滴滴滴！共迭代了 %v 次\n", k)
			break
		}
	}
	fmt.Println("参数 ", x)
	return x
}

func main() {
	LeastSquares([]float64{1, 2, 3, 4, 6, 7, 8}, []float64{2, 3, 6, 7, 5, 3, 2}, 2)
}
