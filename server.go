package main

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/exec"
	"os/signal"
	"path/filepath"
	"runtime"
	"strings"
	"syscall"
	"time"
)

// 服务器配置
type ServerConfig struct {
	Port     string
	DistDir  string
	Host     string
	AutoOpen bool
}

// 默认配置
func getDefaultConfig() *ServerConfig {
	return &ServerConfig{
		Port:     "8080",
		DistDir:  "./dist",
		Host:     "0.0.0.0",
		AutoOpen: true,
	}
}

// 获取本机IP地址
func getLocalIP() string {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return "localhost"
	}

	for _, addr := range addrs {
		if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				return ipnet.IP.String()
			}
		}
	}
	return "localhost"
}

// 检查端口是否可用
func isPortAvailable(port string) bool {
	ln, err := net.Listen("tcp", ":"+port)
	if err != nil {
		return false
	}
	ln.Close()
	return true
}

// 找到可用端口
func findAvailablePort(startPort string) string {
	port := startPort
	for i := 0; i < 100; i++ {
		if isPortAvailable(port) {
			return port
		}
		// 端口号递增
		portNum := 8080 + i + 1
		port = fmt.Sprintf("%d", portNum)
	}
	return startPort
}

// 打开浏览器的函数
func openBrowser(url string) {
	var err error

	switch runtime.GOOS {
	case "linux":
		err = exec.Command("xdg-open", url).Start()
	case "windows":
		err = exec.Command("rundll32", "url.dll,FileProtocolHandler", url).Start()
	case "darwin":
		err = exec.Command("open", url).Start()
	default:
		err = fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}

	if err != nil {
		fmt.Printf("⚠️ 无法自动打开浏览器: %v\n", err)
		fmt.Printf("💡 请手动访问: %s\n", url)
	} else {
		fmt.Printf("🌐 正在打开浏览器...\n")
	}
}

// 创建HTTP处理器
func createHandler(distDir string) http.HandlerFunc {
	fs := http.FileServer(http.Dir(distDir))
	
	return func(w http.ResponseWriter, r *http.Request) {
		// 设置CORS头部，避免跨域问题
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		// 处理OPTIONS预检请求
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}
		
		// 记录访问日志
		fmt.Printf("📝 %s %s %s\n", time.Now().Format("15:04:05"), r.Method, r.URL.Path)
		
		// 检查请求的文件是否存在
		path := filepath.Join(distDir, r.URL.Path)
		if _, err := os.Stat(path); os.IsNotExist(err) && r.URL.Path != "/" {
			// 如果文件不存在且不是根路径，返回index.html（支持SPA路由）
			http.ServeFile(w, r, filepath.Join(distDir, "index.html"))
			return
		}
		
		// 如果是根路径或文件存在，直接服务
		if r.URL.Path == "/" {
			http.ServeFile(w, r, filepath.Join(distDir, "index.html"))
			return
		}
		
		// 服务静态文件
		fs.ServeHTTP(w, r)
	}
}

// 优雅关闭服务器
func gracefulShutdown(server *http.Server) {
	// 创建一个接收系统信号的通道
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	
	// 等待信号
	<-quit
	fmt.Printf("\n🛑 正在关闭服务器...\n")
	
	// 创建一个5秒的超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	// 优雅关闭服务器
	if err := server.Shutdown(ctx); err != nil {
		fmt.Printf("❌ 服务器关闭失败: %v\n", err)
	} else {
		fmt.Printf("✅ 服务器已安全关闭\n")
	}
}

// 打印启动信息
func printStartupInfo(config *ServerConfig, localIP string) {
	fmt.Printf("\n" + strings.Repeat("=", 60) + "\n")
	fmt.Printf("🚀 智能供水平台服务器启动成功！\n")
	fmt.Printf(strings.Repeat("=", 60) + "\n")
	fmt.Printf("📱 本地访问地址: http://localhost:%s\n", config.Port)
	fmt.Printf("🌐 网络访问地址: http://%s:%s\n", localIP, config.Port)
	fmt.Printf("📁 静态文件目录: %s\n", config.DistDir)
	fmt.Printf("🖥️  运行环境: %s/%s\n", runtime.GOOS, runtime.GOARCH)
	fmt.Printf("⏰ 启动时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Printf(strings.Repeat("-", 60) + "\n")
	fmt.Printf("💡 提示:\n")
	fmt.Printf("   • 按 Ctrl+C 优雅停止服务器\n")
	fmt.Printf("   • 访问日志将显示在下方\n")
	fmt.Printf(strings.Repeat("=", 60) + "\n\n")
}

func main() {
	// 获取默认配置
	config := getDefaultConfig()
	
	// 检查dist目录是否存在
	if _, err := os.Stat(config.DistDir); os.IsNotExist(err) {
		fmt.Printf("❌ 错误: %s 目录不存在\n", config.DistDir)
		fmt.Printf("💡 请先运行以下命令进行打包:\n")
		fmt.Printf("   pnpm install\n")
		fmt.Printf("   pnpm run build\n")
		os.Exit(1)
	}
	
	// 检查端口是否可用，如果不可用则寻找其他端口
	if !isPortAvailable(config.Port) {
		fmt.Printf("⚠️ 端口 %s 已被占用，正在寻找可用端口...\n", config.Port)
		config.Port = findAvailablePort(config.Port)
		fmt.Printf("✅ 找到可用端口: %s\n", config.Port)
	}
	
	// 获取本机IP
	localIP := getLocalIP()
	
	// 创建HTTP服务器
	server := &http.Server{
		Addr:         ":" + config.Port,
		Handler:      createHandler(config.DistDir),
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}
	
	// 在单独的goroutine中启动服务器
	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("❌ 服务器启动失败: %v", err)
		}
	}()
	
	// 等待服务器启动
	time.Sleep(1 * time.Second)
	
	// 打印启动信息
	printStartupInfo(config, localIP)
	
	// 自动打开浏览器
	if config.AutoOpen {
		serverURL := fmt.Sprintf("http://localhost:%s", config.Port)
		go func() {
			time.Sleep(500 * time.Millisecond) // 稍微延迟确保服务器完全启动
			openBrowser(serverURL)
		}()
	}
	
	// 优雅关闭处理
	gracefulShutdown(server)
}