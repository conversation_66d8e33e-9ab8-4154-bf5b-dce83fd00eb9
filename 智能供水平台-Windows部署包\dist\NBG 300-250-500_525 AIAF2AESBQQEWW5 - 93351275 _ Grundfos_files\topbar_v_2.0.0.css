/* Top bar SCSS
------------------------- */
#globalTopBar .grundfos-logo .grundfos-logo-helper {
  height: 100%;
  display: inline-block;
  vertical-align: middle; }

/*
#globalTopBar .topbarDropdown {
    background-color: #fff;
    //border: 1px solid #aaa;
    border-top: none;
    box-sizing: border-box;
    color: #333;
    display: none;
    padding: 0;
    position: absolute;
    //width: 340px;
    right: 0;
    top: 80px;
    transition: opacity 0.3s;
    -webkit-transition: opacity 0.3s;
    z-index: 3;
    zoom: 1;
    //font-size: 13px;

	@media screen and (min-width: 768px) {
		top: 46px;
		right: -61px;
	}
}
*/
#globalTopBar .services a b {
  display: block; }

/*
#globalTopBar .services-icon {
    background: transparent url(../images/topbarSprite.png) no-repeat 0px 2px;
}
#globalTopBar .user-icon {
    background: transparent url(../images/topbarSprite.png) no-repeat -21px 1px;
}
#globalTopBar .shop-icon {
    background: transparent url(../images/topbarSprite.png) no-repeat -105px 1px;
}

*/
a.atLink span {
  color: #ABBFD0; }

a.atLink:hover span {
  color: #FFF; }

.atContainerLink {
  margin-left: 42px !important; }
  .atContainerLink a.atLink {
    padding-bottom: 3px; }

/* Settings
------------------------- */
/* stylelint-disable declaration-no-important */
/* stylelint-disable selector-max-compound-selectors, selector-max-universal */
/* stylelint-enable */
/* stylelint-enable */
/* Base
------------------------- */
/* Base - Forms
------------------------- */
fieldset {
  border: 0;
  margin: 0;
  padding: 0; }

button,
label {
  cursor: pointer; }

button {
  border-radius: 0; }

input,
textarea {
  border-radius: 0; }

/* Base - Typography
------------------------- */
.b-type--xsmall {
  font-size: 12px;
  line-height: 1.5; }
  @media only screen and (min-width: 768px) {
    .b-type--xsmall {
      font-size: 12px;
      line-height: 1.5; } }
  @media only screen and (min-width: 1024px) {
    .b-type--xsmall {
      font-size: 12px;
      line-height: 1.35; } }

.b-type--small {
  font-size: 14px;
  line-height: 1.42857; }
  @media only screen and (min-width: 768px) {
    .b-type--small {
      font-size: 14px;
      line-height: 1.5; } }
  @media only screen and (min-width: 1024px) {
    .b-type--small {
      font-size: 14px;
      line-height: 1.5; } }

.b-type--medium {
  font-size: 16px;
  line-height: 1.375; }
  @media only screen and (min-width: 768px) {
    .b-type--medium {
      font-size: 16px;
      line-height: 1.375; } }
  @media only screen and (min-width: 1024px) {
    .b-type--medium {
      font-size: 16px;
      line-height: 1.5; } }

.b-type--large {
  font-size: 18px;
  line-height: 1.33333; }
  @media only screen and (min-width: 768px) {
    .b-type--large {
      font-size: 18px;
      line-height: 1.5; } }
  @media only screen and (min-width: 1024px) {
    .b-type--large {
      font-size: 18px;
      line-height: 1.35; } }

.b-type--xlarge {
  font-size: 18px;
  line-height: 1.33333; }
  @media only screen and (min-width: 768px) {
    .b-type--xlarge {
      font-size: 20px;
      line-height: 1.35; } }
  @media only screen and (min-width: 1024px) {
    .b-type--xlarge {
      font-size: 20px;
      line-height: 1.35; } }

.b-type--xxlarge {
  font-size: 21px;
  line-height: 1.2381; }
  @media only screen and (min-width: 768px) {
    .b-type--xxlarge {
      font-size: 22px;
      line-height: 1.27273; } }
  @media only screen and (min-width: 1024px) {
    .b-type--xxlarge {
      font-size: 23px;
      line-height: 1.30435; } }

.b-type--xxxlarge {
  font-size: 24px;
  line-height: 1.20833; }
  @media only screen and (min-width: 768px) {
    .b-type--xxxlarge {
      font-size: 27px;
      line-height: 1.25926; } }
  @media only screen and (min-width: 1024px) {
    .b-type--xxxlarge {
      font-size: 30px;
      line-height: 1.3; } }

.b-type--xxxxlarge {
  font-size: 30px;
  line-height: 1.2; }
  @media only screen and (min-width: 768px) {
    .b-type--xxxxlarge {
      font-size: 35px;
      line-height: 1.2; } }
  @media only screen and (min-width: 1024px) {
    .b-type--xxxxlarge {
      font-size: 40px;
      line-height: 1.2; } }

.b-type--xxxxxlarge {
  font-size: 40px;
  line-height: 1.15; }
  @media only screen and (min-width: 768px) {
    .b-type--xxxxxlarge {
      font-size: 50px;
      line-height: 1.16; } }
  @media only screen and (min-width: 1024px) {
    .b-type--xxxxxlarge {
      font-size: 60px;
      line-height: 1.16667; } }

.topbar, .topbar-overlay-container {
  font-family: "Grundfos", Lucida Grande, Tahoma, Geneva, Verdana, sans-serif;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 0.1px; }
  .topbar input, .topbar textarea, .topbar select, .topbar button, .topbar-overlay-container input, .topbar-overlay-container textarea, .topbar-overlay-container select, .topbar-overlay-container button {
    font-family: "Grundfos", Lucida Grande, Tahoma, Geneva, Verdana, sans-serif; }

/* Resources
------------------------- */
/* Resources - Typography
------------------------- */
/* Elements
------------------------- */
.topbar-elm-button {
  background-color: #11497B;
  border: 1px solid transparent;
  border-radius: 30px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  color: #FFFFFF;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  letter-spacing: 0.025em;
  min-width: 180px;
  padding: 6px;
  position: relative;
  text-decoration: none;
  height: 40px;
  width: 100%;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 135%;
  /* 18.9px */
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  -webkit-font-smoothing: antialiased; }
  .topbar-elm-button::after {
    content: attr(title);
    -ms-flex-line-pack: center;
        align-content: center; }
  .topbar-elm-button--has-setting.topbar-elm-button::before, .topbar-elm-button--has-return.topbar-elm-button::before, .topbar-elm-button--has-setting.topbar-elm-button:hover:before, .topbar-elm-button--has-return.topbar-elm-button:hover:before {
    content: '';
    background-position: center center;
    width: 16px;
    background-repeat: no-repeat;
    margin-right: 12px; }
  .topbar-elm-button--has-setting.topbar-elm-button::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='17' height='16' viewBox='0 0 17 16' fill='none'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M9.75 5.83492C8.87432 5.32931 7.78029 5.40099 6.97807 6.01652C6.17584 6.63206 5.8234 7.67025 6.0851 8.64696C6.3468 9.62368 7.17109 10.3466 8.1736 10.4786C9.17612 10.6105 10.1594 10.1256 10.665 9.24992C11.3553 8.05422 10.9457 6.5253 9.75 5.83492ZM9.795 8.74992C9.37952 9.46348 8.46548 9.70708 7.75 9.29492C7.40592 9.09915 7.15535 8.77287 7.055 8.38992C6.95159 8.00509 7.00556 7.59491 7.205 7.24992C7.62048 6.53637 8.53452 6.29277 9.25 6.70492C9.96355 7.1204 10.2071 8.03444 9.795 8.74992Z' fill='%23126AF3'/%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M13.56 7.99992C13.5205 7.64432 13.6978 7.29962 14.01 7.12492L15.315 6.37492L15.2 5.99992C15.0466 5.47641 14.8318 4.97291 14.56 4.49992C14.2882 4.02915 13.9608 3.5927 13.585 3.19992L13.315 2.90992L12.025 3.65492C11.716 3.84948 11.3196 3.83561 11.025 3.61992C10.7012 3.47153 10.4953 3.14611 10.5 2.78992V1.28992L10.115 1.19992C9.05284 0.94996 7.94716 0.94996 6.885 1.19992L6.5 1.27992V2.77992C6.51121 3.14305 6.30179 3.47693 5.97 3.62492C5.67749 3.84372 5.28098 3.86156 4.97 3.66992L3.685 2.90992L3.41 3.19492C2.66395 3.99307 2.11289 4.95316 1.8 5.99992L1.685 6.37992L3 7.11992C3.31331 7.29521 3.48775 7.6441 3.44 7.99992C3.48068 8.35185 3.30758 8.69413 3 8.86992L1.685 9.62492L1.8 9.99992C2.10618 11.0492 2.66007 12.0095 3.415 12.7999L3.685 13.0899L5 12.3399C5.30959 12.1454 5.70693 12.1613 6 12.3799C6.31497 12.5346 6.51046 12.8592 6.5 13.2099V14.7099L6.885 14.8049C7.94716 15.0549 9.05284 15.0549 10.115 14.8049L10.5 14.7099V13.2099C10.4881 12.8478 10.6981 12.5151 11.03 12.3699C11.3225 12.1511 11.719 12.1333 12.03 12.3249L13.335 13.0799L13.61 12.7949C14.3475 11.9976 14.8916 11.0413 15.2 9.99992L15.315 9.61992L14.025 8.87492C13.7049 8.70653 13.5205 8.35943 13.56 7.99992ZM13.12 11.8199L12.5 11.4649C11.8784 11.0985 11.1025 11.1179 10.5 11.5149C9.8756 11.8481 9.48968 12.5023 9.5 13.2099V13.9149C8.83766 14.0224 8.16234 14.0224 7.5 13.9149V13.1999C7.5076 12.4836 7.10848 11.8248 6.47 11.4999C5.8662 11.1056 5.09092 11.0881 4.47 11.4549L3.865 11.8049C3.65594 11.5529 3.46861 11.2836 3.305 10.9999C3.1384 10.7089 2.99624 10.4045 2.88 10.0899L3.5 9.73492C4.11433 9.37905 4.47741 8.70891 4.44 7.99992C4.48169 7.28866 4.11773 6.61495 3.5 6.25992L2.9 5.90992C3.13265 5.27912 3.4708 4.69244 3.9 4.17492L4.5 4.52992C5.10943 4.89354 5.87192 4.88193 6.47 4.49992C7.1109 4.17252 7.51024 3.50954 7.5 2.78992V2.08492C8.16192 1.97246 8.83807 1.97246 9.5 2.08492V2.79492C9.49167 3.51275 9.89073 4.17332 10.53 4.49992C11.1338 4.89426 11.9091 4.91171 12.53 4.54492L13.135 4.19492C13.5667 4.70881 13.9052 5.29431 14.135 5.92492L13.5 6.26492C12.8848 6.61996 12.5214 7.29068 12.56 7.99992C12.5183 8.71119 12.8823 9.3849 13.5 9.73992L14.1 10.0899C13.8745 10.7183 13.5431 11.3035 13.12 11.8199Z' fill='%23126AF3'/%3E%3C/svg%3E"); }
  .topbar-elm-button--has-setting.topbar-elm-button:hover:before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='17' height='16' viewBox='0 0 17 16' fill='none'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M9.75 5.83492C8.87432 5.32931 7.78029 5.40099 6.97807 6.01652C6.17584 6.63206 5.8234 7.67025 6.0851 8.64696C6.3468 9.62368 7.17109 10.3466 8.1736 10.4786C9.17612 10.6105 10.1594 10.1256 10.665 9.24992C11.3553 8.05422 10.9457 6.5253 9.75 5.83492ZM9.795 8.74992C9.37952 9.46348 8.46548 9.70708 7.75 9.29492C7.40592 9.09915 7.15535 8.77287 7.055 8.38992C6.95159 8.00509 7.00556 7.59491 7.205 7.24992C7.62048 6.53637 8.53452 6.29277 9.25 6.70492C9.96355 7.1204 10.2071 8.03444 9.795 8.74992Z' fill='%23FFF'/%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M13.56 7.99992C13.5205 7.64432 13.6978 7.29962 14.01 7.12492L15.315 6.37492L15.2 5.99992C15.0466 5.47641 14.8318 4.97291 14.56 4.49992C14.2882 4.02915 13.9608 3.5927 13.585 3.19992L13.315 2.90992L12.025 3.65492C11.716 3.84948 11.3196 3.83561 11.025 3.61992C10.7012 3.47153 10.4953 3.14611 10.5 2.78992V1.28992L10.115 1.19992C9.05284 0.94996 7.94716 0.94996 6.885 1.19992L6.5 1.27992V2.77992C6.51121 3.14305 6.30179 3.47693 5.97 3.62492C5.67749 3.84372 5.28098 3.86156 4.97 3.66992L3.685 2.90992L3.41 3.19492C2.66395 3.99307 2.11289 4.95316 1.8 5.99992L1.685 6.37992L3 7.11992C3.31331 7.29521 3.48775 7.6441 3.44 7.99992C3.48068 8.35185 3.30758 8.69413 3 8.86992L1.685 9.62492L1.8 9.99992C2.10618 11.0492 2.66007 12.0095 3.415 12.7999L3.685 13.0899L5 12.3399C5.30959 12.1454 5.70693 12.1613 6 12.3799C6.31497 12.5346 6.51046 12.8592 6.5 13.2099V14.7099L6.885 14.8049C7.94716 15.0549 9.05284 15.0549 10.115 14.8049L10.5 14.7099V13.2099C10.4881 12.8478 10.6981 12.5151 11.03 12.3699C11.3225 12.1511 11.719 12.1333 12.03 12.3249L13.335 13.0799L13.61 12.7949C14.3475 11.9976 14.8916 11.0413 15.2 9.99992L15.315 9.61992L14.025 8.87492C13.7049 8.70653 13.5205 8.35943 13.56 7.99992ZM13.12 11.8199L12.5 11.4649C11.8784 11.0985 11.1025 11.1179 10.5 11.5149C9.8756 11.8481 9.48968 12.5023 9.5 13.2099V13.9149C8.83766 14.0224 8.16234 14.0224 7.5 13.9149V13.1999C7.5076 12.4836 7.10848 11.8248 6.47 11.4999C5.8662 11.1056 5.09092 11.0881 4.47 11.4549L3.865 11.8049C3.65594 11.5529 3.46861 11.2836 3.305 10.9999C3.1384 10.7089 2.99624 10.4045 2.88 10.0899L3.5 9.73492C4.11433 9.37905 4.47741 8.70891 4.44 7.99992C4.48169 7.28866 4.11773 6.61495 3.5 6.25992L2.9 5.90992C3.13265 5.27912 3.4708 4.69244 3.9 4.17492L4.5 4.52992C5.10943 4.89354 5.87192 4.88193 6.47 4.49992C7.1109 4.17252 7.51024 3.50954 7.5 2.78992V2.08492C8.16192 1.97246 8.83807 1.97246 9.5 2.08492V2.79492C9.49167 3.51275 9.89073 4.17332 10.53 4.49992C11.1338 4.89426 11.9091 4.91171 12.53 4.54492L13.135 4.19492C13.5667 4.70881 13.9052 5.29431 14.135 5.92492L13.5 6.26492C12.8848 6.61996 12.5214 7.29068 12.56 7.99992C12.5183 8.71119 12.8823 9.3849 13.5 9.73992L14.1 10.0899C13.8745 10.7183 13.5431 11.3035 13.12 11.8199Z' fill='%23FFF'/%3E%3C/svg%3E"); }
  .topbar-elm-button--has-return.topbar-elm-button::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='17' height='16' viewBox='0 0 17 16' fill='none'%3E%3Cpath d='M3.5 13H13.5V11.5H14.5V14H2.5V2H14.5V4.5H13.5V3H3.5V13Z' fill='%23126AF3'/%3E%3Cpath d='M14.705 7.995L11.355 11.355L10.645 10.645L12.79 8.5H6V7.5H12.795L10.645 5.355L11.355 4.645L14.705 7.995Z' fill='%23126AF3'/%3E%3C/svg%3E"); }
  .topbar-elm-button--has-return.topbar-elm-button:hover:before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='17' height='16' viewBox='0 0 17 16' fill='none'%3E%3Cpath d='M3.5 13H13.5V11.5H14.5V14H2.5V2H14.5V4.5H13.5V3H3.5V13Z' fill='%23fff'/%3E%3Cpath d='M14.705 7.995L11.355 11.355L10.645 10.645L12.79 8.5H6V7.5H12.795L10.645 5.355L11.355 4.645L14.705 7.995Z' fill='%23fff'/%3E%3C/svg%3E"); }
  .topbar-elm-button:hover {
    animate: dissolve;
    background-color: #0B58D0;
    color: #FFFFFF;
    outline: 5px solid #0B58D0;
    outline-offset: -2px;
    -webkit-transition: 300ms ease-out;
    transition: 300ms ease-out;
    text-decoration: none; }
  .topbar-elm-button:focus-visible {
    outline: 2px solid #126AF3;
    outline-offset: 2px; }
  .topbar-elm-button:active {
    background-color: #0B58D0;
    color: #FFFFFF;
    outline: none;
    -webkit-transform: scale(0.96);
            transform: scale(0.96);
    text-decoration: none; }
  .topbar-elm-button--small {
    padding: 8px 15px;
    min-width: 0;
    font-size: 14px;
    line-height: 1.42857; }
    @media only screen and (min-width: 768px) {
      .topbar-elm-button--small {
        font-size: 14px;
        line-height: 1.5; } }
    @media only screen and (min-width: 1024px) {
      .topbar-elm-button--small {
        font-size: 14px;
        line-height: 1.5; } }
  .topbar-elm-button--ghost {
    background: transparent;
    border: 2px solid #126AF3;
    color: #0C1217; }
  .topbar-elm-button--positive {
    background-color: #126AF3; }
  .topbar-elm-button--no-border {
    border-color: transparent; }

.topbar-elm-icon {
  background-repeat: no-repeat;
  display: inline-block;
  height: 50px;
  width: 40px;
  background-size: 18px;
  background-position: 50% 50%; }
  @media only screen and (min-width: 768px) {
    .topbar-elm-icon {
      background-size: 21px;
      height: 21px;
      width: 21px; } }
  .topbar-elm-icon--profile {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' fill='%23FFFFFF' stroke='none'%3E %3Cg id='OUTLINE'%3E %3Cpath d='M16,8c-1.87,0-5,.78-5,6s3.13,6,5,6c5,0,5-4.53,5-6C21,9,18.28,8,16,8Zm0,10c-1.29,0-3-.42-3-4s1.52-4,3-4,3,.48,3,4C19,17.35,17.9,18,16,18Z'/%3E %3Cpath d='M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2ZM8.52,25.37a21.07,21.07,0,0,1,15,0A12,12,0,0,1,8.52,25.37Zm16.53-1.52a23,23,0,0,0-18.1,0,12,12,0,1,1,18.1,0Z'/%3E %3C/g%3E %3C/svg%3E"); }
  .topbar-elm-icon--cart {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' fill='%23FFFFFF' stroke='none'%3E %3Cg id='OUTLINE'%3E %3Cpath d='M30.23,7H6.82L6.48,5.31a3.74,3.74,0,0,0-1.4-2.54A4.13,4.13,0,0,0,2.49,2H1V4H2.48a2.3,2.3,0,0,1,1.38.36c.25.19.46.35.66,1.34l3,15a3.74,3.74,0,0,0,1.4,2.54,4.13,4.13,0,0,0,2.59.77H27V22H11.52a2.3,2.3,0,0,1-1.38-.36c-.25-.19-.46-.35-.66-1.34L9,18H27.94Zm-3.92,9H8.62L7.22,9H27.77Z'/%3E %3Ccircle cx='11' cy='28' r='2' transform='translate(-4.34 2.12) rotate(-9.22)'/%3E %3Ccircle cx='25' cy='28' r='2'/%3E %3C/g%3E %3C/svg%3E");
    width: 45px; }
    @media only screen and (min-width: 768px) {
      .topbar-elm-icon--cart {
        width: 21px; } }
  .topbar-elm-icon--services {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' fill='%23FFFFFF' stroke='none'%3E %3Cg id='OUTLINE'%3E %3Ccircle cx='26' cy='6' r='2'/%3E %3Ccircle cx='16' cy='6' r='2'/%3E %3Ccircle cx='6' cy='6' r='2'/%3E %3Ccircle cx='26' cy='16' r='2'/%3E %3Ccircle cx='16' cy='16' r='2'/%3E %3Ccircle cx='6' cy='16' r='2'/%3E %3Ccircle cx='26' cy='26' r='2'/%3E %3Ccircle cx='16' cy='26' r='2'/%3E %3Ccircle cx='6' cy='26' r='2'/%3E %3C/g%3E %3C/svg%3E"); }

.topbar-cmp-cart__link--counter .topbar-cmp-cart__counter {
  left: 22px;
  top: 30px; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-cart__link--counter .topbar-cmp-cart__counter {
      left: 12px;
      top: 12px; } }

a.topbar-menu__list-item--link:hover {
  color: #FFF; }
  a.topbar-menu__list-item--link:hover .topbar-elm-icon--profile {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' fill='%23FFFFFF' stroke='none'%3E %3Cg id='OUTLINE'%3E %3Cpath d='M16,8c-1.87,0-5,.78-5,6s3.13,6,5,6c5,0,5-4.53,5-6C21,9,18.28,8,16,8Zm0,10c-1.29,0-3-.42-3-4s1.52-4,3-4,3,.48,3,4C19,17.35,17.9,18,16,18Z'/%3E %3Cpath d='M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2ZM8.52,25.37a21.07,21.07,0,0,1,15,0A12,12,0,0,1,8.52,25.37Zm16.53-1.52a23,23,0,0,0-18.1,0,12,12,0,1,1,18.1,0Z'/%3E %3C/g%3E %3C/svg%3E");
    outline: 6px solid rgba(255, 255, 255, 0.1);
    border-radius: 100%;
    background-color: rgba(255, 255, 255, 0.1); }
  a.topbar-menu__list-item--link:hover .topbar-elm-icon--cart {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' fill='%23FFFFFF' stroke='none'%3E %3Cg id='OUTLINE'%3E %3Cpath d='M30.23,7H6.82L6.48,5.31a3.74,3.74,0,0,0-1.4-2.54A4.13,4.13,0,0,0,2.49,2H1V4H2.48a2.3,2.3,0,0,1,1.38.36c.25.19.46.35.66,1.34l3,15a3.74,3.74,0,0,0,1.4,2.54,4.13,4.13,0,0,0,2.59.77H27V22H11.52a2.3,2.3,0,0,1-1.38-.36c-.25-.19-.46-.35-.66-1.34L9,18H27.94Zm-3.92,9H8.62L7.22,9H27.77Z'/%3E %3Ccircle cx='11' cy='28' r='2' transform='translate(-4.34 2.12) rotate(-9.22)'/%3E %3Ccircle cx='25' cy='28' r='2'/%3E %3C/g%3E %3C/svg%3E");
    outline: 6px solid rgba(255, 255, 255, 0.1);
    border-radius: 100%;
    background-color: rgba(255, 255, 255, 0.1); }
  a.topbar-menu__list-item--link:hover .topbar-elm-icon--services {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg id='Layer_1' data-name='Layer 1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' fill='%23FFFFFF' stroke='none'%3E %3Cg id='OUTLINE'%3E %3Ccircle cx='26' cy='6' r='2'/%3E %3Ccircle cx='16' cy='6' r='2'/%3E %3Ccircle cx='6' cy='6' r='2'/%3E %3Ccircle cx='26' cy='16' r='2'/%3E %3Ccircle cx='16' cy='16' r='2'/%3E %3Ccircle cx='6' cy='16' r='2'/%3E %3Ccircle cx='26' cy='26' r='2'/%3E %3Ccircle cx='16' cy='26' r='2'/%3E %3Ccircle cx='6' cy='26' r='2'/%3E %3C/g%3E %3C/svg%3E");
    outline: 6px solid rgba(255, 255, 255, 0.1);
    border-radius: 100%;
    background-color: rgba(255, 255, 255, 0.1); }

a.topbar-menu__list-item--link:active .topbar-elm-icon {
  outline: 4px solid rgba(255, 255, 255, 0.2);
  border-radius: 100%;
  background-color: rgba(255, 255, 255, 0.2); }

a.topbar-menu__list-item--link:focus-visible .topbar-elm-icon {
  outline: 2px solid #47A6FF;
  outline-offset: 2px;
  border-radius: 100%; }

a.topbar-menu__list-item--link:focus-visible,
a.topbar-menu__list-item--link:focus {
  outline: none; }

.mod-header__mobile-nav {
  /* stylelint-disable media-feature-no-missing-punctuation */
  /* stylelint-enable */ }
  .mod-header__mobile-nav .mod-header__action--search {
    padding: 0; }
  @media only screen and (max-width: 767px) {
    .mod-header__mobile-nav {
      height: 3.75rem; } }

.topbar-elm-link {
  font-size: 16px;
  line-height: 1.375;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background: none;
  border: 0;
  color: #126AF3;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: inherit;
  padding: 0;
  text-decoration: underline;
  -webkit-transition: 0.3s ease color;
  transition: 0.3s ease color;
  text-decoration: underline;
  text-underline-offset: 4px;
  -webkit-text-decoration-skip: ink;
          text-decoration-skip-ink: auto; }
  @media only screen and (min-width: 768px) {
    .topbar-elm-link {
      font-size: 16px;
      line-height: 1.375; } }
  @media only screen and (min-width: 1024px) {
    .topbar-elm-link {
      font-size: 16px;
      line-height: 1.5; } }
  .topbar-elm-link:hover, .topbar-elm-link:focus, .topbar-elm-link:active {
    color: #009EE3;
    text-decoration: none; }
  .topbar-elm-link--block {
    text-decoration: none; }
  .topbar-elm-link--center {
    -ms-flex-item-align: center;
        align-self: center; }
  .topbar-elm-link:hover {
    text-decoration: underline;
    text-decoration-thickness: 1.5px; }
  .topbar-elm-link:hover, .topbar-elm-link:focus, .topbar-elm-link:active {
    color: #063175; }
  .topbar-elm-link:focus-visible {
    outline: 2px solid rgba(18, 106, 243, 0.5); }

/* Element - Square Button
------------------------- */
.topbar-elm-square-button {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background: #FFFFFF center center/16px no-repeat;
  border: 0;
  border-radius: 30px;
  color: #FFFFFF;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 24px;
  padding: 8px;
  position: relative;
  width: 24px;
  /* stylelint-disable max-nesting-depth */
  /* stylelint-enable */
  /* stylelint-disable max-nesting-depth */
  /* stylelint-enable */ }
  .topbar-elm-square-button::after {
    background-color: rgba(0, 0, 0, 0.1);
    content: "";
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    -webkit-transform: scaleX(0);
            transform: scaleX(0);
    -webkit-transform-origin: left center;
            transform-origin: left center;
    -webkit-transition: -webkit-transform 0.2s ease-out;
    transition: -webkit-transform 0.2s ease-out;
    transition: transform 0.2s ease-out;
    transition: transform 0.2s ease-out, -webkit-transform 0.2s ease-out; }
  .topbar-elm-square-button:hover::after, .topbar-elm-square-button:focus::after, .topbar-elm-square-button:active::after {
    -webkit-transform: scaleX(1);
            transform: scaleX(1); }
  .topbar-elm-square-button--disabled, .topbar-elm-square-button[disabled] {
    background-color: #999999;
    cursor: default; }
  .topbar-elm-square-button--medium {
    background-size: 24px;
    height: 54px;
    width: 54px; }
  .topbar-elm-square-button--large {
    background-size: 24px;
    height: 66px;
    width: 66px; }
  .topbar-elm-square-button--icon-close {
    background-image: url("data:image/svg+xml;charset=utf-8,%3C?xml version='1.0' encoding='utf-8'?%3E %3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' x='0px' y='0px' viewBox='0 0 128 128' fill='%23126AF3' stroke-width='4' stroke='none'%3E %3Cpolygon points='128,13.8 114.2,0 64,50.3 13.8,0 0,13.8 50.3,64 0,114.2 13.8,128 64,77.7 114.2,128 128,114.2 77.7,64 '/%3E %3C/svg%3E"); }
  .topbar-elm-square-button--icon-search {
    background-image: url("data:image/svg+xml;charset=utf-8,%3C?xml version='1.0' encoding='utf-8'?%3E %3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' x='0px' y='0px' viewBox='0 0 128 128' fill='%23126AF3' stroke='none'%3E %3Cpath d='M91.5,80.5h-5.8l-2-2c7.2-8.3,11.5-19.2,11.5-31C95.1,21.3,73.8,0,47.6,0C21.3,0,0,21.3,0,47.6s21.3,47.6,47.6,47.6 c11.8,0,22.6-4.3,31-11.5l2,2v5.8l36.6,36.5l10.9-10.9L91.5,80.5z M47.6,80.5c-18.2,0-32.9-14.7-32.9-32.9s14.7-32.9,32.9-32.9 s32.9,14.7,32.9,32.9S65.8,80.5,47.6,80.5z'/%3E %3C/svg%3E"); }

.topbar-elm-square-button__text {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px; }

/* Components
------------------------- */
@media only screen and (min-width: 768px) {
  .topbar-cmp-app-hub {
    right: -75px; } }

.topbar-cmp-app-hub__content-outer-wrap {
  overflow: hidden; }

.topbar-cmp-app-hub__content {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  /* Workaround for hidden scrollbar. The width will be 100% + 22px */
  height: auto;
  overflow-x: hidden;
  overflow-y: scroll;
  padding-right: 22px;
  /* Workaround for hidden scrollbar. Increase/decrease this value for cross-browser compatibility */
  width: calc(100% + 15px);
  /* Workaround for hidden scrollbar.*/ }

@-moz-document url-prefix() {
  .topbar-cmp-app-hub__content {
    width: calc(100% + 15px);
    /* Workaround for hidden scrollbar.*/ } }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-app-hub__content {
      max-height: calc(100vh - 110px); } }

.topbar-cmp-app-hub__content-inner-wrap {
  padding: 24px;
  padding-top: 48px;
  height: 100vh; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-app-hub__content-inner-wrap {
      padding: 24px; } }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-app-hub__content-inner-wrap {
      height: auto; } }

.topbar-cmp-app-hub__section:last-child .topbar-cmp-app-hub__list {
  padding-bottom: 0; }

.topbar-cmp-app-hub__list {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 16px; }

.topbar-cmp-app-hub__list-item-link {
  color: #0C1217;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: start;
  padding: 8px;
  position: relative;
  text-decoration: none; }
  .topbar-cmp-app-hub__list-item-link:hover {
    background-color: #F3F5F7; }
  .topbar-cmp-app-hub__list-item-link:focus-visible {
    outline: 2px solid; }

.topbar-cmp-app-hub__list-item-graphic {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 32px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin: 0 auto 8px;
  width: 32px; }

.topbar-cmp-app-hub__list-item-image {
  max-height: 32px;
  max-width: 32px; }

.topbar-cmp-app-hub__list-item-text {
  font-size: 12px;
  line-height: 1.5;
  text-align: center; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-app-hub__list-item-text {
      font-size: 12px;
      line-height: 1.5; } }
  @media only screen and (min-width: 1024px) {
    .topbar-cmp-app-hub__list-item-text {
      font-size: 12px;
      line-height: 1.35; } }

.topbar-elm-label--app-hub,
.topbar-elm-label--profile {
  color: #FFFFFF; }
  @media only screen and (min-width: 768px) {
    .topbar-elm-label--app-hub,
    .topbar-elm-label--profile {
      display: block;
      margin-top: 2px;
      padding-left: 8px; } }

@media only screen and (min-width: 768px) {
  .topbar-cmp-cart {
    right: -75px; } }

@media only screen and (min-width: 768px) {
  .topbar-cmp-cart .topbar-elm-button--go-to-cart {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px; } }

.topbar-cmp-cart--min-cart .topbar-elm-square-button--icon-close {
  /* stylelint-disable media-feature-no-missing-punctuation */
  /* stylelint-enable */ }
  @media only screen and (max-width: 767px) {
    .topbar-cmp-cart--min-cart .topbar-elm-square-button--icon-close {
      border: 0;
      clip: rect(0 0 0 0);
      height: 1px;
      margin: -1px;
      overflow: hidden;
      padding: 0;
      position: absolute;
      width: 1px; } }

.topbar-cmp-cart--min-cart .topbar-elm-button--check-out {
  /* stylelint-disable media-feature-no-missing-punctuation */
  /* stylelint-enable */ }
  @media only screen and (max-width: 767px) {
    .topbar-cmp-cart--min-cart .topbar-elm-button--check-out {
      border: 0;
      clip: rect(0 0 0 0);
      height: 1px;
      margin: -1px;
      overflow: hidden;
      padding: 0;
      position: absolute;
      width: 1px; } }

.topbar-cmp-cart--full-cart .topbar-elm-button--go-to-cart {
  display: none; }

.topbar-cmp-dropdown.topbar-cmp-cart--min-cart {
  /* stylelint-disable media-feature-no-missing-punctuation */
  /* stylelint-enable */ }
  @media only screen and (max-width: 767px) {
    .topbar-cmp-dropdown.topbar-cmp-cart--min-cart {
      z-index: 10000;
      bottom: 0;
      height: auto;
      position: fixed;
      top: unset;
      padding-top: 5px; }
      .topbar-cmp-dropdown.topbar-cmp-cart--min-cart .topbar-cmp-cart__summary {
        -webkit-box-shadow: none;
                box-shadow: none; } }

.topbar-cmp-cart__counter {
  font-size: 12px;
  line-height: 1.5;
  background-color: #126AF3;
  color: #FFFFFF;
  line-height: 1.35;
  padding: 2px 0 0 1px;
  position: absolute;
  text-decoration: none;
  left: 22px;
  top: 7px;
  border-radius: 30px;
  width: 19px;
  height: 19px;
  text-align: center; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-cart__counter {
      font-size: 12px;
      line-height: 1.5; } }
  @media only screen and (min-width: 1024px) {
    .topbar-cmp-cart__counter {
      font-size: 12px;
      line-height: 1.35; } }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-cart__counter {
      line-height: 1.35;
      left: 30px; } }
  .topbar-cmp-cart__counter--simulation {
    border: 2px solid #11497b;
    border-radius: 50%;
    color: transparent;
    border-top: 2px solid #fff;
    border-bottom: 2px solid #fff;
    width: 15px;
    height: 15px;
    -webkit-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
    background-color: transparent; }

/* Safari */
@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(360deg); } }

@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg); } }

.topbar-cmp-cart__error--loading {
  color: #11497B; }

.topbar-cmp-cart__notification {
  padding: 24px;
  padding-top: 48px;
  border-bottom: solid 1px #BFC8CF;
  display: none;
  height: auto;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  min-height: 34px; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-cart__notification {
      padding: 24px; } }
  .topbar-cmp-cart__notification.active {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex; }
  .topbar-cmp-cart--full-cart .topbar-cmp-cart__notification {
    /* stylelint-disable media-feature-no-missing-punctuation */
    /* stylelint-enable */ }
    @media only screen and (max-width: 767px) {
      .topbar-cmp-cart--full-cart .topbar-cmp-cart__notification {
        border: 0;
        clip: rect(0 0 0 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px; } }

.topbar-cmp-cart__notification-message {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: #11497B;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 0; }
  .topbar-cmp-cart__notification-message::before {
    background: #4AA22C center center;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 33.5 33.5' fill='%23FFFFFF' stroke='none'%3E%3Cdefs%3E%3Cstyle%3E.b{fill:rgba(0,0,0,0);}.c{fill:none;}.d{fill:%23fff;}%3C/style%3E%3C/defs%3E %3Cg transform='translate(-140 -255)'%3E %3Cg transform='translate(142.544 256.696)'%3E %3Crect class='b' width='29.177' height='29.177'/%3E %3Cg transform='translate(1.824 1.824)'%3E %3Crect class='c' width='25.53' height='25.53'/%3E %3Cg transform='translate(14.699 0.359) rotate(45)'%3E %3Crect class='d' width='3.647' height='16.412' transform='translate(7.294)'/%3E %3Crect class='d' width='10.942' height='3.647' transform='translate(0 12.765)'/%3E %3C/g%3E %3C/g%3E %3C/g%3E %3C/g%3E %3C/svg%3E");
    border-radius: 100px;
    content: '';
    display: inline-block;
    height: 34px;
    margin-right: 10px;
    width: 34px; }

.topbar-cmp-cart--min-cart .topbar-cmp-cart__description {
  /* stylelint-disable media-feature-no-missing-punctuation */
  /* stylelint-enable */ }
  @media only screen and (max-width: 767px) {
    .topbar-cmp-cart--min-cart .topbar-cmp-cart__description {
      border: 0;
      clip: rect(0 0 0 0);
      height: 1px;
      margin: -1px;
      overflow: hidden;
      padding: 0;
      position: absolute;
      width: 1px; } }

.topbar-cmp-cart__text-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  border-bottom: solid 1px #BFC8CF;
  padding-bottom: 25px; }

.topbar-cmp-cart__text {
  font-size: 16px;
  line-height: 1.375;
  color: #0C1217;
  margin: 0; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-cart__text {
      font-size: 16px;
      line-height: 1.375; } }
  @media only screen and (min-width: 1024px) {
    .topbar-cmp-cart__text {
      font-size: 16px;
      line-height: 1.5; } }

.topbar-cmp-cart__link--counter {
  position: relative;
  text-decoration: none; }

.topbar-cmp-cart__link--view-all {
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-left: 10px;
  white-space: nowrap; }

.topbar-cmp-cart__content {
  height: 100%;
  overflow: hidden;
  overflow-y: scroll;
  -ms-overflow-style: none;
  padding: 24px;
  padding-top: 48px;
  /* stylelint-disable media-feature-no-missing-punctuation */
  /* stylelint-enable */
  /* stylelint-disable media-feature-no-missing-punctuation */
  /* stylelint-enable */ }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-cart__content {
      padding: 24px; } }
  .topbar-cmp-cart__content::-webkit-scrollbar {
    display: none; }
  @media only screen and (max-width: 767px) {
    .topbar-cmp-cart__content {
      max-height: 100vh; } }
  @media only screen and (max-width: 479px) {
    .topbar-cmp-cart__content {
      height: 70%; } }
  .topbar-cmp-cart--min-cart .topbar-cmp-cart__content {
    /* stylelint-disable media-feature-no-missing-punctuation */
    /* stylelint-enable */ }
    @media only screen and (max-width: 767px) {
      .topbar-cmp-cart--min-cart .topbar-cmp-cart__content {
        border: 0;
        clip: rect(0 0 0 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px; } }

.topbar-cmp-cart__list {
  height: 100%; }

@-moz-document url-prefix() {
  /* only for FF*/
  .topbar-cmp-cart__list-wrapper {
    overflow: hidden; }
  .topbar-cmp-cart__list {
    overflow-x: hidden;
    overflow-y: auto;
    box-sizing: content-box;
    /* Work around for hidden scrollbar. The width will be 100% + 17px */ } }

.topbar-cmp-cart__list-item {
  font-size: 16px;
  line-height: 1.375;
  border-bottom: solid 1px #BFC8CF;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 12px 0px;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  gap: 8px;
  width: 100%; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-cart__list-item {
      font-size: 16px;
      line-height: 1.375; } }
  @media only screen and (min-width: 1024px) {
    .topbar-cmp-cart__list-item {
      font-size: 16px;
      line-height: 1.5; } }
  .topbar-cmp-cart__list-item:last-child {
    border-bottom: solid 1px #BFC8CF; }

.topbar-cmp-cart__product-image-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 20%;
          flex: 0 0 20%;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: start; }

.topbar-cmp-cart__product-image {
  width: 100%; }

.topbar-cmp-cart__product-info {
  -webkit-box-flex: 1;
      -ms-flex: 1 1 50%;
          flex: 1 1 50%;
  max-width: 50%;
  padding: 0 10px;
  word-wrap: break-word;
  overflow-wrap: break-word; }

.topbar-cmp-cart__product-name {
  color: #0C1217;
  margin: 0; }

.topbar-cmp-cart__product-price {
  -webkit-box-flex: 1;
      -ms-flex: 1 0 30%;
          flex: 1 0 30%;
  margin: 0;
  text-align: right; }

.topbar-cmp-cart__product-price-key {
  color: #0C1217;
  font-weight: normal; }

.topbar-cmp-cart__product-price-value {
  color: #0C1217;
  font-weight: bold;
  margin: 0;
  white-space: nowrap; }

.topbar-cmp-cart__product-quantity {
  margin-top: 10px;
  color: #0C1217; }

.topbar-cmp-cart__summary {
  background-color: #FFFFFF;
  bottom: 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  font-weight: bold;
  padding: 24px 24px 48px;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 16px;
  z-index: 10000;
  /* stylelint-disable media-feature-no-missing-punctuation */
  /* stylelint-enable */ }
  .topbar-cmp-cart__summary > *:first-child {
    margin-top: 0 !important; }
    .topbar-cmp-cart__summary > *:first-child > [data-component-root] > * {
      margin-top: 0 !important; }
  .topbar-cmp-cart__summary > *:last-child {
    margin-bottom: 0 !important; }
    .topbar-cmp-cart__summary > *:last-child > [data-component-root] > * {
      margin-bottom: 0 !important; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-cart__summary {
      padding: 24px; } }
  @media only screen and (max-width: 767px) {
    .topbar-cmp-cart__summary {
      -webkit-box-shadow: 0px 5px 5px 2px rgba(0, 0, 0, 0.75);
      box-shadow: 0px 0px 10px 2px rgba(0, 0, 0, 0.2); } }
  .topbar-cmp-cart__summary .topbar-elm-button {
    margin: 0; }
  @media only screen and (min-width: 480px) {
    .topbar-cmp-cart__summary .topbar-elm-button--no-border {
      display: none; } }

.topbar-cmp-cart__total-price {
  font-size: 18px;
  line-height: 1.33333;
  margin-bottom: 0px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-cart__total-price {
      font-size: 18px;
      line-height: 1.5; } }
  @media only screen and (min-width: 1024px) {
    .topbar-cmp-cart__total-price {
      font-size: 18px;
      line-height: 1.35; } }
  .topbar-cmp-cart--min-cart .topbar-cmp-cart__total-price {
    /* stylelint-disable media-feature-no-missing-punctuation */
    /* stylelint-enable */ }
    @media only screen and (max-width: 767px) {
      .topbar-cmp-cart--min-cart .topbar-cmp-cart__total-price {
        border: 0;
        clip: rect(0 0 0 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px; } }

.topbar-cmp-cart__total-price-title {
  color: #0C1217; }

.topbar-cmp-cart__total-price-value {
  color: #0C1217;
  margin: 0; }

.topbar-cmp-dropdown {
  z-index: 10;
  background-color: #FFFFFF;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: none;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  height: 100vh;
  overflow: hidden;
  position: absolute;
  top: 0;
  width: 100%;
  /* stylelint-disable media-feature-no-missing-punctuation */
  /* stylelint-enable */ }
  @media only screen and (max-width: 767px) {
    .topbar-cmp-dropdown {
      position: fixed; } }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-dropdown {
      -webkit-box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.09), 0 2px 4px 0 rgba(0, 0, 0, 0.12);
              box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.09), 0 2px 4px 0 rgba(0, 0, 0, 0.12);
      height: auto;
      overflow: visible;
      top: 50px;
      right: -40px;
      width: 400px; }
      .topbar-cmp-dropdown .topbar-elm-square-button--icon-close {
        display: none; } }
  .topbar-cmp-dropdown--overflow {
    right: 0; }
  .topbar-cmp-dropdown--overlay {
    z-index: 10000;
    bottom: 0;
    display: none;
    height: auto;
    position: fixed;
    top: unset; }
    @media only screen and (min-width: 768px) {
      .topbar-cmp-dropdown--overlay {
        bottom: unset;
        top: 80px; } }
  .topbar-cmp-dropdown .topbar-elm-square-button--icon-close {
    z-index: 1;
    position: absolute;
    right: 10px;
    top: 10px; }
  .topbar-cmp-dropdown a.topbar-elm-link {
    color: #0B58D0; }

/*.topbar-cmp-dropdown__heading {
	color: t-color-text(default);
	line-height: 1.35;
}*/
.topbar-cmp-dropdown__heading,
.topbar-cmp-dropdown__sub-heading {
  color: #11497B;
  margin-top: 0;
  font-weight: bold; }

.topbar-cmp-dropdown__heading {
  font-size: 18px;
  line-height: 1.33333;
  color: #11497B;
  margin-bottom: 12px; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-dropdown__heading {
      font-size: 20px;
      line-height: 1.35; } }
  @media only screen and (min-width: 1024px) {
    .topbar-cmp-dropdown__heading {
      font-size: 20px;
      line-height: 1.35; } }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-dropdown__heading--cart {
      margin-bottom: 8px; } }
  @media only screen and (min-width: 768px) and (min-width: 768px) {
    .topbar-cmp-dropdown__heading--cart {
      margin-bottom: 8px; } }
  .topbar-cmp-dropdown__heading--profile {
    margin-bottom: 16px;
    color: #FFFFFF; }
    @media only screen and (min-width: 768px) {
      .topbar-cmp-dropdown__heading--profile {
        margin-bottom: 16px; } }
  .topbar-cmp-dropdown__heading--sign-in {
    margin-bottom: 16px; }
    @media only screen and (min-width: 768px) {
      .topbar-cmp-dropdown__heading--sign-in {
        margin-bottom: 16px; } }

.topbar-cmp-dropdown__sub-heading {
  font-size: 18px;
  line-height: 1.33333; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-dropdown__sub-heading {
      font-size: 20px;
      line-height: 1.35; } }
  @media only screen and (min-width: 1024px) {
    .topbar-cmp-dropdown__sub-heading {
      font-size: 20px;
      line-height: 1.35; } }

.topbar-cmp-dropdown__text {
  font-size: 14px;
  margin: 0 0 25px;
  color: #0C1217;
  line-height: 1.35;
  font-weight: 400; }

/* Component - Form
------------------------- */
.topbar-cmp-form {
  display: block; }

.topbar-cmp-form-text__label,
.topbar-cmp-form-options__label,
.topbar-cmp-form-options__legend {
  font-size: 16px;
  line-height: 1.375;
  color: #11497B;
  display: block;
  width: 100%; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-form-text__label,
    .topbar-cmp-form-options__label,
    .topbar-cmp-form-options__legend {
      font-size: 16px;
      line-height: 1.375; } }
  @media only screen and (min-width: 1024px) {
    .topbar-cmp-form-text__label,
    .topbar-cmp-form-options__label,
    .topbar-cmp-form-options__legend {
      font-size: 16px;
      line-height: 1.5; } }
  .topbar-cmp-form-text__label--required::after,
  .topbar-cmp-form-options__label--required::after,
  .topbar-cmp-form-options__legend--required::after {
    content: "*"; }

.topbar-cmp-form-text__text,
.topbar-cmp-form-text__textarea,
.topbar-cmp-form-options__field {
  font-size: 16px;
  line-height: 1.375;
  border: 1px solid #C9C9C9;
  color: #333333;
  padding: 8px 10px;
  -webkit-transition: border-color 0.2s ease-out;
  transition: border-color 0.2s ease-out; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-form-text__text,
    .topbar-cmp-form-text__textarea,
    .topbar-cmp-form-options__field {
      font-size: 16px;
      line-height: 1.375; } }
  @media only screen and (min-width: 1024px) {
    .topbar-cmp-form-text__text,
    .topbar-cmp-form-text__textarea,
    .topbar-cmp-form-options__field {
      font-size: 16px;
      line-height: 1.5; } }
  .topbar-cmp-form-text__text:focus,
  .topbar-cmp-form-text__textarea:focus,
  .topbar-cmp-form-options__field:focus {
    border-color: #11497B;
    outline: none; }
  .topbar-cmp-form-text__text[readonly],
  .topbar-cmp-form-text__textarea[readonly],
  .topbar-cmp-form-options__field[readonly] {
    background-color: #F2F2F2; }
  .topbar-cmp-form-text__text[disabled],
  .topbar-cmp-form-text__textarea[disabled],
  .topbar-cmp-form-options__field[disabled] {
    background-color: #F2F2F2;
    color: #999999; }

.topbar-cmp-form-text__help-block,
.topbar-cmp-form-options__help-block,
.topbar-cmp-form-text__error-block,
.topbar-cmp-form-options__error-block {
  font-size: 16px;
  line-height: 1.375;
  margin-top: 8px; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-form-text__help-block,
    .topbar-cmp-form-options__help-block,
    .topbar-cmp-form-text__error-block,
    .topbar-cmp-form-options__error-block {
      font-size: 16px;
      line-height: 1.375; } }
  @media only screen and (min-width: 1024px) {
    .topbar-cmp-form-text__help-block,
    .topbar-cmp-form-options__help-block,
    .topbar-cmp-form-text__error-block,
    .topbar-cmp-form-options__error-block {
      font-size: 16px;
      line-height: 1.5; } }

.topbar-cmp-form-text__help-block,
.topbar-cmp-form-options__help-block {
  color: #11497B; }

.topbar-cmp-form-text__error-block,
.topbar-cmp-form-options__error-block {
  color: #DD0028; }

/* Component - Form Text
------------------------- */
.topbar-cmp-form-text__label {
  font-weight: bold; }

.topbar-cmp-form-text__text::-webkit-input-placeholder, .topbar-cmp-form-text__textarea::-webkit-input-placeholder {
  color: #999999; }

.topbar-cmp-form-text__text::-moz-placeholder, .topbar-cmp-form-text__textarea::-moz-placeholder {
  color: #999999; }

.topbar-cmp-form-text__text:-ms-input-placeholder, .topbar-cmp-form-text__textarea:-ms-input-placeholder {
  color: #999999; }

.topbar-cmp-form-text__text::-ms-input-placeholder, .topbar-cmp-form-text__textarea::-ms-input-placeholder {
  color: #999999; }

.topbar-cmp-form-text__text::placeholder,
.topbar-cmp-form-text__textarea::placeholder {
  color: #999999; }

.topbar-cmp-form-text__text[disabled]::-webkit-input-placeholder, .topbar-cmp-form-text__textarea[disabled]::-webkit-input-placeholder {
  color: #C9C9C9; }

.topbar-cmp-form-text__text[disabled]::-moz-placeholder, .topbar-cmp-form-text__textarea[disabled]::-moz-placeholder {
  color: #C9C9C9; }

.topbar-cmp-form-text__text[disabled]:-ms-input-placeholder, .topbar-cmp-form-text__textarea[disabled]:-ms-input-placeholder {
  color: #C9C9C9; }

.topbar-cmp-form-text__text[disabled]::-ms-input-placeholder, .topbar-cmp-form-text__textarea[disabled]::-ms-input-placeholder {
  color: #C9C9C9; }

.topbar-cmp-form-text__text[disabled]::placeholder,
.topbar-cmp-form-text__textarea[disabled]::placeholder {
  color: #C9C9C9; }

.topbar-cmp-form-text--error .topbar-cmp-form-text__text, .topbar-cmp-form-text--error
.topbar-cmp-form-text__textarea {
  border-color: #DD0028; }

.topbar-cmp-form-text--underline .topbar-cmp-form-text__text, .topbar-cmp-form-text--underline
.topbar-cmp-form-text__textarea {
  border-left-color: transparent;
  border-right-color: transparent;
  border-top-color: transparent; }

/* Component - Form Options
------------------------- */
.topbar-cmp-form-options__legend,
.topbar-cmp-form-options__label {
  font-weight: bold; }

.topbar-cmp-form-options__label--required::after {
  content: "*"; }

.topbar-cmp-form-option {
  display: block;
  /* stylelint-disable selector-max-compound-selectors, selector-max-specificity, declaration-no-important, declaration-bang-space-before */
  /* stylelint-enable */ }
  .topbar-cmp-form-option + .topbar-cmp-form-option {
    margin-top: 8px; }
  .topbar-cmp-form-option label {
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex; }
    .topbar-cmp-form-option label::before {
      background: #FFFFFF center center/80% no-repeat;
      border: 1px solid #C9C9C9;
      content: "";
      display: block;
      height: 16px;
      margin-right: 0.5em;
      width: 16px; }
  .topbar-cmp-form-options--error .topbar-cmp-form-option input:not([disabled]) + label::before {
    border: 1px solid #DD0028 !important; }

.topbar-cmp-form-option__field {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
  /* stylelint-disable selector-max-specificity */
  /* stylelint-enable */
  /* stylelint-disable selector-max-specificity */
  /* stylelint-enable */ }
  .topbar-cmp-form-option__field + label {
    color: #11497B; }
    .topbar-cmp-form-option__field + label::before {
      -webkit-transition: border-color 0.2s ease-out;
      transition: border-color 0.2s ease-out; }
  .topbar-cmp-form-option__field:focus + label::before {
    border-color: #11497B; }
  .topbar-cmp-form-option__field[disabled] + label {
    color: #999999;
    cursor: default; }
    .topbar-cmp-form-option__field[disabled] + label::before {
      background-color: #F2F2F2; }
  .topbar-cmp-form-option__field--checkbox:checked + label::before {
    background-image: url("data:image/svg+xml;charset=utf-8,%3C?xml version='1.0' encoding='utf-8'?%3E %3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' x='0px' y='0px' viewBox='0 0 128 128' fill='%2311497B' stroke='none'%3E %3Cpath d='M123,28.7l-5-4.8c-1.4-1.3-3.7-1.3-5,0L48.5,86.2c-1.4,1.3-3.7,1.3-5,0L15.1,58.8c-1.4-1.3-3.7-1.3-5,0l-5,4.8 c-1.4,1.3-1.4,3.5,0,4.9L41,103.1c2.7,2.6,7.2,2.6,10,0l72.1-69.5C124.4,32.3,124.4,30.2,123,28.7'/%3E %3C/svg%3E"); }
  .topbar-cmp-form-option__field--checkbox:checked[disabled] + label::before {
    background-image: url("data:image/svg+xml;charset=utf-8,%3C?xml version='1.0' encoding='utf-8'?%3E %3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' x='0px' y='0px' viewBox='0 0 128 128' fill='%23C9C9C9' stroke='none'%3E %3Cpath d='M123,28.7l-5-4.8c-1.4-1.3-3.7-1.3-5,0L48.5,86.2c-1.4,1.3-3.7,1.3-5,0L15.1,58.8c-1.4-1.3-3.7-1.3-5,0l-5,4.8 c-1.4,1.3-1.4,3.5,0,4.9L41,103.1c2.7,2.6,7.2,2.6,10,0l72.1-69.5C124.4,32.3,124.4,30.2,123,28.7'/%3E %3C/svg%3E"); }
  .topbar-cmp-form-option__field--radio + label::before {
    border-radius: 50%; }
  .topbar-cmp-form-option__field--radio:checked + label::before {
    background-image: url("data:image/svg+xml;charset=utf-8,%3C?xml version='1.0' encoding='utf-8'?%3E %3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' x='0px' y='0px' viewBox='0 0 128 128' fill='%2311497B' stroke='none'%3E %3Ccircle cx='64' cy='64' r='60'/%3E %3C/svg%3E"); }
  .topbar-cmp-form-option__field--radio:checked[disabled] + label::before {
    background-image: url("data:image/svg+xml;charset=utf-8,%3C?xml version='1.0' encoding='utf-8'?%3E %3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' x='0px' y='0px' viewBox='0 0 128 128' fill='%23C9C9C9' stroke='none'%3E %3Ccircle cx='64' cy='64' r='60'/%3E %3C/svg%3E"); }

.topbar-cmp-form-option__label {
  font-size: 16px;
  line-height: 1.375; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-form-option__label {
      font-size: 16px;
      line-height: 1.375; } }
  @media only screen and (min-width: 1024px) {
    .topbar-cmp-form-option__label {
      font-size: 16px;
      line-height: 1.5; } }

.topbar-cmp-form-options__field {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  /* stylelint-disable max-line-length */
  background: #FFFFFF url("data:image/svg+xml;charset=utf-8,%3C?xml version='1.0' encoding='utf-8'?%3E %3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' x='0px' y='0px' viewBox='0 0 128 78.9' fill='%2311497B' stroke='none'%3E %3Cpolygon points='128,14.9 64,78.9 0,14.9 14.9,0 64,49.1 113.1,0 '/%3E %3C/svg%3E") calc(100% - 10px) center/16px no-repeat;
  /* stylelint-enable max-line-length */
  border-radius: 0;
  cursor: pointer;
  padding-right: 40px;
  width: 100%; }
  .topbar-cmp-form-options__field::-ms-expand {
    display: none; }
  .topbar-cmp-form-options__field[disabled] {
    background-image: url("data:image/svg+xml;charset=utf-8,%3C?xml version='1.0' encoding='utf-8'?%3E %3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' x='0px' y='0px' viewBox='0 0 128 78.9' fill='%23C9C9C9' stroke='none'%3E %3Cpolygon points='128,14.9 64,78.9 0,14.9 14.9,0 64,49.1 113.1,0 '/%3E %3C/svg%3E");
    cursor: default; }
  .topbar-cmp-form-options--error .topbar-cmp-form-options__field {
    background-image: url("data:image/svg+xml;charset=utf-8,%3C?xml version='1.0' encoding='utf-8'?%3E %3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' x='0px' y='0px' viewBox='0 0 128 78.9' fill='%23DD0028' stroke='none'%3E %3Cpolygon points='128,14.9 64,78.9 0,14.9 14.9,0 64,49.1 113.1,0 '/%3E %3C/svg%3E");
    border-color: #DD0028; }
  .topbar-cmp-form-options--underline .topbar-cmp-form-options__field {
    border-left-color: transparent;
    border-right-color: transparent;
    border-top-color: transparent; }

.topbar-cmp-noscroll {
  margin: 0;
  height: 100%;
  overflow: hidden; }

.topbar-cmp-overlay {
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.8);
  height: 100%;
  left: 0;
  position: fixed;
  top: 0;
  -webkit-transition: opacity 0.2s ease-out;
  transition: opacity 0.2s ease-out;
  width: 100%; }
  .topbar-cmp-overlay.topbar-cmp-cart {
    top: unset; }

.topbar-overlay-container {
  z-index: 10000;
  display: none;
  height: 100%;
  left: 0;
  position: fixed;
  top: 0;
  -webkit-transition: opacity 0.2s ease-out;
  transition: opacity 0.2s ease-out;
  width: 100%;
  /* stylelint-disable media-feature-no-missing-punctuation */
  /* stylelint-enable */ }
  @media only screen and (max-width: 767px) {
    .topbar-overlay-container.topbar-overlay-container-visible {
      display: block; }
    .topbar-overlay-container .topbar-cmp-dropdown {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex; } }

.topbar-menu__list-item .topbar-cmp-dropdown {
  /* stylelint-disable media-feature-no-missing-punctuation */
  /* stylelint-enable */
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center; }
  @media only screen and (max-width: 767px) {
    .topbar-menu__list-item .topbar-cmp-dropdown {
      border: 0;
      clip: rect(0 0 0 0);
      height: 1px;
      margin: -1px;
      overflow: hidden;
      padding: 0;
      position: absolute;
      width: 1px; } }

.topbar-cmp-profile {
  padding: 24px;
  padding-top: 48px; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-profile {
      padding: 24px; } }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-profile {
      max-width: 300px; } }

.closeButton {
  float: right;
  margin-top: 1.5rem;
  margin-right: 1.5rem;
  border: 2px solid #0B58D0;
  line-height: 1.35;
  background-color: #FFFFFF;
  color: #0B58D0;
  border-radius: 9999px; }

.topbar-cmp-profile__user {
  padding: 0 0 1.5rem 0;
  border-bottom: 1px solid #dce0e4;
  font-size: 16px;
  line-height: 1.375; }
  .topbar-cmp-profile__user > *:first-child {
    margin-top: 0 !important; }
    .topbar-cmp-profile__user > *:first-child > [data-component-root] > * {
      margin-top: 0 !important; }
  .topbar-cmp-profile__user > *:last-child {
    margin-bottom: 0 !important; }
    .topbar-cmp-profile__user > *:last-child > [data-component-root] > * {
      margin-bottom: 0 !important; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-profile__user {
      font-size: 16px;
      line-height: 1.375; } }
  @media only screen and (min-width: 1024px) {
    .topbar-cmp-profile__user {
      font-size: 16px;
      line-height: 1.5; } }

.topbar-cmp-profile__user-name {
  margin-bottom: 8px;
  color: #11497B;
  font-weight: bold;
  margin-top: 0; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-profile__user-name {
      margin-bottom: 8px; } }

.topbar-cmp-profile__user-company {
  font-size: 16px;
  line-height: 1.375;
  color: #727272;
  margin: 0; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-profile__user-company {
      font-size: 16px;
      line-height: 1.375; } }
  @media only screen and (min-width: 1024px) {
    .topbar-cmp-profile__user-company {
      font-size: 16px;
      line-height: 1.5; } }

.topbar-cmp-profile__user-company-number {
  font-size: 16px;
  line-height: 1.375;
  margin-bottom: 16px;
  color: #727272;
  margin-top: 0; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-profile__user-company-number {
      font-size: 16px;
      line-height: 1.375; } }
  @media only screen and (min-width: 1024px) {
    .topbar-cmp-profile__user-company-number {
      font-size: 16px;
      line-height: 1.5; } }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-profile__user-company-number {
      margin-bottom: 16px; } }

.topbar-cmp-profile__mygrundfos {
  padding: 1.5rem 0;
  border-bottom: 1px solid #dce0e4; }
  .topbar-cmp-profile__mygrundfos > *:first-child {
    margin-top: 0 !important; }
    .topbar-cmp-profile__mygrundfos > *:first-child > [data-component-root] > * {
      margin-top: 0 !important; }
  .topbar-cmp-profile__mygrundfos > *:last-child {
    margin-bottom: 0 !important; }
    .topbar-cmp-profile__mygrundfos > *:last-child > [data-component-root] > * {
      margin-bottom: 0 !important; }
  .topbar-cmp-profile__mygrundfos .topbar-cmp-dropdown__sub-heading {
    font-size: 18px;
    line-height: 1.33333; }
    @media only screen and (min-width: 768px) {
      .topbar-cmp-profile__mygrundfos .topbar-cmp-dropdown__sub-heading {
        font-size: 20px;
        line-height: 1.35; } }
    @media only screen and (min-width: 1024px) {
      .topbar-cmp-profile__mygrundfos .topbar-cmp-dropdown__sub-heading {
        font-size: 20px;
        line-height: 1.35; } }

.topbar-cmp-profile__data-error {
  padding: 24px;
  padding-top: 48px;
  border-bottom: solid 1px #BFC8CF;
  cursor: pointer; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-profile__data-error {
      padding: 24px; } }

.topbar-cmp-profile__data-error-text {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: #11497B;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 0;
  /* stylelint-disable selector-pseudo-element-no-unknown */
  /* stylelint-enable */ }
  .topbar-cmp-profile__data-error-text::before {
    background: url("data:image/svg+xml;charset=utf-8,%3C?xml version='1.0' encoding='UTF-8'?%3E %3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' x='0px' y='0px' viewBox='0 0 96 128' fill='%230B58D0' stroke='none'%3E %3Cpath d='M48,96 L48,80 L72,104 L48,128 L48,112 C21.490332,112 0,90.509668 0,64 C0,52.783573 3.84718985,42.4657053 10.2939832,34.2939832 L21.7269166,45.7269166 C18.1168537,50.9077638 16,57.206673 16,64 C16,81.673112 30.326888,96 48,96 Z M48,16 C74.509668,16 96,37.490332 96,64 C96,75.216427 92.1528101,85.5342947 85.7060168,93.7060168 L74.2730834,82.2730834 C77.8831463,77.0922362 80,70.793327 80,64 C80,46.326888 65.673112,32 48,32 L48,48 L24,24 L48,0 L48,16 Z'/%3E %3C/svg%3E") center center/contain no-repeat;
    content: "";
    display: inline-block;
    height: 16px;
    width: 16px;
    margin-right: 0.5em; }

.topbar-cmp-profile__links {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  -ms-flex-item-align: stretch;
      align-self: stretch;
  gap: 8px;
  padding: 1.5rem 0 0 0;
  /* stylelint-disable media-feature-no-missing-punctuation */
  /* stylelint-enable */ }
  .topbar-cmp-profile__links::before {
    content: '';
    display: block;
    position: absolute;
    height: 1px;
    top: 0;
    width: calc(100% - 30px);
    background-color: #BFC8CF; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-profile__links::before {
      width: calc(100% - 50px); } }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-profile__links {
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
          -ms-flex-direction: column;
              flex-direction: column;
      gap: 16px; } }
  @media only screen and (max-width: 479px) {
    .topbar-cmp-profile__links {
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
          -ms-flex-direction: column;
              flex-direction: column;
      gap: 16px; } }

.topbar-cmp-profile__mygrundfos + .topbar-cmp-profile__links::before {
  display: none; }

.topbar-cmp-profile__data-error + .topbar-cmp-profile__links::before {
  display: none; }

.topbar-cmp-profile__link {
  display: block;
  margin-bottom: 12px; }
  .topbar-cmp-profile__link:last-child {
    margin-bottom: 0; }
  .topbar-cmp-profile__link .a.topbar-elm-button--ghost {
    margin: 0; }

.topbar-cmp-profile__create-account {
  padding: 0 0 1.5rem 0;
  border-bottom: 1px solid #dce0e4; }

.topbar-cmp-profile__sign-in {
  padding: 1.5rem 0 0 0; }
  .topbar-cmp-profile__sign-in .topbar-elm-button.topbar-elm-button--positive {
    margin-bottom: 0;
    border-radius: 30px; }

.topbar-cmp-profile__sign-out {
  position: fixed;
  background-color: #FFFFFF;
  bottom: 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  font-weight: bold;
  padding: 24px 24px 48px;
  width: 100%; }
  .topbar-cmp-profile__sign-out > *:first-child {
    margin-top: 0 !important; }
    .topbar-cmp-profile__sign-out > *:first-child > [data-component-root] > * {
      margin-top: 0 !important; }
  .topbar-cmp-profile__sign-out > *:last-child {
    margin-bottom: 0 !important; }
    .topbar-cmp-profile__sign-out > *:last-child > [data-component-root] > * {
      margin-bottom: 0 !important; }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-profile__sign-out {
      position: relative;
      padding: 0 25px 25px; } }
  @media only screen and (min-width: 768px) {
    .topbar-cmp-profile__sign-out .topbar-elm-button {
      border: none;
      padding: 0;
      -webkit-box-pack: start;
          -ms-flex-pack: start;
              justify-content: start; } }

#global-topbar-settings {
  bottom: 0;
  display: none;
  overflow: hidden;
  opacity: 1;
  padding: 0;
  position: fixed;
  text-align: left;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1040; }

#global-topbar-settings:before {
  background-color: #f2f2f2;
  content: ' ';
  opacity: 0.7;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0; }

#global-topbar-settings div {
  -webkit-box-sizing: border-box;
          box-sizing: border-box; }

#global-topbar-settings .gtbs-modal-dialog {
  height: 100%;
  margin-left: auto;
  margin-right: auto;
  width: auto;
  padding: 10px;
  z-index: 1050;
  max-width: 100%;
  -webkit-transform: translate(0, 0);
          transform: translate(0, 0); }

#global-topbar-settings .gtbs-modal-content {
  position: relative;
  background-clip: padding-box;
  background-color: #fff;
  border: 10px solid #000;
  border: 10px solid rgba(0, 0, 0, 0.5);
  border-radius: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  height: 100%;
  outline: 0; }

#global-topbar-settings .gtbs-modal-header {
  background-color: #dedede;
  border-bottom: 1px solid #c9c9c9;
  margin: 0 0 15px 0;
  padding: 11px; }

#global-topbar-settings .gtbs-modal-header:before {
  background-color: #0068b4;
  content: "";
  display: block;
  height: 7px;
  margin: 0 -11px;
  position: relative;
  top: -11px; }

#global-topbar-settings .gtbs-modal-dialog .gtbs-modal-content button.close {
  background-color: #333;
  border: 0;
  color: #fff;
  cursor: pointer;
  float: right;
  font-family: "Lucida Grande", Tahoma, sans-serif;
  font-size: 14px;
  font-weight: bold;
  line-height: 1;
  margin-top: 0;
  opacity: 1;
  padding: 2px 4px 4px 4px;
  text-shadow: none;
  -webkit-appearance: none; }

#global-topbar-settings .gtbs-modal-title {
  color: #727272;
  font-weight: 500;
  margin: 0;
  line-height: 1.42857143; }

#global-topbar-settings .gtbs-modal-body {
  height: 90%;
  /* IE8 */
  height: calc(100% - 67px);
  position: relative;
  padding: 0; }

#global-topbar-settings .gtbs-modal-body iframe {
  background-image: url(assets/spinner.gif);
  background-repeat: no-repeat;
  background-position: 50% 50%;
  border: 0;
  height: 100%;
  width: 100%;
  overflow-y: auto; }

@media screen and (min-width: 768px) {
  #global-topbar-settings .gtbs-modal-dialog {
    left: 50%;
    right: auto;
    width: 730px;
    padding-top: 30px;
    padding-bottom: 30px; } }

@media screen and (min-width: 992px) {
  #global-topbar-settings .gtbs-modal-dialog {
    width: 940px; } }

@media screen and (min-width: 1200px) {
  #global-topbar-settings .gtbs-modal-dialog {
    width: 1150px; } }

.hidden {
  display: none; }

.topbar-cmp-select__wrapper {
  cursor: pointer;
  display: inline-block;
  position: relative;
  width: 100%;
  height: 40px;
  font-size: 15px; }

.topbar-cmp-select__custom {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 5px 10px;
  color: #000000;
  line-height: 30px;
  overflow: hidden;
  border: 1px solid #C9C9C9; }
  .topbar-cmp-select__custom.active {
    border-color: #11497B;
    outline: none; }
  .topbar-cmp-select__custom:after {
    content: "";
    width: 35px;
    height: 35px;
    background: #FFFFFF url("data:image/svg+xml;charset=utf-8,%3C?xml version='1.0' encoding='utf-8'?%3E %3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' x='0px' y='0px' viewBox='0 0 128 78.9' fill='%2311497B' stroke='none'%3E %3Cpolygon points='128,14.9 64,78.9 0,14.9 14.9,0 64,49.1 113.1,0 '/%3E %3C/svg%3E") calc(100% - 10px) center/16px no-repeat;
    position: absolute;
    top: 2px;
    right: 1px; }

.topbar-cmp-select__custom--list {
  position: absolute;
  top: 100%;
  right: 0;
  left: 0;
  z-index: 999;
  margin: 0 0;
  padding: 0 0;
  list-style: none;
  border: 1px solid #C9C9C9;
  background-color: #FFFFFF;
  -webkit-box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.09), 0 2px 4px 0 rgba(0, 0, 0, 0.12);
          box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.09), 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  max-height: 330px;
  overflow: auto; }
  .topbar-cmp-select__custom--list li {
    margin: 0;
    padding: 10px 12px;
    color: #000000;
    line-height: 18px; }
    .topbar-cmp-select__custom--list li.active {
      background-color: #BFC8CF; }
    .topbar-cmp-select__custom--list li:hover {
      background-color: #009EE3;
      color: #FFFFFF; }

/* Modules
------------------------- */
.topbar-menu, .topbar-menu__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-column-gap: 15px;
     -moz-column-gap: 15px;
          column-gap: 15px; }

.topbar-menu__list-item {
  position: relative;
  text-align: initial; }
  .topbar-menu__list-item.active .topbar-cmp-dropdown {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex; }
  @media only screen and (min-width: 768px) {
    .topbar-menu__list-item.active .topbar-elm-icon::before {
      border: solid 5px transparent;
      border-bottom-color: #FFFFFF;
      border-top-width: 0;
      content: ' ';
      display: block;
      position: absolute;
      left: 6px;
      top: 45px; } }
  .topbar-menu__list-item--link {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    color: #FFFFFF;
    line-height: 99%;
    text-decoration: none;
    padding: 24px 0px; }
    .topbar-menu__list-item--link:hover, .topbar-menu__list-item--link:focus-within {
      text-decoration: none;
      color: #FFFFFF; }

#globalTopBarExtra a {
  color: #FFFFFF; }
  #globalTopBarExtra a:hover {
    text-decoration: none; }

button.elm-link.cmp-country-selector__toggle {
  text-decoration: none;
  color: #FFFFFF; }

.cmp-country-selector__toggle-country {
  color: #FFFFFF; }

.cmp-country-selector__toggle {
  position: relative;
  top: 1px; }
  .cmp-country-selector__toggle .elm-link__text {
    background: none;
    color: #FFFFFF;
    padding-right: 12px;
    padding-left: 10px;
    line-height: 1.35; }
    @media only screen and (min-width: 768px) {
      .cmp-country-selector__toggle .elm-link__text {
        padding-left: 8px; } }
    .cmp-country-selector__toggle .elm-link__text:hover {
      color: #FFFFFF;
      text-decoration: none; }
  .cmp-country-selector__toggle:before {
    content: ' ';
    display: inline-block;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' fill='%23FFFFFF'%3E%3Cg id='Outline_icons' data-name='Outline icons'%3E%3Cpath d='M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm2.73,2.33-.48,2.34a1,1,0,0,1-.66.75L13.72,8.7a3,3,0,0,0-2,2.18l-.66,3.55-.51-.11a1,1,0,0,1-.78-.84l-1-7.06A12,12,0,0,1,16,4,11.73,11.73,0,0,1,18.73,4.33ZM22,20.7a7,7,0,0,1-.36,2.22l-1.44,4.3a11.52,11.52,0,0,1-2.2.6V24.6a3,3,0,0,0-2.23-2.9L15,21.5a1,1,0,0,1-.72-1.13l.51-3.1,6.41,1.37a1,1,0,0,1,.79,1ZM4,16A12,12,0,0,1,7,8.07l.8,5.68a3,3,0,0,0,2.34,2.52l2.68.58L12.31,20a3,3,0,0,0,2.19,3.39l.76.2a1,1,0,0,1,.74,1V28A12,12,0,0,1,4,16Zm18.75,9.91.79-2.36A9.19,9.19,0,0,0,24,20.7V19.62a3,3,0,0,0-2.37-2.94l-8.58-1.84.65-3.53a1,1,0,0,1,.66-.71l3.86-1.29a3,3,0,0,0,2-2.24l.44-2.13a12,12,0,0,1,2.1,21Z'/%3E%3C/g%3E%3C/svg%3E") 0 no-repeat;
    width: 21px;
    height: 21px; }
  .cmp-country-selector__toggle:hover:before {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' fill='%23FFFFFF'%3E%3Cg id='Outline_icons' data-name='Outline icons'%3E%3Cpath d='M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm2.73,2.33-.48,2.34a1,1,0,0,1-.66.75L13.72,8.7a3,3,0,0,0-2,2.18l-.66,3.55-.51-.11a1,1,0,0,1-.78-.84l-1-7.06A12,12,0,0,1,16,4,11.73,11.73,0,0,1,18.73,4.33ZM22,20.7a7,7,0,0,1-.36,2.22l-1.44,4.3a11.52,11.52,0,0,1-2.2.6V24.6a3,3,0,0,0-2.23-2.9L15,21.5a1,1,0,0,1-.72-1.13l.51-3.1,6.41,1.37a1,1,0,0,1,.79,1ZM4,16A12,12,0,0,1,7,8.07l.8,5.68a3,3,0,0,0,2.34,2.52l2.68.58L12.31,20a3,3,0,0,0,2.19,3.39l.76.2a1,1,0,0,1,.74,1V28A12,12,0,0,1,4,16Zm18.75,9.91.79-2.36A9.19,9.19,0,0,0,24,20.7V19.62a3,3,0,0,0-2.37-2.94l-8.58-1.84.65-3.53a1,1,0,0,1,.66-.71l3.86-1.29a3,3,0,0,0,2-2.24l.44-2.13a12,12,0,0,1,2.1,21Z'/%3E%3C/g%3E%3C/svg%3E") 0 no-repeat;
    outline: 6px solid rgba(255, 255, 255, 0.1);
    border-radius: 100%;
    background-color: rgba(255, 255, 255, 0.1); }

/* Helpers
------------------------- */
/* Helpers - Utility
------------------------- */
.h-hidden-mobile {
  /* stylelint-disable media-feature-no-missing-punctuation */
  /* stylelint-enable */ }
  @media only screen and (max-width: 767px) {
    .h-hidden-mobile {
      border: 0;
      clip: rect(0 0 0 0);
      height: 1px;
      margin: -1px;
      overflow: hidden;
      padding: 0;
      position: absolute;
      width: 1px; } }
