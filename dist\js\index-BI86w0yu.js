var je=Object.defineProperty,Ge=Object.defineProperties;var He=Object.getOwnPropertyDescriptors;var ie=Object.getOwnPropertySymbols;var ke=Object.prototype.hasOwnProperty,xe=Object.prototype.propertyIsEnumerable;var ce=(p,t,l)=>t in p?je(p,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):p[t]=l,J=(p,t)=>{for(var l in t||(t={}))ke.call(t,l)&&ce(p,l,t[l]);if(ie)for(var l of ie(t))xe.call(t,l)&&ce(p,l,t[l]);return p},Ee=(p,t)=>Ge(p,He(t));var Ie=(p,t)=>{var l={};for(var o in p)ke.call(p,o)&&t.indexOf(o)<0&&(l[o]=p[o]);if(p!=null&&ie)for(var o of ie(p))t.indexOf(o)<0&&xe.call(p,o)&&(l[o]=p[o]);return l};var Y=(p,t,l)=>ce(p,typeof t!="symbol"?t+"":t,l);var V=(p,t,l)=>new Promise((o,r)=>{var i=k=>{try{T(l.next(k))}catch(D){r(D)}},u=k=>{try{T(l.throw(k))}catch(D){r(D)}},T=k=>k.done?o(k.value):Promise.resolve(k.value).then(i,u);T((l=l.apply(p,t)).next())});import{_ as le}from"./_plugin-vue_export-helper-C6jw_E77.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                        *//* empty css                         *//* empty css                */import{r as C,aX as ne,bh as qe,$ as Je,c as G,U as ee,d as ae,o as W,aq as me,w as a,e,ab as Se,am as pe,ax as Pe,aa as Re,f as v,v as h,n as oe,L as S,O as se,b as O,bi as Qe,F as _e,aN as ge,ac as ye,ad as be,g as Z,bj as We,ap as F,bc as Xe,y as Ae,E as we,h as B,as as Ze,x as E,S as he,Z as Me,bk as Ke,W as Ye,bl as et,bm as tt,bn as lt,b9 as at,bo as ot,bp as nt,bq as st,br as rt,bs as it,aL as ut,a1 as De,a2 as Ce,bt as Be,aP as Te,aQ as Ve,al as Ne,a9 as $e,bu as dt,aw as ct,aF as vt,aG as ft,bv as mt,b7 as pt,av as _t,au as ue,k as gt,Q as yt,J as ve,B as bt,G as wt,H as ht}from"./index-CI3tXMQP.js";/* empty css                   *//* empty css                     *//* empty css                        *//* empty css                    *//* empty css                             *//* empty css                 *//* empty css                   *//* empty css                  *//* empty css                 */class Dt{constructor(){Y(this,"client",null);Y(this,"config",null);Y(this,"connected",C(!1));Y(this,"connecting",C(!1));Y(this,"connectionError",C(null));Y(this,"lastMessage",C(null));Y(this,"messages",ne({}));Y(this,"errors",ne([]));Y(this,"deviceData",ne({}))}connect(t){this.config=t,this.connecting.value=!0,this.connectionError.value=null;const l={clientId:t.clientId,keepalive:t.keepAlive,reconnectPeriod:t.reconnectPeriod,clean:!0};return t.username&&(l.username=t.username,l.password=t.password),new Promise((o,r)=>{try{this.client=qe.connect(t.brokerUrl,l),this.client.on("connect",()=>{console.log("MQTT连接成功"),this.connected.value=!0,this.connecting.value=!1,t.subscribeTopic&&this.subscribe(t.subscribeTopic),o(!0)}),this.client.on("error",i=>{console.error("MQTT连接错误:",i),this.connectionError.value=i,this.connecting.value=!1,r(i)}),this.client.on("message",(i,u)=>{try{const T=JSON.parse(u.toString());this.lastMessage.value=T,this.messages[i]=T,this.handleDeviceData(i,T)}catch(T){console.error("MQTT消息解析错误:",T),this.recordError({id:Date.now().toString(),deviceId:this.extractDeviceId(i),timestamp:Date.now(),errorCode:1001,errorMessage:"消息解析错误: "+T.message,severity:"warning",resolved:!1})}}),this.client.on("disconnect",()=>{console.log("MQTT断开连接"),this.connected.value=!1}),this.client.on("offline",()=>{console.log("MQTT客户端离线"),this.connected.value=!1}),this.client.on("reconnect",()=>{console.log("MQTT尝试重新连接"),this.connecting.value=!0})}catch(i){console.error("MQTT创建客户端错误:",i),this.connectionError.value=i,this.connecting.value=!1,r(i)}})}disconnect(){return new Promise(t=>{if(!this.client){t();return}this.client.end(!1,{},()=>{this.client=null,this.connected.value=!1,t()})})}subscribe(t,l=1){return new Promise((o,r)=>{if(!this.client||!this.connected.value){r(new Error("MQTT客户端未连接"));return}this.client.subscribe(t,{qos:l},i=>{if(i){console.error(`订阅主题 ${t} 失败:`,i),r(i);return}console.log(`成功订阅主题: ${t}`),o()})})}publish(t,l,o=1,r=!1){return new Promise((i,u)=>{if(!this.client||!this.connected.value){u(new Error("MQTT客户端未连接"));return}let T;typeof l=="object"?T=JSON.stringify(l):T=String(l),this.client.publish(t,T,{qos:o,retain:r},k=>{if(k){console.error(`发布消息到主题 ${t} 失败:`,k),u(k);return}console.log(`成功发布消息到主题: ${t}`),i()})})}handleDeviceData(t,l){try{if(!l||typeof l!="object")return;const o=this.extractDeviceId(t);if(!o)return;if(this.isInverterData(l)){const r={deviceId:o,timestamp:l.timestamp||Date.now(),frequency:parseFloat(l.frequency||0),current:parseFloat(l.current||0),voltage:parseFloat(l.voltage||0),power:parseFloat(l.power||0),temperature:parseFloat(l.temperature||0),runningStatus:!!l.runningStatus,errorCode:l.errorCode,errorMessage:l.errorMessage,rotationSpeed:l.rotationSpeed,flowRate:l.flowRate,pressure:l.pressure,torque:l.torque};this.deviceData[o]=r,r.errorCode&&r.errorMessage&&this.recordError({id:`${o}-${Date.now()}`,deviceId:o,timestamp:Date.now(),errorCode:r.errorCode,errorMessage:r.errorMessage,severity:this.determineSeverity(r.errorCode),resolved:!1})}}catch(o){console.error("处理设备数据错误:",o)}}extractDeviceId(t){var i;if(!t||!((i=this.config)!=null&&i.topicPrefix))return"";const l=t.split("/"),r=this.config.topicPrefix.split("/").filter(u=>u.length>0).length;return l.length>r?l[r]:""}isInverterData(t){return typeof t=="object"&&(t.frequency!==void 0||t.current!==void 0||t.voltage!==void 0||t.power!==void 0)}recordError(t){const l=this.errors.findIndex(o=>o.deviceId===t.deviceId&&o.errorCode===t.errorCode&&!o.resolved);l>=0?this.errors[l].timestamp=t.timestamp:this.errors.push(t)}determineSeverity(t){return t>=9e3?"critical":t>=5e3?"warning":"info"}getDeviceData(t){return this.deviceData[t]||null}getAllDeviceData(){return this.deviceData}getDeviceErrors(t){return this.errors.filter(l=>l.deviceId===t)}getAllErrors(){return this.errors}resolveError(t){const l=this.errors.findIndex(o=>o.id===t);return l>=0?(this.errors[l].resolved=!0,this.errors[l].resolvedTime=Date.now(),!0):!1}clearResolvedErrors(){const t=this.errors.length,l=this.errors.filter(o=>!o.resolved);return this.errors.splice(0,this.errors.length,...l),t-this.errors.length}testConnection(t){return V(this,null,function*(){const l=qe.connect(t.brokerUrl,{clientId:`${t.clientId}-test-${Date.now()}`,keepalive:t.keepAlive,reconnectPeriod:0,connectTimeout:5e3,username:t.username,password:t.password});return new Promise(o=>{const r=setTimeout(()=>{l.end(!0),o(!1)},5e3);l.on("connect",()=>{clearTimeout(r),l.end(!0),o(!0)}),l.on("error",()=>{clearTimeout(r),l.end(!0),o(!1)})})})}}const X=new Dt,K=(p=300)=>new Promise(t=>setTimeout(t,p));let L=[],Ct=1;function Tt(){return V(this,null,function*(){return yield K(),[...L]})}function Vt(p){return V(this,null,function*(){yield K();const t=L.find(l=>l.id===p);return t?J({},t):null})}function $t(p){return V(this,null,function*(){yield K();const t=Ee(J({},p),{id:`device-${Ct++}`,status:"offline"});return L.push(t),J({},t)})}function kt(p,t){return V(this,null,function*(){yield K();const l=L.findIndex(u=>u.id===p);if(l===-1)return null;const i=t,{status:o}=i,r=Ie(i,["status"]);return L[l]=J(J({},L[l]),r),J({},L[l])})}function xt(p){return V(this,null,function*(){yield K();const t=L.length;return L=L.filter(l=>l.id!==p),L.length<t})}function Le(p){return V(this,null,function*(){yield K();const t=L.find(l=>l.id===p);return!t||!t.mqttConfig?null:J({},t.mqttConfig)})}function ze(p,t){return V(this,null,function*(){yield K();const l=L.findIndex(o=>o.id===p);return l===-1?!1:(L[l].mqttConfig=J({},t),!0)})}function Fe(p){return V(this,null,function*(){try{const t=yield X.testConnection(p);return{success:t,message:t?"连接成功":"连接失败，请检查配置"}}catch(t){return{success:!1,message:`连接错误: ${t.message}`}}})}function fe(p){return V(this,null,function*(){return yield K(),X.getDeviceData(p)})}function Et(p){return V(this,null,function*(){return yield K(),X.getDeviceErrors(p)})}function It(p){return V(this,null,function*(){yield K(2e3);const t=L.findIndex(l=>l.id===p);return t===-1?!1:(L[t].status="offline",setTimeout(()=>{L[t]&&(L[t].status="online",L[t].lastConnectTime=new Date().toISOString())},5e3),!0)})}function qt(o,r){return V(this,arguments,function*(p,t,l={}){yield K();const i=L.find(u=>u.id===p);if(!i||!i.mqttConfig)return!1;try{const u=`${i.mqttConfig.publishTopic}/${p}/command`;return yield X.publish(u,{command:t,params:l,timestamp:Date.now()}),!0}catch(u){return console.error("发送命令失败:",u),!1}})}C("disconnected");const re=Je("gateway",()=>{const p=C([]),t=C(null),l=C({}),o=C(!1),r=C(null),i=C("disconnected"),u=C(X.connected.value),T=C(null),k=C([]),D=G(()=>p.value.find(c=>c.id===t.value)),Q=G(()=>p.value.filter(c=>c.status==="online").length),x=G(()=>p.value.filter(c=>c.status==="offline").length),I=G(()=>p.value.filter(c=>c.status==="error").length);function $(){return V(this,null,function*(){try{o.value=!0,r.value=null,p.value=yield Tt()}catch(c){r.value=c instanceof Error?c:new Error("获取网关设备列表失败"),console.error("获取网关设备列表失败:",c)}finally{o.value=!1}})}function R(c){return V(this,null,function*(){try{o.value=!0,r.value=null;const n=yield Vt(c);if(n){const b=p.value.findIndex(z=>z.id===c);b!==-1?p.value[b]=n:p.value.push(n)}return n}catch(n){return r.value=n instanceof Error?n:new Error(`获取设备 ${c} 信息失败`),console.error(`获取设备 ${c} 信息失败:`,n),null}finally{o.value=!1}})}function y(c){return V(this,null,function*(){var n;t.value=c,yield R(c),yield w(c),(n=D.value)!=null&&n.mqttConfig&&(yield j(c))})}function d(c){return V(this,null,function*(){try{o.value=!0,r.value=null;const n=yield $t(c);return p.value.push(n),n}catch(n){return r.value=n instanceof Error?n:new Error("添加网关设备失败"),console.error("添加网关设备失败:",n),null}finally{o.value=!1}})}function M(c,n){return V(this,null,function*(){try{o.value=!0,r.value=null;const b=yield kt(c,n);if(!b)throw new Error(`更新设备 ${c} 失败`);const z=p.value.findIndex(s=>s.id===c);return z!==-1&&(p.value[z]=b),b}catch(b){return r.value=b instanceof Error?b:new Error(`更新设备 ${c} 失败`),console.error(`更新设备 ${c} 失败:`,b),null}finally{o.value=!1}})}function m(c){return V(this,null,function*(){try{o.value=!0,r.value=null;const n=yield xt(c);return n&&(p.value=p.value.filter(b=>b.id!==c),t.value===c&&(t.value=null)),n}catch(n){return r.value=n instanceof Error?n:new Error(`删除设备 ${c} 失败`),console.error(`删除设备 ${c} 失败:`,n),!1}finally{o.value=!1}})}function f(c){return V(this,null,function*(){try{return o.value=!0,r.value=null,yield Le(c)}catch(n){return r.value=n instanceof Error?n:new Error(`获取设备 ${c} MQTT配置失败`),console.error(`获取设备 ${c} MQTT配置失败:`,n),null}finally{o.value=!1}})}function _(c,n){return V(this,null,function*(){try{return o.value=!0,r.value=null,yield ze(c,n)}catch(b){return r.value=b instanceof Error?b:new Error(`更新设备 ${c} MQTT配置失败`),console.error(`更新设备 ${c} MQTT配置失败:`,b),!1}finally{o.value=!1}})}function P(c){return V(this,null,function*(){var n;try{o.value=!0,r.value=null;const b=p.value.find(s=>s.id===c);if(!(b!=null&&b.mqttConfig))throw new Error("设备MQTT配置不存在");return yield Fe(b.mqttConfig)}catch(b){return r.value=b instanceof Error?b:new Error(`测试设备 ${c} 连接失败`),console.error(`测试设备 ${c} 连接失败:`,b),{success:!1,message:((n=r.value)==null?void 0:n.message)||"未知错误"}}finally{o.value=!1}})}function g(c){return V(this,null,function*(){var n;try{o.value=!0,r.value=null;const b=yield It(c);if(b){const z=p.value.findIndex(s=>s.id===c);z!==-1&&(p.value[z].status="offline",setTimeout(()=>R(c),1e4))}return{success:b,message:b?"重启成功":"重启失败"}}catch(b){return r.value=b instanceof Error?b:new Error(`重启设备 ${c} 失败`),console.error(`重启设备 ${c} 失败:`,b),{success:!1,message:((n=r.value)==null?void 0:n.message)||"未知错误"}}finally{o.value=!1}})}function w(c){return V(this,null,function*(){try{return o.value=!0,r.value=null,k.value=yield Et(c),k.value}catch(n){return r.value=n instanceof Error?n:new Error(`获取设备 ${c} 错误日志失败`),console.error(`获取设备 ${c} 错误日志失败:`,n),[]}finally{o.value=!1}})}function q(c,n){return V(this,null,function*(){try{o.value=!0,r.value=null;const b=yield fe(n);return b&&(T.value=b),b}catch(b){return r.value=b instanceof Error?b:new Error(`获取变频器 ${n} 最新数据失败`),console.error(`获取变频器 ${n} 最新数据失败:`,b),null}finally{o.value=!1}})}function U(c,n,b,z){return V(this,null,function*(){try{o.value=!0,r.value=null;const s=yield fe(n),N=s?[s]:[];l.value[n]||(l.value[n]=[]);const te=new Set(l.value[n].map(de=>de.timestamp)),Oe=N.filter(de=>!te.has(de.timestamp));return l.value[n]=[...l.value[n],...Oe],N}catch(s){return r.value=s instanceof Error?s:new Error(`获取变频器 ${n} 历史数据失败`),console.error(`获取变频器 ${n} 历史数据失败:`,s),[]}finally{o.value=!1}})}function H(c,n,b,z){return V(this,null,function*(){var s;try{o.value=!0,r.value=null;const N=yield qt(n,b,z);return{success:N,message:N?"命令发送成功":"命令发送失败"}}catch(N){return r.value=N instanceof Error?N:new Error(`发送命令到变频器 ${n} 失败`),console.error(`发送命令到变频器 ${n} 失败:`,N),{success:!1,message:((s=r.value)==null?void 0:s.message)||"未知错误"}}finally{o.value=!1}})}function j(c){return V(this,null,function*(){try{const n=yield f(c);if(n){yield X.disconnect();const b=yield X.connect(n);return u.value=X.connected.value,i.value=X.connected.value?"connected":"disconnected",b}return!1}catch(n){return r.value=n instanceof Error?n:new Error(`连接到MQTT设备 ${c} 失败`),console.error(`连接到MQTT设备 ${c} 失败:`,n),!1}})}function A(){X.disconnect(),u.value=!1,i.value="disconnected"}return ee(()=>X.connected.value,c=>{u.value=c,i.value=c?"connected":"disconnected"}),ee(()=>X.lastMessage.value,c=>{if(c)try{const n="default",b={deviceId:n,timestamp:Date.now(),frequency:c.frequency||0,current:c.current||0,voltage:c.voltage||0,power:c.power||0,runningStatus:c.status||!1,temperature:c.temperature||0,errorCode:c.errorCode,errorMessage:c.errorMessage};T.value=b,l.value[n]||(l.value[n]=[]),l.value[n].push(b)}catch(n){console.error("解析MQTT消息失败:",n)}},{deep:!0}),{devices:p,selectedDeviceId:t,deviceData:l,isLoading:o,error:r,mqttStatus:i,mqttConnected:u,lastReceivedData:T,errorLogs:k,selectedDevice:D,onlineDevicesCount:Q,offlineDevicesCount:x,errorDevicesCount:I,fetchAllGateways:$,fetchDeviceById:R,selectDevice:y,addDevice:d,updateDevice:M,deleteDevice:m,fetchMqttConfig:f,updateMqttConfig:_,testConnection:P,restartDevice:g,fetchErrorLogs:w,getInverterLatestData:q,getInverterHistoricalData:U,sendCommand:H,connectToMqttDevice:j,disconnectMqtt:A}}),Mt=ae({__name:"DeviceEditDialog",props:{visible:{type:Boolean},device:{}},emits:["update:visible","submit"],setup(p,{emit:t}){const l=p,o=t,r=C(),i=C(!1),u=ne({name:"",ip:"",port:1883,modelName:"",manufacturer:"",firmwareVersion:"",location:"",description:""}),T={name:[{required:!0,message:"请输入设备名称",trigger:"blur"}],ip:[{required:!0,message:"请输入IP地址",trigger:"blur"},{pattern:/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,message:"IP地址格式不正确",trigger:"blur"}],port:[{required:!0,message:"请输入端口号",trigger:"blur"},{type:"number",min:1,max:65535,message:"端口范围为1-65535",trigger:"blur"}],modelName:[{required:!0,message:"请输入设备型号",trigger:"blur"}],manufacturer:[{required:!0,message:"请输入制造商",trigger:"blur"}]},k=G(()=>{var I;return!!((I=l.device)!=null&&I.id)}),D=G({get:()=>l.visible,set:I=>o("update:visible",I)});ee(()=>l.device,I=>{I?(u.name=I.name,u.ip=I.ip,u.port=I.port,u.modelName=I.modelName||"",u.manufacturer=I.manufacturer||"",u.firmwareVersion=I.firmwareVersion||"",u.location=I.location||"",u.description=I.description||""):Q()},{immediate:!0});function Q(){u.name="",u.ip="",u.port=1883,u.modelName="",u.manufacturer="",u.firmwareVersion="",u.location="",u.description=""}function x(){return V(this,null,function*(){var I;if(r.value)try{i.value=!0,yield r.value.validate();const $={name:u.name,ip:u.ip,port:u.port,modelName:u.modelName,manufacturer:u.manufacturer,firmwareVersion:u.firmwareVersion,location:u.location,description:u.description};k.value&&((I=l.device)!=null&&I.id)&&Object.assign($,{id:l.device.id}),o("submit",$)}catch($){console.error("表单验证失败:",$)}finally{i.value=!1}})}return(I,$)=>{const R=pe,y=Se,d=Pe,M=Re,m=oe,f=me;return S(),W(f,{title:k.value?"编辑设备":"添加设备",modelValue:D.value,"onUpdate:modelValue":$[9]||($[9]=_=>D.value=_),width:"500px","close-on-click-modal":!1},{footer:a(()=>[v("span",null,[e(m,{onClick:$[8]||($[8]=_=>D.value=!1)},{default:a(()=>$[10]||($[10]=[h("取消")])),_:1,__:[10]}),e(m,{type:"primary",loading:i.value,onClick:x},{default:a(()=>$[11]||($[11]=[h(" 确定 ")])),_:1,__:[11]},8,["loading"])])]),default:a(()=>[e(M,{ref_key:"formRef",ref:r,model:u,rules:T,"label-position":"top","label-width":"100px"},{default:a(()=>[e(y,{label:"设备名称",prop:"name"},{default:a(()=>[e(R,{modelValue:u.name,"onUpdate:modelValue":$[0]||($[0]=_=>u.name=_),placeholder:"请输入设备名称"},null,8,["modelValue"])]),_:1}),e(y,{label:"IP地址",prop:"ip"},{default:a(()=>[e(R,{modelValue:u.ip,"onUpdate:modelValue":$[1]||($[1]=_=>u.ip=_),placeholder:"请输入IP地址"},null,8,["modelValue"])]),_:1}),e(y,{label:"端口",prop:"port"},{default:a(()=>[e(d,{modelValue:u.port,"onUpdate:modelValue":$[2]||($[2]=_=>u.port=_),min:1,max:65535,controls:!1,placeholder:"请输入端口号",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(y,{label:"设备型号",prop:"modelName"},{default:a(()=>[e(R,{modelValue:u.modelName,"onUpdate:modelValue":$[3]||($[3]=_=>u.modelName=_),placeholder:"请输入设备型号"},null,8,["modelValue"])]),_:1}),e(y,{label:"制造商",prop:"manufacturer"},{default:a(()=>[e(R,{modelValue:u.manufacturer,"onUpdate:modelValue":$[4]||($[4]=_=>u.manufacturer=_),placeholder:"请输入制造商"},null,8,["modelValue"])]),_:1}),e(y,{label:"固件版本",prop:"firmwareVersion"},{default:a(()=>[e(R,{modelValue:u.firmwareVersion,"onUpdate:modelValue":$[5]||($[5]=_=>u.firmwareVersion=_),placeholder:"请输入固件版本"},null,8,["modelValue"])]),_:1}),e(y,{label:"设备位置",prop:"location"},{default:a(()=>[e(R,{modelValue:u.location,"onUpdate:modelValue":$[6]||($[6]=_=>u.location=_),placeholder:"请输入设备位置"},null,8,["modelValue"])]),_:1}),e(y,{label:"描述",prop:"description"},{default:a(()=>[e(R,{modelValue:u.description,"onUpdate:modelValue":$[7]||($[7]=_=>u.description=_),type:"textarea",rows:"3",placeholder:"请输入设备描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])}}}),Ut=le(Mt,[["__scopeId","data-v-a0d067d8"]]),St={class:"mqtt-config-panel"},Pt={key:0,class:"loading-container"},Rt={key:0,class:"no-config"},Qt={key:1,class:"has-config"},At={class:"form-row"},Bt={class:"form-row"},Nt={class:"form-row"},Lt={class:"form-row"},zt={class:"form-actions"},Ft={class:"test-result"},Ot=ae({__name:"MqttConfigPanel",props:{deviceId:{}},setup(p){const t=p;re();const l=C(!0),o=C(!1),r=C(null),i=ne({brokerUrl:"",port:1883,clientId:"",topicPrefix:"",publishTopic:"",subscribeTopic:"",topics:[],qos:0,reconnectPeriod:5e3,keepAlive:60,username:"",password:""});C("");const u=C(!1),T=C(!1),k=C(null),D=C(""),Q={brokerUrl:[{required:!0,message:"请输入MQTT服务器地址",trigger:"blur"},{pattern:/^mqtt:\/\/\S+/,message:"地址格式应为 mqtt://xxx",trigger:"blur"}],port:[{required:!0,message:"请输入端口号",trigger:"blur"},{type:"number",min:1,max:65535,message:"端口号必须在1-65535之间",trigger:"blur"}],clientId:[{required:!0,message:"请输入客户端ID",trigger:"blur"}],topics:[{required:!0,message:"请至少添加一个主题",trigger:"change"}]},x=()=>V(this,null,function*(){if(t.deviceId)try{l.value=!0;const M=yield Le(t.deviceId);M&&(r.value=M,Object.assign(i,M))}catch(M){console.error("加载MQTT配置失败:",M),F.error(`加载配置失败: ${M.message}`)}finally{l.value=!1}}),I=()=>V(this,null,function*(){if(t.deviceId)try{yield ze(t.deviceId,J({},i)),F.success("MQTT配置保存成功"),r.value=J({},i)}catch(M){console.error("保存MQTT配置失败:",M),F.error("保存MQTT配置失败")}}),$=()=>{const M=t.deviceId?`inverter-${t.deviceId}`:"inverter",m=Math.random().toString(36).substring(2,10);return`${M}-${m}-${Date.now()}`},R=()=>{r.value=J({},i),F.success("配置已创建")},y=()=>{Object.assign(i,{brokerUrl:"",port:1883,clientId:"",topicPrefix:"",publishTopic:"",subscribeTopic:"",topics:[],qos:0,reconnectPeriod:5e3,keepAlive:60,username:"",password:""}),F.success("配置已重置")};se(()=>{t.deviceId?x():l.value=!1});const d=()=>V(this,null,function*(){if(t.deviceId)try{yield Fe(J({},i)),F.success("连接测试成功"),k.value=!0,D.value="连接成功"}catch(M){console.error("测试连接失败:",M),F.error("连接测试失败"),k.value=!1,D.value=M.message}finally{u.value=!1}});return(M,m)=>{const f=Qe,_=oe,P=ge,g=pe,w=Se,q=Pe,U=be,H=ye,j=Re,A=We,c=me;return S(),O("div",St,[l.value?(S(),O("div",Pt,[e(f,{rows:6,animated:""})])):(S(),O(_e,{key:1},[r.value?(S(),O("div",Qt,[e(j,{ref:"formRef",model:i,rules:Q,"label-position":"top"},{default:a(()=>[e(w,{label:"MQTT代理地址",prop:"brokerUrl"},{default:a(()=>[e(g,{modelValue:i.brokerUrl,"onUpdate:modelValue":m[0]||(m[0]=n=>i.brokerUrl=n),placeholder:"例如: mqtt://example.com 或 mqtt://192.168.1.10"},null,8,["modelValue"])]),_:1}),e(w,{label:"端口",prop:"port"},{default:a(()=>[e(q,{modelValue:i.port,"onUpdate:modelValue":m[1]||(m[1]=n=>i.port=n),min:1,max:65535},null,8,["modelValue"])]),_:1}),e(w,{label:"客户端ID",prop:"clientId"},{extra:a(()=>[e(_,{link:"",type:"primary",onClick:m[3]||(m[3]=n=>i.clientId=$())},{default:a(()=>m[15]||(m[15]=[h(" 生成随机ID ")])),_:1,__:[15]})]),default:a(()=>[e(g,{modelValue:i.clientId,"onUpdate:modelValue":m[2]||(m[2]=n=>i.clientId=n),placeholder:"客户端唯一标识符"},null,8,["modelValue"])]),_:1}),v("div",At,[e(w,{label:"用户名",prop:"username"},{default:a(()=>[e(g,{modelValue:i.username,"onUpdate:modelValue":m[4]||(m[4]=n=>i.username=n),placeholder:"用户名（可选）",clearable:""},null,8,["modelValue"])]),_:1}),e(w,{label:"密码",prop:"password"},{default:a(()=>[e(g,{modelValue:i.password,"onUpdate:modelValue":m[5]||(m[5]=n=>i.password=n),placeholder:"密码（可选）",type:"password","show-password":"",clearable:""},null,8,["modelValue"])]),_:1})]),v("div",Bt,[e(w,{label:"主题前缀",prop:"topicPrefix"},{default:a(()=>[e(g,{modelValue:i.topicPrefix,"onUpdate:modelValue":m[6]||(m[6]=n=>i.topicPrefix=n),placeholder:"例如: devices/inverters"},null,8,["modelValue"])]),_:1}),e(w,{label:"QoS",prop:"qos"},{default:a(()=>[e(H,{modelValue:i.qos,"onUpdate:modelValue":m[7]||(m[7]=n=>i.qos=n),placeholder:"服务质量"},{default:a(()=>[e(U,{value:0,label:"0 - 最多一次"}),e(U,{value:1,label:"1 - 至少一次"}),e(U,{value:2,label:"2 - 恰好一次"})]),_:1},8,["modelValue"])]),_:1})]),v("div",Nt,[e(w,{label:"发布主题",prop:"publishTopic"},{default:a(()=>[e(g,{modelValue:i.publishTopic,"onUpdate:modelValue":m[8]||(m[8]=n=>i.publishTopic=n),placeholder:"例如: devices/inverters/{deviceId}/data"},null,8,["modelValue"])]),_:1}),e(w,{label:"订阅主题",prop:"subscribeTopic"},{default:a(()=>[e(g,{modelValue:i.subscribeTopic,"onUpdate:modelValue":m[9]||(m[9]=n=>i.subscribeTopic=n),placeholder:"例如: devices/inverters/{deviceId}/cmd"},null,8,["modelValue"])]),_:1})]),v("div",Lt,[e(w,{label:"保活时间（秒）",prop:"keepAlive"},{default:a(()=>[e(q,{modelValue:i.keepAlive,"onUpdate:modelValue":m[10]||(m[10]=n=>i.keepAlive=n),min:5,max:300,step:5,"controls-position":"right"},null,8,["modelValue"])]),_:1}),e(w,{label:"重连间隔（毫秒）",prop:"reconnectPeriod"},{default:a(()=>[e(q,{modelValue:i.reconnectPeriod,"onUpdate:modelValue":m[11]||(m[11]=n=>i.reconnectPeriod=n),min:1e3,max:3e4,step:1e3,"controls-position":"right"},null,8,["modelValue"])]),_:1})]),v("div",zt,[e(_,{onClick:y},{default:a(()=>m[16]||(m[16]=[h("重置")])),_:1,__:[16]}),e(_,{type:"primary",onClick:I,loading:o.value},{default:a(()=>m[17]||(m[17]=[h("保存配置")])),_:1,__:[17]},8,["loading"]),e(_,{type:"success",onClick:d,loading:u.value},{default:a(()=>m[18]||(m[18]=[h(" 测试连接 ")])),_:1,__:[18]},8,["loading"])])]),_:1},8,["model"])])):(S(),O("div",Rt,[e(P,{description:"没有MQTT配置信息"},{extra:a(()=>[e(_,{type:"primary",onClick:R},{default:a(()=>m[14]||(m[14]=[h("创建配置")])),_:1,__:[14]})]),_:1})]))],64)),e(c,{modelValue:T.value,"onUpdate:modelValue":m[13]||(m[13]=n=>T.value=n),title:"连接测试结果",width:"400px",center:"","close-on-click-modal":!1},{default:a(()=>[v("div",Ft,[k.value!==null?(S(),W(A,{key:0,icon:k.value?"success":"error",title:k.value?"连接成功":"连接失败","sub-title":D.value},{extra:a(()=>[e(_,{onClick:m[12]||(m[12]=n=>T.value=!1)},{default:a(()=>m[19]||(m[19]=[h("关闭")])),_:1,__:[19]})]),_:1},8,["icon","title","sub-title"])):Z("",!0)])]),_:1},8,["modelValue"])])}}}),jt=le(Ot,[["__scopeId","data-v-1f54619e"]]),Gt={class:"device-data-panel"},Ht={key:0,class:"loading-container"},Jt={key:1,class:"no-data"},Wt={class:"auto-refresh"},Xt={class:"last-update"},Zt={class:"data-grid"},Kt={class:"card-header"},Yt={class:"card-header"},el={class:"data-value"},tl={class:"card-header"},ll={class:"data-value"},al={class:"card-header"},ol={class:"data-value"},nl={class:"card-header"},sl={class:"data-value"},rl={class:"card-header"},il={class:"card-header"},ul={class:"data-value"},dl={class:"card-header"},cl={class:"data-value"},vl={class:"card-header"},fl={class:"data-value"},ml={class:"card-header"},pl={class:"data-value"},_l={class:"card-header error"},gl={class:"error-details"},yl={class:"error-code"},bl={class:"error-message"},wl={class:"data-table"},hl=ae({__name:"DeviceDataPanel",props:{deviceId:{}},setup(p){const t=p,l=C(!0),o=C(null),r=C(!1),i=C(5e3),u=C(null),T=C(null),k=G(()=>{if(!o.value)return"";const y=o.value.temperature;return y>80?"critical":y>60?"warning":"normal"}),D=G(()=>{if(!o.value)return[];const y=[{key:"状态",value:o.value.runningStatus?"运行中":"已停止",unit:""},{key:"频率",value:x(o.value.frequency),unit:"Hz"},{key:"电流",value:x(o.value.current),unit:"A"},{key:"电压",value:x(o.value.voltage),unit:"V"},{key:"功率",value:x(o.value.power),unit:"kW"},{key:"温度",value:x(o.value.temperature),unit:"°C"}];return o.value.rotationSpeed!==void 0&&y.push({key:"转速",value:x(o.value.rotationSpeed),unit:"RPM"}),o.value.flowRate!==void 0&&y.push({key:"流量",value:x(o.value.flowRate),unit:"m³/h"}),o.value.pressure!==void 0&&y.push({key:"压力",value:x(o.value.pressure),unit:"MPa"}),o.value.torque!==void 0&&y.push({key:"扭矩",value:x(o.value.torque),unit:"N·m"}),o.value.errorCode&&(y.push({key:"错误代码",value:String(o.value.errorCode),unit:""}),y.push({key:"错误信息",value:o.value.errorMessage||"未知错误",unit:""})),y});se(()=>{Q()}),Xe(()=>{R()}),ee(()=>t.deviceId,()=>{Q()}),ee([r,i],()=>{R(),r.value&&$()});function Q(){return V(this,null,function*(){if(!t.deviceId){o.value=null,l.value=!1;return}try{l.value=!0;const y=yield fe(t.deviceId);y&&(o.value=y,u.value=Date.now())}catch(y){console.error("加载设备数据失败:",y)}finally{l.value=!1}})}function x(y){return y==null?"-":y.toFixed(2)}function I(y){return new Date(y).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",second:"2-digit"})}function $(){T.value=window.setInterval(()=>{Q()},i.value)}function R(){T.value&&(clearInterval(T.value),T.value=null)}return(y,d)=>{const M=Qe,m=oe,f=ge,_=Ae,P=be,g=ye,w=we,q=he,U=ut,H=Ce,j=De;return S(),O("div",Gt,[l.value?(S(),O("div",Ht,[e(M,{rows:6,animated:""})])):o.value?(S(),O(_e,{key:2},[v("div",Wt,[e(_,{modelValue:r.value,"onUpdate:modelValue":d[0]||(d[0]=A=>r.value=A),"inline-prompt":"","active-text":"自动刷新","inactive-text":"手动刷新"},null,8,["modelValue"]),e(g,{modelValue:i.value,"onUpdate:modelValue":d[1]||(d[1]=A=>i.value=A),size:"small",disabled:!r.value,style:{width:"100px","margin-left":"8px"}},{default:a(()=>[e(P,{label:"1秒",value:1e3}),e(P,{label:"3秒",value:3e3}),e(P,{label:"5秒",value:5e3}),e(P,{label:"10秒",value:1e4})]),_:1},8,["modelValue","disabled"]),e(m,{size:"small",onClick:Q,style:{"margin-left":"8px"}},{default:a(()=>[e(w,null,{default:a(()=>[e(B(Ze))]),_:1}),d[3]||(d[3]=h(" 刷新 "))]),_:1,__:[3]}),v("span",Xt," 最后更新: "+E(u.value?I(u.value):"未更新"),1)]),v("div",Zt,[e(q,{class:"data-card",shadow:"hover"},{header:a(()=>[v("div",Kt,[e(w,null,{default:a(()=>[e(B(Ke))]),_:1}),d[4]||(d[4]=v("span",null,"运行状态",-1))])]),default:a(()=>[v("div",{class:Me(["data-value",o.value.runningStatus?"running":"stopped"])},E(o.value.runningStatus?"运行中":"已停止"),3)]),_:1}),e(q,{class:"data-card",shadow:"hover"},{header:a(()=>[v("div",Yt,[e(w,null,{default:a(()=>[e(B(Ye))]),_:1}),d[5]||(d[5]=v("span",null,"电压",-1))])]),default:a(()=>[v("div",el,[h(E(x(o.value.voltage))+" ",1),d[6]||(d[6]=v("span",{class:"unit"},"V",-1))])]),_:1}),e(q,{class:"data-card",shadow:"hover"},{header:a(()=>[v("div",tl,[e(w,null,{default:a(()=>[e(B(et))]),_:1}),d[7]||(d[7]=v("span",null,"电流",-1))])]),default:a(()=>[v("div",ll,[h(E(x(o.value.current))+" ",1),d[8]||(d[8]=v("span",{class:"unit"},"A",-1))])]),_:1}),e(q,{class:"data-card",shadow:"hover"},{header:a(()=>[v("div",al,[e(w,null,{default:a(()=>[e(B(tt))]),_:1}),d[9]||(d[9]=v("span",null,"频率",-1))])]),default:a(()=>[v("div",ol,[h(E(x(o.value.frequency))+" ",1),d[10]||(d[10]=v("span",{class:"unit"},"Hz",-1))])]),_:1}),e(q,{class:"data-card",shadow:"hover"},{header:a(()=>[v("div",nl,[e(w,null,{default:a(()=>[e(B(lt))]),_:1}),d[11]||(d[11]=v("span",null,"功率",-1))])]),default:a(()=>[v("div",sl,[h(E(x(o.value.power))+" ",1),d[12]||(d[12]=v("span",{class:"unit"},"kW",-1))])]),_:1}),e(q,{class:"data-card",shadow:"hover"},{header:a(()=>[v("div",rl,[e(w,null,{default:a(()=>[e(B(at))]),_:1}),d[13]||(d[13]=v("span",null,"温度",-1))])]),default:a(()=>[v("div",{class:Me(["data-value",k.value])},[h(E(x(o.value.temperature))+" ",1),d[14]||(d[14]=v("span",{class:"unit"},"°C",-1))],2)]),_:1}),o.value.rotationSpeed!==void 0?(S(),W(q,{key:0,class:"data-card",shadow:"hover"},{header:a(()=>[v("div",il,[e(w,null,{default:a(()=>[e(B(ot))]),_:1}),d[15]||(d[15]=v("span",null,"转速",-1))])]),default:a(()=>[v("div",ul,[h(E(x(o.value.rotationSpeed))+" ",1),d[16]||(d[16]=v("span",{class:"unit"},"RPM",-1))])]),_:1})):Z("",!0),o.value.flowRate!==void 0?(S(),W(q,{key:1,class:"data-card",shadow:"hover"},{header:a(()=>[v("div",dl,[e(w,null,{default:a(()=>[e(B(nt))]),_:1}),d[17]||(d[17]=v("span",null,"流量",-1))])]),default:a(()=>[v("div",cl,[h(E(x(o.value.flowRate))+" ",1),d[18]||(d[18]=v("span",{class:"unit"},"m³/h",-1))])]),_:1})):Z("",!0),o.value.pressure!==void 0?(S(),W(q,{key:2,class:"data-card",shadow:"hover"},{header:a(()=>[v("div",vl,[e(w,null,{default:a(()=>[e(B(st))]),_:1}),d[19]||(d[19]=v("span",null,"压力",-1))])]),default:a(()=>[v("div",fl,[h(E(x(o.value.pressure))+" ",1),d[20]||(d[20]=v("span",{class:"unit"},"MPa",-1))])]),_:1})):Z("",!0),o.value.torque!==void 0?(S(),W(q,{key:3,class:"data-card",shadow:"hover"},{header:a(()=>[v("div",ml,[e(w,null,{default:a(()=>[e(B(rt))]),_:1}),d[21]||(d[21]=v("span",null,"扭矩",-1))])]),default:a(()=>[v("div",pl,[h(E(x(o.value.torque))+" ",1),d[22]||(d[22]=v("span",{class:"unit"},"N·m",-1))])]),_:1})):Z("",!0)]),o.value.errorCode?(S(),W(q,{key:0,class:"error-info"},{header:a(()=>[v("div",_l,[e(w,null,{default:a(()=>[e(B(it))]),_:1}),d[23]||(d[23]=v("span",null,"错误信息",-1))])]),default:a(()=>[v("div",gl,[v("p",yl,"错误代码: "+E(o.value.errorCode),1),v("p",bl,E(o.value.errorMessage||"未知错误"),1)])]),_:1})):Z("",!0),e(U),v("div",wl,[d[24]||(d[24]=v("h3",null,"原始数据",-1)),e(j,{data:D.value,stripe:"",style:{width:"100%"}},{default:a(()=>[e(H,{prop:"key",label:"参数",width:"180"}),e(H,{prop:"value",label:"数值"}),e(H,{prop:"unit",label:"单位",width:"100"})]),_:1},8,["data"])])],64)):(S(),O("div",Jt,[e(f,{description:"暂无设备数据"},{extra:a(()=>[e(m,{type:"primary",onClick:Q},{default:a(()=>d[2]||(d[2]=[h("刷新数据")])),_:1,__:[2]})]),_:1})]))])}}}),Dl=le(hl,[["__scopeId","data-v-5f70106c"]]),Cl={class:"device-errors-panel"},Tl={class:"error-controls"},Vl={class:"search-filter"},$l={class:"refresh-controls"},kl={class:"error-summary"},xl={class:"summary-value"},El={class:"summary-value error"},Il={class:"summary-value success"},ql={class:"error-table"},Ml={key:0,class:"no-errors"},Ul=ae({__name:"DeviceErrorsPanel",props:{deviceId:{}},setup(p){const t=p,l=re(),o=C(!1),r=C([]),i=C(!1),u=C(1e4),T=C(""),k=C(!1);let D=null;const Q=()=>V(this,null,function*(){o.value=!0;try{r.value=yield l.fetchErrorLogs(t.deviceId)}catch(f){F.error("加载错误日志失败"),console.error("加载错误日志失败:",f)}finally{o.value=!1}}),x=()=>{I(),D=setInterval(()=>{Q()},u.value)},I=()=>{D&&(clearInterval(D),D=null)},$=f=>{f?x():I()},R=()=>{i.value&&x()},y=G(()=>{let f=[...r.value];if(T.value){const _=T.value.toLowerCase();f=f.filter(P=>P.errorMessage.toLowerCase().includes(_)||P.errorCode.toString().includes(_))}return k.value&&(f=f.filter(_=>!_.resolved)),f}),d=f=>f?(typeof f=="number"?new Date(f):typeof f=="string"?new Date(f):f).toLocaleString("zh-CN"):"--",M=f=>f>=5e3?"danger":f>=4e3?"warning":(f>=3e3,"info"),m=()=>{let f=`Code,Message,Timestamp,Resolved
`;r.value.forEach(w=>{const q=[w.errorCode,`"${w.errorMessage.replace(/"/g,'""')}"`,d(w.timestamp),w.resolved?"是":"否"].join(",");f+=q+`
`});const _=new Blob([f],{type:"text/csv;charset=utf-8;"}),P=document.createElement("a"),g=URL.createObjectURL(_);P.setAttribute("href",g),P.setAttribute("download",`device-${t.deviceId}-errors.csv`),P.style.visibility="hidden",document.body.appendChild(P),P.click(),document.body.removeChild(P)};return se(()=>{Q()}),Be(()=>{I()}),(f,_)=>{const P=pe,g=Ne,w=Ae,q=be,U=ye,H=oe,j=he,A=Ce,c=$e,n=De,b=ge,z=Ve;return Te((S(),O("div",Cl,[v("div",Tl,[v("div",Vl,[e(P,{modelValue:T.value,"onUpdate:modelValue":_[0]||(_[0]=s=>T.value=s),placeholder:"搜索错误信息或代码",clearable:"","prefix-icon":"Search"},null,8,["modelValue"]),e(g,{modelValue:k.value,"onUpdate:modelValue":_[1]||(_[1]=s=>k.value=s),style:{"margin-left":"10px"}},{default:a(()=>_[4]||(_[4]=[h(" 只显示未解决错误 ")])),_:1,__:[4]},8,["modelValue"])]),v("div",$l,[e(w,{modelValue:i.value,"onUpdate:modelValue":_[2]||(_[2]=s=>i.value=s),"inline-prompt":"","active-text":"自动刷新","inactive-text":"关闭",onChange:$},null,8,["modelValue"]),e(U,{modelValue:u.value,"onUpdate:modelValue":_[3]||(_[3]=s=>u.value=s),size:"small",style:{width:"110px","margin-left":"10px"},disabled:!i.value,onChange:R},{default:a(()=>[e(q,{label:"5秒",value:5e3}),e(q,{label:"10秒",value:1e4}),e(q,{label:"30秒",value:3e4}),e(q,{label:"1分钟",value:6e4})]),_:1},8,["modelValue","disabled"]),e(H,{size:"small",onClick:Q,style:{"margin-left":"10px"}},{default:a(()=>_[5]||(_[5]=[h("刷新")])),_:1,__:[5]}),e(H,{size:"small",type:"primary",onClick:m,style:{"margin-left":"10px"}},{default:a(()=>_[6]||(_[6]=[h("导出")])),_:1,__:[6]})])]),v("div",kl,[e(j,{class:"summary-card"},{default:a(()=>[_[7]||(_[7]=v("div",{class:"summary-title"},"总错误数",-1)),v("div",xl,E(r.value.length),1)]),_:1,__:[7]}),e(j,{class:"summary-card"},{default:a(()=>[_[8]||(_[8]=v("div",{class:"summary-title"},"未解决错误",-1)),v("div",El,E(r.value.filter(s=>!s.resolved).length),1)]),_:1,__:[8]}),e(j,{class:"summary-card"},{default:a(()=>[_[9]||(_[9]=v("div",{class:"summary-title"},"已解决错误",-1)),v("div",Il,E(r.value.filter(s=>s.resolved).length),1)]),_:1,__:[9]})]),v("div",ql,[e(n,{data:y.value,border:"",stripe:"",style:{width:"100%"},"empty-text":o.value?"加载中...":"暂无错误日志"},{default:a(()=>[e(A,{prop:"errorCode",label:"错误代码",width:"100",sortable:""}),e(A,{label:"严重程度",width:"120"},{default:a(({row:s})=>[e(c,{type:M(s.errorCode),size:"small"},{default:a(()=>[h(E(s.errorCode>=5e3?"严重":s.errorCode>=4e3?"警告":"信息"),1)]),_:2},1032,["type"])]),_:1}),e(A,{prop:"errorMessage",label:"错误信息","min-width":"200","show-overflow-tooltip":""}),e(A,{label:"时间",width:"180",sortable:""},{default:a(({row:s})=>[h(E(d(new Date(s.timestamp))),1)]),_:1}),e(A,{label:"设备ID",width:"200","show-overflow-tooltip":""},{default:a(({row:s})=>[h(E(s.deviceId||"--"),1)]),_:1}),e(A,{label:"状态",width:"100"},{default:a(({row:s})=>[e(c,{type:s.resolved?"success":"danger",size:"small"},{default:a(()=>[h(E(s.resolved?"已解决":"未解决"),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data","empty-text"])]),!o.value&&r.value.length===0?(S(),O("div",Ml,[e(b,{description:"暂无错误日志"})])):Z("",!0)])),[[z,o.value]])}}}),Sl=le(Ul,[["__scopeId","data-v-1ffb24d7"]]),Pl={class:"drawer-header"},Rl={class:"description-text"},Ql={class:"actions"},Al={class:"drawer-footer"},Bl=ae({__name:"DeviceDetailDrawer",props:{visible:{type:Boolean},device:{}},emits:["update:visible","update","delete","connect","disconnect","restart"],setup(p,{emit:t}){const l=p,o=t,r=re(),i=C("info"),u=C(!1),T=C(5e3);let k=null;const D=G(()=>r.selectedDevice);G(()=>r.mqttConnected),G(()=>r.mqttStatus),G(()=>r.lastReceivedData),ee(()=>{var g;return(g=l.device)==null?void 0:g.id},g=>V(this,null,function*(){g&&(yield r.selectDevice(g))}),{immediate:!0});const Q=()=>{o("update:visible",!1),y()},x=()=>{var g;(g=l.device)!=null&&g.id&&ue.confirm(`确定要删除设备 ${l.device.name} 吗？此操作不可恢复。`,"删除设备",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{l.device&&o("delete",l.device.id),Q()}).catch(()=>{})},I=()=>{var g;(g=l.device)!=null&&g.id&&ue.confirm(`确定要重启设备 ${l.device.name} 吗？`,"重启设备",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{l.device&&o("restart",l.device.id)}).catch(()=>{})},$=()=>V(this,null,function*(){var g;(g=l.device)!=null&&g.id&&(yield r.fetchDeviceById(l.device.id))}),R=()=>{y(),k=setInterval(()=>{$()},T.value)},y=()=>{k&&(clearInterval(k),k=null)};ee(u,g=>{g?R():y()});const d=g=>g?(typeof g=="string"?new Date(g):g).toLocaleString("zh-CN"):"--";Be(()=>{y()});const M=()=>{},m=()=>{var g;(g=l.device)!=null&&g.id&&(l.device.status==="online"?o("disconnect",l.device.id):o("connect",l.device.id))},f=g=>{var w;(w=l.device)!=null&&w.id&&o("update",l.device.id,{mqttConfig:g})},_=g=>{switch(g){case"online":return"success";case"offline":return"info";case"error":return"danger";default:return"info"}},P=g=>{switch(g){case"online":return"在线";case"offline":return"离线";case"error":return"错误";default:return"未知"}};return(g,w)=>{var z;const q=$e,U=ft,H=vt,j=we,A=oe,c=ct,n=_t,b=dt;return S(),W(b,{modelValue:g.visible,"onUpdate:modelValue":w[1]||(w[1]=s=>g.visible=s),title:`设备详情: ${(z=D.value)==null?void 0:z.name}`,direction:"rtl",size:"600px","destroy-on-close":"",onClose:Q},{header:a(()=>{var s,N;return[v("div",Pl,[v("h2",null,E((s=D.value)==null?void 0:s.name),1),e(q,{type:_((N=D.value)==null?void 0:N.status),effect:"light"},{default:a(()=>{var te;return[h(E(P((te=D.value)==null?void 0:te.status)),1)]}),_:1},8,["type"])])]}),footer:a(()=>{var s,N;return[v("div",Al,[e(A,{onClick:Q},{default:a(()=>w[4]||(w[4]=[h("关闭")])),_:1,__:[4]}),e(A,{type:((s=D.value)==null?void 0:s.status)==="online"?"danger":"primary",onClick:m},{default:a(()=>{var te;return[h(E(((te=D.value)==null?void 0:te.status)==="online"?"断开连接":"连接设备"),1)]}),_:1},8,["type"]),e(A,{type:"warning",disabled:((N=D.value)==null?void 0:N.status)!=="online",onClick:I},{default:a(()=>w[5]||(w[5]=[h(" 重启设备 ")])),_:1,__:[5]},8,["disabled"])])]}),default:a(()=>[e(n,{modelValue:i.value,"onUpdate:modelValue":w[0]||(w[0]=s=>i.value=s),type:"border-card"},{default:a(()=>[e(c,{label:"基本信息",name:"info"},{default:a(()=>[e(H,{column:1,border:""},{default:a(()=>[e(U,{label:"设备ID"},{default:a(()=>{var s;return[h(E((s=D.value)==null?void 0:s.id),1)]}),_:1}),e(U,{label:"设备名称"},{default:a(()=>{var s;return[h(E((s=D.value)==null?void 0:s.name),1)]}),_:1}),e(U,{label:"IP地址"},{default:a(()=>{var s;return[h(E((s=D.value)==null?void 0:s.ip),1)]}),_:1}),e(U,{label:"端口"},{default:a(()=>{var s;return[h(E((s=D.value)==null?void 0:s.port),1)]}),_:1}),e(U,{label:"设备型号"},{default:a(()=>{var s;return[h(E(((s=D.value)==null?void 0:s.modelName)||"-"),1)]}),_:1}),e(U,{label:"制造商"},{default:a(()=>{var s;return[h(E(((s=D.value)==null?void 0:s.manufacturer)||"-"),1)]}),_:1}),e(U,{label:"固件版本"},{default:a(()=>{var s;return[h(E(((s=D.value)==null?void 0:s.firmwareVersion)||"-"),1)]}),_:1}),e(U,{label:"位置"},{default:a(()=>{var s;return[h(E(((s=D.value)==null?void 0:s.location)||"-"),1)]}),_:1}),e(U,{label:"最后连接时间"},{default:a(()=>{var s;return[h(E((s=D.value)!=null&&s.lastConnectTime?d(D.value.lastConnectTime):"未连接过"),1)]}),_:1}),e(U,{label:"设备描述",span:1},{default:a(()=>{var s;return[v("div",Rl,E(((s=D.value)==null?void 0:s.description)||"无描述"),1)]}),_:1})]),_:1}),v("div",Ql,[e(A,{type:"primary",onClick:M},{default:a(()=>[e(j,null,{default:a(()=>[e(B(mt))]),_:1}),w[2]||(w[2]=h(" 编辑信息 "))]),_:1,__:[2]}),e(A,{type:"danger",onClick:x},{default:a(()=>[e(j,null,{default:a(()=>[e(B(pt))]),_:1}),w[3]||(w[3]=h(" 删除设备 "))]),_:1,__:[3]})])]),_:1}),e(c,{label:"实时数据",name:"data"},{default:a(()=>{var s;return[e(Dl,{"device-id":(s=D.value)==null?void 0:s.id},null,8,["device-id"])]}),_:1}),e(c,{label:"MQTT配置",name:"mqtt"},{default:a(()=>{var s;return[e(jt,{"device-id":(s=D.value)==null?void 0:s.id,onUpdate:f},null,8,["device-id"])]}),_:1}),e(c,{label:"错误日志",name:"errors"},{default:a(()=>{var s;return[e(Sl,{"device-id":((s=D.value)==null?void 0:s.id)||""},null,8,["device-id"])]}),_:1})]),_:1},8,["modelValue"])]),_:1},8,["modelValue","title"])}}}),Nl=le(Bl,[["__scopeId","data-v-446dc5b8"]]),Ll={class:"test-connection-dialog"},zl={class:"status-messages"},Fl={key:0,class:"test-result"},Ol={class:"auto-close-option"},Ue=3e3,jl=ae({__name:"TestConnectionDialog",props:{visible:{type:Boolean},deviceId:{}},emits:["update:visible"],setup(p,{emit:t}){const l=p,o=t,r=re(),i=C(!1),u=C(null),T=C([]),k=C(!1);let D=null;const Q=()=>V(this,null,function*(){if(!l.deviceId){F.error("缺少设备ID");return}i.value=!0,T.value=["正在初始化测试..."],u.value=null;try{x("正在获取设备信息..."),yield r.fetchDeviceById(l.deviceId),x("正在获取MQTT配置...");const y=yield r.fetchMqttConfig(l.deviceId);x("正在测试MQTT连接...");const d=yield r.testConnection(l.deviceId);u.value=d,x(d.success?`测试成功: ${d.message}`:`测试失败: ${d.message}`),d.success&&y&&(x(`正在尝试订阅主题: ${y.topics.join(", ")}`),yield new Promise(M=>setTimeout(M,1e3)),x("主题订阅成功"),x("测试完成")),k.value&&d.success&&$()}catch(y){const d=y instanceof Error?y.message:"未知错误";x(`测试过程中发生错误: ${d}`),u.value={success:!1,message:d}}finally{i.value=!1}}),x=y=>{T.value.push(y),setTimeout(()=>{const d=document.querySelector(".status-messages");d&&(d.scrollTop=d.scrollHeight)},100)},I=()=>{o("update:visible",!1),R()},$=()=>{R(),x(`${Ue/1e3} 秒后自动关闭...`),D=setTimeout(()=>{I()},Ue)},R=()=>{D&&(clearTimeout(D),D=null)};return ee(()=>l.visible,y=>{y&&l.deviceId?(T.value=[],u.value=null,Q()):R()}),se(()=>{l.visible&&l.deviceId&&Q()}),(y,d)=>{const M=yt,m=Ne,f=oe,_=me,P=Ve;return S(),W(_,{title:"测试设备连接",modelValue:y.visible,"onUpdate:modelValue":d[1]||(d[1]=g=>y.visible=g),width:"500px",onClose:I,"destroy-on-close":"","close-on-click-modal":!1},{footer:a(()=>[e(f,{onClick:I},{default:a(()=>d[3]||(d[3]=[h("关闭")])),_:1,__:[3]}),e(f,{type:"primary",onClick:Q,loading:i.value},{default:a(()=>d[4]||(d[4]=[h("重新测试")])),_:1,__:[4]},8,["loading"])]),default:a(()=>[Te((S(),O("div",Ll,[v("div",zl,[(S(!0),O(_e,null,gt(T.value,(g,w)=>(S(),O("div",{key:w,class:"status-message"},E(g),1))),128))]),u.value?(S(),O("div",Fl,[e(M,{title:u.value.success?"连接测试成功":"连接测试失败",type:u.value.success?"success":"error",description:u.value.message,"show-icon":""},null,8,["title","type","description"])])):Z("",!0),v("div",Ol,[e(m,{modelValue:k.value,"onUpdate:modelValue":d[0]||(d[0]=g=>k.value=g)},{default:a(()=>d[2]||(d[2]=[h("测试成功后自动关闭")])),_:1,__:[2]},8,["modelValue"])])])),[[P,i.value]])]),_:1},8,["modelValue"])}}}),Gl=le(jl,[["__scopeId","data-v-24065ad7"]]),Hl={class:"gateway-management"},Jl={class:"page-header"},Wl={class:"actions"},Xl={class:"statistics-cards"},Zl={class:"stat-value"},Kl={class:"stat-value"},Yl={class:"stat-value"},ea={class:"stat-value"},ta=ae({__name:"index",setup(p){const t=re(),l=C(!1),o=C(!1),r=C(!1),i=C(null),u=C(null),T=C(!0);G(()=>t.mqttStatus),G(()=>t.mqttConnected);const k=()=>V(this,null,function*(){T.value=!0;try{yield t.fetchAllGateways()}catch(m){F.error("获取网关设备列表失败")}finally{T.value=!1}}),D=()=>{i.value={id:"",name:"",ip:"",port:502,modelName:"",manufacturer:"",firmwareVersion:"",status:"offline"},l.value=!0},Q=m=>{i.value=J({},m),l.value=!0},x=m=>V(this,null,function*(){u.value=m,yield t.selectDevice(m),o.value=!0}),I=m=>V(this,null,function*(){m.id?(yield t.updateDevice(m.id,m))&&(F.success("更新设备成功"),l.value=!1):(yield t.addDevice(m))&&(F.success("添加设备成功"),l.value=!1)}),$=m=>V(this,null,function*(){try{yield ue.confirm("确认删除此设备？此操作不可恢复。","确认删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}),(yield t.deleteDevice(m))&&F.success("设备已成功删除")}catch(f){f!=="cancel"&&F.error("删除设备失败")}}),R=m=>V(this,null,function*(){u.value=m,r.value=!0}),y=m=>V(this,null,function*(){try{yield ue.confirm("确认重启此设备？","确认重启",{confirmButtonText:"重启",cancelButtonText:"取消",type:"warning"});const f=yield t.restartDevice(m);f.success?F.success(f.message||"设备正在重启"):F.error(f.message||"重启设备失败")}catch(f){f!=="cancel"&&F.error("重启设备失败")}}),d=m=>{switch(m){case"online":return"success";case"offline":return"info";case"error":return"danger";default:return"warning"}},M=m=>m?(typeof m=="string"?new Date(m):m).toLocaleString("zh-CN"):"--";return se(()=>{k()}),(m,f)=>{const _=ve("Plus"),P=we,g=oe,w=ve("Refresh"),q=he,U=Ce,H=$e,j=ve("arrow-down"),A=ht,c=wt,n=bt,b=De,z=Ve;return S(),O("div",Hl,[v("div",Jl,[f[5]||(f[5]=v("h2",null,"变频器网关设备管理",-1)),v("div",Wl,[e(g,{type:"primary",onClick:D},{default:a(()=>[e(P,null,{default:a(()=>[e(_)]),_:1}),f[3]||(f[3]=h(" 添加设备 "))]),_:1,__:[3]}),e(g,{onClick:k},{default:a(()=>[e(P,null,{default:a(()=>[e(w)]),_:1}),f[4]||(f[4]=h(" 刷新 "))]),_:1,__:[4]})])]),v("div",Xl,[e(q,{class:"stat-card"},{default:a(()=>[f[6]||(f[6]=v("div",{class:"stat-title"},"设备总数",-1)),v("div",Zl,E(B(t).devices.length),1)]),_:1,__:[6]}),e(q,{class:"stat-card online"},{default:a(()=>[f[7]||(f[7]=v("div",{class:"stat-title"},"在线设备",-1)),v("div",Kl,E(B(t).onlineDevicesCount),1)]),_:1,__:[7]}),e(q,{class:"stat-card offline"},{default:a(()=>[f[8]||(f[8]=v("div",{class:"stat-title"},"离线设备",-1)),v("div",Yl,E(B(t).offlineDevicesCount),1)]),_:1,__:[8]}),e(q,{class:"stat-card error"},{default:a(()=>[f[9]||(f[9]=v("div",{class:"stat-title"},"故障设备",-1)),v("div",ea,E(B(t).errorDevicesCount),1)]),_:1,__:[9]})]),e(q,{class:"data-table-card"},{default:a(()=>[Te((S(),W(b,{data:B(t).devices,border:"",style:{width:"100%"}},{default:a(()=>[e(U,{prop:"name",label:"设备名称","min-width":"150"}),e(U,{prop:"protocol",label:"协议",width:"100"}),e(U,{prop:"ip",label:"IP地址","min-width":"120"}),e(U,{prop:"port",label:"端口",width:"100"}),e(U,{label:"状态",width:"100"},{default:a(({row:s})=>[e(H,{type:d(s.status)},{default:a(()=>[h(E(s.status==="online"?"在线":s.status==="offline"?"离线":"故障"),1)]),_:2},1032,["type"])]),_:1}),e(U,{label:"最近连接时间","min-width":"180"},{default:a(({row:s})=>[h(E(M(s.lastConnected)),1)]),_:1}),e(U,{label:"创建时间","min-width":"180"},{default:a(({row:s})=>[h(E(M(s.createdAt)),1)]),_:1}),e(U,{label:"操作",fixed:"right",width:"260"},{default:a(({row:s})=>[e(g,{size:"small",onClick:N=>x(s.id)},{default:a(()=>f[10]||(f[10]=[h("查看")])),_:2,__:[10]},1032,["onClick"]),e(g,{size:"small",type:"primary",onClick:N=>Q(s)},{default:a(()=>f[11]||(f[11]=[h("编辑")])),_:2,__:[11]},1032,["onClick"]),e(g,{size:"small",type:"success",onClick:N=>R(s.id)},{default:a(()=>f[12]||(f[12]=[h("测试连接")])),_:2,__:[12]},1032,["onClick"]),e(n,null,{dropdown:a(()=>[e(c,null,{default:a(()=>[e(A,{onClick:N=>y(s.id)},{default:a(()=>f[14]||(f[14]=[h("重启设备")])),_:2,__:[14]},1032,["onClick"]),e(A,{onClick:N=>$(s.id),divided:""},{default:a(()=>f[15]||(f[15]=[v("span",{class:"text-danger"},"删除设备",-1)])),_:2,__:[15]},1032,["onClick"])]),_:2},1024)]),default:a(()=>[e(g,{size:"small"},{default:a(()=>[f[13]||(f[13]=h(" 更多")),e(P,{class:"el-icon--right"},{default:a(()=>[e(j)]),_:1})]),_:1,__:[13]})]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[z,T.value]])]),_:1}),l.value?(S(),W(Ut,{key:0,visible:l.value,"onUpdate:visible":f[0]||(f[0]=s=>l.value=s),device:i.value,onSave:I},null,8,["visible","device"])):Z("",!0),o.value&&B(t).selectedDevice?(S(),W(Nl,{key:1,visible:o.value,device:B(t).selectedDevice,onClose:f[1]||(f[1]=s=>o.value=!1)},null,8,["visible","device"])):Z("",!0),r.value?(S(),W(Gl,{key:2,visible:r.value,"onUpdate:visible":f[2]||(f[2]=s=>r.value=s),"device-id":u.value},null,8,["visible","device-id"])):Z("",!0)])}}}),ha=le(ta,[["__scopeId","data-v-82a8ef4d"]]);export{ha as default};
