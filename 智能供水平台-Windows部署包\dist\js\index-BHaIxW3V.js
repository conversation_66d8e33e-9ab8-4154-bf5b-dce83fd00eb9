var Te=Object.defineProperty;var pe=Object.getOwnPropertySymbols;var He=Object.prototype.hasOwnProperty,qe=Object.prototype.propertyIsEnumerable;var _e=(A,p,_)=>p in A?Te(A,p,{enumerable:!0,configurable:!0,writable:!0,value:_}):A[p]=_,ce=(A,p)=>{for(var _ in p||(p={}))He.call(p,_)&&_e(A,_,p[_]);if(pe)for(var _ of pe(p))qe.call(p,_)&&_e(A,_,p[_]);return A};var N=(A,p,_)=>new Promise((C,k)=>{var D=i=>{try{c(_.next(i))}catch(r){k(r)}},h=i=>{try{c(_.throw(i))}catch(r){k(r)}},c=i=>i.done?C(i.value):Promise.resolve(i.value).then(D,h);c((_=_.apply(A,p)).next())});import{_ as re}from"./_plugin-vue_export-helper-C6jw_E77.js";/* empty css                    *//* empty css                        *//* empty css                     *//* empty css                   *//* empty css               *//* empty css                     *//* empty css                  *//* empty css                        *//* empty css                  *//* empty css                        *//* empty css                   *//* empty css                         *//* empty css                */import{d as ie,r as Q,c as Y,U as ge,o as se,w as a,e,f as o,av as be,aw as ye,aa as Ae,V as ue,Y as de,ab as he,am as ke,ax as Ve,n as Ee,v as d,E as Qe,h as q,ay as De,az as Ue,a1 as we,a2 as Pe,g as le,b as X,ac as Re,ad as Fe,F as Ce,aA as ze,y as $e,aB as Me,aC as Ie,aD as Oe,ap as R,au as Be,S as me,L as M,O as xe,P as Se,a9 as Ne,x as g,Z as oe,aE as Le,Q as Ge,aF as We,aG as je,k as Je,aH as Ke,aI as Ye,a0 as fe,aJ as Ze,aK as ve,aL as Xe,a4 as ea,a5 as aa,B as la,D as ta,G as oa,H as na,R as sa,af as ra,ar as ia}from"./index-8zz4iTME.js";import{u as ua,i as da,a as ma}from"./chart-config-xwwLzgEh.js";/* empty css                    *//* empty css                  *//* empty css                  *//* empty css                             *//* empty css                 */import{i as ne}from"./index-_zIlD_l3.js";import"./chart-axis-config-BMlDw7JO.js";const pa={class:"card-header"},_a={class:"header-actions"},ca={class:"curve-data-section"},fa={class:"section-header"},va={class:"actions"},ga={class:"footer-actions"},ba=ie({__name:"PumpParameterInput",props:{modelValue:{type:Boolean}},emits:["update:modelValue","parameters-updated"],setup(A,{emit:p}){const _=p,C=Q("basic"),k=Q(!1),D=Q(!1),h=Q(),c=Q({name:"离心泵-001",model:"IS100-80-160",ratedFlow:1e3,ratedHead:40,ratedPower:100,ratedSpeed:2900,efficiency:78,impellerDiameter:160}),i=Q([{Q:0,H:44.28,ETA:0,P:76},{Q:200,H:43.58,ETA:28,P:84},{Q:400,H:42.35,ETA:48,P:91},{Q:600,H:40.42,ETA:63,P:100},{Q:800,H:37.25,ETA:75,P:105},{Q:1e3,H:33.56,ETA:81,P:108},{Q:1200,H:29.17,ETA:83,P:109},{Q:1400,H:23.72,ETA:81,P:107}]),r=Q({algorithm:{type:"least-squares",order:2,epochs:100,learningRate:.01,hiddenLayers:[10,5]},validationSplit:.2,crossValidation:!1}),E=Q("10,5"),L={name:[{required:!0,message:"请输入水泵名称",trigger:"blur"}],model:[{required:!0,message:"请输入型号",trigger:"blur"}],ratedFlow:[{required:!0,message:"请输入额定流量",trigger:"blur"}],ratedHead:[{required:!0,message:"请输入额定扬程",trigger:"blur"}],ratedPower:[{required:!0,message:"请输入额定功率",trigger:"blur"}],ratedSpeed:[{required:!0,message:"请输入额定转速",trigger:"blur"}],efficiency:[{required:!0,message:"请输入额定效率",trigger:"blur"}],impellerDiameter:[{required:!0,message:"请输入叶轮直径",trigger:"blur"}]},ee=Y(()=>["least-squares","polynomial"].includes(r.value.algorithm.type)),f=()=>{const y=i.value[i.value.length-1];i.value.push({Q:y.Q+100,H:y.H-2,ETA:Math.max(0,y.ETA-5),P:y.P+5})},m=y=>{i.value.length>3&&i.value.splice(y,1)},u=y=>{const l=i.value[y];(l.Q<0||l.H<0||l.ETA<0||l.ETA>100||l.P<0)&&R.warning("数据点参数超出合理范围")},v=()=>{R.info("数据导入功能开发中...")},I=()=>{r.value.algorithm.type==="neural-network"?(r.value.algorithm.epochs=1e3,r.value.algorithm.learningRate=.01,r.value.algorithm.hiddenLayers=[10,5]):r.value.algorithm.type==="polynomial"&&(r.value.algorithm.order=4)},G=()=>{try{const y=E.value.split(",").map(l=>parseInt(l.trim())).filter(l=>!isNaN(l)&&l>0);y.length>0&&(r.value.algorithm.hiddenLayers=y)}catch(y){R.warning("隐藏层配置格式错误")}},S=()=>{Be.confirm("确定要重置为默认参数吗？","确认重置",{type:"warning"}).then(()=>{Object.assign(c.value,{name:"离心泵-001",model:"IS100-80-160",ratedFlow:1e3,ratedHead:40,ratedPower:100,ratedSpeed:2900,efficiency:78,impellerDiameter:160}),i.value=[{Q:0,H:44.28,ETA:0,P:76},{Q:200,H:43.58,ETA:28,P:84},{Q:400,H:42.35,ETA:48,P:91},{Q:600,H:40.42,ETA:63,P:100},{Q:800,H:37.25,ETA:75,P:105},{Q:1e3,H:33.56,ETA:81,P:108},{Q:1200,H:29.17,ETA:83,P:109},{Q:1400,H:23.72,ETA:81,P:107}],R.success("已重置为默认参数")})},b=()=>{R.success("参数配置已保存")},t=()=>N(this,null,function*(){k.value=!0;try{yield new Promise(y=>setTimeout(y,1e3)),R.success("曲线预览生成成功")}finally{k.value=!1}}),O=()=>N(this,null,function*(){var l;if(!(yield(l=h.value)==null?void 0:l.validate().catch(()=>!1))){C.value="basic";return}if(i.value.length<3){R.error("至少需要3个数据点"),C.value="curve";return}D.value=!0;try{const T={Q:i.value.map(n=>n.Q),H:i.value.map(n=>n.H),ETA:i.value.map(n=>n.ETA),P:i.value.map(n=>n.P)},x={algorithm:r.value.algorithm,validationSplit:r.value.validationSplit,crossValidation:r.value.crossValidation};_("parameters-updated",c.value,T,x),R.success("参数应用成功")}finally{D.value=!1}});return ge(()=>r.value.algorithm.hiddenLayers,y=>{E.value=(y==null?void 0:y.join(","))||""},{immediate:!0}),(y,l)=>{const T=Qe,x=Ee,n=ke,V=he,z=de,W=ue,w=Ve,U=Ae,j=ye,B=Pe,ae=we,$=Fe,Z=Re,te=ze,P=$e,J=be,K=me;return M(),se(K,{class:"pump-parameter-input"},{header:a(()=>[o("div",pa,[e(T,null,{default:a(()=>[e(q(Oe))]),_:1}),l[18]||(l[18]=o("span",null,"水泵参数配置",-1)),o("div",_a,[e(x,{size:"small",onClick:S},{default:a(()=>l[16]||(l[16]=[d("重置默认")])),_:1,__:[16]}),e(x,{size:"small",type:"primary",onClick:b},{default:a(()=>l[17]||(l[17]=[d("保存配置")])),_:1,__:[17]})])])]),default:a(()=>[e(J,{modelValue:C.value,"onUpdate:modelValue":l[15]||(l[15]=s=>C.value=s),type:"border-card"},{default:a(()=>[e(j,{label:"基础参数",name:"basic"},{default:a(()=>[e(U,{model:c.value,"label-width":"120px",rules:L,ref_key:"basicFormRef",ref:h},{default:a(()=>[e(W,{gutter:20},{default:a(()=>[e(z,{span:12},{default:a(()=>[e(V,{label:"水泵名称",prop:"name"},{default:a(()=>[e(n,{modelValue:c.value.name,"onUpdate:modelValue":l[0]||(l[0]=s=>c.value.name=s),placeholder:"请输入水泵名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(z,{span:12},{default:a(()=>[e(V,{label:"型号",prop:"model"},{default:a(()=>[e(n,{modelValue:c.value.model,"onUpdate:modelValue":l[1]||(l[1]=s=>c.value.model=s),placeholder:"请输入型号"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(W,{gutter:20},{default:a(()=>[e(z,{span:12},{default:a(()=>[e(V,{label:"额定流量",prop:"ratedFlow"},{default:a(()=>[e(w,{modelValue:c.value.ratedFlow,"onUpdate:modelValue":l[2]||(l[2]=s=>c.value.ratedFlow=s),min:0,max:5e3,step:10,"controls-position":"right"},null,8,["modelValue"]),l[19]||(l[19]=o("span",{class:"unit"},"m³/h",-1))]),_:1,__:[19]})]),_:1}),e(z,{span:12},{default:a(()=>[e(V,{label:"额定扬程",prop:"ratedHead"},{default:a(()=>[e(w,{modelValue:c.value.ratedHead,"onUpdate:modelValue":l[3]||(l[3]=s=>c.value.ratedHead=s),min:0,max:200,step:1,precision:1,"controls-position":"right"},null,8,["modelValue"]),l[20]||(l[20]=o("span",{class:"unit"},"m",-1))]),_:1,__:[20]})]),_:1})]),_:1}),e(W,{gutter:20},{default:a(()=>[e(z,{span:12},{default:a(()=>[e(V,{label:"额定功率",prop:"ratedPower"},{default:a(()=>[e(w,{modelValue:c.value.ratedPower,"onUpdate:modelValue":l[4]||(l[4]=s=>c.value.ratedPower=s),min:0,max:1e3,step:1,precision:1,"controls-position":"right"},null,8,["modelValue"]),l[21]||(l[21]=o("span",{class:"unit"},"kW",-1))]),_:1,__:[21]})]),_:1}),e(z,{span:12},{default:a(()=>[e(V,{label:"额定转速",prop:"ratedSpeed"},{default:a(()=>[e(w,{modelValue:c.value.ratedSpeed,"onUpdate:modelValue":l[5]||(l[5]=s=>c.value.ratedSpeed=s),min:0,max:5e3,step:10,"controls-position":"right"},null,8,["modelValue"]),l[22]||(l[22]=o("span",{class:"unit"},"rpm",-1))]),_:1,__:[22]})]),_:1})]),_:1}),e(W,{gutter:20},{default:a(()=>[e(z,{span:12},{default:a(()=>[e(V,{label:"额定效率",prop:"efficiency"},{default:a(()=>[e(w,{modelValue:c.value.efficiency,"onUpdate:modelValue":l[6]||(l[6]=s=>c.value.efficiency=s),min:0,max:100,step:.1,precision:1,"controls-position":"right"},null,8,["modelValue"]),l[23]||(l[23]=o("span",{class:"unit"},"%",-1))]),_:1,__:[23]})]),_:1}),e(z,{span:12},{default:a(()=>[e(V,{label:"叶轮直径",prop:"impellerDiameter"},{default:a(()=>[e(w,{modelValue:c.value.impellerDiameter,"onUpdate:modelValue":l[7]||(l[7]=s=>c.value.impellerDiameter=s),min:0,max:1e3,step:1,"controls-position":"right"},null,8,["modelValue"]),l[24]||(l[24]=o("span",{class:"unit"},"mm",-1))]),_:1,__:[24]})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(j,{label:"曲线数据",name:"curve"},{default:a(()=>[o("div",ca,[o("div",fa,[l[27]||(l[27]=o("h4",null,"性能曲线数据点",-1)),o("div",va,[e(x,{size:"small",onClick:f},{default:a(()=>[e(T,null,{default:a(()=>[e(q(De))]),_:1}),l[25]||(l[25]=d(" 添加数据点 "))]),_:1,__:[25]}),e(x,{size:"small",onClick:v},{default:a(()=>[e(T,null,{default:a(()=>[e(q(Ue))]),_:1}),l[26]||(l[26]=d(" 导入数据 "))]),_:1,__:[26]})])]),e(ae,{data:i.value,border:"",style:{width:"100%"}},{default:a(()=>[e(B,{prop:"Q",label:"流量 (m³/h)",width:"120"},{default:a(({row:s,$index:H})=>[e(w,{modelValue:s.Q,"onUpdate:modelValue":F=>s.Q=F,min:0,step:10,size:"small",onChange:F=>u(H)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),e(B,{prop:"H",label:"扬程 (m)",width:"120"},{default:a(({row:s,$index:H})=>[e(w,{modelValue:s.H,"onUpdate:modelValue":F=>s.H=F,min:0,step:.1,precision:2,size:"small",onChange:F=>u(H)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),e(B,{prop:"ETA",label:"效率 (%)",width:"120"},{default:a(({row:s,$index:H})=>[e(w,{modelValue:s.ETA,"onUpdate:modelValue":F=>s.ETA=F,min:0,max:100,step:.1,precision:1,size:"small",onChange:F=>u(H)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),e(B,{prop:"P",label:"功率 (kW)",width:"120"},{default:a(({row:s,$index:H})=>[e(w,{modelValue:s.P,"onUpdate:modelValue":F=>s.P=F,min:0,step:.1,precision:1,size:"small",onChange:F=>u(H)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),e(B,{label:"操作",width:"100"},{default:a(({$index:s})=>[e(x,{size:"small",type:"danger",onClick:H=>m(s),disabled:i.value.length<=3},{default:a(()=>l[28]||(l[28]=[d(" 删除 ")])),_:2,__:[28]},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])])]),_:1}),e(j,{label:"算法配置",name:"algorithm"},{default:a(()=>[e(U,{model:r.value,"label-width":"120px"},{default:a(()=>[e(V,{label:"拟合算法"},{default:a(()=>[e(Z,{modelValue:r.value.algorithm.type,"onUpdate:modelValue":l[8]||(l[8]=s=>r.value.algorithm.type=s),onChange:I},{default:a(()=>[e($,{label:"最小二乘法",value:"least-squares"}),e($,{label:"多项式拟合",value:"polynomial"}),e($,{label:"神经网络",value:"neural-network"}),e($,{label:"样条插值",value:"spline"})]),_:1},8,["modelValue"])]),_:1}),ee.value?(M(),se(V,{key:0,label:"多项式阶数"},{default:a(()=>[e(w,{modelValue:r.value.algorithm.order,"onUpdate:modelValue":l[9]||(l[9]=s=>r.value.algorithm.order=s),min:1,max:10,step:1},null,8,["modelValue"])]),_:1})):le("",!0),r.value.algorithm.type==="neural-network"?(M(),X(Ce,{key:1},[e(V,{label:"训练轮数"},{default:a(()=>[e(w,{modelValue:r.value.algorithm.epochs,"onUpdate:modelValue":l[10]||(l[10]=s=>r.value.algorithm.epochs=s),min:100,max:1e4,step:100},null,8,["modelValue"])]),_:1}),e(V,{label:"学习率"},{default:a(()=>[e(w,{modelValue:r.value.algorithm.learningRate,"onUpdate:modelValue":l[11]||(l[11]=s=>r.value.algorithm.learningRate=s),min:.001,max:1,step:.001,precision:3},null,8,["modelValue"])]),_:1}),e(V,{label:"隐藏层配置"},{default:a(()=>[e(n,{modelValue:E.value,"onUpdate:modelValue":l[12]||(l[12]=s=>E.value=s),placeholder:"例如: 10,5 表示两个隐藏层，分别有10和5个神经元",onBlur:G},null,8,["modelValue"])]),_:1})],64)):le("",!0),e(V,{label:"验证集比例"},{default:a(()=>[e(te,{modelValue:r.value.validationSplit,"onUpdate:modelValue":l[13]||(l[13]=s=>r.value.validationSplit=s),min:.1,max:.5,step:.05,"show-tooltip":"","format-tooltip":s=>`${(s*100).toFixed(0)}%`},null,8,["modelValue","format-tooltip"])]),_:1}),e(V,{label:"交叉验证"},{default:a(()=>[e(P,{modelValue:r.value.crossValidation,"onUpdate:modelValue":l[14]||(l[14]=s=>r.value.crossValidation=s)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1})]),_:1},8,["modelValue"]),o("div",ga,[e(x,{onClick:t,loading:k.value},{default:a(()=>[e(T,null,{default:a(()=>[e(q(Me))]),_:1}),l[29]||(l[29]=d(" 预览曲线 "))]),_:1,__:[29]},8,["loading"]),e(x,{type:"primary",onClick:O,loading:D.value},{default:a(()=>[e(T,null,{default:a(()=>[e(q(Ie))]),_:1}),l[30]||(l[30]=d(" 应用参数 "))]),_:1,__:[30]},8,["loading"])])]),_:1})}}}),ya=re(ba,[["__scopeId","data-v-27338931"]]),ha={class:"algorithm-comparison"},Va={class:"performance-comparison"},Ea={class:"visualization-comparison"},Qa={class:"recommendation"},wa={class:"recommendation-details"},Pa={class:"features"},Ca={class:"feature-item"},xa={class:"feature-item"},Sa={class:"feature-item"},Ta=ie({__name:"AlgorithmComparison",props:{comparisonData:{}},setup(A){const p=A,_=Q("performance"),C=Q(),k=Q(),D=Q(),h={"least-squares":"最小二乘法",polynomial:"多项式拟合","neural-network":"神经网络",spline:"样条插值"},c=[{algorithm:"least-squares",pros:"计算快速，稳定可靠，理论基础扎实",cons:"对非线性关系拟合能力有限",suitable:"线性或近似线性关系，实时计算场景"},{algorithm:"polynomial",pros:"可拟合复杂非线性关系，参数可解释",cons:"高阶多项式容易过拟合",suitable:"中等复杂度的非线性关系"},{algorithm:"neural-network",pros:"强大的非线性拟合能力，自适应学习",cons:"训练时间长，参数不易解释",suitable:"复杂非线性关系，大数据集"},{algorithm:"spline",pros:"局部拟合精度高，平滑性好",cons:"对噪声敏感，外推能力差",suitable:"数据点密集，要求平滑的曲线"}],i=Y(()=>p.comparisonData.map(f=>{const m=f.results.QH,u=f.results.QETA,v=f.results.QP,I=(m.r2Score+u.r2Score+v.r2Score)/3,G=(m.mse+u.mse+v.mse)/3,S=(m.trainingTime+u.trainingTime+v.trainingTime)/3,b=Math.min(5,Math.max(1,I*3+(1-Math.min(G/10,1))*1.5+(1-Math.min(S/1e3,1))*.5));return{algorithm:f.algorithm,qh:m,qeta:u,qp:v,overallScore:Math.round(b*10)/10}})),r=Y(()=>{if(i.value.length===0)return{algorithm:"least-squares",title:"暂无比较数据",description:"请先运行算法比较",score:0,advantages:"",useCase:""};const f=i.value.reduce((u,v)=>v.overallScore>u.overallScore?v:u),m={"least-squares":{title:"推荐使用最小二乘法",description:"最小二乘法在当前数据集上表现最佳，具有良好的拟合精度和计算效率。",advantages:"计算速度快，稳定性好，适合实时应用",useCase:"适用于线性或近似线性的水泵特性曲线拟合"},polynomial:{title:"推荐使用多项式拟合",description:"多项式拟合在当前数据集上表现最佳，能够很好地捕捉非线性特征。",advantages:"拟合精度高，参数易于理解和调整",useCase:"适用于具有明显非线性特征的水泵特性曲线"},"neural-network":{title:"推荐使用神经网络",description:"神经网络在当前数据集上表现最佳，具有强大的非线性建模能力。",advantages:"非线性拟合能力强，适应性好",useCase:"适用于复杂的非线性水泵特性曲线和大数据集"},spline:{title:"推荐使用样条插值",description:"样条插值在当前数据集上表现最佳，提供了平滑且精确的曲线拟合。",advantages:"局部拟合精度高，曲线平滑性好",useCase:"适用于数据点密集且要求高平滑性的场景"}};return ce({algorithm:f.algorithm,score:f.overallScore},m[f.algorithm])}),E=f=>({"least-squares":"primary",polynomial:"success","neural-network":"warning",spline:"info"})[f]||"primary",L=f=>f>=.95?"score-excellent":f>=.9?"score-good":f>=.8?"score-fair":"score-poor",ee=()=>{if(p.comparisonData.length){if(C.value){const f=ne(C.value),m={tooltip:{trigger:"axis"},legend:{data:["Q-H","Q-η","Q-P"]},xAxis:{type:"category",data:p.comparisonData.map(u=>h[u.algorithm])},yAxis:{type:"value",min:0,max:1},series:[{name:"Q-H",type:"bar",data:p.comparisonData.map(u=>u.results.QH.r2Score)},{name:"Q-η",type:"bar",data:p.comparisonData.map(u=>u.results.QETA.r2Score)},{name:"Q-P",type:"bar",data:p.comparisonData.map(u=>u.results.QP.r2Score)}]};f.setOption(m)}if(k.value){const f=ne(k.value),m={tooltip:{trigger:"axis"},legend:{data:["Q-H","Q-η","Q-P"]},xAxis:{type:"category",data:p.comparisonData.map(u=>h[u.algorithm])},yAxis:{type:"value"},series:[{name:"Q-H",type:"line",data:p.comparisonData.map(u=>u.results.QH.mse)},{name:"Q-η",type:"line",data:p.comparisonData.map(u=>u.results.QETA.mse)},{name:"Q-P",type:"line",data:p.comparisonData.map(u=>u.results.QP.mse)}]};f.setOption(m)}if(D.value){const f=ne(D.value),m={tooltip:{trigger:"item"},series:[{type:"pie",radius:"50%",data:p.comparisonData.map(u=>({name:h[u.algorithm],value:(u.results.QH.trainingTime+u.results.QETA.trainingTime+u.results.QP.trainingTime)/3}))}]};f.setOption(m)}}};return xe(()=>N(this,null,function*(){yield Se(),ee()})),(f,m)=>{const u=Ne,v=Pe,I=Le,G=we,S=ye,b=me,t=de,O=ue,y=Ge,l=je,T=We,x=be;return M(),X("div",ha,[e(x,{modelValue:_.value,"onUpdate:modelValue":m[0]||(m[0]=n=>_.value=n),type:"card"},{default:a(()=>[e(S,{label:"性能对比",name:"performance"},{default:a(()=>[o("div",Va,[e(G,{data:i.value,border:""},{default:a(()=>[e(v,{prop:"algorithm",label:"算法",width:"120"},{default:a(({row:n})=>[e(u,{type:E(n.algorithm)},{default:a(()=>[d(g(h[n.algorithm]||n.algorithm),1)]),_:2},1032,["type"])]),_:1}),e(v,{label:"Q-H曲线",align:"center"},{default:a(()=>[e(v,{prop:"qh.r2Score",label:"R²",width:"80"},{default:a(({row:n})=>[o("span",{class:oe(L(n.qh.r2Score))},g(n.qh.r2Score.toFixed(4)),3)]),_:1}),e(v,{prop:"qh.mse",label:"MSE",width:"80"},{default:a(({row:n})=>[d(g(n.qh.mse.toFixed(4)),1)]),_:1}),e(v,{prop:"qh.trainingTime",label:"训练时间(ms)",width:"100"},{default:a(({row:n})=>[d(g(n.qh.trainingTime.toFixed(1)),1)]),_:1})]),_:1}),e(v,{label:"Q-η曲线",align:"center"},{default:a(()=>[e(v,{prop:"qeta.r2Score",label:"R²",width:"80"},{default:a(({row:n})=>[o("span",{class:oe(L(n.qeta.r2Score))},g(n.qeta.r2Score.toFixed(4)),3)]),_:1}),e(v,{prop:"qeta.mse",label:"MSE",width:"80"},{default:a(({row:n})=>[d(g(n.qeta.mse.toFixed(4)),1)]),_:1}),e(v,{prop:"qeta.trainingTime",label:"训练时间(ms)",width:"100"},{default:a(({row:n})=>[d(g(n.qeta.trainingTime.toFixed(1)),1)]),_:1})]),_:1}),e(v,{label:"Q-P曲线",align:"center"},{default:a(()=>[e(v,{prop:"qp.r2Score",label:"R²",width:"80"},{default:a(({row:n})=>[o("span",{class:oe(L(n.qp.r2Score))},g(n.qp.r2Score.toFixed(4)),3)]),_:1}),e(v,{prop:"qp.mse",label:"MSE",width:"80"},{default:a(({row:n})=>[d(g(n.qp.mse.toFixed(4)),1)]),_:1}),e(v,{prop:"qp.trainingTime",label:"训练时间(ms)",width:"100"},{default:a(({row:n})=>[d(g(n.qp.trainingTime.toFixed(1)),1)]),_:1})]),_:1}),e(v,{label:"综合评分",width:"100"},{default:a(({row:n})=>[e(I,{modelValue:n.overallScore,"onUpdate:modelValue":V=>n.overallScore=V,disabled:"","show-score":"","text-color":"#ff9900","score-template":"{value}"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data"])])]),_:1}),e(S,{label:"可视化对比",name:"visualization"},{default:a(()=>[o("div",Ea,[e(O,{gutter:20},{default:a(()=>[e(t,{span:8},{default:a(()=>[e(b,null,{header:a(()=>m[1]||(m[1]=[d("R²得分对比")])),default:a(()=>[o("div",{ref_key:"r2ChartRef",ref:C,class:"chart"},null,512)]),_:1})]),_:1}),e(t,{span:8},{default:a(()=>[e(b,null,{header:a(()=>m[2]||(m[2]=[d("MSE对比")])),default:a(()=>[o("div",{ref_key:"mseChartRef",ref:k,class:"chart"},null,512)]),_:1})]),_:1}),e(t,{span:8},{default:a(()=>[e(b,null,{header:a(()=>m[3]||(m[3]=[d("训练时间对比")])),default:a(()=>[o("div",{ref_key:"timeChartRef",ref:D,class:"chart"},null,512)]),_:1})]),_:1})]),_:1})])]),_:1}),e(S,{label:"推荐建议",name:"recommendation"},{default:a(()=>[o("div",Qa,[e(y,{title:r.value.title,description:r.value.description,type:"success","show-icon":"",closable:!1},null,8,["title","description"]),o("div",wa,[m[7]||(m[7]=o("h3",null,"详细分析",-1)),e(T,{column:2,border:""},{default:a(()=>[e(l,{label:"最佳算法"},{default:a(()=>[e(u,{type:"success"},{default:a(()=>[d(g(h[r.value.algorithm]),1)]),_:1})]),_:1}),e(l,{label:"综合得分"},{default:a(()=>[d(g(r.value.score.toFixed(2)),1)]),_:1}),e(l,{label:"优势"},{default:a(()=>[d(g(r.value.advantages),1)]),_:1}),e(l,{label:"适用场景"},{default:a(()=>[d(g(r.value.useCase),1)]),_:1})]),_:1}),m[8]||(m[8]=o("h3",null,"各算法特点",-1)),e(O,{gutter:20},{default:a(()=>[(M(),X(Ce,null,Je(c,n=>e(t,{span:12,key:n.algorithm},{default:a(()=>[e(b,{class:"feature-card"},{header:a(()=>[e(u,{type:E(n.algorithm)},{default:a(()=>[d(g(h[n.algorithm]),1)]),_:2},1032,["type"])]),default:a(()=>[o("div",Pa,[o("div",Ca,[m[4]||(m[4]=o("strong",null,"优点：",-1)),d(g(n.pros),1)]),o("div",xa,[m[5]||(m[5]=o("strong",null,"缺点：",-1)),d(g(n.cons),1)]),o("div",Sa,[m[6]||(m[6]=o("strong",null,"适用：",-1)),d(g(n.suitable),1)])])]),_:2},1024)]),_:2},1024)),64))]),_:1})])])]),_:1})]),_:1},8,["modelValue"])])}}}),Ha=re(Ta,[["__scopeId","data-v-cfb7aeaa"]]),qa={class:"intelligent-optimization"},Aa={class:"card-header"},ka={class:"card-header"},Da={class:"header-actions"},Ua={key:0,class:"fitting-quality"},Ra={class:"quality-item"},Fa={class:"metrics"},za={class:"quality-item"},$a={class:"metrics"},Ma={class:"quality-item"},Ia={class:"metrics"},Oa={key:1,class:"optimization-result"},Ba={class:"card-header"},Na={class:"chart-controls"},La=ie({__name:"index",setup(A){const p=Q(),_=Q(!1),C=Q("all"),k=Q(["config"]),D=Q(!1),h=Q({Q:800,H:35}),c={"least-squares":"最小二乘法",polynomial:"多项式拟合","neural-network":"神经网络",spline:"样条插值"},i=ua(),r=Y(()=>i.optimizationResult),E=Y(()=>i.fittingResults),L=Y(()=>h.value.Q>0&&h.value.H>0),ee=Y(()=>{if(!r.value)return 0;const b=100,t=r.value.power;return Math.max(0,(b-t)/b*100)});let f=null;const m=(b,t,O)=>{i.updatePumpParameters(b,t,O),R.success("水泵参数已更新，正在重新拟合曲线..."),S()},u=b=>N(this,null,function*(){_.value=!0;try{i.fittingConfig.algorithm.type=b,yield i.fitCurves(b),S(),R.success(`已切换到${c[b]}`)}catch(t){R.error("算法切换失败")}finally{_.value=!1}}),v=()=>N(this,null,function*(){_.value=!0;try{yield i.compareAlgorithms(),D.value=!0,R.success("算法比较完成")}catch(b){R.error("算法比较失败")}finally{_.value=!1}}),I=()=>{i.updateInput(h.value),S()},G=()=>N(this,null,function*(){_.value=!0;try{yield new Promise(b=>setTimeout(b,1e3)),i.calculateOptimization(h.value.Q,h.value.H),S()}finally{_.value=!1}}),S=()=>{if(f){const b=i.getCurveData;b&&ma(f,b,C.value,h.value,r.value)}};return ge(C,()=>{S()}),xe(()=>N(this,null,function*(){i.initCurveParams(),yield Se(),p.value&&(f=da(p.value),S())})),(b,t)=>{const O=Ke,y=Ye,l=Qe,T=Ee,x=me,n=na,V=oa,z=la,W=Ve,w=he,U=de,j=ue,B=Xe,ae=ea,$=aa,Z=ia,te=ra;return M(),X("div",qa,[e(y,{modelValue:k.value,"onUpdate:modelValue":t[0]||(t[0]=P=>k.value=P),class:"config-collapse"},{default:a(()=>[e(O,{title:"水泵参数配置",name:"config"},{default:a(()=>[e(ya,{onParametersUpdated:m})]),_:1})]),_:1},8,["modelValue"]),D.value?(M(),se(x,{key:0,class:"comparison-panel"},{header:a(()=>[o("div",Aa,[e(l,null,{default:a(()=>[e(q(fe))]),_:1}),t[5]||(t[5]=o("span",null,"算法性能比较",-1)),e(T,{size:"small",onClick:t[1]||(t[1]=P=>D.value=!1)},{default:a(()=>[e(l,null,{default:a(()=>[e(q(Ze))]),_:1})]),_:1})])]),default:a(()=>[e(Ha,{"comparison-data":q(i).algorithmComparison},null,8,["comparison-data"])]),_:1})):le("",!0),e(x,{class:"control-panel"},{header:a(()=>[o("div",ka,[e(l,null,{default:a(()=>[e(q(ve))]),_:1}),t[11]||(t[11]=o("span",null,"智能寻优控制面板",-1)),o("div",Da,[e(T,{size:"small",onClick:v,loading:_.value},{default:a(()=>[e(l,null,{default:a(()=>[e(q(fe))]),_:1}),t[6]||(t[6]=d(" 算法比较 "))]),_:1,__:[6]},8,["loading"]),e(z,{onCommand:u},{dropdown:a(()=>[e(V,null,{default:a(()=>[e(n,{command:"least-squares"},{default:a(()=>t[7]||(t[7]=[d("最小二乘法")])),_:1,__:[7]}),e(n,{command:"polynomial"},{default:a(()=>t[8]||(t[8]=[d("多项式拟合")])),_:1,__:[8]}),e(n,{command:"neural-network"},{default:a(()=>t[9]||(t[9]=[d("神经网络")])),_:1,__:[9]}),e(n,{command:"spline"},{default:a(()=>t[10]||(t[10]=[d("样条插值")])),_:1,__:[10]})]),_:1})]),default:a(()=>[e(T,{size:"small"},{default:a(()=>[d(" 当前算法: "+g(c[q(i).fittingConfig.algorithm.type])+" ",1),e(l,null,{default:a(()=>[e(q(ta))]),_:1})]),_:1})]),_:1})])])]),default:a(()=>[e(j,{gutter:20},{default:a(()=>[e(U,{span:8},{default:a(()=>[e(w,{label:"目标流量 (m³/h)"},{default:a(()=>[e(W,{modelValue:h.value.Q,"onUpdate:modelValue":t[2]||(t[2]=P=>h.value.Q=P),min:0,max:2e3,step:10,onChange:I},null,8,["modelValue"])]),_:1})]),_:1}),e(U,{span:8},{default:a(()=>[e(w,{label:"目标扬程 (m)"},{default:a(()=>[e(W,{modelValue:h.value.H,"onUpdate:modelValue":t[3]||(t[3]=P=>h.value.H=P),min:0,max:60,step:.1,precision:2,onChange:I},null,8,["modelValue"])]),_:1})]),_:1}),e(U,{span:8},{default:a(()=>[e(w,{label:"操作"},{default:a(()=>[e(T,{type:"primary",onClick:G,loading:_.value,disabled:!L.value},{default:a(()=>[e(l,null,{default:a(()=>[e(q(ve))]),_:1}),t[12]||(t[12]=d(" 开始寻优 "))]),_:1,__:[12]},8,["loading","disabled"])]),_:1})]),_:1})]),_:1}),E.value.QH?(M(),X("div",Ua,[e(B,{"content-position":"left"},{default:a(()=>t[13]||(t[13]=[d("拟合质量评估")])),_:1,__:[13]}),e(j,{gutter:20},{default:a(()=>[e(U,{span:8},{default:a(()=>[o("div",Ra,[t[14]||(t[14]=o("h4",null,"Q-H曲线",-1)),e(ae,{percentage:Math.round(E.value.QH.r2Score*100)},null,8,["percentage"]),o("div",Fa,[o("span",null,"R² = "+g(E.value.QH.r2Score.toFixed(4)),1),o("span",null,"MSE = "+g(E.value.QH.mse.toFixed(4)),1)])])]),_:1}),e(U,{span:8},{default:a(()=>{var P,J,K,s,H;return[o("div",za,[t[15]||(t[15]=o("h4",null,"Q-η曲线",-1)),e(ae,{percentage:Math.round((((P=E.value.QETA)==null?void 0:P.r2Score)||0)*100)},null,8,["percentage"]),o("div",$a,[o("span",null,"R² = "+g(((K=(J=E.value.QETA)==null?void 0:J.r2Score)==null?void 0:K.toFixed(4))||"0.0000"),1),o("span",null,"MSE = "+g(((H=(s=E.value.QETA)==null?void 0:s.mse)==null?void 0:H.toFixed(4))||"0.0000"),1)])])]}),_:1}),e(U,{span:8},{default:a(()=>{var P,J,K,s,H;return[o("div",Ma,[t[16]||(t[16]=o("h4",null,"Q-P曲线",-1)),e(ae,{percentage:Math.round((((P=E.value.QP)==null?void 0:P.r2Score)||0)*100)},null,8,["percentage"]),o("div",Ia,[o("span",null,"R² = "+g(((K=(J=E.value.QP)==null?void 0:J.r2Score)==null?void 0:K.toFixed(4))||"0.0000"),1),o("span",null,"MSE = "+g(((H=(s=E.value.QP)==null?void 0:s.mse)==null?void 0:H.toFixed(4))||"0.0000"),1)])])]}),_:1})]),_:1})])):le("",!0),r.value?(M(),X("div",Oa,[e(B,{"content-position":"left"},{default:a(()=>t[17]||(t[17]=[d("智能寻优结果")])),_:1,__:[17]}),e(j,{gutter:20},{default:a(()=>[e(U,{span:6},{default:a(()=>[e($,{title:"最优频率",value:r.value.frequency,suffix:"Hz"},null,8,["value"])]),_:1}),e(U,{span:6},{default:a(()=>[e($,{title:"预期效率",value:r.value.efficiency,suffix:"%"},null,8,["value"])]),_:1}),e(U,{span:6},{default:a(()=>[e($,{title:"预期功率",value:r.value.power,suffix:"kW"},null,8,["value"])]),_:1}),e(U,{span:6},{default:a(()=>[e($,{title:"节能率",value:ee.value,suffix:"%"},null,8,["value"])]),_:1})]),_:1})])):le("",!0)]),_:1}),e(x,{class:"chart-container"},{header:a(()=>[o("div",Ba,[e(l,null,{default:a(()=>[e(q(sa))]),_:1}),t[22]||(t[22]=o("span",null,"水泵特性曲线",-1)),o("div",Na,[e(te,{modelValue:C.value,"onUpdate:modelValue":t[4]||(t[4]=P=>C.value=P),size:"small"},{default:a(()=>[e(Z,{value:"all"},{default:a(()=>t[18]||(t[18]=[d("全部曲线")])),_:1,__:[18]}),e(Z,{value:"qh"},{default:a(()=>t[19]||(t[19]=[d("Q-H曲线")])),_:1,__:[19]}),e(Z,{value:"qeta"},{default:a(()=>t[20]||(t[20]=[d("Q-η曲线")])),_:1,__:[20]}),e(Z,{value:"qp"},{default:a(()=>t[21]||(t[21]=[d("Q-P曲线")])),_:1,__:[21]})]),_:1},8,["modelValue"])])])]),default:a(()=>[o("div",{ref_key:"chartRef",ref:p,class:"chart"},null,512)]),_:1})])}}}),vl=re(La,[["__scopeId","data-v-c9f6e784"]]);export{vl as default};
