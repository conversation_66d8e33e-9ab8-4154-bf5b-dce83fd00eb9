<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useGatewayStore } from '../../stores/gateway';
import DeviceEditDialog from './DeviceEditDialog.vue';
import DeviceDetailDrawer from './DeviceDetailDrawer.vue';
import TestConnectionDialog from './TestConnectionDialog.vue';
import type { GatewayDevice } from '../../types/gateway';

const gatewayStore = useGatewayStore();
const deviceEditDialogVisible = ref(false);
const deviceDetailDrawerVisible = ref(false);
const testConnectionDialogVisible = ref(false);
const editingDevice = ref<GatewayDevice | null>(null);
const selectedDeviceId = ref<string | null>(null);
const loading = ref(true);
const mqttStatus = computed(() => gatewayStore.mqttStatus);
const mqttConnected = computed(() => gatewayStore.mqttConnected);

// 刷新网关设备列表
const fetchGatewayDevices = async () => {
  loading.value = true;
  try {
    await gatewayStore.fetchAllGateways();
  } catch (error) {
    ElMessage.error('获取网关设备列表失败');
  } finally {
    loading.value = false;
  }
};

// 打开添加设备对话框
const openAddDeviceDialog = () => {
  editingDevice.value = {
    id: '',
    name: '',
    ip: '',
    port: 502,
    modelName: '',
    manufacturer: '',
    firmwareVersion: '',
    status: 'offline'
  };
  deviceEditDialogVisible.value = true;
};

// 打开编辑设备对话框
const openEditDeviceDialog = (device: GatewayDevice) => {
  editingDevice.value = { ...device };
  deviceEditDialogVisible.value = true;
};

// 打开设备详情抽屉
const openDeviceDetailDrawer = async (deviceId: string) => {
  selectedDeviceId.value = deviceId;
  await gatewayStore.selectDevice(deviceId);
  deviceDetailDrawerVisible.value = true;
};

// 保存设备信息
const saveDevice = async (device: Partial<GatewayDevice>) => {
  if (device.id) {
    // 更新设备
    const result = await gatewayStore.updateDevice(device.id, device);
    if (result) {
      ElMessage.success('更新设备成功');
      deviceEditDialogVisible.value = false;
    }
  } else {
    // 添加新设备
    const result = await gatewayStore.addDevice(device as Omit<GatewayDevice, 'id' | 'createdAt' | 'updatedAt' | 'status'>);
    if (result) {
      ElMessage.success('添加设备成功');
      deviceEditDialogVisible.value = false;
    }
  }
};

// 删除设备
const deleteDevice = async (deviceId: string) => {
  try {
    await ElMessageBox.confirm('确认删除此设备？此操作不可恢复。', '确认删除', {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const result = await gatewayStore.deleteDevice(deviceId);
    if (result) {
      ElMessage.success('设备已成功删除');
    }
  } catch (e) {
    // 用户取消或其他错误
    if (e !== 'cancel') {
      ElMessage.error('删除设备失败');
    }
  }
};

// 测试设备连接
const testConnection = async (deviceId: string) => {
  selectedDeviceId.value = deviceId;
  testConnectionDialogVisible.value = true;
};

// 重启设备
const restartDevice = async (deviceId: string) => {
  try {
    await ElMessageBox.confirm('确认重启此设备？', '确认重启', {
      confirmButtonText: '重启',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const result = await gatewayStore.restartDevice(deviceId);
    if (result.success) {
      ElMessage.success(result.message || '设备正在重启');
    } else {
      ElMessage.error(result.message || '重启设备失败');
    }
  } catch (e) {
    // 用户取消或其他错误
    if (e !== 'cancel') {
      ElMessage.error('重启设备失败');
    }
  }
};

// 获取设备状态样式
const getStatusStyle = (status: string) => {
  switch (status) {
    case 'online':
      return 'success';
    case 'offline':
      return 'info';
    case 'error':
      return 'danger';
    default:
      return 'warning';
  }
};

// 格式化日期时间
const formatDateTime = (dateString: string | Date | undefined) => {
  if (!dateString) return '--';
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  return date.toLocaleString('zh-CN');
};

// 页面加载时获取设备列表
onMounted(() => {
  fetchGatewayDevices();
});
</script>

<template>
  <div class="gateway-management">
    <div class="page-header">
      <h2>变频器网关设备管理</h2>
      <div class="actions">
        <el-button type="primary" @click="openAddDeviceDialog">
          <el-icon><Plus /></el-icon> 添加设备
        </el-button>
        <el-button @click="fetchGatewayDevices">
          <el-icon><Refresh /></el-icon> 刷新
        </el-button>
      </div>
    </div>

    <div class="statistics-cards">
      <el-card class="stat-card">
        <div class="stat-title">设备总数</div>
        <div class="stat-value">{{ gatewayStore.devices.length }}</div>
      </el-card>
      <el-card class="stat-card online">
        <div class="stat-title">在线设备</div>
        <div class="stat-value">{{ gatewayStore.onlineDevicesCount }}</div>
      </el-card>
      <el-card class="stat-card offline">
        <div class="stat-title">离线设备</div>
        <div class="stat-value">{{ gatewayStore.offlineDevicesCount }}</div>
      </el-card>
      <el-card class="stat-card error">
        <div class="stat-title">故障设备</div>
        <div class="stat-value">{{ gatewayStore.errorDevicesCount }}</div>
      </el-card>
    </div>

    <el-card class="data-table-card">
      <el-table
        v-loading="loading"
        :data="gatewayStore.devices"
        border
        style="width: 100%"
      >
        <el-table-column prop="name" label="设备名称" min-width="150" />
        <el-table-column prop="protocol" label="协议" width="100" />
        <el-table-column prop="ip" label="IP地址" min-width="120" />
        <el-table-column prop="port" label="端口" width="100" />
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusStyle(row.status)">
              {{ row.status === 'online' ? '在线' : row.status === 'offline' ? '离线' : '故障' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="最近连接时间" min-width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.lastConnected) }}
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" min-width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" fixed="right" width="260">
          <template #default="{ row }">
            <el-button size="small" @click="openDeviceDetailDrawer(row.id)">查看</el-button>
            <el-button size="small" type="primary" @click="openEditDeviceDialog(row)">编辑</el-button>
            <el-button size="small" type="success" @click="testConnection(row.id)">测试连接</el-button>
            <el-dropdown>
              <el-button size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="restartDevice(row.id)">重启设备</el-dropdown-item>
                  <el-dropdown-item @click="deleteDevice(row.id)" divided>
                    <span class="text-danger">删除设备</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 设备编辑对话框 -->
    <DeviceEditDialog
      v-if="deviceEditDialogVisible"
      v-model:visible="deviceEditDialogVisible"
      :device="editingDevice"
      @save="saveDevice"
    />

    <!-- 设备详情抽屉 -->
    <DeviceDetailDrawer
      v-if="deviceDetailDrawerVisible && gatewayStore.selectedDevice"
      :visible="deviceDetailDrawerVisible"
      :device="gatewayStore.selectedDevice"
      @close="deviceDetailDrawerVisible = false"
    />

    <!-- 测试连接对话框 -->
    <TestConnectionDialog
      v-if="testConnectionDialogVisible"
      v-model:visible="testConnectionDialogVisible"
      :device-id="selectedDeviceId"
    />
  </div>
</template>

<style lang="scss" scoped>
.gateway-management {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-size: 24px;
    }

    .actions {
      display: flex;
      gap: 10px;
    }
  }

  .statistics-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;

    .stat-card {
      .stat-title {
        font-size: 16px;
        color: #606266;
        margin-bottom: 10px;
      }

      .stat-value {
        font-size: 28px;
        font-weight: bold;
      }

      &.online .stat-value {
        color: #67c23a;
      }

      &.offline .stat-value {
        color: #909399;
      }

      &.error .stat-value {
        color: #f56c6c;
      }
    }
  }

  .data-table-card {
    margin-bottom: 20px;
  }

  .text-danger {
    color: #f56c6c;
  }
}
</style> 