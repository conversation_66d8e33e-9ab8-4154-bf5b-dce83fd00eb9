import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';
import type { GatewayDevice, InverterData, MQTTConfig, GatewayError } from '../types/gateway';
import * as gatewayApi from '../api/gateway';
import { mqttService } from '../services/mqtt-service';

export const useGatewayStore = defineStore('gateway', () => {
  // 状态
  const devices = ref<GatewayDevice[]>([]);
  const selectedDeviceId = ref<string | null>(null);
  const deviceData = ref<Record<string, InverterData[]>>({});
  const isLoading = ref(false);
  const error = ref<Error | null>(null);
  const mqttStatus = ref('disconnected');
  const mqttConnected = ref(mqttService.connected.value);
  const lastReceivedData = ref<InverterData | null>(null);
  const errorLogs = ref<GatewayError[]>([]);
  
  // 计算属性
  const selectedDevice = computed<GatewayDevice | undefined>(() => {
    return devices.value.find(device => device.id === selectedDeviceId.value);
  });
  
  const onlineDevicesCount = computed(() => {
    return devices.value.filter(device => device.status === 'online').length;
  });

  const offlineDevicesCount = computed(() => {
    return devices.value.filter(device => device.status === 'offline').length;
  });

  const errorDevicesCount = computed(() => {
    return devices.value.filter(device => device.status === 'error').length;
  });
  
  // 方法
  async function fetchAllGateways() {
    try {
      isLoading.value = true;
      error.value = null;
      devices.value = await gatewayApi.getAllDevices();
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('获取网关设备列表失败');
      console.error('获取网关设备列表失败:', err);
    } finally {
      isLoading.value = false;
    }
  }

  async function fetchDeviceById(id: string) {
    try {
      isLoading.value = true;
      error.value = null;
      const device = await gatewayApi.getDeviceById(id);
      
      if (device) {
        // 更新设备列表中的设备
        const index = devices.value.findIndex(d => d.id === id);
        if (index !== -1) {
          devices.value[index] = device;
        } else {
          devices.value.push(device);
        }
      }
      
      return device;
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(`获取设备 ${id} 信息失败`);
      console.error(`获取设备 ${id} 信息失败:`, err);
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  async function selectDevice(id: string) {
    selectedDeviceId.value = id;
    await fetchDeviceById(id);
    
    // 获取设备的错误日志
    await fetchErrorLogs(id);
    
    // 如果设备支持MQTT，连接到MQTT服务器
    if (selectedDevice.value?.mqttConfig) {
      await connectToMqttDevice(id);
    }
  }

  async function addDevice(device: Omit<GatewayDevice, 'id' | 'status'>) {
    try {
      isLoading.value = true;
      error.value = null;
      const newDevice = await gatewayApi.createDevice(device);
      devices.value.push(newDevice);
      return newDevice;
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('添加网关设备失败');
      console.error('添加网关设备失败:', err);
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  async function updateDevice(id: string, data: Partial<GatewayDevice>) {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedDevice = await gatewayApi.updateDevice(id, data);
      if (!updatedDevice) {
        throw new Error(`更新设备 ${id} 失败`);
      }
      
      // 更新设备列表
      const index = devices.value.findIndex(device => device.id === id);
      if (index !== -1) {
        devices.value[index] = updatedDevice;
      }
      
      return updatedDevice;
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(`更新设备 ${id} 失败`);
      console.error(`更新设备 ${id} 失败:`, err);
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  async function deleteDevice(id: string) {
    try {
      isLoading.value = true;
      error.value = null;
      const success = await gatewayApi.deleteDevice(id);
      
      if (success) {
        // 从设备列表中移除
        devices.value = devices.value.filter(device => device.id !== id);
        
        // 如果当前选中的是被删除的设备，清除选择
        if (selectedDeviceId.value === id) {
          selectedDeviceId.value = null;
        }
      }
      
      return success;
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(`删除设备 ${id} 失败`);
      console.error(`删除设备 ${id} 失败:`, err);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  async function fetchMqttConfig(deviceId: string) {
    try {
      isLoading.value = true;
      error.value = null;
      const config = await gatewayApi.getDeviceMQTTConfig(deviceId);
      return config;
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(`获取设备 ${deviceId} MQTT配置失败`);
      console.error(`获取设备 ${deviceId} MQTT配置失败:`, err);
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  async function updateMqttConfig(deviceId: string, config: MQTTConfig) {
    try {
      isLoading.value = true;
      error.value = null;
      const success = await gatewayApi.updateDeviceMQTTConfig(deviceId, config);
      return success;
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(`更新设备 ${deviceId} MQTT配置失败`);
      console.error(`更新设备 ${deviceId} MQTT配置失败:`, err);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  async function testConnection(deviceId: string) {
    try {
      isLoading.value = true;
      error.value = null;
      const device = devices.value.find(d => d.id === deviceId);
      if (!device?.mqttConfig) {
        throw new Error('设备MQTT配置不存在');
      }
      const result = await gatewayApi.testMQTTConnection(device.mqttConfig);
      return result;
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(`测试设备 ${deviceId} 连接失败`);
      console.error(`测试设备 ${deviceId} 连接失败:`, err);
      return { success: false, message: error.value?.message || '未知错误' };
    } finally {
      isLoading.value = false;
    }
  }

  async function restartDevice(deviceId: string) {
    try {
      isLoading.value = true;
      error.value = null;
      const success = await gatewayApi.restartDevice(deviceId);
      
      // 如果重启成功，更新设备状态
      if (success) {
        const index = devices.value.findIndex(device => device.id === deviceId);
        if (index !== -1) {
          // 暂时标记为离线，等待重新连接
          devices.value[index].status = 'offline';
          
          // 一段时间后重新获取设备状态
          setTimeout(() => fetchDeviceById(deviceId), 10000);
        }
      }
      
      return { success, message: success ? '重启成功' : '重启失败' };
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(`重启设备 ${deviceId} 失败`);
      console.error(`重启设备 ${deviceId} 失败:`, err);
      return { success: false, message: error.value?.message || '未知错误' };
    } finally {
      isLoading.value = false;
    }
  }

  async function fetchErrorLogs(deviceId: string) {
    try {
      isLoading.value = true;
      error.value = null;
      errorLogs.value = await gatewayApi.getDeviceErrors(deviceId);
      return errorLogs.value;
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(`获取设备 ${deviceId} 错误日志失败`);
      console.error(`获取设备 ${deviceId} 错误日志失败:`, err);
      return [];
    } finally {
      isLoading.value = false;
    }
  }

  async function getInverterLatestData(gatewayId: string, inverterId: string) {
    try {
      isLoading.value = true;
      error.value = null;
      const data = await gatewayApi.getDeviceData(inverterId);
      
      // 更新最后接收的数据
      if (data) {
        lastReceivedData.value = data;
      }
      
      return data;
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(`获取变频器 ${inverterId} 最新数据失败`);
      console.error(`获取变频器 ${inverterId} 最新数据失败:`, err);
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  async function getInverterHistoricalData(
    gatewayId: string,
    inverterId: string,
    startTime: Date,
    endTime: Date
  ) {
    try {
      isLoading.value = true;
      error.value = null;
      // 由于API中没有历史数据接口，这里返回当前数据作为示例
      const currentData = await gatewayApi.getDeviceData(inverterId);
      const data = currentData ? [currentData] : [];
      
      // 更新设备数据缓存
      if (!deviceData.value[inverterId]) {
        deviceData.value[inverterId] = [];
      }
      
      // 合并数据，避免重复
      const existingTimestamps = new Set(deviceData.value[inverterId].map(d => d.timestamp));
      const newData = data.filter(d => !existingTimestamps.has(d.timestamp));
      deviceData.value[inverterId] = [...deviceData.value[inverterId], ...newData];
      
      return data;
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(`获取变频器 ${inverterId} 历史数据失败`);
      console.error(`获取变频器 ${inverterId} 历史数据失败:`, err);
      return [];
    } finally {
      isLoading.value = false;
    }
  }

  async function sendCommand(
    gatewayId: string,
    inverterId: string,
    command: string,
    params: Record<string, any>
  ) {
    try {
      isLoading.value = true;
      error.value = null;
      const success = await gatewayApi.sendCommand(inverterId, command, params);
      return { success, message: success ? '命令发送成功' : '命令发送失败' };
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(`发送命令到变频器 ${inverterId} 失败`);
      console.error(`发送命令到变频器 ${inverterId} 失败:`, err);
      return { success: false, message: error.value?.message || '未知错误' };
    } finally {
      isLoading.value = false;
    }
  }

  // MQTT相关方法
  async function connectToMqttDevice(deviceId: string) {
    try {
      // 获取设备的MQTT配置
      const mqttConfig = await fetchMqttConfig(deviceId);
      
      // 如果获取配置成功，连接到MQTT服务器
      if (mqttConfig) {
        // 断开之前的连接
        await mqttService.disconnect();
        
        // 连接到MQTT服务器
        const connected = await mqttService.connect(mqttConfig);
        
        // 更新状态
        mqttConnected.value = mqttService.connected.value;
        mqttStatus.value = mqttService.connected.value ? 'connected' : 'disconnected';
        
        return connected;
      }
      
      return false;
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(`连接到MQTT设备 ${deviceId} 失败`);
      console.error(`连接到MQTT设备 ${deviceId} 失败:`, err);
      return false;
    }
  }

  function disconnectMqtt() {
    mqttService.disconnect();
    mqttConnected.value = false;
    mqttStatus.value = 'disconnected';
  }

  // 监听MQTT连接状态变化
  watch(() => mqttService.connected.value, (newValue) => {
    mqttConnected.value = newValue;
    mqttStatus.value = newValue ? 'connected' : 'disconnected';
  });

  // 监听MQTT消息
  watch(() => mqttService.lastMessage.value, (newMessage) => {
    if (newMessage) {
      try {
        // 尝试解析为变频器数据
        const deviceId = 'default'; // 简化处理，实际应从主题中提取
        
        const data: InverterData = {
          deviceId,
          timestamp: Date.now(),
          frequency: newMessage.frequency || 0,
          current: newMessage.current || 0,
          voltage: newMessage.voltage || 0,
          power: newMessage.power || 0,
          runningStatus: newMessage.status || false,
          temperature: newMessage.temperature || 0,
          errorCode: newMessage.errorCode,
          errorMessage: newMessage.errorMessage
        };
        
        lastReceivedData.value = data;
        
        // 添加到缓存
        if (!deviceData.value[deviceId]) {
          deviceData.value[deviceId] = [];
        }
        deviceData.value[deviceId].push(data);
      } catch (err) {
        console.error('解析MQTT消息失败:', err);
      }
    }
  }, { deep: true });

  return {
    // 状态
    devices,
    selectedDeviceId,
    deviceData,
    isLoading,
    error,
    mqttStatus,
    mqttConnected,
    lastReceivedData,
    errorLogs,
    
    // 计算属性
    selectedDevice,
    onlineDevicesCount,
    offlineDevicesCount,
    errorDevicesCount,
    
    // 方法
    fetchAllGateways,
    fetchDeviceById,
    selectDevice,
    addDevice,
    updateDevice,
    deleteDevice,
    fetchMqttConfig,
    updateMqttConfig,
    testConnection,
    restartDevice,
    fetchErrorLogs,
    getInverterLatestData,
    getInverterHistoricalData,
    sendCommand,
    connectToMqttDevice,
    disconnectMqtt
  };
});