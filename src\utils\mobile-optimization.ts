/**
 * 移动端性能优化工具
 */

// 设备检测
export const deviceDetection = {
  // 检测是否为移动设备
  isMobile(): boolean {
    return window.innerWidth < 768
  },

  // 检测是否为平板设备
  isTablet(): boolean {
    return window.innerWidth >= 768 && window.innerWidth < 992
  },

  // 检测是否为桌面设备
  isDesktop(): boolean {
    return window.innerWidth >= 992
  },

  // 检测是否为触摸设备
  isTouchDevice(): boolean {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0
  },

  // 检测是否为iOS设备
  isIOS(): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent)
  },

  // 检测是否为Android设备
  isAndroid(): boolean {
    return /Android/.test(navigator.userAgent)
  },

  // 获取设备像素比
  getPixelRatio(): number {
    return window.devicePixelRatio || 1
  }
}

// 图片懒加载
export class LazyImageLoader {
  private observer: IntersectionObserver | null = null
  private images: Set<HTMLImageElement> = new Set()

  constructor() {
    this.init()
  }

  private init() {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement
              this.loadImage(img)
              this.observer?.unobserve(img)
              this.images.delete(img)
            }
          })
        },
        {
          rootMargin: '50px'
        }
      )
    }
  }

  // 添加图片到懒加载队列
  observe(img: HTMLImageElement) {
    if (this.observer && img.dataset.src) {
      this.images.add(img)
      this.observer.observe(img)
    } else {
      // 降级处理
      this.loadImage(img)
    }
  }

  // 加载图片
  private loadImage(img: HTMLImageElement) {
    const src = img.dataset.src
    if (src) {
      img.src = src
      img.removeAttribute('data-src')
      img.classList.add('loaded')
    }
  }

  // 销毁
  destroy() {
    if (this.observer) {
      this.images.forEach(img => this.observer?.unobserve(img))
      this.observer.disconnect()
      this.observer = null
    }
    this.images.clear()
  }
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }

    const callNow = immediate && !timeout

    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)

    if (callNow) func(...args)
  }
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean

  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

// 虚拟滚动
export class VirtualScroller {
  private container: HTMLElement
  private itemHeight: number
  private visibleCount: number
  private startIndex = 0
  private endIndex = 0
  private scrollTop = 0

  constructor(
    container: HTMLElement,
    itemHeight: number,
    visibleCount: number
  ) {
    this.container = container
    this.itemHeight = itemHeight
    this.visibleCount = visibleCount
    this.init()
  }

  private init() {
    this.container.addEventListener('scroll', this.handleScroll.bind(this))
  }

  private handleScroll = throttle(() => {
    this.scrollTop = this.container.scrollTop
    this.updateVisibleRange()
  }, 16)

  private updateVisibleRange() {
    this.startIndex = Math.floor(this.scrollTop / this.itemHeight)
    this.endIndex = Math.min(
      this.startIndex + this.visibleCount,
      this.getTotalCount()
    )
  }

  getVisibleRange() {
    return {
      start: this.startIndex,
      end: this.endIndex
    }
  }

  private getTotalCount(): number {
    // 需要外部提供总数据量
    return 0
  }

  scrollToIndex(index: number) {
    const scrollTop = index * this.itemHeight
    this.container.scrollTop = scrollTop
  }

  destroy() {
    this.container.removeEventListener('scroll', this.handleScroll)
  }
}

// 触摸手势处理
export class TouchGestureHandler {
  private element: HTMLElement
  private startX = 0
  private startY = 0
  private startTime = 0
  private isMoving = false

  constructor(element: HTMLElement) {
    this.element = element
    this.init()
  }

  private init() {
    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true })
    this.element.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: true })
    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true })
  }

  private handleTouchStart(e: TouchEvent) {
    const touch = e.touches[0]
    this.startX = touch.clientX
    this.startY = touch.clientY
    this.startTime = Date.now()
    this.isMoving = false
  }

  private handleTouchMove(e: TouchEvent) {
    this.isMoving = true
  }

  private handleTouchEnd(e: TouchEvent) {
    if (!this.isMoving) return

    const touch = e.changedTouches[0]
    const endX = touch.clientX
    const endY = touch.clientY
    const endTime = Date.now()

    const deltaX = endX - this.startX
    const deltaY = endY - this.startY
    const deltaTime = endTime - this.startTime

    // 判断滑动方向和距离
    const minSwipeDistance = 50
    const maxSwipeTime = 300

    if (deltaTime < maxSwipeTime) {
      if (Math.abs(deltaX) > minSwipeDistance && Math.abs(deltaX) > Math.abs(deltaY)) {
        // 水平滑动
        if (deltaX > 0) {
          this.onSwipeRight()
        } else {
          this.onSwipeLeft()
        }
      } else if (Math.abs(deltaY) > minSwipeDistance && Math.abs(deltaY) > Math.abs(deltaX)) {
        // 垂直滑动
        if (deltaY > 0) {
          this.onSwipeDown()
        } else {
          this.onSwipeUp()
        }
      }
    }
  }

  // 可重写的手势回调
  onSwipeLeft() {}
  onSwipeRight() {}
  onSwipeUp() {}
  onSwipeDown() {}

  destroy() {
    this.element.removeEventListener('touchstart', this.handleTouchStart)
    this.element.removeEventListener('touchmove', this.handleTouchMove)
    this.element.removeEventListener('touchend', this.handleTouchEnd)
  }
}

// 性能监控
export const performanceMonitor = {
  // 监控页面加载性能
  measurePageLoad() {
    if ('performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        firstPaint: this.getFirstPaint(),
        firstContentfulPaint: this.getFirstContentfulPaint()
      }
    }
    return null
  },

  // 获取首次绘制时间
  getFirstPaint(): number {
    const paintEntries = performance.getEntriesByType('paint')
    const fpEntry = paintEntries.find(entry => entry.name === 'first-paint')
    return fpEntry ? fpEntry.startTime : 0
  },

  // 获取首次内容绘制时间
  getFirstContentfulPaint(): number {
    const paintEntries = performance.getEntriesByType('paint')
    const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint')
    return fcpEntry ? fcpEntry.startTime : 0
  },

  // 监控内存使用
  getMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit
      }
    }
    return null
  }
}

// 移动端适配工具
export const mobileAdapter = {
  // 设置viewport
  setViewport() {
    let viewport = document.querySelector('meta[name="viewport"]')
    if (!viewport) {
      viewport = document.createElement('meta')
      viewport.setAttribute('name', 'viewport')
      document.head.appendChild(viewport)
    }
    viewport.setAttribute(
      'content',
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
    )
  },

  // 禁用iOS橡皮筋效果
  disableIOSBounce() {
    if (deviceDetection.isIOS()) {
      document.body.style.overscrollBehavior = 'none'
    }
  },

  // 优化iOS输入框体验
  optimizeIOSInput() {
    if (deviceDetection.isIOS()) {
      // 防止输入框放大
      const inputs = document.querySelectorAll('input, textarea, select')
      inputs.forEach(input => {
        const element = input as HTMLElement
        if (element.style.fontSize === '' || parseFloat(element.style.fontSize) < 16) {
          element.style.fontSize = '16px'
        }
      })
    }
  },

  // 初始化移动端优化
  init() {
    this.setViewport()
    this.disableIOSBounce()
    this.optimizeIOSInput()
  }
}
