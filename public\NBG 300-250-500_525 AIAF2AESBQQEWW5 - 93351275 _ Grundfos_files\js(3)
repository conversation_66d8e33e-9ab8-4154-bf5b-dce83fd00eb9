
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"3",
  
  "macros":[{"function":"__e"}],
  "tags":[{"function":"__ogt_cps","priority":3,"vtp_cpsMode":"ALL","tag_id":8},{"function":"__ogt_1p_data_v2","priority":3,"vtp_isAutoEnabled":true,"vtp_isManualEnabled":false,"vtp_autoPhoneEnabled":false,"vtp_autoCollectExclusionSelectors":["list",["map","exclusionSelector",""]],"vtp_isEnabled":true,"vtp_autoAddressEnabled":false,"vtp_autoEmailEnabled":true,"vtp_manualEmailEnabled":false,"vtp_cityValue":"","vtp_postalCodeValue":"","vtp_lastNameValue":"","vtp_phoneValue":"","vtp_emailValue":"","vtp_firstNameValue":"","vtp_streetValue":"","vtp_regionValue":"","vtp_countryValue":"","vtp_isAutoCollectPiiEnabledFlag":true,"tag_id":10},{"function":"__ccd_fl_first","priority":2,"vtp_instanceDestinationId":"DC-9857227","tag_id":14},{"function":"__ccd_add_1p_data","priority":1,"vtp_acceptAutomatic":false,"vtp_acceptCode":true,"vtp_acceptManualSelector":true,"vtp_acceptUserData":true,"vtp_matchingRules":"{\"type\":5,\"args\":[{\"booleanValue\":true},{\"booleanValue\":true}]}","vtp_instanceDestinationId":"DC-9857227","tag_id":13},{"function":"__rep","vtp_containerId":"DC-9857227","vtp_remoteConfig":["map"],"tag_id":6},{"function":"__ccd_fl_last","priority":0,"vtp_instanceDestinationId":"DC-9857227","tag_id":12}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init_consent"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init"}],
  "rules":[[["if",0],["add",4]],[["if",1],["add",0]],[["if",2],["add",1,5,3,2]]]
},
"runtime":[ [50,"__ccd_add_1p_data",[46,"a"],[52,"b","c"],[52,"c","m"],[52,"d","a"],[52,"e",[15,"__module_metadataSchema"]],[52,"f",[15,"__module_gtagSchema"]],[52,"g",["require","getContainerVersion"]],[52,"h",[30,[17,[15,"a"],"instanceDestinationId"],[17,["g"],"containerId"]]],[52,"i",["require","internal.setProductSettingsParameter"]],["i",[15,"h"],"ccd_add_1p_data",true],[22,[30,[30,[28,[17,[15,"a"],"matchingRules"]],[28,[17,[15,"a"],"acceptUserData"]]],[1,[1,[28,[17,[15,"a"],"acceptAutomatic"]],[28,[17,[15,"a"],"acceptManualSelector"]]],[28,[17,[15,"a"],"acceptCode"]]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"j",["require","internal.registerCcdCallback"]],[52,"k",["require","internal.evaluateBooleanExpression"]],[52,"l",[51,"",[7,"m"],[22,[28,["k",[17,[15,"a"],"matchingRules"],[8,"preHit",[15,"m"]]]],[46,[53,[36]]]],[22,[2,[15,"m"],"getMetadata",[7,[17,[15,"e"],"Y"]]],[46,[53,[2,[15,"m"],"setMetadata",[7,[17,[15,"e"],"AF"],true]],[36]]]],[41,"n"],[41,"o"],[22,[17,[15,"a"],"acceptCode"],[46,[53,[3,"o",[2,[15,"m"],"getMetadata",[7,[17,[15,"e"],"AN"]]]],[22,[20,[15,"o"],[45]],[46,[53,[36]]]],[22,[1,[15,"o"],[16,[15,"o"],"_tag_mode"]],[46,[53,[38,[16,[15,"o"],"_tag_mode"],[46,"AUTO","MANUAL"],[46,[5,[46,[3,"n",[15,"d"]],[4]]],[5,[46,[3,"n",[15,"c"]],[4]]],[9,[46,[3,"n",[15,"b"]],[4]]]]]]],[46,[53,[3,"n",[15,"b"]]]]]]]],[22,[1,[28,[15,"o"]],[17,[15,"a"],"acceptManualSelector"]],[46,[53,[3,"o",[2,[15,"m"],"getMetadata",[7,[17,[15,"e"],"AO"]]]],[3,"n",[15,"c"]]]]],[22,[1,[28,[15,"o"]],[17,[15,"a"],"acceptAutomatic"]],[46,[53,[52,"p",[2,[15,"m"],"getMetadata",[7,[17,[15,"e"],"AM"]]]],[22,[15,"p"],[46,[53,[3,"o",["p",[15,"m"]]],[3,"n",[15,"d"]]]]]]]],[22,[15,"o"],[46,[53,[2,[15,"m"],"setMetadata",[7,[17,[15,"e"],"AK"],[15,"o"]]],[2,[15,"m"],"setHitData",[7,[17,[15,"f"],"CX"],[15,"n"]]]]]],[2,[15,"m"],"setMetadata",[7,[17,[15,"e"],"AF"],true]]]],["j",[15,"h"],[15,"l"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_fl_first",[46,"a"],[50,"d",[46,"e"],[2,[15,"c"],"B",[7,[15,"e"]]]],[52,"b",["require","internal.registerCcdCallback"]],[52,"c",[15,"__module_webPrivacyTasks"]],["b",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"e"],["d",[15,"e"]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_fl_last",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__ogt_1p_data_v2",[46,"a"],[50,"o",[46,"t","u"],[52,"v",[7]],[52,"w",[2,[15,"b"],"keys",[7,[15,"t"]]]],[65,"x",[15,"w"],[46,[53,[52,"y",[30,[16,[15,"t"],[15,"x"]],[7]]],[52,"z",[39,[18,[17,[15,"y"],"length"],0],"1","0"]],[52,"aA",[39,["p",[15,"u"],[15,"x"]],"1","0"]],[2,[15,"v"],"push",[7,[0,[0,[0,[16,[15,"n"],[15,"x"]],"-"],[15,"z"]],[15,"aA"]]]]]]],[36,[2,[15,"v"],"join",[7,"~"]]]],[50,"p",[46,"t","u"],[22,[28,[15,"t"]],[46,[53,[36,false]]]],[38,[15,"u"],[46,"email","phone_number","first_name","last_name","street","city","region","postal_code","country"],[46,[5,[46,[36,[28,[28,[16,[15,"t"],"email"]]]]]],[5,[46,[36,[28,[28,[16,[15,"t"],"phone_number"]]]]]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46,[36,["q",[15,"t"],[15,"u"]]]]],[9,[46,[36,false]]]]]],[50,"q",[46,"t","u"],[36,[1,[28,[28,[16,[15,"t"],"address"]]],[28,[28,[16,[16,[15,"t"],"address"],[15,"u"]]]]]]],[50,"r",[46,"t","u","v"],[22,[20,[16,[15,"u"],"type"],[15,"v"]],[46,[53,[22,[28,[15,"t"]],[46,[53,[3,"t",[8]]]]],[22,[28,[16,[15,"t"],[15,"v"]]],[46,[53,[43,[15,"t"],[15,"v"],[16,[15,"u"],"userData"]]]]]]]],[36,[15,"t"]]],[50,"s",[46,"t","u","v"],[22,[28,[16,[15,"a"],[15,"v"]]],[46,[36]]],[43,[15,"t"],[15,"u"],[8,"value",[16,[15,"a"],[15,"v"]]]]],[22,[28,[17,[15,"a"],"isEnabled"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","Object"]],[52,"c",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"d",["require","internal.getDestinationIds"]],[52,"e",["require","internal.getProductSettingsParameter"]],[52,"f",["require","internal.detectUserProvidedData"]],[52,"g",["require","queryPermission"]],[52,"h",["require","internal.setRemoteConfigParameter"]],[52,"i",["require","internal.registerCcdCallback"]],[52,"j",[15,"__module_metadataSchema"]],[52,"k","_z"],[52,"l",[30,["d"],[7]]],[52,"m",[8,"enable_code",true]],[52,"n",[8,"email","1","phone_number","2","first_name","3","last_name","4","country","5","postal_code","6","street","7","city","8","region","9"]],[22,[17,[15,"a"],"isAutoEnabled"],[46,[53,[52,"t",[7]],[22,[1,[17,[15,"a"],"autoCollectExclusionSelectors"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[46,[53,[53,[41,"w"],[3,"w",0],[63,[7,"w"],[23,[15,"w"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[33,[15,"w"],[3,"w",[0,[15,"w"],1]]],[46,[53,[52,"x",[17,[16,[17,[15,"a"],"autoCollectExclusionSelectors"],[15,"w"]],"exclusionSelector"]],[22,[15,"x"],[46,[53,[2,[15,"t"],"push",[7,[15,"x"]]]]]]]]]]]]],[52,"u",[30,[16,[15,"c"],"enableAutoPhoneAndAddressDetection"],[17,[15,"a"],"isAutoCollectPiiEnabledFlag"]]],[52,"v",[39,[17,[15,"a"],"isAutoCollectPiiEnabledFlag"],[17,[15,"a"],"autoEmailEnabled"],true]],[43,[15,"m"],"auto_detect",[8,"email",[15,"v"],"phone",[1,[15,"u"],[17,[15,"a"],"autoPhoneEnabled"]],"address",[1,[15,"u"],[17,[15,"a"],"autoAddressEnabled"]],"exclude_element_selectors",[15,"t"]]]]]],[22,[17,[15,"a"],"isManualEnabled"],[46,[53,[52,"t",[8]],[22,[17,[15,"a"],"manualEmailEnabled"],[46,[53,["s",[15,"t"],"email","emailValue"]]]],[22,[17,[15,"a"],"manualPhoneEnabled"],[46,[53,["s",[15,"t"],"phone","phoneValue"]]]],[22,[17,[15,"a"],"manualAddressEnabled"],[46,[53,[52,"u",[8]],["s",[15,"u"],"first_name","firstNameValue"],["s",[15,"u"],"last_name","lastNameValue"],["s",[15,"u"],"street","streetValue"],["s",[15,"u"],"city","cityValue"],["s",[15,"u"],"region","regionValue"],["s",[15,"u"],"country","countryValue"],["s",[15,"u"],"postal_code","postalCodeValue"],[43,[15,"t"],"name_and_address",[7,[15,"u"]]]]]],[43,[15,"m"],"selectors",[15,"t"]]]]],[65,"t",[15,"l"],[46,[53,["h",[15,"t"],"user_data_settings",[15,"m"]],[52,"u",[16,[15,"m"],"auto_detect"]],[22,[28,[15,"u"]],[46,[53,[6]]]],[52,"v",[51,"",[7,"w"],[52,"x",[2,[15,"w"],"getMetadata",[7,[17,[15,"j"],"AL"]]]],[22,[15,"x"],[46,[53,[36,[15,"x"]]]]],[52,"y",[1,[16,[15,"c"],"enableDataLayerSearchExperiment"],[20,[2,[15,"t"],"indexOf",[7,"G-"]],0]]],[41,"z"],[22,["g","detect_user_provided_data","auto"],[46,[53,[3,"z",["f",[8,"excludeElementSelectors",[16,[15,"u"],"exclude_element_selectors"],"fieldFilters",[8,"email",[16,[15,"u"],"email"],"phone",[16,[15,"u"],"phone"],"address",[16,[15,"u"],"address"]],"performDataLayerSearch",[15,"y"]]]]]]],[52,"aA",[1,[15,"z"],[16,[15,"z"],"elements"]]],[52,"aB",[8]],[22,[1,[15,"aA"],[18,[17,[15,"aA"],"length"],0]],[46,[53,[41,"aC"],[53,[41,"aD"],[3,"aD",0],[63,[7,"aD"],[23,[15,"aD"],[17,[15,"aA"],"length"]],[33,[15,"aD"],[3,"aD",[0,[15,"aD"],1]]],[46,[53,[52,"aE",[16,[15,"aA"],[15,"aD"]]],["r",[15,"aB"],[15,"aE"],"email"],[22,[16,[15,"c"],"enableAutoPiiOnPhoneAndAddress"],[46,[53,["r",[15,"aB"],[15,"aE"],"phone_number"],[3,"aC",["r",[15,"aC"],[15,"aE"],"first_name"]],[3,"aC",["r",[15,"aC"],[15,"aE"],"last_name"]],[3,"aC",["r",[15,"aC"],[15,"aE"],"country"]],[3,"aC",["r",[15,"aC"],[15,"aE"],"postal_code"]]]]]]]]],[22,[1,[15,"aC"],[28,[16,[15,"aB"],"address"]]],[46,[53,[43,[15,"aB"],"address",[15,"aC"]]]]]]]],[22,[15,"y"],[46,[53,[52,"aC",[1,[15,"z"],[16,[15,"z"],"dataLayerSearchResults"]]],[22,[15,"aC"],[46,[53,[52,"aD",["o",[15,"aC"],[15,"aB"]]],[22,[15,"aD"],[46,[53,[2,[15,"w"],"setHitData",[7,[15,"k"],[15,"aD"]]]]]]]]]]]],[2,[15,"w"],"setMetadata",[7,[17,[15,"j"],"AL"],[15,"aB"]]],[36,[15,"aB"]]]],["i",[15,"t"],[51,"",[7,"w"],[2,[15,"w"],"setMetadata",[7,[17,[15,"j"],"AM"],[15,"v"]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ogt_cps",[46,"a"],[50,"e",[46,"f","g"],[22,[20,[15,"f"],[44]],[46,[36]]],[22,[15,"f"],[46,[53,[43,[15,"d"],[15,"g"],"granted"]]],[46,[53,[43,[15,"d"],[15,"g"],"denied"]]]]],[52,"b",["require","internal.setCorePlatformServices"]],[52,"c",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[22,[16,[15,"c"],"enableOverrideAdsCps"],[46,[53,["b",[8,"search","denied","youtube","denied","maps","denied","playstore","denied","shopping","denied","ads","granted"]],[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"d",[8]],[22,[20,[17,[15,"a"],"cpsMode"],"SOME"],[46,[53,["e",[17,[15,"a"],"cpsSearch"],"search"],["e",[17,[15,"a"],"cpsYoutube"],"youtube"],["e",[17,[15,"a"],"cpsMaps"],"maps"],["e",[17,[15,"a"],"cpsPlaystore"],"playstore"],["e",[17,[15,"a"],"cpsShopping"],"shopping"],["e",[17,[15,"a"],"cpsAds"],"ads"],["b",[15,"d"]]]],[46,[53,[43,[15,"d"],"all","granted"],["b",[15,"d"]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[52,"__module_gtagSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","ad_personalization"],[52,"c","ad_storage"],[52,"d","ad_user_data"],[52,"e","consent_updated"],[52,"f","app_remove"],[52,"g","app_store_refund"],[52,"h","app_store_subscription_cancel"],[52,"i","app_store_subscription_convert"],[52,"j","app_store_subscription_renew"],[52,"k","purchase"],[52,"l","first_open"],[52,"m","first_visit"],[52,"n","gtag.config"],[52,"o","in_app_purchase"],[52,"p","page_view"],[52,"q","session_start"],[52,"r","user_engagement"],[52,"s","gclgb"],[52,"t","gclid"],[52,"u","gclgs"],[52,"v","gcllp"],[52,"w","gclst"],[52,"x","ads_data_redaction"],[52,"y","allow_ad_personalization_signals"],[52,"z","allow_direct_google_requests"],[52,"aA","allow_google_signals"],[52,"aB","auid"],[52,"aC","discount"],[52,"aD","aw_feed_country"],[52,"aE","aw_feed_language"],[52,"aF","items"],[52,"aG","aw_merchant_id"],[52,"aH","aw_basket_type"],[52,"aI","client_id"],[52,"aJ","conversion_id"],[52,"aK","conversion_linker"],[52,"aL","conversion_api"],[52,"aM","cookie_deprecation"],[52,"aN","cookie_expires"],[52,"aO","cookie_update"],[52,"aP","country"],[52,"aQ","currency"],[52,"aR","customer_buyer_stage"],[52,"aS","customer_lifetime_value"],[52,"aT","customer_loyalty"],[52,"aU","customer_ltv_bucket"],[52,"aV","debug_mode"],[52,"aW","shipping"],[52,"aX","engagement_time_msec"],[52,"aY","estimated_delivery_date"],[52,"aZ","event_developer_id_string"],[52,"bA","event"],[52,"bB","event_timeout"],[52,"bC","first_party_collection"],[52,"bD","gdpr_applies"],[52,"bE","google_analysis_params"],[52,"bF","_google_ng"],[52,"bG","gpp_sid"],[52,"bH","gpp_string"],[52,"bI","gsa_experiment_id"],[52,"bJ","gtag_event_feature_usage"],[52,"bK","iframe_state"],[52,"bL","ignore_referrer"],[52,"bM","is_passthrough"],[52,"bN","_lps"],[52,"bO","language"],[52,"bP","merchant_feed_label"],[52,"bQ","merchant_feed_language"],[52,"bR","merchant_id"],[52,"bS","new_customer"],[52,"bT","page_hostname"],[52,"bU","page_path"],[52,"bV","page_referrer"],[52,"bW","page_title"],[52,"bX","_platinum_request_status"],[52,"bY","restricted_data_processing"],[52,"bZ","screen_resolution"],[52,"cA","search_term"],[52,"cB","send_page_view"],[52,"cC","server_container_url"],[52,"cD","session_duration"],[52,"cE","session_engaged_time"],[52,"cF","session_id"],[52,"cG","_shared_user_id"],[52,"cH","topmost_url"],[52,"cI","transaction_id"],[52,"cJ","transport_url"],[52,"cK","update"],[52,"cL","_user_agent_architecture"],[52,"cM","_user_agent_bitness"],[52,"cN","_user_agent_full_version_list"],[52,"cO","_user_agent_mobile"],[52,"cP","_user_agent_model"],[52,"cQ","_user_agent_platform"],[52,"cR","_user_agent_platform_version"],[52,"cS","_user_agent_wow64"],[52,"cT","user_data_auto_latency"],[52,"cU","user_data_auto_meta"],[52,"cV","user_data_auto_multi"],[52,"cW","user_data_auto_selectors"],[52,"cX","user_data_auto_status"],[52,"cY","user_data_mode"],[52,"cZ","user_id"],[52,"dA","user_properties"],[52,"dB","us_privacy_string"],[52,"dC","value"],[52,"dD","_fpm_parameters"],[52,"dE","_host_name"],[52,"dF","_in_page_command"],[52,"dG","non_personalized_ads"],[52,"dH","conversion_label"],[52,"dI","page_location"],[52,"dJ","global_developer_id_string"],[52,"dK","tc_privacy_string"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"D",[15,"e"],"E",[15,"f"],"F",[15,"g"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"P",[15,"q"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"DG",[15,"dH"],"AJ",[15,"aK"],"AK",[15,"aL"],"AL",[15,"aM"],"AM",[15,"aN"],"AN",[15,"aO"],"AO",[15,"aP"],"AP",[15,"aQ"],"AQ",[15,"aR"],"AR",[15,"aS"],"AS",[15,"aT"],"AT",[15,"aU"],"AU",[15,"aV"],"AV",[15,"aW"],"AW",[15,"aX"],"AX",[15,"aY"],"AY",[15,"aZ"],"AZ",[15,"bA"],"BA",[15,"bB"],"BB",[15,"bC"],"BC",[15,"bD"],"DI",[15,"dJ"],"BD",[15,"bE"],"BE",[15,"bF"],"BF",[15,"bG"],"BG",[15,"bH"],"BH",[15,"bI"],"BI",[15,"bJ"],"BJ",[15,"bK"],"BK",[15,"bL"],"BL",[15,"bM"],"BM",[15,"bN"],"BN",[15,"bO"],"BO",[15,"bP"],"BP",[15,"bQ"],"BQ",[15,"bR"],"BR",[15,"bS"],"BS",[15,"bT"],"DH",[15,"dI"],"BT",[15,"bU"],"BU",[15,"bV"],"BV",[15,"bW"],"BW",[15,"bX"],"BX",[15,"bY"],"BY",[15,"bZ"],"BZ",[15,"cA"],"CA",[15,"cB"],"CB",[15,"cC"],"CC",[15,"cD"],"CD",[15,"cE"],"CE",[15,"cF"],"CF",[15,"cG"],"DJ",[15,"dK"],"CG",[15,"cH"],"CH",[15,"cI"],"CI",[15,"cJ"],"CJ",[15,"cK"],"CK",[15,"cL"],"CL",[15,"cM"],"CM",[15,"cN"],"CN",[15,"cO"],"CO",[15,"cP"],"CP",[15,"cQ"],"CQ",[15,"cR"],"CR",[15,"cS"],"CS",[15,"cT"],"CT",[15,"cU"],"CU",[15,"cV"],"CV",[15,"cW"],"CW",[15,"cX"],"CX",[15,"cY"],"CY",[15,"cZ"],"CZ",[15,"dA"],"DA",[15,"dB"],"DB",[15,"dC"],"DC",[15,"dD"],"DD",[15,"dE"],"DE",[15,"dF"],"DF",[15,"dG"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_metadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","accept_by_default"],[52,"c","add_tag_timing"],[52,"d","consent_state"],[52,"e","consent_updated"],[52,"f","conversion_linker_enabled"],[52,"g","cookie_options"],[52,"h","em_event"],[52,"i","event_start_timestamp_ms"],[52,"j","event_usage"],[52,"k","ga4_collection_subdomain"],[52,"l","hit_type"],[52,"m","hit_type_override"],[52,"n","is_conversion"],[52,"o","is_external_event"],[52,"p","is_first_visit"],[52,"q","is_first_visit_conversion"],[52,"r","is_fpm_encryption"],[52,"s","is_fpm_split"],[52,"t","is_gcp_conversion"],[52,"u","is_google_signals_allowed"],[52,"v","is_server_side_destination"],[52,"w","is_session_start"],[52,"x","is_session_start_conversion"],[52,"y","is_sgtm_ga_ads_conversion_study_control_group"],[52,"z","is_sgtm_prehit"],[52,"aA","is_split_conversion"],[52,"aB","is_syn"],[52,"aC","prehit_for_retry"],[52,"aD","redact_ads_data"],[52,"aE","redact_click_ids"],[52,"aF","send_ccm_parallel_ping"],[52,"aG","send_user_data_hit"],[52,"aH","speculative"],[52,"aI","syn_or_mod"],[52,"aJ","transient_ecsid"],[52,"aK","transmission_type"],[52,"aL","user_data"],[52,"aM","user_data_from_automatic"],[52,"aN","user_data_from_automatic_getter"],[52,"aO","user_data_from_code"],[52,"aP","user_data_from_manual"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"D",[15,"e"],"E",[15,"f"],"F",[15,"g"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"P",[15,"q"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"AJ",[15,"aK"],"AK",[15,"aL"],"AL",[15,"aM"],"AM",[15,"aN"],"AN",[15,"aO"],"AO",[15,"aP"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_webPrivacyTasks",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"e",[46,"g"],[52,"h",["c"]],[65,"i",[7,[17,[15,"d"],"DA"],[17,[15,"d"],"BC"],[17,[15,"d"],"DJ"]],[46,[53,[2,[15,"g"],"setHitData",[7,[15,"i"],[16,[15,"h"],[15,"i"]]]]]]]],[50,"f",[46,"g"],[52,"h",["c"]],[22,[16,[15,"h"],[17,[15,"d"],"BG"]],[46,[53,[2,[15,"g"],"setHitData",[7,[17,[15,"d"],"BG"],[16,[15,"h"],[17,[15,"d"],"BG"]]]]]]],[22,[16,[15,"h"],[17,[15,"d"],"BF"]],[46,[53,[2,[15,"g"],"setHitData",[7,[17,[15,"d"],"BF"],[16,[15,"h"],[17,[15,"d"],"BF"]]]]]]]],[52,"b",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"c",["require","internal.getPrivacyStrings"]],[52,"d",[15,"__module_gtagSchema"]],[36,[8,"B",[15,"f"],"A",[15,"e"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__ccd_add_1p_data":{"2":true,"4":true}
,
"__ccd_fl_first":{"2":true,"4":true}
,
"__ccd_fl_last":{"2":true,"4":true}
,
"__e":{"2":true,"4":true}
,
"__ogt_1p_data_v2":{"2":true}
,
"__ogt_cps":{"2":true,"4":true}


}
,"blob":{"1":"3"}
,"permissions":{
"__ccd_add_1p_data":{"read_container_data":{}}
,
"__ccd_fl_first":{}
,
"__ccd_fl_last":{}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__ogt_1p_data_v2":{"detect_user_provided_data":{"limitDataSources":true,"allowAutoDataSources":true,"allowManualDataSources":false,"allowCodeDataSources":false}}
,
"__ogt_cps":{"access_core_platform_services":{"writeAllCps":true}}


}



,"security_groups":{
"google":[
"__ccd_add_1p_data"
,
"__ccd_fl_first"
,
"__ccd_fl_last"
,
"__e"
,
"__ogt_1p_data_v2"
,
"__ogt_cps"

]


}



};




var ba,ca=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},da=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ea=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},fa=ea(this),ha=function(a,b){if(b)a:{for(var c=fa,d=a.split("."),e=0;e<d.length-1;e++){var f=d[e];if(!(f in c))break a;c=c[f]}var g=d[d.length-1],h=c[g],m=b(h);m!=h&&m!=null&&da(c,g,{configurable:!0,writable:!0,value:m})}};
ha("Symbol",function(a){if(a)return a;var b=function(f,g){this.D=f;da(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.D};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});var ia=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ma;
if(typeof Object.setPrototypeOf=="function")ma=Object.setPrototypeOf;else{var oa;a:{var pa={a:!0},qa={};try{qa.__proto__=pa;oa=qa.a;break a}catch(a){}oa=!1}ma=oa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var ra=ma,sa=function(a,b){a.prototype=ia(b.prototype);a.prototype.constructor=a;if(ra)ra(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.lq=b.prototype},k=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:ca(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},ta=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},ua=function(a){return a instanceof Array?a:ta(k(a))},wa=function(a){return va(a,a)},va=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},xa=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};ha("Object.assign",function(a){return a||xa});
var ya=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var za=this||self,Aa=function(a,b){function c(){}c.prototype=b.prototype;a.lq=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.jr=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ba=function(a,b){this.type=a;this.data=b};var Ca=function(){this.map={};this.D={}};Ca.prototype.get=function(a){return this.map["dust."+a]};Ca.prototype.set=function(a,b){var c="dust."+a;this.D.hasOwnProperty(c)||(this.map[c]=b)};Ca.prototype.has=function(a){return this.map.hasOwnProperty("dust."+a)};Ca.prototype.remove=function(a){var b="dust."+a;this.D.hasOwnProperty(b)||delete this.map[b]};
var Da=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Ca.prototype.za=function(){return Da(this,1)};Ca.prototype.zc=function(){return Da(this,2)};Ca.prototype.Xb=function(){return Da(this,3)};var Fa=function(){};Fa.prototype.reset=function(){};var Ga=function(a,b){this.R=a;this.parent=b;this.D=this.J=void 0;this.Rc=!1;this.O=function(c,d,e){return c.apply(d,e)};this.values=new Ca};Ga.prototype.add=function(a,b){Ha(this,a,b,!1)};var Ha=function(a,b,c,d){if(!a.Rc)if(d){var e=a.values;e.set(b,c);e.D["dust."+b]=!0}else a.values.set(b,c)};Ga.prototype.set=function(a,b){this.Rc||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
Ga.prototype.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};Ga.prototype.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};var Ia=function(a){var b=new Ga(a.R,a);a.J&&(b.J=a.J);b.O=a.O;b.D=a.D;return b};Ga.prototype.pe=function(){return this.R};Ga.prototype.fb=function(){this.Rc=!0};var Ja=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.qm=a;this.Wl=c===void 0?!1:c;this.debugInfo=[];this.D=b};sa(Ja,Error);var Ka=function(a){return a instanceof Ja?a:new Ja(a,void 0,!0)};function La(a,b){for(var c,d=k(b),e=d.next();!e.done&&!(c=Ma(a,e.value),c instanceof Ba);e=d.next());return c}function Ma(a,b){try{var c=k(b),d=c.next().value,e=ta(c),f=a.get(String(d));if(!f||typeof f.invoke!=="function")throw Ka(Error("Attempting to execute non-function "+b[0]+"."));return f.invoke.apply(f,[a].concat(ua(e)))}catch(h){var g=a.J;g&&g(h,b.context?{id:b[0],line:b.context.line}:null);throw h;}};var Oa=function(){this.J=new Fa;this.D=new Ga(this.J)};ba=Oa.prototype;ba.pe=function(){return this.J};ba.execute=function(a){return this.Mj([a].concat(ua(ya.apply(1,arguments))))};ba.Mj=function(){for(var a,b=k(ya.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Ma(this.D,c.value);return a};ba.Xn=function(a){var b=ya.apply(1,arguments),c=Ia(this.D);c.D=a;for(var d,e=k(b),f=e.next();!f.done;f=e.next())d=Ma(c,f.value);return d};ba.fb=function(){this.D.fb()};var Pa=function(){this.Da=!1;this.aa=new Ca};ba=Pa.prototype;ba.get=function(a){return this.aa.get(a)};ba.set=function(a,b){this.Da||this.aa.set(a,b)};ba.has=function(a){return this.aa.has(a)};ba.remove=function(a){this.Da||this.aa.remove(a)};ba.za=function(){return this.aa.za()};ba.zc=function(){return this.aa.zc()};ba.Xb=function(){return this.aa.Xb()};ba.fb=function(){this.Da=!0};ba.Rc=function(){return this.Da};function Qa(){for(var a=Ra,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function Sa(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var Ra,Ta;function Ua(a){Ra=Ra||Sa();Ta=Ta||Qa();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(Ra[m],Ra[n],Ra[p],Ra[q])}return b.join("")}
function Va(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=Ta[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}Ra=Ra||Sa();Ta=Ta||Qa();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var Wa={};function Za(a,b){Wa[a]=Wa[a]||[];Wa[a][b]=!0}function $a(){Wa.GTAG_EVENT_FEATURE_CHANNEL=ab}function bb(a){var b=Wa[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return Ua(c.join("")).replace(/\.+$/,"")}function cb(){for(var a=[],b=Wa.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function db(){}function eb(a){return typeof a==="function"}function gb(a){return typeof a==="string"}function hb(a){return typeof a==="number"&&!isNaN(a)}function ib(a){return Array.isArray(a)?a:[a]}function jb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function kb(a,b){if(!hb(a)||!hb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function lb(a,b){for(var c=new mb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function nb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function ob(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function pb(a){return Math.round(Number(a))||0}function qb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function rb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function sb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function tb(){return new Date(Date.now())}function ub(){return tb().getTime()}var mb=function(){this.prefix="gtm.";this.values={}};mb.prototype.set=function(a,b){this.values[this.prefix+a]=b};mb.prototype.get=function(a){return this.values[this.prefix+a]};mb.prototype.contains=function(a){return this.get(a)!==void 0};
function vb(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function wb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function xb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function yb(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function zb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Ab(a,b){var c=l;b=b||[];for(var d=c,e=0;e<a.length-1;e++){if(!d.hasOwnProperty(a[e]))return;d=d[a[e]];if(b.indexOf(d)>=0)return}return d}function Bb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Cb=/^\w{1,9}$/;function Db(a,b){a=a||{};b=b||",";var c=[];nb(a,function(d,e){Cb.test(d)&&e&&c.push(d)});return c.join(b)}function Eb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Fb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Gb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Hb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Jb=globalThis.trustedTypes,Kb;function Lb(){var a=null;if(!Jb)return a;try{var b=function(c){return c};a=Jb.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Mb(){Kb===void 0&&(Kb=Lb());return Kb};var Nb=function(a){this.D=a};Nb.prototype.toString=function(){return this.D+""};function Ob(a){var b=a,c=Mb(),d=c?c.createScriptURL(b):b;return new Nb(d)}function Pb(a){if(a instanceof Nb)return a.D;throw Error("");};var Qb=wa([""]),Rb=va(["\x00"],["\\0"]),Sb=va(["\n"],["\\n"]),Tb=va(["\x00"],["\\u0000"]);function Ub(a){return a.toString().indexOf("`")===-1}Ub(function(a){return a(Qb)})||Ub(function(a){return a(Rb)})||Ub(function(a){return a(Sb)})||Ub(function(a){return a(Tb)});var Vb=function(a){this.D=a};Vb.prototype.toString=function(){return this.D};var Wb=function(a){this.Fp=a};function Xb(a){return new Wb(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var Yb=[Xb("data"),Xb("http"),Xb("https"),Xb("mailto"),Xb("ftp"),new Wb(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function Zb(a){var b;b=b===void 0?Yb:b;if(a instanceof Vb)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof Wb&&d.Fp(a))return new Vb(a)}}var $b=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function ac(a){var b;if(a instanceof Vb)if(a instanceof Vb)b=a.D;else throw Error("");else b=$b.test(a)?a:void 0;return b};function bc(a,b){var c=ac(b);c!==void 0&&(a.action=c)};function cc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var dc=function(a){this.D=a};dc.prototype.toString=function(){return this.D+""};var fc=function(){this.D=ec[0].toLowerCase()};fc.prototype.toString=function(){return this.D};function hc(a,b){var c=[new fc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof fc)g=f.D;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var ic=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function jc(a){return a===null?"null":a===void 0?"undefined":a};var l=window,kc=window.history,y=document,lc=navigator;function mc(){var a;try{a=lc.serviceWorker}catch(b){return}return a}var nc=y.currentScript,oc=nc&&nc.src;function pc(a,b){var c=l[a];l[a]=c===void 0?b:c;return l[a]}function qc(a){return(lc.userAgent||"").indexOf(a)!==-1}function rc(){return qc("Firefox")||qc("FxiOS")}function sc(){return(qc("GSA")||qc("GoogleApp"))&&(qc("iPhone")||qc("iPad"))}function tc(){return qc("Edg/")||qc("EdgA/")||qc("EdgiOS/")}
var uc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},vc={onload:1,src:1,width:1,height:1,style:1};function wc(a,b,c){b&&nb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function xc(a,b,c,d,e){var f=y.createElement("script");wc(f,d,uc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Ob(jc(a));f.src=Pb(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=y.getElementsByTagName("script")[0]||y.body||y.head;r.parentNode.insertBefore(f,r)}return f}
function yc(){if(oc){var a=oc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function zc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=y.createElement("iframe"),h=!0);wc(g,c,vc);d&&nb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=y.body&&y.body.lastChild||y.body||y.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function Ac(a,b,c,d){return Bc(a,b,c,d)}function Cc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Dc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function A(a){l.setTimeout(a,0)}function Ec(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Fc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Gc(a){var b=y.createElement("div"),c=b,d,e=jc("A<div>"+a+"</div>"),f=Mb(),g=f?f.createHTML(e):e;d=new dc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof dc)h=d.D;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Hc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Ic(a,b,c){var d;try{d=lc.sendBeacon&&lc.sendBeacon(a)}catch(e){Za("TAGGING",15)}d?b==null||b():Bc(a,b,c)}function Jc(a,b){try{return lc.sendBeacon(a,b)}catch(c){Za("TAGGING",15)}return!1}var Kc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Lc(a,b,c,d,e){if(Mc()){var f=Object.assign({},Kc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=l.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.Gh)return e==null||e(),!1;if(b){var h=
Jc(a,b);h?d==null||d():e==null||e();return h}Nc(a,d,e);return!0}function Mc(){return typeof l.fetch==="function"}function Oc(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Pc(){var a=l.performance;if(a&&eb(a.now))return a.now()}
function Qc(){var a,b=l.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function Rc(){return l.performance||void 0}function Sc(){var a=l.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Bc=function(a,b,c,d){var e=new Image(1,1);wc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Nc=Ic;function Tc(a,b){return this.evaluate(a)&&this.evaluate(b)}function Uc(a,b){return this.evaluate(a)===this.evaluate(b)}function Vc(a,b){return this.evaluate(a)||this.evaluate(b)}function Wc(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function Xc(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function Yc(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=l.location.href;d instanceof Pa&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var Zc=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,$c=function(a){if(a==null)return String(a);var b=Zc.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},ad=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},bd=function(a){if(!a||$c(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!ad(a,"constructor")&&!ad(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
ad(a,b)},cd=function(a,b){var c=b||($c(a)=="array"?[]:{}),d;for(d in a)if(ad(a,d)){var e=a[d];$c(e)=="array"?($c(c[d])!="array"&&(c[d]=[]),c[d]=cd(e,c[d])):bd(e)?(bd(c[d])||(c[d]={}),c[d]=cd(e,c[d])):c[d]=e}return c};function dd(a){if(a==void 0||Array.isArray(a)||bd(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function ed(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var fd=function(a){a=a===void 0?[]:a;this.aa=new Ca;this.values=[];this.Da=!1;for(var b in a)a.hasOwnProperty(b)&&(ed(b)?this.values[Number(b)]=a[Number(b)]:this.aa.set(b,a[b]))};ba=fd.prototype;ba.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof fd?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
ba.set=function(a,b){if(!this.Da)if(a==="length"){if(!ed(b))throw Ka(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else ed(a)?this.values[Number(a)]=b:this.aa.set(a,b)};ba.get=function(a){return a==="length"?this.length():ed(a)?this.values[Number(a)]:this.aa.get(a)};ba.length=function(){return this.values.length};ba.za=function(){for(var a=this.aa.za(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
ba.zc=function(){for(var a=this.aa.zc(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};ba.Xb=function(){for(var a=this.aa.Xb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};ba.remove=function(a){ed(a)?delete this.values[Number(a)]:this.Da||this.aa.remove(a)};ba.pop=function(){return this.values.pop()};ba.push=function(){return this.values.push.apply(this.values,ua(ya.apply(0,arguments)))};
ba.shift=function(){return this.values.shift()};ba.splice=function(a,b){var c=ya.apply(2,arguments);return b===void 0&&c.length===0?new fd(this.values.splice(a)):new fd(this.values.splice.apply(this.values,[a,b||0].concat(ua(c))))};ba.unshift=function(){return this.values.unshift.apply(this.values,ua(ya.apply(0,arguments)))};ba.has=function(a){return ed(a)&&this.values.hasOwnProperty(a)||this.aa.has(a)};ba.fb=function(){this.Da=!0;Object.freeze(this.values)};ba.Rc=function(){return this.Da};
function gd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var hd=function(a,b){this.functionName=a;this.oe=b;this.aa=new Ca;this.Da=!1};ba=hd.prototype;ba.toString=function(){return this.functionName};ba.getName=function(){return this.functionName};ba.getKeys=function(){return new fd(this.za())};ba.invoke=function(a){return this.oe.call.apply(this.oe,[new id(this,a)].concat(ua(ya.apply(1,arguments))))};ba.Ib=function(a){var b=ya.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(ua(b)))}catch(c){}};ba.get=function(a){return this.aa.get(a)};
ba.set=function(a,b){this.Da||this.aa.set(a,b)};ba.has=function(a){return this.aa.has(a)};ba.remove=function(a){this.Da||this.aa.remove(a)};ba.za=function(){return this.aa.za()};ba.zc=function(){return this.aa.zc()};ba.Xb=function(){return this.aa.Xb()};ba.fb=function(){this.Da=!0};ba.Rc=function(){return this.Da};var jd=function(a,b){hd.call(this,a,b)};sa(jd,hd);var kd=function(a,b){hd.call(this,a,b)};sa(kd,hd);var id=function(a,b){this.oe=a;this.M=b};
id.prototype.evaluate=function(a){var b=this.M;return Array.isArray(a)?Ma(b,a):a};id.prototype.getName=function(){return this.oe.getName()};id.prototype.pe=function(){return this.M.pe()};var ld=function(){this.map=new Map};ld.prototype.set=function(a,b){this.map.set(a,b)};ld.prototype.get=function(a){return this.map.get(a)};var md=function(){this.keys=[];this.values=[]};md.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};md.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function nd(){try{return Map?new ld:new md}catch(a){return new md}};var od=function(a){if(a instanceof od)return a;if(dd(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};od.prototype.getValue=function(){return this.value};od.prototype.toString=function(){return String(this.value)};var qd=function(a){this.promise=a;this.Da=!1;this.aa=new Ca;this.aa.set("then",pd(this));this.aa.set("catch",pd(this,!0));this.aa.set("finally",pd(this,!1,!0))};ba=qd.prototype;ba.get=function(a){return this.aa.get(a)};ba.set=function(a,b){this.Da||this.aa.set(a,b)};ba.has=function(a){return this.aa.has(a)};ba.remove=function(a){this.Da||this.aa.remove(a)};ba.za=function(){return this.aa.za()};ba.zc=function(){return this.aa.zc()};ba.Xb=function(){return this.aa.Xb()};
var pd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new jd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof jd||(d=void 0);e instanceof jd||(e=void 0);var f=Ia(this.M),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new od(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new qd(h)})};qd.prototype.fb=function(){this.Da=!0};qd.prototype.Rc=function(){return this.Da};function rd(a,b,c){var d=nd(),e=function(g,h){for(var m=g.za(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof fd){var m=[];d.set(g,m);for(var n=g.za(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof qd)return g.promise.then(function(u){return rd(u,b,1)},function(u){return Promise.reject(rd(u,b,1))});if(g instanceof Pa){var q={};d.set(g,q);e(g,q);return q}if(g instanceof jd){var r=function(){for(var u=
ya.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=sd(u[w],b,c);var x=new Ga(b?b.pe():new Fa);b&&(x.D=b.D);return f(g.invoke.apply(g,[x].concat(ua(v))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof od&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function sd(a,b,c){var d=nd(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||ob(g)){var m=new fd;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(bd(g)){var p=new Pa;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new jd("",function(){for(var u=ya.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=rd(this.evaluate(u[w]),b,c);return f((0,this.M.O)(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new od(g)};return f(a)};var td={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof fd)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new fd(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new fd(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new fd(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
ua(ya.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Ka(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Ka(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Ka(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Ka(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=gd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new fd(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=gd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(ua(ya.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,ua(ya.apply(1,arguments)))}};var ud={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},vd=new Ba("break"),wd=new Ba("continue");function xd(a,b){return this.evaluate(a)+this.evaluate(b)}function yd(a,b){return this.evaluate(a)&&this.evaluate(b)}
function zd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof fd))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Ka(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=rd(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Ka(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(ud.hasOwnProperty(e)){var m=2;m=1;var n=rd(f,void 0,m);return sd(d[e].apply(d,n),this.M)}throw Ka(Error("TypeError: "+e+" is not a function"));}if(d instanceof fd){if(d.has(e)){var p=d.get(String(e));if(p instanceof jd){var q=gd(f);return p.invoke.apply(p,[this.M].concat(ua(q)))}throw Ka(Error("TypeError: "+e+" is not a function"));}if(td.supportedMethods.indexOf(e)>=
0){var r=gd(f);return td[e].call.apply(td[e],[d,this.M].concat(ua(r)))}}if(d instanceof jd||d instanceof Pa||d instanceof qd){if(d.has(e)){var t=d.get(e);if(t instanceof jd){var u=gd(f);return t.invoke.apply(t,[this.M].concat(ua(u)))}throw Ka(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof jd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof od&&e==="toString")return d.toString();throw Ka(Error("TypeError: Object has no '"+
e+"' property."));}function Ad(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.M;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Bd(){var a=ya.apply(0,arguments),b=Ia(this.M),c=La(b,a);if(c instanceof Ba)return c}function Cd(){return vd}function Dd(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Ba)return d}}
function Ed(){for(var a=this.M,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);Ha(a,c,d,!0)}}}function Fd(){return wd}function Gd(a,b){return new Ba(a,this.evaluate(b))}function Hd(a,b){for(var c=ya.apply(2,arguments),d=new fd,e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(ua(c));this.M.add(a,this.evaluate(g))}function Id(a,b){return this.evaluate(a)/this.evaluate(b)}
function Jd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof od,f=d instanceof od;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function Kd(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Ld(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=La(f,d);if(g instanceof Ba){if(g.type==="break")break;if(g.type==="return")return g}}}
function Md(a,b,c){if(typeof b==="string")return Ld(a,function(){return b.length},function(f){return f},c);if(b instanceof Pa||b instanceof qd||b instanceof fd||b instanceof jd){var d=b.za(),e=d.length;return Ld(a,function(){return e},function(f){return d[f]},c)}}function Nd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Md(function(h){g.set(d,h);return g},e,f)}
function Od(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Md(function(h){var m=Ia(g);Ha(m,d,h,!0);return m},e,f)}function Qd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Md(function(h){var m=Ia(g);m.add(d,h);return m},e,f)}function Rd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Sd(function(h){g.set(d,h);return g},e,f)}
function Td(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Sd(function(h){var m=Ia(g);Ha(m,d,h,!0);return m},e,f)}function Ud(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Sd(function(h){var m=Ia(g);m.add(d,h);return m},e,f)}
function Sd(a,b,c){if(typeof b==="string")return Ld(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof fd)return Ld(a,function(){return b.length()},function(d){return b.get(d)},c);throw Ka(Error("The value is not iterable."));}
function Vd(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof fd))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.M,h=this.evaluate(d),m=Ia(g);for(e(g,m);Ma(m,b);){var n=La(m,h);if(n instanceof Ba){if(n.type==="break")break;if(n.type==="return")return n}var p=Ia(g);e(m,p);Ma(p,c);m=p}}
function Wd(a,b){var c=ya.apply(2,arguments),d=this.M,e=this.evaluate(b);if(!(e instanceof fd))throw Error("Error: non-List value given for Fn argument names.");return new jd(a,function(){return function(){var f=ya.apply(0,arguments),g=Ia(d);g.D===void 0&&(g.D=this.M.D);for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new fd(h));var r=La(g,c);if(r instanceof Ba)return r.type===
"return"?r.data:r}}())}function Xd(a){var b=this.evaluate(a),c=this.M;if(Yd&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function Zd(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Ka(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Pa||d instanceof qd||d instanceof fd||d instanceof jd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:ed(e)&&(c=d[e]);else if(d instanceof od)return;return c}function $d(a,b){return this.evaluate(a)>this.evaluate(b)}function ae(a,b){return this.evaluate(a)>=this.evaluate(b)}
function be(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof od&&(c=c.getValue());d instanceof od&&(d=d.getValue());return c===d}function ce(a,b){return!be.call(this,a,b)}function de(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=La(this.M,d);if(e instanceof Ba)return e}var Yd=!1;
function ee(a,b){return this.evaluate(a)<this.evaluate(b)}function fe(a,b){return this.evaluate(a)<=this.evaluate(b)}function ge(){for(var a=new fd,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function he(){for(var a=new Pa,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function ie(a,b){return this.evaluate(a)%this.evaluate(b)}
function je(a,b){return this.evaluate(a)*this.evaluate(b)}function ke(a){return-this.evaluate(a)}function le(a){return!this.evaluate(a)}function me(a,b){return!Jd.call(this,a,b)}function ne(){return null}function oe(a,b){return this.evaluate(a)||this.evaluate(b)}function pe(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function qe(a){return this.evaluate(a)}function re(){return ya.apply(0,arguments)}function se(a){return new Ba("return",this.evaluate(a))}
function te(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Ka(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof jd||d instanceof fd||d instanceof Pa)&&d.set(String(e),f);return f}function ue(a,b){return this.evaluate(a)-this.evaluate(b)}
function ve(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Ba){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Ba&&(g.type==="return"||g.type==="continue")))return g}
function xe(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function ye(a){var b=this.evaluate(a);return b instanceof jd?"function":typeof b}function ze(){for(var a=this.M,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function Ae(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=La(this.M,e);if(f instanceof Ba){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=La(this.M,e);if(g instanceof Ba){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Be(a){return~Number(this.evaluate(a))}function Ce(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function De(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Ee(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Fe(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Ge(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function He(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Ie(){}
function Je(a,b,c){try{var d=this.evaluate(b);if(d instanceof Ba)return d}catch(h){if(!(h instanceof Ja&&h.Wl))throw h;var e=Ia(this.M);a!==""&&(h instanceof Ja&&(h=h.qm),e.add(a,new od(h)));var f=this.evaluate(c),g=La(e,f);if(g instanceof Ba)return g}}function Ke(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Ja&&f.Wl))throw f;c=f}var e=this.evaluate(b);if(e instanceof Ba)return e;if(c)throw c;if(d instanceof Ba)return d};var Me=function(){this.D=new Oa;Le(this)};Me.prototype.execute=function(a){return this.D.Mj(a)};var Le=function(a){var b=function(c,d){var e=new kd(String(c),d);e.fb();a.D.D.set(String(c),e)};b("map",he);b("and",Tc);b("contains",Wc);b("equals",Uc);b("or",Vc);b("startsWith",Xc);b("variable",Yc)};var Oe=function(){this.J=!1;this.D=new Oa;Ne(this);this.J=!0};Oe.prototype.execute=function(a){return Pe(this.D.Mj(a))};var Qe=function(a,b,c){return Pe(a.D.Xn(b,c))};Oe.prototype.fb=function(){this.D.fb()};
var Ne=function(a){var b=function(c,d){var e=String(c),f=new kd(e,d);f.fb();a.D.D.set(e,f)};b(0,xd);b(1,yd);b(2,zd);b(3,Ad);b(56,Fe);b(57,Ce);b(58,Be);b(59,He);b(60,De);b(61,Ee);b(62,Ge);b(53,Bd);b(4,Cd);b(5,Dd);b(68,Je);b(52,Ed);b(6,Fd);b(49,Gd);b(7,ge);b(8,he);b(9,Dd);b(50,Hd);b(10,Id);b(12,Jd);b(13,Kd);b(67,Ke);b(51,Wd);b(47,Nd);b(54,Od);b(55,Qd);b(63,Vd);b(64,Rd);b(65,Td);b(66,Ud);b(15,Xd);b(16,Zd);b(17,Zd);b(18,$d);b(19,ae);b(20,be);b(21,ce);b(22,de);b(23,ee);b(24,fe);b(25,ie);b(26,je);b(27,
ke);b(28,le);b(29,me);b(45,ne);b(30,oe);b(32,pe);b(33,pe);b(34,qe);b(35,qe);b(46,re);b(36,se);b(43,te);b(37,ue);b(38,ve);b(39,xe);b(40,ye);b(44,Ie);b(41,ze);b(42,Ae)};Oe.prototype.pe=function(){return this.D.pe()};function Pe(a){if(a instanceof Ba||a instanceof jd||a instanceof fd||a instanceof Pa||a instanceof qd||a instanceof od||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var Re=function(a){this.message=a};function Se(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new Re("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function Te(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var Ue=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function Ve(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+Se(e)+c}a<<=2;d||(a|=32);return c=""+Se(a|b)+c};var We=function(){function a(b){return{toString:function(){return b}}}return{Om:a("consent"),bk:a("convert_case_to"),dk:a("convert_false_to"),ek:a("convert_null_to"),fk:a("convert_true_to"),gk:a("convert_undefined_to"),Aq:a("debug_mode_metadata"),Ha:a("function"),Ci:a("instance_name"),ao:a("live_only"),bo:a("malware_disabled"),METADATA:a("metadata"),fo:a("original_activity_id"),Rq:a("original_vendor_template_id"),Qq:a("once_on_load"),eo:a("once_per_event"),zl:a("once_per_load"),Tq:a("priority_override"),
Wq:a("respected_consent_types"),Il:a("setup_tags"),ph:a("tag_id"),Nl:a("teardown_tags")}}();var sf;var tf=[],uf=[],vf=[],wf=[],xf=[],yf,zf,Af;function Bf(a){Af=Af||a}
function Cf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)tf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)wf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)vf.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Df(p[r])}uf.push(p)}}
function Df(a){}var Ef,Ff=[],Gf=[];function Hf(a,b){var c={};c[We.Ha]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function If(a,b,c){try{return zf(Jf(a,b,c))}catch(d){JSON.stringify(a)}return 2}function Kf(a){var b=a[We.Ha];if(!b)throw Error("Error: No function name given for function call.");return!!yf[b]}
var Jf=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Lf(a[e],b,c));return d},Lf=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Lf(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=tf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[We.Ci]);try{var m=Jf(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=Mf(m,{event:b,index:f,type:2,
name:h});Ef&&(d=Ef.Bo(d,m))}catch(z){b.logMacroError&&b.logMacroError(z,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Lf(a[n],b,c)]=Lf(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Lf(a[q],b,c);Af&&(p=p||Af.Cp(r));d.push(r)}return Af&&p?Af.Go(d):d.join("");case "escape":d=Lf(a[1],b,c);if(Af&&Array.isArray(a[1])&&a[1][0]==="macro"&&Af.Dp(a))return Af.Sp(d);d=String(d);for(var t=2;t<a.length;t++)cf[a[t]]&&(d=cf[a[t]](d));return d;
case "tag":var u=a[1];if(!wf[u])throw Error("Unable to resolve tag reference "+u+".");return{dm:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[We.Ha]=a[1];var w=If(v,b,c),x=!!a[4];return x||w!==2?x!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},Mf=function(a,b){var c=a[We.Ha],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=yf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Ff.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&zb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=tf[q];break;case 1:r=wf[q];break;default:n="";break a}var t=r&&r[We.Ci];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,w;if(f&&Gf.indexOf(c)===-1){Gf.push(c);
var x=ub();u=e(g);var z=ub()-x,C=ub();v=sf(c,h,b);w=z-(ub()-C)}else if(e&&(u=e(g)),!e||f)v=sf(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),dd(u)?(Array.isArray(u)?Array.isArray(v):bd(u)?bd(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:v};var Nf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};sa(Nf,Error);Nf.prototype.getMessage=function(){return this.message};function Of(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)Of(a[c],b[c])}};function Pf(){return function(a,b){var c;var d=Qf;a instanceof Ja?(a.D=d,c=a):c=new Ja(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function Qf(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)hb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function Rf(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=Sf(a),f=0;f<uf.length;f++){var g=uf[f],h=Tf(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<wf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function Tf(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function Sf(a){var b=[];return function(c){b[c]===void 0&&(b[c]=If(vf[c],a));return b[c]}};function Uf(a,b){b[We.bk]&&typeof a==="string"&&(a=b[We.bk]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(We.ek)&&a===null&&(a=b[We.ek]);b.hasOwnProperty(We.gk)&&a===void 0&&(a=b[We.gk]);b.hasOwnProperty(We.fk)&&a===!0&&(a=b[We.fk]);b.hasOwnProperty(We.dk)&&a===!1&&(a=b[We.dk]);return a};var Vf=function(){this.D={}},Xf=function(a,b){var c=Wf.D,d;(d=c.D)[a]!=null||(d[a]=[]);c.D[a].push(function(){return b.apply(null,ua(ya.apply(0,arguments)))})};function Yf(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Nf(c,d,g);}}
function Zf(a,b,c){return function(d){if(d){var e=a.D[d],f=a.D.all;if(e||f){var g=c.apply(void 0,[d].concat(ua(ya.apply(1,arguments))));Yf(e,b,d,g);Yf(f,b,d,g)}}}};var cg=function(){var a=data.permissions||{},b=$f.ctid,c=this;this.J={};this.D=new Vf;var d={},e={},f=Zf(this.D,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(ua(ya.apply(1,arguments)))):{}});nb(a,function(g,h){function m(p){var q=ya.apply(1,arguments);if(!n[p])throw ag(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(ua(q)))}var n={};nb(h,function(p,q){var r=bg(p,q);n[p]=r.assert;d[p]||(d[p]=r.U);r.Tl&&!e[p]&&(e[p]=r.Tl)});c.J[g]=function(p,
q){var r=n[p];if(!r)throw ag(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(ua(t.slice(1))))}})},dg=function(a){return Wf.J[a]||function(){}};
function bg(a,b){var c=Hf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=ag;try{return Mf(c)}catch(d){return{assert:function(e){throw new Nf(e,{},"Permission "+e+" is unknown.");},U:function(){throw new Nf(a,{},"Permission "+a+" is unknown.");}}}}function ag(a,b,c){return new Nf(a,b,c)};var eg=!1;var fg={};fg.Gm=qb('');fg.Po=qb('');function kg(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var lg=[],mg={};function ng(a){return lg[a]===void 0?!1:lg[a]};var og=[];function pg(a){switch(a){case 1:return 0;case 38:return 13;case 50:return 10;case 51:return 11;case 53:return 1;case 54:return 2;case 52:return 7;case 75:return 3;case 103:return 14;case 114:return 12;case 115:return 4;case 116:return 5;case 135:return 9;case 136:return 6}}function qg(a,b){og[a]=b;var c=pg(a);c!==void 0&&(lg[c]=b)}function B(a){qg(a,!0)}B(39);B(34);B(35);B(36);
B(56);
B(145);B(18);B(153);
B(144);
B(74);B(120);B(58);
B(5);B(111);B(139);
B(87);B(92);
B(117);
B(159);
B(132);B(20);B(72);
B(113);B(154);
B(116);qg(23,!1),B(24);mg[1]=kg('1',6E4);mg[3]=kg('10',1);
mg[2]=kg('',50);B(29);rg(26,25);
B(9);B(91);
B(123);B(157);
B(158);B(71);B(136);B(127);B(27);B(69);B(135);
B(51);B(50);B(95);B(86);
B(103);B(112);B(63);
B(152);
B(101);
B(122);B(121);
B(108);B(134);
B(115);B(96);B(31);
B(22);B(97);B(48);B(19);B(12);

B(76);B(77);B(81);B(79);
B(28);B(80);
B(90);B(13);
B(163);B(167);B(166);

B(175);
B(179);B(180);function D(a){return!!og[a]}function rg(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?B(b):B(a)};var tg={},ug=(tg.uaa=!0,tg.uab=!0,tg.uafvl=!0,tg.uamb=!0,tg.uam=!0,tg.uap=!0,tg.uapv=!0,tg.uaw=!0,tg);
var Cg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Ag.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Bg.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?zb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Bg=/^[a-z$_][\w-$]*$/i,Ag=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Dg=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Eg(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Fg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Gg=new mb;function Hg(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Gg.get(e);f||(f=new RegExp(b,d),Gg.set(e,f));return f.test(a)}catch(g){return!1}}function Ig(a,b){return String(a).indexOf(String(b))>=0}
function Jg(a,b){return String(a)===String(b)}function Kg(a,b){return Number(a)>=Number(b)}function Lg(a,b){return Number(a)<=Number(b)}function Mg(a,b){return Number(a)>Number(b)}function Ng(a,b){return Number(a)<Number(b)}function Og(a,b){return zb(String(a),String(b))};var Vg=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,Wg={Fn:"function",PixieMap:"Object",List:"Array"};
function Xg(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=Vg.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof jd?n="Fn":m instanceof fd?n="List":m instanceof Pa?n="PixieMap":m instanceof qd?n="PixiePromise":m instanceof od&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((Wg[n]||n)+", which does not match required type ")+
((Wg[h]||h)+"."));}}}function H(a,b,c){for(var d=[],e=k(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof jd?d.push("function"):g instanceof fd?d.push("Array"):g instanceof Pa?d.push("Object"):g instanceof qd?d.push("Promise"):g instanceof od?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function Yg(a){return a instanceof Pa}function Zg(a){return Yg(a)||a===null||$g(a)}
function ah(a){return a instanceof jd}function bh(a){return ah(a)||a===null||$g(a)}function ch(a){return a instanceof fd}function dh(a){return a instanceof od}function eh(a){return typeof a==="string"}function fh(a){return eh(a)||a===null||$g(a)}function gh(a){return typeof a==="boolean"}function hh(a){return gh(a)||$g(a)}function ih(a){return gh(a)||a===null||$g(a)}function jh(a){return typeof a==="number"}function $g(a){return a===void 0};function kh(a){return""+a}
function lh(a,b){var c=[];return c};function mh(a,b){var c=new jd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Ka(g);}});c.fb();return c}
function nh(a,b){var c=new Pa,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];eb(e)?c.set(d,mh(a+"_"+d,e)):bd(e)?c.set(d,nh(a+"_"+d,e)):(hb(e)||gb(e)||typeof e==="boolean")&&c.set(d,e)}c.fb();return c};function oh(a,b){if(!eh(a))throw H(this.getName(),["string"],arguments);if(!fh(b))throw H(this.getName(),["string","undefined"],arguments);var c={},d=new Pa;return d=nh("AssertApiSubject",
c)};function ph(a,b){if(!fh(b))throw H(this.getName(),["string","undefined"],arguments);if(a instanceof qd)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Pa;return d=nh("AssertThatSubject",c)};function qh(a){return function(){for(var b=ya.apply(0,arguments),c=[],d=this.M,e=0;e<b.length;++e)c.push(rd(b[e],d));return sd(a.apply(null,c))}}function rh(){for(var a=Math,b=sh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=qh(a[e].bind(a)))}return c};function th(a){return a!=null&&zb(a,"__cvt_")};function uh(a){var b;return b};function vh(a){var b;return b};function wh(a){try{return encodeURI(a)}catch(b){}};function xh(a){try{return encodeURIComponent(String(a))}catch(b){}};
var yh=function(a,b){for(var c=0;c<b.length;c++){if(a===void 0)return;a=a[b[c]]}return a},zh=function(a,b){var c=b.preHit;if(c){var d=a[0];switch(d){case "hitData":return a.length<2?void 0:yh(c.getHitData(a[1]),a.slice(2));case "metadata":return a.length<2?void 0:yh(c.getMetadata(a[1]),a.slice(2));case "eventName":return c.getEventName();case "destinationId":return c.getDestinationId();default:throw Error(d+" is not a valid field that can be accessed\n                      from PreHit data.");}}},
Bh=function(a,b){if(a){if(a.contextValue!==void 0){var c;a:{var d=a.contextValue,e=d.keyParts;if(e&&e.length!==0){var f=d.namespaceType;switch(f){case 1:c=zh(e,b);break a;case 2:var g=b.macro;c=g?g[e[0]]:void 0;break a;default:throw Error("Unknown Namespace Type used: "+f);}}c=void 0}return c}if(a.booleanExpressionValue!==void 0)return Ah(a.booleanExpressionValue,b);if(a.booleanValue!==void 0)return!!a.booleanValue;if(a.stringValue!==void 0)return String(a.stringValue);if(a.integerValue!==void 0)return Number(a.integerValue);
if(a.doubleValue!==void 0)return Number(a.doubleValue);throw Error("Unknown field used for variable of type ExpressionValue:"+a);}},Ah=function(a,b){var c=a.args;if(!Array.isArray(c)||c.length===0)throw Error('Invalid boolean expression format. Expected "args":'+c+" property to\n         be non-empty array.");var d=function(g){return Bh(g,b)};switch(a.type){case 1:for(var e=0;e<c.length;e++)if(d(c[e]))return!0;return!1;case 2:for(var f=0;f<c.length;f++)if(!d(c[f]))return!1;return c.length>0;case 3:return!d(c[0]);
case 4:return Hg(d(c[0]),d(c[1]),!1);case 5:return Jg(d(c[0]),d(c[1]));case 6:return Og(d(c[0]),d(c[1]));case 7:return Eg(d(c[0]),d(c[1]));case 8:return Ig(d(c[0]),d(c[1]));case 9:return Ng(d(c[0]),d(c[1]));case 10:return Lg(d(c[0]),d(c[1]));case 11:return Mg(d(c[0]),d(c[1]));case 12:return Kg(d(c[0]),d(c[1]));case 13:return Fg(d(c[0]),String(d(c[1])));default:throw Error('Invalid boolean expression format. Expected "type" property tobe a positive integer which is less than 14.');}};function Ch(a){if(!fh(a))throw H(this.getName(),["string|undefined"],arguments);};function Dh(a,b){if(!jh(a)||!jh(b))throw H(this.getName(),["number","number"],arguments);return kb(a,b)};function Eh(){return(new Date).getTime()};function Fh(a){if(a===null)return"null";if(a instanceof fd)return"array";if(a instanceof jd)return"function";if(a instanceof od){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Gh(a){function b(c){return function(d){try{return c(d)}catch(e){(eg||fg.Gm)&&a.call(this,e.message)}}}return{parse:b(function(c){return sd(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(rd(c))}),publicName:"JSON"}};function Hh(a){return pb(rd(a,this.M))};function Ih(a){return Number(rd(a,this.M))};function Jh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Kh(a,b,c){var d=null,e=!1;return e?d:null};var sh="floor ceil round max min abs pow sqrt".split(" ");function Lh(){var a={};return{bp:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Cm:function(b,c){a[b]=c},reset:function(){a={}}}}function Mh(a,b){return function(){return jd.prototype.invoke.apply(a,[b].concat(ua(ya.apply(0,arguments))))}}
function Nh(a,b){if(!eh(a))throw H(this.getName(),["string","any"],arguments);}
function Oh(a,b){if(!eh(a)||!Yg(b))throw H(this.getName(),["string","PixieMap"],arguments);};var Ph={};var Qh=function(a){var b=new Pa;if(a instanceof fd)for(var c=a.za(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof jd)for(var f=a.za(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var m=0;m<a.length;m++)b.set(m,a[m]);return b};
Ph.keys=function(a){Xg(this.getName(),arguments);if(a instanceof fd||a instanceof jd||typeof a==="string")a=Qh(a);if(a instanceof Pa||a instanceof qd)return new fd(a.za());return new fd};
Ph.values=function(a){Xg(this.getName(),arguments);if(a instanceof fd||a instanceof jd||typeof a==="string")a=Qh(a);if(a instanceof Pa||a instanceof qd)return new fd(a.zc());return new fd};
Ph.entries=function(a){Xg(this.getName(),arguments);if(a instanceof fd||a instanceof jd||typeof a==="string")a=Qh(a);if(a instanceof Pa||a instanceof qd)return new fd(a.Xb().map(function(b){return new fd(b)}));return new fd};
Ph.freeze=function(a){(a instanceof Pa||a instanceof qd||a instanceof fd||a instanceof jd)&&a.fb();return a};Ph.delete=function(a,b){if(a instanceof Pa&&!a.Rc())return a.remove(b),!0;return!1};function I(a,b){var c=ya.apply(2,arguments),d=a.M.D;if(!d)throw Error("Missing program state.");if(d.Xp){try{d.Vl.apply(null,[b].concat(ua(c)))}catch(e){throw Za("TAGGING",21),e;}return}d.Vl.apply(null,[b].concat(ua(c)))};var Rh=function(){this.J={};this.D={};this.O=!0;};Rh.prototype.get=function(a,b){var c=this.contains(a)?this.J[a]:void 0;return c};Rh.prototype.contains=function(a){return this.J.hasOwnProperty(a)};
Rh.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.D.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.J[a]=c?void 0:eb(b)?mh(a,b):nh(a,b)};function Sh(a,b){var c=void 0;return c};function Th(){var a={};
return a};var K={m:{Na:"ad_personalization",V:"ad_storage",W:"ad_user_data",ia:"analytics_storage",bc:"region",ja:"consent_updated",qg:"wait_for_update",Tm:"app_remove",Um:"app_store_refund",Vm:"app_store_subscription_cancel",Wm:"app_store_subscription_convert",Xm:"app_store_subscription_renew",Ym:"consent_update",kk:"add_payment_info",lk:"add_shipping_info",Ld:"add_to_cart",Md:"remove_from_cart",mk:"view_cart",Uc:"begin_checkout",Nd:"select_item",hc:"view_item_list",Gc:"select_promotion",jc:"view_promotion",
mb:"purchase",Od:"refund",ub:"view_item",nk:"add_to_wishlist",Zm:"exception",bn:"first_open",dn:"first_visit",qa:"gtag.config",Bb:"gtag.get",fn:"in_app_purchase",Vc:"page_view",gn:"screen_view",hn:"session_start",jn:"source_update",kn:"timing_complete",ln:"track_social",Pd:"user_engagement",mn:"user_id_update",Be:"gclid_link_decoration_source",Ce:"gclid_storage_source",kc:"gclgb",nb:"gclid",pk:"gclid_len",Qd:"gclgs",Rd:"gcllp",Sd:"gclst",ya:"ads_data_redaction",De:"gad_source",Ee:"gad_source_src",
Wc:"gclid_url",qk:"gclsrc",Fe:"gbraid",Td:"wbraid",Fa:"allow_ad_personalization_signals",yg:"allow_custom_scripts",Ge:"allow_direct_google_requests",zg:"allow_display_features",Ag:"allow_enhanced_conversions",Lb:"allow_google_signals",ob:"allow_interest_groups",nn:"app_id",on:"app_installer_id",pn:"app_name",qn:"app_version",Mb:"auid",rn:"auto_detection_enabled",Xc:"aw_remarketing",Sh:"aw_remarketing_only",Bg:"discount",Cg:"aw_feed_country",Dg:"aw_feed_language",sa:"items",Eg:"aw_merchant_id",rk:"aw_basket_type",
He:"campaign_content",Ie:"campaign_id",Je:"campaign_medium",Ke:"campaign_name",Le:"campaign",Me:"campaign_source",Ne:"campaign_term",Nb:"client_id",sk:"rnd",Th:"consent_update_type",sn:"content_group",tn:"content_type",Ob:"conversion_cookie_prefix",Oe:"conversion_id",Qa:"conversion_linker",Uh:"conversion_linker_disabled",Yc:"conversion_api",Fg:"cookie_deprecation",pb:"cookie_domain",qb:"cookie_expires",wb:"cookie_flags",Zc:"cookie_name",Pb:"cookie_path",jb:"cookie_prefix",Hc:"cookie_update",Ud:"country",
Va:"currency",Vh:"customer_buyer_stage",Pe:"customer_lifetime_value",Wh:"customer_loyalty",Xh:"customer_ltv_bucket",Qe:"custom_map",Yh:"gcldc",bd:"dclid",tk:"debug_mode",oa:"developer_id",un:"disable_merchant_reported_purchases",dd:"dc_custom_params",vn:"dc_natural_search",uk:"dynamic_event_settings",vk:"affiliation",Gg:"checkout_option",Zh:"checkout_step",wk:"coupon",Re:"item_list_name",ai:"list_name",wn:"promotions",Se:"shipping",bi:"tax",Hg:"engagement_time_msec",Ig:"enhanced_client_id",Jg:"enhanced_conversions",
xk:"enhanced_conversions_automatic_settings",Kg:"estimated_delivery_date",di:"euid_logged_in_state",Te:"event_callback",xn:"event_category",Qb:"event_developer_id_string",yn:"event_label",ed:"event",Lg:"event_settings",Mg:"event_timeout",zn:"description",An:"fatal",Bn:"experiments",ei:"firebase_id",Vd:"first_party_collection",Ng:"_x_20",nc:"_x_19",yk:"fledge_drop_reason",zk:"fledge",Ak:"flight_error_code",Bk:"flight_error_message",Ck:"fl_activity_category",Dk:"fl_activity_group",fi:"fl_advertiser_id",
Ek:"fl_ar_dedupe",Ue:"match_id",Fk:"fl_random_number",Gk:"tran",Hk:"u",Og:"gac_gclid",Wd:"gac_wbraid",Ik:"gac_wbraid_multiple_conversions",Jk:"ga_restrict_domain",gi:"ga_temp_client_id",Cn:"ga_temp_ecid",fd:"gdpr_applies",Kk:"geo_granularity",Ic:"value_callback",oc:"value_key",qc:"google_analysis_params",Xd:"_google_ng",Yd:"google_signals",Lk:"google_tld",Ve:"gpp_sid",We:"gpp_string",Pg:"groups",Mk:"gsa_experiment_id",Xe:"gtag_event_feature_usage",Nk:"gtm_up",Jc:"iframe_state",Ye:"ignore_referrer",
hi:"internal_traffic_results",Ok:"_is_fpm",Kc:"is_legacy_converted",Lc:"is_legacy_loaded",Qg:"is_passthrough",gd:"_lps",xb:"language",Rg:"legacy_developer_id_string",Ra:"linker",Zd:"accept_incoming",rc:"decorate_forms",la:"domains",Mc:"url_position",Sg:"merchant_feed_label",Tg:"merchant_feed_language",Ug:"merchant_id",Pk:"method",Dn:"name",Qk:"navigation_type",Ze:"new_customer",Vg:"non_interaction",En:"optimize_id",Rk:"page_hostname",af:"page_path",Wa:"page_referrer",Cb:"page_title",Sk:"passengers",
Tk:"phone_conversion_callback",Gn:"phone_conversion_country_code",Uk:"phone_conversion_css_class",Hn:"phone_conversion_ids",Vk:"phone_conversion_number",Wk:"phone_conversion_options",In:"_platinum_request_status",Jn:"_protected_audience_enabled",bf:"quantity",Wg:"redact_device_info",ii:"referral_exclusion_definition",Dq:"_request_start_time",Sb:"restricted_data_processing",Kn:"retoken",Ln:"sample_rate",ji:"screen_name",Nc:"screen_resolution",Xk:"_script_source",Mn:"search_term",rb:"send_page_view",
hd:"send_to",jd:"server_container_url",cf:"session_duration",Xg:"session_engaged",ki:"session_engaged_time",sc:"session_id",Yg:"session_number",df:"_shared_user_id",ef:"delivery_postal_code",Eq:"_tag_firing_delay",Fq:"_tag_firing_time",Gq:"temporary_client_id",li:"_timezone",mi:"topmost_url",Nn:"tracking_id",ni:"traffic_type",Xa:"transaction_id",uc:"transport_url",Yk:"trip_type",ld:"update",Db:"url_passthrough",Zk:"uptgs",ff:"_user_agent_architecture",hf:"_user_agent_bitness",jf:"_user_agent_full_version_list",
kf:"_user_agent_mobile",lf:"_user_agent_model",nf:"_user_agent_platform",pf:"_user_agent_platform_version",qf:"_user_agent_wow64",Ya:"user_data",oi:"user_data_auto_latency",ri:"user_data_auto_meta",si:"user_data_auto_multi",ui:"user_data_auto_selectors",wi:"user_data_auto_status",Tb:"user_data_mode",Zg:"user_data_settings",Sa:"user_id",Ub:"user_properties",al:"_user_region",rf:"us_privacy_string",Ga:"value",bl:"wbraid_multiple_conversions",od:"_fpm_parameters",Ai:"_host_name",ql:"_in_page_command",
rl:"_ip_override",vl:"_is_passthrough_cid",vc:"non_personalized_ads",Oi:"_sst_parameters",mc:"conversion_label",Ba:"page_location",Rb:"global_developer_id_string",kd:"tc_privacy_string"}};var Uh={},Vh=Object.freeze((Uh[K.m.Fa]=1,Uh[K.m.zg]=1,Uh[K.m.Ag]=1,Uh[K.m.Lb]=1,Uh[K.m.sa]=1,Uh[K.m.pb]=1,Uh[K.m.qb]=1,Uh[K.m.wb]=1,Uh[K.m.Zc]=1,Uh[K.m.Pb]=1,Uh[K.m.jb]=1,Uh[K.m.Hc]=1,Uh[K.m.Qe]=1,Uh[K.m.oa]=1,Uh[K.m.uk]=1,Uh[K.m.Te]=1,Uh[K.m.Lg]=1,Uh[K.m.Mg]=1,Uh[K.m.Vd]=1,Uh[K.m.Jk]=1,Uh[K.m.qc]=1,Uh[K.m.Yd]=1,Uh[K.m.Lk]=1,Uh[K.m.Pg]=1,Uh[K.m.hi]=1,Uh[K.m.Kc]=1,Uh[K.m.Lc]=1,Uh[K.m.Ra]=1,Uh[K.m.ii]=1,Uh[K.m.Sb]=1,Uh[K.m.rb]=1,Uh[K.m.hd]=1,Uh[K.m.jd]=1,Uh[K.m.cf]=1,Uh[K.m.ki]=1,Uh[K.m.ef]=1,Uh[K.m.uc]=
1,Uh[K.m.ld]=1,Uh[K.m.Zg]=1,Uh[K.m.Ub]=1,Uh[K.m.od]=1,Uh[K.m.Oi]=1,Uh));Object.freeze([K.m.Ba,K.m.Wa,K.m.Cb,K.m.xb,K.m.ji,K.m.Sa,K.m.ei,K.m.sn]);
var Wh={},Xh=Object.freeze((Wh[K.m.Tm]=1,Wh[K.m.Um]=1,Wh[K.m.Vm]=1,Wh[K.m.Wm]=1,Wh[K.m.Xm]=1,Wh[K.m.bn]=1,Wh[K.m.dn]=1,Wh[K.m.fn]=1,Wh[K.m.hn]=1,Wh[K.m.Pd]=1,Wh)),Yh={},Zh=Object.freeze((Yh[K.m.kk]=1,Yh[K.m.lk]=1,Yh[K.m.Ld]=1,Yh[K.m.Md]=1,Yh[K.m.mk]=1,Yh[K.m.Uc]=1,Yh[K.m.Nd]=1,Yh[K.m.hc]=1,Yh[K.m.Gc]=1,Yh[K.m.jc]=1,Yh[K.m.mb]=1,Yh[K.m.Od]=1,Yh[K.m.ub]=1,Yh[K.m.nk]=1,Yh)),$h=Object.freeze([K.m.Fa,K.m.Ge,K.m.Lb,K.m.Hc,K.m.Vd,K.m.Ye,K.m.rb,K.m.ld]),ai=Object.freeze([].concat(ua($h))),bi=Object.freeze([K.m.qb,
K.m.Mg,K.m.cf,K.m.ki,K.m.Hg]),ci=Object.freeze([].concat(ua(bi))),di={},ei=(di[K.m.V]="1",di[K.m.ia]="2",di[K.m.W]="3",di[K.m.Na]="4",di),fi={},gi=Object.freeze((fi.search="s",fi.youtube="y",fi.playstore="p",fi.shopping="h",fi.ads="a",fi.maps="m",fi));Object.freeze(K.m);var hi={},ii=(hi[K.m.ja]="gcu",hi[K.m.kc]="gclgb",hi[K.m.nb]="gclaw",hi[K.m.pk]="gclid_len",hi[K.m.Qd]="gclgs",hi[K.m.Rd]="gcllp",hi[K.m.Sd]="gclst",hi[K.m.Mb]="auid",hi[K.m.Bg]="dscnt",hi[K.m.Cg]="fcntr",hi[K.m.Dg]="flng",hi[K.m.Eg]="mid",hi[K.m.rk]="bttype",hi[K.m.Nb]="gacid",hi[K.m.mc]="label",hi[K.m.Yc]="capi",hi[K.m.Fg]="pscdl",hi[K.m.Va]="currency_code",hi[K.m.Vh]="clobs",hi[K.m.Pe]="vdltv",hi[K.m.Wh]="clolo",hi[K.m.Xh]="clolb",hi[K.m.tk]="_dbg",hi[K.m.Kg]="oedeld",hi[K.m.Qb]="edid",hi[K.m.yk]=
"fdr",hi[K.m.zk]="fledge",hi[K.m.Og]="gac",hi[K.m.Wd]="gacgb",hi[K.m.Ik]="gacmcov",hi[K.m.fd]="gdpr",hi[K.m.Rb]="gdid",hi[K.m.Xd]="_ng",hi[K.m.Ve]="gpp_sid",hi[K.m.We]="gpp",hi[K.m.Mk]="gsaexp",hi[K.m.Xe]="_tu",hi[K.m.Jc]="frm",hi[K.m.Qg]="gtm_up",hi[K.m.gd]="lps",hi[K.m.Rg]="did",hi[K.m.Sg]="fcntr",hi[K.m.Tg]="flng",hi[K.m.Ug]="mid",hi[K.m.Ze]=void 0,hi[K.m.Cb]="tiba",hi[K.m.Sb]="rdp",hi[K.m.sc]="ecsid",hi[K.m.df]="ga_uid",hi[K.m.ef]="delopc",hi[K.m.kd]="gdpr_consent",hi[K.m.Xa]="oid",hi[K.m.Zk]=
"uptgs",hi[K.m.ff]="uaa",hi[K.m.hf]="uab",hi[K.m.jf]="uafvl",hi[K.m.kf]="uamb",hi[K.m.lf]="uam",hi[K.m.nf]="uap",hi[K.m.pf]="uapv",hi[K.m.qf]="uaw",hi[K.m.oi]="ec_lat",hi[K.m.ri]="ec_meta",hi[K.m.si]="ec_m",hi[K.m.ui]="ec_sel",hi[K.m.wi]="ec_s",hi[K.m.Tb]="ec_mode",hi[K.m.Sa]="userId",hi[K.m.rf]="us_privacy",hi[K.m.Ga]="value",hi[K.m.bl]="mcov",hi[K.m.Ai]="hn",hi[K.m.ql]="gtm_ee",hi[K.m.vc]="npa",hi[K.m.Oe]=null,hi[K.m.Nc]=null,hi[K.m.xb]=null,hi[K.m.sa]=null,hi[K.m.Ba]=null,hi[K.m.Wa]=null,hi[K.m.mi]=
null,hi[K.m.od]=null,hi[K.m.Be]=null,hi[K.m.Ce]=null,hi[K.m.qc]=null,hi);function ji(a,b){if(a){var c=a.split("x");c.length===2&&(ki(b,"u_w",c[0]),ki(b,"u_h",c[1]))}}
function li(a){var b=mi;b=b===void 0?ni:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(oi(q.value)),r.push(oi(q.quantity)),r.push(oi(q.item_id)),r.push(oi(q.start_date)),r.push(oi(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function ni(a){return pi(a.item_id,a.id,a.item_name)}function pi(){for(var a=k(ya.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function qi(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function ki(a,b,c){c===void 0||c===null||c===""&&!ug[b]||(a[b]=c)}function oi(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var L={K:{Uj:"call_conversion",X:"conversion",On:"floodlight",tf:"ga_conversion",Ii:"landing_page",Ia:"page_view",na:"remarketing",Ua:"user_data_lead",La:"user_data_web"}};function ti(a){return ui?y.querySelectorAll(a):null}
function vi(a,b){if(!ui)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!y.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var wi=!1;
if(y.querySelectorAll)try{var xi=y.querySelectorAll(":root");xi&&xi.length==1&&xi[0]==y.documentElement&&(wi=!0)}catch(a){}var ui=wi;function yi(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};function zi(){this.blockSize=-1};function Ai(a,b){this.blockSize=-1;this.blockSize=64;this.O=za.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.R=this.J=0;this.D=[];this.fa=a;this.T=b;this.ma=za.Int32Array?new Int32Array(64):Array(64);Bi===void 0&&(za.Int32Array?Bi=new Int32Array(Ci):Bi=Ci);this.reset()}Aa(Ai,zi);for(var Di=[],Ei=0;Ei<63;Ei++)Di[Ei]=0;var Fi=[].concat(128,Di);
Ai.prototype.reset=function(){this.R=this.J=0;var a;if(za.Int32Array)a=new Int32Array(this.T);else{var b=this.T,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.D=a};
var Gi=function(a){for(var b=a.O,c=a.ma,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.D[0]|0,n=a.D[1]|0,p=a.D[2]|0,q=a.D[3]|0,r=a.D[4]|0,t=a.D[5]|0,u=a.D[6]|0,v=a.D[7]|0,w=0;w<64;w++){var x=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,z=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&u)+(Bi[w]|0)|0)+(c[w]|0)|0)|0;v=u;u=t;t=r;r=q+z|0;q=p;p=n;n=m;m=z+x|0}a.D[0]=a.D[0]+m|0;a.D[1]=a.D[1]+n|0;a.D[2]=a.D[2]+p|0;a.D[3]=a.D[3]+q|0;a.D[4]=a.D[4]+r|0;a.D[5]=a.D[5]+t|0;a.D[6]=a.D[6]+u|0;a.D[7]=a.D[7]+v|0};
Ai.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.J;if(typeof a==="string")for(;c<b;)this.O[d++]=a.charCodeAt(c++),d==this.blockSize&&(Gi(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.O[d++]=g;d==this.blockSize&&(Gi(this),d=0)}else throw Error("message must be string or array");
}this.J=d;this.R+=b};Ai.prototype.digest=function(){var a=[],b=this.R*8;this.J<56?this.update(Fi,56-this.J):this.update(Fi,this.blockSize-(this.J-56));for(var c=63;c>=56;c--)this.O[c]=b&255,b/=256;Gi(this);for(var d=0,e=0;e<this.fa;e++)for(var f=24;f>=0;f-=8)a[d++]=this.D[e]>>f&255;return a};
var Ci=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Bi;function Hi(){Ai.call(this,8,Ii)}Aa(Hi,Ai);var Ii=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Ji=/^[0-9A-Fa-f]{64}$/;function Ki(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Li(a){if(a===""||a==="e0")return Promise.resolve(a);var b;if((b=l.crypto)==null?0:b.subtle){if(Ji.test(a))return Promise.resolve(a);try{var c=Ki(a);return l.crypto.subtle.digest("SHA-256",c).then(function(d){return Mi(d,l)}).catch(function(){return"e2"})}catch(d){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function Mi(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};var Ni={Qm:'100',Rm:'10',Sm:'1000',Sn:'US-CO',Tn:'US-CO',no:'101509157~103116026~103200004~103233427~103351869~103351871~104653070~104653072~104661466~104661468~104698127~104698129'},Oi={Lo:Number(Ni.Qm)||0,Mo:Number(Ni.Rm)||0,Oo:Number(Ni.Sm)||0,hp:Ni.Sn.split("~"),jp:Ni.Tn.split("~"),wq:Ni.no};function M(a){Za("GTM",a)};
var Ti=function(a,b){var c=["tv.1"],d=Pi(a);if(d)return c.push(d),{eb:!1,Nj:c.join("~"),mg:{}};var e={},f=0;var g=Qi(a,function(p,q,r){var t=p.value,u;if(r){var v=q+"__"+f++;u="${userData."+v+"|sha256}";e[v]=t}else u=encodeURIComponent(encodeURIComponent(t));var w;c.push(""+q+((w=p.index)!=null?w:"")+"."+u)}).eb;var h=c.join("~"),m={userData:e},n=b===3;return b===2||n?{eb:g,Nj:h,mg:m,No:n?"tv.9~${"+(h+
"|encryptRsa}"):"tv.1~${"+(h+"|encrypt}"),encryptionKeyString:n?Ri():Si()}:{eb:g,Nj:h,mg:m}},Vi=function(a){if(!(a!=null&&Object.keys(a).length>0))return!1;var b=Ui(a);return Qi(b,function(){}).eb},Qi=function(a,b){b=b===void 0?function(){}:b;for(var c=!1,d=!1,e=k(a),f=e.next();!f.done;f=e.next()){var g=f.value;if(g.value){var h=Wi[g.name];if(h){var m=Xi(g);m&&(c=!0);d=!0;b(g,h,m)}}}return{eb:d,pj:c}},Xi=function(a){var b=Yi(a.name),c=/^e\d+$/.test(a.value),d;if(d=b&&!c){var e=a.value;d=!(Zi.test(e)||
Ji.test(e))}return d},Yi=function(a){return $i.indexOf(a)!==-1},Si=function(){return'{\x22keys\x22:[{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BObkUP6myK7vHywb1JE+F03yiV8tIYRv0g53B6jJB5BQc/xtOoHLiEF4icz59yb3+jtQaC3A/A76GuTDbRHNOcQ\x3d\x22,\x22version\x22:0},\x22id\x22:\x22eb3a7cc8-30f8-4bf9-a8ca-7dbfc41b8a69\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BFRQsbuKwmDKxdB6szivtiuPKUrftiTeBSHHCIJBmLRwqFMoeFwtaAlwKF9zdQWi9mli+b2Y0JRoXcgt9/BBNdc\x3d\x22,\x22version\x22:0},\x22id\x22:\x22d34c6585-1880-4d15-ae4d-1d6b92f1b74d\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BOlqKrXBm/cvVLMwhtnj6XuBUfHoAh+vh0d1l4OF//hByeHHmpg0aGeUzAJzck55wGmkpEHKO+wGDVuJv113h8Q\x3d\x22,\x22version\x22:0},\x22id\x22:\x22a3df8b1b-09b2-4d9a-90b2-4a9a1e19a39c\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BHVl9Ou0HGkzwXpCcoOgv2JfoD0fCW+I9LeGpE1hpD5yupD+rGTCPxf1bPfRS8PAoj8n6sATLIG8PnxMPVCdL8s\x3d\x22,\x22version\x22:0},\x22id\x22:\x2283fe5a42-e35f-49a3-8da5-6c1028808d24\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BKYG7ZiA1+cDAjR3ClNcncBeaMRVITNC9gZBLHvO74UPN6IrpDWBvi/YARjZLkpbVodVy2totNvEPYXVshn73V0\x3d\x22,\x22version\x22:0},\x22id\x22:\x227ae57b53-b7db-413f-8fd6-14e0457767bb\x22}]}'},cj=function(a){if(l.Promise){var b=void 0;return b}},hj=function(a,b,c,d,e){if(l.Promise)try{var f=Ui(a),g=dj(f,e).then(ej);return g}catch(p){}},jj=function(a){try{return ej(ij(Ui(a)))}catch(b){}},bj=function(a,b){var c=void 0;return c},ej=function(a){var b=a.Sc,c=a.time,d=["tv.1"],e=Pi(b);if(e)return d.push(e),{zb:encodeURIComponent(d.join("~")),pj:!1,eb:!1,time:c,oj:!0};var f=b.filter(function(n){return!Xi(n)}),g=Qi(f,function(n,p){var q=n.value,r=n.index;r!==void 0&&(p+=r);d.push(p+"."+q)}),h=g.pj,m=g.eb;return{zb:encodeURIComponent(d.join("~")),pj:h,eb:m,time:c,oj:!1}},Pi=function(a){if(a.length===1&&a[0].name==="error_code")return Wi.error_code+
"."+a[0].value},gj=function(a){if(a.length===1&&a[0].name==="error_code")return!1;for(var b=k(a),c=b.next();!c.done;c=b.next()){var d=c.value;if(Wi[d.name]&&d.value)return!0}return!1},Ui=function(a){function b(r,t,u,v){var w=kj(r);w!==""&&(Ji.test(w)?h.push({name:t,value:w,index:v}):h.push({name:t,value:u(w),index:v}))}function c(r,t){var u=r;if(gb(u)||Array.isArray(u)){u=ib(r);for(var v=0;v<u.length;++v){var w=kj(u[v]),x=Ji.test(w);t&&!x&&M(89);!t&&x&&M(88)}}}function d(r,t){var u=r[t];c(u,!1);var v=
lj[t];r[v]&&(r[t]&&M(90),u=r[v],c(u,!0));return u}function e(r,t,u){for(var v=ib(d(r,t)),w=0;w<v.length;++w)b(v[w],t,u)}function f(r,t,u,v){var w=d(r,t);b(w,t,u,v)}function g(r){return function(t){M(64);return r(t)}}var h=[];if(l.location.protocol!=="https:")return h.push({name:"error_code",value:"e3",index:void 0}),h;e(a,"email",mj);e(a,"phone_number",nj);e(a,"first_name",g(oj));e(a,"last_name",g(oj));var m=a.home_address||{};e(m,"street",g(pj));e(m,"city",g(pj));e(m,"postal_code",g(qj));e(m,"region",
g(pj));e(m,"country",g(qj));for(var n=ib(a.address||{}),p=0;p<n.length;p++){var q=n[p];f(q,"first_name",oj,p);f(q,"last_name",oj,p);f(q,"street",pj,p);f(q,"city",pj,p);f(q,"postal_code",qj,p);f(q,"region",pj,p);f(q,"country",qj,p)}return h},rj=function(a){var b=a?Ui(a):[];return ej({Sc:b})},sj=function(a){return a&&a!=null&&Object.keys(a).length>0&&l.Promise?Ui(a).some(function(b){return b.value&&Yi(b.name)&&!Ji.test(b.value)}):!1},kj=function(a){return a==null?"":gb(a)?sb(String(a)):"e0"},qj=function(a){return a.replace(tj,
"")},oj=function(a){return pj(a.replace(/\s/g,""))},pj=function(a){return sb(a.replace(uj,"").toLowerCase())},nj=function(a){a=a.replace(/[\s-()/.]/g,"");a.charAt(0)!=="+"&&(a="+"+a);return vj.test(a)?a:"e0"},mj=function(a){var b=a.toLowerCase().split("@");if(b.length===2){var c=b[0];/^(gmail|googlemail)\./.test(b[1])&&(c=c.replace(/\./g,""));c=c+"@"+b[1];if(wj.test(c))return c}return"e0"},ij=function(a){var b=Pc();try{a.forEach(function(e){if(e.value&&Yi(e.name)){var f;var g=e.value,h=l;if(g===""||
g==="e0"||Ji.test(g))f=g;else try{var m=new Hi;m.update(Ki(g));f=Mi(m.digest(),h)}catch(n){f="e2"}e.value=f}});var c={Sc:a};if(b!==void 0){var d=Pc();b&&d&&(c.time=Math.round(d)-Math.round(b))}return c}catch(e){return{Sc:[]}}},dj=function(a,b){if(!a.some(function(d){return d.value&&Yi(d.name)}))return Promise.resolve({Sc:a});if(!l.Promise)return Promise.resolve({Sc:[]});var c=b?Pc():void 0;return Promise.all(a.map(function(d){return d.value&&Yi(d.name)?Li(d.value).then(function(e){d.value=e}):Promise.resolve()})).then(function(){var d=
{Sc:a};if(c!==void 0){var e=Pc();c&&e!==void 0&&(d.time=Math.round(e)-Math.round(c))}return d}).catch(function(){return{Sc:[]}})},uj=/[0-9`~!@#$%^&*()_\-+=:;<>,.?|/\\[\]]/g,wj=/^\S+@\S+\.\S+$/,vj=/^\+\d{10,15}$/,tj=/[.~]/g,Zi=/^[0-9A-Za-z_-]{43}$/,xj={},Wi=(xj.email="em",xj.phone_number="pn",xj.first_name="fn",xj.last_name="ln",xj.street="sa",xj.city="ct",xj.region="rg",xj.country="co",xj.postal_code="pc",xj.error_code="ec",xj),yj={},lj=(yj.email="sha256_email_address",yj.phone_number="sha256_phone_number",
yj.first_name="sha256_first_name",yj.last_name="sha256_last_name",yj.street="sha256_street",yj);var $i=Object.freeze(["email","phone_number","first_name","last_name","street"]);var zj={},Aj=(zj[K.m.ob]=1,zj[K.m.jd]=2,zj[K.m.uc]=2,zj[K.m.ya]=3,zj[K.m.Pe]=4,zj[K.m.yg]=5,zj[K.m.Hc]=6,zj[K.m.jb]=6,zj[K.m.pb]=6,zj[K.m.Zc]=6,zj[K.m.Pb]=6,zj[K.m.wb]=6,zj[K.m.qb]=7,zj[K.m.Sb]=9,zj[K.m.zg]=10,zj[K.m.Lb]=11,zj),Bj={},Cj=(Bj.unknown=13,Bj.standard=14,Bj.unique=15,Bj.per_session=16,Bj.transactions=17,Bj.items_sold=18,Bj);var ab=[];function Dj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=k(Object.keys(Aj)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=Aj[f],h=b;h=h===void 0?!1:h;Za("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(ab[g]=!0)}}};var Ej=function(){this.D=new Set;this.J=new Set},Gj=function(a){var b=Fj.ma;a=a===void 0?[]:a;var c=[].concat(ua(b.D)).concat([].concat(ua(b.J))).concat(a);c.sort(function(d,e){return d-e});return c},Hj=function(){var a=[].concat(ua(Fj.ma.D));a.sort(function(b,c){return b-c});return a},Ij=function(){var a=Fj.ma,b=Oi.wq;a.D=new Set;if(b!=="")for(var c=k(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.D.add(e)}};var Jj={Mi:"5641"};Jj.Li=Number("1")||0;Jj.Kb="gtagdataLayer";Jj.zq="ChEI8OmUwgYQ2cis1cGm2fzvARIjAIcVXmI42THgpJp0vMN6gtu2XuCGjkMTXMUASUwvKQ/sG4IaAiBB";var Kj={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},Lj={__paused:1,__tg:1},Mj;for(Mj in Kj)Kj.hasOwnProperty(Mj)&&(Lj[Mj]=1);var Nj=qb("true"),Oj=!1,Pj,Qj=!1;Qj=!0;Pj=Qj;var Rj,Sj=!1;Rj=Sj;Jj.xg="www.googletagmanager.com";var Tj=""+Jj.xg+(Pj?"/gtag/js":"/gtm.js"),Uj=null,Vj=null,Wj={},Xj={};Jj.Pm="";var Yj="";Jj.Pi=Yj;var Fj=new function(){this.ma=new Ej;this.D=this.J=!1;this.O=0;this.Ca=this.Za=this.Fb=this.T="";this.fa=this.R=!1};function Zj(){var a;a=a===void 0?[]:a;return Gj(a).join("~")}
function ak(){var a=Fj.T.length;return Fj.T[a-1]==="/"?Fj.T.substring(0,a-1):Fj.T}function bk(){return Fj.D?D(84)?Fj.O===0:Fj.O!==1:!1}function ck(a){for(var b={},c=k(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var dk=new mb,ek={},fk={},ik={name:Jj.Kb,set:function(a,b){cd(Bb(a,b),ek);gk()},get:function(a){return hk(a,2)},reset:function(){dk=new mb;ek={};gk()}};function hk(a,b){return b!=2?dk.get(a):jk(a)}function jk(a,b){var c=a.split(".");b=b||[];for(var d=ek,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function kk(a,b){fk.hasOwnProperty(a)||(dk.set(a,b),cd(Bb(a,b),ek),gk())}
function lk(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=hk(c,1);if(Array.isArray(d)||bd(d))d=cd(d,null);fk[c]=d}}function gk(a){nb(fk,function(b,c){dk.set(b,c);cd(Bb(b),ek);cd(Bb(b,c),ek);a&&delete fk[b]})}function mk(a,b){var c,d=(b===void 0?2:b)!==1?jk(a):dk.get(a);$c(d)==="array"||$c(d)==="object"?c=cd(d,null):c=d;return c};
var ok=function(a){for(var b=[],c=Object.keys(nk),d=0;d<c.length;d++){var e=c[d],f=nk[e],g=void 0,h=(g=a[e])!=null?g:"0";b.push(f+"-"+h)}return b.join("~")},pk=function(a,b){return a||b?a&&!b?"1":!a&&b?"2":"3":"0"},qk=function(a,b,c,d){if(!c)return!1;for(var e=String(c.value),f,g=e.replace(/\["?'?/g,".").replace(/"?'?\]/g,"").split(",").map(function(w){return w.trim()}).filter(function(w){return w&&!zb(w,"#")&&!zb(w,".")}),h=0;h<g.length;h++){var m=g[h];if(zb(m,"dataLayer."))f=hk(m.substring(10));
else{var n=m.split(".");f=l[n.shift()];for(var p=0;p<n.length;p++)f=f&&f[n[p]]}if(f!==void 0)break}if(f===void 0&&ui)try{var q=ti(e);if(q&&q.length>0){f=[];for(var r=0;r<q.length&&r<(b==="email"||b==="phone_number"?5:1);r++)f.push(Fc(q[r])||sb(q[r].value));f=f.length===1?f[0]:f}}catch(w){M(149)}if(D(60)){for(var t,u=0;u<g.length&&(t=hk(g[u]),t===void 0);u++);var v=f!==void 0;d[b]=pk(t!==void 0,v);v||(f=t)}return f?(a[b]=f,!0):!1},rk=function(a,b){b=b===void 0?{}:b;if(a){var c={},d=!1;d=qk(c,"email",
a.email,b)||d;d=qk(c,"phone_number",a.phone,b)||d;c.address=[];for(var e=a.name_and_address||[],f=0;f<e.length;f++){var g={};d=qk(g,"first_name",e[f].first_name,b)||d;d=qk(g,"last_name",e[f].last_name,b)||d;d=qk(g,"street",e[f].street,b)||d;d=qk(g,"city",e[f].city,b)||d;d=qk(g,"region",e[f].region,b)||d;d=qk(g,"country",e[f].country,b)||d;d=qk(g,"postal_code",e[f].postal_code,b)||d;c.address.push(g)}return d?c:void 0}},sk=function(a,b){switch(a.enhanced_conversions_mode){case "manual":if(b&&bd(b))return b;
var c=a.enhanced_conversions_manual_var;if(c!==void 0)return c;var d=l.enhanced_conversion_data;d&&Za("GTAG_EVENT_FEATURE_CHANNEL",8);return d;case "automatic":return D(184)&&b&&bd(b)?b:rk(a[K.m.xk])}},tk=function(a){return bd(a)?!!a.enable_code:!1},nk={email:"1",phone_number:"2",first_name:"3",last_name:"4",country:"5",postal_code:"6",street:"7",city:"8",region:"9"};var xk=/:[0-9]+$/,yk=/^\d+\.fls\.doubleclick\.net$/;function zk(a,b,c,d){for(var e=[],f=k(a.split("&")),g=f.next();!g.done;g=f.next()){var h=k(g.value.split("=")),m=h.next().value,n=ta(h);if(decodeURIComponent(m.replace(/\+/g," "))===b){var p=n.join("=");if(!c)return d?p:decodeURIComponent(p.replace(/\+/g," "));e.push(d?p:decodeURIComponent(p.replace(/\+/g," ")))}}return c?e:void 0}function Ak(a){try{return decodeURIComponent(a)}catch(b){}}
function Bk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=Ck(a.protocol)||Ck(l.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:l.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||l.location.hostname).replace(xk,"").toLowerCase());return Dk(a,b,c,d,e)}
function Dk(a,b,c,d,e){var f,g=Ck(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=Ek(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(xk,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||Za("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=zk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function Ck(a){return a?a.replace(":","").toLowerCase():""}function Ek(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var Fk={},Gk=0;
function Hk(a){var b=Fk[a];if(!b){var c=y.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||Za("TAGGING",1),d="/"+d);var e=c.hostname.replace(xk,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};Gk<5&&(Fk[a]=b,Gk++)}return b}function Ik(a,b,c){var d=Hk(a);return Gb(b,d,c)}
function Jk(a){var b=Hk(l.location.href),c=Bk(b,"host",!1);if(c&&c.match(yk)){var d=Bk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var Kk={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},Lk=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function Mk(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return Hk(""+c+b).href}}function Nk(a,b){if(bk()||Fj.J)return Mk(a,b)}
function Ok(){return!!Jj.Pi&&Jj.Pi.split("@@").join("")!=="SGTM_TOKEN"}function Pk(a){for(var b=k([K.m.jd,K.m.uc]),c=b.next();!c.done;c=b.next()){var d=O(a,c.value);if(d)return d}}function Qk(a,b,c){c=c===void 0?"":c;if(!bk())return a;var d=b?Kk[a]||"":"";d==="/gs"&&(c="");return""+ak()+d+c}function Rk(a,b){return D(173)?a:Qk(a,b,"")}function Sk(a){if(!bk())return a;for(var b=k(Lk),c=b.next();!c.done;c=b.next())if(zb(a,""+ak()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function Tk(a){var b=String(a[We.Ha]||"").replace(/_/g,"");return zb(b,"cvt")?"cvt":b}var Uk=l.location.search.indexOf("?gtm_latency=")>=0||l.location.search.indexOf("&gtm_latency=")>=0;var Vk={Yp:"0.005000",Lm:"",uq:"0.01",Jo:"0.010000"};function Wk(){var a=Vk.Yp;return Number(a)}
var Xk=Math.random(),Yk=Uk||Xk<Wk(),Zk,$k=Wk()===1||(oc==null?void 0:oc.includes("gtm_debug=d"))||Uk;Zk=D(163)?Uk||Xk>=1-Number(Vk.Jo):$k||Xk>=1-Number(Vk.uq);var al=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},bl=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var dl,el;a:{for(var fl=["CLOSURE_FLAGS"],gl=za,hl=0;hl<fl.length;hl++)if(gl=gl[fl[hl]],gl==null){el=null;break a}el=gl}var il=el&&el[610401301];dl=il!=null?il:!1;function jl(){var a=za.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var kl,ll=za.navigator;kl=ll?ll.userAgentData||null:null;function ml(a){if(!dl||!kl)return!1;for(var b=0;b<kl.brands.length;b++){var c=kl.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function nl(a){return jl().indexOf(a)!=-1};function ol(){return dl?!!kl&&kl.brands.length>0:!1}function pl(){return ol()?!1:nl("Opera")}function ql(){return nl("Firefox")||nl("FxiOS")}function rl(){return ol()?ml("Chromium"):(nl("Chrome")||nl("CriOS"))&&!(ol()?0:nl("Edge"))||nl("Silk")};var sl=function(a){sl[" "](a);return a};sl[" "]=function(){};var tl=function(a){return decodeURIComponent(a.replace(/\+/g," "))};function ul(){return dl?!!kl&&!!kl.platform:!1}function vl(){return nl("iPhone")&&!nl("iPod")&&!nl("iPad")}function wl(){vl()||nl("iPad")||nl("iPod")};pl();ol()||nl("Trident")||nl("MSIE");nl("Edge");!nl("Gecko")||jl().toLowerCase().indexOf("webkit")!=-1&&!nl("Edge")||nl("Trident")||nl("MSIE")||nl("Edge");jl().toLowerCase().indexOf("webkit")!=-1&&!nl("Edge")&&nl("Mobile");ul()||nl("Macintosh");ul()||nl("Windows");(ul()?kl.platform==="Linux":nl("Linux"))||ul()||nl("CrOS");ul()||nl("Android");vl();nl("iPad");nl("iPod");wl();jl().toLowerCase().indexOf("kaios");var xl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{sl(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},yl=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},zl=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},Al=function(a){if(l.top==l)return 0;if(a===void 0?0:a){var b=l.location.ancestorOrigins;
if(b)return b[b.length-1]==l.location.origin?1:2}return xl(l.top)?1:2},Bl=function(a){a=a===void 0?document:a;return a.createElement("img")},Cl=function(){for(var a=l,b=a;a&&a!=a.parent;)a=a.parent,xl(a)&&(b=a);return b};function Dl(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function El(){return Dl("join-ad-interest-group")&&eb(lc.joinAdInterestGroup)}
function Fl(a,b,c){var d=mg[3]===void 0?1:mg[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=y.querySelector(e);g&&(f=[g])}else f=Array.from(y.querySelectorAll(e))}catch(r){}var h;a:{try{h=y.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(mg[2]===void 0?50:mg[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&ub()-q<(mg[1]===void 0?6E4:mg[1])?(Za("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)Gl(f[0]);else{if(n)return Za("TAGGING",10),!1}else f.length>=d?Gl(f[0]):n&&Gl(m[0]);zc(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:ub()});return!0}function Gl(a){try{a.parentNode.removeChild(a)}catch(b){}};function Hl(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var Il=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};ql();vl()||nl("iPod");nl("iPad");!nl("Android")||rl()||ql()||pl()||nl("Silk");rl();!nl("Safari")||rl()||(ol()?0:nl("Coast"))||pl()||(ol()?0:nl("Edge"))||(ol()?ml("Microsoft Edge"):nl("Edg/"))||(ol()?ml("Opera"):nl("OPR"))||ql()||nl("Silk")||nl("Android")||wl();var Jl={},Kl=null,Ll=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Kl){Kl={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));Jl[m]=n;for(var p=0;p<n.length;p++){var q=n[p];Kl[q]===void 0&&(Kl[q]=p)}}}for(var r=Jl[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var x=b[v],
z=b[v+1],C=b[v+2],E=r[x>>2],F=r[(x&3)<<4|z>>4],G=r[(z&15)<<2|C>>6],J=r[C&63];t[w++]=""+E+F+G+J}var N=0,X=u;switch(b.length-v){case 2:N=b[v+1],X=r[(N&15)<<2]||u;case 1:var Q=b[v];t[w]=""+r[Q>>2]+r[(Q&3)<<4|N>>4]+X+u}return t.join("")};var Ml=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Nl=/#|$/,Ol=function(a,b){var c=a.search(Nl),d=Ml(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return tl(a.slice(d,e!==-1?e:0))},Pl=/[?&]($|#)/,Ql=function(a,b,c){for(var d,e=a.search(Nl),f=0,g,h=[];(g=Ml(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(Pl,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function Rl(a,b,c,d,e,f){var g=Ol(c,"fmt");if(d){var h=Ol(c,"random"),m=Ol(c,"label")||"";if(!h)return!1;var n=Ll(tl(m)+":"+tl(h));if(!Hl(a,n,d))return!1}g&&Number(g)!==4&&(c=Ql(c,"rfmt",g));var p=Ql(c,"fmt",4);xc(p,function(){a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},e,f,b.getElementsByTagName("script")[0].parentElement||void 0);return!0};var Sl={},Tl=(Sl[1]={},Sl[2]={},Sl[3]={},Sl[4]={},Sl);function Ul(a,b,c){var d=Vl(b,c);if(d){var e=Tl[b][d];e||(e=Tl[b][d]=[]);e.push(Object.assign({},a))}}function Wl(a,b){var c=Vl(a,b);if(c){var d=Tl[a][c];d&&(Tl[a][c]=d.filter(function(e){return!e.ym}))}}function Xl(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function Vl(a,b){var c=b;if(b[0]==="/"){var d;c=((d=l.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function Yl(a){var b=ya.apply(1,arguments);Zk&&(Ul(a,2,b[0]),Ul(a,3,b[0]));Ic.apply(null,ua(b))}function Zl(a){var b=ya.apply(1,arguments);Zk&&Ul(a,2,b[0]);return Jc.apply(null,ua(b))}function $l(a){var b=ya.apply(1,arguments);Zk&&Ul(a,3,b[0]);Ac.apply(null,ua(b))}
function am(a){var b=ya.apply(1,arguments),c=b[0];Zk&&(Ul(a,2,c),Ul(a,3,c));return Lc.apply(null,ua(b))}function bm(a){var b=ya.apply(1,arguments);Zk&&Ul(a,1,b[0]);xc.apply(null,ua(b))}function cm(a){var b=ya.apply(1,arguments);b[0]&&Zk&&Ul(a,4,b[0]);zc.apply(null,ua(b))}function dm(a){var b=ya.apply(1,arguments);Zk&&Ul(a,1,b[2]);return Rl.apply(null,ua(b))}function em(a){var b=ya.apply(1,arguments);Zk&&Ul(a,4,b[0]);Fl.apply(null,ua(b))};var fm=/gtag[.\/]js/,gm=/gtm[.\/]js/,hm=!1;function im(a){if(hm)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(fm.test(c))return"3";if(gm.test(c))return"2"}return"0"};function jm(a,b){var c=km();c.pending||(c.pending=[]);jb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function lm(){var a=l.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=k(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var mm=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.siloed=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=lm()};
function km(){var a=pc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new mm,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.siloed||(c.siloed=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=lm());return c};var nm={},om=!1,pm=void 0,$f={ctid:"DC-9857227",canonicalContainerId:"183378342",rm:"DC-9857227|GT-WVJD6BWS",sm:"DC-9857227"};nm.Af=qb("");function qm(){return nm.Af&&rm().some(function(a){return a===$f.ctid})}function sm(){var a=tm();return om?a.map(um):a}function vm(){var a=rm();return om?a.map(um):a}
function wm(){var a=vm();if(!om)for(var b=k([].concat(ua(a))),c=b.next();!c.done;c=b.next()){var d=um(c.value),e=km().destination[d];e&&e.state!==0||a.push(d)}return a}function xm(){return ym($f.ctid)}function zm(){return ym($f.canonicalContainerId||"_"+$f.ctid)}function tm(){return $f.rm?$f.rm.split("|"):[$f.ctid]}function rm(){return $f.sm?$f.sm.split("|").filter(function(a){return D(108)?a.indexOf("GTM-")!==0:!0}):[]}function Am(){var a=Bm(Cm()),b=a&&a.parent;if(b)return Bm(b)}
function Bm(a){var b=km();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}function ym(a){return om?um(a):a}function um(a){return"siloed_"+a}function Dm(a){a=String(a);return zb(a,"siloed_")?a.substring(7):a}function Em(){if(Fj.R){var a=km();if(a.siloed){for(var b=[],c=tm().map(um),d=rm().map(um),e={},f=0;f<a.siloed.length;e={uh:void 0},f++)e.uh=a.siloed[f],!om&&jb(e.uh.isDestination?d:c,function(g){return function(h){return h===g.uh.ctid}}(e))?om=!0:b.push(e.uh);a.siloed=b}}}
function Fm(){var a=km();if(a.pending){for(var b,c=[],d=!1,e=sm(),f=pm?pm:wm(),g={},h=0;h<a.pending.length;g={hg:void 0},h++)g.hg=a.pending[h],jb(g.hg.target.isDestination?f:e,function(m){return function(n){return n===m.hg.target.ctid}}(g))?d||(b=g.hg.onLoad,d=!0):c.push(g.hg);a.pending=c;if(b)try{b(zm())}catch(m){}}}
function Gm(){var a=$f.ctid,b=sm(),c=wm();pm=c;for(var d=function(n,p){var q={canonicalContainerId:$f.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};nc&&(q.scriptElement=nc);oc&&(q.scriptSource=oc);if(Am()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var w=Fj.D,x=Hk(v),z=w?x.pathname:""+x.hostname+x.pathname,C=y.scripts,E="",F=0;F<C.length;++F){var G=C[F];if(!(G.innerHTML.length===
0||!w&&G.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||G.innerHTML.indexOf(z)<0)){if(G.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(F);break b}E=String(F)}}if(E){t=E;break b}}t=void 0}var J=t;if(J){hm=!0;r=J;break a}}var N=[].slice.call(y.scripts);r=q.scriptElement?String(N.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=im(q)}var X=p?e.destination:e.container,Q=X[n];Q?(p&&Q.state===0&&M(93),Object.assign(Q,q)):X[n]=q},e=km(),f=k(b),g=f.next();!g.done;g=
f.next())d(g.value,!1);for(var h=k(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[zm()]={};Fm()}function Hm(){var a=zm();return!!km().canonical[a]}function Im(a){return!!km().container[a]}function Jm(a){var b=km().destination[a];return!!b&&!!b.state}function Cm(){return{ctid:xm(),isDestination:nm.Af}}function Km(a,b,c){b.siloed&&Lm({ctid:a,isDestination:!1});var d=Cm();km().container[a]={state:1,context:b,parent:d};jm({ctid:a,isDestination:!1},c)}
function Lm(a){var b=km();(b.siloed=b.siloed||[]).push(a)}function Mm(){var a=km().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}function Nm(){var a={};nb(km().destination,function(b,c){c.state===0&&(a[Dm(b)]=c)});return a}function Om(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Pm(){for(var a=km(),b=k(sm()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1}
function Qm(a){var b=km();return b.destination[a]?1:b.destination[um(a)]?2:0};var Rm={Ka:{ae:0,ee:1,Ji:2}};Rm.Ka[Rm.Ka.ae]="FULL_TRANSMISSION";Rm.Ka[Rm.Ka.ee]="LIMITED_TRANSMISSION";Rm.Ka[Rm.Ka.Ji]="NO_TRANSMISSION";var Sm={Z:{Eb:0,Ea:1,Fc:2,Oc:3}};Sm.Z[Sm.Z.Eb]="NO_QUEUE";Sm.Z[Sm.Z.Ea]="ADS";Sm.Z[Sm.Z.Fc]="ANALYTICS";Sm.Z[Sm.Z.Oc]="MONITORING";function Tm(){var a=pc("google_tag_data",{});return a.ics=a.ics||new Um}var Um=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.D=[]};
Um.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;Za("TAGGING",19);b==null?Za("TAGGING",18):Vm(this,a,b==="granted",c,d,e,f,g)};Um.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)Vm(this,a[d],void 0,void 0,"","",b,c)};
var Vm=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&gb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&l.setTimeout(function(){m[b]===t&&t.quiet&&(Za("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};ba=Um.prototype;ba.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=k(d),n=m.next();!n.done;n=m.next())Wm(this,n.value)}else if(b!==void 0&&h!==b)for(var p=k(d),q=p.next();!q.done;q=p.next())Wm(this,q.value)};
ba.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
ba.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&gb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
ba.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
ba.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};ba.addListener=function(a,b){this.D.push({consentTypes:a,oe:b})};var Wm=function(a,b){for(var c=0;c<a.D.length;++c){var d=a.D[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.tm=!0)}};Um.prototype.notifyListeners=function(a,b){for(var c=0;c<this.D.length;++c){var d=this.D[c];if(d.tm){d.tm=!1;try{d.oe({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var Xm=!1,Ym=!1,Zm={},$m={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(Zm.ad_storage=1,Zm.analytics_storage=1,Zm.ad_user_data=1,Zm.ad_personalization=1,Zm),usedContainerScopedDefaults:!1};function an(a){var b=Tm();b.accessedAny=!0;return(gb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,$m)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function bn(a){var b=Tm();b.accessedAny=!0;return b.getConsentState(a,$m)}function cn(a){var b=Tm();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function dn(){if(!ng(8))return!1;var a=Tm();a.accessedAny=!0;if(a.active)return!0;if(!$m.usedContainerScopedDefaults)return!1;for(var b=k(Object.keys($m.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if($m.containerScopedDefaults[c.value]!==1)return!0;return!1}function en(a,b){Tm().addListener(a,b)}
function fn(a,b){Tm().notifyListeners(a,b)}function gn(a,b){function c(){for(var e=0;e<b.length;e++)if(!cn(b[e]))return!0;return!1}if(c()){var d=!1;en(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function hn(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];an(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=gb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),en(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):l.setTimeout(function(){m(c())},500)}}))};var jn={},kn=(jn[Sm.Z.Eb]=Rm.Ka.ae,jn[Sm.Z.Ea]=Rm.Ka.ae,jn[Sm.Z.Fc]=Rm.Ka.ae,jn[Sm.Z.Oc]=Rm.Ka.ae,jn),ln=function(a,b){this.D=a;this.consentTypes=b};ln.prototype.isConsentGranted=function(){switch(this.D){case 0:return this.consentTypes.every(function(a){return an(a)});case 1:return this.consentTypes.some(function(a){return an(a)});default:cc(this.D,"consentsRequired had an unknown type")}};
var mn={},nn=(mn[Sm.Z.Eb]=new ln(0,[]),mn[Sm.Z.Ea]=new ln(0,["ad_storage"]),mn[Sm.Z.Fc]=new ln(0,["analytics_storage"]),mn[Sm.Z.Oc]=new ln(1,["ad_storage","analytics_storage"]),mn);var pn=function(a){var b=this;this.type=a;this.D=[];en(nn[a].consentTypes,function(){on(b)||b.flush()})};pn.prototype.flush=function(){for(var a=k(this.D),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.D=[]};var on=function(a){return kn[a.type]===Rm.Ka.Ji&&!nn[a.type].isConsentGranted()},qn=function(a,b){on(a)?a.D.push(b):b()},rn=new Map;function sn(a){rn.has(a)||rn.set(a,new pn(a));return rn.get(a)};var tn="/td?id="+$f.ctid,un="v t pid dl tdp exp".split(" "),vn=["mcc"],wn={},xn={},yn=!1,zn=void 0;function An(a,b,c){xn[a]=b;(c===void 0||c)&&Bn(a)}function Bn(a,b){wn[a]!==void 0&&(b===void 0||!b)||D(166)&&zb($f.ctid,"GTM-")&&a==="mcc"||(wn[a]=!0)}
function Cn(a){a=a===void 0?!1:a;var b=Object.keys(wn).filter(function(c){return wn[c]===!0&&xn[c]!==void 0&&(a||!vn.includes(c))}).map(function(c){var d=xn[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+Qk("https://www.googletagmanager.com")+tn+(""+b+"&z=0")}function Dn(){Object.keys(wn).forEach(function(a){un.indexOf(a)<0&&(wn[a]=!1)})}
function En(a){a=a===void 0?!1:a;if(Fj.fa&&Zk&&$f.ctid){var b=sn(Sm.Z.Oc);if(on(b))yn||(yn=!0,qn(b,En));else{var c=Cn(a),d={destinationId:$f.ctid,endpoint:56};a?am(d,c,void 0,{Gh:!0},void 0,function(){$l(d,c+"&img=1")}):$l(d,c);Dn();yn=!1}}}var Fn={};function Gn(a){var b=String(a);Fn.hasOwnProperty(b)||(Fn[b]=!0,An("csp",Object.keys(Fn).join("~")),Bn("csp",!0),zn===void 0&&D(171)&&(zn=l.setTimeout(function(){var c=wn.csp;wn.csp=!0;var d=Cn(!1);wn.csp=c;xc(d+"&script=1");zn=void 0},500)))}
function Hn(){Object.keys(wn).filter(function(a){return wn[a]&&!un.includes(a)}).length>0&&En(!0)}var In=kb();function Jn(){In=kb()}function Kn(){An("v","3");An("t","t");An("pid",function(){return String(In)});An("exp",Zj());Cc(l,"pagehide",Hn);l.setInterval(Jn,864E5)};var Ln=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],Mn=[K.m.jd,K.m.uc,K.m.Vd,K.m.Nb,K.m.sc,K.m.Sa,K.m.Ra,K.m.jb,K.m.pb,K.m.Pb],Nn=!1,On=!1,Pn={},Qn={};function Rn(){!On&&Nn&&(Ln.some(function(a){return $m.containerScopedDefaults[a]!==1})||Sn("mbc"));On=!0}function Sn(a){Zk&&(An(a,"1"),En())}function Tn(a,b){if(!Pn[b]&&(Pn[b]=!0,Qn[b]))for(var c=k(Mn),d=c.next();!d.done;d=c.next())if(a.hasOwnProperty(d.value)){Sn("erc");break}}
function Un(a,b){if(!Pn[b]&&(Pn[b]=!0,Qn[b]))for(var c=k(Mn),d=c.next();!d.done;d=c.next())if(O(a,d.value)){Sn("erc");break}};function Vn(a){Za("HEALTH",a)};var Wn={Hl:"service_worker_endpoint",Qi:"shared_user_id",Ri:"shared_user_id_requested",Gf:"shared_user_id_source",sg:"cookie_deprecation_label",Mm:"aw_user_data_cache",Rn:"ga4_user_data_cache",Pn:"fl_user_data_cache",Al:"pt_listener_set",Ef:"pt_data",yl:"nb_data",Di:"ip_geo_fetch_in_progress",uf:"ip_geo_data_cache"},Xn;function Yn(a){if(!Xn){Xn={};for(var b=k(Object.keys(Wn)),c=b.next();!c.done;c=b.next())Xn[Wn[c.value]]=!0}return!!Xn[a]}
function Zn(a,b){b=b===void 0?!1:b;if(Yn(a)){var c,d,e=(d=(c=pc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=k(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function $n(a,b){var c=Zn(a,!0);c&&c.set(b)}function ao(a){var b;return(b=Zn(a))==null?void 0:b.get()}function bo(a,b){if(typeof b==="function"){var c;return(c=Zn(a,!0))==null?void 0:c.subscribe(b)}}function co(a,b){var c=Zn(a);return c?c.unsubscribe(b):!1};var eo={ap:"eyIwIjoiQ04iLCIxIjoiIiwiMiI6dHJ1ZSwiMyI6Imdvb2dsZS5jbiIsIjQiOiIiLCI1Ijp0cnVlLCI2IjpmYWxzZSwiNyI6ImFkX3N0b3JhZ2V8YW5hbHl0aWNzX3N0b3JhZ2V8YWRfdXNlcl9kYXRhfGFkX3BlcnNvbmFsaXphdGlvbiJ9"},fo={},go=!1;function ho(){function a(){c!==void 0&&co(Wn.uf,c);try{var e=ao(Wn.uf);fo=JSON.parse(e)}catch(f){M(123),Vn(2),fo={}}go=!0;b()}var b=io,c=void 0,d=ao(Wn.uf);d?a(d):(c=bo(Wn.uf,a),jo())}
function jo(){function a(c){$n(Wn.uf,c||"{}");$n(Wn.Di,!1)}if(!ao(Wn.Di)){$n(Wn.Di,!0);var b="";try{l.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function ko(){var a=eo.ap;try{return JSON.parse(Va(a))}catch(b){return M(123),Vn(2),{}}}function lo(){return fo["0"]||""}function mo(){return fo["1"]||""}function no(){var a=!1;return a}function oo(){return fo["6"]!==!1}function po(){var a="";return a}
function qo(){var a=!1;a=!!fo["5"];return a}function ro(){var a="";return a};function so(a){return typeof a!=="object"||a===null?{}:a}function to(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function uo(a){if(a!==void 0&&a!==null)return to(a)}function vo(a){return typeof a==="number"?a:uo(a)};function wo(a){return a&&a.indexOf("pending:")===0?xo(a.substr(8)):!1}function xo(a){if(a==null||a.length===0)return!1;var b=Number(a),c=ub();return b<c+3E5&&b>c-9E5};var yo=!1,zo=!1,Ao=!1,Bo=0,Co=!1,Do=[];function Eo(a){if(Bo===0)Co&&Do&&(Do.length>=100&&Do.shift(),Do.push(a));else if(Fo()){var b=pc('google.tagmanager.ta.prodqueue',[]);b.length>=50&&b.shift();b.push(a)}}function Go(){Ho();Dc(y,"TAProdDebugSignal",Go)}function Ho(){if(!zo){zo=!0;Io();var a=Do;Do=void 0;a==null||a.forEach(function(b){Eo(b)})}}
function Io(){var a=y.documentElement.getAttribute("data-tag-assistant-prod-present");xo(a)?Bo=1:!wo(a)||yo||Ao?Bo=2:(Ao=!0,Cc(y,"TAProdDebugSignal",Go,!1),l.setTimeout(function(){Ho();yo=!0},200))}function Fo(){if(!Co)return!1;switch(Bo){case 1:case 0:return!0;case 2:return!1;default:return!1}};var Jo=!1;function Ko(a,b){var c=tm(),d=rm();if(Fo()){var e=Lo("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;Eo(e)}}
function Mo(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.cb;e=a.isBatched;var f;if(f=Fo()){var g;a:switch(c.endpoint){case 19:case 47:g=!0;break a;default:g=!1}f=!g}if(f){var h=Lo("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);Eo(h)}}function No(a){Fo()&&Mo(a())}
function Lo(a,b){b=b===void 0?{}:b;b.groupId=Oo;var c,d=b,e={publicId:Po};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'3',messageType:a};c.containerProduct=Jo?"OGT":"GTM";c.key.targetRef=Qo;return c}var Po="",Qo={ctid:"",isDestination:!1},Oo;
function Ro(a){var b=$f.ctid,c=qm();Bo=0;Co=!0;Io();Oo=a;Po=b;Jo=Pj;Qo={ctid:b,isDestination:c}};var So=[K.m.V,K.m.ia,K.m.W,K.m.Na],To,Uo;function Vo(a){var b=a[K.m.bc];b||(b=[""]);for(var c={Vf:0};c.Vf<b.length;c={Vf:c.Vf},++c.Vf)nb(a,function(d){return function(e,f){if(e!==K.m.bc){var g=to(f),h=b[d.Vf],m=lo(),n=mo();Ym=!0;Xm&&Za("TAGGING",20);Tm().declare(e,g,h,m,n)}}}(c))}
function Wo(a){Rn();!Uo&&To&&Sn("crc");Uo=!0;var b=a[K.m.qg];b&&M(41);var c=a[K.m.bc];c?M(40):c=[""];for(var d={Wf:0};d.Wf<c.length;d={Wf:d.Wf},++d.Wf)nb(a,function(e){return function(f,g){if(f!==K.m.bc&&f!==K.m.qg){var h=uo(g),m=c[e.Wf],n=Number(b),p=lo(),q=mo();n=n===void 0?0:n;Xm=!0;Ym&&Za("TAGGING",20);Tm().default(f,h,m,p,q,n,$m)}}}(d))}
function Xo(a){$m.usedContainerScopedDefaults=!0;var b=a[K.m.bc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(mo())&&!c.includes(lo()))return}nb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}$m.usedContainerScopedDefaults=!0;$m.containerScopedDefaults[d]=e==="granted"?3:2})}
function Yo(a,b){Rn();To=!0;nb(a,function(c,d){var e=to(d);Xm=!0;Ym&&Za("TAGGING",20);Tm().update(c,e,$m)});fn(b.eventId,b.priorityId)}function Zo(a){a.hasOwnProperty("all")&&($m.selectedAllCorePlatformServices=!0,nb(gi,function(b){$m.corePlatformServices[b]=a.all==="granted";$m.usedCorePlatformServices=!0}));nb(a,function(b,c){b!=="all"&&($m.corePlatformServices[b]=c==="granted",$m.usedCorePlatformServices=!0)})}function $o(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return an(b)})}
function ap(a,b){en(a,b)}function bp(a,b){hn(a,b)}function cp(a,b){gn(a,b)}function dp(){var a=[K.m.V,K.m.Na,K.m.W];Tm().waitForUpdate(a,500,$m)}function ep(a){for(var b=k(a),c=b.next();!c.done;c=b.next()){var d=c.value;Tm().clearTimeout(d,void 0,$m)}fn()}function fp(){if(!Rj)for(var a=oo()?ck(Fj.Za):ck(Fj.Fb),b=0;b<So.length;b++){var c=So[b],d=c,e=a[c]?"granted":"denied";Tm().implicit(d,e)}};var gp=!1,hp=[];function ip(){if(!gp){gp=!0;for(var a=hp.length-1;a>=0;a--)hp[a]();hp=[]}};var jp=l.google_tag_manager=l.google_tag_manager||{};function kp(a,b){return jp[a]=jp[a]||b()}function lp(){var a=xm(),b=mp;jp[a]=jp[a]||b}function np(){var a=Jj.Kb;return jp[a]=jp[a]||{}}function op(){var a=jp.sequence||1;jp.sequence=a+1;return a};function pp(){if(jp.pscdl!==void 0)ao(Wn.sg)===void 0&&$n(Wn.sg,jp.pscdl);else{var a=function(c){jp.pscdl=c;$n(Wn.sg,c)},b=function(){a("error")};try{lc.cookieDeprecationLabel?(a("pending"),lc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var qp=0;function rp(a){Zk&&a===void 0&&qp===0&&(An("mcc","1"),qp=1)};function sp(a,b){b&&nb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};var tp=/^(?:siloed_)?(?:AW|DC|G|GF|GT|HA|MC|UA)$/,up=/\s/;
function vp(a,b){if(gb(a)){a=sb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(tp.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||up.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function wp(a,b){for(var c={},d=0;d<a.length;++d){var e=vp(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[xp[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=k(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var zp={},xp=(zp[0]=0,zp[1]=1,zp[2]=2,zp[3]=0,zp[4]=1,zp[5]=0,zp[6]=0,zp[7]=0,zp);var Ap=Number('')||500,Bp={},Cp={},Dp={initialized:11,complete:12,interactive:13},Ep={},Fp=Object.freeze((Ep[K.m.rb]=!0,Ep)),Gp=void 0;function Hp(a,b){if(b.length&&Zk){var c;(c=Bp)[a]!=null||(c[a]=[]);Cp[a]!=null||(Cp[a]=[]);var d=b.filter(function(e){return!Cp[a].includes(e)});Bp[a].push.apply(Bp[a],ua(d));Cp[a].push.apply(Cp[a],ua(d));!Gp&&d.length>0&&(Bn("tdc",!0),Gp=l.setTimeout(function(){En();Bp={};Gp=void 0},Ap))}}
function Ip(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function Jp(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;$c(t)==="object"?u=t[r]:$c(t)==="array"&&(u=t[r]);return u===void 0?Fp[r]:u},f=Ip(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=$c(m)==="object"||$c(m)==="array",q=$c(n)==="object"||$c(n)==="array";if(p&&q)Jp(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function Kp(){An("tdc",function(){Gp&&(l.clearTimeout(Gp),Gp=void 0);var a=[],b;for(b in Bp)Bp.hasOwnProperty(b)&&a.push(b+"*"+Bp[b].join("."));return a.length?a.join("!"):void 0},!1)};var Lp=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.D=c;this.T=d;this.O=e;this.R=f;this.J=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},Mp=function(a,b){var c=[];switch(b){case 3:c.push(a.D);c.push(a.T);c.push(a.O);c.push(a.R);c.push(a.J);break;case 2:c.push(a.D);break;case 1:c.push(a.T);c.push(a.O);c.push(a.R);c.push(a.J);break;case 4:c.push(a.D),c.push(a.T),c.push(a.O),c.push(a.R)}return c},O=function(a,b,c,d){for(var e=k(Mp(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},Np=function(a){for(var b={},c=Mp(a,4),d=k(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=k(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
Lp.prototype.getMergedValues=function(a,b,c){function d(n){bd(n)&&nb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=Mp(this,b);g.reverse();for(var h=k(g),m=h.next();!m.done;m=h.next())d(m.value[a]);return f?e:void 0};
var Op=function(a){for(var b=[K.m.Le,K.m.He,K.m.Ie,K.m.Je,K.m.Ke,K.m.Me,K.m.Ne],c=Mp(a,3),d=k(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=k(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},Pp=function(a,b){this.eventId=a;this.priorityId=b;this.J={};this.T={};this.D={};this.O={};this.fa={};this.R={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},Qp=function(a,
b){a.J=b;return a},Rp=function(a,b){a.T=b;return a},Sp=function(a,b){a.D=b;return a},Tp=function(a,b){a.O=b;return a},Up=function(a,b){a.fa=b;return a},Vp=function(a,b){a.R=b;return a},Wp=function(a,b){a.eventMetadata=b||{};return a},Xp=function(a,b){a.onSuccess=b;return a},Yp=function(a,b){a.onFailure=b;return a},Zp=function(a,b){a.isGtmEvent=b;return a},$p=function(a){return new Lp(a.eventId,a.priorityId,a.J,a.T,a.D,a.O,a.R,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var P={C:{Rj:"accept_by_default",pg:"add_tag_timing",Lh:"allow_ad_personalization",Tj:"batch_on_navigation",Vj:"client_id_source",ye:"consent_event_id",ze:"consent_priority_id",yq:"consent_state",ja:"consent_updated",Tc:"conversion_linker_enabled",xa:"cookie_options",ug:"create_dc_join",vg:"create_fpm_geo_join",wg:"create_fpm_join",Jd:"create_google_join",Kd:"em_event",Cq:"endpoint_for_debug",jk:"enhanced_client_id_source",Rh:"enhanced_match_result",md:"euid_mode_enabled",kb:"event_start_timestamp_ms",
kl:"event_usage",bh:"extra_tag_experiment_ids",Jq:"add_parameter",yi:"attribution_reporting_experiment",zi:"counting_method",eh:"send_as_iframe",Kq:"parameter_order",fh:"parsed_target",Qn:"ga4_collection_subdomain",ol:"gbraid_cookie_marked",da:"hit_type",pd:"hit_type_override",Vn:"is_config_command",vf:"is_consent_update",wf:"is_conversion",sl:"is_ecommerce",rd:"is_external_event",Ei:"is_fallback_aw_conversion_ping_allowed",xf:"is_first_visit",tl:"is_first_visit_conversion",gh:"is_fl_fallback_conversion_flow_allowed",
be:"is_fpm_encryption",hh:"is_fpm_split",ce:"is_gcp_conversion",Fi:"is_google_signals_allowed",sd:"is_merchant_center",ih:"is_new_to_site",jh:"is_server_side_destination",de:"is_session_start",wl:"is_session_start_conversion",Nq:"is_sgtm_ga_ads_conversion_study_control_group",Oq:"is_sgtm_prehit",xl:"is_sgtm_service_worker",Gi:"is_split_conversion",Wn:"is_syn",yf:"join_id",Hi:"join_elapsed",zf:"join_timer_sec",fe:"tunnel_updated",Sq:"prehit_for_retry",Uq:"promises",Vq:"record_aw_latency",wc:"redact_ads_data",
he:"redact_click_ids",io:"remarketing_only",Fl:"send_ccm_parallel_ping",nh:"send_fledge_experiment",Xq:"send_ccm_parallel_test_ping",Ff:"send_to_destinations",Ni:"send_to_targets",Gl:"send_user_data_hit",ab:"source_canonical_id",Ja:"speculative",Jl:"speculative_in_message",Kl:"suppress_script_load",Ll:"syn_or_mod",Ol:"transient_ecsid",Hf:"transmission_type",Ta:"user_data",ar:"user_data_from_automatic",er:"user_data_from_automatic_getter",je:"user_data_from_code",qh:"user_data_from_manual",Ql:"user_data_mode",
If:"user_id_updated"}};var aq={Km:Number("5"),zr:Number("")},bq=[],cq=!1;function dq(a){bq.push(a)}var eq="?id="+$f.ctid,fq=void 0,gq={},hq=void 0,iq=new function(){var a=5;aq.Km>0&&(a=aq.Km);this.J=a;this.D=0;this.O=[]},jq=1E3;
function kq(a,b){var c=fq;if(c===void 0)if(b)c=op();else return"";for(var d=[Qk("https://www.googletagmanager.com"),"/a",eq],e=k(bq),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Id:!!a}),m=k(h),n=m.next();!n.done;n=m.next()){var p=k(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function lq(){if(Fj.fa&&(hq&&(l.clearTimeout(hq),hq=void 0),fq!==void 0&&mq)){var a=sn(Sm.Z.Oc);if(on(a))cq||(cq=!0,qn(a,lq));else{var b;if(!(b=gq[fq])){var c=iq;b=c.D<c.J?!1:ub()-c.O[c.D%c.J]<1E3}if(b||jq--<=0)M(1),gq[fq]=!0;else{var d=iq,e=d.D++%d.J;d.O[e]=ub();var f=kq(!0);$l({destinationId:$f.ctid,endpoint:56,eventId:fq},f);cq=mq=!1}}}}function nq(){if(Yk&&Fj.fa){var a=kq(!0,!0);$l({destinationId:$f.ctid,endpoint:56,eventId:fq},a)}}var mq=!1;
function oq(a){gq[a]||(a!==fq&&(lq(),fq=a),mq=!0,hq||(hq=l.setTimeout(lq,500)),kq().length>=2022&&lq())}var pq=kb();function qq(){pq=kb()}function rq(){return[["v","3"],["t","t"],["pid",String(pq)]]};var sq={};function tq(a,b,c){Yk&&a!==void 0&&(sq[a]=sq[a]||[],sq[a].push(c+b),oq(a))}function uq(a){var b=a.eventId,c=a.Id,d=[],e=sq[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete sq[b];return d};function vq(a,b,c,d){var e=vp(ym(a),!0);e&&wq.register(e,b,c,d)}function xq(a,b,c,d){var e=vp(c,d.isGtmEvent);e&&(Oj&&(d.deferrable=!0),wq.push("event",[b,a],e,d))}function yq(a,b,c,d){var e=vp(c,d.isGtmEvent);e&&wq.push("get",[a,b],e,d)}function zq(a){var b=vp(ym(a),!0),c;b?c=Aq(wq,b).D:c={};return c}function Bq(a,b){var c=vp(ym(a),!0);c&&Cq(wq,c,b)}
var Dq=function(){this.T={};this.D={};this.J={};this.fa=null;this.R={};this.O=!1;this.status=1},Eq=function(a,b,c,d){this.J=ub();this.D=b;this.args=c;this.messageContext=d;this.type=a},Fq=function(){this.destinations={};this.D={};this.commands=[]},Aq=function(a,b){var c=b.destinationId;om||(c=Dm(c));return a.destinations[c]=a.destinations[c]||new Dq},Gq=function(a,b,c,d){if(d.D){var e=Aq(a,d.D),f=e.fa;if(f){var g=d.D.id;om||(g=Dm(g));var h=cd(c,null),m=cd(e.T[g],null),n=cd(e.R,null),p=cd(e.D,null),
q=cd(a.D,null),r={};if(Yk)try{r=cd(ek,null)}catch(x){M(72)}var t=d.D.prefix,u=function(x){tq(d.messageContext.eventId,t,x)},v=$p(Zp(Yp(Xp(Wp(Up(Tp(Vp(Sp(Rp(Qp(new Pp(d.messageContext.eventId,d.messageContext.priorityId),h),m),n),p),q),r),d.messageContext.eventMetadata),function(){if(u){var x=u;u=void 0;x("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(u){var x=u;u=void 0;x("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),
w=function(){try{tq(d.messageContext.eventId,t,"1");var x=d.type,z=d.D.id;if(Zk&&x==="config"){var C,E=(C=vp(z))==null?void 0:C.ids;if(!(E&&E.length>1)){var F,G=pc("google_tag_data",{});G.td||(G.td={});F=G.td;var J=cd(v.R);cd(v.D,J);var N=[],X;for(X in F)F.hasOwnProperty(X)&&Jp(F[X],J).length&&N.push(X);N.length&&(Hp(z,N),Za("TAGGING",Dp[y.readyState]||14));F[z]=J}}f(d.D.id,b,d.J,v)}catch(Q){tq(d.messageContext.eventId,t,"4")}};b==="gtag.get"?w():qn(e.ma,w)}}};
Fq.prototype.register=function(a,b,c,d){var e=Aq(this,a);e.status!==3&&(e.fa=b,e.status=3,e.ma=sn(c),Cq(this,a,d||{}),this.flush())};
Fq.prototype.push=function(a,b,c,d){c!==void 0&&(Aq(this,c).status===1&&(Aq(this,c).status=2,this.push("require",[{}],c,{})),Aq(this,c).O&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[P.C.Ff]||(d.eventMetadata[P.C.Ff]=[c.destinationId]),d.eventMetadata[P.C.Ni]||(d.eventMetadata[P.C.Ni]=[c.id]));this.commands.push(new Eq(a,c,b,d));d.deferrable||this.flush()};
Fq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={xc:void 0,wh:void 0}){var f=this.commands[0],g=f.D;if(f.messageContext.deferrable)!g||Aq(this,g).O?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Aq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];nb(h,function(u,v){cd(Bb(u,v),b.D)});Dj(h,!0);break;case "config":var m=Aq(this,g);
e.xc={};nb(f.args[0],function(u){return function(v,w){cd(Bb(v,w),u.xc)}}(e));var n=!!e.xc[K.m.ld];delete e.xc[K.m.ld];var p=g.destinationId===g.id;Dj(e.xc,!0);n||(p?m.R={}:m.T[g.id]={});m.O&&n||Gq(this,K.m.qa,e.xc,f);m.O=!0;p?cd(e.xc,m.R):(cd(e.xc,m.T[g.id]),M(70));d=!0;D(166)||(Tn(e.xc,g.id),Nn=!0);break;case "event":e.wh={};nb(f.args[0],function(u){return function(v,w){cd(Bb(v,w),u.wh)}}(e));Dj(e.wh);Gq(this,f.args[1],e.wh,f);if(!D(166)){var q=void 0;!f.D||((q=f.messageContext.eventMetadata)==null?
0:q[P.C.Kd])||(Qn[f.D.id]=!0);Nn=!0}break;case "get":var r={},t=(r[K.m.oc]=f.args[0],r[K.m.Ic]=f.args[1],r);Gq(this,K.m.Bb,t,f);D(166)||(Nn=!0)}this.commands.shift();Hq(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};
var Hq=function(a,b){if(b.type!=="require")if(b.D)for(var c=Aq(a,b.D).J[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.J)for(var g=f.J[b.type]||[],h=0;h<g.length;h++)g[h]()}},Cq=function(a,b,c){var d=cd(c,null);cd(Aq(a,b).D,d);Aq(a,b).D=d},wq=new Fq;function Iq(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function Jq(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function Kq(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Bl(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=ic(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}Jq(e,"load",f);Jq(e,"error",f)};Iq(e,"load",f);Iq(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function Lq(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";yl(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});Mq(c,b)}
function Mq(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else Kq(c,a,b===void 0?!1:b,d===void 0?!1:d)};var Nq=function(){this.fa=this.fa;this.R=this.R};Nq.prototype.fa=!1;Nq.prototype.dispose=function(){this.fa||(this.fa=!0,this.O())};Nq.prototype[Symbol.dispose]=function(){this.dispose()};Nq.prototype.addOnDisposeCallback=function(a,b){this.fa?b!==void 0?a.call(b):a():(this.R||(this.R=[]),b&&(a=a.bind(b)),this.R.push(a))};Nq.prototype.O=function(){if(this.R)for(;this.R.length;)this.R.shift()()};function Oq(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var Pq=function(a,b){b=b===void 0?{}:b;Nq.call(this);this.D=null;this.ma={};this.Fb=0;this.T=null;this.J=a;var c;this.Za=(c=b.timeoutMs)!=null?c:500;var d;this.Ca=(d=b.lr)!=null?d:!1};sa(Pq,Nq);Pq.prototype.O=function(){this.ma={};this.T&&(Jq(this.J,"message",this.T),delete this.T);delete this.ma;delete this.J;delete this.D;Nq.prototype.O.call(this)};var Rq=function(a){return typeof a.J.__tcfapi==="function"||Qq(a)!=null};
Pq.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ca},d=bl(function(){return a(c)}),e=0;this.Za!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Za));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=Oq(c),c.internalBlockOnErrors=b.Ca,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{Sq(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};Pq.prototype.removeEventListener=function(a){a&&a.listenerId&&Sq(this,"removeEventListener",null,a.listenerId)};
var Uq=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=Tq(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&Tq(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?Tq(a.purpose.legitimateInterests,
b)&&Tq(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},Tq=function(a,b){return!(!a||!a[b])},Sq=function(a,b,c,d){c||(c=function(){});var e=a.J;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(Qq(a)){Vq(a);var g=++a.Fb;a.ma[g]=c;if(a.D){var h={};a.D.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},Qq=function(a){if(a.D)return a.D;a.D=zl(a.J,"__tcfapiLocator");return a.D},Vq=function(a){if(!a.T){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.ma[d.callId](d.returnValue,d.success)}catch(e){}};a.T=b;Iq(a.J,"message",b)}},Wq=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=Oq(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(Lq({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var Xq={1:0,3:0,4:0,7:3,9:3,10:3};function Yq(){return kp("tcf",function(){return{}})}var Zq=function(){return new Pq(l,{timeoutMs:-1})};
function $q(){var a=Yq(),b=Zq();Rq(b)&&!ar()&&!br()&&M(124);if(!a.active&&Rq(b)){ar()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,Tm().active=!0,a.tcString="tcunavailable");dp();try{b.addEventListener(function(c){if(c.internalErrorState!==0)cr(a),ep([K.m.V,K.m.Na,K.m.W]),Tm().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,br()&&(a.active=!0),!dr(c)||ar()||br()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in Xq)Xq.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(dr(c)){var g={},h;for(h in Xq)if(Xq.hasOwnProperty(h))if(h==="1"){var m,n=c,p={Zo:!0};p=p===void 0?{}:p;m=Wq(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.Zo)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?Uq(n,"1",0):!0:!1;g["1"]=m}else g[h]=Uq(c,h,Xq[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[K.m.V]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(ep([K.m.V,K.m.Na,K.m.W]),Tm().active=!0):(r[K.m.Na]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[K.m.W]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":ep([K.m.W]),Yo(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:er()||""}))}}else ep([K.m.V,K.m.Na,K.m.W])})}catch(c){cr(a),ep([K.m.V,K.m.Na,K.m.W]),Tm().active=!0}}}
function cr(a){a.type="e";a.tcString="tcunavailable"}function dr(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function ar(){return l.gtag_enable_tcf_support===!0}function br(){return Yq().enableAdvertiserConsentMode===!0}function er(){var a=Yq();if(a.active)return a.tcString}function fr(){var a=Yq();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function gr(a){if(!Xq.hasOwnProperty(String(a)))return!0;var b=Yq();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var hr=[K.m.V,K.m.ia,K.m.W,K.m.Na],ir={},jr=(ir[K.m.V]=1,ir[K.m.ia]=2,ir);function kr(a){if(a===void 0)return 0;switch(O(a,K.m.Fa)){case void 0:return 1;case !1:return 3;default:return 2}}function lr(){return D(182)?(D(183)?Oi.hp:Oi.jp).indexOf(mo())!==-1&&lc.globalPrivacyControl===!0:mo()==="US-CO"&&lc.globalPrivacyControl===!0}
function mr(a){if(lr())return!1;var b=kr(a);if(b===3)return!1;switch(bn(K.m.Na)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}function nr(){return dn()||!an(K.m.V)||!an(K.m.ia)}function or(){var a={},b;for(b in jr)jr.hasOwnProperty(b)&&(a[jr[b]]=bn(b));return"G1"+Te(a[1]||0)+Te(a[2]||0)}var pr={},qr=(pr[K.m.V]=0,pr[K.m.ia]=1,pr[K.m.W]=2,pr[K.m.Na]=3,pr);
function rr(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function sr(a){for(var b="1",c=0;c<hr.length;c++){var d=b,e,f=hr[c],g=$m.delegatedConsentTypes[f];e=g===void 0?0:qr.hasOwnProperty(g)?12|qr[g]:8;var h=Tm();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|rr(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[rr(m.declare)<<4|rr(m.default)<<2|rr(m.update)])}var n=b,p=(lr()?1:0)<<3,q=(dn()?1:0)<<2,r=kr(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[$m.containerScopedDefaults.ad_storage<<4|$m.containerScopedDefaults.analytics_storage<<2|$m.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[($m.usedContainerScopedDefaults?1:0)<<2|$m.containerScopedDefaults.ad_personalization]}
function tr(){if(!an(K.m.W))return"-";for(var a=Object.keys(gi),b={},c=k(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=$m.corePlatformServices[e]!==!1}for(var f="",g=k(a),h=g.next();!h.done;h=g.next()){var m=h.value;b[m]&&(f+=gi[m])}($m.usedCorePlatformServices?$m.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function ur(){return oo()||(ar()||br())&&fr()==="1"?"1":"0"}function vr(){return(oo()?!0:!(!ar()&&!br())&&fr()==="1")||!an(K.m.W)}
function wr(){var a="0",b="0",c;var d=Yq();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=Yq();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;oo()&&(h|=1);fr()==="1"&&(h|=2);ar()&&(h|=4);var m;var n=Yq();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);Tm().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function xr(){return mo()==="US-CO"};function yr(){var a=!1;return a};var zr={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Ar(a){a=a===void 0?{}:a;var b=$f.ctid.split("-")[0].toUpperCase(),c={ctid:$f.ctid,Wp:Jj.Li,Zp:Jj.Mi,Gp:nm.Af?2:1,kq:a.Bm,Mf:$f.canonicalContainerId};c.Mf!==a.Oa&&(c.Oa=a.Oa);var d=Am();c.Mp=d?d.canonicalContainerId:void 0;Pj?(c.Ih=zr[b],c.Ih||(c.Ih=0)):c.Ih=Rj?13:10;Fj.D?(c.Eh=0,c.wo=2):Fj.J?c.Eh=1:yr()?c.Eh=2:c.Eh=3;var e={};e[6]=om;Fj.O===2?e[7]=!0:Fj.O===1&&(e[2]=!0);if(oc){var f=Bk(Hk(oc),"host");f&&(e[8]=f.match(/^(www\.)?googletagmanager\.com$/)===null)}c.zo=e;var g=a.rh,h;var m=c.Ih,
n=c.Eh;m===void 0?h="":(n||(n=0),h=""+Ve(1,1)+Se(m<<2|n));var p=c.wo,q="4"+h+(p?""+Ve(2,1)+Se(p):""),r,t=c.Zp;r=t&&Ue.test(t)?""+Ve(3,2)+t:"";var u,v=c.Wp;u=v?""+Ve(4,1)+Se(v):"";var w;var x=c.ctid;if(x&&g){var z=x.split("-"),C=z[0].toUpperCase();if(C!=="GTM"&&C!=="OPT")w="";else{var E=z[1];w=""+Ve(5,3)+Se(1+E.length)+(c.Gp||0)+E}}else w="";var F=c.kq,G=c.Mf,J=c.Oa,N=c.xr,X=q+r+u+w+(F?""+Ve(6,1)+Se(F):"")+(G?""+Ve(7,3)+Se(G.length)+G:"")+(J?""+Ve(8,3)+Se(J.length)+J:"")+(N?""+Ve(9,3)+Se(N.length)+
N:""),Q;var na=c.zo;na=na===void 0?{}:na;for(var S=[],aa=k(Object.keys(na)),Y=aa.next();!Y.done;Y=aa.next()){var V=Y.value;S[Number(V)]=na[V]}if(S.length){var ka=Ve(10,3),ja;if(S.length===0)ja=Se(0);else{for(var la=[],Na=0,Xa=!1,Ea=0;Ea<S.length;Ea++){Xa=!0;var Ya=Ea%6;S[Ea]&&(Na|=1<<Ya);Ya===5&&(la.push(Se(Na)),Na=0,Xa=!1)}Xa&&la.push(Se(Na));ja=la.join("")}var fb=ja;Q=""+ka+Se(fb.length)+fb}else Q="";var Ib=c.Mp;return X+Q+(Ib?""+Ve(11,3)+Se(Ib.length)+Ib:"")};function Br(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var Cr={P:{jo:0,Sj:1,rg:2,Yj:3,Nh:4,Wj:5,Xj:6,Zj:7,Oh:8,il:9,fl:10,xi:11,jl:12,ah:13,nl:14,Cf:15,ho:16,ie:17,Ui:18,Vi:19,Wi:20,Ml:21,Xi:22,Ph:23,ik:24}};Cr.P[Cr.P.jo]="RESERVED_ZERO";Cr.P[Cr.P.Sj]="ADS_CONVERSION_HIT";Cr.P[Cr.P.rg]="CONTAINER_EXECUTE_START";Cr.P[Cr.P.Yj]="CONTAINER_SETUP_END";Cr.P[Cr.P.Nh]="CONTAINER_SETUP_START";Cr.P[Cr.P.Wj]="CONTAINER_BLOCKING_END";Cr.P[Cr.P.Xj]="CONTAINER_EXECUTE_END";Cr.P[Cr.P.Zj]="CONTAINER_YIELD_END";Cr.P[Cr.P.Oh]="CONTAINER_YIELD_START";Cr.P[Cr.P.il]="EVENT_EXECUTE_END";
Cr.P[Cr.P.fl]="EVENT_EVALUATION_END";Cr.P[Cr.P.xi]="EVENT_EVALUATION_START";Cr.P[Cr.P.jl]="EVENT_SETUP_END";Cr.P[Cr.P.ah]="EVENT_SETUP_START";Cr.P[Cr.P.nl]="GA4_CONVERSION_HIT";Cr.P[Cr.P.Cf]="PAGE_LOAD";Cr.P[Cr.P.ho]="PAGEVIEW";Cr.P[Cr.P.ie]="SNIPPET_LOAD";Cr.P[Cr.P.Ui]="TAG_CALLBACK_ERROR";Cr.P[Cr.P.Vi]="TAG_CALLBACK_FAILURE";Cr.P[Cr.P.Wi]="TAG_CALLBACK_SUCCESS";Cr.P[Cr.P.Ml]="TAG_EXECUTE_END";Cr.P[Cr.P.Xi]="TAG_EXECUTE_START";Cr.P[Cr.P.Ph]="CUSTOM_PERFORMANCE_START";Cr.P[Cr.P.ik]="CUSTOM_PERFORMANCE_END";var Dr=[],Er={},Fr={};var Gr=["1"];function Hr(a){return a.origin!=="null"};function Ir(a,b,c){for(var d=[],e=b.split(";"),f=function(p){return ng(12)?p.trim():p.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&m===a){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));d.push(n)}}return d};function Jr(a,b,c,d){if(!Kr(d))return[];if(Dr.includes("1")){var e;(e=Rc())==null||e.mark("1-"+Cr.P.Ph+"-"+(Fr["1"]||0))}var f=Ir(a,String(b||Lr()),c);if(Dr.includes("1")){var g="1-"+Cr.P.ik+"-"+(Fr["1"]||0),h={start:"1-"+Cr.P.Ph+"-"+(Fr["1"]||0),end:g},m;(m=Rc())==null||m.mark(g);var n,p,q=(p=(n=Rc())==null?void 0:n.measure(g,h))==null?void 0:p.duration;q!==void 0&&(Fr["1"]=(Fr["1"]||0)+1,Er["1"]=q+(Er["1"]||0))}return f}
function Mr(a,b,c,d,e){if(Kr(e)){var f=Nr(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=Or(f,function(g){return g.Ko},b);if(f.length===1)return f[0];f=Or(f,function(g){return g.Op},c);return f[0]}}}function Pr(a,b,c,d){var e=Lr(),f=window;Hr(f)&&(f.document.cookie=a);var g=Lr();return e!==g||c!==void 0&&Jr(b,g,!1,d).indexOf(c)>=0}
function Qr(a,b,c,d){function e(w,x,z){if(z==null)return delete h[x],w;h[x]=z;return w+"; "+x+"="+z}function f(w,x){if(x==null)return w;h[x]=!0;return w+"; "+x}if(!Kr(c.Dc))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=Rr(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.Jp);g=e(g,"samesite",c.aq);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=Sr(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!Tr(u,c.path)&&Pr(v,a,b,c.Dc))return 0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return Tr(n,c.path)?1:Pr(g,a,b,c.Dc)?0:1}function Ur(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");return Qr(a,b,c)}
function Or(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function Nr(a,b,c){for(var d=[],e=Jr(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({Co:e[f],Do:g.join("."),Ko:Number(n[0])||1,Op:Number(n[1])||1})}}}return d}function Rr(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var Vr=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,Wr=/(^|\.)doubleclick\.net$/i;function Tr(a,b){return a!==void 0&&(Wr.test(window.document.location.hostname)||b==="/"&&Vr.test(a))}function Xr(a){if(!a)return 1;var b=a;ng(7)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function Yr(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function Zr(a,b){var c=""+Xr(a),d=Yr(b);d>1&&(c+="-"+d);return c}
var Lr=function(){return Hr(window)?window.document.cookie:""},Kr=function(a){return a&&ng(8)?(Array.isArray(a)?a:[a]).every(function(b){return cn(b)&&an(b)}):!0},Sr=function(){var a=[],b=window.document.location.hostname.split(".");if(b.length===4){var c=b[b.length-1];if(Number(c).toString()===c)return["none"]}for(var d=b.length-2;d>=0;d--)a.push(b.slice(d).join("."));var e=window.document.location.hostname;Wr.test(e)||Vr.test(e)||a.push("none");return a};function $r(a){var b=Math.round(Math.random()*2147483647);return a?String(b^Br(a)&2147483647):String(b)}function as(a){return[$r(a),Math.round(ub()/1E3)].join(".")}function bs(a,b,c,d,e){var f=Xr(b),g;return(g=Mr(a,f,Yr(c),d,e))==null?void 0:g.Do};function cs(a,b,c,d){var e,f=Number(a.Bc!=null?a.Bc:void 0);f!==0&&(e=new Date((b||ub())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Dc:d}};var ds=["ad_storage","ad_user_data"];function es(a,b){if(!a)return Za("TAGGING",32),10;if(b===null||b===void 0||b==="")return Za("TAGGING",33),11;var c=fs(!1);if(c.error!==0)return Za("TAGGING",34),c.error;if(!c.value)return Za("TAGGING",35),2;c.value[a]=b;var d=gs(c);d!==0&&Za("TAGGING",36);return d}
function hs(a){if(!a)return Za("TAGGING",27),{error:10};var b=fs();if(b.error!==0)return Za("TAGGING",29),b;if(!b.value)return Za("TAGGING",30),{error:2};if(!(a in b.value))return Za("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(Za("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function fs(a){a=a===void 0?!0:a;if(!an(ds))return Za("TAGGING",43),{error:3};try{if(!l.localStorage)return Za("TAGGING",44),{error:1}}catch(f){return Za("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=l.localStorage.getItem("_gcl_ls")}catch(f){return Za("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return Za("TAGGING",47),{error:12}}}catch(f){return Za("TAGGING",48),{error:8}}if(b.schema!=="gcl")return Za("TAGGING",49),{error:4};
if(b.version!==1)return Za("TAGGING",50),{error:5};try{var e=is(b);a&&e&&gs({value:b,error:0})}catch(f){return Za("TAGGING",48),{error:8}}return{value:b,error:0}}
function is(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,Za("TAGGING",54),!0}else{for(var c=!1,d=k(Object.keys(a)),e=d.next();!e.done;e=d.next())c=is(a[e.value])||c;return c}return!1}
function gs(a){if(a.error)return a.error;if(!a.value)return Za("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return Za("TAGGING",52),6}try{l.localStorage.setItem("_gcl_ls",c)}catch(d){return Za("TAGGING",53),7}return 0};function js(){if(!ks())return-1;var a=ls();return a!==-1&&ms(a+1)?a+1:-1}function ls(){if(!ks())return-1;var a=hs("gcl_ctr");if(!a||a.error!==0||!a.value||typeof a.value!=="object")return-1;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return-1;var c=b.value.value;return c==null||Number.isNaN(c)?-1:Number(c)}catch(d){return-1}}function ks(){return an(["ad_storage","ad_user_data"])?ng(11):!1}
function ms(a,b){b=b||{};var c=ub();return es("gcl_ctr",{value:{value:a,creationTimeMs:c},expires:Number(cs(b,c,!0).expires)})===0?!0:!1};var ns;function os(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=ps,d=qs,e=rs();if(!e.init){Cc(y,"mousedown",a);Cc(y,"keyup",a);Cc(y,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function ss(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};rs().decorators.push(f)}
function ts(a,b,c){for(var d=rs().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==y.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&xb(e,g.callback())}}return e}
function rs(){var a=pc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var us=/(.*?)\*(.*?)\*(.*)/,vs=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,ws=/^(?:www\.|m\.|amp\.)+/,xs=/([^?#]+)(\?[^#]*)?(#.*)?/;function ys(a){var b=xs.exec(a);if(b)return{Dj:b[1],query:b[2],fragment:b[3]}}function zs(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function As(a,b){var c=[lc.userAgent,(new Date).getTimezoneOffset(),lc.userLanguage||lc.language,Math.floor(ub()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=ns)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}ns=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^ns[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function Bs(a){return function(b){var c=Hk(l.location.href),d=c.search.replace("?",""),e=zk(d,"_gl",!1,!0)||"";b.query=Cs(e)||{};var f=Bk(c,"fragment"),g;var h=-1;if(zb(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=Cs(g||"")||{};a&&Ds(c,d,f)}}function Es(a,b){var c=zs(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function Ds(a,b,c){function d(g,h){var m=Es("_gl",g);m.length&&(m=h+m);return m}if(kc&&kc.replaceState){var e=zs("_gl");if(e.test(b)||e.test(c)){var f=Bk(a,"path");b=d(b,"?");c=d(c,"#");kc.replaceState({},"",""+f+b+c)}}}function Fs(a,b){var c=Bs(!!b),d=rs();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(xb(e,f.query),a&&xb(e,f.fragment));return e}
var Cs=function(a){try{var b=Gs(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=Va(d[e+1]);c[f]=g}Za("TAGGING",6);return c}}catch(h){Za("TAGGING",8)}};function Gs(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=us.exec(d);if(f){c=f;break a}d=decodeURIComponent(d)}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===As(h,p)){m=!0;break a}m=!1}if(m)return h;Za("TAGGING",7)}}}
function Hs(a,b,c,d,e){function f(p){p=Es(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=ys(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.Dj+h+m}
function Is(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],w;for(w in n)if(n.hasOwnProperty(w)){var x=n[w];x!==void 0&&x===x&&x!==null&&x.toString()!=="[object Object]"&&(v.push(w),v.push(Ua(String(x))))}var z=v.join("*");u=["1",As(z),z].join("*");d?(ng(3)||ng(1)||!p)&&Js("_gl",u,a,p,q):Ks("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=ts(b,1,d),f=ts(b,2,d),g=ts(b,4,d),h=ts(b,3,d);c(e,!1,!1);c(f,!0,!1);ng(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
Ls(m,h[m],a)}function Ls(a,b,c){c.tagName.toLowerCase()==="a"?Ks(a,b,c):c.tagName.toLowerCase()==="form"&&Js(a,b,c)}function Ks(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!ng(5)||d)){var h=l.location.href,m=ys(c.href),n=ys(h);g=!(m&&n&&m.Dj===n.Dj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=Hs(a,b,c.href,d,e);$b.test(p)&&(c.href=p)}}
function Js(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=Hs(a,b,f,d,e);$b.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=y.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function ps(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||Is(e,e.hostname)}}catch(g){}}function qs(a){try{var b=a.getAttribute("action");if(b){var c=Bk(Hk(b),"host");Is(a,c)}}catch(d){}}function Ms(a,b,c,d){os();var e=c==="fragment"?2:1;d=!!d;ss(a,b,e,d,!1);e===2&&Za("TAGGING",23);d&&Za("TAGGING",24)}
function Ns(a,b){os();ss(a,[Dk(l.location,"host",!0)],b,!0,!0)}function Os(){var a=y.location.hostname,b=vs.exec(y.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?decodeURIComponent(f[2]):decodeURIComponent(g)}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(ws,""),m=e.replace(ws,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function Ps(a,b){return a===!1?!1:a||b||Os()};var Qs=["1"],Rs={},Ss={};function Ts(a,b){b=b===void 0?!0:b;var c=Us(a.prefix);if(Rs[c])Vs(a);else if(Ws(c,a.path,a.domain)){var d=Ss[Us(a.prefix)]||{id:void 0,Dh:void 0};b&&Xs(a,d.id,d.Dh);Vs(a)}else{var e=Jk("auiddc");if(e)Za("TAGGING",17),Rs[c]=e;else if(b){var f=Us(a.prefix),g=as();Ys(f,g,a);Ws(c,a.path,a.domain);Vs(a,!0)}}}
function Vs(a,b){if((b===void 0?0:b)&&ks()){var c=fs(!1);c.error!==0?Za("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,gs(c)!==0&&Za("TAGGING",41)):Za("TAGGING",40):Za("TAGGING",39)}an(["ad_storage","ad_user_data"])&&ng(10)&&ls()===-1&&ms(0,a)}function Xs(a,b,c){var d=Us(a.prefix),e=Rs[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(ub()/1E3)));Ys(d,h,a,g*1E3)}}}}
function Ys(a,b,c,d){var e;e=["1",Zr(c.domain,c.path),b].join(".");var f=cs(c,d);f.Dc=Zs();Ur(a,e,f)}function Ws(a,b,c){var d=bs(a,b,c,Qs,Zs());if(!d)return!1;$s(a,d);return!0}function $s(a,b){var c=b.split(".");c.length===5?(Rs[a]=c.slice(0,2).join("."),Ss[a]={id:c.slice(2,4).join("."),Dh:Number(c[4])||0}):c.length===3?Ss[a]={id:c.slice(0,2).join("."),Dh:Number(c[2])||0}:Rs[a]=b}function Us(a){return(a||"_gcl")+"_au"}
function at(a){function b(){an(c)&&a()}var c=Zs();gn(function(){b();an(c)||hn(b,c)},c)}function bt(a){var b=Fs(!0),c=Us(a.prefix);at(function(){var d=b[c];if(d){$s(c,d);var e=Number(Rs[c].split(".")[1])*1E3;if(e){Za("TAGGING",16);var f=cs(a,e);f.Dc=Zs();var g=["1",Zr(a.domain,a.path),d].join(".");Ur(c,g,f)}}})}function ct(a,b,c,d,e){e=e||{};var f=function(){var g={},h=bs(a,e.path,e.domain,Qs,Zs());h&&(g[a]=h);return g};at(function(){Ms(f,b,c,d)})}
function Zs(){return["ad_storage","ad_user_data"]};function dt(a){for(var b=[],c=y.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Pj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function et(a,b){var c=dt(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Pj]||(d[c[e].Pj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Pj].push(g)}}return d};var ft={},gt=(ft.k={ba:/^[\w-]+$/},ft.b={ba:/^[\w-]+$/,Kj:!0},ft.i={ba:/^[1-9]\d*$/},ft.h={ba:/^\d+$/},ft.t={ba:/^[1-9]\d*$/},ft.d={ba:/^[A-Za-z0-9_-]+$/},ft.j={ba:/^\d+$/},ft.u={ba:/^[1-9]\d*$/},ft.l={ba:/^[01]$/},ft.o={ba:/^[1-9]\d*$/},ft.g={ba:/^[01]$/},ft.s={ba:/^.+$/},ft);var ht={},lt=(ht[5]={Kh:{2:it},wj:"2",sh:["k","i","b","u"]},ht[4]={Kh:{2:it,GCL:jt},wj:"2",sh:["k","i","b"]},ht[2]={Kh:{GS2:it,GS1:kt},wj:"GS2",sh:"sogtjlhd".split("")},ht);function mt(a,b,c){var d=lt[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Kh[e];if(f)return f(a,b)}}}
function it(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=lt[b];if(f){for(var g=f.sh,h=k(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=gt[p];r&&(r.Kj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function nt(a,b,c){var d=lt[b];if(d)return[d.wj,c||"1",ot(a,b)].join(".")}
function ot(a,b){var c=lt[b];if(c){for(var d=[],e=k(c.sh),f=e.next();!f.done;f=e.next()){var g=f.value,h=gt[g];if(h){var m=a[g];if(m!==void 0)if(h.Kj&&Array.isArray(m))for(var n=k(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function jt(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function kt(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var pt=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function qt(a,b,c){if(lt[b]){for(var d=[],e=Jr(a,void 0,void 0,pt.get(b)),f=k(e),g=f.next();!g.done;g=f.next()){var h=mt(g.value,b,c);h&&d.push(rt(h))}return d}}function st(a,b,c,d,e){d=d||{};var f=Zr(d.domain,d.path),g=nt(b,c,f);if(!g)return 1;var h=cs(d,e,void 0,pt.get(c));return Ur(a,g,h)}function tt(a,b){var c=b.ba;return typeof c==="function"?c(a):c.test(a)}
function rt(a){for(var b=k(Object.keys(a)),c=b.next(),d={};!c.done;d={Of:void 0},c=b.next()){var e=c.value,f=a[e];d.Of=gt[e];d.Of?d.Of.Kj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return tt(h,g.Of)}}(d)):void 0:typeof f==="string"&&tt(f,d.Of)||(a[e]=void 0):a[e]=void 0}return a};var ut=function(){this.value=0};ut.prototype.set=function(a){return this.value|=1<<a};var vt=function(a,b){b<=0||(a.value|=1<<b-1)};ut.prototype.get=function(){return this.value};ut.prototype.clear=function(a){this.value&=~(1<<a)};ut.prototype.clearAll=function(){this.value=0};ut.prototype.equals=function(a){return this.value===a.value};function wt(){var a=String,b=l.location.hostname,c=l.location.pathname,d=b=Hb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Hb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(Br((""+b+e).toLowerCase()))};var xt=/^\w+$/,zt=/^[\w-]+$/,At={},Bt=(At.aw="_aw",At.dc="_dc",At.gf="_gf",At.gp="_gp",At.gs="_gs",At.ha="_ha",At.ag="_ag",At.gb="_gb",At);function Ct(){return["ad_storage","ad_user_data"]}function Dt(a){return!ng(8)||an(a)}function Et(a,b){function c(){var d=Dt(b);d&&a();return d}gn(function(){c()||hn(c,b)},b)}function Ft(a){return Gt(a).map(function(b){return b.gclid})}function Ht(a){return It(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}
function It(a){var b=Jt(a.prefix),c=Kt("gb",b),d=Kt("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=Gt(c).map(e("gb")),g=Lt(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}function Mt(a,b,c,d,e,f){var g=jb(a,function(h){return h.gclid===c});g?(g.timestamp<d&&(g.timestamp=d,g.Cd=f),g.labels=Nt(g.labels||[],e||[])):a.push({version:b,gclid:c,timestamp:d,labels:e,Cd:f})}
function Lt(a){for(var b=qt(a,5)||[],c=[],d=k(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=g.k,m=g.b,n=Ot(f);if(n){var p=void 0;ng(9)&&(p=f.u);Mt(c,"2",h,n,m||[],p)}}return c.sort(function(q,r){return r.timestamp-q.timestamp})}function Gt(a){for(var b=[],c=Jr(a,y.cookie,void 0,Ct()),d=k(c),e=d.next();!e.done;e=d.next()){var f=Pt(e.value);if(f!=null){var g=f;Mt(b,g.version,g.gclid,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return Qt(b)}
function Rt(a,b){for(var c=[],d=k(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=k(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function St(a,b,c){c=c===void 0?!1:c;for(var d,e,f=k(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Aa&&b.Aa&&h.Aa.equals(b.Aa)&&(e=h)}if(d){var m,n,p=(m=d.Aa)!=null?m:new ut,q=(n=b.Aa)!=null?n:new ut;p.value|=q.value;d.Aa=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Cd=b.Cd);d.labels=Rt(d.labels||[],b.labels||[]);d.Ab=Rt(d.Ab||[],b.Ab||[])}else c&&e?Object.assign(e,b):a.push(b)}
function Tt(a){if(!a)return new ut;var b=new ut;if(a===1)return vt(b,2),vt(b,3),b;vt(b,a);return b}
function Ut(){var a=hs("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(zt))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new ut;typeof e==="number"?g=Tt(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Aa:g,Ab:[2]}}catch(h){return null}}
function Vt(){var a=hs("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(zt))return b;var f=new ut,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Aa:f,Ab:[2]});return b},[])}catch(b){return null}}
function Wt(a){for(var b=[],c=Jr(a,y.cookie,void 0,Ct()),d=k(c),e=d.next();!e.done;e=d.next()){var f=Pt(e.value);f!=null&&(f.Cd=void 0,f.Aa=new ut,f.Ab=[1],St(b,f))}var g=Ut();g&&(g.Cd=void 0,g.Ab=g.Ab||[2],St(b,g));if(ng(14)){var h=Vt();if(h)for(var m=k(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Cd=void 0;p.Ab=p.Ab||[2];St(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return Qt(b)}
function Nt(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function Jt(a){return a&&typeof a==="string"&&a.match(xt)?a:"_gcl"}
function Xt(a,b,c){var d=Hk(a),e=Bk(d,"query",!1,void 0,"gclsrc"),f={value:Bk(d,"query",!1,void 0,"gclid"),Aa:new ut};vt(f.Aa,c?4:2);if(b&&(!f.value||!e)){var g=d.hash.replace("#","");f.value||(f.value=zk(g,"gclid",!1),f.Aa.clearAll(),vt(f.Aa,3));e||(e=zk(g,"gclsrc",!1))}return!f.value||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function Yt(a,b){var c=Hk(a),d=Bk(c,"query",!1,void 0,"gclid"),e=Bk(c,"query",!1,void 0,"gclsrc"),f=Bk(c,"query",!1,void 0,"wbraid");f=Fb(f);var g=Bk(c,"query",!1,void 0,"gbraid"),h=Bk(c,"query",!1,void 0,"gad_source"),m=Bk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||zk(n,"gclid",!1);e=e||zk(n,"gclsrc",!1);f=f||zk(n,"wbraid",!1);g=g||zk(n,"gbraid",!1);h=h||zk(n,"gad_source",!1)}return Zt(d,e,m,f,g,h)}function $t(){return Yt(l.location.href,!0)}
function Zt(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(zt))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&zt.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&zt.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&zt.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function au(a){for(var b=$t(),c=!0,d=k(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=Yt(l.document.referrer,!1),b.gad_source=void 0);bu(b,!1,a)}
function cu(a){au(a);var b=Xt(l.location.href,!0,!1);b.length||(b=Xt(l.document.referrer,!1,!0));if(b.length){var c=b[0];a=a||{};var d=ub(),e=cs(a,d,!0),f=Ct(),g=function(){Dt(f)&&e.expires!==void 0&&es("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Aa.get()},expires:Number(e.expires)})};gn(function(){g();Dt(f)||hn(g,f)},f)}}
function du(a,b){b=b||{};var c=ub(),d=cs(b,c,!0),e=Ct(),f=function(){if(Dt(e)&&d.expires!==void 0){var g=Vt()||[];St(g,{version:"",gclid:a,timestamp:c,expires:Number(d.expires),Aa:Tt(5)},!0);es("gcl_aw",g.map(function(h){return{value:{value:h.gclid,creationTimeMs:h.timestamp,linkDecorationSources:h.Aa?h.Aa.get():0},expires:Number(h.expires)}}))}};gn(function(){Dt(e)?f():hn(f,e)},e)}
function bu(a,b,c,d,e){c=c||{};e=e||[];var f=Jt(c.prefix),g=d||ub(),h=Math.round(g/1E3),m=Ct(),n=!1,p=!1,q=function(){if(Dt(m)){var r=cs(c,g,!0);r.Dc=m;for(var t=function(N,X){var Q=Kt(N,f);Q&&(Ur(Q,X,r),N!=="gb"&&(n=!0))},u=function(N){var X=["GCL",h,N];e.length>0&&X.push(e.join("."));return X.join(".")},v=k(["aw","dc","gf","ha","gp"]),w=v.next();!w.done;w=v.next()){var x=w.value;a[x]&&t(x,u(a[x][0]))}if(!n&&a.gb){var z=a.gb[0],C=Kt("gb",f);!b&&Gt(C).some(function(N){return N.gclid===z&&N.labels&&
N.labels.length>0})||t("gb",u(z))}}if(!p&&a.gbraid&&Dt("ad_storage")&&(p=!0,!n)){var E=a.gbraid,F=Kt("ag",f);if(b||!Lt(F).some(function(N){return N.gclid===E&&N.labels&&N.labels.length>0})){var G={},J=(G.k=E,G.i=""+h,G.b=e,G);st(F,J,5,c,g)}}eu(a,f,g,c)};gn(function(){q();Dt(m)||hn(q,m)},m)}
function eu(a,b,c,d){if(a.gad_source!==void 0&&Dt("ad_storage")){if(ng(4)){var e=Qc();if(e==="r"||e==="h")return}var f=a.gad_source,g=Kt("gs",b);if(g){var h=Math.floor((ub()-(Pc()||0))/1E3),m;if(ng(9)){var n=wt(),p={};m=(p.k=f,p.i=""+h,p.u=n,p)}else{var q={};m=(q.k=f,q.i=""+h,q)}st(g,m,5,d,c)}}}
function fu(a,b){var c=Fs(!0);Et(function(){for(var d=Jt(b.prefix),e=0;e<a.length;++e){var f=a[e];if(Bt[f]!==void 0){var g=Kt(f,d),h=c[g];if(h){var m=Math.min(gu(h),ub()),n;b:{for(var p=m,q=Jr(g,y.cookie,void 0,Ct()),r=0;r<q.length;++r)if(gu(q[r])>p){n=!0;break b}n=!1}if(!n){var t=cs(b,m,!0);t.Dc=Ct();Ur(g,h,t)}}}}bu(Zt(c.gclid,c.gclsrc),!1,b)},Ct())}
function hu(a){var b=["ag"],c=Fs(!0),d=Jt(a.prefix);Et(function(){for(var e=0;e<b.length;++e){var f=Kt(b[e],d);if(f){var g=c[f];if(g){var h=mt(g,5);if(h){var m=Ot(h);m||(m=ub());var n;a:{for(var p=m,q=qt(f,5),r=0;r<q.length;++r)if(Ot(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);st(f,h,5,a,m)}}}}},["ad_storage"])}function Kt(a,b){var c=Bt[a];if(c!==void 0)return b+c}function gu(a){return iu(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function Ot(a){return a?(Number(a.i)||0)*1E3:0}function Pt(a){var b=iu(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function iu(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!zt.test(a[2])?[]:a}
function ju(a,b,c,d,e){if(Array.isArray(b)&&Hr(l)){var f=Jt(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=Kt(a[m],f);if(n){var p=Jr(n,y.cookie,void 0,Ct());p.length&&(h[n]=p.sort()[p.length-1])}}return h};Et(function(){Ms(g,b,c,d)},Ct())}}
function ku(a,b,c,d){if(Array.isArray(a)&&Hr(l)){var e=["ag"],f=Jt(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=Kt(e[m],f);if(!n)return{};var p=qt(n,5);if(p.length){var q=p.sort(function(r,t){return Ot(t)-Ot(r)})[0];h[n]=nt(q,5)}}return h};Et(function(){Ms(g,a,b,c)},["ad_storage"])}}function Qt(a){return a.filter(function(b){return zt.test(b.gclid)})}
function lu(a,b){if(Hr(l)){for(var c=Jt(b.prefix),d={},e=0;e<a.length;e++)Bt[a[e]]&&(d[a[e]]=Bt[a[e]]);Et(function(){nb(d,function(f,g){var h=Jr(c+g,y.cookie,void 0,Ct());h.sort(function(t,u){return gu(u)-gu(t)});if(h.length){var m=h[0],n=gu(m),p=iu(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=iu(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];bu(q,!0,b,n,p)}})},Ct())}}
function mu(a){var b=["ag"],c=["gbraid"];Et(function(){for(var d=Jt(a.prefix),e=0;e<b.length;++e){var f=Kt(b[e],d);if(!f)break;var g=qt(f,5);if(g.length){var h=g.sort(function(q,r){return Ot(r)-Ot(q)})[0],m=Ot(h),n=h.b,p={};p[c[e]]=h.k;bu(p,!0,a,m,n)}}},["ad_storage"])}function nu(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function ou(a){function b(h,m,n){n&&(h[m]=n)}if(dn()){var c=$t(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:Fs(!1)._gs);if(nu(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);Ns(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);Ns(function(){return g},1)}}}
function pu(a){if(!ng(1))return null;var b=Fs(!0).gad_source;if(b!=null)return l.location.hash="",b;if(ng(2)){var c=Hk(l.location.href);b=Bk(c,"query",!1,void 0,"gad_source");if(b!=null)return b;var d=$t();if(nu(d,a))return"0"}return null}function qu(a){var b=pu(a);b!=null&&Ns(function(){var c={};return c.gad_source=b,c},4)}
function ru(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}function su(a,b,c,d){var e=[];c=c||{};if(!Dt(Ct()))return e;var f=Gt(a),g=ru(e,f,b);if(g.length&&!d)for(var h=k(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=cs(c,p,!0);r.Dc=Ct();Ur(a,q,r)}return e}
function tu(a,b){var c=[];b=b||{};var d=It(b),e=ru(c,d,a);if(e.length)for(var f=k(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=Jt(b.prefix),n=Kt(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var w={},x=(w.k=r,w.i=""+v,w.b=(t||[]).concat([a]),w);st(n,x,5,b,u)}else if(h.type==="gb"){var z=[q,v,r].concat(t||[],[a]).join("."),C=cs(b,u,!0);C.Dc=Ct();Ur(n,z,C)}}return c}
function uu(a,b){var c=Jt(b),d=Kt(a,c);if(!d)return 0;var e;e=a==="ag"?Lt(d):Gt(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function vu(a){for(var b=0,c=k(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function wu(a){var b=Math.max(uu("aw",a),vu(Dt(Ct())?et():{})),c=Math.max(uu("gb",a),vu(Dt(Ct())?et("_gac_gb",!0):{}));c=Math.max(c,uu("ag",a));return c>b};
var xu=function(a,b){b=b===void 0?!1:b;var c=kp("ads_pageview",function(){return{}});if(c[a])return!1;b||(c[a]=!0);return!0},yu=function(a){return Ik(a,"gclid dclid gbraid wbraid gclaw gcldc gclha gclgf gclgb _gl".split(" "),"0")},Gu=function(a,b,c,d,e){var f=Jt(a.prefix);if(xu(f,!0)){var g=$t(),h=[],m=g.gclid,n=g.dclid,p=g.gclsrc||"aw",q=zu(),r=q.Tf,t=q.fm;!m||p!=="aw.ds"&&p!=="aw"&&p!=="ds"&&p!=="3p.ds"||h.push({gclid:m,xd:p});n&&h.push({gclid:n,xd:"ds"});h.length===2&&M(147);h.length===0&&g.wbraid&&
h.push({gclid:g.wbraid,xd:"gb"});h.length===0&&p==="aw.ds"&&h.push({gclid:"",xd:"aw.ds"});Au(function(){var u=$o(Bu());if(u){Ts(a);var v=[],w=u?Rs[Us(a.prefix)]:void 0;w&&v.push("auid="+w);if($o(K.m.W)){e&&v.push("userId="+e);var x=ao(Wn.Qi);if(x===void 0)$n(Wn.Ri,!0);else{var z=ao(Wn.Gf);v.push("ga_uid="+z+"."+x)}}var C=y.referrer?Bk(Hk(y.referrer),"host"):"",E=u||!d?h:[];E.length===0&&(Cu.test(C)||Du.test(C))&&E.push({gclid:"",xd:""});if(E.length!==0||r!==void 0){C&&v.push("ref="+encodeURIComponent(C));
var F=Eu();v.push("url="+encodeURIComponent(F));v.push("tft="+ub());var G=Pc();G!==void 0&&v.push("tfd="+Math.round(G));var J=Al(!0);v.push("frm="+J);r!==void 0&&v.push("gad_source="+encodeURIComponent(r));t!==void 0&&v.push("gad_source_src="+encodeURIComponent(t.toString()));if(!c){var N={};c=$p(Qp(new Pp(0),(N[K.m.Fa]=wq.D[K.m.Fa],N)))}v.push("gtm="+Ar({Oa:b}));nr()&&v.push("gcs="+or());v.push("gcd="+sr(c));vr()&&v.push("dma_cps="+tr());v.push("dma="+ur());mr(c)?v.push("npa=0"):v.push("npa=1");
xr()&&v.push("_ng=1");Rq(Zq())&&v.push("tcfd="+wr());var X=fr();X&&v.push("gdpr="+X);var Q=er();Q&&v.push("gdpr_consent="+Q);D(23)&&v.push("apve=0");D(123)&&Fs(!1)._up&&v.push("gtm_up=1");Zj()&&v.push("tag_exp="+Zj());if(E.length>0)for(var na=0;na<E.length;na++){var S=E[na],aa=S.gclid,Y=S.xd;if(!Fu(a.prefix,Y+"."+aa,w!==void 0)){var V='http://ad.doubleclick.net/pagead/regclk?'+v.join("&");aa!==""?V=Y==="gb"?V+"&wbraid="+aa:V+"&gclid="+aa+"&gclsrc="+Y:Y==="aw.ds"&&(V+="&gclsrc=aw.ds");Ic(V)}}else if(r!==
void 0&&!Fu(a.prefix,"gad",w!==void 0)){var ka='http://ad.doubleclick.net/pagead/regclk?'+v.join("&");Ic(ka)}}}})}},Fu=function(a,b,c){var d=kp("joined_auid",function(){return{}}),e=(c?a||"_gcl":"")+"."+b;if(d[e])return!0;d[e]=!0;return!1},zu=function(){var a=Hk(l.location.href),b=void 0,c=void 0,d=Bk(a,"query",!1,void 0,"gad_source"),e,f=a.hash.replace("#","").match(Hu);e=f?f[1]:void 0;d&&e?(b=d,c=1):d?(b=d,c=2):e&&(b=e,c=3);return{Tf:b,fm:c}},Eu=function(){var a=Al(!1)===1?l.top.location.href:l.location.href;
return a=a.replace(/[\?#].*$/,"")},Iu=function(a){var b=[];nb(a,function(c,d){d=Qt(d);for(var e=[],f=0;f<d.length;f++)e.push(d[f].gclid);e.length&&b.push(c+":"+e.join(","))});return b.join(";")},Ku=function(a,b){return Ju("dc",a,b)},Lu=function(a,b){return Ju("aw",a,b)},Ju=function(a,b,c){if(a==="aw"||a==="dc"||a==="gb"){var d=Jk("gcl"+a);if(d)return d.split(".")}var e=Jt(b);if(e==="_gcl"){var f=!$o(Bu())&&c,g;g=$t()[a]||[];if(g.length>0)return f?["0"]:g}var h=Kt(a,e);return h?Ft(h):[]},Au=function(a){var b=
Bu();cp(function(){a();$o(b)||hn(a,b)},b)},Bu=function(){return[K.m.V,K.m.W]},Cu=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,Du=/^www\.googleadservices\.com$/,Hu=/^gad_source[_=](\d+)$/;function Mu(){return kp("dedupe_gclid",function(){return as()})};var Nu=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,Ou=/^www.googleadservices.com$/;function Pu(a){a||(a=Qu());return a.tq?!1:a.qp||a.rp||a.vp||a.tp||a.Tf||a.Yo||a.up||a.ep?!0:!1}function Qu(){var a={},b=Fs(!0);a.tq=!!b._up;var c=$t();a.qp=c.aw!==void 0;a.rp=c.dc!==void 0;a.vp=c.wbraid!==void 0;a.tp=c.gbraid!==void 0;a.up=c.gclsrc==="aw.ds";a.Tf=zu().Tf;var d=y.referrer?Bk(Hk(y.referrer),"host"):"";a.ep=Nu.test(d);a.Yo=Ou.test(d);return a};function Ru(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function Su(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function Tu(){return["ad_storage","ad_user_data"]}function Uu(a){if(D(38)&&!ao(Wn.yl)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{Ru(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&($n(Wn.yl,function(d){d.gclid&&du(d.gclid,a)}),Su(c)||M(178))})}catch(c){M(177)}};gn(function(){Dt(Tu())?b():hn(b,Tu())},Tu())}};var Vu=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];
function Wu(){if(D(119)){if(ao(Wn.Ef))return M(176),Wn.Ef;if(ao(Wn.Al))return M(170),Wn.Ef;var a=Cl();if(!a)M(171);else if(a.opener){var b=function(e){if(Vu.includes(e.origin)){e.data.action==="gcl_transfer"&&e.data.gadSource?$n(Wn.Ef,{gadSource:e.data.gadSource}):M(173);var f;(f=e.stopImmediatePropagation)==null||f.call(e);Jq(a,"message",b)}else M(172)};if(Iq(a,"message",b)){$n(Wn.Al,!0);for(var c=k(Vu),d=c.next();!d.done;d=c.next())a.opener.postMessage({action:"gcl_setup"},d.value);M(174);return Wn.Ef}M(175)}}}
;var Xu=function(){this.D=this.gppString=void 0};Xu.prototype.reset=function(){this.D=this.gppString=void 0};var Yu=new Xu;var Zu=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),$u=/^~?[\w-]+(?:\.~?[\w-]+)*$/,av=/^\d+\.fls\.doubleclick\.net$/,bv=/;gac=([^;?]+)/,cv=/;gacgb=([^;?]+)/;
function dv(a,b){if(av.test(y.location.host)){var c=y.location.href.match(b);return c&&c.length===2&&c[1].match(Zu)?Ak(c[1])||"":""}for(var d=[],e=k(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function ev(a,b,c){for(var d=Dt(Ct())?et("_gac_gb",!0):{},e=[],f=!1,g=k(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=su("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{Xo:f?e.join(";"):"",Wo:dv(d,cv)}}function fv(a){var b=y.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match($u)?b[1]:void 0}
function gv(a){var b=ng(9),c={},d,e,f;av.test(y.location.host)&&(d=fv("gclgs"),e=fv("gclst"),b&&(f=fv("gcllp")));if(d&&e&&(!b||f))c.xh=d,c.zh=e,c.yh=f;else{var g=ub(),h=Lt((a||"_gcl")+"_gs"),m=h.map(function(q){return q.gclid}),n=h.map(function(q){return g-q.timestamp}),p=[];b&&(p=h.map(function(q){return q.Cd}));m.length>0&&n.length>0&&(!b||p.length>0)&&(c.xh=m.join("."),c.zh=n.join("."),b&&p.length>0&&(c.yh=p.join(".")))}return c}
function hv(a,b,c,d){d=d===void 0?!1:d;if(av.test(y.location.host)){var e=fv(c);if(e){if(d){var f=new ut;vt(f,2);vt(f,3);return e.split(".").map(function(h){return{gclid:h,Aa:f,Ab:[1]}})}return e.split(".").map(function(h){return{gclid:h}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?Wt(g):Gt(g)}if(b==="wbraid")return Gt((a||"_gcl")+"_gb");if(b==="braids")return It({prefix:a})}return[]}function iv(a){return av.test(y.location.host)?!(fv("gclaw")||fv("gac")):wu(a)}
function jv(a,b,c){var d;d=c?tu(a,b):su((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function kv(){var a=l.__uspapi;if(eb(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};
var pv=function(a){if(a.eventName===K.m.qa&&R(a,P.C.da)===L.K.Ia)if(D(24)){T(a,P.C.he,O(a.F,K.m.ya)!=null&&O(a.F,K.m.ya)!==!1&&!$o([K.m.V,K.m.W]));var b=lv(a),c=O(a.F,K.m.Qa)!==!1;c||U(a,K.m.Uh,"1");var d=Jt(b.prefix),e=R(a,P.C.jh);if(!R(a,P.C.ja)&&!R(a,P.C.If)&&!R(a,P.C.fe)){var f=O(a.F,K.m.Db),g=O(a.F,K.m.Ra)||{};mv({me:c,ue:g,xe:f,Qc:b});if(!e&&!xu(d)){a.isAborted=!0;return}}if(e)a.isAborted=!0;else{U(a,K.m.ed,K.m.Vc);if(R(a,P.C.ja))U(a,K.m.ed,K.m.Ym),U(a,K.m.ja,"1");else if(R(a,P.C.If))U(a,K.m.ed,
K.m.mn);else if(R(a,P.C.fe))U(a,K.m.ed,K.m.jn);else{var h=$t();U(a,K.m.Wc,h.gclid);U(a,K.m.bd,h.dclid);U(a,K.m.qk,h.gclsrc);nv(a,K.m.Wc)||nv(a,K.m.bd)||(U(a,K.m.Td,h.wbraid),U(a,K.m.Fe,h.gbraid));U(a,K.m.Wa,y.referrer?Bk(Hk(y.referrer),"host"):"");U(a,K.m.Ba,Eu());if(D(27)&&oc){var m=Bk(Hk(oc),"host");m&&U(a,K.m.Xk,m)}if(!R(a,P.C.fe)){var n=zu(),p=n.fm;U(a,K.m.De,n.Tf);U(a,K.m.Ee,p)}U(a,K.m.Jc,Al(!0));var q=Qu();Pu(q)&&U(a,K.m.gd,"1");U(a,K.m.sk,Mu());Fs(!1)._up==="1"&&U(a,K.m.Nk,"1")}Nn=!0;U(a,K.m.Cb);
U(a,K.m.Mb);var r=$o([K.m.V,K.m.W]);r&&(U(a,K.m.Cb,ov()),c&&(Ts(b),U(a,K.m.Mb,Rs[Us(b.prefix)])));U(a,K.m.kc);U(a,K.m.nb);if(!nv(a,K.m.Wc)&&!nv(a,K.m.bd)&&iv(d)){var t=Ht(b);t.length>0&&U(a,K.m.kc,t.join("."))}else if(!nv(a,K.m.Td)&&r){var u=Ft(d+"_aw");u.length>0&&U(a,K.m.nb,u.join("."))}D(31)&&U(a,K.m.Qk,Qc());a.F.isGtmEvent&&(a.F.D[K.m.Fa]=wq.D[K.m.Fa]);mr(a.F)?U(a,K.m.vc,!1):U(a,K.m.vc,!0);T(a,P.C.pg,!0);var v=kv();v!==void 0&&U(a,K.m.rf,v||"error");var w=fr();w&&U(a,K.m.fd,w);if(D(137))try{var x=
Intl.DateTimeFormat().resolvedOptions().timeZone;U(a,K.m.li,x||"-")}catch(F){U(a,K.m.li,"e")}var z=er();z&&U(a,K.m.kd,z);var C=Yu.gppString;C&&U(a,K.m.We,C);var E=Yu.D;E&&U(a,K.m.Ve,E);T(a,P.C.Ja,!1)}}else a.isAborted=!0},lv=function(a){var b={prefix:O(a.F,K.m.Ob)||O(a.F,K.m.jb),domain:O(a.F,K.m.pb),Bc:O(a.F,K.m.qb),flags:O(a.F,K.m.wb)};a.F.isGtmEvent&&(b.path=O(a.F,K.m.Pb));return b},qv=function(a,b){var c,d,e,f,g,h,m,n;c=a.me;d=a.ue;e=a.xe;f=a.Oa;g=a.F;h=a.ve;m=a.nr;n=a.Im;mv({me:c,ue:d,xe:e,Qc:b});
c&&m!==!0&&(n!=null?n=String(n):n=void 0,Gu(b,f,g,h,n))},rv=function(a,b){if(!R(a,P.C.fe)){var c=Wu();if(c){var d=ao(c),e=function(g){T(a,P.C.fe,!0);var h=nv(a,K.m.De),m=nv(a,K.m.Ee);U(a,K.m.De,String(g.gadSource));U(a,K.m.Ee,6);T(a,P.C.ja);T(a,P.C.If);U(a,K.m.ja);b();U(a,K.m.De,h);U(a,K.m.Ee,m);T(a,P.C.fe,!1)};if(d)e(d);else{var f=void 0;f=bo(c,function(g,h){e(h);co(c,f)})}}}},mv=function(a){var b,c,d,e;b=a.me;c=a.ue;d=a.xe;e=a.Qc;b&&(Ps(c[K.m.Zd],!!c[K.m.la])&&(fu(sv,e),hu(e),bt(e)),Al()!==2?(cu(e),
Uu(e)):au(e),lu(sv,e),mu(e));c[K.m.la]&&(ju(sv,c[K.m.la],c[K.m.Mc],!!c[K.m.rc],e.prefix),ku(c[K.m.la],c[K.m.Mc],!!c[K.m.rc],e.prefix),ct(Us(e.prefix),c[K.m.la],c[K.m.Mc],!!c[K.m.rc],e),ct("FPAU",c[K.m.la],c[K.m.Mc],!!c[K.m.rc],e));d&&(D(101)?ou(tv):ou(uv));qu(uv)},vv=function(a,b,c,d){var e,f,g;e=a.Jm;f=a.callback;g=a.im;if(typeof f==="function")if(e===K.m.nb&&g===void 0){var h=d(b.prefix,c);h.length===0?f(void 0):h.length===1?f(h[0]):f(h)}else e===K.m.Mb?(M(65),Ts(b,!1),f(Rs[Us(b.prefix)])):f(g)},
wv=function(a,b){Array.isArray(b)||(b=[b]);var c=R(a,P.C.da);return b.indexOf(c)>=0},sv=["aw","dc","gb"],uv=["aw","dc","gb","ag"],tv=["aw","dc","gb","ag","gad_source"];function xv(a){var b=O(a.F,K.m.Lc),c=O(a.F,K.m.Kc);b&&!c?(a.eventName!==K.m.qa&&a.eventName!==K.m.Pd&&M(131),a.isAborted=!0):!b&&c&&(M(132),a.isAborted=!0)}function yv(a){var b=$o(K.m.V)?jp.pscdl:"denied";b!=null&&U(a,K.m.Fg,b)}function zv(a){var b=Al(!0);U(a,K.m.Jc,b)}
function Av(a){xr()&&U(a,K.m.Xd,1)}function ov(){var a=y.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&Ak(a.substring(0,b))===void 0;)b--;return Ak(a.substring(0,b))||""}function Bv(a){Cv(a,"ce",O(a.F,K.m.qb))}function Cv(a,b,c){nv(a,K.m.od)||U(a,K.m.od,{});nv(a,K.m.od)[b]=c}function Dv(a){T(a,P.C.Hf,Sm.Z.Ea)}function Ev(a){var b=bb("GTAG_EVENT_FEATURE_CHANNEL");b&&(U(a,K.m.Xe,b),$a())}function Fv(a){var b=a.F.getMergedValues(K.m.qc);b&&U(a,K.m.qc,b)}
function Gv(a,b){b=b===void 0?!1:b;if(D(108)){var c=R(a,P.C.Ff);if(c)if(c.indexOf(a.target.destinationId)<0){if(T(a,P.C.Rj,!1),b||!Hv(a,"custom_event_accept_rules",!1))a.isAborted=!0}else T(a,P.C.Rj,!0)}}function Iv(a){D(166)&&Zk&&(Nn=!0,a.eventName===K.m.qa?Un(a.F,a.target.id):(R(a,P.C.Kd)||(Qn[a.target.id]=!0),rp(R(a,P.C.ab))))};
var Ov=function(a,b){var c=a&&!$o([K.m.V,K.m.W]);return b&&c?"0":b},Rv=function(a){var b=a.Qc===void 0?{}:a.Qc,c=Jt(b.prefix);xu(c)&&cp(function(){function d(x,z,C){var E=$o([K.m.V,K.m.W]),F=m&&E,G=b.prefix||"_gcl",J=Pv(),N=(F?G:"")+"."+($o(K.m.V)?1:0)+"."+($o(K.m.W)?1:0);if(!J[N]){J[N]=!0;var X={},Q=function(ka,ja){if(ja||typeof ja==="number")X[ka]=ja.toString()},na="https://www.google.com";nr()&&(Q("gcs",or()),x&&Q("gcu",1));Q("gcd",sr(h));Zj()&&Q("tag_exp",Zj());if(dn()){Q("rnd",Mu());if((!p||
q&&q!=="aw.ds")&&E){var S=Ft(G+"_aw");Q("gclaw",S.join("."))}Q("url",String(l.location).split(/[?#]/)[0]);Q("dclid",Ov(f,r));E||(na="https://pagead2.googlesyndication.com")}vr()&&Q("dma_cps",tr());Q("dma",ur());Q("npa",mr(h)?0:1);xr()&&Q("_ng",1);Rq(Zq())&&Q("tcfd",wr());Q("gdpr_consent",er()||"");Q("gdpr",fr()||"");Fs(!1)._up==="1"&&Q("gtm_up",1);Q("gclid",Ov(f,p));Q("gclsrc",q);if(!(X.hasOwnProperty("gclid")||X.hasOwnProperty("dclid")||X.hasOwnProperty("gclaw"))&&(Q("gbraid",Ov(f,t)),!X.hasOwnProperty("gbraid")&&
dn()&&E)){var aa=Ft(G+"_gb");aa.length>0&&Q("gclgb",aa.join("."))}Q("gtm",Ar({Oa:h.eventMetadata[P.C.ab],rh:!g}));m&&$o(K.m.V)&&(Ts(b||{}),F&&Q("auid",Rs[Us(b.prefix)]||""));Qv||a.am&&Q("did",a.am);a.mj&&Q("gdid",a.mj);a.jj&&Q("edid",a.jj);a.qj!==void 0&&Q("frm",a.qj);D(23)&&Q("apve","0");var Y=Object.keys(X).map(function(ka){return ka+"="+encodeURIComponent(X[ka])}),V=na+"/pagead/landing?"+Y.join("&");Ic(V);v&&g!==void 0&&Mo({targetId:g,request:{url:V,parameterEncoding:3,endpoint:E?12:13},cb:{eventId:h.eventId,
priorityId:h.priorityId},th:z===void 0?void 0:{eventId:z,priorityId:C}})}}var e=!!a.dj,f=!!a.ve,g=a.targetId,h=a.F,m=a.Bh===void 0?!0:a.Bh,n=$t(),p=n.gclid||"",q=n.gclsrc,r=n.dclid||"",t=n.wbraid||"",u=!e&&((!p||q&&q!=="aw.ds"?!1:!0)||t),v=dn();if(u||v)if(v){var w=[K.m.V,K.m.W,K.m.Na];d();(function(){$o(w)||bp(function(x){d(!0,x.consentEventId,x.consentPriorityId)},w)})()}else d()},[K.m.V,K.m.W,K.m.Na])},Pv=function(){return kp("reported_gclid",function(){return{}})},Qv=!1;function Sv(a,b,c,d){var e=yc(),f;if(e===1)a:{var g=Tj;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=y.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==l.location.protocol?a:b)+c};function dw(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return nv(a,b)},setHitData:function(b,c){U(a,b,c)},setHitDataIfNotDefined:function(b,c){nv(a,b)===void 0&&U(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return R(a,b)},setMetadata:function(b,c){T(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return O(a.F,b)},yb:function(){return a},getHitKeys:function(){return Object.keys(a.D)},getMergedValues:function(b){return a.F.getMergedValues(b,3)}}};var fw=function(a){var b=ew[om?a.target.destinationId:Dm(a.target.destinationId)];if(!a.isAborted&&b)for(var c=dw(a),d=0;d<b.length;++d){try{b[d](c)}catch(e){a.isAborted=!0}if(a.isAborted)break}},gw=function(a,b){var c=ew[a];c||(c=ew[a]=[]);c.push(b)},ew={};function jw(a,b){return arguments.length===1?kw("set",a):kw("set",a,b)}function lw(a,b){return arguments.length===1?kw("config",a):kw("config",a,b)}function mw(a,b,c){c=c||{};c[K.m.hd]=a;return kw("event",b,c)}function kw(){return arguments};var ow=function(){this.messages=[];this.D=[]};ow.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=Object.assign({},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.D.length;g++)try{this.D[g](f)}catch(h){}};ow.prototype.listen=function(a){this.D.push(a)};
ow.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};ow.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function pw(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[P.C.ab]=$f.canonicalContainerId;qw().enqueue(a,b,c)}
function rw(){var a=sw;qw().listen(a)}function qw(){return kp("mb",function(){return new ow})};var tw,uw=!1;function vw(){uw=!0;tw=tw||{}}function ww(a){uw||vw();return tw[a]};function xw(){var a=l.screen;return{width:a?a.width:0,height:a?a.height:0}}
function yw(a){if(y.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!l.getComputedStyle)return!0;var c=l.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=l.getComputedStyle(d,null))}return!1}
var Iw=function(a){return a.tagName+":"+a.isVisible+":"+a.ka.length+":"+Hw.test(a.ka)},Ww=function(a){a=a||{se:!0,te:!0,Jh:void 0};a.Wb=a.Wb||{email:!0,phone:!1,address:!1};var b=Jw(a),c=Kw[b];if(c&&ub()-c.timestamp<200)return c.result;var d=Lw(),e=d.status,f=[],g,h,m=[];if(!D(33)){if(a.Wb&&a.Wb.email){var n=Mw(d.elements);f=Nw(n,a&&a.Pf);g=Ow(f);n.length>10&&(e="3")}!a.Jh&&g&&(f=[g]);for(var p=0;p<f.length;p++)m.push(Pw(f[p],!!a.se,!!a.te));m=m.slice(0,10)}else if(a.Wb){}g&&(h=Pw(g,!!a.se,!!a.te));var F={elements:m,
Gj:h,status:e};Kw[b]={timestamp:ub(),result:F};return F},Xw=function(a,b){if(a){var c=a.trim().replaceAll(/\s+/g,"").replaceAll(/(\d{2,})\./g,"$1").replaceAll(/-/g,"").replaceAll(/\((\d+)\)/g,"$1");if(b&&c.match(/^\+?\d{3,7}$/))return c;c.charAt(0)!=="+"&&(c="+"+c);if(c.match(/^\+\d{10,15}$/))return c}},Zw=function(a){var b=Yw(/^(\w|[- ])+$/)(a);if(!b)return b;var c=b.replaceAll(/[- ]+/g,"");return c.length>10?void 0:c},Yw=function(a){return function(b){var c=b.match(a);return c?c[0].trim().toLowerCase():
void 0}},Vw=function(a,b,c){var d=a.element,e={ka:a.ka,type:a.wa,tagName:d.tagName};b&&(e.querySelector=$w(d));c&&(e.isVisible=!yw(d));return e},Pw=function(a,b,c){return Vw({element:a.element,ka:a.ka,wa:Uw.fc},b,c)},Jw=function(a){var b=!(a==null||!a.se)+"."+!(a==null||!a.te);a&&a.Pf&&a.Pf.length&&(b+="."+a.Pf.join("."));a&&a.Wb&&(b+="."+a.Wb.email+"."+a.Wb.phone+"."+a.Wb.address);return b},Ow=function(a){if(a.length!==0){var b;b=ax(a,function(c){return!bx.test(c.ka)});b=ax(b,function(c){return c.element.tagName.toUpperCase()===
"INPUT"});b=ax(b,function(c){return!yw(c.element)});return b[0]}},Nw=function(a,b){if(!b||b.length===0)return a;for(var c=[],d=0;d<a.length;d++){for(var e=!0,f=0;f<b.length;f++){var g=b[f];if(g&&vi(a[d].element,g)){e=!1;break}}e&&c.push(a[d])}return c},ax=function(a,b){if(a.length<=1)return a;var c=a.filter(b);return c.length===0?a:c},$w=function(a){var b;if(a===y.body)b="body";else{var c;if(a.id)c="#"+a.id;else{var d;if(a.parentElement){var e;a:{var f=a.parentElement;if(f){for(var g=0;g<f.childElementCount;g++)if(f.children[g]===
a){e=g+1;break a}e=-1}else e=1}d=$w(a.parentElement)+">:nth-child("+e.toString()+")"}else d="";c=d}b=c}return b},Mw=function(a){for(var b=[],c=0;c<a.length;c++){var d=a[c],e=d.textContent;d.tagName.toUpperCase()==="INPUT"&&d.value&&(e=d.value);if(e){var f=e.match(cx);if(f){var g=f[0],h;if(l.location){var m=Dk(l.location,"host",!0);h=g.toLowerCase().indexOf(m)>=0}else h=!1;h||b.push({element:d,ka:g})}}}return b},Lw=function(){var a=[],b=y.body;if(!b)return{elements:a,status:"4"};for(var c=b.querySelectorAll("*"),
d=0;d<c.length&&d<1E4;d++){var e=c[d];if(!(dx.indexOf(e.tagName.toUpperCase())>=0)&&e.children instanceof HTMLCollection){for(var f=!1,g=0;g<e.childElementCount&&g<1E4;g++)if(!(ex.indexOf(e.children[g].tagName.toUpperCase())>=0)){f=!0;break}(!f||D(33)&&fx.indexOf(e.tagName)!==-1)&&a.push(e)}}return{elements:a,status:c.length>1E4?"2":"1"}},gx=!1;var cx=/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i,
Hw=/@(gmail|googlemail)\./i,bx=/support|noreply/i,dx="SCRIPT STYLE IMG SVG PATH BR NOSCRIPT TEXTAREA".split(" "),ex=["BR"],hx=kg('',2),Uw={fc:"1",vd:"2",nd:"3",ud:"4",Ae:"5",Df:"6",kh:"7",Ti:"8",Mh:"9",Ki:"10"},Kw={},fx=["INPUT","SELECT"],ix=Yw(/^([^\x00-\x40\x5b-\x60\x7b-\xff]|[.-]|\s)+$/);
var Wf;var Nx=Number('')||5,Ox=Number('')||50,Px=kb();
var Rx=function(a,b){a&&(Qx("sid",a.targetId,b),Qx("cc",a.clientCount,b),Qx("tl",a.totalLifeMs,b),Qx("hc",a.heartbeatCount,b),Qx("cl",a.clientLifeMs,b))},Qx=function(a,b,c){b!=null&&c.push(a+"="+b)},Sx=function(){var a=y.referrer;if(a){var b;return Bk(Hk(a),"host")===((b=l.location)==null?void 0:b.host)?1:2}return 0},Ux=function(){this.T=Tx;this.O=0};Ux.prototype.J=function(a,b,c,d){var e=Sx(),f,g=[];f=l===l.top&&e!==0&&b?(b==null?void 0:b.clientCount)>
1?e===2?1:2:e===2?0:3:4;a&&Qx("si",a.Zf,g);Qx("m",0,g);Qx("iss",f,g);Qx("if",c,g);Rx(b,g);d&&Qx("fm",encodeURIComponent(d.substring(0,Ox)),g);this.R(g);};Ux.prototype.D=function(a,b,c,d,e){var f=[];Qx("m",1,f);Qx("s",a,f);Qx("po",Sx(),f);b&&(Qx("st",b.state,f),Qx("si",b.Zf,f),Qx("sm",b.lg,f));Rx(c,f);Qx("c",d,f);e&&Qx("fm",encodeURIComponent(e.substring(0,Ox)),f);this.R(f);};
Ux.prototype.R=function(a){a=a===void 0?[]:a;!Yk||this.O>=Nx||(Qx("pid",Px,a),Qx("bc",++this.O,a),a.unshift("ctid="+$f.ctid+"&t=s"),this.T("https://www.googletagmanager.com/a?"+a.join("&")))};var Vx=Number('')||500,Wx=Number('')||5E3,Xx=Number('20')||10,Yx=Number('')||5E3;function Zx(a){return a.performance&&a.performance.now()||Date.now()}
var $x=function(a,b){var c;var d=function(e,f,g){g=g===void 0?{lm:function(){},om:function(){},km:function(){},onFailure:function(){}}:g;this.po=e;this.D=f;this.O=g;this.fa=this.ma=this.heartbeatCount=this.oo=0;this.mh=!1;this.J={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.Zf=Zx(this.D);this.lg=Zx(this.D);this.T=10};d.prototype.init=function(){this.R(1);this.Ca()};d.prototype.getState=function(){return{state:this.state,
Zf:Math.round(Zx(this.D)-this.Zf),lg:Math.round(Zx(this.D)-this.lg)}};d.prototype.R=function(e){this.state!==e&&(this.state=e,this.lg=Zx(this.D))};d.prototype.Pl=function(){return String(this.oo++)};d.prototype.Ca=function(){var e=this;this.heartbeatCount++;this.Za({type:0,clientId:this.id,requestId:this.Pl(),maxDelay:this.oh()},function(f){if(f.type===0){var g;if(((g=f.failure)==null?void 0:g.failureType)!=null)if(f.stats&&(e.stats=f.stats),e.fa++,f.isDead||e.fa>Xx){var h=f.isDead&&f.failure.failureType;
e.T=h||10;e.R(4);e.mo();var m,n;(n=(m=e.O).km)==null||n.call(m,{failureType:h||10,data:f.failure.data})}else e.R(3),e.Rl();else{if(e.heartbeatCount>f.stats.heartbeatCount+Xx){e.heartbeatCount=f.stats.heartbeatCount;var p,q;(q=(p=e.O).onFailure)==null||q.call(p,{failureType:13})}e.stats=f.stats;var r=e.state;e.R(2);if(r!==2)if(e.mh){var t,u;(u=(t=e.O).om)==null||u.call(t)}else{e.mh=!0;var v,w;(w=(v=e.O).lm)==null||w.call(v)}e.fa=0;e.qo();e.Rl()}}})};d.prototype.oh=function(){return this.state===2?
Wx:Vx};d.prototype.Rl=function(){var e=this;this.D.setTimeout(function(){e.Ca()},Math.max(0,this.oh()-(Zx(this.D)-this.ma)))};d.prototype.uo=function(e,f,g){var h=this;this.Za({type:1,clientId:this.id,requestId:this.Pl(),command:e},function(m){if(m.type===1)if(m.result)f(m.result);else{var n,p,q,r={failureType:(q=(n=m.failure)==null?void 0:n.failureType)!=null?q:12,data:(p=m.failure)==null?void 0:p.data},t,u;(u=(t=h.O).onFailure)==null||u.call(t,r);g(r)}})};d.prototype.Za=function(e,f){var g=this;
if(this.state===4)e.failure={failureType:this.T},f(e);else{var h=this.state!==2&&e.type!==0,m=e.requestId,n,p=this.D.setTimeout(function(){var r=g.J[m];r&&g.Bf(r,7)},(n=e.maxDelay)!=null?n:Yx),q={request:e,Am:f,vm:h,Ip:p};this.J[m]=q;h||this.sendRequest(q)}};d.prototype.sendRequest=function(e){this.ma=Zx(this.D);e.vm=!1;this.po(e.request)};d.prototype.qo=function(){for(var e=k(Object.keys(this.J)),f=e.next();!f.done;f=e.next()){var g=this.J[f.value];g.vm&&this.sendRequest(g)}};d.prototype.mo=function(){for(var e=
k(Object.keys(this.J)),f=e.next();!f.done;f=e.next())this.Bf(this.J[f.value],this.T)};d.prototype.Bf=function(e,f){this.Fb(e);var g=e.request;g.failure={failureType:f};e.Am(g)};d.prototype.Fb=function(e){delete this.J[e.request.requestId];this.D.clearTimeout(e.Ip)};d.prototype.op=function(e){this.ma=Zx(this.D);var f=this.J[e.requestId];if(f)this.Fb(f),f.Am(e);else{var g,h;(h=(g=this.O).onFailure)==null||h.call(g,{failureType:14})}};c=new d(a,l,b);return c};var ay;
var by=function(){ay||(ay=new Ux);return ay},Tx=function(a){qn(sn(Sm.Z.Oc),function(){Bc(a)})},cy=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},dy=function(a){var b=a,c=Fj.Ca;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},ey=function(a){var b=ao(Wn.Hl);return b&&b[a]},fy=function(a,
b,c,d,e){var f=this;this.J=d;this.T=this.R=!1;this.fa=null;this.initTime=c;this.D=15;this.O=this.Fo(a);l.setTimeout(function(){f.initialize()},1E3);A(function(){f.zp(a,b,e)})};ba=fy.prototype;ba.delegate=function(a,b,c){this.getState()!==2?(this.J.D(this.D,{state:this.getState(),Zf:this.initTime,lg:Math.round(ub())-this.initTime},void 0,a.commandType),c({failureType:this.D})):this.O.uo(a,b,c)};ba.getState=function(){return this.O.getState().state};ba.zp=function(a,b,c){var d=l.location.origin,e=this,
f=zc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?cy(h):"",p;D(133)&&(p={sandbox:"allow-same-origin allow-scripts"});zc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.fa=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.O.op(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.D=11,this.J.J(void 0,void 0,this.D,r.toString())}};ba.Fo=function(a){var b=this,c=$x(function(d){var e;(e=b.fa)==null||e.postMessage(d,a.origin)},{lm:function(){b.R=!0;b.J.J(c.getState(),c.stats)},om:function(){},km:function(d){b.R?(b.D=(d==null?void 0:d.failureType)||10,b.J.D(b.D,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.D=(d==null?void 0:
d.failureType)||4,b.J.J(c.getState(),c.stats,b.D,d==null?void 0:d.data))},onFailure:function(d){b.D=d.failureType;b.J.D(b.D,c.getState(),c.stats,d.command,d.data)}});return c};ba.initialize=function(){this.T||this.O.init();this.T=!0};function gy(){var a=Zf(Wf.D,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function hy(a,b){var c=Math.round(ub());b=b===void 0?!1:b;var d=l.location.origin;if(!d||!gy()||D(168))return;bk()&&(a=""+d+ak()+"/_/service_worker");var e=dy(a);if(e===null||ey(e.origin))return;if(!mc()){by().J(void 0,void 0,6);return}var f=new fy(e,!!a,c||Math.round(ub()),by(),b),g;a:{var h=Wn.Hl,m={},n=Zn(h);if(!n){n=Zn(h,!0);if(!n){g=void 0;break a}n.set(m)}g=n.get()}g[e.origin]=f;}
var iy=function(a,b,c,d){var e;if((e=ey(a))==null||!e.delegate){var f=mc()?16:6;by().D(f,void 0,void 0,b.commandType);d({failureType:f});return}ey(a).delegate(b,c,d);};
function jy(a,b,c,d,e){var f=dy();if(f===null){d(mc()?16:6);return}var g,h=(g=ey(f.origin))==null?void 0:g.initTime,m=Math.round(ub()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);iy(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function ky(a,b,c,d){var e=dy(a);if(e===null){d("_is_sw=f"+(mc()?16:6)+"te");return}var f=b?1:0,g=Math.round(ub()),h,m=(h=ey(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;D(169)&&(p=!0);iy(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:l.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,u=(t=ey(e.origin))==
null?void 0:t.getState();u!==void 0&&(r+="s"+u);d(n?r+("t"+n):r+"te")});};function ly(a){if(D(10)||bk()||Fj.J||Pk(a.F)||D(168))return;hy(void 0,D(131));};var my="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function ny(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function oy(){var a=l.google_tag_data,b;if(a!=null&&a.uach){var c=a.uach,d=Object.assign({},c);c.fullVersionList&&(d.fullVersionList=c.fullVersionList.slice(0));b=d}else b=null;return b}function py(){var a,b;return(b=(a=l.google_tag_data)==null?void 0:a.uach_promise)!=null?b:null}
function qy(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function ry(){var a=l;if(!qy(a))return null;var b=ny(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(my).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var ty=function(a,b){if(a)for(var c=sy(a),d=k(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;U(b,f,c[f])}},sy=function(a){var b={};b[K.m.ff]=a.architecture;b[K.m.hf]=a.bitness;a.fullVersionList&&(b[K.m.jf]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[K.m.kf]=a.mobile?"1":"0";b[K.m.lf]=a.model;b[K.m.nf]=a.platform;b[K.m.pf]=a.platformVersion;b[K.m.qf]=a.wow64?"1":"0";return b},uy=function(a){var b=0,c=function(g,
h){try{a(g,h)}catch(m){}},d=oy();if(d)c(d);else{var e=py();if(e){b=Math.min(Math.max(isFinite(b)?b:0,0),1E3);var f=l.setTimeout(function(){c.cg||(c.cg=!0,M(106),c(null,Error("Timeout")))},b);e.then(function(g){c.cg||(c.cg=!0,M(104),l.clearTimeout(f),c(g))}).catch(function(g){c.cg||(c.cg=!0,M(105),l.clearTimeout(f),c(null,g))})}else c(null)}},wy=function(){if(qy(l)&&(vy=ub(),!py())){var a=ry();a&&(a.then(function(){M(95)}),a.catch(function(){M(96)}))}},vy;function xy(a){var b=a.location.href;if(a===a.top)return{url:b,Ep:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,Ep:c}};function oz(a,b){var c=!!bk();switch(a){case 45:return!c||D(76)||D(173)?"https://www.google.com/ccm/collect":ak()+"/g/ccm/collect";case 46:return c&&!D(173)?ak()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return!c||D(173)||D(80)?"https://www.google.com/travel/flights/click/conversion":ak()+"/travel/flights/click/conversion";case 9:return D(173)||D(77)||!c?"https://googleads.g.doubleclick.net/pagead/viewthroughconversion":ak()+"/pagead/viewthroughconversion";case 17:return c?
D(187)?lz()?mz():""+ak()+"/ag/g/c":lz().toLowerCase()==="region1"?""+ak()+"/r1ag/g/c":""+ak()+"/ag/g/c":mz();case 16:if(c){if(D(187))return lz()?nz():""+ak()+"/ga/g/c";var d=D(179)&&lz().toLowerCase()==="region1"?"/r1ga/g/c":"/ga/g/c";return""+ak()+d}return nz();case 1:return D(173)||D(81)||!c?"https://ad.doubleclick.net/activity;":ak()+"/activity;";case 2:return!c||D(173)||D(173)?"https://ade.googlesyndication.com/ddm/activity/":ak()+"/ddm/activity/";case 33:return D(173)||D(81)||!c?"https://ad.doubleclick.net/activity;register_conversion=1;":
ak()+"/activity;register_conversion=1;";case 11:return!D(173)&&c?D(79)?ak()+"/d/pagead/form-data":ak()+"/pagead/form-data":D(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return D(173)||D(81)||!c?"https://"+b.Ul+".fls.doubleclick.net/activityi;":ak()+"/activityi/"+b.Ul+";";case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return!D(173)&&c?ak()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";case 8:return"https://www.google.com/pagead/1p-conversion";
case 22:return D(180)?c&&b.zd?ak()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion":c?b.zd?ak()+"/as/d/ccm/conversion":ak()+"/as/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?ak()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";case 23:return D(180)?c&&b.zd?ak()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion":c?b.zd?ak()+"/g/d/ccm/conversion":ak()+"/g/ccm/conversion":"https://www.google.com/ccm/conversion";
case 21:return D(180)?c&&b.zd?ak()+"/d/ccm/form-data":D(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data":c?b.zd?ak()+"/d/ccm/form-data":ak()+"/ccm/form-data":D(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 12:case 13:case 14:case 15:case 18:case 19:case 20:case 24:case 25:case 26:case 27:case 28:case 29:case 30:case 31:case 32:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 42:case 43:case 44:case 47:case 48:case 49:case 50:case 52:case 53:case 54:case 55:case 56:case 57:case 58:case 59:case 0:throw Error("Unsupported endpoint");
default:cc(a,"Unknown endpoint")}};function pz(a){a=a===void 0?[]:a;return Gj(a).join("~")}function qz(){if(!D(118))return"";var a,b;return(((a=Bm(Cm()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};
var sz=function(a,b){for(var c={},d=function(p,q){var r;r=q===!0?"1":q===!1?"0":encodeURIComponent(String(q));c[p]=r},e=k(Object.keys(a.D)),f=e.next();!f.done;f=e.next()){var g=f.value,h=nv(a,g),m=rz[g];m&&h!==void 0&&h!==""&&(!R(a,P.C.he)||g!==K.m.Wc&&g!==K.m.bd&&g!==K.m.Td&&g!==K.m.Fe||(h="0"),d(m,h))}d("gtm",Ar({Oa:R(a,P.C.ab)}));nr()&&d("gcs",or());d("gcd",sr(a.F));vr()&&d("dma_cps",tr());d("dma",ur());Rq(Zq())&&d("tcfd",wr());pz()&&d("tag_exp",pz());qz()&&d("ptag_exp",qz());if(R(a,P.C.pg)){d("tft",
ub());var n=Pc();n!==void 0&&d("tfd",Math.round(n))}D(24)&&d("apve","1");(D(25)||D(26))&&d("apvf",Mc()?D(26)?"f":"sb":"nf");kn[Sm.Z.Ea]!==Rm.Ka.ee||nn[Sm.Z.Ea].isConsentGranted()||(c.limited_ads="1");b(c)},tz=function(a,b,c){var d=b.F;Mo({targetId:b.target.destinationId,request:{url:a,parameterEncoding:2,endpoint:c},cb:{eventId:d.eventId,priorityId:d.priorityId},th:{eventId:R(b,P.C.ye),priorityId:R(b,P.C.ze)}})},uz=function(a,b,c){var d={destinationId:b.target.destinationId,endpoint:c,eventId:b.F.eventId,
priorityId:b.F.priorityId};tz(a,b,c);am(d,a,void 0,{Gh:!0,method:"GET"},function(){},function(){$l(d,a+"&img=1")})},vz=function(a){var b=tc()||rc()?"www.google.com":"www.googleadservices.com",c=[];nb(a,function(d,e){d==="dl"?c.push("url="+e):d==="dr"?c.push("ref="+e):d==="uid"?c.push("userId="+e):c.push(d+"="+e)});return"https://"+b+"/pagead/set_partitioned_cookie?"+c.join("&")},wz=function(a){sz(a,function(b){if(R(a,P.C.da)===L.K.Ia){var c=[];D(28)&&a.target.destinationId&&c.push("tid="+a.target.destinationId);
nb(b,function(r,t){c.push(r+"="+t)});var d=$o([K.m.V,K.m.W])?45:46,e=oz(d)+"?"+c.join("&");tz(e,a,d);var f=a.F,g={destinationId:a.target.destinationId,endpoint:d,eventId:f.eventId,priorityId:f.priorityId};if(D(26)&&Mc()){am(g,e,void 0,{Gh:!0},function(){},function(){$l(g,e+"&img=1")});var h=$o([K.m.V,K.m.W]),m=nv(a,K.m.gd)==="1",n=nv(a,K.m.Uh)==="1";if(h&&m&&!n){var p=vz(b),q=tc()||rc()?58:57;uz(p,a,q)}}else Zl(g,e)||$l(g,e+"&img=1");if(eb(a.F.onSuccess))a.F.onSuccess()}})},xz={},rz=(xz[K.m.ja]="gcu",
xz[K.m.kc]="gclgb",xz[K.m.nb]="gclaw",xz[K.m.De]="gad_source",xz[K.m.Ee]="gad_source_src",xz[K.m.Wc]="gclid",xz[K.m.qk]="gclsrc",xz[K.m.Fe]="gbraid",xz[K.m.Td]="wbraid",xz[K.m.Mb]="auid",xz[K.m.sk]="rnd",xz[K.m.Uh]="ncl",xz[K.m.Yh]="gcldc",xz[K.m.bd]="dclid",xz[K.m.Qb]="edid",xz[K.m.ed]="en",xz[K.m.fd]="gdpr",xz[K.m.Rb]="gdid",xz[K.m.Xd]="_ng",xz[K.m.Ve]="gpp_sid",xz[K.m.We]="gpp",xz[K.m.Xe]="_tu",xz[K.m.Nk]="gtm_up",xz[K.m.Jc]="frm",xz[K.m.gd]="lps",xz[K.m.Rg]="did",xz[K.m.Qk]="navt",xz[K.m.Ba]=
"dl",xz[K.m.Wa]="dr",xz[K.m.Cb]="dt",xz[K.m.Xk]="scrsrc",xz[K.m.df]="ga_uid",xz[K.m.kd]="gdpr_consent",xz[K.m.li]="u_tz",xz[K.m.Sa]="uid",xz[K.m.rf]="us_privacy",xz[K.m.vc]="npa",xz);var yz={};yz.P=Cr.P;var zz={Pq:"L",ko:"S",gr:"Y",xq:"B",Iq:"E",Mq:"I",Zq:"TC",Lq:"HTC"},Az={ko:"S",Hq:"V",Bq:"E",Yq:"tag"},Bz={},Cz=(Bz[yz.P.Vi]="6",Bz[yz.P.Wi]="5",Bz[yz.P.Ui]="7",Bz);function Dz(){function a(c,d){var e=bb(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var Ez=!1;function Uz(a){}
function Vz(a){}function Wz(){}
function Xz(a){}function Yz(a){}
function Zz(a){}
function $z(){}
function aA(a,b){}
function bA(a,b,c){}
function cA(){};var dA=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function eA(a,b,c,d,e,f,g){var h=Object.assign({},dA);c&&(h.body=c,h.method="POST");Object.assign(h,e);l.fetch(b,h).then(function(m){if(!m.ok)g==null||g();else if(m.body){var n=m.body.getReader(),p=new TextDecoder;return new Promise(function(q){function r(){n.read().then(function(t){var u;u=t.done;var v=p.decode(t.value,{stream:!u});fA(d,v);u?(f==null||f(),q()):r()}).catch(function(){q()})}r()})}}).catch(function(){g?g():D(128)&&(b+="&_z=retryFetch",c?Zl(a,b,c):Yl(a,b))})};var gA=function(a){this.R=a;this.D=""},hA=function(a,b){a.J=b;return a},iA=function(a,b){a.O=b;return a},fA=function(a,b){b=a.D+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=k(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}jA(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.D=b},kA=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};jA(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},jA=function(a,b){b&&(lA(b.send_pixel,b.options,a.R),lA(b.create_iframe,b.options,a.J),lA(b.fetch,b.options,a.O))};function mA(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function lA(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=bd(b)?b:{},f=k(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};function bB(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};function cB(a,b,c){c=c===void 0?!1:c;dB().addRestriction(0,a,b,c)}function eB(a,b,c){c=c===void 0?!1:c;dB().addRestriction(1,a,b,c)}function fB(){var a=zm();return dB().getRestrictions(1,a)}var gB=function(){this.container={};this.D={}},hB=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
gB.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.D[b]){var e=hB(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
gB.prototype.getRestrictions=function(a,b){var c=hB(this,b);if(a===0){var d,e;return[].concat(ua((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),ua((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(ua((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),ua((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
gB.prototype.getExternalRestrictions=function(a,b){var c=hB(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};gB.prototype.removeExternalRestrictions=function(a){var b=hB(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.D[a]=!0};function dB(){return kp("r",function(){return new gB})};var iB=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),jB={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},kB={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},lB="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function mB(){var a=hk("gtm.allowlist")||hk("gtm.whitelist");a&&M(9);Pj&&(a=["google","gtagfl","lcl","zone"],D(48)&&a.push("cmpPartners"));iB.test(l.location&&l.location.hostname)&&(Pj?M(116):(M(117),nB&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&yb(rb(a),jB),c=hk("gtm.blocklist")||hk("gtm.blacklist");c||(c=hk("tagTypeBlacklist"))&&M(3);c?M(8):c=[];iB.test(l.location&&l.location.hostname)&&(c=rb(c),c.push("nonGooglePixels","nonGoogleScripts",
"sandboxedScripts"));rb(c).indexOf("google")>=0&&M(2);var d=c&&yb(rb(c),kB),e={};return function(f){var g=f&&f[We.Ha];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=Xj[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(D(48)&&Pj&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){M(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=lb(d,h||
[]);t&&M(10);q=t}}var u=!m||q;!u&&(h.indexOf("sandboxedScripts")===-1?0:D(48)&&Pj&&h.indexOf("cmpPartners")>=0?!oB():b&&b.indexOf("sandboxedScripts")!==-1?0:lb(d,lB))&&(u=!0);return e[g]=u}}function oB(){var a=Zf(Wf.D,xm(),function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var nB=!1;nB=!0;
function pB(){om&&cB(zm(),function(a){var b=Hf(a.entityId),c;if(Kf(b)){var d=b[We.Ha];if(!d)throw Error("Error: No function name given for function call.");var e=yf[d];c=!!e&&!!e.runInSiloedMode}else c=!!bB(b[We.Ha],4);return c})};function qB(a,b,c,d,e){if(!rB()){var f=d.siloed?um(a):a;if(!Im(f)){d.loadExperiments=Hj();Km(f,d,e);var g=sB(a),h=function(){km().container[f]&&(km().container[f].state=3);tB()},m={destinationId:f,endpoint:0};if(bk())bm(m,ak()+"/"+g,void 0,h);else{var n=zb(a,"GTM-"),p=Ok(),q=c?"/gtag/js":"/gtm.js",r=Nk(b,q+g);if(!r){var t=Jj.xg+q;p&&oc&&n&&(t=oc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);r=Sv("https://","http://",t+g)}bm(m,r,void 0,h)}}}}
function tB(){Mm()||nb(Nm(),function(a,b){uB(a,b.transportUrl,b.context);M(92)})}
function uB(a,b,c,d){if(!rB()){var e=c.siloed?um(a):a;if(!Jm(e))if(c.loadExperiments||(c.loadExperiments=Hj()),Mm()){var f;(f=km().destination)[e]!=null||(f[e]={state:0,transportUrl:b,context:c,parent:Cm()});km().destination[e].state=0;jm({ctid:e,isDestination:!0},d);M(91)}else{c.siloed&&Lm({ctid:e,isDestination:!0});var g;(g=km().destination)[e]!=null||(g[e]={context:c,state:1,parent:Cm()});km().destination[e].state=1;jm({ctid:e,isDestination:!0},d);var h={destinationId:e,endpoint:0};if(bk())bm(h,
ak()+("/gtd"+sB(a,!0)));else{var m="/gtag/destination"+sB(a,!0),n=Nk(b,m);n||(n=Sv("https://","http://",Jj.xg+m));bm(h,n)}}}}function sB(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);Jj.Kb!=="dataLayer"&&(c+="&l="+Jj.Kb);if(!zb(a,"GTM-")||b)c=D(130)?c+(bk()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+Ar();Ok()&&(c+="&sign="+Jj.Pi);var d=Fj.O;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");Hj().join("~")&&(c+="&tag_exp="+Hj().join("~"));return c}
function rB(){if(yr()){return!0}return!1};var vB=function(){this.J=0;this.D={}};vB.prototype.addListener=function(a,b,c){var d=++this.J;this.D[a]=this.D[a]||{};this.D[a][String(d)]={listener:b,ac:c};return d};vB.prototype.removeListener=function(a,b){var c=this.D[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var xB=function(a,b){var c=[];nb(wB.D[a],function(d,e){c.indexOf(e.listener)<0&&(e.ac===void 0||b.indexOf(e.ac)>=0)&&c.push(e.listener)});return c};function yB(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:xm()}};var AB=function(a,b){this.D=!1;this.R=[];this.eventData={tags:[]};this.T=!1;this.J=this.O=0;zB(this,a,b)},BB=function(a,b,c,d){if(Lj.hasOwnProperty(b)||b==="__zone")return-1;var e={};bd(d)&&(e=cd(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},CB=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},DB=function(a){if(!a.D){for(var b=a.R,c=0;c<b.length;c++)b[c]();a.D=!0;a.R.length=0}},zB=function(a,b,c){b!==void 0&&a.Jf(b);c&&l.setTimeout(function(){DB(a)},
Number(c))};AB.prototype.Jf=function(a){var b=this,c=wb(function(){A(function(){a(xm(),b.eventData)})});this.D?c():this.R.push(c)};var EB=function(a){a.O++;return wb(function(){a.J++;a.T&&a.J>=a.O&&DB(a)})},FB=function(a){a.T=!0;a.J>=a.O&&DB(a)};var GB={};function HB(){return l[IB()]}
function IB(){return l.GoogleAnalyticsObject||"ga"}function LB(){var a=xm();}
function MB(a,b){return function(){var c=HB(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var SB=["es","1"],TB={},UB={};function VB(a,b){if(Yk){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";TB[a]=[["e",c],["eid",a]];oq(a)}}function WB(a){var b=a.eventId,c=a.Id;if(!TB[b])return[];var d=[];UB[b]||d.push(SB);d.push.apply(d,ua(TB[b]));c&&(UB[b]=!0);return d};var XB={},YB={},ZB={};function $B(a,b,c,d){Yk&&D(120)&&((d===void 0?0:d)?(ZB[b]=ZB[b]||0,++ZB[b]):c!==void 0?(YB[a]=YB[a]||{},YB[a][b]=Math.round(c)):(XB[a]=XB[a]||{},XB[a][b]=(XB[a][b]||0)+1))}function aC(a){var b=a.eventId,c=a.Id,d=XB[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete XB[b];return e.length?[["md",e.join(".")]]:[]}
function bC(a){var b=a.eventId,c=a.Id,d=YB[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete YB[b];return e.length?[["mtd",e.join(".")]]:[]}function cC(){for(var a=[],b=k(Object.keys(ZB)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+ZB[d])}return a.length?[["mec",a.join(".")]]:[]};var dC={},eC={};function fC(a,b,c){if(Yk&&b){var d=Tk(b);dC[a]=dC[a]||[];dC[a].push(c+d);var e=(Kf(b)?"1":"2")+d;eC[a]=eC[a]||[];eC[a].push(e);oq(a)}}function gC(a){var b=a.eventId,c=a.Id,d=[],e=dC[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=eC[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete dC[b],delete eC[b]);return d};function hC(a,b,c,d){var e=wf[a],f=iC(a,b,c,d);if(!f)return null;var g=Lf(e[We.Il],c,[]);if(g&&g.length){var h=g[0];f=hC(h.index,{onSuccess:f,onFailure:h.dm===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function iC(a,b,c,d){function e(){function w(){Vn(3);var J=ub()-G;fC(c.id,f,"7");CB(c.Pc,E,"exception",J);D(109)&&bA(c,f,yz.P.Ui);F||(F=!0,h())}if(f[We.bo])h();else{var x=Jf(f,c,[]),z=x[We.Om];if(z!=null)for(var C=0;C<z.length;C++)if(!$o(z[C])){h();return}var E=BB(c.Pc,String(f[We.Ha]),Number(f[We.ph]),x[We.METADATA]),F=!1;x.vtp_gtmOnSuccess=function(){if(!F){F=!0;var J=ub()-G;fC(c.id,wf[a],"5");CB(c.Pc,E,"success",J);D(109)&&bA(c,f,yz.P.Wi);g()}};x.vtp_gtmOnFailure=function(){if(!F){F=!0;var J=ub()-
G;fC(c.id,wf[a],"6");CB(c.Pc,E,"failure",J);D(109)&&bA(c,f,yz.P.Vi);h()}};x.vtp_gtmTagId=f.tag_id;x.vtp_gtmEventId=c.id;c.priorityId&&(x.vtp_gtmPriorityId=c.priorityId);fC(c.id,f,"1");D(109)&&aA(c,f);var G=ub();try{Mf(x,{event:c,index:a,type:1})}catch(J){w(J)}D(109)&&bA(c,f,yz.P.Ml)}}var f=wf[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Lf(f[We.Nl],c,[]);if(n&&n.length){var p=n[0],q=hC(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.dm===
2?m:q}if(f[We.zl]||f[We.eo]){var r=f[We.zl]?xf:c.mq,t=g,u=h;if(!r[a]){var v=jC(a,r,wb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function jC(a,b,c){var d=[],e=[];b[a]=kC(d,e,c);return{onSuccess:function(){b[a]=lC;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=mC;for(var f=0;f<e.length;f++)e[f]()}}}function kC(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function lC(a){a()}function mC(a,b){b()};var pC=function(a,b){for(var c=[],d=0;d<wf.length;d++)if(a[d]){var e=wf[d];var f=EB(b.Pc);try{var g=hC(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[We.Ha];if(!h)throw Error("Error: No function name given for function call.");var m=yf[h];c.push({Em:d,priorityOverride:(m?m.priorityOverride||0:0)||bB(e[We.Ha],1)||0,execute:g})}else nC(d,b),f()}catch(p){f()}}c.sort(oC);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function qC(a,b){if(!wB)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=xB(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=EB(b);try{d[e](a,f)}catch(g){f()}}return!0}function oC(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.Em,h=b.Em;f=g>h?1:g<h?-1:0}return f}
function nC(a,b){if(Yk){var c=function(d){var e=b.isBlocked(wf[d])?"3":"4",f=Lf(wf[d][We.Il],b,[]);f&&f.length&&c(f[0].index);fC(b.id,wf[d],e);var g=Lf(wf[d][We.Nl],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var rC=!1,wB;function sC(){wB||(wB=new vB);return wB}
function tC(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(D(109)){}if(d==="gtm.js"){if(rC)return!1;rC=!0}var e=!1,f=fB(),g=cd(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}VB(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:uC(g,e),mq:[],logMacroError:function(){M(6);Vn(0)},cachedModelValues:vC(),Pc:new AB(function(){if(D(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};D(120)&&Yk&&(n.reportMacroDiscrepancy=$B);D(109)&&Yz(n.id);var p=Rf(n);D(109)&&Zz(n.id);e&&(p=wC(p));D(109)&&Xz(b);var q=pC(p,n),r=qC(a,n.Pc);FB(n.Pc);d!=="gtm.js"&&d!=="gtm.sync"||LB();return xC(p,q)||r}function vC(){var a={};a.event=mk("event",1);a.ecommerce=mk("ecommerce",1);a.gtm=mk("gtm");a.eventModel=mk("eventModel");return a}
function uC(a,b){var c=mB();return function(d){if(c(d))return!0;var e=d&&d[We.Ha];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=zm();f=dB().getRestrictions(0,g);var h=a;b&&(h=cd(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=Xj[e]||[],n=k(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function wC(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(wf[c][We.Ha]);if(Kj[d]||wf[c][We.fo]!==void 0||bB(d,2))b[c]=!0}return b}function xC(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&wf[c]&&!Lj[String(wf[c][We.Ha])])return!0;return!1};function yC(){sC().addListener("gtm.init",function(a,b){Fj.fa=!0;En();b()})};var zC=!1,AC=0,BC=[];function CC(a){if(!zC){var b=y.createEventObject,c=y.readyState==="complete",d=y.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){zC=!0;for(var e=0;e<BC.length;e++)A(BC[e])}BC.push=function(){for(var f=ya.apply(0,arguments),g=0;g<f.length;g++)A(f[g]);return 0}}}function DC(){if(!zC&&AC<140){AC++;try{var a,b;(b=(a=y.documentElement).doScroll)==null||b.call(a,"left");CC()}catch(c){l.setTimeout(DC,50)}}}
function EC(){zC=!1;AC=0;if(y.readyState==="interactive"&&!y.createEventObject||y.readyState==="complete")CC();else{Cc(y,"DOMContentLoaded",CC);Cc(y,"readystatechange",CC);if(y.createEventObject&&y.documentElement.doScroll){var a=!0;try{a=!l.frameElement}catch(b){}a&&DC()}Cc(l,"load",CC)}}function FC(a){zC?a():BC.push(a)};var GC={},HC={};function IC(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={Fj:void 0,nj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.Fj=vp(g,b),e.Fj){var h=pm?pm:wm();jb(h,function(r){return function(t){return r.Fj.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=GC[g]||[];e.nj={};m.forEach(function(r){return function(t){r.nj[t]=!0}}(e));for(var n=sm(),p=0;p<n.length;p++)if(e.nj[n[p]]){c=c.concat(vm());break}var q=HC[g]||[];q.length&&(c=c.concat(q))}}return{yj:c,Kp:d}}
function JC(a){nb(GC,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function KC(a){nb(HC,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var LC=!1,MC=!1;function NC(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=cd(b,null),b[K.m.Te]&&(d.eventCallback=b[K.m.Te]),b[K.m.Mg]&&(d.eventTimeout=b[K.m.Mg]));return d}function OC(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:op()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function PC(a,b){var c=a&&a[K.m.hd];c===void 0&&(c=hk(K.m.hd,2),c===void 0&&(c="default"));if(gb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?gb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=IC(d,b.isGtmEvent),f=e.yj,g=e.Kp;if(g.length)for(var h=QC(a),m=0;m<g.length;m++){var n=vp(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q;if(!(q=zb(p,"siloed_"))){var r=n.destinationId,t=km().destination[r];q=!!t&&t.state===0}q||uB(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var u=
f.concat(g);return{yj:wp(f,b.isGtmEvent),vo:wp(u,b.isGtmEvent)}}}var RC=void 0,SC=void 0;function TC(a,b,c){var d=cd(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&M(136);var e=cd(b,null);cd(c,e);pw(lw(sm()[0],e),a.eventId,d)}function QC(a){for(var b=k([K.m.jd,K.m.uc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||wq.D[d];if(e)return e}}
var UC={config:function(a,b){var c=OC(a,b);if(!(a.length<2)&&gb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!bd(a[2])||a.length>3)return;d=a[2]}var e=vp(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!nm.Af){var m=Bm(Cm());if(Om(m)){var n=m.parent,p=n.isDestination;h={Np:Bm(n),Hp:p};break a}}h=void 0}var q=h;q&&(f=q.Np,g=q.Hp);VB(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?vm().indexOf(r)===-1:sm().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[K.m.Lc]){var u=QC(d);if(t)uB(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;RC?TC(b,v,RC):SC||(SC=cd(v,null))}else qB(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(M(128),g&&M(130),b.inheritParentConfig)){var w;var x=d;SC?(TC(b,SC,x),w=!1):(!x[K.m.ld]&&Nj&&RC||(RC=cd(x,null)),w=!0);w&&f.containers&&f.containers.join(",");return}Zk&&(qp===1&&(wn.mcc=!1),qp=2);if(Nj&&!t&&!d[K.m.ld]){var z=MC;MC=!0;if(z)return}LC||M(43);if(!b.noTargetGroup)if(t){KC(e.id);
var C=e.id,E=d[K.m.Pg]||"default";E=String(E).split(",");for(var F=0;F<E.length;F++){var G=HC[E[F]]||[];HC[E[F]]=G;G.indexOf(C)<0&&G.push(C)}}else{JC(e.id);var J=e.id,N=d[K.m.Pg]||"default";N=N.toString().split(",");for(var X=0;X<N.length;X++){var Q=GC[N[X]]||[];GC[N[X]]=Q;Q.indexOf(J)<0&&Q.push(J)}}delete d[K.m.Pg];var na=b.eventMetadata||{};na.hasOwnProperty(P.C.rd)||(na[P.C.rd]=!b.fromContainerExecution);b.eventMetadata=na;delete d[K.m.Te];for(var S=t?[e.id]:vm(),aa=0;aa<S.length;aa++){var Y=d,
V=S[aa],ka=cd(b,null),ja=vp(V,ka.isGtmEvent);ja&&wq.push("config",[Y],ja,ka)}}}}},consent:function(a,b){if(a.length===3){M(39);var c=OC(a,b),d=a[1],e={},f=so(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===K.m.qg?Array.isArray(h)?NaN:Number(h):g===K.m.bc?(Array.isArray(h)?h:[h]).map(to):uo(h)}b.fromContainerExecution||(e[K.m.W]&&M(139),e[K.m.Na]&&M(140));d==="default"?Wo(e):d==="update"?Yo(e,c):d==="declare"&&b.fromContainerExecution&&Vo(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&gb(c)){var d=void 0;if(a.length>2){if(!bd(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=NC(c,d),f=OC(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=PC(d,b);if(m){var n=m.yj,p=m.vo,q,r,t;if(!om&&D(108)){q=p.map(function(J){return J.id});r=p.map(function(J){return J.destinationId});t=n.map(function(J){return J.id});for(var u=k(pm?pm:wm()),v=u.next();!v.done;v=u.next()){var w=v.value;
!zb(w,"siloed_")&&r.indexOf(w)<0&&r.indexOf(um(w))<0&&t.push(w)}}else q=n.map(function(J){return J.id}),r=n.map(function(J){return J.destinationId}),t=q;VB(g,c);for(var x=k(t),z=x.next();!z.done;z=x.next()){var C=z.value,E=cd(b,null),F=cd(d,null);delete F[K.m.Te];var G=E.eventMetadata||{};G.hasOwnProperty(P.C.rd)||(G[P.C.rd]=!E.fromContainerExecution);G[P.C.Ni]=q.slice();G[P.C.Ff]=r.slice();E.eventMetadata=G;xq(c,F,C,E);D(166)||rp(G[P.C.ab])}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[K.m.hd]=
q.join(","):delete e.eventModel[K.m.hd];LC||M(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata[P.C.Ll]&&(b.noGtmEvent=!0);e.eventModel[K.m.Kc]&&(b.noGtmEvent=!0);return b.noGtmEvent?void 0:e}}},get:function(a,b){M(53);if(a.length===4&&gb(a[1])&&gb(a[2])&&eb(a[3])){var c=vp(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){LC||M(43);var f=QC();if(jb(vm(),function(h){return c.destinationId===h})){OC(a,b);var g={};cd((g[K.m.oc]=d,g[K.m.Ic]=e,g),null);yq(d,function(h){A(function(){e(h)})},c.id,
b)}else uB(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){LC=!0;var c=OC(a,b),d=c.eventId,e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&gb(a[1])&&eb(a[2])){if(Xf(a[1],a[2]),M(74),a[1]==="all"){M(75);var b=!1;try{b=a[2](xm(),"unknown",{})}catch(c){}b||M(76)}}else M(73)},set:function(a,b){var c=void 0;a.length===
2&&bd(a[1])?c=cd(a[1],null):a.length===3&&gb(a[1])&&(c={},bd(a[2])||Array.isArray(a[2])?c[a[1]]=cd(a[2],null):c[a[1]]=a[2]);if(c){var d=OC(a,b),e=d.eventId,f=d.priorityId;cd(c,null);var g=cd(c,null);wq.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},VC={policy:!0};var XC=function(a){if(WC(a))return a;this.value=a};XC.prototype.getUntrustedMessageValue=function(){return this.value};var WC=function(a){return!a||$c(a)!=="object"||bd(a)?!1:"getUntrustedMessageValue"in a};XC.prototype.getUntrustedMessageValue=XC.prototype.getUntrustedMessageValue;var YC=!1,ZC=[];function $C(){if(!YC){YC=!0;for(var a=0;a<ZC.length;a++)A(ZC[a])}}function aD(a){YC?A(a):ZC.push(a)};var bD=0,cD={},dD=[],eD=[],fD=!1,gD=!1;function hD(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function iD(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return jD(a)}function kD(a,b){if(!hb(b)||b<0)b=0;var c=jp[Jj.Kb],d=0,e=!1,f=void 0;f=l.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(l.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function lD(a,b){var c=a._clear||b.overwriteModelFields;nb(a,function(e,f){e!=="_clear"&&(c&&kk(e),kk(e,f))});Uj||(Uj=a["gtm.start"]);var d=a["gtm.uniqueEventId"];if(!a.event)return!1;typeof d!=="number"&&(d=op(),a["gtm.uniqueEventId"]=d,kk("gtm.uniqueEventId",d));return tC(a)}function mD(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(ob(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function nD(){var a;if(eD.length)a=eD.shift();else if(dD.length)a=dD.shift();else return;var b;var c=a;if(fD||!mD(c.message))b=c;else{fD=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=op(),f=op(),c.message["gtm.uniqueEventId"]=op());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};dD.unshift(n,c);b=h}return b}
function oD(){for(var a=!1,b;!gD&&(b=nD());){gD=!0;delete ek.eventModel;gk();var c=b,d=c.message,e=c.messageContext;if(d==null)gD=!1;else{e.fromContainerExecution&&lk();try{if(eb(d))try{d.call(ik)}catch(u){}else if(Array.isArray(d)){if(gb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=hk(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(u){}}}else{var n=void 0;if(ob(d))a:{if(d.length&&gb(d[0])){var p=UC[d[0]];if(p&&(!e.fromContainerExecution||!VC[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;n&&(a=lD(n,e)||a)}}finally{e.fromContainerExecution&&gk(!0);var q=d["gtm.uniqueEventId"];if(typeof q==="number"){for(var r=cD[String(q)]||[],t=0;t<r.length;t++)eD.push(pD(r[t]));r.length&&eD.sort(hD);delete cD[String(q)];q>bD&&(bD=q)}gD=!1}}}return!a}
function qD(){if(D(109)){var a=!Fj.R;}var c=oD();if(D(109)){}try{var e=xm(),f=l[Jj.Kb].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function sw(a){if(bD<a.notBeforeEventId){var b=String(a.notBeforeEventId);cD[b]=cD[b]||[];cD[b].push(a)}else eD.push(pD(a)),eD.sort(hD),A(function(){gD||oD()})}function pD(a){return{message:a.message,messageContext:a.messageContext}}
function rD(){function a(f){var g={};if(WC(f)){var h=f;f=WC(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=pc(Jj.Kb,[]),c=np();c.pruned===!0&&M(83);cD=qw().get();rw();FC(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});aD(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(jp.SANDBOXED_JS_SEMAPHORE>0){f=
[];for(var g=0;g<arguments.length;g++)f[g]=new XC(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});dD.push.apply(dD,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(M(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return oD()&&p};var e=b.slice(0).map(function(f){return a(f)});dD.push.apply(dD,e);if(!Fj.R){if(D(109)){}A(qD)}}var jD=function(a){return l[Jj.Kb].push(a)};function sD(a){jD(a)};function tD(){var a,b=Hk(l.location.href);(a=b.hostname+b.pathname)&&An("dl",encodeURIComponent(a));var c;var d=$f.ctid;if(d){var e=nm.Af?1:0,f,g=Bm(Cm());f=g&&g.context;c=d+";"+$f.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&An("tdp",h);var m=Al(!0);m!==void 0&&An("frm",String(m))};function uD(){Zk&&l.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){M(179);var b=Xl(a.effectiveDirective);if(b){var c;var d=Vl(b,a.blockedURI);c=d?Tl[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=void 0}var h=e;if(h){for(var m=k(c),n=m.next();!n.done;n=m.next()){var p=n.value;if(!p.ym){p.ym=!0;if(D(59)){var q={eventId:p.eventId,priorityId:p.priorityId};
if(Fo()){var r=q,t={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if(Fo()){var u=Lo("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});u.tagDiagnostics=t;Eo(u)}}}Gn(p.endpoint)}}Wl(b,a.blockedURI)}}}}})};function vD(){var a;var b=Am();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&An("pcid",e)};var wD=/^(https?:)?\/\//;
function xD(){var a;var b=Bm(Cm());if(b){for(;b.parent;){var c=Bm(b.parent);if(!c)break;b=c}a=b}else a=void 0;var d=a;if(d){var e;a:{var f,g=(f=d.scriptElement)==null?void 0:f.src;if(g){var h;try{var m;h=(m=Rc())==null?void 0:m.getEntriesByType("resource")}catch(u){}if(h){for(var n=-1,p=k(h),q=p.next();!q.done;q=p.next()){var r=q.value;if(r.initiatorType==="script"&&(n+=1,r.name.replace(wD,"")===g.replace(wD,""))){e=n;break a}}M(146)}else M(145)}e=void 0}var t=e;t!==void 0&&(d.canonicalContainerId&&
An("rtg",String(d.canonicalContainerId)),An("slo",String(t)),An("hlo",d.htmlLoadOrder||"-1"),An("lst",String(d.loadScriptType||"0")))}else M(144)};
function SD(){};var TD=function(){};TD.prototype.toString=function(){return"undefined"};var UD=new TD;function aE(a,b){function c(g){var h=Hk(g),m=Bk(h,"protocol"),n=Bk(h,"host",!0),p=Bk(h,"port"),q=Bk(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function bE(a){return cE(a)?1:0}
function cE(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=cd(a,{});cd({arg1:c[d],any_of:void 0},e);if(bE(e))return!0}return!1}switch(a["function"]){case "_cn":return Ig(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Dg.length;g++){var h=Dg[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Eg(b,c);case "_eq":return Jg(b,c);case "_ge":return Kg(b,c);case "_gt":return Mg(b,c);case "_lc":return Fg(b,c);case "_le":return Lg(b,
c);case "_lt":return Ng(b,c);case "_re":return Hg(b,c,a.ignore_case);case "_sw":return Og(b,c);case "_um":return aE(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var dE=function(a,b,c,d){Nq.call(this);this.mh=b;this.Bf=c;this.Fb=d;this.Za=new Map;this.oh=0;this.ma=new Map;this.Ca=new Map;this.T=void 0;this.J=a};sa(dE,Nq);dE.prototype.O=function(){delete this.D;this.Za.clear();this.ma.clear();this.Ca.clear();this.T&&(Jq(this.J,"message",this.T),delete this.T);delete this.J;delete this.Fb;Nq.prototype.O.call(this)};
var eE=function(a){if(a.D)return a.D;a.Bf&&a.Bf(a.J)?a.D=a.J:a.D=zl(a.J,a.mh);var b;return(b=a.D)!=null?b:null},gE=function(a,b,c){if(eE(a))if(a.D===a.J){var d=a.Za.get(b);d&&d(a.D,c)}else{var e=a.ma.get(b);if(e&&e.xj){fE(a);var f=++a.oh;a.Ca.set(f,{Hh:e.Hh,Io:e.hm(c),persistent:b==="addEventListener"});a.D.postMessage(e.xj(c,f),"*")}}},fE=function(a){a.T||(a.T=function(b){try{var c;c=a.Fb?a.Fb(b):void 0;if(c){var d=c.Qp,e=a.Ca.get(d);if(e){e.persistent||a.Ca.delete(d);var f;(f=e.Hh)==null||f.call(e,
e.Io,c.payload)}}}catch(g){}},Iq(a.J,"message",a.T))};var hE=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},iE=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},jE={hm:function(a){return a.listener},xj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Hh:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},kE={hm:function(a){return a.listener},xj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Hh:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function lE(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,Qp:b.__gppReturn.callId}}
var mE=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;Nq.call(this);this.caller=new dE(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},lE);this.caller.Za.set("addEventListener",hE);this.caller.ma.set("addEventListener",jE);this.caller.Za.set("removeEventListener",iE);this.caller.ma.set("removeEventListener",kE);this.timeoutMs=c!=null?c:500};sa(mE,Nq);mE.prototype.O=function(){this.caller.dispose();Nq.prototype.O.call(this)};
mE.prototype.addEventListener=function(a){var b=this,c=bl(function(){a(nE,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);gE(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(oE,!0);return}a(pE,!0)}}})};
mE.prototype.removeEventListener=function(a){gE(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var pE={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},nE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},oE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function qE(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){Yu.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");Yu.D=d}}function rE(){try{var a=new mE(l,{timeoutMs:-1});eE(a.caller)&&a.addEventListener(qE)}catch(b){}};function sE(){var a;a=a===void 0?"":a;var b,c;return((b=data)==null?0:(c=b.blob)==null?0:c.hasOwnProperty(1))?String(data.blob[1]):a};function tE(){var a=[["cv",sE()],["rv",Jj.Mi],["tc",wf.filter(function(b){return b}).length]];Jj.Li&&a.push(["x",Jj.Li]);Zj()&&a.push(["tag_exp",Zj()]);return a};var uE={},vE={};function wE(a){var b=a.eventId,c=a.Id,d=[],e=uE[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=vE[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete uE[b],delete vE[b]);return d};function xE(){return!1}function yE(){var a={};return function(b,c,d){}};function zE(){var a=AE;return function(b,c,d){var e=d&&d.event;BE(c);var f=th(b)?void 0:1,g=new Pa;nb(c,function(r,t){var u=sd(t,void 0,f);u===void 0&&t!==void 0&&M(44);g.set(r,u)});a.D.D.J=Pf();var h={Vl:dg(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Jf:e!==void 0?function(r){e.Pc.Jf(r)}:void 0,Gb:function(){return b},log:function(){},To:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},Xp:!!bB(b,3),originalEventData:e==null?void 0:
e.originalEventData};e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(xE()){var m=yE(),n,p;h.tb={Oj:[],Kf:{},Yb:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},Fh:Lh()};h.log=function(r){var t=ya.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=
Qe(a,h,[b,g]);a.D.D.J=void 0;q instanceof Ba&&(q.type==="return"?q=q.data:q=void 0);return rd(q,void 0,f)}}function BE(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;eb(b)&&(a.gtmOnSuccess=function(){A(b)});eb(c)&&(a.gtmOnFailure=function(){A(c)})};function CE(a){}CE.N="internal.addAdsClickIds";function DE(a,b){var c=this;}DE.publicName="addConsentListener";var EE=!1;function FE(a){for(var b=0;b<a.length;++b)if(EE)try{a[b]()}catch(c){M(77)}else a[b]()}function GE(a,b,c){var d=this,e;return e}GE.N="internal.addDataLayerEventListener";function HE(a,b,c){}HE.publicName="addDocumentEventListener";function IE(a,b,c,d){}IE.publicName="addElementEventListener";function JE(a){return a.M.D};function KE(a){}KE.publicName="addEventCallback";
function $E(a){}$E.N="internal.addFormAbandonmentListener";function aF(a,b,c,d){}
aF.N="internal.addFormData";var bF={},cF=[],dF={},eF=0,fF=0;
function mF(a,b){}mF.N="internal.addFormInteractionListener";
function tF(a,b){}tF.N="internal.addFormSubmitListener";
function yF(a){}yF.N="internal.addGaSendListener";function zF(a){if(!a)return{};var b=a.To;return yB(b.type,b.index,b.name)}function AF(a){return a?{originatingEntity:zF(a)}:{}};function IF(a){var b=jp.zones;return b?b.getIsAllowedFn(sm(),a):function(){return!0}}function JF(){var a=jp.zones;a&&a.unregisterChild(sm())}
function KF(){eB(zm(),function(a){var b=a.originalEventData["gtm.uniqueEventId"],c=jp.zones;return c?c.isActive(sm(),b):!0});cB(zm(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return IF(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var LF=function(a,b){this.tagId=a;this.Mf=b};
function MF(a,b){var c=this,d=void 0;
return d}MF.N="internal.loadGoogleTag";function NF(a){return new jd("",function(b){var c=this.evaluate(b);if(c instanceof jd)return new jd("",function(){var d=ya.apply(0,arguments),e=this,f=cd(JE(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=Ia(this.M);h.D=f;return c.Ib.apply(c,[h].concat(ua(g)))})})};function OF(a,b,c){var d=this;}OF.N="internal.addGoogleTagRestriction";var PF={},QF=[];
function XF(a,b){}
XF.N="internal.addHistoryChangeListener";function YF(a,b,c){}YF.publicName="addWindowEventListener";function ZF(a,b){return!0}ZF.publicName="aliasInWindow";function $F(a,b,c){}$F.N="internal.appendRemoteConfigParameter";function aG(a){var b;return b}
aG.publicName="callInWindow";function bG(a){}bG.publicName="callLater";function cG(a){}cG.N="callOnDomReady";function dG(a){}dG.N="callOnWindowLoad";function eG(a,b){var c;return c}eG.N="internal.computeGtmParameter";function fG(a,b){var c=this;}fG.N="internal.consentScheduleFirstTry";function gG(a,b){var c=this;}gG.N="internal.consentScheduleRetry";function hG(a){var b;return b}hG.N="internal.copyFromCrossContainerData";function iG(a,b){var c;var d=sd(c,this.M,th(JE(this).Gb())?2:1);d===void 0&&c!==void 0&&M(45);return d}iG.publicName="copyFromDataLayer";
function jG(a){var b=void 0;return b}jG.N="internal.copyFromDataLayerCache";function kG(a){var b;return b}kG.publicName="copyFromWindow";function lG(a){var b=void 0;return sd(b,this.M,1)}lG.N="internal.copyKeyFromWindow";var mG=function(a){return a===Sm.Z.Ea&&kn[a]===Rm.Ka.ee&&!$o(K.m.V)};var nG=function(){return"0"},oG=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];D(102)&&b.push("gbraid");return Ik(a,b,"0")};var pG={},qG={},rG={},sG={},tG={},uG={},vG={},wG={},xG={},yG={},zG={},AG={},BG={},CG={},DG={},EG={},FG={},GG={},HG={},IG={},JG={},KG={},LG={},MG={},NG={},OG={},PG=(OG[K.m.Sa]=(pG[2]=[mG],pG),OG[K.m.df]=(qG[2]=[mG],qG),OG[K.m.Ue]=(rG[2]=[mG],rG),OG[K.m.oi]=(sG[2]=[mG],sG),OG[K.m.ri]=(tG[2]=[mG],tG),OG[K.m.si]=(uG[2]=[mG],uG),OG[K.m.ui]=(vG[2]=[mG],vG),OG[K.m.wi]=(wG[2]=[mG],wG),OG[K.m.Tb]=(xG[2]=[mG],xG),OG[K.m.ff]=(yG[2]=[mG],yG),OG[K.m.hf]=(zG[2]=[mG],zG),OG[K.m.jf]=(AG[2]=[mG],AG),OG[K.m.kf]=(BG[2]=
[mG],BG),OG[K.m.lf]=(CG[2]=[mG],CG),OG[K.m.nf]=(DG[2]=[mG],DG),OG[K.m.pf]=(EG[2]=[mG],EG),OG[K.m.qf]=(FG[2]=[mG],FG),OG[K.m.nb]=(GG[1]=[mG],GG),OG[K.m.Wc]=(HG[1]=[mG],HG),OG[K.m.bd]=(IG[1]=[mG],IG),OG[K.m.Td]=(JG[1]=[mG],JG),OG[K.m.Fe]=(KG[1]=[function(a){return D(102)&&mG(a)}],KG),OG[K.m.dd]=(LG[1]=[mG],LG),OG[K.m.Ba]=(MG[1]=[mG],MG),OG[K.m.Wa]=(NG[1]=[mG],NG),OG),QG={},RG=(QG[K.m.nb]=nG,QG[K.m.Wc]=nG,QG[K.m.bd]=nG,QG[K.m.Td]=nG,QG[K.m.Fe]=nG,QG[K.m.dd]=function(a){if(!bd(a))return{};var b=cd(a,
null);delete b.match_id;return b},QG[K.m.Ba]=oG,QG[K.m.Wa]=oG,QG),SG={},TG={},UG=(TG[P.C.Ta]=(SG[2]=[mG],SG),TG),VG={};var WG=function(a,b,c,d){this.D=a;this.O=b;this.R=c;this.T=d};WG.prototype.getValue=function(a){a=a===void 0?Sm.Z.Eb:a;if(!this.O.some(function(b){return b(a)}))return this.R.some(function(b){return b(a)})?this.T(this.D):this.D};WG.prototype.J=function(){return $c(this.D)==="array"||bd(this.D)?cd(this.D,null):this.D};
var XG=function(){},YG=function(a,b){this.conditions=a;this.D=b},ZG=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new WG(c,e,g,a.D[b]||XG)},$G,aH;var bH=function(a,b,c){this.eventName=b;this.F=c;this.D={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=k(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;T(this,g,d[g])}},nv=function(a,b){var c,d;return(c=a.D[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,R(a,P.C.Hf))},U=function(a,b,c){var d=a.D,e;c===void 0?e=void 0:($G!=null||($G=new YG(PG,RG)),e=ZG($G,b,c));d[b]=e},Hx=function(a,b,c){var d,e,f;(d=(e=a.D[b])==null?void 0:(f=e.J)==null?void 0:
f.call(e))?bd(d)&&U(a,b,Object.assign(d,c)):U(a,b,c)},cH=function(a,b){b=b===void 0?{}:b;for(var c=k(Object.keys(a.D)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.D[e])==null?void 0:(h=(g=f).J)==null?void 0:h.call(g)}return b};bH.prototype.copyToHitData=function(a,b,c){var d=O(this.F,a);d===void 0&&(d=b);if(d!==void 0&&c!==void 0&&gb(d)&&D(92))try{d=c(d)}catch(e){}d!==void 0&&U(this,a,d)};
var R=function(a,b){var c=a.metadata[b];if(b===P.C.Hf){var d;return c==null?void 0:(d=c.J)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,R(a,P.C.Hf))},T=function(a,b,c){var d=a.metadata,e;c===void 0?e=void 0:(aH!=null||(aH=new YG(UG,VG)),e=ZG(aH,b,c));d[b]=e},dH=function(a,b){b=b===void 0?{}:b;for(var c=k(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).J)==null?void 0:
h.call(g)}return b},Hv=function(a,b,c){var d=a.target.destinationId;om||(d=Dm(d));var e=ww(d);return e&&e[b]!==void 0?e[b]:c};function eH(a,b){var c;return c}eH.N="internal.copyPreHit";function fH(a,b){var c=null;return sd(c,this.M,2)}fH.publicName="createArgumentsQueue";function gH(a){return sd(function(c){var d=HB();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
HB(),n=m&&m.getByName&&m.getByName(f);return(new l.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.M,1)}gH.N="internal.createGaCommandQueue";function hH(a){return sd(function(){if(!eb(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.M,
th(JE(this).Gb())?2:1)}hH.publicName="createQueue";function iH(a,b){var c=null;return c}iH.N="internal.createRegex";function jH(){var a={};return a};function kH(a){}kH.N="internal.declareConsentState";function lH(a){var b="";return b}lH.N="internal.decodeUrlHtmlEntities";function mH(a,b,c){var d;return d}mH.N="internal.decorateUrlWithGaCookies";function nH(){}nH.N="internal.deferCustomEvents";function oH(a){var b;I(this,"detect_user_provided_data","auto");var c=rd(a)||{},d=Ww({se:!!c.includeSelector,te:!!c.includeVisibility,Pf:c.excludeElementSelectors,Wb:c.fieldFilters,Jh:!!c.selectMultipleElements});b=new Pa;var e=new fd;b.set("elements",e);for(var f=d.elements,g=0;g<f.length;g++)e.push(pH(f[g]));d.Gj!==void 0&&b.set("preferredEmailElement",pH(d.Gj));b.set("status",d.status);if(D(129)&&c.performDataLayerSearch&&!/Mobile|iPhone|iPad|iPod|Android|IEMobile/.test(lc&&
lc.userAgent||"")){}return b}
var qH=function(a){switch(a){case Uw.fc:return"email";case Uw.vd:return"phone_number";case Uw.nd:return"first_name";case Uw.ud:return"last_name";case Uw.Ti:return"street";case Uw.Mh:return"city";case Uw.Ki:return"region";case Uw.Df:return"postal_code";case Uw.Ae:return"country"}},pH=function(a){var b=new Pa;b.set("userData",a.ka);b.set("tagName",a.tagName);a.querySelector!==void 0&&b.set("querySelector",a.querySelector);a.isVisible!==void 0&&b.set("isVisible",a.isVisible);if(D(33)){}else switch(a.type){case Uw.fc:b.set("type","email")}return b};oH.N="internal.detectUserProvidedData";
function tH(a,b){return f}tH.N="internal.enableAutoEventOnClick";
function BH(a,b){return p}BH.N="internal.enableAutoEventOnElementVisibility";function CH(){}CH.N="internal.enableAutoEventOnError";var DH={},EH=[],FH={},GH=0,HH=0;
function NH(a,b){var c=this;return d}NH.N="internal.enableAutoEventOnFormInteraction";
function SH(a,b){var c=this;return f}SH.N="internal.enableAutoEventOnFormSubmit";
function XH(){var a=this;}XH.N="internal.enableAutoEventOnGaSend";var YH={},ZH=[];
function fI(a,b){var c=this;return f}fI.N="internal.enableAutoEventOnHistoryChange";var gI=["http://","https://","javascript:","file://"];
function kI(a,b){var c=this;return h}kI.N="internal.enableAutoEventOnLinkClick";var lI,mI;
function xI(a,b){var c=this;return d}xI.N="internal.enableAutoEventOnScroll";function yI(a){return function(){if(a.limit&&a.Aj>=a.limit)a.Ch&&l.clearInterval(a.Ch);else{a.Aj++;var b=ub();jD({event:a.eventName,"gtm.timerId":a.Ch,"gtm.timerEventNumber":a.Aj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.Dm,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.Dm,"gtm.triggers":a.sq})}}}
function zI(a,b){
return f}zI.N="internal.enableAutoEventOnTimer";var ec=wa(["data-gtm-yt-inspected-"]),BI=["www.youtube.com","www.youtube-nocookie.com"],CI,DI=!1;
function NI(a,b){var c=this;return e}NI.N="internal.enableAutoEventOnYouTubeActivity";DI=!1;function OI(a,b){if(!eh(a)||!Zg(b))throw H(this.getName(),["string","Object|undefined"],arguments);var c=b?rd(b):{},d=a,e=!1;var f=JSON.parse(d);if(!f)throw Error("Invalid boolean expression string was given.");e=Ah(f,c);return e}OI.N="internal.evaluateBooleanExpression";var PI;function QI(a){var b=!1;return b}QI.N="internal.evaluateMatchingRules";
var VI=function(a){var b=R(a,P.C.eh);if(!$o([K.m.V,K.m.W]))return{endpoint:2,Dd:8};if(b){if(R(a,P.C.gh)){var c=[4];D(71)&&c.unshift(11);return{endpoint:3,Dd:3,wd:c}}return{endpoint:3,Dd:1}}if(R(a,P.C.gh)){var d=[7];D(71)&&d.unshift(5);return{endpoint:1,Dd:6,wd:d}}return{endpoint:1,Dd:5}},ZI=function(a,b,c){if(R(a,P.C.da)===L.K.Ia)wz(a);else{var d=VI(a);WI(a,d.Dd,function(e){var f=R(a,P.C.fh),g=[];nb(e,function(w,x){g.push(w+"="+x)});var h=function(w){Mo({targetId:f.target.destinationId,request:{url:w,
parameterEncoding:4,endpoint:d.endpoint},cb:{eventId:a.F.eventId,priorityId:a.F.priorityId},th:b===void 0?void 0:{eventId:b,priorityId:c}})},m={destinationId:f.target.destinationId,endpoint:d.endpoint,eventId:a.F.eventId,priorityId:a.F.priorityId},n=function(w,x,z){return w.map(function(C){return C.indexOf(x+"=")===0?x+"="+z:C})};if(d.wd){g.splice(-1,0,"dc_random="+R(a,P.C.kb));var p=""+XI(d.Dd,a)+g.join(";")+"?",q=g.slice(),r=function(w){var x;if(!(w>=(((x=d.wd)==null?void 0:x.length)||0))){var z=
d.wd[w],C=n(q,"dc_fmt",z),E=""+XI(z,a)+C.join(";")+"?",F;a:switch(z){case 1:case 2:case 9:F=4;break a;case 3:case 4:case 6:case 7:F=2;break a;default:F=3}var G=F;G===2?am(m,E,void 0,void 0,function(){h(E);a.F.onSuccess()},function(){r(w+1)}):G===3&&$l(m,E,function(){h(E);a.F.onSuccess()},function(){r(w+1)})}};am(m,p,void 0,R(a,P.C.yi)?{attributionReporting:YI}:void 0,function(){h(p);a.F.onSuccess()},function(){r(0)});g.splice(-1,0,"_dc_test=1");g=n(g,"dc_fmt",2)}var t=XI(d.Dd,a)+g.join(";")+"?";d.wd||
h(t);R(a,P.C.eh)?cm(m,XI(2,a)+g.join(";")+"?",a.F.onSuccess):d.wd||$l(m,t,a.F.onSuccess,a.F.onFailure);if(R(a,P.C.yi)&&!d.wd){g=n(g,"dc_fmt",10);var u=""+XI(10,a)+g.join(";")+"?";$l({destinationId:f.target.destinationId,endpoint:33,eventId:a.F.eventId,priorityId:a.F.priorityId},u,void 0,void 0,{attributionsrc:""})}if(R(a,P.C.nh)){g=n(g,"dc_fmt",9);var v=""+XI(9,a)+g.join(";")+"?";em({destinationId:f.target.destinationId,endpoint:43,eventId:a.F.eventId,priorityId:a.F.priorityId},v,Dm(f.target.id));
Mo({targetId:f.target.destinationId,request:{url:v,parameterEncoding:4,endpoint:43},cb:{eventId:a.F.eventId,priorityId:a.F.priorityId},th:b===void 0?void 0:{eventId:b,priorityId:c}})}})}},WI=function(a,b,c){for(var d=[],e={},f=function(C,E,F){var G=F?String(E):encodeURIComponent(String(E));e[C]=G},g=bd(nv(a,K.m.dd))?nv(a,K.m.dd):{},h=k(Object.keys(a.D)),m=h.next();!m.done;m=h.next()){var n=m.value,p=nv(a,n),q=$I[n];if(q){var r=ug[q]===!0;if(p===void 0||!r&&p==="")continue;f(q,p)}q===void 0&&f(n,p)}f("gtm",
Ar({Oa:R(a,P.C.ab)}));nr()&&f("gcs",or());f("gcd",sr(a.F));vr()&&f("dma_cps",tr());f("dma",ur());f("dc_fmt",b);Rq(Zq())&&f("tcfd",wr());pz()&&f("tag_exp",pz());qz()&&f("ptag_exp",qz());var t=aJ(a);t&&f("prd",t,!0);f("epver","2");kn[Sm.Z.Ea]!==Rm.Ka.ee||nn[Sm.Z.Ea].isConsentGranted()||f("limited_ads","1");var u=nv(a,K.m.Ba);u&&R(a,P.C.he)&&(u=yu(String(u)));nb(g,function(C,E){if(E!=null)if(C==="~oref")u=E;else{var F=encodeURIComponent(C);e[F]!=null&&M(141);f(F,E)}});var v=$o(K.m.W);D(125)&&(v=!1);
var w=R(a,P.C.Ta);if(w&&v){var x=hj(w);x&&d.push(x.then(function(C){f("em",C.zb)}))}var z=function(){u&&f("~oref",u);c(e)};if(d.length)try{Promise.all(d).then(function(){z()});return}catch(C){}z()},XI=function(a,b){switch(a){case 1:case 2:return oz(3,{Ul:String(nv(b,K.m.fi))});case 4:case 7:return"https://www.google.com/gmp/conversion;";case 8:return oz(2);case 10:return oz(33);case 9:return"https://td.doubleclick.net/td/fls/rul/activityi;fledge=1;";default:return""+oz(1)}},aJ=function(a){var b=nv(a,
K.m.sa);if(!Array.isArray(b))return"";for(var c=[],d=function(n,p,q){q!==void 0&&q!==""&&c.push(""+n+p+":"+encodeURIComponent(String(q)))},e=0;e<b.length;e++){var f=b[e],g=D(72),h=f.id;f.item_id!==void 0&&(g&&(h=f.item_id),f.id!==void 0?(M(150),f.id!==f.item_id&&M(151)):M(152));h===void 0&&g&&(h=f.item_name);var m=e+1;d("i",m,h);d("p",m,f.price);d("q",m,f[K.m.bf]);d("c",m,f[K.m.Ud]);d("l",m,f[K.m.xb]);d("a",m,f.accountId)}return c.join("|")},YI={eventSourceEligible:!1,triggerEligible:!0},bJ={},$I=
(bJ[K.m.ja]="gcu",bJ[K.m.kc]="gclgb",bJ[K.m.nb]="gclaw",bJ[K.m.Qd]="gclgs",bJ[K.m.Rd]="gcllp",bJ[K.m.Sd]="gclst",bJ[K.m.Mb]="auiddc",bJ[K.m.Yc]="ps",bJ[K.m.Fg]="pscdl",bJ[K.m.Yh]="gcldc",bJ[K.m.Qb]="edid",bJ[K.m.Ck]="cat",bJ[K.m.Dk]="type",bJ[K.m.fi]="src",bJ[K.m.Ek]="pcor",bJ[K.m.Ue]="match_id",bJ[K.m.Fk]="num",bJ[K.m.Gk]="tran",bJ[K.m.Hk]="u",bJ[K.m.Og]="gac",bJ[K.m.Wd]="gacgb",bJ[K.m.fd]="gdpr",bJ[K.m.Rb]="gdid",bJ[K.m.Xd]="_ng",bJ[K.m.Ve]="gpp_sid",bJ[K.m.We]="gpp",bJ[K.m.Xe]="_tu",bJ[K.m.Jc]=
"frm",bJ[K.m.Qg]="gtm_up",bJ[K.m.bf]="qty",bJ[K.m.kd]="gdpr_consent",bJ[K.m.Xa]="ord",bJ[K.m.ff]="uaa",bJ[K.m.hf]="uab",bJ[K.m.jf]="uafvl",bJ[K.m.kf]="uamb",bJ[K.m.lf]="uam",bJ[K.m.nf]="uap",bJ[K.m.pf]="uapv",bJ[K.m.qf]="uaw",bJ[K.m.Ga]="cost",bJ[K.m.vc]="npa",bJ[K.m.Ba]=null,bJ[K.m.dd]=null,bJ[K.m.sa]=null,bJ);
var cJ=function(){return[K.m.V,K.m.W]},eJ=function(a){var b=vp(a);if(b&&(b.ids.length===1||b.ids.length===3)){var c=b.ids[xp[4]]||"",d=b.ids[xp[2]],e="",f="unknown";if(d){var g=d.split("+");if(g.length!==2)return;e=g[0];f=dJ[g[1].toLowerCase()]}if(f)return{target:b,ro:c,so:e,Eo:f}}},fJ=function(a){fw(a);},gJ=function(a){var b={};D(167)&&(b=so(wq.D[K.m.oa]));var c=Db(a.F.getMergedValues(K.m.oa,1,b),"."),d=Db(a.F.getMergedValues(K.m.oa,
2,void 0),".");U(a,K.m.Rb,c);U(a,K.m.Qb,d)},hJ=function(a){if(!R(a,P.C.ja)){var b=O(a.F,K.m.vn);if(bd(b)&&b.exclusion_parameters&&b.engines)if(yr()){}else{var c=O(a.F,K.m.Qa)!==!1,d=lv(a),e=function(){if($o(cJ())){var f={config:b,gtm:Ar({Oa:R(a,P.C.ab)})};c&&(Ts(d),f.auiddc=Rs[Us(d.prefix)]);l.__dc_ns_processor===void 0&&(l.__dc_ns_processor=[]);l.__dc_ns_processor.push(f);xc("https://www.googletagmanager.com/dclk/ns/v1.js")}};
$o(cJ())?e():hn(e,cJ())}}},kJ=function(a,b,c,d){function e(p,q){for(var r=k(g),t=r.next();!t.done;t=r.next()){var u=t.value;if(!R(u,P.C.ja)||R(u,P.C.da)===L.K.Ia||$o(cJ())){for(var v=R(u,P.C.da)===L.K.Ia?iJ:jJ,w=k(v),x=w.next();!x.done;x=w.next()){var z=x.value;z(u);if(u.isAborted)break}R(u,P.C.Ja)||u.isAborted||(ZI(u,p,q),R(u,P.C.da)===L.K.Ia&&rv(u,function(){e(p,q)}))}}}var f=eJ(a);if(f){var g=[],h=new bH(f.target,b,d);T(h,P.C.fh,f);T(h,P.C.zi,f.Eo);T(h,P.C.da,L.K.On);D(158)&&T(h,P.C.gh,!0);g.push(h);
if(D(24)&&b===K.m.qa){var m=new bH(f.target,b,d);T(m,P.C.da,L.K.Ia);T(m,P.C.Ja,!0);g.push(m)}var n=cJ();cp(function(){e();$o(n)||bp(function(p){var q,r;q=p.consentEventId;r=p.consentPriorityId;for(var t=k(g),u=t.next();!u.done;u=t.next()){var v=u.value;T(v,P.C.ja,!0);T(v,P.C.kb,ub());T(v,P.C.ye,q);T(v,P.C.ze,r)}e(q,r)},n)},n)}else A(d.onFailure)},jJ=[Gv,xv,Dv,Iv,function(a){T(a,P.C.Tc,O(a.F,K.m.Qa)!==!1);T(a,P.C.kb,ub());var b=O(a.F,K.m.yg)===!0,c=$o(cJ());if(yr()&&b){b=!1}T(a,P.C.eh,b&&c);var d=O(a.F,K.m.ya);T(a,P.C.wc,d!==void 0&&d!==!1);T(a,P.C.he,R(a,P.C.wc)&&!$o(cJ()));T(a,P.C.xa,lv(a))},function(a){if(!R(a,P.C.ja)){var b=a.F.isGtmEvent?O(a.F,"oref"):Ek(Hk(l.location.href));U(a,K.m.Ba,b)}},function(a){if(a.eventName===K.m.Bb&&!a.F.isGtmEvent){if(!R(a,P.C.ja)){var b=function(f){return O(a.F,f)},c={callback:b(K.m.Ic),im:b(b(K.m.oc)),Jm:b(K.m.oc)},d=R(a,P.C.xa),e=R(a,P.C.wc);vv(c,d,e,Ku)}a.isAborted=!0}},function(a){if(a.eventName===
K.m.qa&&!a.F.isGtmEvent){if(!R(a,P.C.ja)&&!D(24)){var b=R(a,P.C.Tc),c=R(a,P.C.xa),d=R(a,P.C.wc);qv({me:b,ue:O(a.F,K.m.Ra)||{},xe:O(a.F,K.m.Db),Oa:R(a,P.C.ab),F:a.F,ve:d,Im:O(a.F,K.m.Sa)},c);hJ(a);var e=R(a,P.C.fh).target,f=Db(a.F.getMergedValues(K.m.oa,2),"."),g=Db(a.F.getMergedValues(K.m.oa,1),"."),h=Al(!0);Rv({dj:!0,Qc:b?c:void 0,jj:f,F:a.F,mj:g,Bh:b,ve:d,targetId:e.ids.length>1?e.id:"",qj:h})}a.isAborted=!0}},function(a){var b=R(a,P.C.fh),c={},d=O(a.F,K.m.dd);bd(d)&&nb(d,function(e,f){f!=null&&
(c[e]=f)});U(a,K.m.fi,b.target.ids[xp[3]]);U(a,K.m.Dk,b.ro);U(a,K.m.Ck,b.so);U(a,K.m.dd,c)},function(a){var b=R(a,P.C.zi);Za("GTAG_EVENT_FEATURE_CHANNEL",Cj[b]);switch(b){case "standard":U(a,K.m.Xa,kb(1E11,1E13));Za("GTAG_EVENT_FEATURE_CHANNEL",12);return;case "unique":U(a,K.m.Xa,"1");U(a,K.m.Fk,kb(1E11,1E13));Za("GTAG_EVENT_FEATURE_CHANNEL",12);return;case "per_session":var c=O(a.F,K.m.sc);U(a,K.m.Xa,c);return}var d=b==="transactions"?"1":O(a.F,K.m.bf);U(a,K.m.bf,d);a.copyToHitData(K.m.Ga);a.copyToHitData(K.m.Xa)},
function(a){a.F.isGtmEvent&&(a.copyToHitData(K.m.Hk),a.copyToHitData(K.m.Gk),a.copyToHitData(K.m.Ue))},function(a){R(a,P.C.ja)&&U(a,K.m.ja,"1")},function(a){var b=fr();b&&U(a,K.m.fd,b);var c=er();c&&U(a,K.m.kd,c)},function(a){Fs(!1)._up==="1"&&U(a,K.m.Qg,"1")},function(a){mr(a.F)?U(a,K.m.vc,"0"):U(a,K.m.vc,"1")},function(a){if(R(a,P.C.Tc)){var b=$o(cJ()),c=R(a,P.C.xa),d=R(a,P.C.wc),e=Ku(c.prefix,d),f=!1;Ts(c);var g=b?Rs[Us(c.prefix)]:void 0;e&&e.length&&(U(a,K.m.Yh,e.join(".")),f=!0);if($o(K.m.V)){var h=
Jt(c.prefix),m=gv(h);U(a,K.m.Qd,m.xh);U(a,K.m.Sd,m.zh);U(a,K.m.Rd,m.yh)}if(R(a,P.C.eh)){var n=Jt(c.prefix)!=="_gcl",p;if(!(p=f)){var q=c.prefix;p=!(Jk("gclaw")||Jk("gac")||($t().aw||[]).length>0?0:($t().gb||[]).length>0||$t().gbraid!==void 0||wu(q))}if(p){var r=Lu(c.prefix,d);r&&r.length&&(U(a,K.m.nb,r.join(".")),M(59));var t,u=Jk("gac");(t=u?!$o(Bu())&&d?"0":Ak(u)||"":Iu(Dt(Ct())?et():{}))&&(n||U(a,K.m.Og,t))}else{var v;a:{var w=c.prefix,x=Jk("gclgb");if(x)v=x.split(".");else{var z=Jt(w);if(z===
"_gcl"){var C=!$o(Bu())&&d,E=$t(),F=[];E.wbraid&&F.push(E.wbraid);E.gbraid&&F.push(E.gbraid);if(F.length>0){v=C?["0"]:F;break a}}v=Ht({prefix:z})}}var G=v;G.length&&U(a,K.m.kc,G.join("."));if(!n){var J,N=Jk("gacgb");(J=N?!$o(Bu())&&d?"0":Ak(N)||"":Iu(Dt(Ct())?et("_gac_gb",!0):{}))&&U(a,K.m.Wd,J)}}}g&&U(a,K.m.Mb,g)}},function(a){var b=R(a,P.C.zi);if(b==="transactions"||b==="items_sold"){var c=O(a.F,K.m.sa);if(Array.isArray(c)){if(!a.F.isGtmEvent)for(var d=O(a.F,K.m.Ud),e=O(a.F,K.m.xb),f=k(c),g=f.next();!g.done;g=
f.next()){var h=g.value;bd(h)&&(h[K.m.Ud]=d,h[K.m.xb]=e,h.accountId=void 0)}U(a,K.m.sa,c)}}},function(a){var b=O(a.F,K.m.Qe),c={};bd(b)&&nb(b,function(g,h){gb(h)&&lJ.test(g)&&(c[g]=h)});for(var d=Np(a.F),e=0;e<d.length;e++){var f=d[e];lJ.test(f)&&(c[f]=f)}nb(c,function(g,h){U(a,g,O(a.F,h))})},gJ,function(a){var b=$o(cJ())&&Dl("attribution-reporting");T(a,P.C.yi,b);b&&!R(a,P.C.gh)&&(U(a,K.m.Yc,"1"),U(a,K.m.Ek,kb()))},function(a){if(a.F.isGtmEvent&&om){var b=O(a.F,K.m.Ya);bd(b)&&T(a,P.C.Ta,b)}},function(a){if(!qy(l))M(87);
else if(vy!==void 0){M(85);var b=oy();b?ty(b,a):M(86)}},function(a){D(56)&&O(a.F,K.m.ob)!==!1&&$o(cJ())&&mr(a.F)&&El()&&T(a,P.C.nh,!0)},function(a){if(Hv(a,"ccd_add_1p_data",!1)&&$o(K.m.W)){var b=a.F.J[K.m.Zg];if(tk(b)){var c=O(a.F,K.m.Ya);c===null?T(a,P.C.je,null):(b.enable_code&&bd(c)&&T(a,P.C.je,c),bd(b.selectors)&&T(a,P.C.qh,rk(b.selectors)))}}},yv,zv,Av,fJ,Ev],iJ=[Dv,pv,gJ,hJ,Av,Iv,fJ,Ev],dJ={"":"unknown",standard:"standard",unique:"unique",per_session:"per_session",transactions:"transactions",
items_sold:"items_sold"},lJ=/^u([1-9]\d?|100)$/;function zJ(){return gr(7)&&gr(9)&&gr(10)};function uK(a,b,c,d){}uK.N="internal.executeEventProcessor";function vK(a){var b;return sd(b,this.M,1)}vK.N="internal.executeJavascriptString";function wK(a){var b;return b};function xK(a){var b="";return b}xK.N="internal.generateClientId";function yK(a){var b={};return sd(b)}yK.N="internal.getAdsCookieWritingOptions";function zK(a,b){var c=!1;return c}zK.N="internal.getAllowAdPersonalization";function AK(){var a;return a}AK.N="internal.getAndResetEventUsage";function BK(a,b){b=b===void 0?!0:b;var c;return c}BK.N="internal.getAuid";var CK=null;
function DK(){var a=new Pa;I(this,"read_container_data"),D(49)&&CK?a=CK:(a.set("containerId",'DC-9857227'),a.set("version",'3'),a.set("environmentName",''),a.set("debugMode",eg),a.set("previewMode",fg.Gm),a.set("environmentMode",fg.Po),a.set("firstPartyServing",bk()||Fj.J),a.set("containerUrl",oc),a.fb(),D(49)&&(CK=a));return a}
DK.publicName="getContainerVersion";function EK(a,b){b=b===void 0?!0:b;var c;return c}EK.publicName="getCookieValues";function FK(){var a="";return a}FK.N="internal.getCorePlatformServicesParam";function GK(){return lo()}GK.N="internal.getCountryCode";function HK(){var a=[];a=vm();return sd(a)}HK.N="internal.getDestinationIds";function IK(a){var b=new Pa;return b}IK.N="internal.getDeveloperIds";function JK(a){var b;return b}JK.N="internal.getEcsidCookieValue";function KK(a,b){var c=null;return c}KK.N="internal.getElementAttribute";function LK(a){var b=null;return b}LK.N="internal.getElementById";function MK(a){var b="";return b}MK.N="internal.getElementInnerText";function NK(a,b){var c=null;return sd(c)}NK.N="internal.getElementProperty";function OK(a){var b;return b}OK.N="internal.getElementValue";function PK(a){var b=0;return b}PK.N="internal.getElementVisibilityRatio";function QK(a){var b=null;return b}QK.N="internal.getElementsByCssSelector";
function RK(a){var b;if(!eh(a))throw H(this.getName(),["string"],arguments);I(this,"read_event_data",a);var c;a:{var d=a,e=JE(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],x="",z=k(n),C=z.next();!C.done;C=
z.next()){var E=C.value;E===m?(w.push(x),x=""):x=E===g?x+"\\":E===h?x+".":x+E}x&&w.push(x);for(var F=k(w),G=F.next();!G.done;G=F.next()){if(f==null){c=void 0;break a}f=f[G.value]}c=f}else c=void 0}b=sd(c,this.M,1);return b}RK.N="internal.getEventData";var SK={};SK.enableAWFledge=D(34);SK.enableAdsConversionSplitHit=D(168);SK.enableAdsConversionValidation=D(18);SK.enableAdsSupernovaParams=D(30);SK.enableAutoPhoneAndAddressDetection=D(32);SK.enableAutoPiiOnPhoneAndAddress=D(33);SK.enableCachedEcommerceData=D(40);SK.enableCcdSendTo=D(41);SK.enableCloudRecommentationsErrorLogging=D(42);SK.enableCloudRecommentationsSchemaIngestion=D(43);SK.enableCloudRetailInjectPurchaseMetadata=D(45);SK.enableCloudRetailLogging=D(44);
SK.enableCloudRetailPageCategories=D(46);SK.enableCustomerLifecycleData=D(47);SK.enableDCFledge=D(56);SK.enableDataLayerSearchExperiment=D(129);SK.enableDecodeUri=D(92);SK.enableDeferAllEnhancedMeasurement=D(58);SK.enableDv3Gact=D(174);SK.enableEcMetadata=D(178);SK.enableFormSkipValidation=D(74);SK.enableGa4OutboundClicksFix=D(96);SK.enableGaAdsConversions=D(122);SK.enableGaAdsConversionsClientId=D(121);SK.enableMerchantRenameForBasketData=D(113);SK.enableOverrideAdsCps=D(170);
SK.enableUrlDecodeEventUsage=D(139);SK.enableZoneConfigInChildContainers=D(142);SK.useEnableAutoEventOnFormApis=D(156);function TK(){return sd(SK)}TK.N="internal.getFlags";function UK(){var a;return a}UK.N="internal.getGsaExperimentId";function VK(){return new od(UD)}VK.N="internal.getHtmlId";function WK(a){var b;return b}WK.N="internal.getIframingState";function XK(a,b){var c={};return sd(c)}XK.N="internal.getLinkerValueFromLocation";function YK(){var a=new Pa;if(arguments.length!==0)throw H(this.getName(),[],arguments);var b=kv();b!==void 0&&a.set(K.m.rf,b||"error");var c=fr();c&&a.set(K.m.fd,c);var d=er();d&&a.set(K.m.kd,d);var e=Yu.gppString;e&&a.set(K.m.We,e);var f=Yu.D;f&&a.set(K.m.Ve,f);return a}YK.N="internal.getPrivacyStrings";function ZK(a,b){var c;if(!eh(a)||!eh(b))throw H(this.getName(),["string","string"],arguments);var d=ww(a)||{};c=sd(d[b],this.M);return c}ZK.N="internal.getProductSettingsParameter";function $K(a,b){var c;return c}$K.publicName="getQueryParameters";function aL(a,b){var c;return c}aL.publicName="getReferrerQueryParameters";function bL(a){var b="";return b}bL.publicName="getReferrerUrl";function cL(){return mo()}cL.N="internal.getRegionCode";function dL(a,b){var c;return c}dL.N="internal.getRemoteConfigParameter";function eL(){var a=new Pa;a.set("width",0);a.set("height",0);return a}eL.N="internal.getScreenDimensions";function fL(){var a="";return a}fL.N="internal.getTopSameDomainUrl";function gL(){var a="";return a}gL.N="internal.getTopWindowUrl";function hL(a){var b="";return b}hL.publicName="getUrl";function iL(){I(this,"get_user_agent");return lc.userAgent}iL.N="internal.getUserAgent";function jL(){var a;return a?sd(sy(a)):a}jL.N="internal.getUserAgentClientHints";function rL(){return l.gaGlobal=l.gaGlobal||{}}function sL(){var a=rL();a.hid=a.hid||kb();return a.hid}function tL(a,b){var c=rL();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};
function RL(a){(Lx(a)||bk())&&U(a,K.m.al,mo()||lo());!Lx(a)&&bk()&&U(a,K.m.rl,"::")}function SL(a){if(bk()&&!Lx(a)&&(D(176)&&U(a,K.m.Ok,!0),D(78))){Bv(a);Cv(a,"cpf",vo(O(a.F,K.m.jb)));var b=O(a.F,K.m.Hc);Cv(a,"cu",b===!0?1:b===!1?0:void 0);Cv(a,"cf",vo(O(a.F,K.m.wb)));Cv(a,"cd",Zr(uo(O(a.F,K.m.pb)),uo(O(a.F,K.m.Pb))))}};var nM={AW:Wn.Mm,G:Wn.Rn,DC:Wn.Pn};function oM(a){var b=Ui(a);return""+Br(b.map(function(c){return c.value}).join("!"))}function pM(a){var b=vp(a);return b&&nM[b.prefix]}function qM(a,b){var c=a[b];c&&(c.clearTimerId&&l.clearTimeout(c.clearTimerId),c.clearTimerId=l.setTimeout(function(){delete a[b]},36E5))};var VM=window,WM=document,XM=function(a){var b=VM._gaUserPrefs;if(b&&b.ioo&&b.ioo()||WM.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&VM["ga-disable-"+a]===!0)return!0;try{var c=VM.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(WM.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h=="AMP_TOKEN"){var m=g.slice(1).join("=").replace(/^\s*|\s*$/g,"");m&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return WM.getElementById("__gaOptOutExtension")?!0:!1};function iN(a){nb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[K.m.Ub]||{};nb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};function QN(a,b){}function RN(a,b){var c=function(){};return c}
function SN(a,b,c){};var TN=RN;function VN(a,b,c){var d=this;}VN.N="internal.gtagConfig";
function XN(a,b){}
XN.publicName="gtagSet";function YN(){var a={};return a};function ZN(a){}ZN.N="internal.initializeServiceWorker";function $N(a,b){}$N.publicName="injectHiddenIframe";var aO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function bO(a,b,c,d,e){}bO.N="internal.injectHtml";var fO={};
function hO(a,b,c,d){}var iO={dl:1,id:1},jO={};
function kO(a,b,c,d){}D(160)?kO.publicName="injectScript":hO.publicName="injectScript";kO.N="internal.injectScript";function lO(){return qo()}lO.N="internal.isAutoPiiEligible";function mO(a){var b=!0;return b}mO.publicName="isConsentGranted";function nO(a){var b=!1;return b}nO.N="internal.isDebugMode";function oO(){return oo()}oO.N="internal.isDmaRegion";function pO(a){var b=!1;return b}pO.N="internal.isEntityInfrastructure";function qO(){var a=!1;return a}qO.N="internal.isFpfe";function rO(){var a=!1;return a}rO.N="internal.isGcpConversion";function sO(){var a=!1;return a}sO.N="internal.isLandingPage";function tO(){var a;return a}tO.N="internal.isSafariPcmEligibleBrowser";function uO(){var a=Gh(function(b){JE(this).log("error",b)});a.publicName="JSON";return a};function vO(a){var b=void 0;return sd(b)}vO.N="internal.legacyParseUrl";function wO(){return!1}
var xO={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function yO(){}yO.publicName="logToConsole";function zO(a,b){}zO.N="internal.mergeRemoteConfig";function AO(a,b,c){c=c===void 0?!0:c;var d=[];return sd(d)}AO.N="internal.parseCookieValuesFromString";function BO(a){var b=void 0;return b}BO.publicName="parseUrl";function CO(a){}CO.N="internal.processAsNewEvent";function DO(a,b,c){var d;return d}DO.N="internal.pushToDataLayer";function EO(a){var b=ya.apply(1,arguments),c=!1;if(!eh(a))throw H(this.getName(),["string"],arguments);for(var d=[this,a],e=k(b),f=e.next();!f.done;f=e.next())d.push(rd(f.value,this.M,1));try{I.apply(null,d),c=!0}catch(g){return!1}return c}EO.publicName="queryPermission";function FO(a){var b=this;}FO.N="internal.queueAdsTransmission";function GO(a,b){var c=void 0;return c}GO.publicName="readAnalyticsStorage";function HO(){var a="";return a}HO.publicName="readCharacterSet";function IO(){return Jj.Kb}IO.N="internal.readDataLayerName";function JO(){var a="";return a}JO.publicName="readTitle";function KO(a,b){var c=this;if(!eh(a)||!ah(b))throw H(this.getName(),["string","function"],arguments);gw(a,function(d){b.invoke(c.M,sd(d,c.M,1))});}KO.N="internal.registerCcdCallback";function LO(a,b){return!0}LO.N="internal.registerDestination";var MO=["config","event","get","set"];function NO(a,b,c){}NO.N="internal.registerGtagCommandListener";function OO(a,b){var c=!1;return c}OO.N="internal.removeDataLayerEventListener";function PO(a,b){}
PO.N="internal.removeFormData";function QO(){}QO.publicName="resetDataLayer";function RO(a,b,c){var d=void 0;return d}RO.N="internal.scrubUrlParams";function SO(a){}SO.N="internal.sendAdsHit";function TO(a,b,c,d){}TO.N="internal.sendGtagEvent";function UO(a,b,c){}UO.publicName="sendPixel";function VO(a,b){}VO.N="internal.setAnchorHref";function WO(a){}WO.N="internal.setContainerConsentDefaults";function XO(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}XO.publicName="setCookie";function YO(a){if(!Yg(a))throw H(this.getName(),["Object"],arguments);I(this,"access_core_platform_services","write");Zo(rd(a));}YO.N="internal.setCorePlatformServices";function ZO(a,b){}ZO.N="internal.setDataLayerValue";function $O(a){}$O.publicName="setDefaultConsentState";function aP(a,b){}aP.N="internal.setDelegatedConsentType";function bP(a,b){}bP.N="internal.setFormAction";function cP(a,b,c){c=c===void 0?!1:c;}cP.N="internal.setInCrossContainerData";function dP(a,b,c){return!1}dP.publicName="setInWindow";function eP(a,b,c){if(!eh(a)||!eh(b)||arguments.length!==3)throw H(this.getName(),["string","string","any"],arguments);var d=ww(a)||{};d[b]=rd(c,this.M);var e=a;uw||vw();tw[e]=d;}eP.N="internal.setProductSettingsParameter";function fP(a,b,c){if(!eh(a)||!eh(b)||arguments.length!==3)throw H(this.getName(),["string","string","any"],arguments);for(var d=b.split("."),e=zq(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)e[d[f]]={};else if(!bd(e[d[f]]))throw Error("setRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}e[d[f]]=rd(c,this.M,1);}fP.N="internal.setRemoteConfigParameter";function gP(a,b){}gP.N="internal.setTransmissionMode";function hP(a,b,c,d){var e=this;}hP.publicName="sha256";function iP(a,b,c){}
iP.N="internal.sortRemoteConfigParameters";function jP(a){}jP.N="internal.storeAdsBraidLabels";function kP(a,b){var c=void 0;return c}kP.N="internal.subscribeToCrossContainerData";var lP={},mP={};lP.getItem=function(a){var b=null;return b};lP.setItem=function(a,b){};
lP.removeItem=function(a){};lP.clear=function(){};lP.publicName="templateStorage";function nP(a,b){var c=!1;return c}nP.N="internal.testRegex";function oP(a){var b;return b};function pP(a){var b;return b}pP.N="internal.unsiloId";function qP(a,b){var c;return c}qP.N="internal.unsubscribeFromCrossContainerData";function rP(a){}rP.publicName="updateConsentState";function sP(a){var b=!1;return b}sP.N="internal.userDataNeedsEncryption";var tP;function uP(a,b,c){tP=tP||new Rh;tP.add(a,b,c)}function vP(a,b){var c=tP=tP||new Rh;if(c.D.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.D[a]=eb(b)?mh(a,b):nh(a,b)}
function wP(){return function(a){var b;var c=tP;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.D.hasOwnProperty(a)){var e=this.M.D;if(e){var f=!1,g=e.Gb();if(g){th(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.D.hasOwnProperty(a)?c.D[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function xP(){var a=function(c){return void vP(c.N,c)},b=function(c){return void uP(c.publicName,c)};b(DE);b(KE);b(ZF);b(aG);b(bG);b(iG);b(kG);b(fH);b(uO());b(hH);b(DK);b(EK);b($K);b(aL);b(bL);b(hL);b(XN);b($N);b(mO);b(yO);b(BO);b(EO);b(HO);b(JO);b(UO);b(XO);b($O);b(dP);b(hP);b(lP);b(rP);uP("Math",rh());uP("Object",Ph);uP("TestHelper",Th());uP("assertApi",oh);uP("assertThat",ph);uP("decodeUri",uh);uP("decodeUriComponent",vh);uP("encodeUri",wh);uP("encodeUriComponent",xh);uP("fail",Ch);uP("generateRandom",
Dh);uP("getTimestamp",Eh);uP("getTimestampMillis",Eh);uP("getType",Fh);uP("makeInteger",Hh);uP("makeNumber",Ih);uP("makeString",Jh);uP("makeTableMap",Kh);uP("mock",Nh);uP("mockObject",Oh);uP("fromBase64",wK,!("atob"in l));uP("localStorage",xO,!wO());uP("toBase64",oP,!("btoa"in l));a(CE);a(GE);a(aF);a(mF);a(tF);a(yF);a(OF);a(XF);a($F);a(cG);a(dG);a(eG);a(fG);a(gG);a(hG);a(jG);a(lG);a(eH);a(gH);a(iH);a(kH);a(lH);a(mH);a(nH);a(oH);a(tH);a(BH);a(CH);a(NH);a(SH);a(XH);a(fI);a(kI);a(xI);a(zI);a(NI);a(OI);
a(QI);a(uK);a(vK);a(xK);a(yK);a(zK);a(AK);a(BK);a(GK);a(HK);a(IK);a(JK);a(KK);a(LK);a(MK);a(NK);a(OK);a(PK);a(QK);a(RK);a(TK);a(UK);a(VK);a(WK);a(XK);a(YK);a(ZK);a(cL);a(dL);a(eL);a(fL);a(gL);a(jL);a(VN);a(ZN);a(bO);a(kO);a(lO);a(nO);a(oO);a(pO);a(qO);a(rO);a(sO);a(tO);a(vO);a(MF);a(zO);a(AO);a(CO);a(DO);a(FO);a(IO);a(KO);a(LO);a(NO);a(OO);a(PO);a(RO);a(SO);a(TO);a(VO);a(WO);a(YO);a(ZO);a(aP);a(bP);a(cP);a(eP);a(fP);a(gP);a(iP);a(jP);a(kP);a(nP);a(pP);a(qP);a(sP);vP("internal.CrossContainerSchema",
jH());vP("internal.IframingStateSchema",YN());D(104)&&a(FK);D(160)?b(kO):b(hO);D(177)&&b(GO);return wP()};var AE;
function yP(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;AE=new Oe;zP();sf=zE();var e=AE,f=xP(),g=new kd("require",f);g.fb();e.D.D.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&Of(n,d[m]);try{AE.execute(n),D(120)&&Yk&&n[0]===50&&h.push(n[1])}catch(r){}}D(120)&&(Ff=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,"");Xj[q]=
["sandboxedScripts"]}AP(b)}function zP(){AE.D.D.O=function(a,b,c){jp.SANDBOXED_JS_SEMAPHORE=jp.SANDBOXED_JS_SEMAPHORE||0;jp.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{jp.SANDBOXED_JS_SEMAPHORE--}}}function AP(a){a&&nb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");Xj[e]=Xj[e]||[];Xj[e].push(b)}})};function BP(a){pw(jw("developer_id."+a,!0),0,{})};var CP=Array.isArray;function DP(a,b){return cd(a,b||null)}function W(a){return window.encodeURIComponent(a)}function EP(a,b,c){Bc(a,b,c)}function FP(a,b){if(!a)return!1;var c=Bk(Hk(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}
function GP(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}var PP=l.clearTimeout,QP=l.setTimeout;function RP(a,b,c){if(yr()){b&&A(b)}else return xc(a,b,c,void 0)}function SP(){return l.location.href}function TP(a,b){return hk(a,b||2)}function UP(a,b){l[a]=b}function VP(a,b,c){b&&(l[a]===void 0||c&&!l[a])&&(l[a]=b);return l[a]}function WP(a,b){if(yr()){b&&A(b)}else zc(a,b)}

var XP={};var Z={securityGroups:{}};

Z.securityGroups.v=["google"],Z.__v=function(a){var b=a.vtp_name;if(!b||!b.replace)return!1;var c=TP(b.replace(/\\\./g,"."),a.vtp_dataLayerVersion||1);return c!==void 0?c:a.vtp_defaultValue},Z.__v.H="v",Z.__v.isVendorTemplate=!0,Z.__v.priorityOverride=0,Z.__v.isInfrastructure=!0,Z.__v.runInSiloedMode=!1;

Z.securityGroups.rep=["google"],Z.__rep=function(a){var b=Dm(a.vtp_containerId),c=vp(b,!0);if(c){var d,e;switch(c.prefix){case "AW":d=UI;e=Sm.Z.Ea;break;case "DC":d=kJ;e=Sm.Z.Ea;break;case "GF":d=pJ;e=Sm.Z.Eb;break;case "HA":d=vJ;e=Sm.Z.Eb;break;case "UA":d=TJ;e=Sm.Z.Eb;break;case "MC":d=TN(c,a.vtp_gtmEventId);e=Sm.Z.Fc;break;default:A(a.vtp_gtmOnFailure);return}d?(A(a.vtp_gtmOnSuccess),D(185)?vq(b,d,e,a.vtp_remoteConfig):(vq(a.vtp_containerId,d,e),a.vtp_remoteConfig&&Bq(b,a.vtp_remoteConfig||{}))):
A(a.vtp_gtmOnFailure)}else A(a.vtp_gtmOnFailure)},Z.__rep.H="rep",Z.__rep.isVendorTemplate=!0,Z.__rep.priorityOverride=0,Z.__rep.isInfrastructure=!1,Z.__rep.runInSiloedMode=!0;
Z.securityGroups.access_core_platform_services=["google"],function(){function a(b,c){var d={write:!1};switch(c){case "write":d.write=!0;break;default:throw Error("Invalid "+b+" request "+c);}return d}(function(b){Z.__access_core_platform_services=b;Z.__access_core_platform_services.H="access_core_platform_services";Z.__access_core_platform_services.isVendorTemplate=!0;Z.__access_core_platform_services.priorityOverride=0;Z.__access_core_platform_services.isInfrastructure=!1;Z.__access_core_platform_services.runInSiloedMode=
!1})(function(b){var c=b.vtp_writeAllCps,d=b.vtp_createPermissionError;return{assert:function(e,f){if(f==="write"){if(c)return}else throw d(e,{},"Access type must be 'write', was '"+f+"'");throw d(e,{},"Prohibited "+f+".");},U:a}})}();
Z.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_event_data=b;Z.__read_event_data.H="read_event_data";Z.__read_event_data.isVendorTemplate=!0;Z.__read_event_data.priorityOverride=0;Z.__read_event_data.isInfrastructure=!1;Z.__read_event_data.runInSiloedMode=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!gb(g))throw e(f,{key:g},"Key must be a string.");
if(c!=="any"){try{if(c==="specific"&&g!=null&&Cg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},U:a}})}();





Z.securityGroups.read_container_data=["google"],Z.__read_container_data=function(){return{assert:function(){},U:function(){return{}}}},Z.__read_container_data.H="read_container_data",Z.__read_container_data.isVendorTemplate=!0,Z.__read_container_data.priorityOverride=0,Z.__read_container_data.isInfrastructure=!1,Z.__read_container_data.runInSiloedMode=!1;

Z.securityGroups.detect_user_provided_data=["google"],function(){function a(b,c){return{dataSource:c}}(function(b){Z.__detect_user_provided_data=b;Z.__detect_user_provided_data.H="detect_user_provided_data";Z.__detect_user_provided_data.isVendorTemplate=!0;Z.__detect_user_provided_data.priorityOverride=0;Z.__detect_user_provided_data.isInfrastructure=!1;Z.__detect_user_provided_data.runInSiloedMode=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(e!=="auto"&&e!=="manual"&&
e!=="code")throw c(d,{},"Unknown user provided data source.");if(b.vtp_limitDataSources)if(e!=="auto"||b.vtp_allowAutoDataSources){if(e==="manual"&&!b.vtp_allowManualDataSources)throw c(d,{},"Detection of user provided data via manually specified CSS selectors is not allowed.");if(e==="code"&&!b.vtp_allowCodeDataSources)throw c(d,{},"Detection of user provided data from an in-page variable is not allowed.");}else throw c(d,{},"Automatic detection of user provided data is not allowed.");},U:a}})}();









Z.securityGroups.get=["google"],Z.__get=function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=mw(String(b.streamId),d,c);pw(f,e.eventId,e);a.vtp_gtmOnSuccess()},Z.__get.H="get",Z.__get.isVendorTemplate=!0,Z.__get.priorityOverride=0,Z.__get.isInfrastructure=!1,Z.__get.runInSiloedMode=!1;




var mp={dataLayer:ik,callback:function(a){Wj.hasOwnProperty(a)&&eb(Wj[a])&&Wj[a]();delete Wj[a]},bootstrap:0};
function YP(){lp();Gm();tB();xb(Xj,Z.securityGroups);var a=Bm(Cm()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;Ko(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||M(142);Ef={Bo:Uf}}var ZP=!1;
function io(){try{if(ZP||!Pm()){Ij();Fj.T="";
Fj.Fb="ad_storage|analytics_storage|ad_user_data|ad_personalization";Fj.Za="ad_storage|analytics_storage|ad_user_data";Fj.Ca="55j0";Fj.Ca="55j0";Em();if(D(109)){}lg[8]=!0;var a=kp("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});Ro(a);ip();rE();$q();pp();if(Hm()){JF();dB().removeExternalRestrictions(zm());}else{wy();pB();Cf();yf=Z;zf=bE;Wf=new cg;yP();YP();go||(fo=ko());
fp();rD();EC();YC=!1;y.readyState==="complete"?$C():Cc(l,"load",$C);yC();Yk&&(dq(rq),l.setInterval(qq,864E5),dq(tE),dq(WB),dq(Dz),dq(uq),dq(wE),dq(gC),D(120)&&(dq(aC),dq(bC),dq(cC)));Zk&&(Kn(),Kp(),tD(),xD(),vD(),An("bt",String(Fj.D?2:Fj.J?1:0)),An("ct",String(Fj.D?0:Fj.J?1:yr()?2:3)),uD());
SD();Vn(1);KF();Vj=ub();mp.bootstrap=Vj;Fj.R&&qD();D(109)&&Wz();D(134)&&(typeof l.name==="string"&&zb(l.name,"web-pixel-sandbox-CUSTOM")&&Sc()?BP("dMDg0Yz"):l.Shopify&&(BP("dN2ZkMj"),Sc()&&BP("dNTU0Yz")))}}}catch(b){Vn(4),nq()}}
(function(a){function b(){n=y.documentElement.getAttribute("data-tag-assistant-present");xo(n)&&(m=h.ml)}function c(){m&&oc?g(m):a()}if(!l["__TAGGY_INSTALLED"]){var d=!1;if(y.referrer){var e=Hk(y.referrer);d=Dk(e,"host")==="cct.google"}if(!d){var f=Jr("googTaggyReferrer");d=!(!f.length||!f[0].length)}d&&(l["__TAGGY_INSTALLED"]=!0,xc("https://cct.google/taggy/agent.js"))}var g=function(u){var v="GTM",w="GTM";Pj&&(v="OGT",w="GTAG");var x=l["google.tagmanager.debugui2.queue"];x||(x=
[],l["google.tagmanager.debugui2.queue"]=x,xc("https://"+Jj.xg+"/debug/bootstrap?id="+$f.ctid+"&src="+w+"&cond="+u+"&gtm="+Ar()));var z={messageType:"CONTAINER_STARTING",data:{scriptSource:oc,containerProduct:v,debug:!1,id:$f.ctid,targetRef:{ctid:$f.ctid,isDestination:qm()},aliases:tm(),destinations:rm()}};z.data.resume=function(){a()};Jj.Pm&&(z.data.initialPublish=!0);x.push(z)},h={Un:1,pl:2,Dl:3,hk:4,ml:5};h[h.Un]="GTM_DEBUG_LEGACY_PARAM";h[h.pl]="GTM_DEBUG_PARAM";h[h.Dl]="REFERRER";h[h.hk]="COOKIE";h[h.ml]="EXTENSION_PARAM";
var m=void 0,n=void 0,p=Bk(l.location,"query",!1,void 0,"gtm_debug");xo(p)&&(m=h.pl);if(!m&&y.referrer){var q=Hk(y.referrer);Dk(q,"host")==="tagassistant.google.com"&&(m=h.Dl)}if(!m){var r=Jr("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.hk)}m||b();if(!m&&wo(n)){var t=!1;Cc(y,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);l.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){D(83)&&ZP&&!ko()["0"]?ho():io()});

})()

