
/*@preserve
***Version 2.30.0***
*/

/*@license
 *                       Copyright 2002 - 2018 Qualtrics, LLC.
 *                                All rights reserved.
 *
 * Notice: All code, text, concepts, and other information herein (collectively, the
 * "Materials") are the sole property of Qualtrics, LLC, except to the extent
 * otherwise indicated. The Materials are proprietary to Qualtrics and are protected
 * under all applicable laws, including copyright, patent (as applicable), trade
 * secret, and contract law. Disclosure or reproduction of any Materials is strictly
 * prohibited without the express prior written consent of an authorized signatory
 * of Qualtrics. For disclosure requests, <NAME_EMAIL>.
 */

try {
  (window["WAFQualtricsWebpackJsonP-cloud-2.30.0"]=window["WAFQualtricsWebpackJsonP-cloud-2.30.0"]||[]).push([[8],{17:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"e",(function(){return r})),n.d(t,"f",(function(){return a})),n.d(t,"d",(function(){return s})),n.d(t,"c",(function(){return c})),n.d(t,"b",(function(){return u}));var o=[],i=function(e){var t=e;return"string"==typeof e&&(t=document.getElementById(e)),t};function r(e,t,n){try{var o=e[t];return e[t]=n(o),function(){e[t]=o}}catch(e){return function(){}}}var a=function(e,t){var n;13!==e.which&&32!==e.which||(e.preventDefault(),(n=document.querySelector('button, a[href], input, select, textarea, [tabindex]:not([tabindex="-1"])'))&&n.focus(),t())},s=function(e,t,n,i){void 0===i&&(i=!1),o=o||[],e&&(o.push({elementToObserve:e,eventName:t,eventHandler:n,preventRemove:i||!1}),e.addEventListener(t,n,!1))},c=function(e){return"string"==typeof e},u=function(e){return"object"==typeof e&&e instanceof Array}},26:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var o=n(0),i=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var o,i,r=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(o=r.next()).done;)a.push(o.value)}catch(e){i={error:e}}finally{try{o&&!o.done&&(n=r.return)&&n.call(r)}finally{if(i)throw i.error}}return a},r=function(){function e(t,n){var r=this;this.components=e.components,this.overallLatencyStartTime=null,this.overallLatency=null,this.componentStartTimes={},this.componentLatencies={},this.componentStatuses={},this.isDuplicateScriptExecution=!1,this.setRequestId=function(e){r.requestId=e},this.startTimer=function(){r.latencyStartTime=Date.now()},this.endTimer=function(){null!==r.latencyStartTime?r.overallLatency=Date.now()-r.latencyStartTime:r.debugLog.e("Tried to log overall end time without a start time.")},this.startComponentTimer=function(e){r.componentStartTimes[e]=Date.now()},this.endComponentTimer=function(e){var t=r.componentStartTimes[e];void 0!==t?(r.componentLatencies[e]=Date.now()-t,r.debugLog.safeConsole("log","Component "+e+" loaded in "+r.componentLatencies[e]+"ms")):r.debugLog.e("Tried to log a component end time without a component start time.  Component name: "+e)},this.setComponentStatus=function(e,t){r.componentStatuses[e]=t},this.markComponentAlreadyFetched=function(t){r.componentLatencies[t]||(r.componentLatencies[t]=e.alreadyFetchedMarker)},this.markDuplicateScriptExecution=function(){r.isDuplicateScriptExecution=!0},this.getPerformanceResourceTiming=function(){try{var e={},t=0,n=window.QSI.global,o=n.baseURL,r=n.hostedJSLocation,a=window.QSI.Orchestrator.scriptSrc;if(!o||!r||!a)return null;var s=new RegExp(a.split("?")[0]),c=new RegExp(r),u=new RegExp(o),l=new RegExp(c.source+"|"+u.source+"|"+s.source);return performance.getEntriesByType("resource").filter((function(e){return e.name.match(l)})).forEach((function(n){var o=n.name.split("?")[0],r=!1,a=o.match(c),u=o.match(s)&&"script"===n.initiatorType,l=o.match(/(Asset|Targeting)\.php/);if(a)o=o.split("dxjsmodule/")[1],r=!0;else if(u)o="Orchestrator",r=!0;else{if(!l)return;o=i(l,1)[0]}var d=n.transferSize,g=n.duration,f=n.decodedBodySize,p=n.domainLookupStart,S=n.domainLookupEnd;r&&(t+=d),e[o]={transferSize:d,decodedBodySize:f,totalLatency:g,dnsLatency:S-p}})),t>0&&(e.totalJSTransfer=t),e}catch(e){return null}},this.send=function(e){try{if(null===r.overallLatency)return void r.debugLog.e("Incomplete Latency Data Provided");var t=o.a.baseURL+"Ajax.php?action=LatencyLog&"+o.a.getClientVersionQueryString(),n=r.buildLoggingData(e);QSI.util.sendHttpRequest({type:"POST",url:t,header:{"Content-type":"application/x-www-form-urlencoded"},includeCookies:!1,data:QSI.util.buildQueryString({LoggingData:JSON.stringify(n)})})}catch(e){r.debugLog.e(e)}},this.buildLoggingData=function(e){if(e&&e.length>0){var t={},n={};return e.forEach((function(e){e in r.componentLatencies&&(t[e]=r.componentLatencies[e]),e in r.componentStatuses&&(n[e]=r.componentStatuses[e])})),{MetricName:r.metricName,Latency:-1,ComponentLatencies:t,ComponentStatuses:n,AdditionalData:{RequestID:r.requestId,UserAgent:navigator.userAgent,BrandID:o.a.global.brandID,BrandDC:o.a.global.brandDC,OtherRequestIDsExecuted:[],IsDuplicateScriptExecution:!1}}}var i=[];for(var a in o.a.Request)Object.prototype.hasOwnProperty.call(o.a.Request,a)&&a!==r.requestId&&i.push(a);var s={};for(var c in r.componentLatencies)c.startsWith(r.components.SR)||(s[c]=r.componentLatencies[c]);var u={};for(var c in r.componentStatuses)c.startsWith(r.components.SR)||(u[c]=r.componentStatuses[c]);var l={MetricName:r.metricName,Latency:r.overallLatency,ComponentLatencies:s,ComponentStatuses:u,AdditionalData:{RequestID:r.requestId,UserAgent:navigator.userAgent,BrandID:o.a.global.brandID,BrandDC:o.a.global.brandDC,OtherRequestIDsExecuted:i,IsDuplicateScriptExecution:r.isDuplicateScriptExecution}},d=r.getPerformanceResourceTiming();return d&&Object.keys(d).length&&(l.PerformanceResourceTiming=d),l},this.sampledSend=function(e){try{Math.random()<=e/100&&r.send()}catch(e){r.debugLog.e(e)}},this.sampledComponentSend=function(e,t){try{Math.random()<=e/100&&r.send(t)}catch(e){r.debugLog.e(e)}},this.debugLog=t,this.metricName=null!=n?n:"si.SILatency"}return e.alreadyFetchedMarker="ALREADY_FETCHED",e.components={CORE_MODULE:"coreModuleRequest",TARGETING:"targetingRequest",DPR_TARGETING:"dprTargetingRequest",XMD_DEBUG_TARGETING:"xmdDebugTargetingRequest",ASSETS_AND_MODULES:"allAssetDefinitionsAndJSModules",CONTACT_FREQUENCY:"contactFrequencyCheck",SR:"sessionReplayRequest"},e}()},27:function(e,t,n){"use strict";var o,i;n.d(t,"e",(function(){return o})),n.d(t,"d",(function(){return i})),n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return s})),function(e){e.ISOLATION_ERROR="ISOLATION_ERROR",e.NOT_ISOLATED="NOT_ISOLATED",e.ISOLATED_V1="ISOLATED_V1"}(o||(o={})),function(e){e.NOT_ISOLATED="not_isolated",e.ISOLATED_V1="isolated_v1"}(i||(i={}));var r,a,s;!function(e){e.IPAddress="IPAddress"}(r||(r={})),function(e){e.StartingUrl="StartingUrl",e.Device="Device",e.UserAgent="UserAgent",e.Referrer="Referrer",e.ExtRefId="ExtRefId",e.DirectoryId="DirectoryId",e.ScreenResolutionWidth="ScreenResolutionWidth",e.ScreenResolutionHeight="ScreenResolutionHeight",e.BrowserWidth="BrowserWidth",e.BrowserHeight="BrowserHeight",e.Browser="Browser",e.OS="OperatingSystem",e.Country="Country"}(a||(a={})),function(e){e.RageClick="RageClick",e.MouseThrash="MouseThrash",e.DeadClick="DeadClick",e.ErrorClick="ErrorClick"}(s||(s={}))},3:function(e,t,n){"use strict";n.r(t);n(43),n(44),n(45),n(46);var o,i,r,a,s,c,u,l=n(17),d=function(){var e=this;this.trackElements=function(){if(0==e.loaded){e.loadCounts();var t=window.QSI,n=t.dbg,o=t.global.eventTrackers;Object.keys(o).forEach((function(t){var n=o[t];e.trackElement(n,t)})),Object(l.d)(window,"beforeunload",(function(){try{e.storeCounts()}catch(e){void 0!==window.QSI&&n&&n.e&&n.e(e)}})),Object(l.d)(window,"pagehide",(function(){try{e.storeCounts()}catch(e){void 0!==window.QSI&&n&&n.e&&n.e(e)}})),e.loaded=!0}},this.trackElement=function(t,n){var o=window.QSI.dbg,i=Object(l.a)(t);i&&Object(l.d)(i,"click",(function(){try{e.track(n)}catch(e){void 0!==window.QSI&&o&&o.e&&o.e(e)}}))},this.track=function(t){e.clicked=!0,e.counts[t]?e.counts[t]++:e.counts[t]=1},this.storeCounts=function(){if(!0===e.clicked){var t=window.QSI.cookie,n=JSON.stringify(e.counts);t.set(e.cookieName,n)}},this.loadCounts=function(){var t=window.QSI.cookie.get(e.cookieName);t&&(e.counts=JSON.parse(t))},this.get=function(t){return e.counts[t]?e.counts[t]:0},this.incrementEventList=function(){if("_qsie"in window&&Object(l.b)(window._qsie))for(var t=0,n=window._qsie.length;t<n;t++){var o=window._qsie[t];Object(l.c)(o)&&e.track(o)}},this.counts={},this.cookieName="QSI_CT",this.loaded=!1,this.clicked=!1},g=n(27),f=function(){function e(t){this.dataLayer=t,!o&&t.push&&(o=t.push,t.push=e.qualtricsPush)}return e.prototype.get=function(e,t){void 0===t&&(t=function(){return null});try{var n=window.QSI.config.gtmContainerID;if(!n)throw new Error("Data layer value retrieval failed because of missing Google Tag Manager container id");if(!window.google_tag_manager[n])throw new Error("Google Tag Manager container with id '"+n+"' does not exist");return window.google_tag_manager[n].dataLayer.get(e)}catch(e){return t()}},e.qualtricsPush=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=[].slice.call(e,0),i=o.apply(void 0,n);if(window.QSI){var r=window.QSI.InterceptReevaluator,a=window.QSI.DataLayerHelper;if(r.isActive()&&a)try{r.debouncedEvaluate()}catch(e){var s=window.QSI.dbg;s.e(e)}}return i},e}(),p=function(){var e=window.QSI.DataLayerHelper;if(null==e){var t=window.QSI.config.gtmContainerID;if(!t)throw new Error("Google Tag Manager container ID was not provided and is needed for the Qualtrics data layer integration");if(!window.google_tag_manager||!window.google_tag_manager[t])throw new Error("Google Tag Manager container '"+t+"' does not exist");var n=window.google_tag_manager[t].dataLayer.name;if(!n)throw new Error("Google Tag Manager container '"+t+"' does not have a data layer name");var o=window[n];e=new f(o),window.QSI.DataLayerHelper=e}return window.QSI.InterceptReevaluator.isActive()||window.QSI.InterceptReevaluator.activate(),e},S=n(47),h=n.n(S),I=function(e,t,n,o){return new(n||(n=Promise))((function(i,r){function a(e){try{c(o.next(e))}catch(e){r(e)}}function s(e){try{c(o.throw(e))}catch(e){r(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((o=o.apply(e,t||[])).next())}))},w=function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(r){return function(s){return function(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==r[0]&&2!==r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(e){r=[6,e],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,s])}}},v=function(){function e(){var t=this;this.evaluationResults={},this.oldEvaluationResults={},this.moduleActive=!1,this.debouncedEvaluate=h()((function(){t.evaluateIntercepts()}),e.evaluateDebounceTimeout),this.debouncedEvaluateAndRun=h()((function(){t.evaluateIntercepts().then((function(){window.QSI.RunIntercepts(null,!0)}))}),e.evaluateDebounceTimeout),this.interceptHasRun=new Set,this.interceptsWithFrustrationLogic=new Set,this.moduleActive=!1}return e.prototype.activate=function(){this.moduleActive=!0},e.prototype.isActive=function(){return this.moduleActive},e.prototype.hasInterceptAlreadyRun=function(e){return this.interceptHasRun.has(e)},e.prototype.setInterceptHasRun=function(e){this.interceptHasRun.add(e)},e.prototype.resetInterceptHasRun=function(){this.interceptHasRun=new Set},e.prototype.setOldEvaluationResults=function(e){this.oldEvaluationResults=JSON.parse(JSON.stringify(e))},e.prototype.hasFrustrationSignalLogic=function(e){return this.interceptsWithFrustrationLogic.has(e)},e.prototype.addInterceptWithFrustrationSignalLogic=function(e){this.interceptsWithFrustrationLogic.add(e)},e.prototype.getInterceptEvaluationResult=function(e){return this.evaluationResults[e]},e.prototype.setInterceptEvaluationResult=function(e,t){this.evaluationResults[e]=t},e.prototype.resetEvaluationResults=function(){this.evaluationResults={}},e.prototype.evaluateIntercepts=function(){return I(this,void 0,void 0,(function(){var e,t,o,i,r,a,s,c;return w(this,(function(u){switch(u.label){case 0:return e=window.QSI,t=e.Orchestrator,o=t.csTargetingParams,i=o.targetingResponse,r=o.targetingRequest,a=o.clientVersion,s=o.assetLoadingParams,[4,t.doCSTargetingEvaluation(i,r,a)];case 1:return u.sent(),(e.config.debug||e.isDebug)&&this.refreshDebug(i),i.Intercepts.length>0?(n.e(5).then(n.bind(null,42)).then((function(e){(0,e.addPopunderEmbeddedDataHandler)(e.updatePopunderEDCallback)})),c=t.Deferred(),s.deferred=c,t.loadModules(s,i),[2,new Promise((function(e,t){c.promise().then(e).fail(t)}))]):[2]}}))}))},e.prototype.refreshDebug=function(e){var t=this.oldEvaluationResults,n=this.evaluationResults;if(JSON.stringify(t)!==JSON.stringify(n)){var o={debugInfo:JSON.parse(e.DebugInfo)},i=window.QSI;i.debuggerHasDisplayed=!1,window.QSI.debugHandlerInstance=new i.DebugHandler(o),i.debuggerHasDisplayed=!0}else window.QSI.debugHandlerInstance&&window.QSI.foundFrustrationLogic&&window.QSI.InterceptReevaluator.isActive()&&window.QSI.debugHandlerInstance.refreshFrustrationSignalCounts();this.setOldEvaluationResults(n)},e.evaluateDebounceTimeout=100,e}(),y=function(){function e(e,t,n){this.retryerName=e,this.retryLimit=t,this.retryBackoff=n,this.retryCount=0}return e.prototype.backOffAndRetry=function(e){this.retryCount<this.retryLimit?(this.retryCount++,setTimeout(e,this.retryBackoff)):window.QSI.dbg.es("Retryer for "+this.retryerName+" - Exceeded retry limit of "+this.retryLimit+"; No longer retrying")},e.prototype.getRetryCount=function(){return this.retryCount},e.prototype.resetRetryCount=function(){this.retryCount=0},e}(),b=n(26),m=n(0);!function(e){e.AND="AND",e.OR="OR"}(i||(i={})),function(e){e.EQ="EQ",e.NEQ="NEQ",e.GT="GT",e.GTE="GTE",e.LT="LT",e.LTE="LTE",e.IS_EMPTY="IS_EMPTY",e.NOT_EMPTY="NOT_EMPTY",e.CONTAINS="CONTAINS",e.NOT_CONTAINS="NOT_CONTAINS",e.STARTS_WITH="STARTS_WITH",e.REGEX="REGEX",e.HAS_BEEN_TAKEN="HAS_BEEN_TAKEN",e.HAS_NOT_BEEN_TAKEN="HAS_NOT_BEEN_TAKEN",e.HAS_BEEN_SEEN="HAS_BEEN_SEEN",e.HAS_NOT_BEEN_SEEN="HAS_NOT_BEEN_SEEN",e.SAMPLING="SAMPLING"}(r||(r={})),function(e){e.VALUE_NODE="ValueNode",e.COMPARATOR_NODE="ComparatorNode",e.DEBUG_COMPARATOR_NODE="DebugComparatorNode",e.CONJUNCTION_NODE="ConjunctionNode",e.LOGIC_NODE="LogicNode",e.FAILURE_NODE="FailureNode"}(a||(a={})),function(e){e.ACTION_SET_LOGIC="ActionSetLogic",e.ADOBE_ANALYTICS="AdobeAnalyticsVariable",e.COOKIE="Cookie",e.EVENT_TRACKING="EventTracking",e.HTML_ON_SITE="HtmlOnSite",e.SITE_HISTORY="SiteHistory",e.INTERCEPT_LOGIC="InterceptLogic",e.INTERCEPT_LOGIC_ANY="InterceptLogicAny",e.INTERCEPT_LOGIC_NONE="InterceptLogicNone",e.JAVASCRIPT_EXPRESSION="JavaScriptExpression",e.PAGE_COUNT="PageCount",e.REFERRER="Referrer",e.RESOLUTION="Resolution",e.SEARCH_TERM="SearchTerm",e.TIME_ON_SITE="TimeOnSite",e.DATE_TIME_LOGIC="DateTime",e.BROWSER="Browser",e.DEVICE_TYPE="DeviceType",e.SAMPLING="Sampling",e.CURRENT_URL="CurrentUrl",e.QUERY_PARAM="QueryParameter",e.LOCATION="Location",e.IP_ADDRESS="IpAddress",e.USER_AGENT="UserAgent",e.QUALTRICS_SURVEY="QualtricsSurvey",e.SEGMENTATION="Segmentation",e.GOOGLE_DATA_LAYER="GoogleDataLayer",e.RAGE_CLICK="RageClick",e.MOUSE_THRASH="MouseThrash",e.ERROR_CLICK="ErrorClick",e.DEAD_CLICK="DeadClick"}(s||(s={})),function(e){e.ACTION_SET_LOGIC="ActionSetLogic",e.ADOBE_ANALYTICS="AdobeAnalyticsVariable",e.COOKIE="Cookie",e.EVENT_TRACKING="EventTracking",e.HTML_ON_SITE="HtmlOnSite",e.SITE_HISTORY="SiteHistory",e.INTERCEPT_LOGIC="InterceptLogic",e.INTERCEPT_LOGIC_ANY="InterceptLogicAny",e.INTERCEPT_LOGIC_NONE="InterceptLogicNone",e.JAVASCRIPT_EXPRESSION="JavaScriptExpression",e.PAGE_COUNT="PageCount",e.REFERRER="Referrer",e.RESOLUTION="Resolution",e.SEARCH_TERM="SearchTerm",e.TIME_ON_SITE="TimeOnSite",e.DATE_TIME_LOGIC="DateTime",e.GOOGLE_DATA_LAYER="GoogleDataLayer",e.RAGE_CLICK="RageClick",e.MOUSE_THRASH="MouseThrash",e.ERROR_CLICK="ErrorClick",e.DEAD_CLICK="DeadClick"}(c||(c={})),function(e){e.ANY="Any",e.FIRST="First",e.LAST="Last",e.ONLY="Only"}(u||(u={}));var R=function(){return(R=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},D=function(e,t,n,o){return new(n||(n=Promise))((function(i,r){function a(e){try{c(o.next(e))}catch(e){r(e)}}function s(e){try{c(o.throw(e))}catch(e){r(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((o=o.apply(e,t||[])).next())}))},Q=function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(r){return function(s){return function(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==r[0]&&2!==r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(e){r=[6,e],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,s])}}},E=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var o,i,r=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(o=r.next()).done;)a.push(o.value)}catch(e){i={error:e}}finally{try{o&&!o.done&&(n=r.return)&&n.call(r)}finally{if(i)throw i.error}}return a},C=function(e,t){for(var n=0,o=t.length,i=e.length;n<o;n++,i++)e[i]=t[n];return e},T=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],o=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&o>=e.length&&(e=void 0),{value:e&&e[o++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},O=function(){function e(){var e=this;this.init=function(t,o,i){e.latencyLog=t,e.debugLog=o,e.scriptSrc=i,e.defaultClientSideDelayMilliseconds=5e3,e.setupJFEMessageEventHandlerForIOSOptimization(),window.QSI.windows||(window.QSI.windows={}),window.QSI.targetWindows||(window.QSI.targetWindows=[]),window.addEventListener("message",e.addWindowHandlersPostMessageListener),e.latencyLog.startTimer(),m.a.config.evaluate&&e.handleSPAEvaluation(m.a.config.evaluate),window.QSI.InitializeDataLayerHelper=p,window.QSI.foundFrustrationLogic=!1,window.QSI.InterceptReevaluator=new v,m.a.Request||(m.a.Request={}),m.a.debugConfig=m.a.debugConfig||{};var r,a=m.a.global.legacyId,s={id:a};if(e.latencyLog.setRequestId(a),~Object.keys(m.a.Request).indexOf(a)&&t.markDuplicateScriptExecution(),0===a.indexOf("ZN")?s.ZoneID=a:s.InterceptID=a,m.a.global.isHostedJS())(void 0!==(r=e.parseQueryString(window.location.href)).Q_DEBUG||m.a.config.debug)&&(s.Q_DEBUG=null,e.debugLog.enableFullDebug()),(m.a.config.editing||"0"===m.a.global.version)&&(s.version="0"),e.debugLog.safeConsole("log","DX Tag Loading HostedJS version "+m.a.global.clientVersion);else{var c=void 0;if(i)c=i;else try{var u=document.querySelectorAll("script"),l=[];for(var d in u)Object.prototype.hasOwnProperty.call(u,d)&&(l[d]=u[d]);var g=m.a.global.baseURL;0===g.indexOf("https://")?g=g.substring(8):0===g.indexOf("http://")?g=g.substring(7):0===g.indexOf("//")&&(g=g.substring(2)),c=l.filter((function(e){return(-1!==e.src.indexOf(g+"/WRSiteInterceptEngine/?")||-1!==e.src.indexOf(g+"/SIE/?"))&&(-1===e.src.indexOf("Q_Impress")&&-1===e.src.indexOf("Q_Redirect")&&-1===e.src.indexOf("Q_Click")&&-1===e.src.indexOf("Q_DPR"))}))[0].src}catch(t){e.debugLog.e("An error occurred while loading the intercept. "+t)}void 0!==(r=e.parseQueryString(c)).Q_NOCACHE&&(s.Q_NOCACHE=null),void 0!==r.Q_BOOKMARKLET&&(s.Q_BOOKMARKLET=null,s.Q_DEBUG=null,e.debugLog.enableFullDebug()),void 0!==r.Q_DEBUG&&(s.Q_DEBUG=null,e.debugLog.enableFullDebug()),void 0!==r.Q_VERSION&&(s.version=r.Q_VERSION)}return void 0!==m.a.global.clientVersion&&null!==m.a.global.clientVersion&&(s.Q_CLIENTVERSION=m.a.global.clientVersion),void 0!==m.a.global.clientType&&null!==m.a.global.clientType&&(s.Q_CLIENTTYPE=m.a.global.clientType,void 0!==m.a.clientTypeVariant&&(s.Q_CLIENTTYPE+=m.a.clientTypeVariant)),-1!==window.location.search.indexOf("Q_WAF_PREVIEWER")?Promise.all([n.e(20),n.e(1),n.e(17)]).then(n.bind(null,78)).then((function(e){new(0,e.LivePreviewer)})).catch((function(t){e.debugLog.e("An error occurred while loading the live previewer. "+t)})):(e.load(s,!1),Promise.resolve(null))},this.loadAndStartSessionRecording=function(){return D(e,void 0,void 0,(function(){var e;return Q(this,(function(t){return(e=window.QSI.API).load().then((function(){return e.SessionRecording.start()})),[2]}))}))},this.debouncedLoadAndStartSessionRecording=this.debounce(this.loadAndStartSessionRecording,5e3),this.isSRThrottled=function(t){var n=e.getSRThrottledCookieName(t);return!!window.QSI.cookie.get(n)},this.getSRThrottledCookieName=function(e){return"QSI_ReplaySession_Throttled_"+e},this.generateQueryString=m.a.generateQueryString,this.getClientVersionQueryString=m.a.getClientVersionQueryString,this.Deferred=function(){var t={},n="pending",o=[],i=[],r=[],a=[],s=e,c={state:function(){return n},then:function(e,t){return this.done(e).fail(t),this},done:function(e){if("pending"===n&&e)r.push(e);else if("resolved"===n)try{e.apply(this,o)}catch(e){s.debugLog.e(e)}return this},fail:function(e){if("pending"===n&&e)a.push(e);else if("rejected"===n)try{e.apply(this,i)}catch(e){s.debugLog.e(e)}return this},promise:function(){return c}};return e.forOwn(c,(function(e,n){t[n]=c[n]})),t.resolve=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];"pending"===n&&(n="resolved",o=e,s.each(r,(function(t){try{t.apply(s,e)}catch(e){s.debugLog.e(e)}})))},t.reject=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];"pending"===n&&(n="rejected",i=e,s.each(a,(function(t){try{t.apply(s,e)}catch(e){s.debugLog.e(e)}})))},t},this.getInterceptFromSiResponse=function(t){var n=e.lastSiResponse;if(n&&n.ClientSideIntercepts){for(var o=null,i=0;i<n.ClientSideIntercepts.length;i++){if((a=n.ClientSideIntercepts[i]).InterceptID===t){o=a;break}}var r=null;if(n.Intercepts)for(i=0;i<n.Intercepts.length;i++){var a;if((a=n.Intercepts[i]).InterceptID===t){r=a;break}}return[o,r]}return null}}return e.prototype.debounce=function(e,t){return function(){for(var n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];window.QSI.global.srDebounce&&clearTimeout(window.QSI.global.srDebounce),window.QSI.global.srDebounce=window.setTimeout((function(){e.apply(void 0,C([],E(n)))}),t)}},e.prototype.doSPAReload=function(){var e;if(window.QSI&&window.QSI.API){var t=window.QSI,n=t.API,o=t.global,i=null!==(e=!!t.SR)&&void 0!==e&&e;n.unloadForSPA(i),o.featureFlags["DX.DXA_Auto_SPA"]&&(i||window.QSI.global.srDebounce)?this.debouncedLoadAndStartSessionRecording():setTimeout((function(){n.load()}),100)}},e.prototype.handleSPAEvaluation=function(e){var t=this;if(e&&!window.QSI.spaEvaluation)if(window.QSI.spaEvaluation=!0,e.onUrlPathChange){if(window.history.pushState){var n=window.history.pushState;window.history.pushState=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];n.apply(window.history,e);try{this.doSPAReload()}catch(e){this.debugLog.e("An error occurred while handling SPA reload on pushState. "+e)}}.bind(this)}if(window.history.replaceState){var o=window.history.replaceState;window.history.replaceState=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];o.apply(window.history,e);try{this.doSPAReload()}catch(e){this.debugLog.e("An error occurred while handling SPA reload on replaceState. "+e)}}.bind(this)}window.addEventListener("popstate",(function(){try{t.doSPAReload()}catch(e){t.debugLog.e("An error occurred while handling SPA reload on popstate. "+e)}}))}else e.onHashChange&&window.addEventListener("hashchange",(function(){try{t.doSPAReload()}catch(e){t.debugLog.e("An error occurred while handling SPA reload on hashchange. "+e)}}))},e.prototype.addWindowHandlersPostMessageListener=function(e){if("string"==typeof e.data&&-1!=e.data.indexOf("QSI_popunderwatcher_addWindowHandler")){var t=E(e.data.split("|"),3),n=t[1],o=t[2],i=e.source;try{JSON.parse(window.sessionStorage.getItem("QSI_OptInIDsAndWindowNames"))[n]!==o||window.QSI.windows[o]||(window.QSI.windows[o]=i)}catch(e){this.debugLog.e(e)}}},e.prototype.load=function(e,t){var n=this,o=window.QSI.AssetManager;if(!t){this.targetingRetryer=new y("Targeting Call",5,2500)}t||e.Q_EXT_REF||m.a.LoadingState.push(!0);var i=this.generateTargetingURL(e);this.xmdDebugRetryer=new y("Q_XMD_DEBUG Call",6,2500);var r=b.a.components.TARGETING;t?r=b.a.components.TARGETING+"_retry_"+this.targetingRetryer.getRetryCount():e.Q_EXT_REF&&(r=b.a.components.TARGETING+"_extRefRetry"),this.latencyLog.startComponentTimer(r);var a=this.getTargetingPostData(this.targetingRetryer.getRetryCount()),s=o.promiseFetch("POST",i,a);m.a.Request[e.id]={Intercepts:{},Params:e},this.debugLog.safeConsole("log","Making Targeting call to: "+i),s.then((function(){n.latencyLog.endComponentTimer(r)})).then(this.handleTargetingResponse.bind(this,e),(function(t){n.debugLog.e(t.Message),e.deferred&&e.deferred.reject()}))},e.prototype.getTargetingPostData=function(e){var t;if(void 0===e&&(e=0),t=m.a.shouldStripQueryParamsInQLoc?"Q_LOC="+encodeURIComponent(window.location.href.split("?")[0]):"Q_LOC="+encodeURIComponent(window.location.href),!(null==m.a.config.externalReference)){var n=m.a.config.externalReference;if("number"==typeof m.a.config.externalReference&&(n=""+m.a.config.externalReference),this.xmdContactInfoIsValid(n,100)?t+="&extRef="+encodeURIComponent(n):this.debugLog.e("External reference input exceeds character limit: {100}, will be dropped."),null!=m.a.config.firstName&&(this.xmdContactInfoIsValid(m.a.config.firstName,100)?t+="&firstName="+encodeURIComponent(m.a.config.firstName):this.debugLog.e("First name input exceeds character limit: {100}, will be dropped.")),null!=m.a.config.lastName&&(this.xmdContactInfoIsValid(m.a.config.lastName,100)?t+="&lastName="+encodeURIComponent(m.a.config.lastName):this.debugLog.e("Last name input exceeds character limit: {100}, will be dropped.")),null!=m.a.config.email&&(t+="&email="+encodeURIComponent(m.a.config.email)),null!=m.a.config.language&&(this.xmdContactInfoIsValid(m.a.config.language,20)?t+="&language="+encodeURIComponent(m.a.config.language):this.debugLog.e("Language input exceeds character limit: {20}, invalid language will be dropped.")),null!=m.a.config.contactEmbeddedData){var o=m.a.config.contactEmbeddedData;Object.keys(o).length>200&&(o=this.reduceContactEmbeddedDataToLegalSize(o,200),this.debugLog.e("Contact embedded data input exceeds size limit: 200, only keeps first 200 and drop extra items"));var i=this.constructLegitimateContactEmbeddedData(o);if(Object.keys(i).length>0){var r=JSON.stringify(i);t+="&contactEmbeddedData="+encodeURIComponent(r)}}t+="&xmdRetryCount="+encodeURIComponent(e)}var a=this.formatHasAnalyticsConsent();return t+="&hasAnalyticsConsent="+encodeURIComponent(a),t+=this.getCJAEventData()},e.prototype.xmdContactInfoIsValid=function(e,t){return e.length<=t},e.prototype.getCJAEventData=function(){var e=new Date;return"&Timestamp="+encodeURIComponent(e.toISOString())+this.getCjaCustomValues()},e.prototype.getCjaCustomValues=function(){var e=m.a.config.customCjaData;if(null!==e&&"object"==typeof e&&!Array.isArray(e)){var t={};for(var n in e){if(Object.keys(t).length>=20)break;var o=e[n],i="string"==typeof n?n:JSON.stringify(n),r="string"==typeof o?o:JSON.stringify(o);i.length>100||r.length>100||(t[i]=r)}if(0!==Object.keys(t).length)return"&customCjaData="+encodeURIComponent(JSON.stringify(t))}return""},e.prototype.formatHasAnalyticsConsent=function(){var e=m.a.config.hasAnalyticsConsent;if(!(null==e)){var t=e.toString().toLowerCase();if("true"==t||"false"==t)return t}return null},e.prototype.reduceContactEmbeddedDataToLegalSize=function(e,t){var n,o,i=Object.entries(e).slice(0,t),r={};try{for(var a=T(i),s=a.next();!s.done;s=a.next()){var c=E(s.value,2),u=c[0],l=c[1];r[u]=l}}catch(e){n={error:e}}finally{try{s&&!s.done&&(o=a.return)&&o.call(a)}finally{if(n)throw n.error}}return r},e.prototype.constructLegitimateContactEmbeddedData=function(e){var t,n,o={};try{for(var i=T(Object.entries(e)),r=i.next();!r.done;r=i.next()){var a=E(r.value,2),s=a[0],c=a[1];null!=c&&s.length<=100&&c.toString().length<=200?o[s]=c:this.debugLog.e("Drop illegal item due to exceeding size limit (key > 100 characters, value > 200 characters or contains undefined value for key: "+s+"}")}}catch(e){t={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}return o},e.prototype.generateTargetingURL=function(e){var t=m.a.baseURL+"Targeting.php?",n=[];e.InterceptID&&n.push("Q_InterceptID="+e.InterceptID),e.ZoneID&&n.push("Q_ZoneID="+e.ZoneID),e.Q_XMD_DEBUG?n.push("Q_XMD_DEBUG"):(null===e.Q_DEBUG&&(n.push("Q_DEBUG"),m.a.isDebug=!0),null===e.Q_BOOKMARKLET&&n.push("Q_BOOKMARKLET")),null===e.Q_NOCACHE&&n.push("Q_NOCACHE"),void 0!==e.version&&null!==e.version&&n.push("Version="+e.version);var o=window.QSI.profile.get("QualtricsSurveyHistory","",1);if(o){var i=encodeURIComponent(Object.keys(o).toString());n.push("Q_QualtricsSurveyTaken="+i)}return void 0!==e.Q_CLIENTVERSION&&null!==e.Q_CLIENTVERSION&&n.push("Q_CLIENTVERSION="+e.Q_CLIENTVERSION),void 0!==e.Q_CLIENTTYPE&&null!==e.Q_CLIENTTYPE&&n.push("Q_CLIENTTYPE="+e.Q_CLIENTTYPE),e.Q_EXT_REF&&n.push("Q_EXT_REF"),t+n.join("&")},e.prototype.isMessageEventOriginAllowed=function(e){if(m.a.reg)for(var t in m.a.reg)if(Object.prototype.hasOwnProperty.call(m.a.reg,t)&&m.a.reg[t]&&m.a.reg[t].options){var n=m.a.reg[t].options;if(n.targetURLOrigin===e)return!0;if(n.target&&n.target.OriginalURLOrigin===e)return!0}return!1},e.prototype.doCSTargetingEvaluation=function(e,t,n){return D(this,void 0,void 0,(function(){var o,i,r,a,s,c=this;return Q(this,(function(u){switch(u.label){case 0:return function(e){e.EMBEDDED_TARGET="EmbeddedTarget"}(o||(o={})),Array.isArray(e.ClientSideIntercepts)?(i=window.QSI.ClientSideTargeting,e.Intercepts=[],r={Intercepts:{},Type:m.a.config.editing?"Editing":"Published"},a={FirstDCFInterceptPassed:!1},s=e.ClientSideIntercepts.map((function(s){return D(c,void 0,void 0,(function(){var c,u,l,d=this;return Q(this,(function(g){switch(g.label){case 0:return s?s.Error?(this.debugLog.log(s.Message),[3,3]):[3,1]:[3,3];case 1:return[4,new Promise((function(e,t){setTimeout((function(){try{var n=void 0;d.usePreviousEvaluationResult(s.InterceptID)?n=window.QSI.InterceptReevaluator.getInterceptEvaluationResult(s.InterceptID):(n=i.evaluateIntercept(s,a,m.a.isDebug),window.QSI.InterceptReevaluator.setInterceptEvaluationResult(s.InterceptID,n)),e(n)}catch(e){t(e)}}))}))];case 2:c=g.sent(),u=c.interceptEntry,l=c.interceptDebugInfo,r.Intercepts[s.InterceptID]=l,u&&(window.QSI.InterceptReevaluator.isActive()&&window.QSI.InterceptReevaluator.hasInterceptAlreadyRun(u.InterceptID)||(t.Intercepts[s.InterceptID]={},t.Intercepts[s.InterceptID].Targeting=u,e.Intercepts.push(u),u.Decision.Creative&&(u.Decision.Creative.Type&&(e.Modules[u.Decision.Creative.Type]=n),u.Decision.PopUnderTarget&&(e.Modules.PopUnder=n)),u.Decision.Target&&u.Decision.Target.DisplayType===o.EMBEDDED_TARGET&&(e.Modules.EmbeddedTarget=n))),g.label=3;case 3:return[2]}}))}))})),[4,Promise.all(s)]):[2];case 1:return u.sent(),e.Intercepts.length>0&&(e.Modules.ScreenCapture=n,e.Intercepts.forEach((function(e){var t=e.InterceptID;window.QSI.InterceptReevaluator.setInterceptHasRun(t)}))),m.a.isDebug&&(e.DebugInfo=JSON.stringify(r)),delete e.Modules.DependencyResolver,[2]}}))}))},e.prototype.usePreviousEvaluationResult=function(e){var t=window.QSI.InterceptReevaluator;return!!t.isActive()&&(!!t.hasInterceptAlreadyRun(e)||!window.QSI.DataLayerHelper&&(null!=window.QSI.InterceptReevaluator.getInterceptEvaluationResult(e)&&!t.hasFrustrationSignalLogic(e)))},e.prototype.doStartTrackingMatrixCheck=function(e){var t=this.formatHasAnalyticsConsent(),n=null!=t?"true"===t:null,o=e.SRConfiguration.recordByDefault;return n||o&&null==n},e.prototype.canStartRegularSession=function(e){return!!e.RequestData.ID&&!!e.SRConfiguration&&!this.isSRThrottled(e.RequestData.ID)&&this.doStartTrackingMatrixCheck(e)},e.prototype.canStartTestSession=function(e){return!!e.RequestData.ID&&!!e.SRTestConfiguration&&!e.RequestData.isDebug&&"Editing"!==e.RequestData.bVersion},e.prototype.getExistingSessionTypeFromCookie=function(e){var t=window.QSI.cookie,n="QSI_ReplaySession_Info_"+e,o=JSON.parse(t.get(n));return null===o?(window.QSI.global.featureFlags["DX.PepperPotts_TestingTools"]&&console.log("No active session info cookie found called: "+n),"No_Active_Session"):!0===o.isTestSession?(window.QSI.global.featureFlags["DX.PepperPotts_TestingTools"]&&console.log("Found test session info cookie called: "+n),"Test_Session"):!1===o.isTestSession?(window.QSI.global.featureFlags["DX.PepperPotts_TestingTools"]&&console.log("Found regular session info cookie called: "+n),window.QSI.SRGlobalConfiguration.isTestSession&&(window.QSI.global.featureFlags["DX.PepperPotts_TestingTools"]&&console.log("Deleting regular session info cookie called: "+n+" to make way for test session"),t.erase(n,m.a.CookieDomain)),"Regular_Session"):void 0},e.prototype.getMillisecondsToDelay=function(){var e=m.a.config.clientSideDelayMilliseconds;return"number"!=typeof e||!e&&0!==e||e>=72e6||e<0?this.defaultClientSideDelayMilliseconds:e},e.prototype.convertIsolationStatus=function(e){switch(e){case g.e.NOT_ISOLATED:return g.d.NOT_ISOLATED;case g.e.ISOLATED_V1:return g.d.ISOLATED_V1;default:throw new Error("Invalid isolation status: "+e)}},e.prototype.setSRGlobalConfiguration=function(e){var t=window.QSI.util;!e.SRTestConfiguration&&window.QSI.SRGlobalConfiguration||(window.QSI.SRGlobalConfiguration={brandId:e.RequestData.brandID,brandDc:t.getBrandDC(e.RequestData.brandDC),zoneId:e.RequestData.ID,srBaseUrl:m.a.getSRBaseURLFromConfigAndOverrides(t.getBrandDC(e.RequestData.brandDC)),cookieDomain:e.RequestData.cookieDomain,configuration:e.SRConfiguration||e.SRTestConfiguration,brandBaseUrl:e.RequestData.brandBaseUrl,isTestSession:!!e.SRTestConfiguration,isolationStatus:void 0!==e.RequestData.isolationStatus?this.convertIsolationStatus(e.RequestData.isolationStatus):void 0})},e.prototype.canStartSR=function(e){var t,n,o,i,r,s,c,u=window.QSI.global;if(!e.SRConfiguration&&!e.SRTestConfiguration)return!1;if(!e.RequestData||!(null===(t=e.RequestData)||void 0===t?void 0:t.ID)||!(null===(o=null===(n=e.RequestData)||void 0===n?void 0:n.ID)||void 0===o?void 0:o.startsWith("ZN_")))return this.debugLog.e("Missing or incorrectly formatted request data in targeting response"),!1;if(window.QSI.global.featureFlags["DX.DXAConditionalTargeting_EA"]){if(!e.SRConfiguration.logicTree||e.SRConfiguration.logicTree.Type!==a.VALUE_NODE)return!1;var l=e.SRConfiguration.logicTree;if(!l||!l.Value)return!1}return e.RequestData.isolationStatus!==g.e.ISOLATION_ERROR&&(e.SRConfiguration&&(null===(i=window.QSI.SR)||void 0===i?void 0:i.zoneId)?((null===(r=window.QSI.SR)||void 0===r?void 0:r.zoneId)!==e.RequestData.ID&&(u.srMultiZoneErrors+=1,this.debugLog.e("Multiple SR Zones Detected on page. "+(null===(s=window.QSI.SR)||void 0===s?void 0:s.zoneId)+" is already initialized, "+e.RequestData.ID+" attempted to initialize after. To resolve, please use only one zone snippet per page."),1==u.srMultiZoneErrors&&console.error("Qualtrics: Multiple Zones Detected on page. "+(null===(c=window.QSI.SR)||void 0===c?void 0:c.zoneId)+" is already initialized, "+e.RequestData.ID+" attempted to initialize after. To resolve, please use only one zone snippet per page.")),!1):!e.SRConfiguration||!e.RequestData||"SR_ENABLED_FOR_WEB_ONLY"===e.RequestData.srEnabled||"SR_ENABLED_FOR_WEB_AND_MOBILE"===e.RequestData.srEnabled)},e.prototype.startSR=function(e){var t=this,o=window.QSI,i=o.API,r=o.SR,a=o.SRGlobalConfiguration,s=this.canStartRegularSession(e),c=this.canStartTestSession(e),u=this.getExistingSessionTypeFromCookie(a.zoneId);c&&Promise.all([n.e(2),n.e(16)]).then(n.bind(null,76)).then((function(e){var n=e.RenderTestSessionComponents;r&&"No_Active_Session"===u&&(window.QSI.SR=null),n({zoneId:a.zoneId,brandBaseUrl:a.brandBaseUrl,isTestSessionActive:"Test_Session"===u,errorCallback:t.debugLog.e})})),(s||c&&"Test_Session"===u)&&i.SessionRecording.start()},e.prototype.handleTargetingResponse=function(e,t){var o=this,i=window.QSI,r=i.AssetManager,a=i.API,s=i.EventTracker,c=i.history;try{var u;if("SampleRejected"===t)return;try{u=JSON.parse(t)}catch(e){return void this.debugLog.e("Failed to parse JSON of targeting response: "+t)}if(u.Error)return void this.debugLog.e(u.Message);if("XMD_RETRY"===u.Message)return void this.targetingRetryer.backOffAndRetry((function(){o.load(e,!0)}));var l=u.Modules;this.setGlobalVars(u);var d=this.isBrowserSupported();if("EXTREF_RETRY"===u.Message){if(l.Core&&d)r.promiseLoadScript("Core",l.Core,m.a,this.latencyLog).then((function(){return D(o,void 0,void 0,(function(){return Q(this,(function(t){return this.evaluateAndSetExternalReference(u),this.load(R(R({},e),{Q_EXT_REF:!0}),!1),[2]}))}))}));return}if(u.RequestData&&u.RequestData.reevaluateInterceptOnUrlChange&&this.handleSPAEvaluation({onHashChange:!0,onUrlPathChange:!0}),c.logVisit(),this.canStartSR(u)&&(this.setSRGlobalConfiguration(u),setTimeout((function(){o.startSR(u)}),this.getMillisecondsToDelay())),!m.a.Request[e.id].hasDependencies&&m.a.Request[e.id].hasBeenResolved||(s.trackElements(),s.incrementEventList(),window._qsie=a.Events),this.csTargetingParams={targetingResponse:u,targetingRequest:m.a.Request[e.id],clientVersion:m.a.global.clientVersion,assetLoadingParams:e},l.Core&&d)r.promiseLoadScript("Core",l.Core,m.a,this.latencyLog).then((function(){return D(o,void 0,void 0,(function(){var t,o,i=this;return Q(this,(function(r){switch(r.label){case 0:return r.trys.push([0,4,,5]),t=window.QSI.ClientSideTargeting,m.a.global.featureFlags["DUX.ClientSideTargetingM1"]&&u.ClientSideIntercepts&&u.ClientSideIntercepts.length>0?(t.setEnabled(!0),[4,this.doCSTargetingEvaluation(u,m.a.Request[e.id],m.a.global.clientVersion)]):[3,2];case 1:return r.sent(),[3,3];case 2:u.Intercepts&&u.Intercepts.forEach((function(t){t&&(t.Error?i.debugLog.log(t.Message):(m.a.Request[e.id].Intercepts[t.InterceptID]={},m.a.Request[e.id].Intercepts[t.InterceptID].Targeting=t))})),r.label=3;case 3:return!u.Dependencies||m.a.global.featureFlags["DUX.ClientSideTargetingM1"]&&u.ClientSideIntercepts&&u.ClientSideIntercepts.length>0?(n.e(5).then(n.bind(null,42)).then((function(e){(0,e.addPopunderEmbeddedDataHandler)(e.updatePopunderEDCallback)})),this.shouldMakeXMDDebugCall()?(e.Q_XMD_DEBUG=!0,this.handleXMDDebug(e,u)):this.loadModules(e,u)):this.handleDependencyResolver(e,u),[3,5];case 4:return o=r.sent(),this.debugLog.e(o),[3,5];case 5:return[2]}}))}))}),(function(){}));else this.doneLoading(e,u)}catch(e){this.debugLog.e(e)}},e.prototype.evaluateAndSetExternalReference=function(e){var t,n=e.RequestData.extRefType,o=e.RequestData.extRefValue;switch(n){case"JS":t=window.QSI.EmbeddedData.getJavaScriptValue(o).toString();break;case"Cookie":t=window.QSI.EmbeddedData.getCookieVal(o);break;case"HTML":t=window.QSI.EmbeddedData.getHTMLFromDOM(o);break;case"StaticValue":t=o;break;default:return void this.debugLog.log("Invalid type when evaluating external reference of type "+n+" and value "+o+" in "+e.RequestData.ID)}t&&(m.a.config.externalReference=t)},e.prototype.shouldMakeXMDDebugCall=function(){return!window.QSI.ClientSideTargeting.isEnabled()&&(!!m.a.config.externalReference&&m.a.isDebug)},e.prototype.handleDependencyResolver=function(e,t){var n=this,o=window.QSI.AssetManager;e.BrandDC=t.RequestData.brandDC;var i=this.generateTargetingURL(e);i+="&t="+(new Date).getTime(),i+="&Q_VSI="+encodeURIComponent(JSON.stringify(t.RequestData.validIntercepts)),i+="&Q_DPR=true",this.latencyLog.startComponentTimer(b.a.components.DPR_TARGETING);var r="";for(var a in t.Dependencies)Object.prototype.hasOwnProperty.call(t.Dependencies,a)&&("SiteCatalyst"===a&&(QSI["Resolve"+a].rootName=m.a.adobeVar),r+=QSI["Resolve"+a].prepare(t.Dependencies[a]));r+="&"+this.getTargetingPostData(),o.promiseFetch("POST",i,r).then((function(){this.latencyLog.endComponentTimer(b.a.components.DPR_TARGETING)})).then(this.handleTargetingResponse.bind(this,e),(function(t){n.debugLog.e(t.Message),e.deferred&&e.deferred.reject()}))},e.prototype.handleXMDDebug=function(e,t){var n=this,o=window.QSI.AssetManager,i={};t.Intercepts.forEach((function(e){if(e.Decision&&e.Decision.ActionSetID){var t=e.InterceptID,n=e.Decision.ActionSetID;i[t]=n}})),e.BrandDC=t.RequestData.brandDC;var r=this.generateTargetingURL(e),a="";a+=this.getTargetingPostData();var s=""+b.a.components.XMD_DEBUG_TARGETING+this.xmdDebugRetryer.getRetryCount();this.latencyLog.startComponentTimer(s),o.promiseFetch("POST",r,a).then((function(o){n.latencyLog.endComponentTimer(s),n.handleXMDDebugResponse(e,t,i,o)}))},e.prototype.handleXMDDebugResponse=function(e,t,n,o){var i,r,a=this;try{i=JSON.parse(o)}catch(e){return void this.debugLog.e("Failed to parse JSON of Q_XMD_DEBUG targeting response: "+o)}if(m.a.isDebug)try{r=JSON.parse(t.DebugInfo)}catch(e){return void this.debugLog.e("Failed to parse debugInfo JSON of Q_DEBUG targeting response: "+t)}m.a.isDebug&&(i.Intercepts.forEach((function(e){if(e){var t=e.InterceptID;Object.prototype.hasOwnProperty.call(r.Intercepts,t)&&Object.prototype.hasOwnProperty.call(n,t)&&e.Error&&a.debugLog.log(e.Message)}})),i.DebugInfo=JSON.stringify(r),i.Modules.Debug=m.a.global.clientVersion),i.Intercepts.forEach((function(n){if(n)if(n.Error)a.debugLog.log(n.Message);else{for(var o=void 0,i=0;i<t.Intercepts.length;i++)n.InterceptID===t.Intercepts[i].InterceptID&&(o=t.Intercepts[i]);o&&(n.ContactID=o.ContactID||n.ContactID,n.DirectoryID=o.DirectoryID||n.DirectoryID,n.SurveyID=o.SurveyID||n.SurveyID,n.DistributionID=o.DistributionID||n.DistributionID),m.a.Request[e.id].Intercepts[n.InterceptID]={},m.a.Request[e.id].Intercepts[n.InterceptID].Targeting=n}})),this.loadModules(e,i)},e.prototype.loadDOMPurify=function(){var e=this,t=this.Deferred();return n.e(19).then(n.t.bind(null,74,7)).then((function(e){window.QSI.DOMPurify=e,t.resolve()})).catch((function(){e.debugLog.e("Failed to load DOMPurify when enableJSSanitization is enabled"),t.reject()})),t.promise()},e.prototype.loadModules=function(e,t){var o=this,i=window.QSI.AssetManager,r=[],a=t.Modules;for(var s in this.latencyLog.startComponentTimer(b.a.components.ASSETS_AND_MODULES),n.e(1).then(n.bind(null,68)),m.a.global.enableJSSanitization&&r.push(this.loadDOMPurify()),a)if(Object.prototype.hasOwnProperty.call(a,s)){if("ScreenCapture"===s){this.setupScreenCaptureListener(a[s]);continue}if("ClientLog"===s)continue;if("DependencyResolver"===s)continue;if(("HTTPRedirect"===s||"LatencyLog"===s)&&m.a.global.isHostedJS()){this.debugLog.c("Module type '"+s+"' is not supported when using Site Intercept Hosted JS");continue}if("EmbeddedFeedback"===s)continue;var c=i.promiseLoadScript(s,a[s],m.a,this.latencyLog);r.push(c)}for(var u=0;u<t.Intercepts.length;u++){var l=t.Intercepts[u];if(l&&!l.Error&&null!==l.Decision.ActionSetID){var d=i.promiseLoadIntercept(e.id,l,e,this.latencyLog),g=i.promiseLoadCreative(e.id,l,e,this.latencyLog);if(r.push(d,g),l.Decision.PopUnderTarget&&l.Decision.PopUnderTarget.ID&&"Target"!==l.Decision.PopUnderTarget.ID){var f=i.promiseLoadPopUnderTarget(e.id,l,e);r.push(f)}}}this.when.apply(this,r).then((function(){try{o.latencyLog.endComponentTimer(b.a.components.ASSETS_AND_MODULES),o.prepareIntercepts(e,t)}catch(e){o.debugLog.e(e)}}),(function(t){var n="";("string"==typeof t||t&&"string"==typeof t.Message)&&(n=t),o.debugLog.e("An error occurred while loading the intercept. "+n),e.deferred&&e.deferred.reject()}))},e.prototype.setInterceptDisplayOptionCallback=function(e,t){var n=window.QSI.util;switch(e){case"":case"onfocus":n.waitForFocus().then(t);break;case"onload":t();break;case"onexit":n.waitForExitIntent().then(t);break;default:return}},e.prototype.prepareIntercepts=function(e,t){var o=this;return n.e(1).then(n.bind(null,68)).then((function(){var n=window.QSI.RunIntercepts;!0!==e.loadingFromAPI&&(o.latencyLog.endTimer(),o.xmdDebugRetryer.getRetryCount()>0?o.latencyLog.send():o.latencyLog.sampledSend(m.a.global.latencySamplePercentage)),o.setGlobalIncludes(e,t);var i=m.a.Request[e.id].displayInterceptType,r=m.a.global.featureFlags["DUX.ClientSideTargetingM1"]&&t.ClientSideIntercepts&&t.ClientSideIntercepts.length>0;("manual"!==i&&!m.a.Request[e.id].zoneManualDisplay||m.a.Request[e.id].hasDependencies&&!r)&&n(e.id,!1),o.doneLoading(e,t),e.deferred&&e.deferred.resolve()}))},e.prototype.isBrowserSupported=function(){return!("Chrome"===window.QSI.Browser.name&&window.QSI.Browser.version<=20)&&(!("Firefox"===window.QSI.Browser.name&&window.QSI.Browser.version<=59)&&!("Internet Explorer"===window.QSI.Browser.name&&window.QSI.Browser.version<=10))},e.prototype.setGlobalIncludes=function(e,t){if(m.a.isDebug&&t.DebugInfo){m.a.Request[e.id].Debug={debugInfo:JSON.parse(t.DebugInfo),version:t.RequestData.bVersion};var n={version:t.RequestData.bVersion,debugInfo:JSON.parse(t.DebugInfo)};m.a.debugConfig=n}m.a.version=t.RequestData.bVersion,m.a.InterceptsRan=!1,m.a.Request[e.id].hasDependencies=t.RequestData.hasDependencies,m.a.Request[e.id].hasBeenResolved=t.RequestData.hasBeenResolved,m.a.Request[e.id].displayInterceptType=t.RequestData.displayInterceptType,m.a.Request[e.id].zoneManualDisplay=t.RequestData.zoneManualDisplay},e.prototype.setGlobalVars=function(e){window.QSI.Browser={name:e.RequestData.browser,version:e.RequestData.browserVersion,isMobile:e.RequestData.isMobile,isBrowserSupported:e.RequestData.isBrowserSupported},window.QSI.CORSOrigin=e.RequestData.CORSOrigin,window.QSI.OS={name:e.RequestData.osName,version:e.RequestData.osVersion},window.QSI.config.gtmContainerID=e.RequestData.googleDataLayerVariable,window.QSI.config.customJavaScriptPermission=e.RequestData.customJavaScriptPermission,m.a.global.brandID=e.RequestData.brandID,m.a.global.brandDC=e.RequestData.brandDC,m.a.global.geo=e.RequestData.geo,m.a.global.graphicPath=m.a.overrides.graphicPath||"https://"+m.a.global.brandDC+"/WRQualtricsSiteIntercept/Graphic.php?IM=",m.a.global.imagePath=m.a.global.baseURL+"/WRQualtricsShared/Graphics",m.a.global.maxCookieSize=e.RequestData.maxCookieSize,m.a.global.featureFlags=e.FeatureFlags,m.a.global.screenCaptureServiceBaseURL=e.RequestData.screenCaptureServiceBaseURL,m.a.global.brandBaseUrl=e.RequestData.brandBaseUrl,m.a.global.eventTrackers=e.RequestData.eventTrackers,m.a.adobeVar=e.RequestData.adobeSCVariable,m.a.id=e.RequestData.ID,m.a.reqID[e.RequestData.ID]=!0,m.a.CookieDomain=e.RequestData.cookieDomain,m.a.historyStorageType=e.RequestData.historyStorageType,m.a.historyStorageSize=e.RequestData.historyStorageSize,m.a.currentURL=window.location.href.split("?")[0],function(){var e=document.createElement("div");e.className="scrollbar-measure",e.style.width="100px",e.style.height="100px",e.style.overflow="scroll",e.style.position="absolute",e.style.top="-99999px",document.body.appendChild(e);var t=e.offsetWidth-e.clientWidth;m.a.scrollbarWidth=t,document.body.removeChild(e)}()},e.prototype.parseQueryString=function(e){var t={};if(e&&-1!==e.indexOf("?"))for(var n=e.split("?",2)[1].split("&"),o=0;o<n.length;o++){var i=n[o].split("=",2);"Q_LOC"===i[0]&&-1!==i[1].indexOf("Q_DEBUG")&&(t.Q_DEBUG=!0),i[0]&&(t[i[0]]=decodeURIComponent(i[1]))}return t},e.prototype.replaceAll=function(e,t,n){return t=t.replace(/([.*+?^${}()|[\]\\=!:/])/g,"\\$1"),e.replace(new RegExp(t,"g"),n)},e.prototype.when=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var o=this,i=C([e],E(t)),r=i.length,a=r,s=1===a?e:this.Deferred(),c=function(e,t){return function(n){for(var i=[],r=1;r<arguments.length;r++)i[r-1]=arguments[r];t[e]=arguments.length>1?n:o.createArrayFromArguments(C([n],E(i))),--a||s.resolve(t)}};if(r>1)for(var u=0;u<r;u++)i[u]&&i[u].promise?i[u].promise().done(c(u,i)).fail(s.reject):a--;return a<1&&s.resolve(i),s.promise()},e.prototype.createArrayFromArguments=function(e){return e?Array.prototype.slice.call(e):[]},e.prototype.isFunction=function(e){return"function"==typeof e||!1},e.prototype.forOwn=function(e,t){if(e&&e instanceof Object&&this.isFunction(t))for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t(e[n],n,e)},e.prototype.each=function(e,t){var n=e.length;if(n)for(var o=0;o<n;o++)t(e[o],o)},e.prototype.doneLoading=function(e,t){if(m.a.LoadingState.pop(),0===m.a.LoadingState.length){if(!0!==e.loadingFromAPI){var n=document.createEvent("Event");n.initEvent("qsi_js_loaded",!0,!0),window.dispatchEvent(n)}this.lastSiResponse=t;for(var o=m.a.PendingQueue.length,i=0;i<o;i++){m.a.PendingQueue.shift()()}}},e.prototype.setupJFEMessageEventHandlerForIOSOptimization=function(){var e=this;if(!m.a.JFEListenerRegistered){m.a.JFEListenerRegistered=!0;return window.addEventListener("message",(function(t){try{if(!e.isMessageEventOriginAllowed(t.origin))return;(function(e){var t=null;if("string"==typeof e)try{t=JSON.parse(e)}catch(e){return!1}return null!==t&&"JFE"===t.from&&"SI"===t.to&&"JFELoaded"===t.event&&"iOS"===window.QSI.OS.name})(t.data)&&(n=t.source,o=t.origin,n.postMessage({event:"addIOSSIWorkaround",from:"SI",to:"JFE"},o))}catch(t){e.debugLog.e(t)}var n,o}))}},e.prototype.setupScreenCaptureListener=function(e){var t=this;if(!m.a.screenCaptureListenerRegistered)return m.a.screenCaptureListenerRegistered=!0,window.addEventListener("message",(function(n){try{if(!t.isMessageEventOriginAllowed(n.origin))return;var o=window.QSI,i=o.AssetManager,r=o.util.getOriginInterceptOfMessage(n.source);if(!r)return;var a=n.data;if("string"==typeof a)try{a=JSON.parse(a)}catch(e){return}if(!a||"JFE"!==a.from||"SI"!==a.to)return;if(m.a.screenCaptureHandlers||(m.a.screenCaptureHandlers={}),"canScreenCapture"===a.event){void 0===m.a.screenCaptureModulePromise&&(m.a.screenCaptureModulePromise=i.promiseLoadScript("ScreenCapture",e,m.a,t.latencyLog));return void n.source.postMessage(JSON.stringify({event:"canScreenCapture",from:"SI",to:"JFE",canScreenCapture:!0}),n.origin)}void 0!==m.a.screenCaptureModulePromise&&m.a.screenCaptureModulePromise.then((function(){var e=window.QSI.ScreenCaptureHandler;switch(m.a.screenCaptureHandlers[a.sessionId]||(m.a.screenCaptureHandlers[a.sessionId]=new e(r,n.source,a.sessionId,a.translations,n.origin)),a.event){case"startScreenCapture":m.a.screenCaptureHandlers[a.sessionId].captureScreen(a.questionId);break;case"editScreenCapture":m.a.screenCaptureHandlers[a.sessionId].editAnnotations(a.questionId);break;case"removeScreenCapture":m.a.screenCaptureHandlers[a.sessionId].removeScreenCapture(a.questionId);break;case"sessionFinished":m.a.screenCaptureHandlers[a.sessionId].removeAllScreenCaptures();break;default:return}}))}catch(e){t.debugLog.e(e)}}))},e}(),L=function(){var e=this;this.isFullDbgInitialized=-1!==window.location.href.indexOf("Q_DEBUG"),this.safeConsole=function(t,n){if(e.isFullDbgInitialized)try{console[t]("Qualtrics: "+n)}catch(e){}},this.enableFullDebug=function(){e.isFullDbgInitialized=!0},this.disableFullDebug=function(){e.isFullDbgInitialized=!1},this.log=function(e,t,n){var o=window.QSI,i=o.ClientLog,r=o.global;i&&(r.featureFlags.isClientLoggingEnabled?i.send(e,t,n):i.sampledSend(e,t,n,.02))},this.alwaysLog=function(e,t,n){var o=window.QSI.ClientLog;o&&o.send(e,t,n)},this.c=function(t){e.safeConsole("log",t),e.log(t)},this.d=function(t){e.safeConsole("dir",t),e.log(t)},this.t=function(t){e.safeConsole("trace",t),e.log(t)},this.e=function(t){e.safeConsole("log",t&&t.message||"error"),e.safeConsole("error",t),e.log(t)},this.es=function(t){e.safeConsole("log",t&&t.message||"error"),e.safeConsole("error",t),e.alwaysLog(t)}},A=function(){function e(){}return e.prototype.send=function(e,t,n){void 0===t&&(t=!0),void 0===n&&(n="error");try{var o=window.QSI,i=o.baseURL+"Ajax.php?action=ClientLog&"+(0,o.getClientVersionQueryString)(),r=window.QSI.reqID,a="";for(var s in r)r[s]&&(a+=s+" ");t&&e.stack&&(e=e.stack);var c=navigator.userAgent,u=window.QSI.global.brandID,l=void 0===u?"unknown":u,d={LevelName:n,Message:"requestId: "+a+"\n currentURL: "+location.origin+"\n userAgent: "+c+"\n brandID: "+l+"\n message: "+e};QSI.util.sendHttpRequest({type:"POST",url:i,header:{"Content-type":"application/x-www-form-urlencoded"},includeCookies:!1,data:QSI.util.buildQueryString(d)})}catch(e){window.QSI.isDebug&&console.log(e)}},e.prototype.sampledSend=function(e,t,n,o){Math.random()<=o/100&&this.send(e,t,n)},e}(),_=n(33),N=function(){function e(){this.storageCache={},this.cookieName="QSI_DATA",this.refreshFromCookie()}return e.prototype.refreshFromCookie=function(){try{var e=window.QSI.cookie.get(this.cookieName);e&&(this.storageCache=JSON.parse(e))}catch(e){this.storageCache={}}},e.prototype.setToCookie=function(){try{window.QSI.cookie.set(this.cookieName,JSON.stringify(this.storageCache))}catch(e){}},e.prototype.setItem=function(e,t){this.storageCache[e]=t,this.setToCookie()},e.prototype.getItem=function(e){return this.storageCache[e]||null},e.prototype.removeItem=function(e){delete this.storageCache[e],this.setToCookie()},e.prototype.reload=function(){this.refreshFromCookie()},e.prototype.clear=function(){this.storageCache={},this.setToCookie()},e}(),P=function(){function e(){}return e.prototype.increment=function(e){try{window.QSI.EventTracker.track(e)}catch(e){window.QSI.dbg.e(e)}},e.prototype.count=function(e){try{return window.QSI.EventTracker.get(e)}catch(e){window.QSI.dbg.e(e)}},e.prototype.push=function(e){try{window.QSI.EventTracker.track(e)}catch(e){window.QSI.dbg.e(e)}},e}(),k=function(){return(k=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},M=["close"],x=function(){function e(e,t){var n=this;this.intercept=e,this.evaluation=t,this.events=M.reduce((function(e,t){return e[t]={counter:0,callbacks:{}},e}),{}),this.addCallbackForEvent=function(e,t){var o=n.events[e],i=String(o.counter++);return o.callbacks[i]=t,function(){o.callbacks[i]&&delete o.callbacks[i]}},this.runCallbacksForEvent=function(e){var t=n.events[e];Object.keys(t.callbacks).forEach((function(e){t.callbacks[e]()}))},this.onClose=function(e){return window.QSI.callbacks[n.id].onClose=function(){n.runCallbacksForEvent("close")},n.addCallbackForEvent("close",e)},this.getEvaluationResult=function(){var e={passed:!1};if(n.evaluation&&n.evaluation.Decision&&n.evaluation.Decision.ActionSetID){var t=n.evaluation.Decision.Target.Type;if(e.passed=!0,e=k(k({},e),{targetType:t,targetValue:n.evaluation.Decision.Target.URL}),n.evaluation.Decision.Creative){var o=n.evaluation.Decision.Creative;e=k(k({},e),{creative:{id:o.ID,name:o.Name,type:o.Type,revision:o.Revision}})}}return e},window.QSI.callbacks=window.QSI.callbacks||{},window.QSI.callbacks[this.id]=window.QSI.callbacks[this.id]||{}}return Object.defineProperty(e.prototype,"name",{get:function(){return this.intercept.Name},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"id",{get:function(){return this.intercept.InterceptID},enumerable:!1,configurable:!0}),e.instanceCache={},e.getInstance=function(t,n){var o=t.InterceptID;return e.instanceCache[o]||(e.instanceCache[o]=new e(t,n)),e.instanceCache[o]},e.clearInstanceCache=function(){delete e.instanceCache,e.instanceCache={}},e}(),q=function(e,t,n,o){return new(n||(n=Promise))((function(i,r){function a(e){try{c(o.next(e))}catch(e){r(e)}}function s(e){try{c(o.throw(e))}catch(e){r(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((o=o.apply(e,t||[])).next())}))},U=function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(r){return function(s){return function(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==r[0]&&2!==r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(e){r=[6,e],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,s])}}},F=function(){var e=this;this.start=function(){return q(e,void 0,void 0,(function(){var e,t,o,i,r,a=this;return U(this,(function(s){switch(s.label){case 0:e=function(e){},t=function(e){},o=new Promise((function(n,o){e=n,t=o})),s.label=1;case 1:if(s.trys.push([1,3,,4]),this.isStarting||window.QSI.SR)throw i=new Error("SessionRecording is already initializing"),window.QSI.dbg.e(i),i;if(!window.QSI.SRGlobalConfiguration)throw i=new Error("SessionRecording config not found"),window.QSI.dbg.e(i),i;return this.isStarting=!0,[4,Promise.all([n.e(11),n.e(13)]).then(n.bind(null,75)).then((function(n){(0,n.initSR)({brandId:window.QSI.SRGlobalConfiguration.brandId,zoneId:window.QSI.SRGlobalConfiguration.zoneId,brandDc:window.QSI.SRGlobalConfiguration.brandDc,srBaseUrl:window.QSI.SRGlobalConfiguration.srBaseUrl,cookieDomain:window.QSI.SRGlobalConfiguration.cookieDomain,configuration:window.QSI.SRGlobalConfiguration.configuration,brandBaseUrl:window.QSI.SRGlobalConfiguration.brandBaseUrl,latencyLog:window.QSI.LatencyLog,isolationStatus:window.QSI.SRGlobalConfiguration.isolationStatus,errorCallback:window.QSI.dbg.e,resolveSessionStartCallback:e,rejectSessionStartCallback:t})}))];case 2:return s.sent(),[3,4];case 3:return r=s.sent(),t(r.message),[3,4];case 4:return[2,o.finally((function(){a.isStarting=!1,window.QSI.dbg.c("DXJSAPIEvents: QSI.API.SessionRecording.start")}))]}}))}))},this.stop=function(){return q(e,void 0,void 0,(function(){var e;return U(this,(function(t){if(!window.QSI.SR)throw e=new Error("SessionRecording is not initialized"),window.QSI.dbg.e(e),e;return[2,window.QSI.SR.persistEventsAndCloseSession().then((function(e){return e})).finally((function(){window.QSI.dbg.c("DXJSAPIEvents: QSI.API.SessionRecording.stop"),window.QSI.SR=void 0}))]}))}))},this.pause=function(){return q(e,void 0,void 0,(function(){var e;return U(this,(function(t){if(!window.QSI.SR)throw e=new Error("SessionRecording is not initialized"),window.QSI.dbg.e(e),e;return window.QSI.SR.detachEventListeners(),[2,window.QSI.SR.persistEventsAndPauseSession().then((function(e){return e})).finally((function(){window.QSI.dbg.c("DXJSAPIEvents: QSI.API.SessionRecording.pause"),window.QSI.SR=void 0}))]}))}))},this.isStarting=!1},H=function(){return(H=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},B=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],o=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&o>=e.length&&(e=void 0),{value:e&&e[o++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},G=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var o,i,r=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(o=r.next()).done;)a.push(o.value)}catch(e){i={error:e}}finally{try{o&&!o.done&&(n=r.return)&&n.call(r)}finally{if(i)throw i.error}}return a},j=function(){function e(){var e=this;this.unloading=!1,this.Events=new P,this.SessionRecording=new F,this.load=function(){try{var t=window.QSI.Orchestrator.Deferred(),n=t.promise();return window.QSI.PendingQueue||(window.QSI.PendingQueue=[]),window.QSI.LoadingState&&window.QSI.LoadingState.length>0?window.QSI.PendingQueue.push(e.loadHelper.bind(e,t)):e.loadHelper.bind(e,t)(),n}catch(e){window.QSI.dbg.e(e)}finally{window.QSI.dbg.c("DXJSAPIEvents: QSI.API.load()")}},this.unloadForSPA=function(t){window.QSI.global.featureFlags["DX.DXA_Auto_SPA"]&&t&&window.QSI.API.SessionRecording.pause(),e.unloadBase(!0)},this.unload=function(){e.unloadBase(!1)},this.unloadBase=function(t){void 0===t&&(t=!1),window.QSI.DataLayerHelper=void 0,window.QSI.InterceptReevaluator=new v;try{if(window.QSI.PendingQueue||(window.QSI.PendingQueue=[]),window.QSI.LoadingState&&window.QSI.LoadingState.length>0)return void(t?window.QSI.PendingQueue.push(window.QSI.API.unloadForSPA):window.QSI.PendingQueue.push(window.QSI.API.unload));if(e.unloading=!0,window.QSI.SR&&!t){window.QSI.API.SessionRecording.pause();var n=document.getElementById("QSI_TestSessionToolbar");n&&n.parentNode&&n.parentNode.removeChild(n)}if(window.QSI.reg&&(window.QSI.util.forOwn(window.QSI.reg,(function(e,t){window.QSI.reg[t].remove()})),window.QSI.util.removeObservers(),window.QSI.util.removeTimeouts()),window.QSI.debug&&(window.QSI.util.remove(window.QSI.util.$("QSI_Debug")),window.QSI.debuggerHasDisplayed=!1),window.QSI.styleElements)for(var o=window.QSI.styleElements,i=0;i<o.length;i++)window.QSI.util.remove(o[i]);window.QSI.reg=void 0,window.QSI.Request=void 0,window.QSI.styleElements=void 0,window.QSI.callbacks=void 0,window.QSI.runOptions=void 0,x.clearInstanceCache(),e.unloading=!1}catch(e){window.QSI.dbg.e(e)}finally{window.QSI.dbg.c("DXJSAPIEvents: QSI.API.unload() invoked via "+(t?"SPA":"API"))}},this.run=function(t){try{if(window.QSI.PendingQueue||(window.QSI.PendingQueue=[]),window.QSI.LoadingState&&window.QSI.LoadingState.length>0)return void window.QSI.PendingQueue.push((function(){window.QSI.API.run(t)}));window.QSI.InterceptsRan||void 0===window.QSI.reg||(window.QSI.runOptions=t,e.shouldRunWithOptions(t)?(e.prepareForEvaluationWithOptions(),window.QSI.InterceptReevaluator.debouncedEvaluateAndRun()):window.QSI.RunIntercepts(null,!0))}catch(e){window.QSI.dbg.e(e)}finally{window.QSI.dbg.c("DXJSAPIEvents: QSI.API.run()")}},this.prepareForEvaluationWithOptions=function(){var e,t;try{for(var n=B(Object.entries(window.QSI.Request)),o=n.next();!o.done;o=n.next()){var i=G(o.value,2);i[0];i[1].Intercepts={}}}catch(t){e={error:t}}finally{try{o&&!o.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}window.QSI.InterceptReevaluator.resetInterceptHasRun(),window.QSI.InterceptReevaluator.resetEvaluationResults()},this.shouldRunWithOptions=function(e){var t=null==e?void 0:e.jsExpressionValues;return t&&"object"==typeof t&&Object.keys(t).length>0},this.getIntercept=function(e){return new Promise((function(e,t){try{window.QSI.PendingQueue||(window.QSI.PendingQueue=[]),window.QSI.LoadingState&&window.QSI.LoadingState.length>0?window.QSI.PendingQueue.push((function(){e()})):e()}catch(e){t(e)}})).then((function(){var t=G(window.QSI.Orchestrator.getInterceptFromSiResponse(e),2),n=t[0],o=t[1];if(!n)throw e+" is not a valid Intercept ID";return x.getInstance(H({},n),o?H({},o):null)})).catch((function(e){throw window.QSI.dbg.e(e),e})).finally((function(){window.QSI.dbg.log("DXJSAPIEvents: QSI.API.getIntercept()",!1,"info")}))}}return e.prototype.loadHelper=function(e){try{if(window.QSI.reg||this.unloading)return void e.reject();void 0===window.QSI.reg&&(window.QSI.reg={}),void 0===window.QSI.ed&&(window.QSI.ed={}),void 0===window.QSI.reqID&&(window.QSI.reqID={}),void 0===window.QSI.Request&&(window.QSI.Request={}),void 0===window.QSI.styleElements&&(window.QSI.styleElements=[]),window.QSI.util.forOwn(window.QSI.reqID,(function(t,n){var o={loadingFromAPI:!0};o.id=n,"Editing"===window.QSI.version&&(o.version="0"),void 0!==window.QSI.global.clientVersion&&null!==window.QSI.global.clientVersion&&(o.Q_CLIENTVERSION=window.QSI.global.clientVersion),void 0!==window.QSI.global.clientType&&null!==window.QSI.global.clientType&&(o.Q_CLIENTTYPE=window.QSI.global.clientType,void 0!==window.QSI.clientTypeVariant&&(o.Q_CLIENTTYPE+=window.QSI.clientTypeVariant)),0===n.search(/ZN/)?o.ZoneID=n:o.InterceptID=n,window.QSI.isDebug&&(o.Q_DEBUG=null),o.deferred=e,window.QSI.Orchestrator.load(o,!1)}))}catch(e){window.QSI.dbg.e(e)}},e}();function V(e){var t=new L;window.QSI&&window.QSI.closelyLoadedMultiZoneInfo&&(t.c("Multiple zone detected with info "+window.QSI.closelyLoadedMultiZoneInfo),window.QSI.closelyLoadedMultiZoneInfo=void 0);var n=new b.a(t);window.QSI.API=window.QSI.API||new j,window.QSI.ClientLog=new A,window.QSI.dbg=t,window.QSI.LatencyLog=n,window.QSI.EventTracker=new d,window.QSI.cookie=new _.a,window.QSI.CookieStorage=new N,window.QSI.Orchestrator=new O,window.QSI.Orchestrator.init(n,t,e)}n.d(t,"initialize",(function(){return V}))},32:function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},33:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var o=n(0),i=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],o=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&o>=e.length&&(e=void 0),{value:e&&e[o++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},r=["^QSI_ReplaySession_Info_ZN_.*"],a=function(){function e(){var e=this;this.cookieSize=0,document.cookie.split(";").forEach((function(t){var n=t.indexOf("QSI");~n&&(e.cookieSize+=t.length-n)}))}return e.prototype.set=function(e,t,n){void 0===n&&(n={});var i=n.force,r=n.erase,a=n.secure,s=n.daysToExpire,c=n.domain,u=n.shouldEncode,l=void 0!==u&&u,d=o.a.global.maxCookieSize,g=this.get(e),f=this.getCookieSize();g&&(f-=(e+"="+g).length);var p="";if(s){var S=new Date;S.setTime(S.getTime()+864e5*s),p="; expires="+S.toUTCString()}var h="";c?h="domain="+c:o.a.CookieDomain&&(h="domain="+o.a.CookieDomain);var I=e+"="+(l?this.encode(t):t),w=f+I.length;if(!(i||null!==d&&w<=d||null===d))throw new Error("Cannot exceed the specified maximum cookie size");this.cookieSize=r?f:w;var v=""+I+p+"; path=/; "+h;("https:"===location.protocol||a)&&(v+="; secure"),document.cookie=v},e.prototype.get=function(e){for(var t=document.cookie.split(";"),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0].trim()===e){var i=o.slice(1).join("=");return this.shouldDecode(e)?this.decode(i):i}}return null},e.prototype.erase=function(e,t){this.set(e,"",{force:!0,erase:!0,daysToExpire:-1,domain:t})},e.prototype.getCookieSize=function(){return this.cookieSize},e.prototype.areCookiesEnabled=function(){try{document.cookie="cookietest=1";var e=-1!==document.cookie.indexOf("cookietest=");return document.cookie="cookietest=1; expires=Thu, 01-Jan-1970 00:00:01 GMT",e}catch(e){return!1}},e.prototype.encode=function(e){return encodeURIComponent(e)},e.prototype.decode=function(e){return decodeURIComponent(e)},e.prototype.shouldDecode=function(e){var t,n;try{for(var o=i(r),a=o.next();!a.done;a=o.next()){var s=a.value;if(e.match(s))return!0}}catch(e){t={error:e}}finally{try{a&&!a.done&&(n=o.return)&&n.call(o)}finally{if(t)throw t.error}}return!1},e}()},43:function(e,t){QSI.history&&!window.QTestLib||(QSI.historyStorage={historySessionName:"QSI_HistorySession",get useCookie(){return"sessionStorage"!==QSI.historyStorageType},get limit(){return this.useCookie?2e3:1e4},getHistorySessionData:function(){return this.useCookie?QSI.cookie.get(this.historySessionName):sessionStorage.getItem(this.historySessionName)},eraseHistorySessionData:function(){this.useCookie?QSI.cookie.erase(this.historySessionName):sessionStorage.removeItem(this.historySessionName)},setHistorySessionData:function(e){this.useCookie?QSI.cookie.set(this.historySessionName,e,{daysToExpire:0}):sessionStorage.setItem(this.historySessionName,e)}},QSI.history={historyStorage:QSI.historyStorage,logVisit:function(){this.logCurrentURL(),this.logSearch(),this.startFocusTracking(),this.logReferrer()},startFocusTracking:function(){if(!this.started)try{this.started=!0,this.focusTime=this.getFocusTimeFromBrowserStorage(),this.blurTime=this.getBlurTimeFromBrowserStorage();var e=this;setInterval((function(){try{e.focused?e.focusTime+=1:e.blurTime+=1}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}}),1e3);var t=function(){try{e.focused=!0}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}};t(),QSI.util.observe(window,"focus",t),QSI.util.observe(window,"blur",(function(){try{e.focused=!1}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}})),QSI.util.observe(window,"pagehide",(function(){try{QSI.profile.set("History","BlurTime",e.blurTime),QSI.profile.set("History","FocusTime",e.focusTime)}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}}))}catch(e){QSI.dbg.e(e)}},logSite:function(e,t){var n,o=this.historyStorage.getHistorySessionData();if(o){var i=(o=decodeURIComponent(o)).split("|");n=i[i.length-1].split("~")[0],o+="|"}else o="",n="";e!==n&&(o+=e+"~"+t,this.writeHistory(o))},writeHistory:function(e,t){if(null!=t&&t<=0)this.historyStorage.eraseHistorySessionData();else{t=t||QSI.historyStorageSize||QSI.global.maxCookieSize||this.historyStorage.limit;var n=encodeURIComponent(e),o=this.limitSize(n,t);try{this.historyStorage.setHistorySessionData(o)}catch(n){e=decodeURIComponent(o),this.writeHistory(e,t-500)}}},limitSize:function(e,t){if(!e.length)return e;for(t=t||this.historyStorage.limit;e.length>t;){var n=decodeURIComponent(e).split("|");n.splice(0,1),e=n.join("|"),e=encodeURIComponent(e)}return e},get:function(){var e=this.historyStorage.getHistorySessionData();return e||(e=[]),e=this.limitSize(e),decodeURIComponent(e)},logCurrentURL:function(){var e=window.location.href,t=1*new Date;this.logSite(e,t)},getReferrer:function(){return document.referrer},logSearch:function(){var e,t,n=this.getReferrer();if(n.search(/(google.com)|(bing.com)|(yahoo.com)/)>=0){var o="";n.search(/(google.com)|(bing.com)/)>=0?(e=/q=(.*?)\&/,(t=n.match(e))&&t.length&&t[1]&&(o=t[1])):n.search(/yahoo.com/)>=0&&(e=/p=(.*?)\&/,(t=n.match(e))&&t.length&&t[1]&&(o=t[1])),o=decodeURIComponent(o),QSI.profile.set("History","SearchTerm",o)}},logReferrer:function(){var e=this.getReferrer();e&&(QSI.util.build("a",{href:e}).hostname!==document.location.host&&QSI.profile.set("History","SiteReferrer",e),QSI.profile.set("History","PageReferrer",e))},logIntercept:function(e,t){t&&this.logActionSet(t)},logActionSet:function(e){if(e.search("AS_")>=0){var t=e,n=1*new Date;QSI.profile.set("ActionSetHistory",t,n),QSI.profile.set("ActionSetHistory",t,n,1)}},logSurvey:function(e,t){QSI.profile.set("QualtricsSurveyHistory",e,t,1)},getSiteReferrer:function(){return QSI.profile.get("History","SiteReferrer")},getPageReferrer:function(){return QSI.profile.get("History","PageReferrer")},getSearch:function(){var e=QSI.profile.get("History","SearchTerm");return e||(e=""),e},getTimeOnSite:function(){var e=this.focusTime;return e+this.blurTime+"|"+e},getFocusTimeFromBrowserStorage:function(){var e=QSI.profile.get("History","FocusTime");return e||(e=0),e},getBlurTimeFromBrowserStorage:function(){var e=QSI.profile.get("History","BlurTime");return e||(e=0),e},getActionSetHistory:function(e,t){var n=QSI.profile.get("ActionSetHistory",e,t);return n||(n=0),n},getPageCount:function(){var e,t,n=this.historyStorage.getHistorySessionData(),o=0,i=[];if(n){var r=(n=decodeURIComponent(n)).split("|");for(t=r.length,e=0;e<t;e++)i.push(r[e].split("~")[0]);var a={};for(t=i.length,e=0;e<t;e++)a[i[e]]||(o++,a[i[e]]=!0)}return{unique:o,total:i.length}}})},44:function(e,t){QSI.profile&&!window.QTestLib||(QSI.profile={namespace:"QSI_",set:function(e,t,n,o){if(void 0===e||void 0===t||void 0===n)throw new Error("To few arguments");try{var i=this.getStorage(o),r=this.namespace+e,a=i.getItem(r);(a=a?JSON.parse(a):{})[t]=n,a=JSON.stringify(a),i.setItem(r,a)}catch(e){QSI.dbg.e("error setting profile item"),QSI.dbg.e(e)}},get:function(e,t,n){var o=this.getStorage(n),i=this.namespace+e,r=o.getItem(i);return r?(r=JSON.parse(r),t?r[t]?r[t]:null:r):null},erase:function(e,t,n){var o=this.getStorage(n),i=this.namespace+e;if(t){var r=JSON.parse(o.getItem(i));delete r[t],r=JSON.stringify(r),o.setItem(i,r)}else o.removeItem(i)},getStorage:function(e){if(this.hasSessionStorage())return e?localStorage:sessionStorage;if(QSI.UserDataStorage){var t=QSI.UserDataStorage;return e?t.isPermanent(!0):t.isPermanent(!1),t}return QSI.CookieStorage},hasSessionStorage:function(){try{var e="qualtricssessionstoragetestkey",t=window.sessionStorage;return t.setItem(e,e),t.removeItem(e),!0}catch(e){return!1}}})},45:function(module,exports){void 0===QSI.util&&(QSI.util={$:function(e){return"string"==typeof e&&(e=document.getElementById(e)),e},forOwn:function(e,t){if(e&&e instanceof Object&&this.isFunction(t))for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t(e[n],n,e)},build:function(e,t,n){var o=document.createElement(e);if(t){var i=this;QSI.util.forOwn(t,(function(e,n){switch(n){case"style":i.setStyle(o,t[n]);break;case"className":o.className=t[n];break;case"id":o.id=t[n];break;default:o.setAttribute(n,t[n])}}))}if(n)if(QSI.util.isString(n))"style"===e&&o.styleSheet?o.styleSheet.cssText=n:o.appendChild(document.createTextNode(String(n)));else if(QSI.util.isArray(n))for(var r=0,a=n.length;r<a;r++){var s=n[r];"string"==typeof s||"number"==typeof s?o.appendChild(document.createTextNode(String(s))):s&&s.nodeType&&o.appendChild(s)}return o},setStyle:function(e,t){QSI.util.forOwn(t,(function(n,o){try{e.style[o]=t[o]}catch(e){QSI.dbg.e(e)}}))},isString:function(e){return"string"==typeof e},isArray:function(e){return"object"==typeof e&&e instanceof Array},getQueryParam:function(e,t){t=t.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var n=new RegExp("[\\?&]"+t+"=([^&#]*)").exec(e);return null===n?"":n[1]},getBrandDC:function(){return QSI.global.brandDC.match(/(.*).qualtrics.com$/)[1]},observe:function(e,t,n,o){this.obs=this.obs||[],e&&(this.obs.push({el:e,e:t,f:n,preventRemove:o||!1}),e.addEventListener?e.addEventListener(t,n,!1):e.attachEvent?e.attachEvent("on"+t,n):e["on"+this.capFirst(t)]&&(e["on"+this.capFirst(t)]=n))},stopObserving:function(e,t,n){e.removeEventListener?e.removeEventListener(t,n,!1):e.detachEvent?e.detachEvent("on"+t,n):e["on"+this.capFirst(t)]&&(e["on"+this.capFirst(t)]=null)},removeObservers:function(){var e=this;this.each(this.obs||[],(function(t){t.preventRemove||e.stopObserving(t.el,t.e,t.f)}))},removeTimeouts:function(){var e=this;this.each(this.tims||[],(function(t){e.clearTimeout(t)}))},remove:function(e){e&&e.parentNode&&e.parentNode.removeChild(e)},isFunction:function(e){return"function"==typeof e||!1},capFirst:function(e){return e.charAt(0).toUpperCase()+e.slice(1)},each:function(e,t){var n=e.length;if(n)for(var o=0;o<n;o++)t(e[o],o)}}),void 0===QSI.doEvalJS&&(QSI.doEvalJS=function(e){return eval(e)}),void 0===QSI.strToVal&&(QSI.strToVal=function(e){try{for(var t=e.split(/[\'"\[\]]/).filter((function(e){return""!=e})),n=t[0].split("."),o=t.slice(1,t.length),i=n.concat(o),r=0;r<i.length;r++)if(0===i[r].indexOf(".")){var a=i[r].split(".");a=a.filter((function(e){return""!=e})),i.splice(r,1,a[0]),r++;for(var s=1;s<a.length;s++)i.splice(r,0,a[s]),r++}var c=window;return QSI.config&&QSI.config.variableRoot&&(c=QSI.config.variableRoot),i.forEach((function(e){c=c[e]})),c}catch(e){return}})},46:function(e,t){QSI.AssetManager={promiseFetch:function(e,t,n){var o=QSI.Orchestrator.Deferred(),i=new XMLHttpRequest;return i.open(e,t,!0),i.withCredentials=!0,"POST"===e&&i.setRequestHeader("Content-type","application/x-www-form-urlencoded"),i.onreadystatechange=function(){4===i.readyState&&(200===i.status?o.resolve(i.responseText):o.reject(i.responseText))},i.send(n),o.promise()},generateDefinitionRequestURL:function(e,t,n,o){var i=QSI.baseURL+"Asset.php?";const r=QSI.config.brandId||QSI.global.brandID,a=QSI.config.zoneId;try{if(QSI.global.featureFlags["DX.UpdateAssetHostName"]&&r&&a){const e=QSI.baseURL.split(/(https?:\/\/)/);i=e[1]+a+"-"+r+"."+e[2]+"Asset.php?"}}catch(e){QSI.dbg.e("Failed to deconstruct base URL and reconstruct new URL. Error: "+e)}var s=[];if(t=t||n.version,s.push("Module="+e),s.push("Version="+t),null!=o&&s.push("Q_InterceptID="+o),null===n.Q_NOCACHE&&s.push("Q_NOCACHE"),QSI.CORSOrigin&&s.push("Q_ORIGIN="+QSI.CORSOrigin),void 0!==n.Q_CLIENTVERSION&&null!==n.Q_CLIENTVERSION&&s.push("Q_CLIENTVERSION="+n.Q_CLIENTVERSION),void 0!==n.Q_CLIENTTYPE&&null!==n.Q_CLIENTTYPE){var c="Q_CLIENTTYPE="+n.Q_CLIENTTYPE;s.push(c)}null!==window.QSI.config.BrandTier&&""!==window.QSI.config.BrandTier&&void 0!==window.QSI.config.BrandTier||(window.QSI.config.BrandTier=""),s.push("Q_BrandTier="+window.QSI.config.BrandTier),s.push("Q_ARCACHEVERSION=21");const u=window.QSI.util.getBrandDC();return u&&s.push("Q_BRANDDC="+u),i+=s.join("&")},loadDefinition:function(e,t){var n=QSI.Orchestrator.Deferred(),o=new XMLHttpRequest;return o.open("GET",e,!0),o.onreadystatechange=function(){if(4===o.readyState)if(200===o.status)try{var e=JSON.parse(o.responseText);e.Error?n.reject(e):(t(e),n.resolve(o.responseText))}catch(e){n.reject(o.responseText)}else n.reject(o.responseText)},o.send(),n.promise()},promiseLoadIntercept:function(e,t,n,o){var i=t.InterceptID,r=QSI.AssetManager.generateDefinitionRequestURL(i,t.InterceptRevision,n);return o.startComponentTimer("AssetDefinition-"+i),QSI.AssetManager.loadDefinition(r,(function(t){o.endComponentTimer("AssetDefinition-"+i),QSI.Request[e].Intercepts[i].Intercept=t.InterceptDefinition}))},promiseLoadCreative:function(e,t,n,o){var i=t.Decision.Creative.ID;if("CR_NoCreative"!==i){var r=QSI.AssetManager.generateDefinitionRequestURL(i,t.Decision.Creative.Revision,n,t.InterceptID);return o.startComponentTimer("AssetDefinition-"+i),QSI.AssetManager.loadDefinition(r,(function(n){o.endComponentTimer("AssetDefinition-"+i),QSI.util.isLegacyCreative(n.CreativeDefinition.Type)&&(n.CreativeDefinition=QSI.AssetManager.sortCreativeDefinition(n.CreativeDefinition)),QSI.Request[e].Intercepts[t.InterceptID].Creative=n.CreativeDefinition}))}return QSI.Request[e].Intercepts[t.InterceptID].Creative=null,QSI.Orchestrator.Deferred().resolve(null)},promiseLoadPopUnderTarget:function(e,t,n){var o=QSI.AssetManager.generateDefinitionRequestURL(t.Decision.PopUnderTarget.ID,t.Decision.PopUnderTarget.Revision,n,t.InterceptID);return QSI.AssetManager.loadDefinition(o,(function(n){QSI.Request[e].Intercepts[t.InterceptID].PopUnderTarget=n.CreativeDefinition}))},promiseLoadScript:function(e,t,n,o){var i,r=e+":"+QSI.global.clientVersion;if(-1!==n.global.alreadyFetchedJSModules.indexOf(r))return o.markComponentAlreadyFetched("JSModule-"+e),(i=QSI.Orchestrator.Deferred()).resolve(),i.promise();i=QSI.Orchestrator.Deferred();var a=document.createElement("script");a.src=QSI.global.hostedJSLocation+e+"Module.js?";var s=[];void 0!==QSI.Orchestrator&&void 0!==QSI.Orchestrator.getClientVersionQueryString&&s.push(QSI.Orchestrator.getClientVersionQueryString()),(-1!==window.location.href.indexOf("Q_DEBUG")||QSI.config.debug)&&s.push("Q_DEBUG=true");const c=QSI.config.brandId||QSI.global.brandID||window.location.host;return s.push("Q_BRANDID="+encodeURIComponent(c)),a.src+=s.join("&"),a.defer=!0,a.addEventListener("load",(function(){try{!0===QSI.wrongModuleVersionRequested&&(i.reject(),QSI.dbg.e("Script: "+e+" failed to load because an unavailable version ("+t+") was requested.")),n.global.alreadyFetchedJSModules.push(r),o.endComponentTimer("JSModule-"+e),i.resolve()}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}}),!1),a.addEventListener("error",(function(){try{i.reject(),QSI.dbg.e("Script: "+e+" failed to load.")}catch(e){"undefined"!=typeof QSI&&QSI.dbg&&QSI.dbg.e&&QSI.dbg.e(e)}})),o.startComponentTimer("JSModule-"+e),document.body.appendChild(a),i.promise()},sortCreativeDefinition:function(e){if(e&&e.Options&&e.Options.elements&&e.Options.elements.Elements){var t=e.Options.elements.Elements;t=QSI.util.stableSort(t,(function(e,t){return Number(e.style.zIndex)<Number(t.style.zIndex)?-1:Number(e.style.zIndex)>Number(t.style.zIndex)?1:0}));for(var n=0;n<t.length;n++)t[n].style&&t[n].style.zIndex&&(t[n].style.zIndex=QSI.global.currentZIndex++);return t=QSI.util.stableSort(t,(function(e,t){return Number(e.position.left)<Number(t.position.left)?-1:Number(e.position.left)>Number(t.position.left)?1:0})),t=QSI.util.stableSort(t,(function(e,t){return Number(e.position.top)<Number(t.position.top)?-1:Number(e.position.top)>Number(t.position.top)?1:0})),e.Options.elements.Elements=t,e}return e}}},47:function(e,t,n){(function(t){var n=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,i=/^0b[01]+$/i,r=/^0o[0-7]+$/i,a=parseInt,s="object"==typeof t&&t&&t.Object===Object&&t,c="object"==typeof self&&self&&self.Object===Object&&self,u=s||c||Function("return this")(),l=Object.prototype.toString,d=Math.max,g=Math.min,f=function(){return u.Date.now()};function p(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function S(e){if("number"==typeof e)return e;if(function(e){return"symbol"==typeof e||function(e){return!!e&&"object"==typeof e}(e)&&"[object Symbol]"==l.call(e)}(e))return NaN;if(p(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=p(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(n,"");var s=i.test(e);return s||r.test(e)?a(e.slice(2),s?2:8):o.test(e)?NaN:+e}e.exports=function(e,t,n){var o,i,r,a,s,c,u=0,l=!1,h=!1,I=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function w(t){var n=o,r=i;return o=i=void 0,u=t,a=e.apply(r,n)}function v(e){return u=e,s=setTimeout(b,t),l?w(e):a}function y(e){var n=e-c;return void 0===c||n>=t||n<0||h&&e-u>=r}function b(){var e=f();if(y(e))return m(e);s=setTimeout(b,function(e){var n=t-(e-c);return h?g(n,r-(e-u)):n}(e))}function m(e){return s=void 0,I&&o?w(e):(o=i=void 0,a)}function R(){var e=f(),n=y(e);if(o=arguments,i=this,c=e,n){if(void 0===s)return v(c);if(h)return s=setTimeout(b,t),w(c)}return void 0===s&&(s=setTimeout(b,t)),a}return t=S(t)||0,p(n)&&(l=!!n.leading,r=(h="maxWait"in n)?d(S(n.maxWait)||0,t):r,I="trailing"in n?!!n.trailing:I),R.cancel=function(){void 0!==s&&clearTimeout(s),u=0,o=c=i=s=void 0},R.flush=function(){return void 0===s?a:m(f())},R}}).call(this,n(32))}}]);
} catch(e) {
  if (typeof QSI !== 'undefined' && QSI.dbg && QSI.dbg.e) {
    QSI.dbg.e(e);
  }
}