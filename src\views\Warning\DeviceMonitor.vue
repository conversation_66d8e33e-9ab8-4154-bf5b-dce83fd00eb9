<template>
  <div class="device-monitor">
    <el-card>
      <template #header>
        <div class="monitor-header">
          <span>设备监控</span>
          <div class="header-actions">
            <el-button size="small" @click="refreshDevices">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 设备状态概览 -->
      <div class="device-overview">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="status-card online">
              <div class="status-icon">
                <el-icon size="24"><CircleCheck /></el-icon>
              </div>
              <div class="status-info">
                <div class="status-count">{{ onlineCount }}</div>
                <div class="status-label">在线设备</div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="status-card fault">
              <div class="status-icon">
                <el-icon size="24"><Warning /></el-icon>
              </div>
              <div class="status-info">
                <div class="status-count">{{ faultCount }}</div>
                <div class="status-label">故障设备</div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="status-card maintenance">
              <div class="status-icon">
                <el-icon size="24"><Tools /></el-icon>
              </div>
              <div class="status-info">
                <div class="status-count">{{ maintenanceCount }}</div>
                <div class="status-label">维护中</div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="status-card offline">
              <div class="status-icon">
                <el-icon size="24"><CircleClose /></el-icon>
              </div>
              <div class="status-info">
                <div class="status-count">{{ offlineCount }}</div>
                <div class="status-label">离线设备</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <!-- 设备列表 -->
      <div class="device-list">
        <el-table :data="devices" stripe v-loading="warningStore.loading">
          <el-table-column prop="name" label="设备名称" width="150" />
          
          <el-table-column prop="type" label="设备类型" width="120" />
          
          <el-table-column prop="status" label="运行状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="health" label="健康度" width="120">
            <template #default="{ row }">
              <div class="health-indicator">
                <el-progress 
                  :percentage="row.health" 
                  :color="getHealthColor(row.health)"
                  :stroke-width="8"
                  :show-text="false"
                />
                <span class="health-text">{{ row.health.toFixed(0) }}%</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="关键参数">
            <template #default="{ row }">
              <div class="parameters">
                <div class="param-item">
                  <span class="param-label">温度:</span>
                  <span class="param-value" :class="getParamClass('temperature', row.parameters.temperature)">
                    {{ row.parameters.temperature.toFixed(1) }}°C
                  </span>
                </div>
                <div class="param-item">
                  <span class="param-label">振动:</span>
                  <span class="param-value" :class="getParamClass('vibration', row.parameters.vibration)">
                    {{ row.parameters.vibration.toFixed(2) }}mm/s
                  </span>
                </div>
                <div class="param-item">
                  <span class="param-label">效率:</span>
                  <span class="param-value" :class="getParamClass('efficiency', row.parameters.efficiency)">
                    {{ row.parameters.efficiency.toFixed(1) }}%
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="nextMaintenance" label="下次维护" width="120">
            <template #default="{ row }">
              <div class="maintenance-info">
                <span :class="getMaintenanceClass(row.nextMaintenance)">
                  {{ formatMaintenanceDate(row.nextMaintenance) }}
                </span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="lastUpdate" label="最后更新" width="150">
            <template #default="{ row }">
              {{ formatTime(row.lastUpdate) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button size="small" @click="showDeviceDetail(row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    
    <!-- 设备详情对话框 -->
    <DeviceDetailDialog 
      v-model="showDetailDialog"
      :device="selectedDevice"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  Refresh, 
  CircleCheck, 
  Warning, 
  Tools, 
  CircleClose 
} from '@element-plus/icons-vue'
import { useWarningStore } from '@/stores/warning'
import DeviceDetailDialog from './DeviceDetailDialog.vue'
import type { DeviceStatus } from '@/types'
import dayjs from 'dayjs'

const warningStore = useWarningStore()

// 响应式数据
const showDetailDialog = ref(false)
const selectedDevice = ref<DeviceStatus | null>(null)

// 计算属性
const devices = computed(() => warningStore.devices)

const onlineCount = computed(() => 
  devices.value.filter(d => d.status === 'online').length
)

const faultCount = computed(() => 
  devices.value.filter(d => d.status === 'fault').length
)

const maintenanceCount = computed(() => 
  devices.value.filter(d => d.status === 'maintenance').length
)

const offlineCount = computed(() => 
  devices.value.filter(d => d.status === 'offline').length
)

// 方法
const refreshDevices = () => {
  warningStore.loadWarnings()
}

const getStatusType = (status: DeviceStatus['status']) => {
  const types = {
    online: 'success',
    fault: 'danger',
    maintenance: 'warning',
    offline: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status: DeviceStatus['status']) => {
  const texts = {
    online: '在线',
    fault: '故障',
    maintenance: '维护中',
    offline: '离线'
  }
  return texts[status] || status
}

const getHealthColor = (health: number) => {
  if (health >= 90) return '#67C23A'
  if (health >= 80) return '#409EFF'
  if (health >= 70) return '#E6A23C'
  return '#F56C6C'
}

const getParamClass = (param: string, value: number) => {
  switch (param) {
    case 'temperature':
      if (value > 80) return 'param-danger'
      if (value > 70) return 'param-warning'
      return 'param-normal'
    case 'vibration':
      if (value > 2.5) return 'param-danger'
      if (value > 2.0) return 'param-warning'
      return 'param-normal'
    case 'efficiency':
      if (value < 70) return 'param-danger'
      if (value < 80) return 'param-warning'
      return 'param-normal'
    default:
      return 'param-normal'
  }
}

const getMaintenanceClass = (maintenanceDate: string) => {
  const days = dayjs(maintenanceDate).diff(dayjs(), 'day')
  if (days <= 3) return 'maintenance-urgent'
  if (days <= 7) return 'maintenance-warning'
  return 'maintenance-normal'
}

const formatMaintenanceDate = (date: string) => {
  const days = dayjs(date).diff(dayjs(), 'day')
  if (days < 0) return '已过期'
  if (days === 0) return '今天'
  if (days === 1) return '明天'
  return `${days}天后`
}

const formatTime = (timestamp: string) => {
  return dayjs(timestamp).format('MM-DD HH:mm')
}

const showDeviceDetail = (device: DeviceStatus) => {
  selectedDevice.value = device
  showDetailDialog.value = true
}
</script>

<style lang="scss" scoped>
.device-monitor {
  .monitor-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
  }
  
  .device-overview {
    margin-bottom: 20px;
    
    .status-card {
      display: flex;
      align-items: center;
      padding: 16px;
      border-radius: 8px;
      background: var(--el-bg-color);
      border: 1px solid var(--el-border-color-light);
      
      .status-icon {
        margin-right: 12px;
      }
      
      .status-info {
        .status-count {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 4px;
        }
        
        .status-label {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }
      
      &.online {
        border-left: 4px solid #67C23A;
        .status-icon { color: #67C23A; }
        .status-count { color: #67C23A; }
      }
      
      &.fault {
        border-left: 4px solid #F56C6C;
        .status-icon { color: #F56C6C; }
        .status-count { color: #F56C6C; }
      }
      
      &.maintenance {
        border-left: 4px solid #E6A23C;
        .status-icon { color: #E6A23C; }
        .status-count { color: #E6A23C; }
      }
      
      &.offline {
        border-left: 4px solid #909399;
        .status-icon { color: #909399; }
        .status-count { color: #909399; }
      }
    }
  }
  
  .health-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .health-text {
      font-size: 12px;
      font-weight: 600;
    }
  }
  
  .parameters {
    display: flex;
    flex-direction: column;
    gap: 4px;
    
    .param-item {
      display: flex;
      align-items: center;
      font-size: 12px;
      
      .param-label {
        color: var(--el-text-color-regular);
        margin-right: 4px;
      }
      
      .param-value {
        font-weight: 600;
        
        &.param-normal { color: #67C23A; }
        &.param-warning { color: #E6A23C; }
        &.param-danger { color: #F56C6C; }
      }
    }
  }
  
  .maintenance-info {
    font-size: 12px;
    
    .maintenance-normal { color: var(--el-text-color-regular); }
    .maintenance-warning { color: #E6A23C; font-weight: 600; }
    .maintenance-urgent { color: #F56C6C; font-weight: 600; }
  }
}
</style>
