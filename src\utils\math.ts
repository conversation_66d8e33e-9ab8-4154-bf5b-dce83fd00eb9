/**
 * 最小二乘法拟合多项式
 * @param x x坐标数组
 * @param y y坐标数组  
 * @param m 多项式阶数
 * @returns 拟合参数数组
 */
export function leastSquares(x: number[], y: number[], m: number): number[] {
  if (x.length !== y.length) {
    throw new Error('输入数组长度不一致！')
  }

  const n = x.length
  const ATA: number[] = []
  const ATy: number[] = []

  // 计算系数矩阵 A^T * A
  for (let i = 0; i < 2 * m + 1; i++) {
    let sum = 0
    for (let j = 0; j < n; j++) {
      sum += Math.pow(x[j], i)
    }
    ATA.push(sum)
  }

  // 计算 A^T * y
  for (let i = 0; i < m + 1; i++) {
    let sum = 0
    for (let j = 0; j < n; j++) {
      sum += Math.pow(x[j], i) * y[j]
    }
    ATy.push(sum)
  }

  // 构建系数矩阵
  const matrix: number[][] = []
  for (let i = 0; i < m + 1; i++) {
    matrix[i] = ATA.slice(i, m + 1 + i)
  }

  // 使用高斯-赛德尔迭代求解
  return gaussSeidel(matrix, ATy, m)
}

/**
 * 高斯-赛德尔迭代法求解线性方程组
 * @param a 系数矩阵
 * @param b 常数项数组
 * @param m 方程组阶数
 * @returns 解向量
 */
function gaussSeidel(a: number[][], b: number[], m: number): number[] {
  const x = new Array(m + 1).fill(0)
  const maxIterations = (m - 1) * 1000
  const tolerance = 1e-8
  const relaxationFactor = 1.7

  for (let k = 0; k < maxIterations; k++) {
    const xOld = [...x]

    for (let i = 0; i < m + 1; i++) {
      let sum = 0
      for (let j = 0; j < m + 1; j++) {
        if (j !== i) {
          sum += a[i][j] * x[j]
        }
      }
      
      // SOR (Successive Over-Relaxation) 方法
      x[i] = (1 - relaxationFactor) * x[i] + 
             (relaxationFactor / a[i][i]) * (b[i] - sum)
    }

    // 检查收敛性
    let converged = true
    for (let i = 0; i < m + 1; i++) {
      if (Math.abs(x[i] - xOld[i]) > tolerance) {
        converged = false
        break
      }
    }

    if (converged) {
      console.log(`最小二乘法收敛，迭代次数: ${k + 1}`)
      break
    }
  }

  return x
}

/**
 * 计算多项式值
 * @param coefficients 多项式系数
 * @param x 自变量值
 * @returns 多项式值
 */
export function evaluatePolynomial(coefficients: number[], x: number): number {
  let result = 0
  for (let i = 0; i < coefficients.length; i++) {
    result += coefficients[i] * Math.pow(x, i)
  }
  return result
}

/**
 * 生成等间隔数据点
 * @param start 起始值
 * @param end 结束值
 * @param count 点数
 * @returns 数据点数组
 */
export function generatePoints(start: number, end: number, count: number): number[] {
  const points: number[] = []
  const step = (end - start) / (count - 1)
  
  for (let i = 0; i < count; i++) {
    points.push(start + i * step)
  }
  
  return points
}

/**
 * 数值微分
 * @param coefficients 多项式系数
 * @returns 导数系数
 */
export function derivative(coefficients: number[]): number[] {
  if (coefficients.length <= 1) return [0]
  
  const result: number[] = []
  for (let i = 1; i < coefficients.length; i++) {
    result.push(i * coefficients[i])
  }
  
  return result
}

/**
 * 寻找函数极值点
 * @param coefficients 多项式系数
 * @param start 搜索起始点
 * @param end 搜索结束点
 * @returns 极值点数组
 */
export function findExtrema(coefficients: number[], start: number, end: number): number[] {
  const derivativeCoeffs = derivative(coefficients)
  const extrema: number[] = []
  
  // 简单的数值方法寻找零点
  const step = (end - start) / 1000
  for (let x = start; x < end; x += step) {
    const y1 = evaluatePolynomial(derivativeCoeffs, x)
    const y2 = evaluatePolynomial(derivativeCoeffs, x + step)
    
    // 检查符号变化
    if (y1 * y2 < 0) {
      extrema.push(x + step / 2)
    }
  }
  
  return extrema
}
