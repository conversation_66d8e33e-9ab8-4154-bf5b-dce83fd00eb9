<template>
  <div class="curve-settings-panel">
    <div class="settings-header">
      <h3>
        <el-icon><Setting /></el-icon>
        曲线设置
      </h3>
      <el-button 
        type="text" 
        @click="togglePanel"
        class="toggle-btn"
      >
        <el-icon>
          <component :is="isExpanded ? 'ArrowUp' : 'ArrowDown'" />
        </el-icon>
      </el-button>
    </div>
    
    <el-collapse-transition>
      <div v-show="isExpanded" class="settings-content">
        <!-- 泵参数设置 -->
        <div class="settings-section">
          <h4>泵参数设置</h4>
          <div class="settings-row">
            <div class="setting-item">
              <label>泵数量:</label>
              <el-input-number
                v-model="localPumpCount"
                :min="1"
                :max="10"
                size="small"
                @change="handlePumpCountChange"
              />
            </div>
            <div class="setting-item">
              <label>频率 (Hz):</label>
              <el-input-number
                v-model="localFrequency"
                :min="30"
                :max="60"
                :step="0.1"
                size="small"
                @change="handleFrequencyChange"
              />
            </div>
          </div>
        </div>

        <!-- 工作点设置 -->
        <div class="settings-section">
          <h4>工作点设置</h4>
          <div class="settings-row">
            <div class="setting-item">
              <label>流量 (m³/h):</label>
              <el-input-number
                v-model="localWorkingFlow"
                :min="0"
                :max="1000"
                :step="10"
                size="small"
                @change="handleWorkingFlowChange"
              />
            </div>
            <div class="setting-item">
              <label>扬程 (m):</label>
              <el-input-number
                v-model="localWorkingHead"
                :min="0"
                :max="60"
                :step="0.1"
                size="small"
                @change="handleWorkingHeadChange"
              />
            </div>
          </div>
        </div>

        <!-- 曲线显示设置 -->
        <div class="settings-section">
          <h4>曲线显示</h4>
          <div class="curve-toggles">
            <el-checkbox
              v-model="localShowCurveTypes.showHead"
              @change="handleCurveToggle"
            >
              扬程曲线
            </el-checkbox>
            <el-checkbox
              v-model="localShowCurveTypes.showEfficiency"
              @change="handleCurveToggle"
            >
              效率曲线
            </el-checkbox>
            <el-checkbox
              v-model="localShowCurveTypes.showP1Power"
              @change="handleCurveToggle"
            >
              P1功率曲线
            </el-checkbox>
            <el-checkbox
              v-model="localShowCurveTypes.showP2Power"
              @change="handleCurveToggle"
            >
              P2功率曲线
            </el-checkbox>
            <el-checkbox
              v-model="localShowCurveTypes.showNPSH"
              @change="handleCurveToggle"
            >
              NPSH曲线
            </el-checkbox>
          </div>
        </div>

        <!-- 快速预设 -->
        <div class="settings-section">
          <h4>快速预设</h4>
          <div class="preset-buttons">
            <el-button size="small" @click="applyPreset('single')">
              单泵运行
            </el-button>
            <el-button size="small" @click="applyPreset('parallel')">
              双泵并联
            </el-button>
            <el-button size="small" @click="applyPreset('variable')">
              变频运行
            </el-button>
            <el-button size="small" @click="resetToDefault">
              恢复默认
            </el-button>
          </div>
        </div>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Setting, ArrowUp, ArrowDown } from '@element-plus/icons-vue'

// Props
interface Props {
  pumpCount: number
  frequency: number
  workingFlow: number
  workingHead: number
  showCurveTypes: {
    showHead: boolean
    showEfficiency: boolean
    showP1Power: boolean
    showP2Power: boolean
    showNPSH: boolean
  }
}

const props = withDefaults(defineProps<Props>(), {
  pumpCount: 1,
  frequency: 50,
  workingFlow: 400,
  workingHead: 25,
  showCurveTypes: () => ({
    showHead: true,
    showEfficiency: true,
    showP1Power: true,
    showP2Power: true,
    showNPSH: true
  })
})

// Emits
const emit = defineEmits<{
  'update:pumpCount': [value: number]
  'update:frequency': [value: number]
  'update:workingFlow': [value: number]
  'update:workingHead': [value: number]
  'update:showCurveTypes': [value: any]
  'settings-changed': []
}>()

// 本地状态
const isExpanded = ref(true)
const localPumpCount = ref(props.pumpCount)
const localFrequency = ref(props.frequency)
const localWorkingFlow = ref(props.workingFlow)
const localWorkingHead = ref(props.workingHead)
const localShowCurveTypes = ref({ ...props.showCurveTypes })

// 监听props变化
watch(() => props.pumpCount, (val) => { localPumpCount.value = val })
watch(() => props.frequency, (val) => { localFrequency.value = val })
watch(() => props.workingFlow, (val) => { localWorkingFlow.value = val })
watch(() => props.workingHead, (val) => { localWorkingHead.value = val })
watch(() => props.showCurveTypes, (val) => { localShowCurveTypes.value = { ...val } }, { deep: true })

// 事件处理
const togglePanel = () => {
  isExpanded.value = !isExpanded.value
}

const handlePumpCountChange = (value: number) => {
  emit('update:pumpCount', value)
  emit('settings-changed')
}

const handleFrequencyChange = (value: number) => {
  emit('update:frequency', value)
  emit('settings-changed')
}

const handleWorkingFlowChange = (value: number) => {
  emit('update:workingFlow', value)
  emit('settings-changed')
}

const handleWorkingHeadChange = (value: number) => {
  emit('update:workingHead', value)
  emit('settings-changed')
}

const handleCurveToggle = () => {
  emit('update:showCurveTypes', { ...localShowCurveTypes.value })
  emit('settings-changed')
}

// 预设配置
const applyPreset = (type: string) => {
  switch (type) {
    case 'single':
      localPumpCount.value = 1
      localFrequency.value = 50
      localWorkingFlow.value = 400
      localWorkingHead.value = 25
      break
    case 'parallel':
      localPumpCount.value = 2
      localFrequency.value = 50
      localWorkingFlow.value = 600
      localWorkingHead.value = 25
      break
    case 'variable':
      localPumpCount.value = 1
      localFrequency.value = 40
      localWorkingFlow.value = 320
      localWorkingHead.value = 16
      break
  }
  
  emit('update:pumpCount', localPumpCount.value)
  emit('update:frequency', localFrequency.value)
  emit('update:workingFlow', localWorkingFlow.value)
  emit('update:workingHead', localWorkingHead.value)
  emit('settings-changed')
}

const resetToDefault = () => {
  localPumpCount.value = 1
  localFrequency.value = 50
  localWorkingFlow.value = 400
  localWorkingHead.value = 25
  localShowCurveTypes.value = {
    showHead: true,
    showEfficiency: true,
    showP1Power: true,
    showP2Power: true,
    showNPSH: true
  }
  
  emit('update:pumpCount', localPumpCount.value)
  emit('update:frequency', localFrequency.value)
  emit('update:workingFlow', localWorkingFlow.value)
  emit('update:workingHead', localWorkingHead.value)
  emit('update:showCurveTypes', { ...localShowCurveTypes.value })
  emit('settings-changed')
}
</script>

<style scoped>
.curve-settings-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
}

.settings-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggle-btn {
  padding: 4px;
}

.settings-content {
  padding: 20px;
}

.settings-section {
  margin-bottom: 20px;
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: 600;
  color: #666;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 8px;
}

.settings-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 10px;
  min-width: 200px;
}

.setting-item label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  min-width: 80px;
}

.curve-toggles {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.preset-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-row {
    flex-direction: column;
    gap: 15px;
  }
  
  .setting-item {
    min-width: auto;
  }
  
  .curve-toggles {
    flex-direction: column;
    gap: 10px;
  }
  
  .preset-buttons {
    flex-direction: column;
  }
}
</style>
