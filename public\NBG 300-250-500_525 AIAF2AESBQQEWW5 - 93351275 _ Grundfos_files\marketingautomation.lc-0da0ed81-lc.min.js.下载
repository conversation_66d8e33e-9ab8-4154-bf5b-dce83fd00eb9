import{w as g,a as E,b as L,L as k,G as w,l as y,s as M,t as u,P as x}from"/etc.clientlibs/settings/wcm/design/marketingautomation/marketingautomation/resources/chunks/vendor.96d44fbe.js";window.grundfos=window.grundfos||{};window.grundfos.imports=window.grundfos.imports||{};const h=window.grundfos.imports.Vue,A=window.grundfos.imports.Vuex;window.grundfos.imports.store;const O=["checkbox","radio"],R=["INPUT","TEXTAREA"],f={isElementTextInput(e){return R.includes(e.tagName)},isElementSelectionInput(e){return O.includes(e.getAttribute("type"))},isElementDropdownInput(e){return e.tagName==="SELECT"},isBoolean(e){const t=e.toLowerCase();return t==="true"||t==="false"},isBooleanValueChanged(e,t){return this.isBoolean(t)&&String(e.checked)!==t.toLowerCase()}};function p(e,t,s,a,i,r,o,m){var n=typeof e=="function"?e.options:e;t&&(n.render=t,n.staticRenderFns=s,n._compiled=!0),a&&(n.functional=!0),r&&(n._scopeId="data-v-"+r);var l;if(o?(l=function(d){d=d||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,!d&&typeof __VUE_SSR_CONTEXT__<"u"&&(d=__VUE_SSR_CONTEXT__),i&&i.call(this,d),d&&d._registeredComponents&&d._registeredComponents.add(o)},n._ssrRegister=l):i&&(l=m?function(){i.call(this,(n.functional?this.parent:this).$root.$options.shadowRoot)}:i),l)if(n.functional){n._injectStyles=l;var v=n.render;n.render=function(T,_){return l.call(_),v(T,_)}}else{var b=n.beforeCreate;n.beforeCreate=b?[].concat(b,l):[l]}return{exports:e,options:n}}const N=20*1024*1024,D={name:"CmpMarketoForm",mixins:[g,E,L],props:{formId:{type:String,required:!0},name:{type:String,default:"formId"},baseUrl:{type:String,required:!0},munchkinId:{type:String,required:!0},onSuccessMessageMode:{type:String,default:"custom"},disableTranslation:{type:Boolean,default:!1},isFormWithAttachments:{type:Boolean,default:!1},recipientEmail:{type:String,default:""},path:{type:String,default:""},alignment:{type:String,default:"center"},formObj:{type:Object,default:()=>({})},thankyouPage:{type:String,default:""},labels:{type:Object,default:()=>({})}},data(){return{successMessage:!1,LOADING:k,isLoading:!1,isUserLoggedIn:!1,translation:{},country:"",language:"",firstName:"",lastName:"",company:"",email:"",isDataLoggerErrorMessageVisible:!1,translationElements:[],totalSize:0,companies:[]}},computed:{formIdString(){return`mktoForm_${this.formId}`},alignmentClass(){return this.alignment==="left"?"left-aligned":"center-aligned"}},async mounted(){this.isLoading=!0,this.isUserLoggedIn=await this.isLoggedIn(),await this.prepareFormData(),this.initializeMarketo()},methods:{initializeMarketo(){window.MktoForms2?this.prepareForm():this.addScript()},submitForm(){window.MktoForms2.whenReady(()=>{const e=document.querySelector(`#${this.formIdString} button.mktoButton`);e&&(e.onclick=t=>{t.preventDefault(),this.isDataLoggerErrorMessageVisible=!1,this.addDtidField(),this.addBasicFields(),this.addMcidField();const s=document.getElementById(this.formIdString),a=new FormData(s);window.MktoForms2.getForm(this.formId).validate()&&this.handleFormSubmit(a)})})},addDtidField(){const e=new URLSearchParams(window.location.search),t=this.getDtid(e);t&&window.MktoForms2.getForm(this.formId).addHiddenFields({dtid:t})},addBasicFields(){window.MktoForms2.getForm(this.formId).addHiddenFields({LastFormURL:document.location.href,gfFormSubmissionDateAndTime:new Date().toISOString()})},addMcidField(){const{mcid:e}=window.dataLayer;e&&window.MktoForms2.getForm(this.formId).addHiddenFields({mcid:e})},initializeCompanySuggestions(){window.MktoForms2.whenReady(async()=>{const t=document.getElementById(this.formIdString).querySelector('input[name="Company"]');t&&(this.companies.length===0&&(this.companies=await this.fetchCompanies()),t.addEventListener("focus",()=>{t.focus()}),t.addEventListener("input",()=>{this.showCompanySuggestions(t)}))})},async fetchCompanies(){const e=await fetch(this.config.companiesUrl);return e.status===200?(await e.json()).companies||[]:[]},showCompanySuggestions(e){try{const t=`${this.formIdString}-companySuggestions`,s=this.createCompanyDatalist(t);this.createCompanyOptions(t,s,e)}catch(t){console.error("An error occurred while fetching companies:",t)}},createCompanyDatalist(e){let t=document.getElementById(e);return t&&t.remove(),t=document.createElement("datalist"),t.id=e,t},createCompanyOptions(e,t,s){this.companies.filter(i=>i.toLowerCase().startsWith(s.value.toLowerCase())).forEach(i=>{const r=document.createElement("option");r.value=i,t.appendChild(r)}),s.setAttribute("list",e),s.setAttribute("autocomplete","on"),s.parentNode.insertBefore(t,s.nextSibling)},handleFormSubmit(e){this.isDataloggerCompliant(e)?this.sendToDataLogger(e).then(()=>{this.submitToMarketo(this.formId)},()=>{this.isDataLoggerErrorMessageVisible=!0}):this.submitToMarketo(this.formId)},submitToMarketo(e){this.hasValidAttachments(e)&&(this.enableAttachments(),window.MktoForms2.getForm(e).submit())},hasValidAttachments(e){return this.hasValidAttachmentTypes()?this.hasValidTotalAttachmentSize()?!0:(window.MktoForms2.getForm(e).showErrorMessage(this.labels.attachmentsMaxSizeMessage),!1):(window.MktoForms2.getForm(e).showErrorMessage(this.labels.attachmentsTypeMessage),!1)},hasValidTotalAttachmentSize(){return this.totalSize<=N},hasValidAttachmentTypes(){const t=document.getElementById(this.formIdString).querySelectorAll('input[type="file"]');for(let s=0;s<t.length;s++){const a=t[s].files[0];if(a&&!a.type.startsWith("image/"))return!1}return!0},isDataloggerCompliant(e){const s=window.location.pathname.split("/")[1],a={};return e.forEach((i,r)=>{a[r]=i}),a.url=s,this.isRussian(a)},isRussian(e){return e.url.toLowerCase()==="ru"||e.Country==="RU"||e.Email.endsWith(".ru")||e.gfLanguage==="ru"},getDtid(e){return e.get("dtid")?e.get("dtid"):window.dataLayer.dtid},addScript(){const e=document.createElement("script");e.type="text/javascript",e.src=`//${this.baseUrl}/js/forms2/js/forms2.min.js`,e.classList.add("grundfos-marketo-script"),e.onload=()=>{this.prepareForm()},this.appendScript(e)},prepareForm(){this.loadForm(),this.initializeCompanySuggestions(),this.submitForm(),this.attachOnSuccessMessage(),this.isLoading=!1},appendScript(e){const t=document.querySelector("body.basepage");t?t.appendChild(e):document.querySelector("body").appendChild(e)},loadForm(){window.MktoForms2.loadForm(`//${this.baseUrl}`,this.munchkinId,this.formId),window.MktoForms2.whenReady(e=>{this.findTranslationElements(),this.translate(),this.ensureUniqueElementIds(),this.setInputFile(),this.prefillData(e),this.enableTracking(e),this.detectChange(),this.setSubmitButtonClasses()})},prepareFormData(){const e=this.prepareTranslation(),t=this.getFormData();return Promise.all([e,t])},async prepareTranslation(){const e={};e[this.name]=this.formId;let t={};try{return t=await w(this.config.translationApiUrl,{params:e}),this.handleTranslationResponse(t)}catch{return this.handleTranslationResponse(t)}},async getFormData(){let e={};try{return e=await w(this.config.dataApiUrl,{}),this.handleDataResponse(e)}catch{return this.handleDataResponse(e)}},findTranslationElements(){const e=document.querySelectorAll(`#${this.formIdString} label[class*="mktoLabel"]`),t=document.querySelectorAll(`#${this.formIdString} input[placeholder]:not([placeholder=""])`),s=document.querySelectorAll(`#${this.formIdString} option`),a=document.querySelectorAll(`#${this.formIdString} button`),i=document.querySelectorAll(`#${this.formIdString} textarea[placeholder]:not([placeholder=""])`),r=document.querySelectorAll(`#${this.formIdString} .mktoRadioList label`),o=document.querySelectorAll(`#${this.formIdString} .mktoHtmlText h3`),m=document.querySelectorAll(`#${this.formIdString} div[class*="mktoCaptchaDisclaimer"]`);e.forEach(n=>{n.setAttribute("data-qa","marketingAutomation-marketoForm-label")}),this.translationElements=[...e,...t,...s,...a,...i,...r,...o,...m]},translate(){!y.isEmpty(this.translation)&&!this.disableTranslation&&this.translationElements.forEach(e=>{const t=e;switch(t.tagName){case"OPTION":this.handleLastChildData(e);break;case"INPUT":case"TEXTAREA":t.placeholder=this.getTranslation(e.id,e.placeholder);break;case"BUTTON":t.lastChild.data=this.getTranslation(e.className,e.lastChild.data);break;case"H3":t.innerHTML=this.getTranslation(e.innerHTML,e.innerHTML);break;case"LABEL":t.hasAttribute("data-gfundefined")?t.textContent=this.getTranslation(t.getAttribute("data-gfundefined"),t.textContent):this.applyCorrectLabel(e);break;default:this.applyCorrectLabel(e)}})},handleLastChildData(e){const{lastChild:t}=e;t&&t.data&&(t.data=this.getTranslation(e.value,t.data))},ensureUniqueElementIds(){const e=document.querySelectorAll(`#${this.formIdString} input[class*="mktoField"]`);this.translationElements.forEach(t=>{if(t.nodeName.toLocaleLowerCase()==="label"&&t.getAttribute("for")&&t.getAttribute("for").indexOf(this.formId)<0){const s=t.getAttribute("for"),a=[...e].filter(i=>i.id===s)[0];if(a){const i="_";a.id+=i+this.formId,t.setAttribute("for",s+i+this.formId),a.setAttribute("data-qa","marketingAutomation-marketoForm-input")}}})},handleTranslationResponse({data:e}){this.translation=e||[]},getTranslation(e,t){return y.has(this.translation,e)?this.translation[e].value:t},applyCorrectLabel(e){const t=e;this.isLegalText(e)?t.innerHTML=this.getTranslation(e.id,""):this.isRecaptchaText(e)?t.innerHTML=this.getTranslation("mktoCaptchaDisclaimer",""):t.lastChild.data=this.getTranslation(e.id,e.lastChild.data)},isLegalText(e){const t=e;return t.id.toLowerCase().includes("permission")||t.id.toLowerCase().includes("subscription")},isRecaptchaText(e){return e.classList.contains("mktoCaptchaDisclaimer")},attachOnSuccessMessage(){window.MktoForms2.whenReady(e=>{e.onSuccess(()=>{const t=this.onSuccessMessageMode;return this.isCustom(t)?(e.getFormElem().hide(),this.successMessage=!0,this.$nextTick().then(()=>{const s=this.$refs.successMessage;M(s,!0)})):this.isLandingPage(t)?this.thankyouPage!==""&&(window.location.href=this.thankyouPage):(this.isNoConfirmation(t)||this.isDefault(t))&&e.getFormElem().hide(),!1})})},isCustom(e){return e==="custom"},isLandingPage(e){return e==="landingPage"},isNoConfirmation(e){return e==="noConfirmation"},isDefault(e){return e==="default"},handleDataResponse({data:e}){this.country=e.country,this.language=e.language,this.firstName=e.firstName,this.lastName=e.lastName,this.company=e.company,this.email=e.email},prefillData(e){e.vals({Country:this.country,gfLanguage:this.language}),this.checkAndPrefillUserData(e),this.prefillFormWithURLParameters()},checkAndPrefillUserData(e){if(this.isUserLoggedIn)e.vals({FirstName:this.firstName,LastName:this.lastName,Company:this.company,Email:this.email});else{const t=new URLSearchParams(window.location.search);e.vals({Email:t.get("Email"),LastName:t.get("LastName"),FirstName:t.get("FirstName"),Company:t.get("Company")})}},enableAttachments(){const e=document.getElementById(this.formIdString),t=this;this.isFormWithAttachments&&this.recipientEmail!==""&&t.sendMail(e,this.handleErrorResponse)},handleErrorResponse(e,t){window.MktoForms2.getForm(this.formId).showErrorMessage(t.message)},setInputFile(){if(this.isFormWithAttachments&&this.recipientEmail!==""){const t=document.getElementById(this.formIdString).querySelectorAll('*[id^="gfAttachment"]');this.totalSize=0,t.forEach(s=>{const a=s;a.type="file",a.setAttribute("accept","image/*"),s.files.length>0&&(this.totalSize+=s.files[0].size)})}},sendMail(e,t){const s=new XMLHttpRequest,a=new FormData(e),i="hidden",r=["munchkinId","cmpid","formid"];Array.from(a.keys()).forEach(o=>{const m=a.get(o);a.delete(o);const n=this.resolveSendableFormElement(e,o);if(n!==null&&!r.includes(o)){let l=`${i}-${o}`;n.type!==i&&(l=n.textContent.replace("*","")),a.append(l,m)}}),a.append("formId",this.formId),s.onreadystatechange=()=>{if(s.readyState===XMLHttpRequest.DONE&&s.status!==200){const o=JSON.parse(s.responseText);t(null,o)}},s.open("POST",`${this.path}.sendmail.json`),s.send(a)},resolveSendableFormElement(e,t){const s=e.querySelector(`#Lbl${t}`);return s!==null?s:e.querySelector(`input[name=${t}]`)},sendToDataLogger(e){const t=this.config.dataloggerApiUrl;return new Promise((s,a)=>{Array.from(e.keys()).forEach(r=>{r.includes("gfAttachment")&&e.delete(r)});const i=new XMLHttpRequest;i.responseType="json",i.open("POST",t),i.send(e),i.onload=()=>{i.readyState===XMLHttpRequest.DONE&&i.status===200?s(i.statusText):a(i.statusText)},i.onerror=()=>{a(i.statusText)}})},enableTracking(e){const t="form event";u({form:this.formObj},t);const s=document.querySelector(`#mktoForm_${this.formId} input`);s&&s.addEventListener("focus",()=>{u({form:Object.assign(this.formObj,{event:"start"})},t)},{once:!0}),e.onSuccess(()=>{const a=document.querySelector(`#gfSubscriptionFlowTrigger_${this.formId}`);a!==null&&a.checked&&u({permissions:["terms and conditions accepted"]},t),u({form:Object.assign(this.formObj,{event:"sent"})},t)})},prefillFormWithURLParameters(){new URLSearchParams(window.location.search).forEach((e,t)=>{const s=document.querySelector(`[name='${t}']`);s!=null&&(f.isElementSelectionInput(s)&&f.isBooleanValueChanged(s,e)?s.checked=!s.checked:f.isElementDropdownInput(s)?s.selectedIndex=e:s.value=e)})},detectChange(){document.getElementById(this.formIdString).addEventListener("change",()=>{this.setInputFile(),this.findTranslationElements(),this.translate()})},updateNameplateFields(e){const t=document.getElementById(this.formIdString),s={product_number:"gfUndefined2",product_code:"gfUndefined11",serial_number:"gfUndefined3",model:"gfUndefined12"};e.forEach(a=>{const i=t.querySelector(`input[name='${s[a.name]}']`);i.focus({preventScroll:!0}),i.value=a.value,i.blur()})},setSubmitButtonClasses(){const e=document.querySelector(`#mktoForm_${this.formId} button[type="submit"]`);if(!e)return;e.classList.add("elm-btn","elm-btn--large"),e.setAttribute("data-qa","marketingAutomation-marketoForm-submit"),e.closest(".mktoButtonRow").classList.add("cmp-marketo-form__submit-container")}}};var $=function(){var t=this,s=t._self._c;return s("div",{class:["cmp-marketo-form",t.alignmentClass,t.isLoading&&t.LOADING],attrs:{"data-qa":"myGrundfos-requestWidget-cmpMarketoForm"}},[t.isDataLoggerErrorMessageVisible?s("div",{staticClass:"cmp-alert cmp-formbuilder-error-section"},[t._m(0)]):t._e(),t.formId==="1883"?s("cmp-nameplate-extract",{attrs:{labels:t.labels.nameplate,"nameplate-lookup-url":t.config.nameplateLookupUrl,"form-id":t.formId},on:{"update-nameplate-fields":t.updateNameplateFields}},[t._t("nameplate-how-to")],2):t._e(),s("form",{attrs:{id:`mktoForm_${t.formId}`,"data-qa":"marketingAutomation-marketoForm-mktoFormId"}}),t.successMessage?s("div",{ref:"successMessage"},[t._t("success-message")],2):t._e()],1)},U=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"cmp-text cmp-alert__message"},[t("p",[e._v("Server error: connection error")])])}],P=p(D,$,U,!1,null,null,null,null);const S=P.exports,q=10485760,B={name:"CmpNameplateExtract",mixins:[g],props:{labels:{type:Object,default:()=>({})},nameplateHowTo:{type:String,default:""},nameplateLookupUrl:{type:String,default:""},formId:{type:String,default:""}},data(){return{file:null,isLoading:!1,previewImage:null,status:null,partialFields:[]}},computed:{previewImageStyle(){return this.isLoading||!this.previewImage?null:{backgroundImage:`url(${this.previewImage})`}}},methods:{async fileInputChanged(e){if(this.isLoading=!0,[this.file]=e.target.files,!this.file){this.isLoading=!1,this.file=null;return}if(this.file.size>q){this.file=null,this.status="exceeded",this.partialFields=[],this.isLoading=!1;return}this.previewImage=await this.readPreviewImage();try{const{data:t}=await this.getNameplateInformation();this.status=t.status,this.partialFields=t.fields.filter(s=>!s.confident),this.$emit("update-nameplate-fields",t.fields)}catch{this.status="failure",this.partialFields=[]}this.isLoading=!1},readPreviewImage(){return new Promise(e=>{const t=new FileReader;t.addEventListener("load",()=>{e(t.result)}),t.readAsDataURL(this.file)})},getNameplateInformation(){const e=new FormData;e.append("myFile",this.file);const t={name:`mktoForm_${this.formId}`,type:"contact form",event:"upload",error:""};return u({form:t},"form event"),x(this.nameplateLookupUrl,e)}}};var V=function(){var t=this,s=t._self._c;return s("div",{staticClass:"cmp-nameplate-extract"},[s("cmp-nameplate-extract-content",{attrs:{status:t.status,labels:t.labels,"partial-fields":t.partialFields}}),s("div",{staticClass:"cmp-nameplate-extract__upload"},[s("div",{staticClass:"cmp-nameplate-extract-upload__scan-area"},[s("div",{class:["cmp-nameplate-extract-upload__scan-image",{"cmp-nameplate-extract-upload__scan-image--loading":t.isLoading},{"cmp-nameplate-extract-upload__scan-image--preview":!t.isLoading&&t.previewImage}],style:t.previewImageStyle})]),s("div",{staticClass:"cmp-nameplate-extract-upload-button__wrapper"},[s("input",{attrs:{id:"actual-btn",type:"file",hidden:""},on:{change:t.fileInputChanged}}),s("label",{attrs:{for:"actual-btn"}},[t._v(t._s(t.file?t.labels.linkChangeImage:t.labels.linkUploadImage))])]),s("div",{staticClass:"cmp-nameplate-extract-upload-explanation"},[s("p",{staticClass:"cmp-nameplate-extract-upload-explanation__max-size"},[t._v(" "+t._s(t.labels.maxUploadSize)+" ")]),s("p",{staticClass:"cmp-nameplate-extract-upload-explanation__legal"},[t._v(" "+t._s(t.labels.legalText)+" ")])])])],1)},H=[],j=p(B,V,H,!1,null,null,null,null);const F=j.exports,{mapMutations:z}=A,c={success:"success",failure:"failure",partial:"partial",exceeded:"exceeded"},W={name:"CmpNameplateExtractContent",mixins:[g],props:{labels:{type:Object,default:()=>({})},status:{type:String,default:null},partialFields:{type:Array,default:()=>[]}},computed:{content(){return this.status===c.success?{headline:this.labels.headlineSuccess,descriptionOne:this.labels.descriptionOneSuccess,descriptionTwo:this.labels.descriptionTwoSuccess}:this.status===c.partial?this.partialFieldsWithMissingValues.length?{headline:this.labels.headlineMissing,descriptionOne:this.labels.descriptionOneMissing,descriptionTwo:this.labels.descriptionTwoMissing}:{headline:this.labels.headlinePartial,descriptionOne:this.labels.descriptionOnePartial,descriptionTwo:this.labels.descriptionTwoPartial}:this.status===c.failure?{headline:this.labels.headlineFailure,descriptionOne:this.labels.descriptionOneFailure,descriptionTwo:this.labels.descriptionTwoFailure}:this.status===c.exceeded?{headline:this.labels.headlineFailure,descriptionOne:this.labels.descriptionExceeded,descriptionTwo:this.labels.descriptionTwoFailure}:{headline:this.labels.headlineWhichProduct,descriptionOne:this.labels.descriptionOneRegistration}},fieldsToShowInList(){return this.partialFieldsWithMissingValues.length?this.partialFieldsWithMissingValues:this.partialFields},partialFieldsWithMissingValues(){return this.partialFields.filter(e=>e.value==="")},shouldShowErrorStyle(){return this.status===c.failure||this.status===c.exceeded}},methods:{...z("base",["setOverlayComponent"]),clickedOnNameplateHowTo(){this.setOverlayComponent({component:"CmpNameplateExtractOverlay",fullScreen:this.isMobile,useSlideIn:!0,props:{headline:this.labels.howTo,text:this.labels.howToText}})},mapPartialFieldToLabel(e){return{product_number:this.labels.partialFieldProductNumber,serial_number:this.labels.partialFieldSerialNumber,product_code:this.labels.partialFieldProductCode,model:this.labels.partialModel}[e.name]}}};var X=function(){var t=this,s=t._self._c;return s("div",{class:["cmp-nameplate-extract__content",{"cmp-nameplate-extract__content--error":t.shouldShowErrorStyle}]},[s("h3",[t._v(t._s(t.content.headline))]),s("p",[t._v(t._s(t.content.descriptionOne))]),t.status!==null?[t.partialFields.length?s("ul",t._l(t.fieldsToShowInList,function(a,i){return s("li",{key:i},[t._v(" "+t._s(t.mapPartialFieldToLabel(a))+" ")])}),0):t._e(),s("p",[t._v(t._s(t.content.descriptionTwo))])]:[s("button",{staticClass:"elm-link elm-link--block",on:{click:t.clickedOnNameplateHowTo}},[t._v(" "+t._s(t.labels.howTo)+" ")])]],2)},G=[],Z=p(W,X,G,!1,null,null,null,null);const I=Z.exports,J={name:"CmpNameplateExtractOverlay",props:{headline:{type:String,default:""},text:{type:String,default:""}}};var K=function(){var t=this,s=t._self._c;return s("div",{staticClass:"cmp-nameplate-extract__overlay"},[s("h3",[t._v(t._s(t.headline))]),s("p",[t._v(t._s(t.text))])])},Q=[],Y=p(J,K,Q,!1,null,null,null,null);const C=Y.exports,tt={bind:function(e,t,s){e.clickOutsideEvent=function(a){e===a.target||e.contains(a.target)||s.context[t.expression](a)},document.body.addEventListener("click",e.clickOutsideEvent)},unbind:function(e){document.body.removeEventListener("click",e.clickOutsideEvent)}};h.component(S.name,S);h.component(F.name,F);h.component(I.name,I);h.component(C.name,C);h.directive("click-outside",tt);
