<template>
  <div class="algorithm-config">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>曲线拟合算法配置</h2>
      <p>选择合适的算法来拟合水泵性能曲线，获得更精确的模拟效果</p>
    </div>
    
    <!-- 算法选择器 -->
    <AlgorithmSelector 
      v-model:selected-algorithm="selectedAlgorithm"
    />
    
    <!-- 算法参数设置 -->
    <AlgorithmParams
      v-if="selectedAlgorithm"
      :selected-algorithm="selectedAlgorithm"
      v-model:algorithm-params="algorithmParams"
    />
    
    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button 
        type="primary" 
        :icon="Check"
        :disabled="!selectedAlgorithm"
        @click="applyAlgorithm"
        :loading="isApplying"
      >
        应用算法
      </el-button>
      
      <el-button 
        type="success" 
        :icon="View"
        :disabled="!selectedAlgorithm"
        @click="previewAlgorithm"
      >
        预览效果
      </el-button>
    </div>
    
    <!-- 算法评估指标 -->
    <AlgorithmMetrics 
      v-if="selectedAlgorithm && hasApplied"
      :metrics="evaluationMetrics" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { Check, View } from '@element-plus/icons-vue'
import AlgorithmSelector from './AlgorithmSelector.vue'
import AlgorithmParams from './AlgorithmParams.vue'
import AlgorithmMetrics from './AlgorithmMetrics.vue'
import { ElMessage } from 'element-plus'
import type { AlgorithmParamsType, MetricsType } from './types'

// 当前选择的算法
const selectedAlgorithm = ref<string>('polynomial')

// 算法应用状态
const isApplying = ref<boolean>(false)
const hasApplied = ref<boolean>(false)

// 各算法参数
const algorithmParams = reactive<AlgorithmParamsType>({
  linear: {
    type: 'linear',
    weights: 'equal'
  },
  polynomial: {
    degree: 3,
    regularization: 0.01
  },
  spline: {
    type: 'cubic',
    smoothing: 0.5
  },
  neural: {
    hiddenLayers: 2,
    neurons: 20,
    learningRate: 0.01,
    epochs: 1000
  },
  bezier: {
    controlPoints: 4,
    tension: 0.5
  }
})

// 评估指标
const evaluationMetrics = reactive<MetricsType>({
  rSquared: 0.95,
  mse: 0.025,
  mae: 0.12,
  computeTime: 45
})

// 获取算法中文名称
const getAlgorithmName = computed(() => {
  const names: Record<string, string> = {
    linear: '最小二乘法',
    polynomial: '多项式拟合',
    spline: '样条插值',
    neural: '神经网络',
    bezier: '贝塞尔曲线'
  }
  return names[selectedAlgorithm.value] || '未知算法'
})

// 应用算法
const applyAlgorithm = async () => {
  if (!selectedAlgorithm.value) {
    ElMessage.warning('请先选择一个算法')
    return
  }
  
  isApplying.value = true
  const startTime = performance.now()
  
  try {
    // 模拟算法应用过程
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 根据算法类型设置不同的评估指标
    switch (selectedAlgorithm.value) {
      case 'linear':
        evaluationMetrics.rSquared = 0.85 + Math.random() * 0.05
        evaluationMetrics.mse = 0.04 + Math.random() * 0.02
        break
      case 'polynomial':
        evaluationMetrics.rSquared = 0.93 + Math.random() * 0.04
        evaluationMetrics.mse = 0.02 + Math.random() * 0.01
        break
      case 'spline':
        evaluationMetrics.rSquared = 0.96 + Math.random() * 0.03
        evaluationMetrics.mse = 0.01 + Math.random() * 0.01
        break
      case 'neural':
        evaluationMetrics.rSquared = 0.97 + Math.random() * 0.02
        evaluationMetrics.mse = 0.005 + Math.random() * 0.01
        break
      case 'bezier':
        evaluationMetrics.rSquared = 0.88 + Math.random() * 0.04
        evaluationMetrics.mse = 0.03 + Math.random() * 0.02
        break
    }
    
    const endTime = performance.now()
    evaluationMetrics.computeTime = Math.round(endTime - startTime)
    
    hasApplied.value = true
    
    ElMessage.success(`${getAlgorithmName.value}算法应用成功`)
  } catch (error) {
    console.error('算法应用失败:', error)
    ElMessage.error(`算法应用失败: ${(error as Error).message}`)
  } finally {
    isApplying.value = false
  }
}

// 预览算法
const previewAlgorithm = () => {
  if (!selectedAlgorithm.value) {
    ElMessage.warning('请先选择一个算法')
    return
  }
  
  ElMessage({
    type: 'info',
    message: `正在预览${getAlgorithmName.value}算法效果...`,
    duration: 2000
  })
  
  // TODO: 实现预览功能，可以在图表上显示预览曲线
}
</script>

<style lang="scss" scoped>
.algorithm-config {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
}

.page-header {
  text-align: center;
  margin-bottom: 2.5rem;
  
  h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 0.75rem;
    background: linear-gradient(90deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  
  p {
    font-size: 1rem;
    color: var(--el-text-color-secondary);
    margin: 0;
    line-height: 1.5;
    max-width: 36rem;
    margin: 0 auto;
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .algorithm-config {
    padding: 1rem;
  }
  
  .page-header {
    margin-bottom: 1.5rem;
    
    h2 {
      font-size: 1.5rem;
    }
    
    p {
      font-size: 0.875rem;
    }
  }
}
</style> 