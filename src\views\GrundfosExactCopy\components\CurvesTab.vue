<template>
  <div class="curves-section">
    <div class="enlarge-button">
      <button class="enlarge-btn" @click="toggleEnlarge">
        {{ isEnlarged ? '缩小' : '放大' }}
      </button>
    </div>

    <!-- 水泵参数输入区域 -->
    <div class="pump-parameters-section">
      <h3>水泵参数设置</h3>
      <div class="parameters-grid">
        <div class="parameter-group">
          <label>额定流量 (m³/h)</label>
          <input type="number" v-model="pumpParameters.ratedFlow" placeholder="275" />
        </div>
        <div class="parameter-group">
          <label>额定扬程 (m)</label>
          <input type="number" v-model="pumpParameters.ratedHead" placeholder="52.5" />
        </div>
        <div class="parameter-group">
          <label>额定功率 (kW)</label>
          <input type="number" v-model="pumpParameters.ratedPower" placeholder="75" />
        </div>
        <div class="parameter-group">
          <label>额定效率 (%)</label>
          <input type="number" v-model="pumpParameters.ratedEfficiency" placeholder="88.5" />
        </div>
        <div class="parameter-group">
          <label>转速 (rpm)</label>
          <input type="number" v-model="pumpParameters.speed" placeholder="2900" />
        </div>
        <div class="parameter-group">
          <label>叶轮直径 (mm)</label>
          <input type="number" v-model="pumpParameters.impellerDiameter" placeholder="250" />
        </div>
      </div>
      <div class="parameter-actions">
        <button class="apply-btn" @click="applyParameters">应用参数</button>
        <button class="reset-btn" @click="resetParameters">重置默认</button>
      </div>
    </div>

    <!-- 曲线设置区域 -->
    <div class="curve-settings-section">
      <div class="settings-header">
        <h3>曲线设置</h3>
      </div>
      
      <div class="settings-grid">
        <div class="setting-group" :class="{ collapsed: !expandedSections.operatingPoint }">
          <div class="setting-header" @click="toggleSection('operatingPoint')">
            <h4>工作点</h4>
            <span class="toggle-icon">{{ expandedSections.operatingPoint ? '▼' : '▶' }}</span>
          </div>
          <div class="setting-content" v-show="expandedSections.operatingPoint">
            <div class="radio-group">
              <el-radio-group v-model="settings.operatingPoint">
                <el-radio value="none">无工作点</el-radio>
                <el-radio value="bep">最佳效率点</el-radio>
                <el-radio value="custom">自定义</el-radio>
              </el-radio-group>
            </div>
            
            <div class="custom-point" v-if="settings.operatingPoint === 'custom'">
              <div class="input-group">
                <label>流量 (m³/h)</label>
                <input type="number" v-model="customOperatingPoint.flow" @input="updateCharts" />
              </div>
              <div class="input-group">
                <label>扬程 (m)</label>
                <input type="number" v-model="customOperatingPoint.head" @input="updateCharts" />
              </div>
            </div>
          </div>
        </div>
        
        <div class="setting-group" :class="{ collapsed: !expandedSections.fluidity }">
          <div class="setting-header" @click="toggleSection('fluidity')">
            <h4>流体特性</h4>
            <span class="toggle-icon">{{ expandedSections.fluidity ? '▼' : '▶' }}</span>
          </div>
          <div class="setting-content" v-show="expandedSections.fluidity">
            <div class="select-group">
              <label>流体类型</label>
              <el-select v-model="settings.fluidType" placeholder="选择流体类型">
                <el-option value="water" label="清水"></el-option>
                <el-option value="seawater" label="海水"></el-option>
                <el-option value="oil" label="油"></el-option>
                <el-option value="chemical" label="化学品"></el-option>
              </el-select>
            </div>
            
            <div class="input-group">
              <label>流体温度 (°C)</label>
              <el-input-number v-model="settings.fluidTemperature" :min="0" :max="100" :step="1"></el-input-number>
            </div>
          </div>
        </div>
        
        <div class="setting-group" :class="{ collapsed: !expandedSections.curveTypes }">
          <div class="setting-header" @click="toggleSection('curveTypes')">
            <h4>曲线类型</h4>
            <span class="toggle-icon">{{ expandedSections.curveTypes ? '▼' : '▶' }}</span>
          </div>
          <div class="setting-content" v-show="expandedSections.curveTypes">
            <div class="checkbox-group">
              <el-checkbox v-model="settings.curveTypes.powerP1">输入功率 (P1)</el-checkbox>
              <el-checkbox v-model="settings.curveTypes.powerP2">轴功率 (P2)</el-checkbox>
              <el-checkbox v-model="settings.curveTypes.npsh">NPSH</el-checkbox>
              <el-checkbox v-model="settings.curveTypes.eta">效率</el-checkbox>
              <el-checkbox v-model="settings.curveTypes.isoEta">等效率线</el-checkbox>
              <el-checkbox v-model="settings.curveTypes.tolerance">公差范围</el-checkbox>
              <el-checkbox v-model="settings.curveTypes.minMax">最小/最大曲线</el-checkbox>
            </div>
          </div>
        </div>
        
        <div class="setting-group" :class="{ collapsed: !expandedSections.hydraulicLayout }">
          <div class="setting-header" @click="toggleSection('hydraulicLayout')">
            <h4>水力布置</h4>
            <span class="toggle-icon">{{ expandedSections.hydraulicLayout ? '▼' : '▶' }}</span>
          </div>
          <div class="setting-content" v-show="expandedSections.hydraulicLayout">
            <div class="select-group">
              <label>安装类型</label>
              <el-select v-model="settings.installationType" placeholder="选择安装类型" @change="updateCharts">
                <el-option value="single" label="单泵"></el-option>
                <el-option value="parallel" label="并联"></el-option>
                <el-option value="series" label="串联"></el-option>
              </el-select>
            </div>
            
            <div class="select-group" v-if="settings.installationType !== 'single'">
              <label>泵数量</label>
              <el-select v-model="settings.pumpCount" placeholder="选择泵数量" @change="updateCharts">
                <el-option value="2" label="2"></el-option>
                <el-option value="3" label="3"></el-option>
                <el-option value="4" label="4"></el-option>
              </el-select>
            </div>
            
            <div class="select-group">
              <label>变频控制</label>
              <el-select v-model="settings.variableSpeed" placeholder="选择变频控制" @change="updateCharts">
                <el-option value="no" label="无变频"></el-option>
                <el-option value="yes" label="变频控制"></el-option>
              </el-select>
            </div>
            
            <div class="checkbox-group">
              <el-checkbox v-model="settings.solarCurves" @change="updateCharts">显示太阳能曲线</el-checkbox>
            </div>
          </div>
        </div>
        
        <div class="setting-group" :class="{ collapsed: !expandedSections.algorithm }">
          <div class="setting-header" @click="toggleSection('algorithm')">
            <h4>算法设置</h4>
            <span class="toggle-icon">{{ expandedSections.algorithm ? '▼' : '▶' }}</span>
          </div>
          <div class="setting-content" v-show="expandedSections.algorithm">
            <div class="radio-group">
              <label>曲线拟合算法</label>
              <el-radio-group v-model="currentAlgorithm" @change="updateCharts">
                <el-radio value="polynomial">多项式拟合</el-radio>
                <el-radio value="spline">样条插值</el-radio>
              </el-radio-group>
            </div>
            
            <div v-if="currentAlgorithm === 'polynomial'" class="algorithm-params">
              <div class="input-group">
                <label>多项式阶数</label>
                <el-input-number v-model="algorithmParams.polynomial.degree" :min="2" :max="6" :step="1" @change="updateCharts"></el-input-number>
              </div>
              <div class="input-group">
                <label>正则化系数</label>
                <el-input-number v-model="algorithmParams.polynomial.regularization" :min="0" :max="1" :step="0.01" :precision="2" @change="updateCharts"></el-input-number>
              </div>
            </div>
            
            <div v-if="currentAlgorithm === 'spline'" class="algorithm-params">
              <div class="input-group">
                <label>样条类型</label>
                <el-select v-model="algorithmParams.spline.type" placeholder="选择样条类型" @change="updateCharts">
                  <el-option value="cubic" label="三次样条"></el-option>
                  <el-option value="natural" label="自然样条"></el-option>
                </el-select>
              </div>
              <div class="input-group">
                <label>平滑系数</label>
                <el-input-number v-model="algorithmParams.spline.smoothing" :min="0" :max="1" :step="0.1" :precision="1" @change="updateCharts"></el-input-number>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 曲线图表区域 -->
    <div class="charts-container">
      <div class="chart-wrapper">
        <div class="chart-title">流量-扬程-效率曲线</div>
        <div class="chart-container" :class="{ enlarged: isEnlarged }" ref="performanceChartContainer"></div>
      </div>
      
      <div class="chart-wrapper">
        <div class="chart-title">流量-功率-NPSH曲线</div>
        <div class="chart-container" :class="{ enlarged: isEnlarged }" ref="powerNpshChartContainer"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'
import { createPerformanceChartOption, createPowerNpshChartOption, updateGrundfosChart } from '../chart-config'

const props = defineProps<{
  dataPoints?: any[]
  npshPoints?: any[]
}>()

const emit = defineEmits(['update-curves'])

// 图表实例
const performanceChartContainer = ref<HTMLElement | null>(null)
const powerNpshChartContainer = ref<HTMLElement | null>(null)
let performanceChart: ECharts | null = null
let powerNpshChart: ECharts | null = null

// 图表控制
const isEnlarged = ref(false)
const toggleEnlarge = () => {
  isEnlarged.value = !isEnlarged.value
  nextTick(() => {
    resizeCharts()
  })
}

// 工作点
const customOperatingPoint = reactive({
  flow: 300,
  head: 42
})

// 泵参数
const pumpParameters = reactive({
  ratedFlow: 300,
  ratedHead: 42,
  ratedEfficiency: 87,
  ratedPower: 56,
  speed: 2900,
  impellerDiameter: 500
})

// 算法设置
const currentAlgorithm = ref('spline')
const algorithmParams = reactive({
  polynomial: {
    degree: 4,
    regularization: 0.005
  },
  spline: {
    type: 'natural',
    smoothing: 0.2
  }
})

// 曲线设置
const settings = reactive({
  operatingPoint: 'bep',
  fluidType: 'water',
  fluidTemperature: 20,
  curveTypes: {
    powerP1: true,
    powerP2: true,
    npsh: true,
    eta: true,
    isoEta: false,
    tolerance: true,
    minMax: false
  },
  installationType: 'single',
  pumpCount: '2',
  variableSpeed: 'no',
  solarCurves: false
})

// 折叠设置区域控制
const expandedSections = reactive({
  operatingPoint: true,
  fluidity: true,
  curveTypes: true,
  hydraulicLayout: true,
  algorithm: true
})

const toggleSection = (section: string) => {
  if (expandedSections.hasOwnProperty(section)) {
    // @ts-ignore
    expandedSections[section] = !expandedSections[section]
  }
}

// 应用参数
const applyParameters = () => {
  // 应用参数到曲线
  updateCharts()
}

// 重置默认参数
const resetParameters = () => {
  Object.assign(pumpParameters, {
    ratedFlow: 300,
    ratedHead: 42,
    ratedEfficiency: 87,
    ratedPower: 56,
    speed: 2900,
    impellerDiameter: 500
  })
  updateCharts()
}

// 初始化图表
const initCharts = () => {
  // 确保容器已挂载
  if (!performanceChartContainer.value || !powerNpshChartContainer.value) {
    return
  }

  // 检查是否已经初始化
  if (performanceChart) {
    performanceChart.dispose()
  }
  if (powerNpshChart) {
    powerNpshChart.dispose()
  }

  // 检查容器大小
  const containerWidth = performanceChartContainer.value.offsetWidth || 800
  const containerHeight = performanceChartContainer.value.offsetHeight || 400

  // 设置容器大小
  performanceChartContainer.value.style.width = `${containerWidth}px`
  performanceChartContainer.value.style.height = `${containerHeight}px`
  powerNpshChartContainer.value.style.width = `${containerWidth}px`
  powerNpshChartContainer.value.style.height = `${containerHeight}px`

  // 获取数据
  const effectiveDataPoints = props.dataPoints && props.dataPoints.length > 0 
    ? props.dataPoints 
    : []
      
  const effectiveNPSHPoints = props.npshPoints && props.npshPoints.length > 0
    ? props.npshPoints
    : []

  // 如果没有数据，则不初始化图表
  if (effectiveDataPoints.length === 0 || effectiveNPSHPoints.length === 0) {
    console.warn('没有有效的数据点，无法初始化图表')
    return
  }

  // 初始化性能曲线图表
  performanceChart = echarts.init(performanceChartContainer.value)
  // 初始化功率与NPSH曲线图表
  powerNpshChart = echarts.init(powerNpshChartContainer.value)

  // 设置图表选项
  updateCharts()

  // 监听窗口尺寸变化
  window.addEventListener('resize', resizeCharts)
  
  // 设置尺寸监听器
  setupResizeObserver()
}

// 监听容器尺寸变化
let resizeObserver: ResizeObserver | null = null
const setupResizeObserver = () => {
  if (typeof ResizeObserver !== 'undefined') {
    resizeObserver = new ResizeObserver(() => {
      resizeCharts()
    })
    
    if (performanceChartContainer.value) {
      resizeObserver.observe(performanceChartContainer.value)
    }
    if (powerNpshChartContainer.value) {
      resizeObserver.observe(powerNpshChartContainer.value)
    }
  }
}

// 更新图表
const updateCharts = () => {
  if (!performanceChart || !powerNpshChart) return

  // 使用传入的数据点，确保参数标签中的数据优先
  const effectiveDataPoints = props.dataPoints && props.dataPoints.length > 0 
    ? props.dataPoints 
    : []
      
  const effectiveNPSHPoints = props.npshPoints && props.npshPoints.length > 0
    ? props.npshPoints
    : []

  // 如果没有数据，则不更新图表
  if (effectiveDataPoints.length === 0 || effectiveNPSHPoints.length === 0) {
    console.warn('没有有效的数据点，无法更新图表')
    return
  }

  // 设置通用参数
  const commonParams = {
    dataPoints: effectiveDataPoints,
    npshPoints: effectiveNPSHPoints,
    settings,
    pumpParameters,
    customOperatingPoint,
    algorithm: currentAlgorithm.value,
    algorithmParams: currentAlgorithm.value === 'polynomial' 
      ? algorithmParams.polynomial 
      : algorithmParams.spline
  };

  // 设置性能曲线图表
  performanceChart.setOption(createPerformanceChartOption(commonParams), true);
  
  // 设置功率与NPSH曲线图表
  powerNpshChart.setOption(createPowerNpshChartOption(commonParams), true);
}

// 调整图表大小
const resizeCharts = () => {
  performanceChart?.resize()
  powerNpshChart?.resize()
}

// 清理资源
onUnmounted(() => {
  // 清理事件监听
  window.removeEventListener('resize', resizeCharts)
  
  // 清理尺寸监听器
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
  
  // 清理图表实例
  if (performanceChart) {
    performanceChart.dispose()
    performanceChart = null
  }
  if (powerNpshChart) {
    powerNpshChart.dispose()
    powerNpshChart = null
  }
})

// 监听参数变化
watch(settings, () => {
  updateCharts()
}, { deep: true })

watch(() => props.dataPoints, () => {
  updateCharts()
}, { deep: true })

watch(() => props.npshPoints, () => {
  updateCharts()
}, { deep: true })

// 延迟初始化图表，确保DOM已渲染
onMounted(() => {
  nextTick(() => {
    setTimeout(() => {
      initCharts()
    }, 100)
  })
})

// 导出方法
defineExpose({
  updateCharts,
  resizeCharts
})
</script>

<style scoped>
.curves-section {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.pump-parameters-section {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.parameters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.parameter-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.parameter-group label {
  font-size: 14px;
  color: #606266;
}

.parameter-group input {
  padding: 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.parameter-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.apply-btn, .reset-btn {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.apply-btn {
  background-color: #409eff;
  color: white;
}

.reset-btn {
  background-color: #f56c6c;
  color: white;
}

.curve-settings-section {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.setting-group {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #ecf5ff;
  cursor: pointer;
}

.setting-header h4 {
  margin: 0;
  color: #409eff;
}

.toggle-icon {
  color: #409eff;
}

.setting-content {
  padding: 15px;
  background-color: white;
}

.radio-group, .checkbox-group, .select-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 10px;
}

.custom-point {
  margin-top: 15px;
  padding: 10px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
}

.charts-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-wrapper {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-title {
  padding: 10px 15px;
  font-size: 16px;
  font-weight: 600;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.chart-container {
  height: 400px;
  width: 100%;
  transition: all 0.3s ease;
}

.chart-container.enlarged {
  height: 600px;
}

.enlarge-button {
  display: flex;
  justify-content: flex-end;
}

.enlarge-btn {
  padding: 6px 12px;
  background-color: #909399;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.algorithm-params {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f9fb;
  border-radius: 4px;
}
</style>
