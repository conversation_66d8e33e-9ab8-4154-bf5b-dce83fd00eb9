<template>
  <div class="responsive-table" :class="{ 'responsive-table--mobile': isMobile }">
    <!-- 桌面端表格 -->
    <el-table
      v-if="!isMobile"
      :data="data"
      :loading="loading"
      v-bind="$attrs"
      class="responsive-table__desktop"
    >
      <slot />
    </el-table>
    
    <!-- 移动端卡片列表 -->
    <div v-else class="responsive-table__mobile">
      <!-- 搜索和筛选 -->
      <div v-if="showMobileSearch" class="responsive-table__mobile-search">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索..."
          :prefix-icon="Search"
          size="small"
          clearable
        />
      </div>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="responsive-table__loading">
        <el-skeleton :rows="3" animated />
      </div>
      
      <!-- 卡片列表 -->
      <div v-else class="responsive-table__cards">
        <div
          v-for="(item, index) in filteredData"
          :key="getRowKey(item, index)"
          class="responsive-table__card"
          @click="handleCardClick(item, index)"
        >
          <div class="responsive-table__card-header">
            <span class="responsive-table__card-title">
              {{ getCardTitle(item) }}
            </span>
            <span class="responsive-table__card-index">#{{ index + 1 }}</span>
          </div>
          
          <div class="responsive-table__card-content">
            <div
              v-for="field in mobileFields"
              :key="field.key"
              class="responsive-table__card-field"
            >
              <span class="responsive-table__card-label">{{ field.label }}:</span>
              <span class="responsive-table__card-value">
                <slot 
                  :name="`mobile-${field.key}`" 
                  :row="item" 
                  :value="getFieldValue(item, field.key)"
                >
                  {{ formatFieldValue(getFieldValue(item, field.key), field) }}
                </slot>
              </span>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div v-if="$slots['mobile-actions']" class="responsive-table__card-actions">
            <slot name="mobile-actions" :row="item" :index="index" />
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="filteredData.length === 0" class="responsive-table__empty">
          <el-empty description="暂无数据" />
        </div>
      </div>
      
      <!-- 分页 -->
      <div v-if="showPagination && !loading" class="responsive-table__pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next"
          small
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Search } from '@element-plus/icons-vue'

interface MobileField {
  key: string
  label: string
  formatter?: (value: any) => string
}

interface Props {
  data: any[]
  loading?: boolean
  mobileFields: MobileField[]
  titleField?: string
  rowKey?: string | ((row: any) => string)
  showMobileSearch?: boolean
  showPagination?: boolean
  total?: number
}

interface Emits {
  (e: 'card-click', row: any, index: number): void
  (e: 'update:current-page', page: number): void
  (e: 'update:page-size', size: number): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  titleField: 'name',
  rowKey: 'id',
  showMobileSearch: true,
  showPagination: false,
  total: 0
})

const emit = defineEmits<Emits>()

// 响应式数据
const isMobile = ref(false)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(10)

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
}

// 监听窗口大小变化
const handleResize = () => {
  checkMobile()
}

// 过滤数据
const filteredData = computed(() => {
  if (!searchKeyword.value) return props.data
  
  return props.data.filter(item => {
    return props.mobileFields.some(field => {
      const value = getFieldValue(item, field.key)
      return String(value).toLowerCase().includes(searchKeyword.value.toLowerCase())
    })
  })
})

// 获取行键
const getRowKey = (row: any, index: number): string => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(row)
  }
  return row[props.rowKey] || index
}

// 获取卡片标题
const getCardTitle = (item: any): string => {
  return item[props.titleField] || '未命名'
}

// 获取字段值
const getFieldValue = (item: any, key: string): any => {
  return key.split('.').reduce((obj, k) => obj?.[k], item)
}

// 格式化字段值
const formatFieldValue = (value: any, field: MobileField): string => {
  if (field.formatter) {
    return field.formatter(value)
  }
  
  if (value === null || value === undefined) {
    return '-'
  }
  
  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }
  
  if (typeof value === 'number') {
    return value.toLocaleString()
  }
  
  return String(value)
}

// 处理卡片点击
const handleCardClick = (row: any, index: number) => {
  emit('card-click', row, index)
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', handleResize, { passive: true })
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 监听分页变化
const handleCurrentPageChange = (page: number) => {
  emit('update:current-page', page)
}

const handlePageSizeChange = (size: number) => {
  emit('update:page-size', size)
}
</script>

<style lang="scss" scoped>
@use '@/styles/mixins.scss' as *;
@use '@/styles/variables.scss' as *;

.responsive-table {
  &__desktop {
    width: 100%;
  }
  
  &__mobile {
    width: 100%;
  }
  
  &__mobile-search {
    @include responsive-spacing(margin-bottom, $spacing-md, $spacing-mobile-md);
  }
  
  &__loading {
    @include responsive-spacing(padding, $spacing-lg, $spacing-mobile-md);
  }
  
  &__cards {
    display: flex;
    flex-direction: column;
    gap: $spacing-mobile-md;
  }
  
  &__card {
    @include card-style;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      box-shadow: $box-shadow-medium;
      transform: translateY(-2px);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  &__card-header {
    @include flex-between;
    @include responsive-spacing(padding, $spacing-md, $spacing-mobile-md);
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
  
  &__card-title {
    @include responsive-font-size(16px, 14px);
    font-weight: 600;
    color: var(--el-text-color-primary);
    @include text-ellipsis;
    flex: 1;
  }
  
  &__card-index {
    @include responsive-font-size(12px, 10px);
    color: var(--el-text-color-secondary);
    background: var(--el-bg-color-page);
    padding: 2px 6px;
    border-radius: $border-radius-sm;
  }
  
  &__card-content {
    @include responsive-spacing(padding, $spacing-md, $spacing-mobile-md);
  }
  
  &__card-field {
    @include flex-between;
    @include responsive-spacing(margin-bottom, $spacing-sm, $spacing-mobile-sm);
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  &__card-label {
    @include responsive-font-size(14px, 12px);
    color: var(--el-text-color-secondary);
    font-weight: 500;
    min-width: 80px;
  }
  
  &__card-value {
    @include responsive-font-size(14px, 12px);
    color: var(--el-text-color-primary);
    text-align: right;
    @include text-ellipsis;
    flex: 1;
    margin-left: $spacing-mobile-sm;
  }
  
  &__card-actions {
    @include flex-end;
    gap: $spacing-mobile-sm;
    @include responsive-spacing(padding, $spacing-md, $spacing-mobile-md);
    border-top: 1px solid var(--el-border-color-lighter);
  }
  
  &__empty {
    @include responsive-spacing(padding, $spacing-xl, $spacing-mobile-lg);
    text-align: center;
  }
  
  &__pagination {
    @include flex-center;
    @include responsive-spacing(margin-top, $spacing-lg, $spacing-mobile-md);
    @include responsive-spacing(padding, $spacing-md, $spacing-mobile-sm);
  }
}
</style>
