{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "E:\\h5-app"], "description": "文件系统操作 - 读写文件、创建目录、搜索文件等"}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "description": "逻辑推理和思维链 - 帮助AI进行复杂问题分析"}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_SEARCH_API_KEY": "你的_BRAVE_API_密钥_在这里"}, "description": "搜索引擎集成 - 网络搜索和本地搜索功能"}}, "配置说明": {"Claude Desktop": "将 mcpServers 部分复制到 ~/.config/claude/claude_desktop_config.json", "Cursor": "在 Cursor 设置中配置 MCP 服务器", "Windsurf": "在 Windsurf 设置中添加 MCP 配置", "注意事项": ["确保已安装 Node.js 和 npm", "Brave Search 需要 API 密钥，可在 https://brave.com/search/api/ 获取", "文件系统服务器的路径可以根据需要修改", "重启 AI 工具以使配置生效"]}}