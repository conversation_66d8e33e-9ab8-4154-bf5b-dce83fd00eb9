var isUI = {} // 界面
var myConfig = {}
var isData = {} // 数据

var isUIInit // 界面初始化
var isDataInit // 数据初始


if ( !localStorage.getItem("isUIInit") ) {
    $(document).ready(function(){
            $.getJSON("./config/menu.json", function(isUI){
                localStorage.setItem("isUI", JSON.stringify(isUI));
            });
            // alert("系统更新成功！");

            myConfig = {

                "isIndex": 0

            };

            localStorage.setItem("myConfig", JSON.stringify(myConfig));
            console.log(myConfig)
    });
    
    isUIInit = true;
    localStorage.setItem("isUIInit", JSON.stringify(isUIInit));
};


if ( !localStorage.getItem("isDataInit") ) {
    $(document).ready(function(){
            $.getJSON("./config/data.json", function(isData){
                localStorage.setItem("isData", JSON.stringify(isData));
            });
            // alert("系统更新成功！");
    });
    isDataInit = true;
    localStorage.setItem("isDataInit", JSON.stringify(isDataInit));
};
