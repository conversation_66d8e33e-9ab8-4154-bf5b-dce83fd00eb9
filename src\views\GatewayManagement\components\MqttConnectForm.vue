<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import type { MQTTConfig } from '@/types/gateway';
import { useGatewayStore } from '@/stores/gateway';

const props = defineProps<{
  deviceId: string;
  initialConfig?: MQTTConfig;
}>();

const emit = defineEmits<{
  (e: 'save', config: MQTTConfig): void;
  (e: 'test', result: { success: boolean; message: string }): void;
  (e: 'connect'): void;
  (e: 'disconnect'): void;
}>();

const gatewayStore = useGatewayStore();
const isSaving = ref(false);
const isTesting = ref(false);
const isLoading = ref(true);
const error = ref<string | null>(null);

// 表单数据
const formData = reactive<MQTTConfig>({
  brokerUrl: '',
  port: 1883,
  clientId: `client-${Date.now()}`,
  username: '',
  password: '',
  topicPrefix: 'gateway',
  publishTopic: 'gateway/publish',
  subscribeTopic: 'gateway/subscribe',
  topics: ['gateway/subscribe'],
  qos: 1,
  keepAlive: 60,
  reconnectPeriod: 1000
});

// 表单验证规则
const rules = {
  brokerUrl: [
    { required: true, message: 'MQTT服务器地址不能为空', trigger: 'blur' },
    { pattern: /^(mqtt|ws):\/\/.*/, message: '格式应为 mqtt://host:port 或 ws://host:port', trigger: 'blur' }
  ],
  clientId: [
    { required: true, message: '客户端ID不能为空', trigger: 'blur' }
  ],
  publishTopic: [
    { required: true, message: '发布主题不能为空', trigger: 'blur' }
  ],
  subscribeTopic: [
    { required: true, message: '订阅主题不能为空', trigger: 'blur' }
  ]
};

const formRef = ref();
const mqttConnected = computed(() => gatewayStore.mqttConnected);

// 加载初始配置
const loadConfig = async () => {
  isLoading.value = true;
  error.value = null;
  
  try {
    // 如果传入了初始配置，使用它
    if (props.initialConfig) {
      Object.assign(formData, props.initialConfig);
      isLoading.value = false;
      return;
    }
    
    // 否则尝试从服务器获取配置
    const config = await gatewayStore.fetchMqttConfig(props.deviceId);
    if (config) {
      Object.assign(formData, config);
    }
  } catch (err) {
    error.value = (err as Error).message;
    console.error('加载MQTT配置失败:', err);
  } finally {
    isLoading.value = false;
  }
};

// 保存配置
const saveConfig = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid: boolean) => {
    if (!valid) return;
    
    isSaving.value = true;
    error.value = null;
    
    try {
      const updatedConfig = await gatewayStore.updateMqttConfig(props.deviceId, formData);
      if (updatedConfig) {
        emit('save', formData);
      }
    } catch (err) {
      error.value = (err as Error).message;
      console.error('保存MQTT配置失败:', err);
    } finally {
      isSaving.value = false;
    }
  });
};

// 测试连接
const testConnection = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid: boolean) => {
    if (!valid) return;
    
    isTesting.value = true;
    error.value = null;
    
    try {
      const result = await gatewayStore.testConnection(JSON.stringify(formData));
      emit('test', result);
    } catch (err) {
      emit('test', {
        success: false,
        message: (err as Error).message
      });
      error.value = (err as Error).message;
      console.error('测试MQTT连接失败:', err);
    } finally {
      isTesting.value = false;
    }
  });
};

// 连接到MQTT
const connect = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid: boolean) => {
    if (!valid) return;
    
    isLoading.value = true;
    error.value = null;
    
    try {
      const success = await gatewayStore.connectToMqttDevice(props.deviceId);
      if (success) {
        emit('connect');
      }
    } catch (err) {
      error.value = (err as Error).message;
      console.error('连接MQTT设备失败:', err);
    } finally {
      isLoading.value = false;
    }
  });
};

// 断开MQTT连接
const disconnect = async () => {
  isLoading.value = true;
  error.value = null;
  
  try {
    await gatewayStore.disconnectMqtt();
    emit('disconnect');
  } catch (err) {
    error.value = (err as Error).message;
    console.error('断开MQTT连接失败:', err);
  } finally {
    isLoading.value = false;
  }
};

// 生成随机客户端ID
const generateClientId = () => {
  formData.clientId = `client-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
};

// 监听设备ID变化，重新加载配置
watch(() => props.deviceId, () => {
  loadConfig();
});

// 组件挂载时加载配置
loadConfig();
</script>

<template>
  <div class="mqtt-connect-form">
    <el-card v-loading="isLoading">
      <template #header>
        <div class="card-header">
          <span>MQTT连接配置</span>
          <div class="connection-status">
            <el-tag v-if="mqttConnected" type="success">已连接</el-tag>
            <el-tag v-else type="info">未连接</el-tag>
          </div>
        </div>
      </template>
      
      <div v-if="error" class="error-message">
        <el-alert :title="error" type="error" show-icon />
      </div>
      
      <el-form 
        ref="formRef" 
        :model="formData" 
        :rules="rules" 
        label-width="120px"
        label-position="left"
      >
        <el-form-item label="MQTT服务器" prop="brokerUrl">
          <el-input v-model="formData.brokerUrl" placeholder="如: mqtt://broker.emqx.io:1883" />
          <div class="form-item-help">MQTT代理服务器地址，格式为 mqtt://主机:端口 或 ws://主机:端口</div>
        </el-form-item>
        
        <el-form-item label="客户端ID" prop="clientId">
          <div class="input-with-button">
            <el-input v-model="formData.clientId" />
            <el-button type="primary" @click="generateClientId">随机生成</el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="用户名" prop="username">
          <el-input v-model="formData.username" placeholder="可选" />
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input v-model="formData.password" type="password" placeholder="可选" show-password />
        </el-form-item>
        
        <el-form-item label="主题前缀" prop="topicPrefix">
          <el-input v-model="formData.topicPrefix" placeholder="如: gateway" />
          <div class="form-item-help">主题前缀用于组织消息主题</div>
        </el-form-item>
        
        <el-form-item label="发布主题" prop="publishTopic">
          <el-input v-model="formData.publishTopic" />
          <div class="form-item-help">用于向设备发送命令的主题</div>
        </el-form-item>
        
        <el-form-item label="订阅主题" prop="subscribeTopic">
          <el-input v-model="formData.subscribeTopic" />
          <div class="form-item-help">用于接收设备数据的主题，可以使用通配符 # 和 +</div>
        </el-form-item>
        
        <el-form-item label="服务质量" prop="qos">
          <el-select v-model="formData.qos">
            <el-option :value="0" label="QoS 0 - 至多一次" />
            <el-option :value="1" label="QoS 1 - 至少一次" />
            <el-option :value="2" label="QoS 2 - 仅一次" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="保活间隔" prop="keepAlive">
          <el-input-number v-model="formData.keepAlive" :min="5" :max="300" />
          <div class="form-item-help">心跳包间隔，单位为秒</div>
        </el-form-item>
        
        <el-form-item label="重连间隔" prop="reconnectPeriod">
          <el-input-number v-model="formData.reconnectPeriod" :min="100" :max="10000" :step="100" />
          <div class="form-item-help">断线重连间隔，单位为毫秒</div>
        </el-form-item>
        
        <div class="form-actions">
          <el-button 
            type="primary" 
            @click="saveConfig" 
            :loading="isSaving"
          >
            保存配置
          </el-button>
          
          <el-button 
            @click="testConnection" 
            :loading="isTesting"
          >
            测试连接
          </el-button>
          
          <el-button 
            v-if="!mqttConnected" 
            type="success" 
            @click="connect"
            :loading="isLoading"
          >
            连接
          </el-button>
          
          <el-button 
            v-else 
            type="danger" 
            @click="disconnect"
            :loading="isLoading"
          >
            断开连接
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
.mqtt-connect-form {
  width: 100%;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .error-message {
    margin-bottom: 20px;
  }
  
  .form-item-help {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 5px;
  }
  
  .input-with-button {
    display: flex;
    gap: 10px;
  }
  
  .form-actions {
    margin-top: 30px;
    display: flex;
    gap: 15px;
    justify-content: flex-start;
  }
}
</style> 