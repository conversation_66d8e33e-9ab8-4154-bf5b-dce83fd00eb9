/*!************************************************************************
 *
 * ADOBE CONFIDENTIAL
 * ___________________
 *
 *  Copyright 2013 Adobe Systems Incorporated
 *  All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Adobe Systems Incorporated and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Adobe Systems Incorporated and its
 * suppliers and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Adobe Systems Incorporated.
 **************************************************************************/
"use strict";if(typeof s7responsiveImage=="undefined"){(function(a){var y="data-breakpoints";var K="data-mode";var f="smartcrop";var x="data-aspectratio";var I=0.5;var F="data-enablehd";var H="always";var i="never";function g(L){if(typeof window.console!="undefined"){window.console.log(L)}}var m=(function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(M,L){window.setTimeout(M,1000/60)}})();var q=q||[];var c=c||[];a.s7RIJSONResponse=function t(N,M){for(var L=0;L<q.length;L++){if(q[L]!=null){if(q[L].id==parseInt(M)){if(q[L].callback){q[L].callback(N)}delete q[L]}}}};a.s7jsonError=function e(O,N){var L=true;for(var M=0;M<c.length;M++){if(c[M]!=null){if(c[M].id==parseInt(N)){if(c[M].callback){c[M].callback(O)}delete c[M];L=false}}}if(L){g(O.message)}};function B(O,N,L,R){var M="";var Q=N.indexOf("?");if(Q>=0){M=N+"&"+O}else{M=N+"?"+O}var S=E(M);M+="&id="+S+"&handler=s7RIJSONResponse";if(typeof L!="undefined"){q.push({callback:L,id:S})}if(typeof R!="undefined"){c.push({callback:R,id:S})}var P=document.getElementById("sjScript_"+S);if(P){document.getElementsByTagName("head")[0].removeChild(P);P=null}P=document.createElement("script");P.type="text/javascript";P.id="sjScript_"+S;P.src=M;if(typeof P!="undefined"){document.getElementsByTagName("head")[0].appendChild(P)}}function E(N){var M=0;if(M==0&&N.length>0){for(var L=0;L<N.length;L++){M=31*M+N.charCodeAt(L);M=M&4294967295}}return M}function j(){var L=1;if(window.devicePixelRatio){L=window.devicePixelRatio}else{if(screen.deviceXDPI){L=window.screen.deviceXDPI/window.screen.logicalXDPI}else{if("matchMedia" in window&&window.matchMedia){if(window.matchMedia("(min-resolution: 2dppx)").matches||win.matchMedia("(min-resolution: 192dpi)").matches){L=2}else{if(window.matchMedia("(min-resolution: 1.5dppx)").matches||window.matchMedia("(min-resolution: 144dpi)").matches){L=1.5}}}}}return L}function C(){var L="landscape";var M=1;if(typeof(window.orientation)!="undefined"){L=((screen.width/screen.height)>1)?"landscape":"portrait";M=document.documentElement.clientWidth/window.innerWidth;return{zoom:M,orient:L}}else{if(document.documentElement.clientWidth>document.documentElement.clientHeight){L="landscape"}else{L="portrait"}return{zoom:1,orient:L}}}function d(M,L){return M-L}function J(M,L){return L-M}function l(M,L){return parseInt(M.width,10)-parseInt(L.width,10)}function p(L,M){return parseInt(L)||M}function o(L,N){var M=parseFloat(L);if(isNaN(M)){M=N}return M}function n(L){L=L||{};this.top=o(L.top,null);this.left=o(L.left,null);this.width=o(L.width,null);this.height=o(L.height,null)}function D(M,N){var L=M.split(",");if(L.length>=4){return new n({top:L[0],left:L[1],width:L[2],height:L[3]})}return null}function w(O,Q,P){var N;var M=decodeURI(O);if(Q&&(N=M.indexOf(Q))!=-1){var L=M.substring(0,N)+encodeURI(P);if(N+Q.length<M.length){L+=encodeURI(M.substring(N+Q.length))}return L}else{return O.match(/.*\//)[0]+encodeURI(P)}}function b(O,N,M){if(O.rect&&N&&M){var L=O.rect;if(L.width!=null&&L.width>0&&L.height!=null&&L.height>0){O.maxWidth=Math.min(Math.round(N*L.width),N);O.maxHeight=Math.min(Math.round(M*L.height),M);return true}}return false}function s(N,M,L){if(!b(N,M,L)){N.maxWidth=N.width;N.maxHeight=N.height}}function r(R,O){if(R.isCommands){var S=(R.isCommands).split("&");for(var L=0;L<S.length;L++){var N=S[L];var Q=N.toLowerCase().indexOf("src=");if(Q==0){if(O){R.src=N.substring(4);var M=O.match(/.*\//)[0]+R.src;var P=function(T){R.maxWidth=p(T["image.width"],null);R.maxHeight=p(T["image.height"],null)};G(M,P)}else{g("Cannot request req=props for src= due to missing dataSrc")}break}}}}function k(Q,L,P,O,N,M){Q=Q||{};this.name=String(Q.name||"");this.path=String(Q.path||"");this.width=p(Q.width,0);this.height=p(Q.height,null);if(Q.rect){this.rect=D(Q.rect)}this.isCommands=String(Q.isCommands||"");this.src=String(Q.src||"");this.maxWidth=null;this.maxHeight=null;if(this.width&&this.height){this.aspectRatio=this.width/this.height}if(typeof M!="undefined"){s(this,O,N)}else{r(this,L)}}function z(S,M,R,Q,P,N){if(!S||!S.set||!S.set.relation){return null}var O=[];var L;if(Array.isArray(S.set.relation)){L=S.set.relation}else{L=[S.set.relation]}L.forEach(function(V){var T={};if(V.n&&V.userdata){T.path=V.n;var U=V.userdata;if(U.SmartCropType==="Banner"&&U.SmartCropDef&&U.SmartCropWidth&&U.SmartCropHeight){T.name=U.SmartCropDef;T.width=U.SmartCropWidth;T.height=U.SmartCropHeight;T.rect=U.SmartCropRect;T.isCommands=U.isCommands;O.push(new k(T,M,R,Q,P,N))}}});return O}function h(N,L){var O=null;if(N&&N.length>0){O=[];for(var M=0;M<N.length;M++){var P=N[M].split(":");O.push(new k({width:parseInt(P[0],10),isCommands:P[1]},L))}}return O}function u(M){var N=new k();var L=new k();if(M&&M.length>0){M.sort(l);L=M[M.length-1];N=M[0]}return{breakpoints:M,minBreakpoint:N,maxBreakpoint:L}}function G(L,N,M){B("req=props,json&scl=1",L,function(O){N(O)},function(O){g("failed loading req=props for image ["+L+"]: "+O.message);if(M){M()}})}function v(O,N){var P=O-I;var L=O+I;if(N&&N.length>0){for(var M=N.length-1;M>=0;M--){if(P>N[M].aspectRatio||N[M].aspectRatio>L){N.splice(M,1)}}}}a.s7responsiveImage=function A(aa){var ar=aa;if(!ar){return}var L=ar.getAttribute("src")||"";var ab=ar.getAttribute("data-src")||"";ab=ab||L;if(!ab){return}var U;var Y=0;var al=-1;var am=null;var aj=null;var ad=null;var N=null;var ai=null;var S=null;var W=null;var R=false;var an=false;var X=false;var ah=navigator.userAgent.toLowerCase();var ac=ah.indexOf("msie");var Q=parseFloat(navigator.appVersion);var ao=null;var ae=true;var ak=false;var ag=null;var af=false;var Z=ar.getAttribute(K);ak=Z===f;af=ar.getAttribute(x)==="true";ae=(ar.getAttribute(F)&&ar.getAttribute(F).toLowerCase()===i)?false:true;var V=function(at){R=true;X=!ak||an;N=aj=ar.maxWidth=parseInt(at["image.width"]);ai=ad=ar.maxHeight=parseInt(at["image.height"]);ao=ar.maxWidth/ar.maxHeight;ar.style.width="100%";if(ak&&W){(W).forEach(function(au){b(au,aj,ad)})}};G(ab,V);if(!ak){var ap=ar.getAttribute(y);var M=(ap&&ap.split(",").length>0)?ap.split(","):null;var T=u(h(M,ab));W=T.breakpoints;S=T.maxBreakpoint}else{B("req=set,json",ab,function(au){X=R;if(au&&au.set){if(au.set.n){ag=au.set.n}}var at=u(z(au,ab,ag,aj,ad,ak));W=at.breakpoints;S=at.maxBreakpoint;an=true},function(at){g("failed loading req=set for image ["+ab+"]: "+at.message)})}function P(at,au){if(at>au){return au}var av=Math.ceil(au/Math.pow(2,(Math.floor(Math.log(au/at)/Math.log(2)))));if(av>1000){av=Math.min(Math.max(1000,Math.ceil(at/100)*100),au)}return av}function O(au,ay){var aw=al;var at;if(typeof U!="undefined"){at=U?U.width:-1}var av=am?am.width:-1;U=am;var ax=new Image();ax.onload=function(){ar.src=ax.src;var az=ax.width/ax.height;ar.style.width="100%";if((ac!=-1)&&(Q<9)){ar.style.height=ar.offsetWidth/az+"px"}if(Y==0||(at!=-1||av!=-1)){var aA=document.createEvent("Events");aA.s7responsiveViewerEvent={type:"breakpointchanged",width:av,oldWidth:at};aA.initEvent("s7responsiveViewer",true,false);ar.dispatchEvent(aA)}Y=aw;if(ay){ay()}};ax.onerror=function(){ax.src=ar.src};ax.onabort=function(){};al=Math.min(N,al);ax.src=au}function aq(az){if(!ar||!ar.parentNode||ar.parentNode.nodeType===11){return}if(!X||!ar.offsetWidth||ar.offsetWidth<=0){m(aq);return}var ay=parseFloat(document.defaultView.getComputedStyle(ar,null).getPropertyValue("width"));al=Math.ceil(ay);var ax=ae?j()*C().zoom:C().zoom;var aw="";if(W){if(af){v(ao,W)}if(ak&&al>S.width){am=null;al=P(al*ax,aj);N=ar.maxWidth=aj;ai=ar.maxHeight=ad}else{for(var av=0;av<W.length;av++){if(al<=W[av].width||av==W.length-1){am=W[av];al=Math.ceil(W[av].width*ax);aw=W[av].isCommands;if(ak||(!ak&&W[av].src&&W[av].src.length>0)){if(!W[av].maxWidth||!W[av].maxHeight){m(aq);return}}if(W[av].maxWidth&&W[av].maxHeight){N=ar.maxWidth=W[av].maxWidth;ai=ar.maxHeight=W[av].maxHeight}al=Math.min(N,al);break}}}}else{am=null;N=ar.maxWidth=aj;ai=ar.maxHeight=ad;al=P(al*ax,aj)}var au=U?U.width:-1;var at=am?am.width:-1;if(al!=Y||au!=at){var aA=ab;if(ak&&am){aA=w(ab,ag,am.path)}aA=aA+((aA.indexOf("?")==-1)?"?":"&")+"wid="+al;if(ak){aA+="&hei="+Math.ceil(al*ai/N)}aA+=((aw!="")?"&"+aw:"");aA+="&dpr=off";O(aA,function(){m(aq)})}else{m(aq)}}m(aq)}})(this)};