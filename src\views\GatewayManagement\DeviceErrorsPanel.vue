<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { ElMessage } from 'element-plus';
import { useGatewayStore } from '../../stores/gateway';
import type { GatewayError } from '../../types/gateway';

const props = defineProps<{
  deviceId: string;
}>();

const gatewayStore = useGatewayStore();
const loading = ref(false);
const errorLogs = ref<GatewayError[]>([]);
const autoRefresh = ref(false);
const refreshInterval = ref(10000); // 默认刷新间隔10秒
const searchQuery = ref('');
const filterResolved = ref(false); // 是否只显示未解决的错误
let refreshTimer: ReturnType<typeof setInterval> | null = null;

// 加载错误日志
const loadErrorLogs = async () => {
  loading.value = true;
  try {
    errorLogs.value = await gatewayStore.fetchErrorLogs(props.deviceId);
  } catch (error) {
    ElMessage.error('加载错误日志失败');
    console.error('加载错误日志失败:', error);
  } finally {
    loading.value = false;
  }
};

// 开始自动刷新
const startAutoRefresh = () => {
  stopAutoRefresh(); // 先停止现有的定时器
  
  refreshTimer = setInterval(() => {
    loadErrorLogs();
  }, refreshInterval.value);
};

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

// 监听自动刷新开关
const handleAutoRefreshChange = (value: boolean) => {
  if (value) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
};

// 监听刷新间隔变化
const handleRefreshIntervalChange = () => {
  if (autoRefresh.value) {
    startAutoRefresh();
  }
};

// 筛选错误日志
const filteredErrorLogs = computed(() => {
  let result = [...errorLogs.value];
  
  // 按查询条件过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(log => 
      log.errorMessage.toLowerCase().includes(query) || 
      log.errorCode.toString().includes(query)
    );
  }
  
  // 按解决状态过滤
  if (filterResolved.value) {
    result = result.filter(log => !log.resolved);
  }
  
  return result;
});

// 格式化日期时间
const formatDateTime = (dateString: string | Date | number) => {
  if (!dateString) return '--';
  const date = typeof dateString === 'number' ? new Date(dateString) : 
               typeof dateString === 'string' ? new Date(dateString) : dateString;
  return date.toLocaleString('zh-CN');
};

// 获取错误严重程度对应的类型
const getErrorTypeByCode = (code: number) => {
  if (code >= 5000) return 'danger';
  if (code >= 4000) return 'warning';
  if (code >= 3000) return 'info';
  return 'info';
};

// 导出错误日志
const exportErrorLogs = () => {
  // 创建CSV内容
  let csvContent = 'Code,Message,Timestamp,Resolved\n';
  errorLogs.value.forEach(log => {
    const row = [
      log.errorCode,
      `"${log.errorMessage.replace(/"/g, '""')}"`,
      formatDateTime(log.timestamp),
      log.resolved ? '是' : '否'
    ].join(',');
    csvContent += row + '\n';
  });
  
  // 创建Blob对象
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  
  // 创建下载链接并触发下载
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `device-${props.deviceId}-errors.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 组件挂载时加载数据
onMounted(() => {
  loadErrorLogs();
});

// 在组件销毁前清理定时器
onBeforeUnmount(() => {
  stopAutoRefresh();
});
</script>

<template>
  <div class="device-errors-panel" v-loading="loading">
    <div class="error-controls">
      <div class="search-filter">
        <el-input
          v-model="searchQuery"
          placeholder="搜索错误信息或代码"
          clearable
          prefix-icon="Search"
        />
        <el-checkbox v-model="filterResolved" style="margin-left: 10px;">
          只显示未解决错误
        </el-checkbox>
      </div>
      
      <div class="refresh-controls">
        <el-switch
          v-model="autoRefresh"
          inline-prompt
          active-text="自动刷新"
          inactive-text="关闭"
          @change="handleAutoRefreshChange"
        />
        <el-select
          v-model="refreshInterval"
          size="small"
          style="width: 110px; margin-left: 10px;"
          :disabled="!autoRefresh"
          @change="handleRefreshIntervalChange"
        >
          <el-option label="5秒" :value="5000" />
          <el-option label="10秒" :value="10000" />
          <el-option label="30秒" :value="30000" />
          <el-option label="1分钟" :value="60000" />
        </el-select>
        <el-button size="small" @click="loadErrorLogs" style="margin-left: 10px;">刷新</el-button>
        <el-button size="small" type="primary" @click="exportErrorLogs" style="margin-left: 10px;">导出</el-button>
      </div>
    </div>
    
    <div class="error-summary">
      <el-card class="summary-card">
        <div class="summary-title">总错误数</div>
        <div class="summary-value">{{ errorLogs.length }}</div>
      </el-card>
      
      <el-card class="summary-card">
        <div class="summary-title">未解决错误</div>
        <div class="summary-value error">
          {{ errorLogs.filter(log => !log.resolved).length }}
        </div>
      </el-card>
      
      <el-card class="summary-card">
        <div class="summary-title">已解决错误</div>
        <div class="summary-value success">
          {{ errorLogs.filter(log => log.resolved).length }}
        </div>
      </el-card>
    </div>
    
    <div class="error-table">
      <el-table
        :data="filteredErrorLogs"
        border
        stripe
        style="width: 100%"
        :empty-text="loading ? '加载中...' : '暂无错误日志'"
      >
        <el-table-column prop="errorCode" label="错误代码" width="100" sortable />
        <el-table-column label="严重程度" width="120">
          <template #default="{ row }">
            <el-tag :type="getErrorTypeByCode(row.errorCode)" size="small">
              {{ row.errorCode >= 5000 ? '严重' : row.errorCode >= 4000 ? '警告' : '信息' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="errorMessage" label="错误信息" min-width="200" show-overflow-tooltip />
        <el-table-column label="时间" width="180" sortable>
          <template #default="{ row }">
            {{ formatDateTime(new Date(row.timestamp)) }}
          </template>
        </el-table-column>
        <el-table-column label="设备ID" width="200" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.deviceId || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.resolved ? 'success' : 'danger'" size="small">
              {{ row.resolved ? '已解决' : '未解决' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <div v-if="!loading && errorLogs.length === 0" class="no-errors">
      <el-empty description="暂无错误日志" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.device-errors-panel {
  .error-controls {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    
    .search-filter {
      display: flex;
      align-items: center;
      width: 50%;
    }
    
    .refresh-controls {
      display: flex;
      align-items: center;
    }
  }
  
  .error-summary {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
    
    .summary-card {
      text-align: center;
      
      .summary-title {
        font-size: 14px;
        color: #606266;
        margin-bottom: 10px;
      }
      
      .summary-value {
        font-size: 24px;
        font-weight: bold;
        
        &.error {
          color: #f56c6c;
        }
        
        &.success {
          color: #67c23a;
        }
      }
    }
  }
  
  .error-table {
    margin-bottom: 20px;
  }
  
  .no-errors {
    padding: 30px 0;
  }
}
</style> 