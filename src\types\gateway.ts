// 变频器网关设备类型定义

export interface GatewayDevice {
  id: string;
  name: string;
  ip: string;
  port: number;
  modelName: string;
  manufacturer: string;
  firmwareVersion: string;
  status: 'online' | 'offline' | 'error';
  lastConnectTime?: string;
  description?: string;
  location?: string;
  mqttConfig?: MQTTConfig;
}

export interface InverterData {
  deviceId: string;
  timestamp: number;
  frequency: number;
  current: number;
  voltage: number;
  power: number;
  runningStatus: boolean;
  temperature: number;
  errorCode?: number;
  errorMessage?: string;
  rotationSpeed?: number;
  flowRate?: number;
  pressure?: number;
  torque?: number;
}

export interface MQTTConfig {
  brokerUrl: string;
  port: number;
  clientId: string;
  username?: string;
  password?: string;
  topicPrefix: string;
  publishTopic: string;
  subscribeTopic: string;
  topics: string[];
  qos: 0 | 1 | 2;
  keepAlive: number;
  reconnectPeriod: number;
}

export interface GatewayError {
  id: string;
  deviceId: string;
  timestamp: number;
  errorCode: number;
  errorMessage: string;
  severity: 'critical' | 'warning' | 'info';
  resolved: boolean;
  resolvedTime?: number;
} 