<template>
  <div class="warning-system">
    <!-- 控制面板 -->
    <el-card class="control-panel">
      <template #header>
        <div class="card-header">
          <el-icon><Warning /></el-icon>
          <span>故障预警系统</span>
          <div class="header-actions">
            <el-button-group size="small">
              <el-button
                :type="activeTab === 'overview' ? 'primary' : ''"
                @click="activeTab = 'overview'"
              >
                总览
              </el-button>
              <el-button
                :type="activeTab === 'warnings' ? 'primary' : ''"
                @click="activeTab = 'warnings'"
              >
                预警列表
              </el-button>
              <el-button
                :type="activeTab === 'devices' ? 'primary' : ''"
                @click="activeTab = 'devices'"
              >
                设备监控
              </el-button>
              <el-button
                :type="activeTab === 'rules' ? 'primary' : ''"
                @click="activeTab = 'rules'"
              >
                规则配置
              </el-button>
            </el-button-group>

            <el-switch
              v-model="warningStore.realTimeEnabled"
              @change="toggleRealTime"
              inline-prompt
              active-text="实时"
              inactive-text="静态"
              size="small"
            />

            <el-button size="small" @click="refreshData" :loading="warningStore.loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
    </el-card>

    <!-- 总览页面 -->
    <div v-if="activeTab === 'overview'" class="overview-section">
      <!-- 关键指标卡片 -->
      <div class="kpi-cards">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="kpi-card critical">
              <div class="kpi-content">
                <div class="kpi-icon">
                  <el-icon :size="32" color="#F56C6C">
                    <Warning />
                  </el-icon>
                </div>
                <div class="kpi-data">
                  <div class="kpi-value">{{ warningStore.getCriticalCount }}</div>
                  <div class="kpi-label">严重预警</div>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6">
            <el-card class="kpi-card warning">
              <div class="kpi-content">
                <div class="kpi-icon">
                  <el-icon :size="32" color="#E6A23C">
                    <Bell />
                  </el-icon>
                </div>
                <div class="kpi-data">
                  <div class="kpi-value">{{ warningStore.getUnresolvedCount }}</div>
                  <div class="kpi-label">未处理预警</div>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6">
            <el-card class="kpi-card success">
              <div class="kpi-content">
                <div class="kpi-icon">
                  <el-icon :size="32" color="#67C23A">
                    <CircleCheck />
                  </el-icon>
                </div>
                <div class="kpi-data">
                  <div class="kpi-value">{{ warningStore.getOnlineDevicesCount }}</div>
                  <div class="kpi-label">在线设备</div>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6">
            <el-card class="kpi-card info">
              <div class="kpi-content">
                <div class="kpi-icon">
                  <el-icon :size="32" color="#909399">
                    <Tools />
                  </el-icon>
                </div>
                <div class="kpi-data">
                  <div class="kpi-value">{{ warningStore.getMaintenanceDevicesCount }}</div>
                  <div class="kpi-label">维护中设备</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 图表区域 -->
      <el-row :gutter="20" class="charts-section">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>今日预警趋势</span>
            </template>
            <div ref="trendChartRef" class="chart" />
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card>
            <template #header>
              <span>预警类别分布</span>
            </template>
            <div ref="categoryChartRef" class="chart" />
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="charts-section">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>设备健康度分布</span>
            </template>
            <div ref="healthChartRef" class="chart" />
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card>
            <template #header>
              <span>预警处理统计</span>
            </template>
            <div ref="resolutionChartRef" class="chart" />
          </el-card>
        </el-col>
      </el-row>

      <!-- 最新预警列表 -->
      <el-card class="recent-warnings">
        <template #header>
          <div class="section-header">
            <span>最新预警</span>
            <el-button size="small" @click="activeTab = 'warnings'">
              查看全部
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
        </template>

        <RecentWarningsList :warnings="recentWarnings" @resolve="resolveWarning" />
      </el-card>
    </div>

    <!-- 预警列表页面 -->
    <div v-if="activeTab === 'warnings'" class="warnings-section">
      <WarningsList />
    </div>

    <!-- 设备监控页面 -->
    <div v-if="activeTab === 'devices'" class="devices-section">
      <DeviceMonitor />
    </div>

    <!-- 规则配置页面 -->
    <div v-if="activeTab === 'rules'" class="rules-section">
      <RulesConfig />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import {
  Warning,
  Bell,
  CircleCheck,
  Tools,
  Refresh,
  ArrowRight
} from '@element-plus/icons-vue'
import { useWarningStore } from '@/stores/warning'
import { initWarningCharts } from './chart-config'
import RecentWarningsList from './RecentWarningsList.vue'
import WarningsList from './WarningsList.vue'
import DeviceMonitor from './DeviceMonitor.vue'
import RulesConfig from './RulesConfig.vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const warningStore = useWarningStore()
const activeTab = ref('overview')

// 图表引用
const trendChartRef = ref<HTMLElement>()
const categoryChartRef = ref<HTMLElement>()
const healthChartRef = ref<HTMLElement>()
const resolutionChartRef = ref<HTMLElement>()

// 计算属性
const recentWarnings = computed(() =>
  warningStore.getFilteredWarnings.slice(0, 10)
)

// 图表实例
let chartInstances: any = {}

// 方法
const toggleRealTime = (enabled: boolean) => {
  if (enabled) {
    warningStore.startRealTimeUpdate()
    ElMessage.success('已开启实时监控')
  } else {
    warningStore.stopRealTimeUpdate()
    ElMessage.info('已关闭实时监控')
  }
}

const refreshData = () => {
  warningStore.loadWarnings()
  ElMessage.success('数据已刷新')
}

const resolveWarning = async (warningId: string) => {
  const success = await warningStore.resolveWarning(warningId)
  if (success) {
    ElMessage.success('预警已处理')
    updateCharts()
  } else {
    ElMessage.error('处理失败')
  }
}

const updateCharts = () => {
  if (!warningStore.statistics) return

  nextTick(() => {
    chartInstances = initWarningCharts({
      trendChart: trendChartRef.value,
      categoryChart: categoryChartRef.value,
      healthChart: healthChartRef.value,
      resolutionChart: resolutionChartRef.value
    }, {
      statistics: warningStore.statistics,
      trendData: warningStore.getTodayWarningTrend,
      healthDistribution: warningStore.getDeviceHealthDistribution
    })
  })
}

// 生命周期
onMounted(async () => {
  await warningStore.loadWarnings()
  await nextTick()
  updateCharts()
})
</script>

<style lang="scss" scoped>
.warning-system {
  .control-panel {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      > div:first-child {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }
  }

  .overview-section {
    .kpi-cards {
      margin-bottom: 20px;

      .kpi-card {
        height: 120px;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .kpi-content {
          display: flex;
          align-items: center;
          height: 100%;

          .kpi-icon {
            margin-right: 16px;
          }

          .kpi-data {
            flex: 1;

            .kpi-value {
              font-size: 28px;
              font-weight: bold;
              color: var(--el-text-color-primary);
              margin-bottom: 4px;
            }

            .kpi-label {
              font-size: 14px;
              color: var(--el-text-color-regular);
            }
          }
        }

        &.critical {
          border-left: 4px solid #F56C6C;
        }

        &.warning {
          border-left: 4px solid #E6A23C;
        }

        &.success {
          border-left: 4px solid #67C23A;
        }

        &.info {
          border-left: 4px solid #909399;
        }
      }
    }

    .charts-section {
      margin-bottom: 20px;

      .chart {
        height: 300px;
        width: 100%;
      }
    }

    .recent-warnings {
      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
      }
    }
  }
}

// 全局样式覆盖
:deep(.el-card__body) {
  padding: 16px;
}
</style>
