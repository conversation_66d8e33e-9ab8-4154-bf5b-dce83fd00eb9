export interface AlgorithmParamsType {
  linear: {
    type: string;
    weights: string;
  };
  polynomial: {
    degree: number;
    regularization: number;
  };
  spline: {
    type: string;
    smoothing: number;
  };
  neural: {
    hiddenLayers: number;
    neurons: number;
    learningRate: number;
    epochs: number;
  };
  bezier: {
    controlPoints: number;
    tension: number;
  };
}

export interface MetricsType {
  rSquared: number;
  mse: number;
  mae: number;
  computeTime: number;
}

export interface Algorithm {
  id: string;
  title: string;
  icon: string;
  description: string;
  accuracy: number;
  speed: number;
} 