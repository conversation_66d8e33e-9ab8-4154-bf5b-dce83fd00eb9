<template>
  <div class="algorithm-comparison">
    <el-tabs v-model="activeTab" type="card">
      <!-- 性能对比 -->
      <el-tab-pane label="性能对比" name="performance">
        <div class="performance-comparison">
          <el-table :data="performanceData" border>
            <el-table-column prop="algorithm" label="算法" width="120">
              <template #default="{ row }">
                <el-tag :type="getAlgorithmTagType(row.algorithm)">
                  {{ (algorithmNames as Record<string, string>)[row.algorithm] || row.algorithm }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="Q-H曲线" align="center">
              <el-table-column prop="qh.r2Score" label="R²" width="80">
                <template #default="{ row }">
                  <span :class="getScoreClass(row.qh.r2Score)">
                    {{ row.qh.r2Score.toFixed(4) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="qh.mse" label="MSE" width="80">
                <template #default="{ row }">
                  {{ row.qh.mse.toFixed(4) }}
                </template>
              </el-table-column>
              <el-table-column prop="qh.trainingTime" label="训练时间(ms)" width="100">
                <template #default="{ row }">
                  {{ row.qh.trainingTime.toFixed(1) }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="Q-η曲线" align="center">
              <el-table-column prop="qeta.r2Score" label="R²" width="80">
                <template #default="{ row }">
                  <span :class="getScoreClass(row.qeta.r2Score)">
                    {{ row.qeta.r2Score.toFixed(4) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="qeta.mse" label="MSE" width="80">
                <template #default="{ row }">
                  {{ row.qeta.mse.toFixed(4) }}
                </template>
              </el-table-column>
              <el-table-column prop="qeta.trainingTime" label="训练时间(ms)" width="100">
                <template #default="{ row }">
                  {{ row.qeta.trainingTime.toFixed(1) }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="Q-P曲线" align="center">
              <el-table-column prop="qp.r2Score" label="R²" width="80">
                <template #default="{ row }">
                  <span :class="getScoreClass(row.qp.r2Score)">
                    {{ row.qp.r2Score.toFixed(4) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="qp.mse" label="MSE" width="80">
                <template #default="{ row }">
                  {{ row.qp.mse.toFixed(4) }}
                </template>
              </el-table-column>
              <el-table-column prop="qp.trainingTime" label="训练时间(ms)" width="100">
                <template #default="{ row }">
                  {{ row.qp.trainingTime.toFixed(1) }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="综合评分" width="100">
              <template #default="{ row }">
                <el-rate
                  v-model="row.overallScore"
                  disabled
                  show-score
                  text-color="#ff9900"
                  score-template="{value}"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- 可视化对比 -->
      <el-tab-pane label="可视化对比" name="visualization">
        <div class="visualization-comparison">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card>
                <template #header>R²得分对比</template>
                <div ref="r2ChartRef" class="chart" />
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card>
                <template #header>MSE对比</template>
                <div ref="mseChartRef" class="chart" />
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card>
                <template #header>训练时间对比</template>
                <div ref="timeChartRef" class="chart" />
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>

      <!-- 推荐建议 -->
      <el-tab-pane label="推荐建议" name="recommendation">
        <div class="recommendation">
          <el-alert
            :title="bestAlgorithm.title"
            :description="bestAlgorithm.description"
            type="success"
            show-icon
            :closable="false"
          />
          
          <div class="recommendation-details">
            <h3>详细分析</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="最佳算法">
                <el-tag type="success">{{ algorithmNames[bestAlgorithm.algorithm] }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="综合得分">
                {{ bestAlgorithm.score.toFixed(2) }}
              </el-descriptions-item>
              <el-descriptions-item label="优势">
                {{ bestAlgorithm.advantages }}
              </el-descriptions-item>
              <el-descriptions-item label="适用场景">
                {{ bestAlgorithm.useCase }}
              </el-descriptions-item>
            </el-descriptions>
            
            <h3>各算法特点</h3>
            <el-row :gutter="20">
              <el-col :span="12" v-for="item in algorithmFeatures" :key="item.algorithm">
                <el-card class="feature-card">
                  <template #header>
                    <el-tag :type="getAlgorithmTagType(item.algorithm)">
                      {{ algorithmNames[item.algorithm] }}
                    </el-tag>
                  </template>
                  <div class="features">
                    <div class="feature-item">
                      <strong>优点：</strong>{{ item.pros }}
                    </div>
                    <div class="feature-item">
                      <strong>缺点：</strong>{{ item.cons }}
                    </div>
                    <div class="feature-item">
                      <strong>适用：</strong>{{ item.suitable }}
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { AlgorithmType } from '@/types'

interface Props {
  comparisonData: Array<{
    algorithm: AlgorithmType
    results: {
      QH: any
      QETA: any
      QP: any
    }
  }>
}

const props = defineProps<Props>()

const activeTab = ref('performance')
const r2ChartRef = ref<HTMLElement>()
const mseChartRef = ref<HTMLElement>()
const timeChartRef = ref<HTMLElement>()

// 算法名称映射
const algorithmNames = {
  'least-squares': '最小二乘法',
  'polynomial': '多项式拟合',
  'neural-network': '神经网络',
  'spline': '样条插值'
}

// 算法特点
const algorithmFeatures = [
  {
    algorithm: 'least-squares' as AlgorithmType,
    pros: '计算快速，稳定可靠，理论基础扎实',
    cons: '对非线性关系拟合能力有限',
    suitable: '线性或近似线性关系，实时计算场景'
  },
  {
    algorithm: 'polynomial' as AlgorithmType,
    pros: '可拟合复杂非线性关系，参数可解释',
    cons: '高阶多项式容易过拟合',
    suitable: '中等复杂度的非线性关系'
  },
  {
    algorithm: 'neural-network' as AlgorithmType,
    pros: '强大的非线性拟合能力，自适应学习',
    cons: '训练时间长，参数不易解释',
    suitable: '复杂非线性关系，大数据集'
  },
  {
    algorithm: 'spline' as AlgorithmType,
    pros: '局部拟合精度高，平滑性好',
    cons: '对噪声敏感，外推能力差',
    suitable: '数据点密集，要求平滑的曲线'
  }
]

// 计算属性
const performanceData = computed(() => {
  return props.comparisonData.map(item => {
    const qh = item.results.QH
    const qeta = item.results.QETA
    const qp = item.results.QP
    
    // 计算综合评分
    const avgR2 = (qh.r2Score + qeta.r2Score + qp.r2Score) / 3
    const avgMSE = (qh.mse + qeta.mse + qp.mse) / 3
    const avgTime = (qh.trainingTime + qeta.trainingTime + qp.trainingTime) / 3
    
    // 综合评分算法：R²权重0.6，MSE权重0.3，时间权重0.1
    const overallScore = Math.min(5, Math.max(1, 
      avgR2 * 3 + (1 - Math.min(avgMSE / 10, 1)) * 1.5 + (1 - Math.min(avgTime / 1000, 1)) * 0.5
    ))
    
    return {
      algorithm: item.algorithm,
      qh,
      qeta,
      qp,
      overallScore: Math.round(overallScore * 10) / 10
    }
  })
})

const bestAlgorithm = computed(() => {
  if (performanceData.value.length === 0) {
    return {
      algorithm: 'least-squares' as AlgorithmType,
      title: '暂无比较数据',
      description: '请先运行算法比较',
      score: 0,
      advantages: '',
      useCase: ''
    }
  }
  
  const best = performanceData.value.reduce((prev, current) => 
    current.overallScore > prev.overallScore ? current : prev
  )
  
  const algorithmInfo = {
    'least-squares': {
      title: '推荐使用最小二乘法',
      description: '最小二乘法在当前数据集上表现最佳，具有良好的拟合精度和计算效率。',
      advantages: '计算速度快，稳定性好，适合实时应用',
      useCase: '适用于线性或近似线性的水泵特性曲线拟合'
    },
    'polynomial': {
      title: '推荐使用多项式拟合',
      description: '多项式拟合在当前数据集上表现最佳，能够很好地捕捉非线性特征。',
      advantages: '拟合精度高，参数易于理解和调整',
      useCase: '适用于具有明显非线性特征的水泵特性曲线'
    },
    'neural-network': {
      title: '推荐使用神经网络',
      description: '神经网络在当前数据集上表现最佳，具有强大的非线性建模能力。',
      advantages: '非线性拟合能力强，适应性好',
      useCase: '适用于复杂的非线性水泵特性曲线和大数据集'
    },
    'spline': {
      title: '推荐使用样条插值',
      description: '样条插值在当前数据集上表现最佳，提供了平滑且精确的曲线拟合。',
      advantages: '局部拟合精度高，曲线平滑性好',
      useCase: '适用于数据点密集且要求高平滑性的场景'
    }
  }
  
  return {
    algorithm: best.algorithm,
    score: best.overallScore,
    ...algorithmInfo[best.algorithm]
  }
})

// 方法
const getAlgorithmTagType = (algorithm: AlgorithmType) => {
  const types = {
    'least-squares': 'primary',
    'polynomial': 'success',
    'neural-network': 'warning',
    'spline': 'info'
  }
  return types[algorithm] || 'primary'
}

const getScoreClass = (score: number) => {
  if (score >= 0.95) return 'score-excellent'
  if (score >= 0.90) return 'score-good'
  if (score >= 0.80) return 'score-fair'
  return 'score-poor'
}

const initCharts = () => {
  if (!props.comparisonData.length) return
  
  // R²得分对比图
  if (r2ChartRef.value) {
    const r2Chart = echarts.init(r2ChartRef.value)
    const r2Option = {
      tooltip: { trigger: 'axis' },
      legend: { data: ['Q-H', 'Q-η', 'Q-P'] },
      xAxis: {
        type: 'category',
        data: props.comparisonData.map(item => algorithmNames[item.algorithm])
      },
      yAxis: { type: 'value', min: 0, max: 1 },
      series: [
        {
          name: 'Q-H',
          type: 'bar',
          data: props.comparisonData.map(item => item.results.QH.r2Score)
        },
        {
          name: 'Q-η',
          type: 'bar',
          data: props.comparisonData.map(item => item.results.QETA.r2Score)
        },
        {
          name: 'Q-P',
          type: 'bar',
          data: props.comparisonData.map(item => item.results.QP.r2Score)
        }
      ]
    }
    r2Chart.setOption(r2Option)
  }
  
  // MSE对比图
  if (mseChartRef.value) {
    const mseChart = echarts.init(mseChartRef.value)
    const mseOption = {
      tooltip: { trigger: 'axis' },
      legend: { data: ['Q-H', 'Q-η', 'Q-P'] },
      xAxis: {
        type: 'category',
        data: props.comparisonData.map(item => algorithmNames[item.algorithm])
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: 'Q-H',
          type: 'line',
          data: props.comparisonData.map(item => item.results.QH.mse)
        },
        {
          name: 'Q-η',
          type: 'line',
          data: props.comparisonData.map(item => item.results.QETA.mse)
        },
        {
          name: 'Q-P',
          type: 'line',
          data: props.comparisonData.map(item => item.results.QP.mse)
        }
      ]
    }
    mseChart.setOption(mseOption)
  }
  
  // 训练时间对比图
  if (timeChartRef.value) {
    const timeChart = echarts.init(timeChartRef.value)
    const timeOption = {
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: '50%',
        data: props.comparisonData.map(item => ({
          name: algorithmNames[item.algorithm],
          value: (item.results.QH.trainingTime + item.results.QETA.trainingTime + item.results.QP.trainingTime) / 3
        }))
      }]
    }
    timeChart.setOption(timeOption)
  }
}

onMounted(async () => {
  await nextTick()
  initCharts()
})
</script>

<style lang="scss" scoped>
.algorithm-comparison {
  .performance-comparison {
    .score-excellent { color: #67C23A; font-weight: bold; }
    .score-good { color: #409EFF; font-weight: bold; }
    .score-fair { color: #E6A23C; }
    .score-poor { color: #F56C6C; }
  }
  
  .visualization-comparison {
    .chart {
      height: 300px;
    }
  }
  
  .recommendation {
    .recommendation-details {
      margin-top: 20px;
      
      h3 {
        margin: 20px 0 10px 0;
        color: var(--el-text-color-primary);
      }
    }
    
    .feature-card {
      margin-bottom: 16px;
      
      .features {
        .feature-item {
          margin-bottom: 8px;
          line-height: 1.6;
          
          strong {
            color: var(--el-text-color-primary);
          }
        }
      }
    }
  }
}
</style>
