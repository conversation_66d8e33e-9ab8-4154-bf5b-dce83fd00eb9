<template>
  <div class="user-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><User /></el-icon>
          <span>用户管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="showAddUser">
              <el-icon><Plus /></el-icon>
              添加用户
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 统计卡片 -->
      <div class="user-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon size="24" color="#67C23A"><UserFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ settingsStore.getActiveUsersCount }}</div>
                <div class="stat-label">活跃用户</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon size="24" color="#409EFF"><Avatar /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ settingsStore.users.length }}</div>
                <div class="stat-label">总用户数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon size="24" color="#E6A23C"><Lock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ settingsStore.getRoleStats.admin }}</div>
                <div class="stat-label">管理员</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon size="24" color="#F56C6C"><Tools /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ settingsStore.getRoleStats.operator }}</div>
                <div class="stat-label">操作员</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <!-- 筛选条件 -->
      <div class="user-filters">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-select v-model="filters.role" placeholder="选择角色" clearable>
              <el-option label="管理员" value="admin" />
              <el-option label="操作员" value="operator" />
              <el-option label="维护人员" value="maintenance" />
              <el-option label="查看者" value="viewer" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filters.status" placeholder="选择状态" clearable>
              <el-option label="活跃" value="active" />
              <el-option label="非活跃" value="inactive" />
              <el-option label="锁定" value="locked" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filters.department" placeholder="选择部门" clearable>
              <el-option label="信息技术部" value="信息技术部" />
              <el-option label="运行部" value="运行部" />
              <el-option label="维护部" value="维护部" />
              <el-option label="质量部" value="质量部" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button @click="clearFilters">清除筛选</el-button>
          </el-col>
        </el-row>
      </div>
      
      <!-- 用户表格 -->
      <el-table :data="filteredUsers" stripe v-loading="settingsStore.loading">
        <el-table-column prop="avatar" label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :src="row.avatar" :alt="row.fullName">
              {{ row.fullName.charAt(0) }}
            </el-avatar>
          </template>
        </el-table-column>
        
        <el-table-column prop="username" label="用户名" width="120" />
        
        <el-table-column prop="fullName" label="姓名" width="120" />
        
        <el-table-column prop="email" label="邮箱" min-width="180" />
        
        <el-table-column prop="role" label="角色" width="100">
          <template #default="{ row }">
            <el-tag :type="getRoleType(row.role)">
              {{ getRoleText(row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="department" label="部门" width="120" />
        
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="lastLogin" label="最后登录" width="150">
          <template #default="{ row }">
            {{ formatTime(row.lastLogin) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="editUser(row)">
              编辑
            </el-button>
            <el-button 
              size="small" 
              :type="row.status === 'active' ? 'warning' : 'success'"
              @click="toggleUserStatus(row.id)"
            >
              {{ row.status === 'active' ? '禁用' : '启用' }}
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteUser(row.id)"
              :disabled="row.role === 'admin'"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 用户编辑对话框 -->
    <UserEditDialog 
      v-model="settingsStore.showUserDialog"
      :user="settingsStore.selectedUser"
      @save="handleUserSave"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  User, 
  Plus, 
  UserFilled, 
  Avatar, 
  Lock, 
  Tools 
} from '@element-plus/icons-vue'
import { useSettingsStore } from '@/stores/settings'
import UserEditDialog from './UserEditDialog.vue'
import type { User as UserType, UserRole } from '@/types'
import dayjs from 'dayjs'
import { ElMessage, ElMessageBox } from 'element-plus'

const settingsStore = useSettingsStore()

// 筛选条件
const filters = ref({
  role: null as UserRole | null,
  status: null as UserType['status'] | null,
  department: null as string | null
})

// 计算属性
const filteredUsers = computed(() => {
  let users = [...settingsStore.users]
  
  if (filters.value.role) {
    users = users.filter(u => u.role === filters.value.role)
  }
  
  if (filters.value.status) {
    users = users.filter(u => u.status === filters.value.status)
  }
  
  if (filters.value.department) {
    users = users.filter(u => u.department === filters.value.department)
  }
  
  return users.sort((a, b) => 
    new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
  )
})

// 方法
const getRoleType = (role: UserRole) => {
  const types = {
    admin: 'danger',
    operator: 'primary',
    maintenance: 'warning',
    viewer: 'info'
  }
  return types[role] || 'info'
}

const getRoleText = (role: UserRole) => {
  const texts = {
    admin: '管理员',
    operator: '操作员',
    maintenance: '维护人员',
    viewer: '查看者'
  }
  return texts[role] || role
}

const getStatusType = (status: UserType['status']) => {
  const types = {
    active: 'success',
    inactive: 'warning',
    locked: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: UserType['status']) => {
  const texts = {
    active: '活跃',
    inactive: '非活跃',
    locked: '锁定'
  }
  return texts[status] || status
}

const formatTime = (timestamp: string) => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm')
}

const showAddUser = () => {
  settingsStore.showUserEditor()
}

const editUser = (user: UserType) => {
  settingsStore.showUserEditor(user)
}

const toggleUserStatus = async (userId: string) => {
  try {
    const newStatus = await settingsStore.toggleUserStatus(userId)
    ElMessage.success(`用户已${newStatus === 'active' ? '启用' : '禁用'}`)
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const deleteUser = async (userId: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个用户吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const success = await settingsStore.deleteUser(userId)
    if (success) {
      ElMessage.success('用户删除成功')
    } else {
      ElMessage.error('用户删除失败')
    }
  } catch {
    // 用户取消
  }
}

const handleUserSave = async (userData: any) => {
  try {
    if (settingsStore.selectedUser) {
      await settingsStore.updateUser(settingsStore.selectedUser.id, userData)
      ElMessage.success('用户更新成功')
    } else {
      await settingsStore.createUser(userData)
      ElMessage.success('用户创建成功')
    }
    settingsStore.hideUserEditor()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const clearFilters = () => {
  filters.value = {
    role: null,
    status: null,
    department: null
  }
}
</script>

<style lang="scss" scoped>
.user-management {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    > div:first-child {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
    }
  }
  
  .user-stats {
    margin-bottom: 20px;
    
    .stat-card {
      display: flex;
      align-items: center;
      padding: 16px;
      background: var(--el-bg-color);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      
      .stat-icon {
        margin-right: 12px;
      }
      
      .stat-info {
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
  
  .user-filters {
    margin-bottom: 20px;
  }
}
</style>
