// 格兰富精确复制样式
.grundfos-exact-copy {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #ffffff;
  color: #333333;
  line-height: 1.5;

  // 面包屑导航
  .breadcrumb-nav {
    padding: 16px 24px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;

    .breadcrumb {
      list-style: none;
      display: flex;
      align-items: center;
      margin: 0;
      padding: 0;
      font-size: 14px;

      li {
        display: flex;
        align-items: center;

        &:not(:last-child)::after {
          content: '>';
          margin: 0 8px;
          color: #6c757d;
        }

        a {
          color: #0066cc;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }

        &.current {
          color: #6c757d;
          font-weight: 500;
        }
      }
    }
  }

  // 工具栏
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px;
    background-color: #ffffff;
    border-bottom: 1px solid #e9ecef;

    .toggle-btn {
      background: none;
      border: 1px solid #dee2e6;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;

      &:hover {
        background-color: #f8f9fa;
      }
    }

    .toolbar-buttons {
      display: flex;
      gap: 12px;

      button {
        background: none;
        border: 1px solid #dee2e6;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        white-space: nowrap;

        &:hover {
          background-color: #f8f9fa;
        }
      }

      .catalog-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 4px 12px;

        span {
          font-size: 12px;
          line-height: 1.2;
        }
      }
    }
  }

  // 产品信息区域
  .product-info-section {
    padding: 24px;
    background-color: #ffffff;

    .product-header {
      display: grid;
      grid-template-columns: auto 1fr auto;
      gap: 24px;
      align-items: start;

      .action-buttons {
        display: flex;
        gap: 12px;

        button {
          background: none;
          border: 1px solid #dee2e6;
          padding: 8px 16px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;

          &:hover {
            background-color: #f8f9fa;
          }
        }
      }

      .product-details {
        .category-link a {
          color: #0066cc;
          text-decoration: none;
          font-size: 14px;

          &:hover {
            text-decoration: underline;
          }
        }

        .product-title {
          font-size: 32px;
          font-weight: 700;
          margin: 8px 0;
          color: #333333;
        }

        .product-number {
          font-size: 16px;
          color: #6c757d;
          margin: 0 0 16px 0;
        }

        .product-applications {
          display: flex;
          gap: 24px;
          margin-top: 16px;

          .applications-section {
            strong {
              display: block;
              margin-bottom: 8px;
              font-weight: 600;
            }

            .application-list {
              list-style: none;
              padding: 0;
              margin: 0 0 8px 0;

              li {
                margin-bottom: 4px;

                a {
                  color: #0066cc;
                  text-decoration: none;
                  font-size: 14px;

                  &:hover {
                    text-decoration: underline;
                  }
                }
              }
            }

            .view-more-btn {
              background: none;
              border: none;
              color: #0066cc;
              cursor: pointer;
              font-size: 14px;
              text-decoration: underline;

              &:hover {
                text-decoration: none;
              }
            }
          }

          .product-description {
            color: #6c757d;
            font-size: 14px;
            margin: 0;
          }
        }
      }

      .product-actions {
        display: flex;
        flex-direction: column;
        gap: 16px;
        align-items: flex-end;

        .action-buttons-right {
          display: flex;
          gap: 8px;

          button {
            background: none;
            border: 1px solid #dee2e6;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;

            &:hover {
              background-color: #f8f9fa;
            }
          }
        }

        .price-section {
          text-align: right;
          font-size: 14px;

          .price-label {
            font-weight: 600;
            margin-bottom: 4px;
          }

          .price-status {
            color: #6c757d;
            margin-bottom: 4px;
          }

          .price-note {
            color: #6c757d;
            font-size: 12px;
            margin-bottom: 8px;
          }

          .buy-btn {
            background-color: #0066cc;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            display: inline-block;
            font-weight: 500;

            &:hover {
              background-color: #0052a3;
            }
          }
        }
      }
    }
  }

  // 主要内容区域
  .main-content {
    background-color: #ffffff;

    .tab-container {
      padding: 0 24px;
      border-bottom: 1px solid #e9ecef;

      .product-title-with-number {
        font-size: 24px;
        font-weight: 600;
        margin: 16px 0;
        color: #333333;

        .module-number {
          font-size: 16px;
          font-weight: 400;
          color: #6c757d;
          margin-left: 8px;
        }
      }

      .tabs {
        display: flex;
        gap: 0;

        .tab {
          background: none;
          border: none;
          padding: 12px 24px;
          cursor: pointer;
          font-size: 16px;
          border-bottom: 3px solid transparent;
          color: #6c757d;

          &:hover {
            color: #333333;
          }

          &.active {
            color: #0066cc;
            border-bottom-color: #0066cc;
            font-weight: 500;
          }
        }
      }
    }

    .performance-section {
      position: relative;
      padding: 24px;

      .enlarge-button {
        position: absolute;
        top: 24px;
        right: 24px;
        z-index: 10;

        .enlarge-btn {
          background: rgba(255, 255, 255, 0.9);
          border: 1px solid #dee2e6;
          padding: 6px 12px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
          backdrop-filter: blur(4px);

          &:hover {
            background: rgba(255, 255, 255, 1);
          }
        }
      }

      .performance-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 32px;
        align-items: start;

        .performance-chart-area {
          h3 {
            font-size: 20px;
            font-weight: 600;
            margin: 0 0 16px 0;
            color: #333333;
          }

          .chart-container {
            width: 100%;
            height: 500px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background-color: #ffffff;
            position: relative;
          }
        }

        .settings-panel {
          h3 {
            font-size: 20px;
            font-weight: 600;
            margin: 0 0 16px 0;
            color: #333333;
          }

          .setting-group {
            border: 1px solid #e9ecef;
            border-radius: 4px;
            margin-bottom: 8px;
            overflow: hidden;

            .setting-header {
              width: 100%;
              background: #f8f9fa;
              border: none;
              padding: 12px 16px;
              text-align: left;
              cursor: pointer;
              display: flex;
              justify-content: space-between;
              align-items: center;

              &:hover {
                background: #e9ecef;
              }

              &.expanded {
                background: #e9ecef;
              }

              h4 {
                margin: 0;
                font-size: 16px;
                font-weight: 500;
                color: #333333;
              }

              &::after {
                content: '▼';
                font-size: 12px;
                color: #6c757d;
                transition: transform 0.2s;
              }

              &:not(.expanded)::after {
                transform: rotate(-90deg);
              }
            }

            .setting-content {
              padding: 16px;
              background: #ffffff;

              .setting-select {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-size: 14px;
                background: white;

                &:focus {
                  outline: none;
                  border-color: #0066cc;
                  box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
                }
              }

              .fluid-setting,
              .temperature-setting,
              .hydraulic-setting,
              .series-setting,
              .solar-setting {
                margin-bottom: 16px;

                &:last-child {
                  margin-bottom: 0;
                }

                label {
                  display: block;
                  margin-bottom: 6px;
                  font-size: 14px;
                  font-weight: 500;
                  color: #333333;
                }
              }

              .temperature-input {
                display: flex;
                align-items: center;
                gap: 8px;

                .temperature-field {
                  flex: 1;
                  padding: 8px 12px;
                  border: 1px solid #dee2e6;
                  border-radius: 4px;
                  font-size: 14px;

                  &:focus {
                    outline: none;
                    border-color: #0066cc;
                    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
                  }
                }

                .temperature-unit {
                  font-size: 14px;
                  color: #6c757d;
                }
              }

              .curve-options {
                .curve-option {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 8px 0;
                  border-bottom: 1px solid #f1f3f4;

                  &:last-child {
                    border-bottom: none;
                  }

                  label {
                    margin: 0;
                    font-size: 14px;
                    color: #333333;
                  }

                  input[type="checkbox"] {
                    width: 18px;
                    height: 18px;
                    cursor: pointer;
                  }
                }
              }

              .variable-speed-setting {
                .radio-group {
                  display: flex;
                  gap: 16px;
                  margin-top: 8px;

                  .radio-option {
                    display: flex;
                    align-items: center;
                    gap: 6px;

                    input[type="radio"] {
                      width: 16px;
                      height: 16px;
                      cursor: pointer;
                    }

                    label {
                      margin: 0;
                      font-size: 14px;
                      cursor: pointer;
                    }
                  }
                }
              }
            }
          }

          .advanced-options-btn {
            width: 100%;
            background: none;
            border: 1px solid #dee2e6;
            padding: 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 16px;

            &:hover {
              background-color: #f8f9fa;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .grundfos-exact-copy {
    .product-info-section .product-header {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .performance-content {
      grid-template-columns: 1fr !important;
      gap: 24px !important;
    }
  }
}

@media (max-width: 768px) {
  .grundfos-exact-copy {
    .toolbar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .toolbar-buttons {
        justify-content: center;
        flex-wrap: wrap;
      }
    }

    .tabs {
      flex-wrap: wrap;
      
      .tab {
        flex: 1;
        min-width: 80px;
      }
    }
  }
}
