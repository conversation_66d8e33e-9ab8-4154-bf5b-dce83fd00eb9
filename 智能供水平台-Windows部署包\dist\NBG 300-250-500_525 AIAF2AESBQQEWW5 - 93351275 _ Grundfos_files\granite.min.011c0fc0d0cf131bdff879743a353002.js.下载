(function(g,e){e.Granite=e.Granite||{};e.Granite.$=e.Granite.$||g;e._g=e._g||{};e._g.$=e._g.$||g;var k=Granite.HTTP;g.ajaxSetup({externalize:!0,encodePath:!0,hook:!0,beforeSend:function(h,c){"undefined"!==typeof G_IS_HOOKED&&G_IS_HOOKED(c.url)||(c.externalize&&(c.url=k.externalize(c.url)),c.encodePath&&(c.url=k.encodePathOfURI(c.url)));c.hook&&(h=k.getXhrHook(c.url,c.type,c.data))&&(c.url=h.url,h.params&&("GET"===c.type.toUpperCase()?c.url+="?"+g.param(h.params):c.data=g.param(h.params)))},statusCode:{403:function(h){"Authentication Failed"===
h.getResponseHeader("X-Reason")&&k.handleLoginRedirect()}}});g.ajaxSettings.traditional=!0})(jQuery,this);
(function(g){window.Granite.csrf||(window.Granite.csrf=g(window.Granite.HTTP))})(function(g){function e(){this._handler=[]}function k(a){var b="//"+document.location.host,d=document.location.protocol+b;return a===d||a.slice(0,d.length+1)===d+"/"||a===b||a.slice(0,b.length+1)===b+"/"||!/^(\/\/|http:|https:).*/.test(a)}function h(a){window.console&&console.warn("CSRF data not available;The data may be unavailable by design, such as during non-authenticated requests: "+a)}function c(){var a=new e;m=
a;var b=new XMLHttpRequest;b.onreadystatechange=function(){if(4===b.readyState)try{f=JSON.parse(b.responseText).token,a.resolve(f)}catch(d){h(d),a.reject(b.responseText)}};b.open("GET",n,!0);b.send();return a}function p(){var a=new XMLHttpRequest;a.open("GET",n,!1);a.send();try{return f=JSON.parse(a.responseText).token}catch(b){h(b)}}function q(a){var b=a.getAttribute("action");"GET"===a.method.toUpperCase()||b&&!k(b)||(f||p(),f&&(b=a.querySelector('input[name\x3d":cq_csrf_token"]'),b||(b=document.createElement("input"),
b.setAttribute("type","hidden"),b.setAttribute("name",":cq_csrf_token"),a.appendChild(b)),b.setAttribute("value",f)))}function r(a){var b=function(d){d=d.target;"FORM"===d.nodeName&&q(d)};a.addEventListener?a.addEventListener("submit",b,!0):a.attachEvent&&a.attachEvent("submit",b)}e.prototype={then:function(a,b){this._handler.push({resolve:a,reject:b})},resolve:function(){this._execute("resolve",arguments)},reject:function(){this._execute("reject",arguments)},_execute:function(a,b){if(null===this._handler)throw Error("Promise already completed.");
for(var d=0,t=this._handler.length;d<t;d++)this._handler[d][a].apply(window,b);this.then=function(u,v){("resolve"===a?u:v).apply(window,b)};this._handler=null}};var n=g.externalize("/libs/granite/csrf/token.json"),m,f;r(document);var w=XMLHttpRequest.prototype.open;XMLHttpRequest.prototype.open=function(a,b,d){"get"!==a.toLowerCase()&&k(b)&&(this._csrf=!0,this._async=d);return w.apply(this,arguments)};var l=XMLHttpRequest.prototype.send;XMLHttpRequest.prototype.send=function(){if(this._csrf)if(f)this.setRequestHeader("CSRF-Token",
f),l.apply(this,arguments);else if(!1===this._async)p(),f&&this.setRequestHeader("CSRF-Token",f),l.apply(this,arguments);else{var a=this,b=Array.prototype.slice.call(arguments);m.then(function(d){a.setRequestHeader("CSRF-Token",d);l.apply(a,b)},function(){l.apply(a,b)})}else l.apply(this,arguments)};var x=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){q(this);return x.apply(this,arguments)};if(window.Node){var y=Node.prototype.appendChild;Node.prototype.appendChild=function(){var a=
y.apply(this,arguments);if("IFRAME"===a.nodeName)try{a.contentWindow&&!a._csrf&&(a._csrf=!0,r(a.contentWindow.document))}catch(b){a.src&&a.src.length&&k(a.src)&&window.console&&console.error("Unable to attach CSRF token to an iframe element on the same origin")}return a}}c();setInterval(function(){c()},3E5);return{initialised:!1,refreshToken:c,_clearToken:function(){f=void 0;c()}}});