<template>
  <div class="responsive-form" :class="{ 'responsive-form--mobile': isMobile }">
    <el-form
      ref="formRef"
      :model="modelValue"
      :rules="rules"
      :label-position="labelPosition"
      :label-width="labelWidth"
      :size="formSize"
      v-bind="$attrs"
      class="responsive-form__form"
    >
      <template v-for="field in formFields" :key="field.prop">
        <!-- 分组标题 -->
        <div v-if="field.type === 'group'" class="responsive-form__group">
          <h3 class="responsive-form__group-title">{{ field.label }}</h3>
          <div class="responsive-form__group-content">
            <responsive-form
              v-model="modelValue"
              :form-fields="field.children"
              :rules="rules"
              :is-mobile="isMobile"
            />
          </div>
        </div>
        
        <!-- 普通表单项 -->
        <el-form-item
          v-else
          :prop="field.prop"
          :label="field.label"
          :required="field.required"
          :class="getFormItemClass(field)"
        >
          <!-- 输入框 -->
          <el-input
            v-if="field.type === 'input'"
            v-model="modelValue[field.prop]"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :clearable="field.clearable !== false"
            v-bind="field.props"
          />
          
          <!-- 数字输入框 -->
          <el-input-number
            v-else-if="field.type === 'number'"
            v-model="modelValue[field.prop]"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :min="field.min"
            :max="field.max"
            :step="field.step"
            :controls-position="isMobile ? 'right' : undefined"
            v-bind="field.props"
            class="w-full"
          />
          
          <!-- 选择器 -->
          <el-select
            v-else-if="field.type === 'select'"
            v-model="modelValue[field.prop]"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :clearable="field.clearable !== false"
            :multiple="field.multiple"
            :filterable="field.filterable"
            v-bind="field.props"
            class="w-full"
          >
            <el-option
              v-for="option in field.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
              :disabled="option.disabled"
            />
          </el-select>
          
          <!-- 日期选择器 -->
          <el-date-picker
            v-else-if="field.type === 'date'"
            v-model="modelValue[field.prop]"
            :type="field.dateType || 'date'"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :clearable="field.clearable !== false"
            v-bind="field.props"
            class="w-full"
          />
          
          <!-- 开关 -->
          <el-switch
            v-else-if="field.type === 'switch'"
            v-model="modelValue[field.prop]"
            :disabled="field.disabled"
            :active-text="field.activeText"
            :inactive-text="field.inactiveText"
            v-bind="field.props"
          />
          
          <!-- 单选框组 -->
          <el-radio-group
            v-else-if="field.type === 'radio'"
            v-model="modelValue[field.prop]"
            :disabled="field.disabled"
            v-bind="field.props"
          >
            <el-radio
              v-for="option in field.options"
              :key="option.value"
              :label="option.value"
              :disabled="option.disabled"
            >
              {{ option.label }}
            </el-radio>
          </el-radio-group>
          
          <!-- 复选框组 -->
          <el-checkbox-group
            v-else-if="field.type === 'checkbox'"
            v-model="modelValue[field.prop]"
            :disabled="field.disabled"
            v-bind="field.props"
          >
            <el-checkbox
              v-for="option in field.options"
              :key="option.value"
              :label="option.value"
              :disabled="option.disabled"
            >
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>
          
          <!-- 文本域 -->
          <el-input
            v-else-if="field.type === 'textarea'"
            v-model="modelValue[field.prop]"
            type="textarea"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :rows="field.rows || (isMobile ? 3 : 4)"
            :autosize="field.autosize"
            v-bind="field.props"
          />
          
          <!-- 自定义插槽 -->
          <slot
            v-else-if="field.type === 'slot'"
            :name="field.slotName"
            :field="field"
            :value="modelValue[field.prop]"
            :is-mobile="isMobile"
          />
          
          <!-- 帮助文本 -->
          <div v-if="field.help" class="responsive-form__help">
            {{ field.help }}
          </div>
        </el-form-item>
      </template>
      
      <!-- 表单操作按钮 -->
      <div v-if="showActions" class="responsive-form__actions">
        <slot name="actions" :is-mobile="isMobile">
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ submitText }}
          </el-button>
        </slot>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'

interface FormField {
  prop: string
  label: string
  type: string
  placeholder?: string
  required?: boolean
  disabled?: boolean
  clearable?: boolean
  help?: string
  // 特定类型的属性
  options?: Array<{ label: string; value: any; disabled?: boolean }>
  min?: number
  max?: number
  step?: number
  multiple?: boolean
  filterable?: boolean
  dateType?: string
  activeText?: string
  inactiveText?: string
  rows?: number
  autosize?: boolean
  slotName?: string
  props?: Record<string, any>
  // 分组相关
  children?: FormField[]
  // 样式相关
  span?: number
  offset?: number
}

interface Props {
  modelValue: Record<string, any>
  formFields: FormField[]
  rules?: FormRules
  loading?: boolean
  showActions?: boolean
  submitText?: string
}

interface Emits {
  (e: 'update:modelValue', value: Record<string, any>): void
  (e: 'submit', value: Record<string, any>): void
  (e: 'reset'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showActions: true,
  submitText: '提交'
})

const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<FormInstance>()
const isMobile = ref(false)

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
}

// 监听窗口大小变化
const handleResize = () => {
  checkMobile()
}

// 计算属性
const labelPosition = computed(() => {
  return isMobile.value ? 'top' : 'right'
})

const labelWidth = computed(() => {
  return isMobile.value ? 'auto' : '120px'
})

const formSize = computed(() => {
  return isMobile.value ? 'default' : 'default'
})

// 获取表单项样式类
const getFormItemClass = (field: FormField) => {
  const classes = ['responsive-form__item']
  
  if (isMobile.value) {
    classes.push('responsive-form__item--mobile')
  }
  
  if (field.type === 'textarea') {
    classes.push('responsive-form__item--textarea')
  }
  
  return classes
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    emit('submit', props.modelValue)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  emit('reset')
}

// 暴露表单方法
const validate = () => formRef.value?.validate()
const validateField = (prop: string) => formRef.value?.validateField(prop)
const resetFields = () => formRef.value?.resetFields()
const clearValidate = () => formRef.value?.clearValidate()

defineExpose({
  validate,
  validateField,
  resetFields,
  clearValidate,
  formRef
})

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
@use '@/styles/mixins.scss' as *;
@use '@/styles/variables.scss' as *;

.responsive-form {
  &--mobile {
    .responsive-form__form {
      :deep(.el-form-item__label) {
        @include responsive-font-size(14px, 13px);
        font-weight: 500;
        margin-bottom: $spacing-mobile-xs;
      }
      
      :deep(.el-form-item__content) {
        margin-left: 0 !important;
      }
      
      :deep(.el-input__wrapper) {
        @include touch-friendly;
      }
      
      :deep(.el-select .el-input__wrapper) {
        @include touch-friendly;
      }
      
      :deep(.el-button) {
        @include touch-friendly;
      }
    }
  }
  
  &__form {
    width: 100%;
  }
  
  &__item {
    @include respond-to(mobile) {
      margin-bottom: $spacing-mobile-lg;
    }
    
    &--mobile {
      :deep(.el-form-item__label) {
        padding-bottom: $spacing-mobile-xs;
      }
    }
    
    &--textarea {
      :deep(.el-textarea__inner) {
        @include respond-to(mobile) {
          min-height: 80px;
          font-size: 16px; // 防止iOS缩放
        }
      }
    }
  }
  
  &__group {
    @include responsive-spacing(margin-bottom, $spacing-xl, $spacing-mobile-lg);
  }
  
  &__group-title {
    @include responsive-font-size(18px, 16px);
    font-weight: 600;
    color: var(--el-text-color-primary);
    @include responsive-spacing(margin-bottom, $spacing-md, $spacing-mobile-md);
    padding-bottom: $spacing-mobile-sm;
    border-bottom: 2px solid var(--el-color-primary);
  }
  
  &__group-content {
    @include responsive-spacing(padding-left, $spacing-lg, 0);
  }
  
  &__help {
    @include responsive-font-size(12px, 11px);
    color: var(--el-text-color-secondary);
    @include responsive-spacing(margin-top, $spacing-xs, $spacing-mobile-xs);
    line-height: 1.4;
  }
  
  &__actions {
    @include flex-end;
    gap: $spacing-md;
    @include responsive-spacing(margin-top, $spacing-xl, $spacing-mobile-lg);
    @include responsive-spacing(padding-top, $spacing-lg, $spacing-mobile-md);
    border-top: 1px solid var(--el-border-color-lighter);
    
    @include respond-to(mobile) {
      @include flex-column;
      gap: $spacing-mobile-md;
      
      :deep(.el-button) {
        width: 100%;
        order: 2;
        
        &.el-button--primary {
          order: 1;
        }
      }
    }
  }
}

// 全局样式覆盖
:deep(.w-full) {
  width: 100%;
}
</style>
