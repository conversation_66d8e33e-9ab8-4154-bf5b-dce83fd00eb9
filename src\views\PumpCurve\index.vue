<template>
  <div class="pump-curve">
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><TrendCharts /></el-icon>
          <span>水泵特性曲线分析</span>
        </div>
      </template>
      
      <div class="content">
        <el-alert
          title="功能说明"
          description="此页面展示水泵的基础特性曲线，包括Q-H、Q-η、Q-P曲线的原始数据和拟合结果。"
          type="info"
          :closable="false"
          show-icon
        />
        
        <div class="chart-wrapper">
          <div ref="chartRef" class="chart" />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { TrendCharts } from '@element-plus/icons-vue'
import { usePumpStore } from '@/stores/pump'
import { initChart, updateChart } from '@/views/IntelligentOptimization/chart-config'

const chartRef = ref<HTMLElement>()
const pumpStore = usePumpStore()

let chartInstance: any = null

onMounted(async () => {
  pumpStore.initCurveParams()
  
  await nextTick()
  
  if (chartRef.value) {
    chartInstance = initChart(chartRef.value)
    const curveData = pumpStore.getCurveData
    if (curveData) {
      updateChart(chartInstance, curveData, 'all', { Q: 0, H: 0 }, null)
    }
  }
})
</script>

<style lang="scss" scoped>
.pump-curve {
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
  }
  
  .content {
    .chart-wrapper {
      margin-top: 20px;
      
      .chart {
        height: 600px;
        width: 100%;
      }
    }
  }
}
</style>
