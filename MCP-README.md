# MCP (Model Context Protocol) 工具安装指南

## 🎉 安装成功！

我们已经成功在你的 Vue + Vite 项目中安装了以下 MCP 工具：

### 📦 已安装的 MCP 包

1. **@modelcontextprotocol/server-filesystem** (v2025.3.28)
   - 功能：文件系统操作
   - 用途：读写文件、创建目录、搜索文件等

2. **@modelcontextprotocol/server-sequential-thinking** (v0.6.2)
   - 功能：逻辑推理和思维链
   - 用途：帮助AI进行复杂问题分析和推理

3. **@modelcontextprotocol/server-brave-search** (v0.6.2)
   - 功能：搜索引擎集成
   - 用途：网络搜索和本地搜索功能

4. **@modelcontextprotocol/sdk** (v1.12.1)
   - 功能：MCP 开发工具包
   - 用途：开发自定义 MCP 服务器

5. **@modelcontextprotocol/inspector** (v0.14.0)
   - 功能：MCP 调试工具
   - 用途：测试和调试 MCP 服务器

## 🚀 快速开始

### 1. 启动 MCP Inspector
```bash
npx @modelcontextprotocol/inspector
```
然后访问：http://127.0.0.1:6274

### 2. 测试 MCP 工具
```bash
node test-mcp.js
```

## ⚙️ 配置 AI 工具

### Claude Desktop 配置
将 `mcp-config.json` 中的配置复制到：
- Windows: `%APPDATA%\Claude\claude_desktop_config.json`
- macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`

### Cursor/Windsurf 配置
在相应的设置中添加 MCP 服务器配置。

## 🔧 使用示例

### 文件系统操作
```javascript
// 通过 MCP 可以让 AI 直接操作文件
// 例如：读取项目文件、创建新文件、搜索代码等
```

### 逻辑推理
```javascript
// AI 可以使用思维链进行复杂推理
// 例如：分析代码逻辑、解决复杂问题等
```

### 网络搜索
```javascript
// AI 可以进行实时网络搜索
// 需要配置 Brave Search API 密钥
```

## 📝 注意事项

1. **API 密钥**：Brave Search 需要 API 密钥，可在 [Brave Search API](https://brave.com/search/api/) 获取
2. **重启应用**：配置 MCP 后需要重启 AI 工具
3. **权限设置**：文件系统服务器只能访问指定目录
4. **网络连接**：某些 MCP 服务器需要网络连接

## 🎯 下一步

1. 在你喜欢的 AI 工具中配置 MCP 服务器
2. 重启 AI 工具
3. 开始享受增强的 AI 功能！

## 🔗 相关链接

- [MCP 官方文档](https://modelcontextprotocol.io/)
- [MCP GitHub 仓库](https://github.com/modelcontextprotocol/servers)
- [Claude Desktop 配置指南](https://docs.anthropic.com/claude/docs/mcp)

---

**安装时间**: 2025-06-16  
**项目路径**: E:\h5-app  
**包管理器**: pnpm
