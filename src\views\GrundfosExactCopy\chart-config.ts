import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'
import { polynomialFit, naturalCubicSpline, generateCurveFromPoints, affinityLaw, calculateMultiPumpPerformance } from './pump-algorithms'
import { createYAxisConfig, createAlignedPowerNpshYAxis } from '@/utils/chart-axis-config'
import type { HydraulicLayoutConfig, SystemPerformanceResult } from '@/utils/pump-system-calculator'

// 接口定义
export interface ChartDataPoint {
  flow: number
  head: number
  efficiency1: number
  efficiency2: number
  powerP1: number
  powerP2: number
}

export interface NPSHDataPoint {
  flow: number
  npsh: number
}

export interface PumpParameters {
  ratedFlow: number
  ratedHead: number
  ratedEfficiency: number
  ratedPower: number
  speed: number
  impellerDiameter: number
  nominalSpeed?: number
  maxFlow?: number
  maxHead?: number
}

interface ChartConfigOptions {
  dataPoints: ChartDataPoint[]
  npshPoints: NPSHDataPoint[]
  singlePumpData?: ChartDataPoint[]
  settings: any
  pumpParameters: PumpParameters
  customOperatingPoint: any
  algorithm?: string
  algorithmParams?: any
  hydraulicConfig?: HydraulicLayoutConfig
  systemPerformance?: SystemPerformanceResult | null
}

// 生成完整的曲线点
function generateSmoothCurve(points: number[][], method: string, params: any): number[][] {
  // 确保点按流量排序
  const sortedPoints = [...points].sort((a, b) => a[0] - b[0]);
  
  if (method === 'polynomial') {
    const degree = params?.degree || 4;
    const regularization = params?.regularization || 0.005;
    return generateCurveFromPoints(sortedPoints, 200, 'polynomial', { 
      degree, 
      regularization 
    });
  } else if (method === 'spline') {
    return generateCurveFromPoints(sortedPoints, 200, 'spline', params);
  } else {
    // 默认使用样条插值
    return generateCurveFromPoints(sortedPoints, 200, 'spline');
  }
}

// 根据效率计算曲线粗细，模拟格兰富的视觉效果
function calculateLineWidth(efficiency: number): number {
  // 高效率区间（>75%）：粗线强调推荐工作区域
  if (efficiency > 75) return 3;
  // 中等效率区间（60-75%）：中等粗细
  if (efficiency > 60) return 2.5;
  // 低效率区间（45-60%）：正常粗细
  if (efficiency > 45) return 2;
  // 极低效率区间（<45%）：细线表示非推荐区域
  return 1.5;
}

// 为曲线数据添加动态线宽
function addDynamicLineWidth(points: [number, number][], efficiencyPoints: [number, number][]): any[] {
  return points.map((point, index) => {
    const efficiency = efficiencyPoints[index] ? efficiencyPoints[index][1] : 70;
    return {
      value: point,
      lineStyle: {
        width: calculateLineWidth(efficiency)
      }
    };
  });
}

// 创建格兰富图表选项 - 性能曲线
export function createPerformanceChartOption(options: ChartConfigOptions): EChartsOption {
  const {
    dataPoints,
    singlePumpData,
    settings,
    pumpParameters,
    customOperatingPoint,
    algorithm,
    algorithmParams,
    hydraulicConfig,
    systemPerformance
  } = options

  // 优先使用系统性能数据，如果没有则使用原始数据点
  const effectiveDataPoints = systemPerformance ? systemPerformance.systemCurve : dataPoints;
  const effectiveSinglePumpData = singlePumpData || dataPoints;

  // 确保数据点按流量排序
  const sortedDataPoints = [...effectiveDataPoints].sort((a, b) => a.flow - b.flow)
  const sortedSinglePumpData = [...effectiveSinglePumpData].sort((a, b) => a.flow - b.flow)

  // 提取系统曲线数据（多泵并联后的数据）
  const rawFlowHeadPoints = sortedDataPoints.map(point => [point.flow, point.head]);
  const rawEfficiency1Points = sortedDataPoints.map(point => [point.flow, point.efficiency1 || 0]);
  const rawEfficiency2Points = sortedDataPoints.map(point => [point.flow, point.efficiency2 || 0]);

  // 提取单泵数据用于对比显示
  const singlePumpFlowHeadPoints = sortedSinglePumpData.map(point => [point.flow, point.head]);
  const singlePumpEfficiency1Points = sortedSinglePumpData.map(point => [point.flow, point.efficiency1 || 0]);
  const singlePumpEfficiency2Points = sortedSinglePumpData.map(point => [point.flow, point.efficiency2 || 0]);

  // 获取最大流量、最大扬程和最大效率
  const maxFlow = Math.max(...sortedDataPoints.map(point => point.flow));
  const maxHead = Math.max(...sortedDataPoints.map(point => point.head));
  const maxEfficiency = 200; // 效率最大值固定为200%

  // 获取最佳效率点
  const bestEfficiencyPoint = sortedDataPoints.reduce((best, current) => {
    const currentEff = current.efficiency1 || 0;
    const bestEff = best.efficiency1 || 0;
    return currentEff > bestEff ? current : best;
  }, sortedDataPoints[0]);

  // 设置默认的图表配置
  const showGrid = settings?.showGrid !== false;
  const showLegend = settings?.showLegend !== false;
  const showDataPoints = settings?.showDataPoints !== false;
  const showTooltip = settings?.showTooltip !== false;
  const showOperatingPoint = settings?.showOperatingPoint !== false;
  const showBEP = settings?.showBEP !== false;
  const lineWidth = settings?.lineWidth || 2;
  const opacity = settings?.opacity || 1;

  // 获取水力布局配置
  const { installationType = 'single', pumpCount = 1, runningPumps = 1 } = hydraulicConfig || {};

  // 动态计算X轴范围，考虑多泵并联系统
  let flowAxisMax = 900;  // 默认X轴最大值为900

  // 如果是并联系统，需要扩大X轴范围
  if (installationType === 'parallel' && pumpCount > 1) {
    // 根据泵数量动态计算X轴最大值
    // 根据格兰富官网的实际图表拟合得到的计算公式
    const baseFlow = 800; // 单泵最大流量约800 m³/h
    
    // 基础系数 - 随泵数量增加而降低，模拟管路损失
    const efficiencyFactor = pumpCount <= 2 ? 0.98 : 
                            pumpCount <= 4 ? 0.95 : 
                            pumpCount <= 6 ? 0.92 : 
                            pumpCount <= 8 ? 0.88 : 0.85;
    
    // 计算系统最大流量，考虑效率因素
    const systemMaxFlow = baseFlow * pumpCount * efficiencyFactor;
    
    // 确保X轴范围能容纳并联系统的最大流量，并留有余量
    // 对于不同泵数量，使用不同的余量系数
    const marginFactor = pumpCount <= 2 ? 1.2 : 
                         pumpCount <= 4 ? 1.3 : 
                         pumpCount === 5 ? 1.5 : // 特别为5泵并联增加余量
                         pumpCount <= 6 ? 1.4 : 1.5;
    
    // 计算X轴最大值，向上取整到最接近的100的倍数
    flowAxisMax = Math.max(900, Math.ceil(systemMaxFlow * marginFactor / 100) * 100);
    
    // 特殊处理大规模并联系统
    if (runningPumps >= 6) {
      flowAxisMax = Math.max(flowAxisMax, runningPumps * 300);
    }
    
    // 特殊处理5泵并联情况，确保X轴范围足够大
    if (runningPumps === 5) {
      flowAxisMax = Math.max(flowAxisMax, 4000); // 格兰富官网5泵并联图表X轴范围约为4000
    }
    
    // 对于8泵并联，特别扩大X轴范围
    if (runningPumps === 8) {
      flowAxisMax = Math.max(flowAxisMax, 2400);
    }
    
    console.log(`📊 多泵并联系统 - 泵数量: ${runningPumps}, 计算的X轴最大值: ${flowAxisMax}`);
  }

  // 智能计算X轴步长，确保显示整数且视觉美观
  const calculateFlowStep = (maxValue: number, pumpCount: number = 1): number => {
    // 预定义的步长选项（确保都是整数）
    const stepOptions = [25, 50, 100, 150, 200, 250, 300, 400, 500, 750, 1000, 1500, 2000];

    // 根据泵数量调整目标刻度数量
    const targetTickCount = pumpCount === 1 ? 10 : Math.max(8, 12 - pumpCount);

    // 计算理想步长
    const idealStep = maxValue / targetTickCount;

    // 找到最接近理想步长的预定义步长
    let bestStep = stepOptions[0];
    let minDiff = Math.abs(idealStep - bestStep);

    for (const step of stepOptions) {
      const diff = Math.abs(idealStep - step);
      if (diff < minDiff) {
        minDiff = diff;
        bestStep = step;
      }
    }

    // 确保步长不会导致刻度过多（最多15个刻度）
    while (maxValue / bestStep > 15 && bestStep < stepOptions[stepOptions.length - 1]) {
      const currentIndex = stepOptions.indexOf(bestStep);
      if (currentIndex < stepOptions.length - 1) {
        bestStep = stepOptions[currentIndex + 1];
      } else {
        break;
      }
    }

    return bestStep;
  };

  const flowStep = calculateFlowStep(flowAxisMax, pumpCount);

  // 调试信息：输出X轴范围和步长计算结果
  console.log(`📊 X轴配置 - 范围: 0-${flowAxisMax}, 步长: ${flowStep}, 系统类型: ${installationType}, 泵数量: ${pumpCount}`);

  // 使用新的Y轴配置工具创建扬程和效率Y轴
  const headYAxisConfig = {
    type: 'value' as const,
    name: '扬程 (m)',
    min: 0,
    max: maxHead * 1.2,
    interval: Math.ceil(maxHead / 5),
    splitNumber: 5,
    axisLine: {
      lineStyle: {
        color: '#f00'
      }
    },
    axisLabel: {
      formatter: (value: number) => `${value}`
    }
  };

  const efficiencyYAxisConfig = {
    type: 'value' as const,
    name: '效率 (%)',
    min: 0,
        max: 200,
        interval: 20,
    splitNumber: 5,
    position: 'right' as const,
    axisLine: {
      lineStyle: {
        color: '#000'
      }
    },
    axisLabel: {
      formatter: (value: number) => `${value}`
    }
  };

  // 处理多泵系统 - 使用系统性能计算结果
  const isMultiPumpSystem = installationType === 'parallel' && pumpCount > 1;
  const seriesData: any[] = [];

  // 定义曲线颜色和样式
  const colors = {
    singlePump: {
      head: '#f00',
      efficiency1: '#000',
      efficiency2: '#333'
    },
    multiPump: {
      head: '#5470c6',
      efficiency1: '#2f4554',
      efficiency2: '#749f83'
    }
  };

  // 添加单泵扬程曲线
  seriesData.push({
    name: '单泵扬程',
    type: 'line',
    yAxisIndex: 0,
    data: singlePumpFlowHeadPoints,
    symbol: showDataPoints ? 'circle' : 'none',
    symbolSize: 6,
    lineStyle: {
      width: lineWidth,
      color: colors.singlePump.head,
      opacity
    },
    itemStyle: {
      color: colors.singlePump.head
    }
  });

  // 添加单泵效率曲线
  seriesData.push({
    name: '单泵效率',
    type: 'line',
    yAxisIndex: 1,
    data: singlePumpEfficiency1Points,
    symbol: showDataPoints ? 'circle' : 'none',
    symbolSize: 4,
    lineStyle: {
      width: 1.5, // 更细的线条
      color: colors.singlePump.efficiency1,
      opacity
    },
    itemStyle: {
      color: colors.singlePump.efficiency1
    }
  });

  // 添加单泵效率2曲线（如果有）
  if (singlePumpEfficiency2Points.some(point => point[1] > 0)) {
    seriesData.push({
      name: '单泵效率2',
      type: 'line',
      yAxisIndex: 1,
      data: singlePumpEfficiency2Points,
      symbol: showDataPoints ? 'circle' : 'none',
      symbolSize: 4,
      lineStyle: {
        width: 1.5, // 更细的线条
        color: colors.singlePump.efficiency2,
        opacity,
        type: 'dashed' // 使用虚线区分
      },
      itemStyle: {
        color: colors.singlePump.efficiency2
      }
    });
  }

  // 添加多泵系统曲线（如果是多泵系统）
  if (isMultiPumpSystem) {
    // 添加系统扬程曲线
    seriesData.push({
      name: `${runningPumps}泵系统扬程`,
      type: 'line',
      yAxisIndex: 0,
      data: rawFlowHeadPoints,
      symbol: showDataPoints ? 'circle' : 'none',
      symbolSize: 6,
      lineStyle: {
        width: lineWidth + 1, // 系统曲线线宽稍大
        color: colors.multiPump.head,
        opacity
      },
      itemStyle: {
        color: colors.multiPump.head
      }
    });

    // 添加系统效率曲线
    seriesData.push({
      name: `${runningPumps}泵系统效率`,
      type: 'line',
      yAxisIndex: 1,
      data: rawEfficiency1Points,
      symbol: showDataPoints ? 'circle' : 'none',
      symbolSize: 6,
      lineStyle: {
        width: lineWidth + 1, // 系统曲线线宽稍大
        color: colors.multiPump.efficiency1,
        opacity
      },
      itemStyle: {
        color: colors.multiPump.efficiency1
      }
    });

    // 添加系统效率2曲线（如果有）
    if (rawEfficiency2Points.some(point => point[1] > 0)) {
      seriesData.push({
        name: `${runningPumps}泵系统效率2`,
        type: 'line',
        yAxisIndex: 1,
        data: rawEfficiency2Points,
        symbol: showDataPoints ? 'circle' : 'none',
        symbolSize: 6,
        lineStyle: {
          width: lineWidth + 1, // 系统曲线线宽稍大
          color: colors.multiPump.efficiency2,
          opacity
        },
        itemStyle: {
          color: colors.multiPump.efficiency2
        }
      });
    }
  }

  // 添加最佳效率点
  if (showBEP && bestEfficiencyPoint) {
    seriesData.push({
      name: '系统最佳效率点',
      type: 'scatter',
      yAxisIndex: 0,
      data: [[bestEfficiencyPoint.flow, bestEfficiencyPoint.head]],
      symbol: 'circle',
      symbolSize: 12,
      itemStyle: {
        color: '#1890ff'
      },
      label: {
        show: true,
        position: 'top',
        formatter: `BEP (${bestEfficiencyPoint.flow.toFixed(1)}, ${bestEfficiencyPoint.head.toFixed(2)}, ${bestEfficiencyPoint.efficiency1.toFixed(2)}%)`
      }
    });
  }

  // 创建图表选项
  const option: EChartsOption = {
    title: {
      text: 'NBG 300-250-500/525, 3*400 V, 50Hz',
      left: 'center',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333'
      },
      subtext: isMultiPumpSystem ? 
        `流量-扬程-效率曲线 (${runningPumps}泵并联)` : 
        '流量-扬程-效率曲线'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function(params: any) {
        let result = `流量: ${params[0].value[0].toFixed(1)} m³/h<br/>`;

        params.forEach((param: any) => {
          if (param.seriesName.includes('扬程')) {
            result += `${param.seriesName}: ${param.value[1].toFixed(2)} m<br/>`;
          } else if (param.seriesName.includes('效率')) {
            result += `${param.seriesName}: ${param.value[1].toFixed(2)} %<br/>`;
          } else if (param.seriesName === '系统最佳效率点') {
            // 系统最佳效率点显示效率值
            result += `${param.seriesName}: ${bestEfficiencyPoint.efficiency1.toFixed(2)} %<br/>`;
          }
        });

        return result;
      }
    },
    legend: {
      data: seriesData.map(series => series.name),
      top: 40,
      textStyle: {
        color: '#333'
      }
    },
    grid: {
      left: 60,
      right: 60,
      bottom: 80,
      top: 100,
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '流量 Q (m³/h)',
      nameLocation: 'middle',
      nameGap: 30,
      min: 0,
      max: flowAxisMax,
      interval: flowStep,
      axisLabel: {
        formatter: '{value}'
      }
    },
    yAxis: [headYAxisConfig, efficiencyYAxisConfig],
    series: seriesData
  };

  return option;
}

// 创建功率-NPSH曲线图表选项
export function createPowerNpshChartOption(options: ChartConfigOptions): EChartsOption {
  const {
    dataPoints,
    npshPoints,
    singlePumpData,
    algorithm,
    algorithmParams,
    settings,
    pumpParameters,
    hydraulicConfig,
    systemPerformance
  } = options;

  // 优先使用系统性能数据，如果没有则使用原始数据点
  const effectiveDataPoints = systemPerformance ? systemPerformance.systemCurve : dataPoints;
  const effectiveNPSHPoints = systemPerformance ? systemPerformance.npshCurve : npshPoints;
  const effectiveSinglePumpData = singlePumpData || dataPoints;

  // 确保数据点按流量排序
  const sortedDataPoints = [...effectiveDataPoints].sort((a, b) => a.flow - b.flow);
  const sortedNPSHPoints = [...effectiveNPSHPoints].sort((a, b) => a.flow - b.flow);
  const sortedSinglePumpData = [...effectiveSinglePumpData].sort((a, b) => a.flow - b.flow);

  // 提取系统曲线数据（多泵并联后的数据）
  const rawPowerP1Points = sortedDataPoints.map(point => [point.flow, point.powerP1]);
  const rawPowerP2Points = sortedDataPoints.map(point => [point.flow, point.powerP2]);
  const rawNPSHPoints = sortedNPSHPoints.map(point => [point.flow, point.npsh]);

  // 提取单泵数据用于对比显示
  const singlePumpPowerP1Points = sortedSinglePumpData.map(point => [point.flow, point.powerP1]);
  const singlePumpPowerP2Points = sortedSinglePumpData.map(point => [point.flow, point.powerP2]);

  // 使用算法生成平滑曲线
  const fitMethod = algorithm || 'spline'; // 默认使用样条插值
  const powerP1Points = generateSmoothCurve(rawPowerP1Points, fitMethod, algorithmParams);
  const powerP2Points = generateSmoothCurve(rawPowerP2Points, fitMethod, algorithmParams);
  const npshPoints2 = generateSmoothCurve(rawNPSHPoints, fitMethod, algorithmParams);

  // 生成单泵曲线用于对比
  const singlePowerP1Points = generateSmoothCurve(singlePumpPowerP1Points, fitMethod, algorithmParams);
  const singlePowerP2Points = generateSmoothCurve(singlePumpPowerP2Points, fitMethod, algorithmParams);

  // 优先使用hydraulicConfig，如果没有则使用settings
  const installationType = hydraulicConfig?.installationType || settings.installationType;
  const pumpCount = hydraulicConfig?.pumpCount || parseInt(settings.pumpCount || '1');
  const runningPumps = hydraulicConfig?.runningPumps || pumpCount;
  const seriesConnection = hydraulicConfig?.seriesConnection || settings.seriesConnection;

  // 获取最大值用于设置坐标轴范围
  const maxFlow = Math.max(...sortedDataPoints.map(point => point.flow));
  const maxPowerP1 = Math.max(...sortedDataPoints.map(point => point.powerP1));
  const maxPowerP2 = Math.max(...sortedDataPoints.map(point => point.powerP2));
  let maxPower = Math.max(maxPowerP1, maxPowerP2);
  const maxNPSH = Math.max(...sortedNPSHPoints.map(point => point.npsh));

  // 如果是并联系统，功率最大值需要乘以运行泵数量
  if (installationType === 'parallel' && pumpCount > 1) {
    maxPower = maxPower * runningPumps;
  } else if (seriesConnection !== 'none' && seriesConnection !== undefined) {
    maxPower = maxPower * 2; // 串联系统功率增加
  }

  // 动态计算X轴范围，考虑多泵并联系统
  let flowAxisMax = 900;  // 默认X轴最大值为900

  // 如果是并联系统，需要扩大X轴范围
  if (installationType === 'parallel' && pumpCount > 1) {
    // 根据泵数量动态计算X轴最大值
    // 根据格兰富官网的实际图表拟合得到的计算公式
    const baseFlow = 800; // 单泵最大流量约800 m³/h
    
    // 基础系数 - 随泵数量增加而降低，模拟管路损失
    const efficiencyFactor = pumpCount <= 2 ? 0.98 : 
                            pumpCount <= 4 ? 0.95 : 
                            pumpCount <= 6 ? 0.92 : 
                            pumpCount <= 8 ? 0.88 : 0.85;
    
    // 计算系统最大流量，考虑效率因素
    const systemMaxFlow = baseFlow * pumpCount * efficiencyFactor;
    
    // 确保X轴范围能容纳并联系统的最大流量，并留有余量
    // 对于不同泵数量，使用不同的余量系数
    const marginFactor = pumpCount <= 2 ? 1.2 : 
                         pumpCount <= 4 ? 1.3 : 
                         pumpCount === 5 ? 1.5 : // 特别为5泵并联增加余量
                         pumpCount <= 6 ? 1.4 : 1.5;
    
    // 计算X轴最大值，向上取整到最接近的100的倍数
    flowAxisMax = Math.max(900, Math.ceil(systemMaxFlow * marginFactor / 100) * 100);
    
    // 特殊处理大规模并联系统
    if (runningPumps >= 6) {
      flowAxisMax = Math.max(flowAxisMax, runningPumps * 300);
    }
    
    // 特殊处理5泵并联情况，确保X轴范围足够大
    if (runningPumps === 5) {
      flowAxisMax = Math.max(flowAxisMax, 4000); // 格兰富官网5泵并联图表X轴范围约为4000
    }
    
    // 对于8泵并联，特别扩大X轴范围
    if (runningPumps === 8) {
      flowAxisMax = Math.max(flowAxisMax, 2400);
    }
    
    console.log(`📊 多泵并联系统 - 泵数量: ${runningPumps}, 计算的X轴最大值: ${flowAxisMax}`);
  }

  // 智能计算X轴步长，确保显示整数且视觉美观（与性能图保持一致）
  const calculateFlowStep = (maxValue: number, pumpCount: number = 1): number => {
    // 预定义的步长选项（确保都是整数）
    const stepOptions = [25, 50, 100, 150, 200, 250, 300, 400, 500, 750, 1000, 1500, 2000];

    // 根据泵数量调整目标刻度数量
    const targetTickCount = pumpCount === 1 ? 10 : Math.max(8, 12 - pumpCount);

    // 计算理想步长
    const idealStep = maxValue / targetTickCount;

    // 找到最接近理想步长的预定义步长
    let bestStep = stepOptions[0];
    let minDiff = Math.abs(idealStep - bestStep);

    for (const step of stepOptions) {
      const diff = Math.abs(idealStep - step);
      if (diff < minDiff) {
        minDiff = diff;
        bestStep = step;
      }
    }

    // 确保步长不会导致刻度过多（最多15个刻度）
    while (maxValue / bestStep > 15 && bestStep < stepOptions[stepOptions.length - 1]) {
      const currentIndex = stepOptions.indexOf(bestStep);
      if (currentIndex < stepOptions.length - 1) {
        bestStep = stepOptions[currentIndex + 1];
      } else {
        break;
      }
    }

    return bestStep;
  };

  const flowStep = calculateFlowStep(flowAxisMax, pumpCount);

  // 调试信息：输出X轴范围和步长计算结果
  console.log(`📊 功率图X轴配置 - 范围: 0-${flowAxisMax}, 步长: ${flowStep}, 系统类型: ${installationType}, 泵数量: ${pumpCount}`);

  // 处理多泵系统 - 使用系统性能计算结果
  const isMultiPumpSystem = installationType === 'parallel' && pumpCount > 1;
  const seriesData: any[] = [];

  // 始终显示单泵曲线作为参考
  seriesData.push(
    {
      name: '单泵功率 P1',
      type: 'line',
      symbol: 'none',
      smooth: true,
      data: singlePowerP1Points,
      lineStyle: {
        width: 2,
        color: '#f00'
      },
      z: 1
    },
    {
      name: '单泵功率 P2',
      type: 'line',
      symbol: 'none',
      smooth: true,
      data: singlePowerP2Points,
      lineStyle: {
        width: 2,
        color: '#ffa500'
      },
      z: 1
    },
    {
      name: '单泵NPSH',
      type: 'line',
      symbol: 'none',
      smooth: true,
      yAxisIndex: 1,
      data: npshPoints2,
      lineStyle: {
        width: 2,
        color: '#00f'
      },
      z: 1
    }
  );

  // 如果是多泵系统，显示系统曲线（使用已计算的系统性能数据）
  if (isMultiPumpSystem && systemPerformance) {
    console.log('🔧 显示多泵系统功率曲线', {
      installationType,
      pumpCount,
      runningPumps,
      systemCurveLength: systemPerformance.systemCurve.length
    });

    // 系统功率P1曲线
    seriesData.push({
      name: `${pumpCount}泵系统功率 P1`,
      type: 'line',
      symbol: 'none',
      smooth: true,
      data: powerP1Points,
      lineStyle: {
        width: 3,
        type: 'solid',
        color: '#d62728'
      },
      z: 3
    });

    // 系统功率P2曲线
    seriesData.push({
      name: `${pumpCount}泵系统功率 P2`,
      type: 'line',
      symbol: 'none',
      smooth: true,
      data: powerP2Points,
      lineStyle: {
        width: 3,
        type: 'solid',
        color: '#ff7f0e'
      },
      z: 3
    });

    // 系统NPSH曲线（NPSH值不变，但流量范围扩大）
    seriesData.push({
      name: `${pumpCount}泵系统NPSH`,
      type: 'line',
      symbol: 'none',
      smooth: true,
      yAxisIndex: 1,
      data: npshPoints2,
      lineStyle: {
        width: 3,
        type: 'solid',
        color: '#1f77b4'
      },
      z: 3
    });
  }
  
  // 使用新的工具函数创建对齐的功率和NPSH Y轴配置
  const yAxisConfigs = createAlignedPowerNpshYAxis(maxPower, maxNPSH) as any;
  
  // 创建图表选项
  const option: EChartsOption = {
    title: {
      text: 'NBG 300-250-500/525, 3*400 V, 50Hz',
      left: 'center',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333'
      },
      subtext: '流量-功率-NPSH曲线'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function(params: any) {
        let result = `流量: ${params[0].value[0].toFixed(1)} m³/h<br/>`;
        
        params.forEach((param: any) => {
          if (param.seriesName === '功率 P1') {
            result += `输入功率 P1: ${param.value[1].toFixed(2)} kW<br/>`;
          } else if (param.seriesName === '功率 P2') {
            result += `轴功率 P2: ${param.value[1].toFixed(2)} kW<br/>`;
          } else if (param.seriesName === 'NPSH') {
            result += `NPSH: ${param.value[1].toFixed(2)} m<br/>`;
          }
        });
        
        return result;
      }
    },
    legend: {
      data: seriesData.map(series => series.name),
      top: 40,
      selectedMode: true,
      textStyle: {
        color: '#333'
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '10%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '流量 Q (m³/h)',
      nameLocation: 'middle',
      nameGap: 30,
      min: 0,
      max: flowAxisMax,
      interval: flowStep,
      axisLine: {
        lineStyle: {
          color: '#333'
        }
      },
      axisLabel: {
        formatter: '{value}'
      }
    },
    yAxis: yAxisConfigs,
    series: seriesData
  };
  
  // 添加525毫米标注
  option.graphic = [
    {
      type: 'text',
      left: '10%',
      top: '20%',
      style: {
        text: '525 毫米',
        fontSize: 14,
        fontWeight: 'bold'
      }
    }
  ];
  
  return option;
}

// 创建格兰富图表选项 - 主函数
export function createGrundfosChartOption(options: ChartConfigOptions): EChartsOption {
  // 默认显示性能曲线
  return createPerformanceChartOption(options);
}

// 初始化图表
export function initGrundfosChart(
  container: HTMLElement, 
  dataPoints: ChartDataPoint[], 
  npshPoints: NPSHDataPoint[], 
  settings: any, 
  pumpParameters: PumpParameters, 
  isPowerNpshChart: boolean = false
) {
  // 初始化图表
  const chart = echarts.init(container);
  
  // 准备选项
  const options: ChartConfigOptions = {
    dataPoints,
    npshPoints,
    settings,
    pumpParameters,
    customOperatingPoint: { flow: 300, head: 40 }
  };
  
  // 设置图表选项
  if (isPowerNpshChart) {
    chart.setOption(createPowerNpshChartOption(options));
  } else {
    chart.setOption(createPerformanceChartOption(options));
  }
  
  return chart;
}

// 更新图表
export function updateGrundfosChart(
  chart: echarts.ECharts, 
  options: ChartConfigOptions, 
  isPowerNpshChart: boolean = false
) {
  if (!chart) return;
  
  // 设置图表选项
  if (isPowerNpshChart) {
    chart.setOption(createPowerNpshChartOption(options), true);
  } else {
    chart.setOption(createPerformanceChartOption(options), true);
  }
}
