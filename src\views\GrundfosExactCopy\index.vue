<template>
  <div class="high-precision-curves">
    <div class="tab-navigation">
      <div 
        class="tab-item" 
        :class="{ active: activeTab === 'curves' }"
        @click="activeTab = 'curves'"
      >
        曲线
      </div>
      <div 
        class="tab-item" 
        :class="{ active: activeTab === 'parameters' }"
        @click="activeTab = 'parameters'"
      >
        参数
      </div>
      <div 
        class="tab-item" 
        :class="{ active: activeTab === 'algorithm' }"
        @click="activeTab = 'algorithm'"
      >
        算法
      </div>
    </div>
    
    <!-- 曲线标签页 - 使用两列布局 -->
    <div class="curves-layout" v-show="activeTab === 'curves'">
      <!-- 左侧图表区域 -->
      <div class="charts-column">

        
        <div class="charts-container">
          <div class="chart-wrapper">
            <div class="chart-title">流量-扬程-效率曲线</div>
            <div class="chart-container" ref="performanceChartContainer"></div>
          </div>
          
          <div class="chart-wrapper">
            <div class="chart-title">流量-功率-NPSH曲线</div>
            <div class="chart-container" ref="powerNpshChartContainer"></div>
          </div>
        </div>
      </div>
      
      <!-- 右侧设置区域 -->
      <div class="settings-column">


        <!-- 曲线设置区域 -->
        <div class="curve-settings-section">
          <div class="settings-header">
            <h3>曲线设置</h3>
          </div>
          
          <div class="settings-grid">
            <div class="setting-group" :class="{ collapsed: !expandedSections.operatingPoint }">
              <div class="setting-header" @click="toggleSection('operatingPoint')">
                <h4>运行点</h4>
                <span class="toggle-icon">{{ expandedSections.operatingPoint ? '▼' : '▶' }}</span>
              </div>
              <div class="setting-content" v-show="expandedSections.operatingPoint">
                <!-- 运行点设置内容 -->
                <div class="operating-point-container">
                  <!-- 运行点模式选择 -->
                  <div class="input-group">
                    <el-select v-model="operatingPoint.mode" placeholder="选择运行点模式" @change="updateOperatingPoint">
                      <el-option value="none" label="不显示工作点"></el-option>
                      <el-option value="nominal" label="名义上工作点"></el-option>
                      <el-option value="input" label="输入："></el-option>
                    </el-select>
                  </div>

                  <!-- 自定义输入区域 -->
                  <div v-if="operatingPoint.mode === 'input'" class="custom-input-area">
                    <div class="input-group">
                      <label>Q</label>
                      <div class="input-with-unit">
                        <el-input-number
                          v-model="operatingPoint.flow"
                          :min="0"
                          :max="1000"
                          :step="1"
                          :precision="1"
                          @change="updateOperatingPoint"
                        ></el-input-number>
                        <el-select v-model="operatingPoint.flowUnit" class="unit-select">
                          <el-option value="m³/h" label="m³/h"></el-option>
                          <el-option value="l/s" label="升/秒"></el-option>
                          <el-option value="l/min" label="升/分钟"></el-option>
                          <el-option value="l/h" label="l/h"></el-option>
                          <el-option value="m³/min" label="m³/min"></el-option>
                          <el-option value="gpm" label="US加仑/分钟"></el-option>
                          <el-option value="gpd" label="US 加仑/日"></el-option>
                        </el-select>
                      </div>
                    </div>

                    <div class="input-group">
                      <label>H</label>
                      <div class="input-with-unit">
                        <el-input-number
                          v-model="operatingPoint.head"
                          :min="0"
                          :max="200"
                          :step="0.1"
                          :precision="2"
                          @change="updateOperatingPoint"
                        ></el-input-number>
                        <el-select v-model="operatingPoint.headUnit" class="unit-select">
                          <el-option value="m" label="米"></el-option>
                          <el-option value="ft" label="英尺"></el-option>
                          <el-option value="kPa" label="千帕"></el-option>
                          <el-option value="MPa" label="MPa"></el-option>
                          <el-option value="bar" label="巴"></el-option>
                          <el-option value="psi" label="磅/英寸2"></el-option>
                        </el-select>
                      </div>
                    </div>

                    <div class="input-group">
                      <label>H 静态 *</label>
                      <div class="input-with-unit">
                        <el-input-number
                          v-model="operatingPoint.staticHead"
                          :min="0"
                          :max="100"
                          :step="0.1"
                          :precision="2"
                          @change="updateOperatingPoint"
                        ></el-input-number>
                        <span class="unit">米</span>
                      </div>
                    </div>

                    <div class="input-group">
                      <label>叶轮</label>
                      <div class="input-with-unit">
                        <el-input-number
                          v-model="operatingPoint.impellerDiameter"
                          :min="100"
                          :max="600"
                          :step="1"
                          :precision="0"
                          @change="updateOperatingPoint"
                        ></el-input-number>
                        <span class="unit">毫米</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 流动性设置 -->
            <div class="setting-group" :class="{ collapsed: !expandedSections.fluidity }">
              <div class="setting-header" @click="toggleSection('fluidity')">
                <h4>流动性</h4>
                <span class="toggle-icon">{{ expandedSections.fluidity ? '▼' : '▶' }}</span>
              </div>
              <div class="setting-content" v-show="expandedSections.fluidity">
                <!-- 流动性设置内容 -->
                <div class="select-group">
                  <label>输送的液体</label>
                  <el-select v-model="settings.fluidType" placeholder="选择液体类型">
                    <el-option value="water" label="水"></el-option>
                    <el-option value="emulsion" label="乳剂"></el-option>
                    <el-option value="viscous" label="任何粘性液体"></el-option>
                    <el-option value="antifreeze-l" label="抗冻型 L"></el-option>
                    <el-option value="antifreeze-n" label="抗冻型N"></el-option>
                    <el-option value="antifreeze-sol" label="抗冻型SOL"></el-option>
                    <el-option value="ethanol" label="乙醇"></el-option>
                    <el-option value="ethylene-glycol" label="乙二醇"></el-option>
                    <el-option value="methanol" label="甲醇"></el-option>
                    <el-option value="propylene-glycol" label="丙二醇"></el-option>
                    <el-option value="temper-10" label="Temper -10"></el-option>
                    <el-option value="temper-20" label="Temper -20"></el-option>
                    <el-option value="temper-30" label="Temper -30"></el-option>
                    <el-option value="temper-40" label="Temper -40"></el-option>
                    <el-option value="temper-55" label="Temper -55"></el-option>
                    <el-option value="tyfocor" label="Tyfocor"></el-option>
                    <el-option value="tyfocor-l" label="Tyfocor L"></el-option>
                  </el-select>
                </div>

                <div class="input-group">
                  <label>运行期间的液体温度 *</label>
                  <div class="input-with-unit">
                    <el-input-number v-model="settings.fluidTemperature" :min="0" :max="100" :step="1"></el-input-number>
                    <span class="unit">°C</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 泵参数设置 -->
            <div class="setting-group" :class="{ collapsed: !expandedSections.pumpParams }">
              <div class="setting-header" @click="toggleSection('pumpParams')">
                <h4>泵参数设置</h4>
                <span class="toggle-icon">{{ expandedSections.pumpParams ? '▼' : '▶' }}</span>
              </div>
              <div class="setting-content" v-show="expandedSections.pumpParams">
                <div class="input-group">
                  <label>转速 (rpm)</label>
                  <el-input-number v-model="settings.pumpSpeed" :min="0" :max="3600" :step="10"></el-input-number>
                </div>
                <div class="input-group">
                  <label>叶轮直径 (mm)</label>
                  <el-input-number v-model="settings.impellerDiameter" :min="100" :max="500" :step="1"></el-input-number>
                </div>
                <div class="input-group">
                  <label>叶轮修正系数</label>
                  <el-input-number v-model="settings.impellerCorrection" :min="0.5" :max="1.5" :step="0.01" :precision="2"></el-input-number>
                </div>
                <div class="input-group">
                  <label>效率修正系数</label>
                  <el-input-number v-model="settings.efficiencyCorrection" :min="0.5" :max="1.5" :step="0.01" :precision="2"></el-input-number>
                </div>
              </div>
            </div>

            <!-- 系统参数设置 -->
            <div class="setting-group" :class="{ collapsed: !expandedSections.systemParams }">
              <div class="setting-header" @click="toggleSection('systemParams')">
                <h4>系统参数</h4>
                <span class="toggle-icon">{{ expandedSections.systemParams ? '▼' : '▶' }}</span>
              </div>
              <div class="setting-content" v-show="expandedSections.systemParams">
                <div class="input-group">
                  <label>静扬程 (m)</label>
                  <el-input-number v-model="settings.staticHead" :min="0" :max="100" :step="0.1"></el-input-number>
                </div>
                <div class="input-group">
                  <label>管路阻力系数</label>
                  <el-input-number v-model="settings.resistanceCoeff" :min="0" :max="10" :step="0.001" :precision="3"></el-input-number>
                </div>
                <div class="input-group">
                  <label>吸入压力 (bar)</label>
                  <el-input-number v-model="settings.suctionPressure" :min="0" :max="10" :step="0.1"></el-input-number>
                </div>
                <div class="input-group">
                  <label>排出压力 (bar)</label>
                  <el-input-number v-model="settings.dischargePressure" :min="0" :max="50" :step="0.1"></el-input-number>
                </div>
              </div>
            </div>

            <!-- 显示选项 -->
            <div class="setting-group" :class="{ collapsed: !expandedSections.displayOptions }">
              <div class="setting-header" @click="toggleSection('displayOptions')">
                <h4>显示选项</h4>
                <span class="toggle-icon">{{ expandedSections.displayOptions ? '▼' : '▶' }}</span>
              </div>
              <div class="setting-content" v-show="expandedSections.displayOptions">
                <div class="checkbox-group">
                  <el-checkbox v-model="settings.displayOptions.showGrid">显示网格</el-checkbox>
                  <el-checkbox v-model="settings.displayOptions.showLegend">显示图例</el-checkbox>
                  <el-checkbox v-model="settings.displayOptions.showDataPoints">显示数据点</el-checkbox>
                  <el-checkbox v-model="settings.displayOptions.showTooltip">显示提示框</el-checkbox>
                  <el-checkbox v-model="settings.displayOptions.showOperatingPoint">显示工况点</el-checkbox>
                  <el-checkbox v-model="settings.displayOptions.showBEP">显示最佳效率点</el-checkbox>
                </div>
                <div class="input-group">
                  <label>曲线线宽</label>
                  <el-slider v-model="settings.displayOptions.lineWidth" :min="1" :max="5" :step="1"></el-slider>
                </div>
                <div class="input-group">
                  <label>透明度</label>
                  <el-slider v-model="settings.displayOptions.opacity" :min="0.1" :max="1" :step="0.1"></el-slider>
                </div>
              </div>
            </div>
            
            <!-- 曲线类型设置 -->
            <div class="setting-group" :class="{ collapsed: !expandedSections.curveTypes }">
              <div class="setting-header" @click="toggleSection('curveTypes')">
                <h4>曲线类型</h4>
                <span class="toggle-icon">{{ expandedSections.curveTypes ? '▼' : '▶' }}</span>
              </div>
              <div class="setting-content" v-show="expandedSections.curveTypes">
                <!-- 曲线类型设置内容 -->
                <div class="checkbox-group">
                  <el-checkbox v-model="settings.curveTypes.powerP1">功率曲线 P1</el-checkbox>
                  <el-checkbox v-model="settings.curveTypes.powerP2">功率曲线 P2</el-checkbox>
                  <el-checkbox v-model="settings.curveTypes.npsh">NPSH</el-checkbox>
                  <el-checkbox v-model="settings.curveTypes.eta">eta</el-checkbox>
                  <el-checkbox v-model="settings.curveTypes.isoEta">Iso eta curve</el-checkbox>
                  <el-checkbox v-model="settings.curveTypes.tolerance">公差（Tolerance）</el-checkbox>
                  <el-checkbox v-model="settings.curveTypes.minMax">Show min. & max curve</el-checkbox>
                </div>
              </div>
            </div>
            
            <!-- 水力布局组件 -->
            <HydraulicLayout
              v-model="hydraulicConfig"
              :single-pump-data="dataPoints"
              :npsh-data="npshPoints"
              @configuration-changed="onHydraulicConfigChanged"
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 参数标签页 -->
    <ParametersTab
      ref="parametersTabRef"
      v-show="activeTab === 'parameters'"
      :data-points="dataPoints"
      :npsh-points="npshPoints"
      @update-curves="updateCurves"
      @add-data-point="addDataPoint"
      @remove-data-point="removeDataPoint"
      @clear-data-points="clearDataPoints"
      @add-npsh-point="addNPSHPoint"
      @remove-npsh-point="removeNPSHPoint"
      @clear-npsh-points="clearNPSHPoints"
      @import-grundfos-data="importGrundfosData"
      @batch-add-data-points="batchAddDataPoints"
      @interpolate-data-points="interpolateDataPoints"
      @download-template="downloadTemplate"
      @import-data="importData"
      @export-data="exportData"
      @generate-curves="generateCurves"
    />
    
    <!-- 算法标签页 -->
    <div class="algorithm-section" v-show="activeTab === 'algorithm'">
      <div class="algorithm-header">
        <h2>高精度曲线拟合算法</h2>
        <p>选择合适的算法来拟合水泵性能曲线，获得更精确的模拟效果</p>
      </div>

      <AlgorithmSelector
        v-model:selected-algorithm="selectedAlgorithm"
      />

      <AlgorithmParams
        :selected-algorithm="selectedAlgorithm"
        v-model:algorithm-params="algorithmConfig"
      />

      <div class="algorithm-status">
        <el-alert
          v-if="selectedAlgorithm"
          :title="`当前算法: ${getAlgorithmDisplayName(selectedAlgorithm)}`"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>当前设置: {{ settingsStatus }}</p>
            <p>最后保存时间: {{ lastSaveTime || '未保存' }}</p>
          </template>
        </el-alert>

        <el-alert
          v-if="loadedFromStorage"
          title="已从本地存储加载设置"
          type="success"
          :closable="true"
          show-icon
          style="margin-top: 10px;"
        >
          <template #default>
            <p>算法: {{ getAlgorithmDisplayName(selectedAlgorithm) }}</p>
            <p>加载时间: {{ loadTime }}</p>
          </template>
        </el-alert>
      </div>

      <div class="algorithm-actions">
        <el-button
          type="primary"
          @click="saveAlgorithmSettings"
          :loading="isSaving"
          icon="DocumentAdd"
        >
          保存设置到本地
        </el-button>

        <el-button
          type="info"
          @click="loadAlgorithmSettings"
          :loading="isLoading"
          icon="FolderOpened"
        >
          加载本地设置
        </el-button>

        <el-button
          type="success"
          @click="switchToCurves"
          icon="View"
        >
          查看曲线效果
        </el-button>

        <el-button
          type="warning"
          :disabled="!selectedAlgorithm"
          @click="resetAlgorithmParams"
        >
          重置参数
        </el-button>

        <el-button
          type="danger"
          @click="clearLocalStorage"
        >
          清除本地数据
        </el-button>
      </div>

      <AlgorithmMetrics
        v-if="selectedAlgorithm && hasAppliedAlgorithm"
        :selected-algorithm="selectedAlgorithm"
        :data-points="dataPoints"
        :algorithm-params="algorithmConfig"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, nextTick, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
// @ts-ignore
import Papa from 'papaparse'

// 导入组件
import ParametersTab from './components/ParametersTab.vue'
import AlgorithmSelector from './components/AlgorithmSelector.vue'
import AlgorithmParams from './components/AlgorithmParams.vue'
import AlgorithmMetrics from './components/AlgorithmMetrics.vue'
import HydraulicLayout from './components/HydraulicLayout.vue'
import { createPerformanceChartOption, createPowerNpshChartOption } from './chart-config'
import {
  GrundfosSystemCalculator,
  type HydraulicLayoutConfig,
  type SystemPerformanceResult
} from '@/utils/pump-system-calculator'

// 响应式数据
const activeTab = ref('curves')

const performanceChartContainer = ref<HTMLElement | null>(null)
const powerNpshChartContainer = ref<HTMLElement | null>(null)
const parametersTabRef = ref<any>(null)
let performanceChart: ECharts | null = null
let powerNpshChart: ECharts | null = null

// 运行点
const operatingPoint = reactive({
  mode: 'input', // 运行点模式：none, nominal, input
  flow: 100,
  head: 0,
  staticHead: 0,
  impellerDiameter: 485,
  flowUnit: 'm³/h',
  headUnit: 'm',
  efficiency: 0,
  power: 0,
  showOnChart: true,
  showEfficiencyLine: false,
  showPowerLine: false
})

// 工作点（保留兼容性）
const customOperatingPoint = reactive({
  flow: 300,
  head: 42
})



// 算法设置已移至专门的算法标签页

// 水力布局配置
const hydraulicConfig = ref<HydraulicLayoutConfig>({
  installationType: 'single',
  pumpCount: 1,
  standbyPumps: 0,
  runningPumps: 1,
  seriesConnection: 'none',
  solarRelevant: false,
  variableSpeed: 'none'
})

// 系统性能结果
const systemPerformance = ref<SystemPerformanceResult | null>(null)

// 曲线设置
const settings = reactive({
  operatingPoint: 'bep',
  fluidType: 'water',
  fluidTemperature: 20,
  fluidDensity: 1000,
  fluidViscosity: 1.0,
  pumpSpeed: 2900,
  impellerDiameter: 500,
  impellerCorrection: 1.0,
  efficiencyCorrection: 1.0,
  staticHead: 0,
  resistanceCoeff: 0.001,
  suctionPressure: 1.0,
  dischargePressure: 5.0,
  curveTypes: {
    powerP1: true,
    powerP2: true,
    npsh: true,
    eta: true,
    isoEta: false,
    tolerance: false,
    minMax: false
  },
  installationType: 'single',
  seriesConnection: 'no',
  variableSpeed: 'no',
  solarCurves: false,
  displayOptions: {
    showGrid: true,
    showLegend: true,
    showDataPoints: true,
    showTooltip: true,
    showOperatingPoint: true,
    showBEP: true,
    lineWidth: 2,
    opacity: 0.8
  }
})

// 折叠设置区域控制
const expandedSections = reactive({
  operatingPoint: true,
  fluidity: true,
  pumpParams: true,
  systemParams: true,
  displayOptions: true,
  curveTypes: true,
  hydraulicLayout: true
})

const toggleSection = (section: string) => {
  if (expandedSections.hasOwnProperty(section)) {
    // @ts-ignore
    expandedSections[section] = !expandedSections[section]
  }
}

// 运行点计算方法
const updateOperatingPoint = () => {
  if (operatingPoint.mode === 'input') {
    // 根据流量计算其他参数
    const flow = convertFlowToStandard(operatingPoint.flow, operatingPoint.flowUnit)
    const point = interpolateFromCurve(flow)
    if (point) {
      operatingPoint.head = convertHeadFromStandard(point.head, operatingPoint.headUnit)
      operatingPoint.efficiency = point.efficiency
      operatingPoint.power = point.power
    }
  } else if (operatingPoint.mode === 'nominal') {
    // 使用标准工作点
    operatingPoint.flow = 300
    operatingPoint.head = 42
    operatingPoint.efficiency = 87
    operatingPoint.power = 56
  }

  updateCharts()
}

// 单位转换函数
const convertFlowToStandard = (value: number, unit: string): number => {
  const conversions = {
    'm³/h': 1,
    'l/s': 3.6,
    'l/min': 0.06,
    'l/h': 0.001,
    'm³/min': 60,
    'gpm': 0.227124,
    'gpd': 0.00000946
  }
  return value * (conversions[unit as keyof typeof conversions] || 1)
}

const convertHeadFromStandard = (value: number, unit: string): number => {
  const conversions = {
    'm': 1,
    'ft': 3.28084,
    'kPa': 9.80665,
    'MPa': 0.00980665,
    'bar': 0.0980665,
    'psi': 1.42233
  }
  return value * (conversions[unit as keyof typeof conversions] || 1)
}

// 从曲线数据插值计算
const interpolateFromCurve = (flow: number) => {
  if (dataPoints.value.length < 2) return null

  // 找到相邻的两个数据点
  let lowerPoint = dataPoints.value[0]
  let upperPoint = dataPoints.value[dataPoints.value.length - 1]

  for (let i = 0; i < dataPoints.value.length - 1; i++) {
    if (dataPoints.value[i].flow <= flow && dataPoints.value[i + 1].flow >= flow) {
      lowerPoint = dataPoints.value[i]
      upperPoint = dataPoints.value[i + 1]
      break
    }
  }

  // 线性插值
  const ratio = (flow - lowerPoint.flow) / (upperPoint.flow - lowerPoint.flow)
  return {
    head: lowerPoint.head + ratio * (upperPoint.head - lowerPoint.head),
    efficiency: lowerPoint.efficiency1 + ratio * (upperPoint.efficiency1 - lowerPoint.efficiency1),
    power: lowerPoint.powerP1 + ratio * (upperPoint.powerP1 - lowerPoint.powerP1)
  }
}

const interpolateFromHead = (head: number) => {
  // 根据扬程反向插值计算流量
  if (dataPoints.value.length < 2) return null

  // 找到相邻的两个数据点（按扬程排序）
  const sortedPoints = [...dataPoints.value].sort((a, b) => b.head - a.head)

  let lowerPoint = sortedPoints[0]
  let upperPoint = sortedPoints[sortedPoints.length - 1]

  for (let i = 0; i < sortedPoints.length - 1; i++) {
    if (sortedPoints[i].head >= head && sortedPoints[i + 1].head <= head) {
      lowerPoint = sortedPoints[i]
      upperPoint = sortedPoints[i + 1]
      break
    }
  }

  const ratio = (head - upperPoint.head) / (lowerPoint.head - upperPoint.head)
  return {
    flow: upperPoint.flow + ratio * (lowerPoint.flow - upperPoint.flow),
    efficiency: upperPoint.efficiency1 + ratio * (lowerPoint.efficiency1 - upperPoint.efficiency1),
    power: upperPoint.powerP1 + ratio * (lowerPoint.powerP1 - upperPoint.powerP1)
  }
}

const interpolateFromPower = (power: number) => {
  // 根据功率反向插值计算流量和扬程
  if (dataPoints.value.length < 2) return null

  // 找到相邻的两个数据点（按功率排序）
  const sortedPoints = [...dataPoints.value].sort((a, b) => a.powerP1 - b.powerP1)

  let lowerPoint = sortedPoints[0]
  let upperPoint = sortedPoints[sortedPoints.length - 1]

  for (let i = 0; i < sortedPoints.length - 1; i++) {
    if (sortedPoints[i].powerP1 <= power && sortedPoints[i + 1].powerP1 >= power) {
      lowerPoint = sortedPoints[i]
      upperPoint = sortedPoints[i + 1]
      break
    }
  }

  const ratio = (power - lowerPoint.powerP1) / (upperPoint.powerP1 - lowerPoint.powerP1)
  return {
    flow: lowerPoint.flow + ratio * (upperPoint.flow - lowerPoint.flow),
    head: lowerPoint.head + ratio * (upperPoint.head - lowerPoint.head),
    efficiency: lowerPoint.efficiency1 + ratio * (upperPoint.efficiency1 - lowerPoint.efficiency1)
  }
}

// 图表控制

// 格兰富NBG-300-250-500/485泵数据 - 基于官方曲线图精确提取
const dataPoints = ref<any[]>([
  { flow: 0, head: 35.0, efficiency1: 0, efficiency2: 0, powerP1: 28, powerP2: 26 },
  { flow: 50, head: 34.8, efficiency1: 20, efficiency2: 18, powerP1: 30, powerP2: 28 },
  { flow: 100, head: 34.5, efficiency1: 35, efficiency2: 33, powerP1: 33, powerP2: 31 },
  { flow: 150, head: 34.2, efficiency1: 48, efficiency2: 46, powerP1: 36, powerP2: 34 },
  { flow: 200, head: 33.8, efficiency1: 58, efficiency2: 56, powerP1: 39, powerP2: 37 },
  { flow: 250, head: 33.3, efficiency1: 66, efficiency2: 64, powerP1: 42, powerP2: 40 },
  { flow: 300, head: 32.8, efficiency1: 72, efficiency2: 70, powerP1: 45, powerP2: 43 },
  { flow: 350, head: 32.2, efficiency1: 76, efficiency2: 74, powerP1: 48, powerP2: 46 },
  { flow: 400, head: 31.5, efficiency1: 79, efficiency2: 77, powerP1: 51, powerP2: 49 },
  { flow: 450, head: 30.8, efficiency1: 81, efficiency2: 79, powerP1: 54, powerP2: 52 },
  { flow: 500, head: 30.0, efficiency1: 82, efficiency2: 80, powerP1: 57, powerP2: 55 },
  { flow: 550, head: 29.2, efficiency1: 82, efficiency2: 80, powerP1: 60, powerP2: 58 },
  { flow: 600, head: 28.3, efficiency1: 81, efficiency2: 79, powerP1: 63, powerP2: 61 },
  { flow: 650, head: 27.4, efficiency1: 80, efficiency2: 78, powerP1: 66, powerP2: 64 },
  { flow: 700, head: 26.4, efficiency1: 78, efficiency2: 76, powerP1: 69, powerP2: 67 },
  { flow: 750, head: 25.4, efficiency1: 76, efficiency2: 74, powerP1: 72, powerP2: 70 },
  { flow: 800, head: 24.3, efficiency1: 73, efficiency2: 71, powerP1: 75, powerP2: 73 },
  { flow: 850, head: 23.2, efficiency1: 70, efficiency2: 68, powerP1: 78, powerP2: 76 }
])

const npshPoints = ref<any[]>([
  { flow: 0, npsh: 2.0 },
  { flow: 50, npsh: 2.0 },
  { flow: 100, npsh: 2.1 },
  { flow: 150, npsh: 2.2 },
  { flow: 200, npsh: 2.3 },
  { flow: 250, npsh: 2.4 },
  { flow: 300, npsh: 2.6 },
  { flow: 350, npsh: 2.8 },
  { flow: 400, npsh: 3.0 },
  { flow: 450, npsh: 3.2 },
  { flow: 500, npsh: 3.4 },
  { flow: 550, npsh: 3.7 },
  { flow: 600, npsh: 4.0 },
  { flow: 650, npsh: 4.3 },
  { flow: 700, npsh: 4.6 },
  { flow: 750, npsh: 4.9 },
  { flow: 800, npsh: 5.2 },
  { flow: 850, npsh: 5.5 }
])

// 算法相关数据
const selectedAlgorithm = ref('polynomial') // 先设置默认值
const isApplyingAlgorithm = ref(false)
const hasAppliedAlgorithm = ref(false)

// 保存和加载状态
const isSaving = ref(false)
const isLoading = ref(false)
const loadedFromStorage = ref(false)
const lastSaveTime = ref('')
const loadTime = ref('')
const settingsStatus = ref('未保存')

// 获取默认算法配置
const getDefaultAlgorithmConfig = () => {
  return {
    linear: {
      type: 'linear',
      weights: 'equal'
    },
    polynomial: {
      degree: 3,
      regularization: 0.01
    },
    spline: {
      type: 'cubic',
      smoothing: 0.5
    },
    neural: {
      hiddenLayers: 2,
      neurons: 20,
      learningRate: 0.01,
      epochs: 1000
    },
    bezier: {
      controlPoints: 4,
      tension: 0.5
    }
  }
}

// 算法参数配置 - 使用默认配置
let algorithmConfig = reactive(getDefaultAlgorithmConfig())

// 数据点操作方法
const addDataPoint = () => {
  const newFlow = dataPoints.value.length > 0 
    ? dataPoints.value[dataPoints.value.length - 1].flow + 20 
    : 0
  
  dataPoints.value.push({
    flow: newFlow,
    head: 0,
    efficiency1: 0,
    efficiency2: 0,
    powerP1: 0,
    powerP2: 0
  })
  updateCharts()
}

const removeDataPoint = (index: number) => {
  dataPoints.value.splice(index, 1)
  updateCharts()
}

const clearDataPoints = () => {
  if (confirm('确定要清空所有数据点吗？')) {
    dataPoints.value.splice(0, dataPoints.value.length)
    updateCharts()
  }
}

const addNPSHPoint = () => {
  const newFlow = npshPoints.value.length > 0 
    ? npshPoints.value[npshPoints.value.length - 1].flow + 20 
    : 0
  
  npshPoints.value.push({
    flow: newFlow,
    npsh: 0
  })
  updateCharts()
}

const removeNPSHPoint = (index: number) => {
  npshPoints.value.splice(index, 1)
  updateCharts()
}

const clearNPSHPoints = () => {
  if (confirm('确定要清空所有NPSH数据点吗？')) {
    npshPoints.value.splice(0, npshPoints.value.length)
    updateCharts()
  }
}

// 批量操作方法
const batchAddDataPoints = (count: number) => {
  const startFlow = dataPoints.value.length > 0 
    ? dataPoints.value[dataPoints.value.length - 1].flow + 20 
    : 0
  
  for (let i = 0; i < count; i++) {
    dataPoints.value.push({
      flow: startFlow + i * 20,
      head: 0,
      efficiency1: 0,
      efficiency2: 0,
      powerP1: 0,
      powerP2: 0
    })
  }
  updateCharts()
}

const interpolateDataPoints = () => {
  if (dataPoints.value.length < 2) {
    console.warn('⚠️ 插值计算需要至少2个数据点，当前数据点数量:', dataPoints.value.length)
    return
  }
  
  // 排序数据点
  dataPoints.value.sort((a, b) => a.flow - b.flow)
  
  // 简单线性插值
  const result = []
  const first = dataPoints.value[0]
  const last = dataPoints.value[dataPoints.value.length - 1]
  const step = 20
  
  for (let flow = first.flow; flow <= last.flow; flow += step) {
    // 找到相邻的两个数据点
    let lowerPoint = first
    let upperPoint = last
    
    for (let i = 0; i < dataPoints.value.length - 1; i++) {
      if (dataPoints.value[i].flow <= flow && dataPoints.value[i + 1].flow >= flow) {
        lowerPoint = dataPoints.value[i]
        upperPoint = dataPoints.value[i + 1]
        break
      }
    }
    
    // 线性插值
    const ratio = (flow - lowerPoint.flow) / (upperPoint.flow - lowerPoint.flow)
    const head = lowerPoint.head + ratio * (upperPoint.head - lowerPoint.head)
    const efficiency1 = lowerPoint.efficiency1 + ratio * (upperPoint.efficiency1 - lowerPoint.efficiency1)
    const efficiency2 = lowerPoint.efficiency2 + ratio * (upperPoint.efficiency2 - lowerPoint.efficiency2)
    const powerP1 = lowerPoint.powerP1 + ratio * (upperPoint.powerP1 - lowerPoint.powerP1)
    const powerP2 = lowerPoint.powerP2 + ratio * (upperPoint.powerP2 - lowerPoint.powerP2)
    
    // 检查是否已存在该流量点
    const existingPoint = dataPoints.value.find(p => Math.abs(p.flow - flow) < 0.001)
    if (!existingPoint) {
      result.push({
        flow,
        head,
        efficiency1,
        efficiency2,
        powerP1,
        powerP2
      })
    }
  }
  
  // 添加新插值点
  dataPoints.value.push(...result)
  
  // 重新排序
  dataPoints.value.sort((a, b) => a.flow - b.flow)
  updateCharts()
}

const importGrundfosData = () => {
  // 导入格兰富NBG-300-250-500/485泵数据 - 基于官方曲线图精确提取
  dataPoints.value = [
    { flow: 0, head: 35.0, efficiency1: 0, efficiency2: 0, powerP1: 28, powerP2: 26 },
    { flow: 50, head: 34.8, efficiency1: 20, efficiency2: 18, powerP1: 30, powerP2: 28 },
    { flow: 100, head: 34.5, efficiency1: 35, efficiency2: 33, powerP1: 33, powerP2: 31 },
    { flow: 150, head: 34.2, efficiency1: 48, efficiency2: 46, powerP1: 36, powerP2: 34 },
    { flow: 200, head: 33.8, efficiency1: 58, efficiency2: 56, powerP1: 39, powerP2: 37 },
    { flow: 250, head: 33.3, efficiency1: 66, efficiency2: 64, powerP1: 42, powerP2: 40 },
    { flow: 300, head: 32.8, efficiency1: 72, efficiency2: 70, powerP1: 45, powerP2: 43 },
    { flow: 350, head: 32.2, efficiency1: 76, efficiency2: 74, powerP1: 48, powerP2: 46 },
    { flow: 400, head: 31.5, efficiency1: 79, efficiency2: 77, powerP1: 51, powerP2: 49 },
    { flow: 450, head: 30.8, efficiency1: 81, efficiency2: 79, powerP1: 54, powerP2: 52 },
    { flow: 500, head: 30.0, efficiency1: 82, efficiency2: 80, powerP1: 57, powerP2: 55 },
    { flow: 550, head: 29.2, efficiency1: 82, efficiency2: 80, powerP1: 60, powerP2: 58 },
    { flow: 600, head: 28.3, efficiency1: 81, efficiency2: 79, powerP1: 63, powerP2: 61 },
    { flow: 650, head: 27.4, efficiency1: 80, efficiency2: 78, powerP1: 66, powerP2: 64 },
    { flow: 700, head: 26.4, efficiency1: 78, efficiency2: 76, powerP1: 69, powerP2: 67 },
    { flow: 750, head: 25.4, efficiency1: 76, efficiency2: 74, powerP1: 72, powerP2: 70 },
    { flow: 800, head: 24.3, efficiency1: 73, efficiency2: 71, powerP1: 75, powerP2: 73 },
    { flow: 850, head: 23.2, efficiency1: 70, efficiency2: 68, powerP1: 78, powerP2: 76 }
  ]

  npshPoints.value = [
    { flow: 0, npsh: 2.0 },
    { flow: 50, npsh: 2.0 },
    { flow: 100, npsh: 2.1 },
    { flow: 150, npsh: 2.2 },
    { flow: 200, npsh: 2.3 },
    { flow: 250, npsh: 2.4 },
    { flow: 300, npsh: 2.6 },
    { flow: 350, npsh: 2.8 },
    { flow: 400, npsh: 3.0 },
    { flow: 450, npsh: 3.2 },
    { flow: 500, npsh: 3.4 },
    { flow: 550, npsh: 3.7 },
    { flow: 600, npsh: 4.0 },
    { flow: 650, npsh: 4.3 },
    { flow: 700, npsh: 4.6 },
    { flow: 750, npsh: 4.9 },
    { flow: 800, npsh: 5.2 },
    { flow: 850, npsh: 5.5 }
  ]

  updateCharts()
}

// 文件操作方法
const downloadTemplate = () => {
  const template = {
    performance: [
      { flow: 0, head: 0, efficiency1: 0, efficiency2: 0, powerP1: 0, powerP2: 0 },
      { flow: 0, head: 0, efficiency1: 0, efficiency2: 0, powerP1: 0, powerP2: 0 }
    ],
    npsh: [
      { flow: 0, npsh: 0 },
      { flow: 0, npsh: 0 }
    ]
  }
  
  const wb = XLSX.utils.book_new()
  const wsPerformance = XLSX.utils.json_to_sheet(template.performance)
  const wsNPSH = XLSX.utils.json_to_sheet(template.npsh)
  
  XLSX.utils.book_append_sheet(wb, wsPerformance, '性能曲线')
  XLSX.utils.book_append_sheet(wb, wsNPSH, 'NPSH')
  
  XLSX.writeFile(wb, '格兰富水泵数据模板.xlsx')
}

const importData = (file: File) => {
  const reader = new FileReader()
  
  reader.onload = (e) => {
    try {
      const data = e.target?.result
      const workbook = XLSX.read(data, { type: 'binary' })
      
      // 读取性能曲线工作表
      const performanceSheet = workbook.Sheets[workbook.SheetNames[0]]
      const performanceData = XLSX.utils.sheet_to_json(performanceSheet)
      
      // 读取NPSH工作表
      const npshSheet = workbook.Sheets[workbook.SheetNames[1]]
      const npshData = XLSX.utils.sheet_to_json(npshSheet)
      
      // 更新数据
      dataPoints.value = performanceData as any[]
      npshPoints.value = npshData as any[]
      
      updateCharts()
      console.log('✅ 数据导入成功', {
        performanceDataPoints: performanceData.length,
        npshDataPoints: npshData.length
      })
    } catch (error) {
      console.error('❌ 数据导入失败:', error)
      alert('导入失败，请检查文件格式是否正确')
    }
  }
  
  reader.readAsBinaryString(file)
}

const exportData = () => {
  const wb = XLSX.utils.book_new()
  const wsPerformance = XLSX.utils.json_to_sheet(dataPoints.value)
  const wsNPSH = XLSX.utils.json_to_sheet(npshPoints.value)
  
  XLSX.utils.book_append_sheet(wb, wsPerformance, '性能曲线')
  XLSX.utils.book_append_sheet(wb, wsNPSH, 'NPSH')
  
  XLSX.writeFile(wb, '格兰富水泵数据.xlsx')
}



// 初始化图表
const initCharts = () => {
  // 确保容器已挂载
  if (!performanceChartContainer.value || !powerNpshChartContainer.value) {
    console.warn('图表容器未挂载，延迟初始化')
    return
  }

  // 检查是否已经初始化
  if (performanceChart) {
    performanceChart.dispose()
  }
  if (powerNpshChart) {
    powerNpshChart.dispose()
  }

  // 使用 requestAnimationFrame 确保DOM完全渲染
  requestAnimationFrame(() => {
    if (!performanceChartContainer.value || !powerNpshChartContainer.value) return

    // 强制设置容器尺寸，确保有明确的宽高
    performanceChartContainer.value.style.width = '100%'
    performanceChartContainer.value.style.height = '400px'
    performanceChartContainer.value.style.minWidth = '800px'
    performanceChartContainer.value.style.minHeight = '400px'

    powerNpshChartContainer.value.style.width = '100%'
    powerNpshChartContainer.value.style.height = '400px'
    powerNpshChartContainer.value.style.minWidth = '800px'
    powerNpshChartContainer.value.style.minHeight = '400px'

    // 再次使用 requestAnimationFrame 确保样式生效
    requestAnimationFrame(() => {
      if (!performanceChartContainer.value || !powerNpshChartContainer.value) return

      // 检查容器大小
      const perfWidth = performanceChartContainer.value.offsetWidth
      const perfHeight = performanceChartContainer.value.offsetHeight
      const powerWidth = powerNpshChartContainer.value.offsetWidth
      const powerHeight = powerNpshChartContainer.value.offsetHeight

      console.log(`📊 图表容器尺寸 - 性能图: ${perfWidth}x${perfHeight}, 功率图: ${powerWidth}x${powerHeight}`)

      if (perfWidth > 0 && perfHeight > 0 && powerWidth > 0 && powerHeight > 0) {
        try {
          // 初始化性能曲线图表
          performanceChart = echarts.init(performanceChartContainer.value)
          // 初始化功率与NPSH曲线图表
          powerNpshChart = echarts.init(powerNpshChartContainer.value)

          // 设置图表选项
          updateCharts()

          // 监听窗口尺寸变化
          window.addEventListener('resize', resizeCharts)

          // 设置尺寸监听器
          setupResizeObserver()

          console.log('✅ 图表初始化成功')
        } catch (error) {
          console.error('❌ 图表初始化失败:', error)
        }
      } else {
        console.warn('⚠️ 图表容器尺寸仍为0，延迟重试')
        // 延迟重试
        setTimeout(() => initCharts(), 100)
      }
    })
  })
}

// 监听容器尺寸变化
let resizeObserver: ResizeObserver | null = null
const setupResizeObserver = () => {
  if (typeof ResizeObserver !== 'undefined') {
    resizeObserver = new ResizeObserver(() => {
      resizeCharts()
    })
    
    if (performanceChartContainer.value) {
      resizeObserver.observe(performanceChartContainer.value)
    }
    if (powerNpshChartContainer.value) {
      resizeObserver.observe(powerNpshChartContainer.value)
    }
  }
}

// 获取当前算法的参数
const getCurrentAlgorithmParams = () => {
  const algorithm = selectedAlgorithm.value
  switch (algorithm) {
    case 'linear':
      return algorithmConfig.linear
    case 'polynomial':
      return algorithmConfig.polynomial
    case 'spline':
      return algorithmConfig.spline
    case 'neural':
      return algorithmConfig.neural
    case 'bezier':
      return algorithmConfig.bezier
    default:
      return algorithmConfig.polynomial
  }
}

// 更新图表
const updateCharts = () => {
  if (!performanceChart || !powerNpshChart) return

  // 如果有系统性能结果，使用系统曲线数据，否则使用单泵数据
  const chartDataPoints = systemPerformance.value ? systemPerformance.value.systemCurve : dataPoints.value
  const chartNpshPoints = systemPerformance.value ? systemPerformance.value.npshCurve : npshPoints.value

  // 设置通用参数
  const commonParams = {
    dataPoints: chartDataPoints,
    npshPoints: chartNpshPoints,
    singlePumpData: dataPoints.value, // 保留单泵数据用于对比
    settings,
    operatingPoint,
    customOperatingPoint,
    algorithm: selectedAlgorithm.value,
    algorithmParams: getCurrentAlgorithmParams(),
    algorithmConfig: algorithmConfig, // 传递完整的算法配置
    hydraulicConfig: hydraulicConfig.value, // 传递水力布局配置
    systemPerformance: systemPerformance.value // 传递系统性能结果
  };

  console.log('更新图表 - 当前算法:', selectedAlgorithm.value, '水力布局:', hydraulicConfig.value.installationType)

  // 设置性能曲线图表
  performanceChart.setOption(createPerformanceChartOption({
    ...commonParams,
    pumpParameters: {
      ratedFlow: 400,
      ratedHead: 50,
      ratedEfficiency: 85,
      ratedPower: 22,
      speed: 2900,
      impellerDiameter: 250
    }
  }), true);

  // 设置功率与NPSH曲线图表
  powerNpshChart.setOption(createPowerNpshChartOption({
    ...commonParams,
    pumpParameters: {
      ratedFlow: 400,
      ratedHead: 50,
      ratedEfficiency: 85,
      ratedPower: 22,
      speed: 2900,
      impellerDiameter: 250
    }
  }), true);
}

// 添加缺失的 updateCurves 方法
const updateCurves = () => {
  console.log('更新曲线数据')
  updateCharts()
}

// 调整图表大小
const resizeCharts = () => {
  performanceChart?.resize()
  powerNpshChart?.resize()
}

// 清理资源
onUnmounted(() => {
  // 清理事件监听
  window.removeEventListener('resize', resizeCharts)
  
  // 清理尺寸监听器
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
  
  // 清理图表实例
  if (performanceChart) {
    performanceChart.dispose()
    performanceChart = null
  }
  if (powerNpshChart) {
    powerNpshChart.dispose()
    powerNpshChart = null
  }
})

// 算法参数更新
const updateAlgorithmParams = (newParams: any) => {
  Object.assign(algorithmConfig, newParams)
}

// 更新曲线和生成曲线
const generateCurves = () => {
  activeTab.value = 'curves'
  updateCharts()
}

// 算法应用方法
const applyAlgorithm = async () => {
  if (!selectedAlgorithm.value) {
    console.warn('⚠️ 请先选择一个算法')
    return
  }

  isApplyingAlgorithm.value = true

  try {
    // 模拟算法应用过程
    await new Promise(resolve => setTimeout(resolve, 800))

    hasAppliedAlgorithm.value = true
    updateCharts() // 重新渲染图表

    console.log(`✅ ${selectedAlgorithm.value}算法应用成功`, {
      algorithm: selectedAlgorithm.value,
      params: getCurrentAlgorithmParams(),
      dataPointsCount: dataPoints.value.length
    })
  } catch (error) {
    console.error('❌ 算法应用失败:', error)
    alert('算法应用失败')
  } finally {
    isApplyingAlgorithm.value = false
  }
}

// 获取算法显示名称
const getAlgorithmDisplayName = (algorithm: string) => {
  const names = {
    linear: '线性回归',
    polynomial: '多项式拟合',
    spline: '样条插值',
    neural: '神经网络',
    bezier: '贝塞尔曲线'
  }
  return names[algorithm as keyof typeof names] || algorithm
}

// 切换到曲线标签页
const switchToCurves = () => {
  activeTab.value = 'curves'
}

// 重置算法参数
const resetAlgorithmParams = () => {
  const defaultConfig = {
    linear: { type: 'linear', weights: 'equal' },
    polynomial: { degree: 3, regularization: 0.01 },
    spline: { type: 'cubic', smoothing: 0.5 },
    neural: { hiddenLayers: 2, neurons: 20, learningRate: 0.01, epochs: 1000 },
    bezier: { controlPoints: 4, tension: 0.5 }
  }

  Object.assign(algorithmConfig, defaultConfig)
  console.log('🔄 算法参数已重置为默认值', defaultConfig)
}

// 保存算法设置到localStorage
const saveAlgorithmSettings = async () => {
  isSaving.value = true

  try {
    // 模拟保存过程
    await new Promise(resolve => setTimeout(resolve, 500))

    const settings = {
      selectedAlgorithm: selectedAlgorithm.value,
      algorithmConfig: algorithmConfig,
      timestamp: new Date().toISOString()
    }

    localStorage.setItem('grundfos-selected-algorithm', selectedAlgorithm.value)
    localStorage.setItem('grundfos-algorithm-config', JSON.stringify(algorithmConfig))
    localStorage.setItem('grundfos-algorithm-settings', JSON.stringify(settings))

    lastSaveTime.value = new Date().toLocaleString()
    settingsStatus.value = '已保存到本地'

    console.log('✅ 算法设置已成功保存到本地存储！', {
      selectedAlgorithm: selectedAlgorithm.value,
      algorithmConfig: algorithmConfig,
      timestamp: lastSaveTime.value
    })
  } catch (error) {
    console.error('保存失败:', error)
    alert('保存失败，请重试')
  } finally {
    isSaving.value = false
  }
}

// 从localStorage加载算法设置
const loadAlgorithmSettings = async () => {
  isLoading.value = true

  try {
    // 模拟加载过程
    await new Promise(resolve => setTimeout(resolve, 500))

    const savedAlgorithm = localStorage.getItem('grundfos-selected-algorithm')
    const savedConfig = localStorage.getItem('grundfos-algorithm-config')
    const savedSettings = localStorage.getItem('grundfos-algorithm-settings')

    if (savedAlgorithm && savedConfig) {
      // 加载算法选择
      selectedAlgorithm.value = savedAlgorithm

      // 加载算法配置
      const parsedConfig = JSON.parse(savedConfig)
      Object.assign(algorithmConfig, parsedConfig)

      // 更新状态
      loadedFromStorage.value = true
      loadTime.value = new Date().toLocaleString()
      settingsStatus.value = '已从本地加载'

      if (savedSettings) {
        const settings = JSON.parse(savedSettings)
        lastSaveTime.value = new Date(settings.timestamp).toLocaleString()
      }

      // 更新图表
      updateCharts()

      console.log('✅ 算法设置已成功从本地存储加载！', {
        selectedAlgorithm: selectedAlgorithm.value,
        loadedConfig: savedSettings,
        timestamp: new Date().toLocaleString()
      })
    } else {
      console.log('ℹ️ 未找到保存的设置，将使用默认配置')
      settingsStatus.value = '使用默认配置'
    }
  } catch (error) {
    console.error('❌ 算法设置加载失败:', error)
    alert('加载失败，将使用默认配置')
    settingsStatus.value = '加载失败，使用默认配置'
  } finally {
    isLoading.value = false
  }
}

// 清除本地存储
const clearLocalStorage = () => {
  if (confirm('确定要清除所有本地保存的算法设置吗？此操作不可撤销。')) {
    localStorage.removeItem('grundfos-selected-algorithm')
    localStorage.removeItem('grundfos-algorithm-config')
    localStorage.removeItem('grundfos-algorithm-settings')

    // 重置为默认值
    selectedAlgorithm.value = 'polynomial'
    Object.assign(algorithmConfig, getDefaultAlgorithmConfig())

    // 重置状态
    loadedFromStorage.value = false
    lastSaveTime.value = ''
    loadTime.value = ''
    settingsStatus.value = '已清除，使用默认配置'

    updateCharts()
    console.log('🗑️ 本地存储已清除，已重置为默认配置')
  }
}

// 水力布局配置变化处理
const onHydraulicConfigChanged = (result: SystemPerformanceResult) => {
  systemPerformance.value = result

  // 同步到settings中的相关配置
  settings.installationType = hydraulicConfig.value.installationType
  settings.seriesConnection = hydraulicConfig.value.seriesConnection === 'none' ? 'no' : 'yes'
  settings.variableSpeed = hydraulicConfig.value.variableSpeed === 'none' ? 'no' : 'yes'

  // 重新渲染图表
  updateCharts()
}

// 导出算法配置
const exportAlgorithmConfig = () => {
  const config = {
    selectedAlgorithm: selectedAlgorithm.value,
    algorithmConfig: algorithmConfig,
    timestamp: new Date().toISOString()
  }

  const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `grundfos-algorithm-config-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

// 监听算法选择变化 - 只更新图表，不自动保存
watch(selectedAlgorithm, (newValue) => {
  settingsStatus.value = '有未保存的更改'
  updateCharts() // 算法变化时重新渲染图表
})

// 监听算法参数变化 - 只更新图表，不自动保存
watch(algorithmConfig, (newValue) => {
  settingsStatus.value = '有未保存的更改'
  updateCharts() // 参数变化时重新渲染图表
}, { deep: true })

// 监听标签页切换，当切换到参数标签时初始化预览图表
watch(activeTab, (newTab) => {
  if (newTab === 'parameters') {
    nextTick(() => {
      setTimeout(() => {
        if (parametersTabRef.value && parametersTabRef.value.initChartWhenVisible) {
          parametersTabRef.value.initChartWhenVisible()
        }
      }, 100)
    })
  }
})

// 组件挂载时初始化
onMounted(() => {
  // 检查是否有保存的设置
  const savedAlgorithm = localStorage.getItem('grundfos-selected-algorithm')
  const savedConfig = localStorage.getItem('grundfos-algorithm-config')

  if (savedAlgorithm && savedConfig) {
    // 有保存的设置，自动加载
    setTimeout(() => {
      console.log('🔄 检测到本地保存的算法设置，自动加载中...', {
        savedAlgorithm,
        hasConfig: !!savedConfig
      })
      loadAlgorithmSettings()
    }, 500) // 减少延迟时间
  } else {
    settingsStatus.value = '使用默认配置'
  }

  nextTick(() => {
    setTimeout(() => {
      initCharts()
    }, 200) // 增加延迟确保DOM完全渲染
  })
})
</script>

<style lang="scss" scoped>
.high-precision-curves {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding: 20px;
  
  .tab-navigation {
    display: flex;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;
    overflow: hidden;
    
    .tab-item {
      flex: 1;
      padding: 16px 24px;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      color: #666;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      
      &:not(:last-child) {
        border-right: 1px solid #f0f0f0;
      }
      
      &:hover {
        color: #333;
        background-color: #f9f9f9;
      }
      
      &.active {
        color: #0066cc;
        background-color: #f0f7ff;
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 3px;
          background: linear-gradient(90deg, #0066cc, #3399ff);
        }
      }
    }
  }
  
  // 新的两列布局样式
  .curves-layout {
    display: flex;
    gap: 24px;
    
    .charts-column {
      flex: 1;
      min-width: 0; // 解决flex子项溢出问题
    }
    
    .settings-column {
      width: 380px;
      flex-shrink: 0;
    }
  }
  
  .charts-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .chart-wrapper {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
  
  .chart-title {
    padding: 10px 15px;
    font-size: 16px;
    font-weight: 600;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
  }
  
  .chart-container {
    height: 400px;
    width: 100%;
    transition: all 0.3s ease;
    

  }
  

  
  .curve-settings-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
  }
  
  .settings-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .setting-group {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
    transition: all 0.3s ease;
  }
  
  .setting-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #ecf5ff;
    cursor: pointer;
  }
  
  .setting-header h4 {
    margin: 0;
    color: #409eff;
  }
  
  .toggle-icon {
    color: #409eff;
  }
  
  .setting-content {
    padding: 15px;
    background-color: white;
  }
  
  .radio-group, .checkbox-group, .select-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
  }
  
  .input-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-bottom: 10px;
  }
  
  .custom-point {
    margin-top: 15px;
    padding: 10px;
    border: 1px dashed #dcdfe6;
    border-radius: 4px;
  }

  .operating-point-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .custom-input-area {
    margin-top: 10px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
  }

  .input-with-unit {
    display: flex;
    align-items: center;
    gap: 8px;

    .el-input-number {
      flex: 1;
    }

    .unit-select {
      width: 120px;
    }

    .unit {
      color: #606266;
      font-size: 14px;
      min-width: 60px;
      text-align: center;
    }
  }
  
  .algorithm-section {
    padding: 24px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

    .algorithm-header {
      text-align: center;
      margin-bottom: 2rem;

      h2 {
        font-size: 1.75rem;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0 0 0.75rem;
        background: linear-gradient(90deg, #409eff 0%, #79bbff 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      p {
        font-size: 1rem;
        color: var(--el-text-color-secondary);
        margin: 0;
        line-height: 1.5;
      }
    }

    .algorithm-status {
      margin: 1.5rem 0;

      .el-alert {
        border-radius: 8px;

        p {
          margin: 0.25rem 0;
          font-size: 0.9rem;
        }
      }
    }

    .algorithm-actions {
      display: flex;
      justify-content: center;
      gap: 1rem;
      margin: 2rem 0;
      flex-wrap: wrap;

      .el-button {
        min-width: 120px;
      }
    }
  }
  
  .algorithm-params {
    margin-top: 10px;
    padding: 10px;
    background-color: #f8f9fb;
    border-radius: 4px;
  }
}

// 响应式适配
@media (max-width: 1200px) {
  .high-precision-curves {
    .curves-layout {
      flex-direction: column;

      .settings-column {
        width: 100%;
      }
    }
  }
}

@media (max-width: 768px) {
  .high-precision-curves {
    padding: 12px;
    
    .tab-navigation {
      .tab-item {
        padding: 12px 16px;
        font-size: 14px;
      }
    }
    
    .algorithm-section {
      grid-template-columns: 1fr;
      padding: 16px;
    }
  }
}
</style>