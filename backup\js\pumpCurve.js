var myConfig = JSON.parse(localStorage.getItem("myConfig"))
var isIndex = myConfig["isIndex"]

if ( isIndex == 0 ){
        
    var dom = document.getElementById("main");
    var myChart = echarts.init(dom);
    var app = {};

    var option;



    echarts.registerTransform(ecStat.transform.regression);

    const colors = ['#000000', 'rgba(0,0,0,0)'];

    const data1 = [
            [0, 44.28],
            [200, 43.58],
            [400, 42.35],
            [600, 40.42],
            [800, 37.25],
            [1000, 33.56],
            [1200, 29.17],
            [1400, 23.72],
            ];
          
    const data2 = [
            [0, 0],
            [200, 28],
            [400, 48],
            [600, 63],
            [800, 75],
            [1000, 81],
            [1200, 83],
            [1400, 81],
            ];

    const data3 = [
            [0, 76],
            [200, 84],
            [400, 91],
            [600, 100],
            [800, 105],
            [1000, 108],
            [1200, 109],
            [1400, 107],
            ];

    //电流，在下面自动补全
    const data4 = [
            [0, ],
            [200, ],
            [400, ],
            [600, ],
            [800, ],
            [1000, ],
            [1200, ],
            [1400, ],
            ];





    //鼠标位置，全局变量
    var qValue;
    var hValue;
    var etaValue;
    var qhOrder = 3;
    var qetaOrder = 2;
    var qpOrder = 2;

    //坐标轴尺度
    var x1data1max = 0
    var y11data1max = 0
    var y21data1max = 0

    for(var i=0; i<data1.length; i++){
    if(data1[i][0] > x1data1max){
        x1data1max = data1[i][0]
    }
    if(data1[i][1] > y11data1max){
        y11data1max = data1[i][1]
    }
    if(data3[i][1] > y21data1max){
        y21data1max = data3[i][1]
    }

    //电流
    data4[i][1] = data3[i][1] * 1000 /(1.732 * 380 * 0.85)
    }
    x1data1max = Math.floor(Math.ceil(x1data1max*(1+1/7)));
    y11data1max = Math.floor(Math.ceil(y11data1max/10)*10);

    itemstimes = 0;
    while (y21data1max > 10){
    y21data1max = y21data1max/10
    itemstimes += 1
    }
    y21data1max = Math.floor(Math.ceil(y21data1max/6)*6*10*itemstimes);


    option = {
    
    
    dataset: [
        {
            source: data1 //0
        },{
            transform: {
                type: 'ecStat:regression',
                config: { method: 'polynomial', order: qhOrder } //1
            }
        },


        {
            source: data2 //2
        },{
            fromDatasetIndex: 2,
            transform: {
                type: 'ecStat:regression',
                config: { method: 'polynomial', order: qetaOrder } //3
            }
        },

        {
            source: data3 //4
        },{
            fromDatasetIndex: 4,
            transform: {
                type: 'ecStat:regression',
                config: { method: 'polynomial', order: qpOrder } //5
            }
        },

        {
            source: data4 //6
        },{
            fromDatasetIndex: 6,
            transform: {
                type: 'ecStat:regression',
                config: { method: 'polynomial', order: 2 } //7
            }
        },
        
    ],

    
    color: colors,
    tooltip: {
        trigger: 'item',
        showContent: true,
        triggerOn: 'mousemove',
        formatter: function (params,value) {
        //console.log(params)
        return '流量：' + qValue + ' m<br>扬程：' + hValue + ' m<sup>3</sup>/s'
        },
        
        snap: false,
        axisPointer: {
        type: 'cross',
        snap: false,
        label:{show:false}
        }
    },
    
    
    
    grid: [
        { left: '10%', right: '10%', top: '6%', height: '50%' },
        { left: '10%', right: '10%', bottom: '6%', height: '30%' },
    ],
    
    
    toolbox: {
        right: '15%',
        feature: {
        restore: { show: true },
        saveAsImage: { show: true }
        }
    },
    
    
    legend: {
        data: ['流量-扬程',  '流量-效率', '等效线']
    },
    
    
    
    xAxis: [
        //0
        {
        type: 'value',
        splitNumber: 8,
        min: 0,
        max: x1data1max,
        
        gridIndex: 0,
        name: '流量 (Q)',
        nameLocation: 'middle',

        axisTick: {
            alignWithLabel: true
        },
        


        axisPointer: {
            label: {
            formatter: function (params) {
                qValue = params.value.toFixed(2);
            }
            }
        }
        },

        //1
        {
        type: 'value',
        splitNumber: 8,
        min: 0,
        max: x1data1max,
        
        gridIndex: 1,
        name: '流量 (Q)',
        nameLocation: 'middle',

        axisTick: {
            alignWithLabel: true
        },

        }
        
    ],
    
    
    
    yAxis: [
        

        //0扬程轴
        {
        type: 'value',
        name: '扬程 (H)',
        //maxInterval:10,
        min: 0,
        max: y11data1max,

        gridIndex: 0,
        splitNumber: 12,
        axisLabel: {
            formatter: '{value} m'
        },
        
        
        axisPointer: {
            label: {
            formatter: function (params) {
                hValue = params.value.toFixed(2);
            }
            }
        }
        },
        
        

        //1效率轴
        {
        type: 'value',
        name: '效率 (η)',
        min: 0,
        max: y11data1max * 4,
        //maxInterval:40,

        gridIndex: 0,
        splitNumber: 12,
        

        axisLabel: {
            show: true,
            lineStyle: {
            color: 'colors[0]',
            },
            formatter: function(value, index) {
            //console.log(value, index);
            // Y轴的自定义刻度值，对应图上的y轴的值
            
            if (value <= 100) {
                g = value + '%'
            }else{
                g =  ''
            }
                return g;
            }
        },
        
        
        axisTick: {show: false},  //不显示刻度
        axisPointer: {
            label: {
            formatter: function (params) {
                if (params.value <= 100 ){
                etaValue = params.value.toFixed(2);
                }
            }
            }
        }
        },


        //2功率轴
        {
        type: 'value',
        name: '功率 (P)',
        //maxInterval:10,
        min: 0,
        max: y21data1max,

        gridIndex: 1,
        splitNumber: 6,
        axisLabel: {
            formatter: '{value} kW'
        },
        
        
        axisPointer: {
            label: {
            formatter: function (params) {
                hValue = params.value.toFixed(2);
            }
            }
        }
        },

        //3电流轴
        {
        type: 'value',
        name: '电流 (I)',
        min: 0,
        max: y21data1max * 3,
        // maxInterval:60,

        gridIndex: 1,
        splitNumber: 6,
        

        axisLabel: {
            show: true,
            lineStyle: {
            color: 'colors[0]',
            },
            // formatter: function(value, index) {
            //   // console.log(value, index);
            //   // Y轴的自定义刻度值，对应图上的y轴的值
            
            //   if (value <= 100) {
            //     g = value + 'A'
            //   }else{
            //     g =  ''
            //   }
            //     return g;
            // }
        },
        
        
        axisTick: {show: false},  //不显示刻度
        axisPointer: {
            label: {
            formatter: function (params) {
                if (params.value <= 100 ){
                etaValue = params.value.toFixed(2);
                }
            }
            }
        }
        },
        
    
    
    
    ],
    
    
    
    

    series: [
        

        
        {
        name: '流量-扬程',
        type: 'line',
        smooth: true,
        symbolSize: 8,
        symbol: 'circle',
        lineStyle: {
            width: 3, //线宽
        },
        datasetIndex: 1, //数据1
        xAxisIndex: 0,
        yAxisIndex: 0, //左轴

        },
        
        {
        name: '数据1',
        type: 'scatter',
        symbolSize:2000, //调大触发tooltip
        xAxisIndex: 0,
        yAxisIndex: 0,
        },
        
        {
        name: '流量-效率',
        type: 'line',
        smooth: true,
        symbolSize: 8, 
        symbol: 'circle',
        lineStyle: {
            width: 3,
        },
        datasetIndex: 3, //数据3
        xAxisIndex: 0,
        yAxisIndex: 1, //右轴
        },
        

        
        
        {
        name: '数据2',
        type: 'scatter',
        datasetIndex: 2,  //数据3
        xAxisIndex: 0,
        yAxisIndex: 1,//右轴
        },


        {
        name: '流量-功率',
        type: 'line',
        smooth: true,
        symbolSize: 8,
        symbol: 'circle',
        lineStyle: {
            width: 3, //线宽
        },
        datasetIndex: 5, //数据5
        xAxisIndex: 1,
        yAxisIndex: 2, //左轴

        },
        {name: '数据3',
        type: 'scatter',
        datasetIndex: 4,  //数据3
        xAxisIndex: 1,
        yAxisIndex: 2,//右轴},

        },


        {
        name: '流量-电流',
        type: 'line',
        smooth: true,
        symbolSize: 8, 
        symbol: 'circle',
        lineStyle: {
            width: 3,
        },
        datasetIndex: 7, //数据7
        xAxisIndex: 1,
        yAxisIndex: 3, //右轴
        },

        // {
        //   name: '等效线',
        //   type: 'line',
        //   smooth: true,
        //   symbolSize: 8,
        //   symbol: 'circle',
        //   showSymbol:true,
        //   lineStyle: {
        //     width: 3,
        //   },
        //   datasetIndex: 3, //数据2
        //   yAxisIndex: 0, //左轴

        // },
    ]
    };

    if (option && typeof option === 'object') {
        myChart.setOption(option);
    }

    //点击变频
    myChart.on('click' ,function () {
        // 在用户点击后控制台打印数据的名称
        // console.log(qValue);
        // console.log(hValue);
        var minF = 0;
        var maxF = 50;
        var medianF = (minF + maxF) /2;

        var times = 0;
            while (times < 30){
                
                //深拷贝
                var data1Copy = JSON.parse(JSON.stringify(data1));

                //转换数据
                for(var i=0; i<data1.length; i++){
                    data1Copy[i][0] = data1Copy[i][0] * medianF/50;
                    data1Copy[i][1] = data1Copy[i][1] * (medianF/50)**2 ;
                }
                // console.log((medianF/50)**2)

                //预测扬程
                var myRegression = ecStat.regression('polynomial', data1Copy, qhOrder);
                var predhValue = 0;
                for(var i=0; i<myRegression.parameter.length; i++){
                    predhValue += myRegression.parameter[i] * qValue ** i;
                }
                // console.log(predhValue, hValue)

                //扬程和预测扬程之差
                if(Math.abs(predhValue - hValue) < 0.0001){
                    // console.log('迭代',times, '次', '预测扬程：',predhValue, '扬程：', hValue, '频率：', medianF/50);
                    
                    //获取变频后的流量-效率数据
                    var data2Copy = JSON.parse(JSON.stringify(data2));
                    //获取变频后的流量-功率数据
                    var data3Copy = JSON.parse(JSON.stringify(data3));
                    //获取变频后的流量-电流数据
                    var data4Copy = JSON.parse(JSON.stringify(data4));
                    for(var i=0; i<data1.length; i++){
                    //效率不变
                    data2Copy[i][0] = data2Copy[i][0] * medianF/50;
                    
                    //功率三次方
                    data3Copy[i][0] = data2Copy[i][0]
                    data3Copy[i][1] = data3Copy[i][1] * (medianF/50)**3;

                    //电流
                    data4Copy[i][0] = data2Copy[i][0]
                    data4Copy[i][1] = data3Copy[i][1] * 1000 /(1.732 * 380 * 0.85)
                    }


                    //更新数据
                    myChart.setOption({dataset:[{source: data1Copy}, {}, {source: data2Copy}, {}, {source: data3Copy}, {}, {source: data4Copy}]})
                    break;
                }
                else if(predhValue < hValue){
                    minF = medianF;
                    medianF = (minF + maxF) /2;
                }
                else if(predhValue > hValue){
                    maxF = medianF;
                    medianF = (minF + maxF) /2;
                }
                
                times += 1
            }  
    });

}


