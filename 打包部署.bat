@echo off
chcp 65001 >nul
title 智能供水平台 - 打包部署

echo.
echo ========================================
echo    智能供水平台 - 打包部署工具
echo ========================================
echo.

echo [INFO] 正在停止可能在运行的旧版服务器...
taskkill /F /IM smart-water-server.exe /T >nul 2>&1

echo [INFO] 正在编译前端Vue项目...
pnpm run build
if %errorlevel% neq 0 (
    echo [ERROR] 前端编译失败!
    pause
    exit /b 1
)

echo [INFO] 正在编译后端Go服务器...
go build -o smart-water-server.exe server.go
if %errorlevel% neq 0 (
    echo [ERROR] 后端编译失败!
    pause
    exit /b 1
)

set "DEPLOY_DIR=智能供水平台-Windows部署包"
echo [INFO] 正在清理旧的部署目录...
if exist "%DEPLOY_DIR%" (
    rmdir /s /q "%DEPLOY_DIR%"
)

echo [INFO] 正在创建和复制文件到部署目录: %DEPLOY_DIR%
mkdir "%DEPLOY_DIR%"
mkdir "%DEPLOY_DIR%\dist"

xcopy "dist" "%DEPLOY_DIR%\dist" /E /I /H /Y /Q >nul
copy "smart-water-server.exe" "%DEPLOY_DIR%\" >nul
copy "启动服务器.bat" "%DEPLOY_DIR%\" >nul
copy "README-部署说明.md" "%DEPLOY_DIR%\" >nul

echo.
echo [SUCCESS] 打包成功完成！
echo [INFO] 部署包位于: %CD%\%DEPLOY_DIR%
echo.
echo [INSTRUCTIONS] 使用说明:
echo 1. 将 "%DEPLOY_DIR%" 文件夹复制到目标服务器。
echo 2. 在服务器上, 双击 "启动服务器.bat" 启动服务。
echo 3. 在浏览器中访问 http://localhost:8080
echo.

pause