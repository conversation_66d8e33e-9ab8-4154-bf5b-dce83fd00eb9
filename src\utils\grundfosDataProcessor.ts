/**
 * 格兰富数据处理工具
 * 基于格兰富官网的数据格式和计算逻辑
 */

// 格兰富标准数据接口
export interface GrundfosConfig {
  qcId?: string
  qc?: any
  results?: any
  groups?: any[]
  questions?: any[]
  isQcComplete?: boolean
  isConsistent?: boolean
}

export interface PumpCurveData {
  flow: number
  head: number
  efficiency: number
  power: number
  npsh: number
  bestEfficiencyPoint?: { q: number; h: number; efficiency: number }
}

export interface PumpParameters {
  fluid: string
  backupPumps: number
  runningPumps: number
  impellerDiameter: number
  speed: number
  temperature: number
  density: number
}

// 格兰富标准曲线数据
export const GRUNDFOS_CURVE_DATA = {
  // NBG 300-250-500/525 标准数据
  headCurve: [
    { flow: 0, head: 42.5 },
    { flow: 100, head: 42.2 },
    { flow: 200, head: 41.5 },
    { flow: 300, head: 40.5 },
    { flow: 400, head: 39.2 },
    { flow: 500, head: 37.5 },
    { flow: 600, head: 35.5 },
    { flow: 700, head: 33.2 },
    { flow: 800, head: 30.5 },
    { flow: 900, head: 27.5 }
  ],
  
  efficiencyCurve: [
    { flow: 0, efficiency: 0 },
    { flow: 100, efficiency: 0.35 },
    { flow: 200, efficiency: 0.55 },
    { flow: 300, efficiency: 0.68 },
    { flow: 400, efficiency: 0.75 },
    { flow: 500, efficiency: 0.80 },
    { flow: 600, efficiency: 0.82 },
    { flow: 700, efficiency: 0.81 },
    { flow: 800, efficiency: 0.78 },
    { flow: 900, efficiency: 0.73 }
  ].map(p => ({ ...p, efficiency: p.efficiency * 100 })),
  
  // 单泵效率2曲线 - 稍微不同的效率值
  efficiency2Curve: [
    { flow: 0, efficiency: 0 },
    { flow: 100, efficiency: 0.32 },
    { flow: 200, efficiency: 0.52 },
    { flow: 300, efficiency: 0.65 },
    { flow: 400, efficiency: 0.72 },
    { flow: 500, efficiency: 0.77 },
    { flow: 600, efficiency: 0.79 },
    { flow: 700, efficiency: 0.78 },
    { flow: 800, efficiency: 0.75 },
    { flow: 900, efficiency: 0.70 }
  ].map(p => ({ ...p, efficiency: p.efficiency * 100 })),
  
  powerCurve: [
    { flow: 0, power: 38 },
    { flow: 100, power: 42 },
    { flow: 200, power: 48 },
    { flow: 300, power: 55 },
    { flow: 400, power: 63 },
    { flow: 500, power: 72 },
    { flow: 600, power: 82 },
    { flow: 700, power: 93 },
    { flow: 800, power: 105 },
    { flow: 900, power: 118 }
  ],
  
  npshCurve: [
    { flow: 0, npsh: 2 },
    { flow: 100, npsh: 2.5 },
    { flow: 200, npsh: 3 },
    { flow: 300, npsh: 3.5 },
    { flow: 400, npsh: 4 },
    { flow: 500, npsh: 4.5 },
    { flow: 600, npsh: 5 },
    { flow: 700, npsh: 5.5 },
    { flow: 800, npsh: 6 },
    { flow: 900, npsh: 6.5 }
  ]
}

// 格兰富标准颜色方案
export const GRUNDFOS_COLORS = {
  primary: '#1f5582',      // 格兰富蓝
  secondary: '#ff8c00',    // 效率橙
  power: '#00aa00',        // 功率绿
  npsh: '#ff0000',         // NPSH红
  grid: '#e0e0e0',         // 网格灰
  background: '#ffffff',   // 背景白
  text: '#333333'          // 文字黑
}

// 插值计算函数
export function interpolateValue(flow: number, curveData: Array<{flow: number, [key: string]: number}>): number {
  if (flow <= curveData[0].flow) {
    const keys = Object.keys(curveData[0]).filter(k => k !== 'flow')
    return curveData[0][keys[0]] || 0
  }
  
  if (flow >= curveData[curveData.length - 1].flow) {
    const keys = Object.keys(curveData[curveData.length - 1]).filter(k => k !== 'flow')
    return curveData[curveData.length - 1][keys[0]] || 0
  }
  
  for (let i = 0; i < curveData.length - 1; i++) {
    const current = curveData[i]
    const next = curveData[i + 1]
    
    if (flow >= current.flow && flow <= next.flow) {
      const ratio = (flow - current.flow) / (next.flow - current.flow)
      const keys = Object.keys(current).filter(k => k !== 'flow')
      const key = keys[0]
      const currentValue = current[key] || 0
      const nextValue = next[key] || 0
      return currentValue + (nextValue - currentValue) * ratio
    }
  }
  
  return 0
}

// 根据流量计算所有参数
export function calculatePumpParameters(flow: number): PumpCurveData {
  const bestEfficiencyPoint = GRUNDFOS_CURVE_DATA.efficiencyCurve.reduce((max, p) => p.efficiency > max.efficiency ? p : max, GRUNDFOS_CURVE_DATA.efficiencyCurve[0]);

  return {
    flow,
    head: interpolateValue(flow, GRUNDFOS_CURVE_DATA.headCurve),
    efficiency: interpolateValue(flow, GRUNDFOS_CURVE_DATA.efficiencyCurve),
    power: interpolateValue(flow, GRUNDFOS_CURVE_DATA.powerCurve),
    npsh: interpolateValue(flow, GRUNDFOS_CURVE_DATA.npshCurve),
    bestEfficiencyPoint: {
      q: bestEfficiencyPoint.flow,
      h: interpolateValue(bestEfficiencyPoint.flow, GRUNDFOS_CURVE_DATA.headCurve),
      efficiency: bestEfficiencyPoint.efficiency
    }
  }
}

// 计算单泵效率2的值
export function calculateEfficiency2(flow: number): number {
  return interpolateValue(flow, GRUNDFOS_CURVE_DATA.efficiency2Curve)
}

// 格兰富API模拟
export class GrundfosAPI {
  private static instance: GrundfosAPI
  
  static getInstance(): GrundfosAPI {
    if (!GrundfosAPI.instance) {
      GrundfosAPI.instance = new GrundfosAPI()
    }
    return GrundfosAPI.instance
  }
  
  // 模拟格兰富的QC API
  async postQc(config: any): Promise<any> {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return {
      data: {
        id: `qc_${Date.now()}`,
        complete: true,
        consistent: true,
        groups: [
          {
            label: 'performance',
            questions: [
              {
                label: 'fluid_type',
                text: '泵送流体',
                type: 3,
                options: [
                  { key: 'water', value: '水', selected: true },
                  { key: 'glycol', value: '乙二醇', selected: false },
                  { key: 'oil', value: '油', selected: false }
                ]
              }
            ]
          }
        ]
      }
    }
  }
  
  // 模拟格兰富的结果API
  async getResults(qcId: string): Promise<any> {
    await new Promise(resolve => setTimeout(resolve, 800))
    
    return {
      data: {
        recommended: {
          name: 'NBG 300-250-500/525',
          productnumber: 'AIAF2AESBQQEWW5',
          pumpsystemid: '93351275',
          features: [
            '扬程: 32.8 m',
            '流量: 750 m³/h', 
            '效率: 82%',
            '功率: 89.1 kW'
          ],
          links: [
            {
              rel: 'productimage',
              href: '/content/dam/grundfos/product-images/nbg/nbg-300-250-500-525.jpg'
            }
          ]
        },
        alternatives: [
          {
            name: 'NBG 300-250-500/500',
            productnumber: 'AIAF2AESBQQEWW4',
            pumpsystemid: '93351274',
            features: [
              '扬程: 30.5 m',
              '流量: 720 m³/h',
              '效率: 80%',
              '功率: 85.2 kW'
            ]
          }
        ]
      }
    }
  }
}

// 格兰富数据验证
export function validateGrundfosData(data: any): boolean {
  if (!data || typeof data !== 'object') return false
  
  // 检查必要的格兰富数据结构
  const requiredFields = ['qcId', 'qc', 'results']
  return requiredFields.some(field => field in data)
}

// 格兰富URL生成器
export function generateGrundfosUrls(baseUrl: string, params: Record<string, string>) {
  const urls: Record<string, string> = {
    qcUrl: `${baseUrl}/qc.json`,
    resultUrl: `${baseUrl}/results.json`,
    productUrl: `${baseUrl}/product/{pumpsystemid}`,
    imageUrl: `${baseUrl}/images/{productId}.jpg`
  }
  
  // 替换参数
  Object.keys(urls).forEach(key => {
    Object.keys(params).forEach(param => {
      urls[key] = urls[key].replace(`{${param}}`, params[param])
    })
  })
  
  return urls
}

// 导出默认实例
export const grundfosAPI = GrundfosAPI.getInstance()