{"inputs": [{"type": "promptString", "id": "motherduck-token", "description": "<PERSON><PERSON><PERSON>", "password": true}, {"type": "promptString", "id": "posthog-api-key", "description": "PostHog Personal API Key", "password": true}, {"type": "promptString", "id": "heroku-api-key", "description": "Heroku API Key", "password": true}], "servers": {"serverMemory": {"type": "stdio", "command": "pnpm", "args": ["dlx", "@modelcontextprotocol/server-memory"]}, "sequentialThinking": {"type": "stdio", "command": "pnpm", "args": ["dlx", "@zengwenliang/mcp-server-sequential-thinking"]}, "browserTools": {"type": "stdio", "command": "pnpm", "args": ["dlx", "@agentdeskai/browser-tools-mcp@1.2.1"]}, "puppeteer": {"type": "stdio", "command": "pnpm", "args": ["dlx", "@modelcontextprotocol/server-puppeteer"]}, "postgres": {"type": "stdio", "command": "pnpm", "args": ["dlx", "@modelcontextprotocol/server-postgres", "postgresql://localhost/heat"]}, "filesystem": {"type": "stdio", "command": "pnpm", "args": ["dlx", "@modelcontextprotocol/server-filesystem", "${workspaceFolder}"]}, "playwright": {"type": "stdio", "command": "pnpm", "args": ["dlx", "@playwright/mcp@latest"]}, "magicui": {"type": "stdio", "command": "pnpm", "args": ["dlx", "@magicuidesign/mcp@latest"]}, "context7": {"type": "stdio", "command": "pnpm", "args": ["dlx", "@upstash/context7-mcp@latest"]}, "sentry": {"type": "sse", "url": "https://mcp.sentry.dev/sse"}, "duckdb": {"type": "stdio", "command": "uvx", "args": ["mcp-server-motherduck", "--db-path", ":memory:"], "env": {"motherduck_token": "${input:motherduck-token}"}}, "posthog": {"type": "stdio", "command": "pnpm", "args": ["dlx", "mcp-remote@latest", "https://mcp.posthog.com/sse", "--header", "Authorization:Bearer ${input:posthog-api-key}"]}, "snyk": {"type": "stdio", "command": "snyk", "args": ["mcp", "-t", "stdio", "--experimental"]}, "heroku": {"type": "stdio", "command": "pnpm", "args": ["dlx", "@heroku/mcp-server"], "env": {"HEROKU_API_KEY": "${input:heroku-api-key}"}}, "netlify": {"type": "stdio", "command": "pnpm", "args": ["dlx", "@netlify/mcp"]}, "figma": {"type": "sse", "url": "http://127.0.0.1:3845/sse"}}}