<template>
  <div class="notification-settings">
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><Bell /></el-icon>
          <span>通知设置</span>
        </div>
      </template>
      
      <el-tabs v-model="activeTab">
        <!-- 邮件通知 -->
        <el-tab-pane label="邮件通知" name="email">
          <el-form :model="emailForm" label-width="120px">
            <el-form-item>
              <template #label>
                <div class="form-label">
                  <span>启用邮件通知</span>
                  <el-switch v-model="emailForm.enabled" />
                </div>
              </template>
            </el-form-item>
            
            <div v-if="emailForm.enabled">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="SMTP服务器">
                    <el-input v-model="emailForm.smtpServer" placeholder="smtp.example.com" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="端口">
                    <el-input-number v-model="emailForm.smtpPort" :min="1" :max="65535" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="安全类型">
                    <el-select v-model="emailForm.smtpSecurity">
                      <el-option label="无" value="none" />
                      <el-option label="SSL" value="ssl" />
                      <el-option label="TLS" value="tls" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="发件人名称">
                    <el-input v-model="emailForm.fromName" placeholder="智慧水务平台" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="用户名">
                    <el-input v-model="emailForm.username" placeholder="邮箱用户名" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="密码">
                    <el-input v-model="emailForm.password" type="password" placeholder="邮箱密码" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item label="发件人地址">
                <el-input v-model="emailForm.fromAddress" placeholder="<EMAIL>" />
              </el-form-item>
              
              <el-form-item label="默认收件人">
                <el-select v-model="emailForm.recipients" multiple placeholder="选择或输入收件人">
                  <el-option 
                    v-for="user in settingsStore.users" 
                    :key="user.id" 
                    :label="user.email" 
                    :value="user.email" 
                  />
                </el-select>
              </el-form-item>
              
              <el-form-item>
                <el-button @click="testEmail" :loading="testing">测试邮件发送</el-button>
              </el-form-item>
            </div>
          </el-form>
        </el-tab-pane>
        
        <!-- 短信通知 -->
        <el-tab-pane label="短信通知" name="sms">
          <el-form :model="smsForm" label-width="120px">
            <el-form-item>
              <template #label>
                <div class="form-label">
                  <span>启用短信通知</span>
                  <el-switch v-model="smsForm.enabled" />
                </div>
              </template>
            </el-form-item>
            
            <div v-if="smsForm.enabled">
              <el-form-item label="服务提供商">
                <el-select v-model="smsForm.provider">
                  <el-option label="阿里云" value="aliyun" />
                  <el-option label="腾讯云" value="tencent" />
                  <el-option label="华为云" value="huawei" />
                </el-select>
              </el-form-item>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="API Key">
                    <el-input v-model="smsForm.apiKey" placeholder="API密钥" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="API Secret">
                    <el-input v-model="smsForm.apiSecret" type="password" placeholder="API密钥" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item label="默认收件人">
                <el-select v-model="smsForm.defaultRecipients" multiple placeholder="输入手机号">
                  <el-option 
                    v-for="user in settingsStore.users" 
                    :key="user.id" 
                    :label="user.phone" 
                    :value="user.phone" 
                  />
                </el-select>
              </el-form-item>
              
              <el-form-item>
                <el-button @click="testSms" :loading="testing">测试短信发送</el-button>
              </el-form-item>
            </div>
          </el-form>
        </el-tab-pane>
        
        <!-- 推送通知 -->
        <el-tab-pane label="推送通知" name="push">
          <el-form :model="pushForm" label-width="120px">
            <el-form-item>
              <template #label>
                <div class="form-label">
                  <span>启用推送通知</span>
                  <el-switch v-model="pushForm.enabled" />
                </div>
              </template>
            </el-form-item>
            
            <div v-if="pushForm.enabled">
              <el-form-item label="Web推送">
                <el-switch v-model="pushForm.webPush" />
                <div class="form-tip">在浏览器中显示桌面通知</div>
              </el-form-item>
              
              <el-form-item label="移动端推送">
                <el-switch v-model="pushForm.mobile" />
                <div class="form-tip">向移动应用发送推送通知</div>
              </el-form-item>
              
              <el-form-item label="桌面推送">
                <el-switch v-model="pushForm.desktop" />
                <div class="form-tip">向桌面应用发送推送通知</div>
              </el-form-item>
            </div>
          </el-form>
        </el-tab-pane>
        
        <!-- 声音设置 -->
        <el-tab-pane label="声音设置" name="sound">
          <el-form :model="soundForm" label-width="120px">
            <el-form-item>
              <template #label>
                <div class="form-label">
                  <span>启用声音提醒</span>
                  <el-switch v-model="soundForm.enabled" />
                </div>
              </template>
            </el-form-item>
            
            <div v-if="soundForm.enabled">
              <el-form-item label="音量">
                <el-slider v-model="soundForm.volume" :max="100" show-input />
              </el-form-item>
              
              <el-form-item label="严重警报音">
                <el-select v-model="soundForm.criticalAlertSound">
                  <el-option label="警报声1" value="critical1.mp3" />
                  <el-option label="警报声2" value="critical2.mp3" />
                  <el-option label="警报声3" value="critical3.mp3" />
                </el-select>
                <el-button @click="playSound('critical')" style="margin-left: 10px;">试听</el-button>
              </el-form-item>
              
              <el-form-item label="警告提示音">
                <el-select v-model="soundForm.warningAlertSound">
                  <el-option label="提示音1" value="warning1.mp3" />
                  <el-option label="提示音2" value="warning2.mp3" />
                  <el-option label="提示音3" value="warning3.mp3" />
                </el-select>
                <el-button @click="playSound('warning')" style="margin-left: 10px;">试听</el-button>
              </el-form-item>
              
              <el-form-item label="信息提示音">
                <el-select v-model="soundForm.infoAlertSound">
                  <el-option label="轻提示1" value="info1.mp3" />
                  <el-option label="轻提示2" value="info2.mp3" />
                  <el-option label="轻提示3" value="info3.mp3" />
                </el-select>
                <el-button @click="playSound('info')" style="margin-left: 10px;">试听</el-button>
              </el-form-item>
            </div>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      
      <div class="form-actions">
        <el-button @click="resetSettings">重置</el-button>
        <el-button type="primary" @click="saveSettings" :loading="settingsStore.loading">
          保存设置
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Bell } from '@element-plus/icons-vue'
import { useSettingsStore } from '@/stores/settings'
import { ElMessage } from 'element-plus'

const settingsStore = useSettingsStore()
const activeTab = ref('email')
const testing = ref(false)

// 表单数据
const emailForm = reactive({ ...settingsStore.settings.notification.email })
const smsForm = reactive({ ...settingsStore.settings.notification.sms })
const pushForm = reactive({ ...settingsStore.settings.notification.push })
const soundForm = reactive({ ...settingsStore.settings.notification.sound })

// 方法
const testEmail = async () => {
  testing.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('测试邮件发送成功')
  } catch {
    ElMessage.error('测试邮件发送失败')
  } finally {
    testing.value = false
  }
}

const testSms = async () => {
  testing.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('测试短信发送成功')
  } catch {
    ElMessage.error('测试短信发送失败')
  } finally {
    testing.value = false
  }
}

const playSound = (type: string) => {
  ElMessage.info(`播放${type}提示音`)
}

const saveSettings = async () => {
  try {
    await settingsStore.updateSettings('notification', {
      email: emailForm,
      sms: smsForm,
      push: pushForm,
      sound: soundForm
    })
    ElMessage.success('通知设置保存成功')
  } catch {
    ElMessage.error('通知设置保存失败')
  }
}

const resetSettings = () => {
  Object.assign(emailForm, settingsStore.settings.notification.email)
  Object.assign(smsForm, settingsStore.settings.notification.sms)
  Object.assign(pushForm, settingsStore.settings.notification.push)
  Object.assign(soundForm, settingsStore.settings.notification.sound)
  ElMessage.info('已重置为默认设置')
}
</script>

<style lang="scss" scoped>
.notification-settings {
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
  }
  
  .form-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  
  .form-tip {
    font-size: 12px;
    color: var(--el-text-color-regular);
    margin-top: 4px;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--el-border-color-light);
  }
}
</style>
