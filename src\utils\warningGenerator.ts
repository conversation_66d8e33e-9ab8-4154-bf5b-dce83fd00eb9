import type { 
  WarningInfo, 
  WarningRule, 
  WarningStatistics, 
  DeviceStatus,
  WarningCategory,
  WarningSeverity,
  WarningCondition
} from '@/types'
import dayjs from 'dayjs'

/**
 * 预警数据生成器
 */
export class WarningGenerator {
  private warningHistory: WarningInfo[] = []
  private deviceStatuses: DeviceStatus[] = []
  private warningRules: WarningRule[] = []

  constructor() {
    this.initializeDevices()
    this.initializeRules()
    this.generateHistoricalWarnings()
  }

  /**
   * 初始化设备状态
   */
  private initializeDevices() {
    this.deviceStatuses = [
      {
        id: 'pump_001',
        name: '主水泵#1',
        type: '离心泵',
        status: 'online',
        lastUpdate: dayjs().toISOString(),
        parameters: {
          temperature: 45 + Math.random() * 10,
          vibration: 1.2 + Math.random() * 0.8,
          pressure: 3.5 + Math.random() * 0.5,
          flow: 800 + Math.random() * 200,
          power: 85 + Math.random() * 15,
          efficiency: 78 + Math.random() * 10
        },
        health: 85 + Math.random() * 10,
        nextMaintenance: dayjs().add(15, 'day').format('YYYY-MM-DD')
      },
      {
        id: 'pump_002',
        name: '备用水泵#2',
        type: '离心泵',
        status: 'online',
        lastUpdate: dayjs().toISOString(),
        parameters: {
          temperature: 42 + Math.random() * 8,
          vibration: 1.0 + Math.random() * 0.6,
          pressure: 3.2 + Math.random() * 0.4,
          flow: 600 + Math.random() * 150,
          power: 65 + Math.random() * 12,
          efficiency: 82 + Math.random() * 8
        },
        health: 92 + Math.random() * 5,
        nextMaintenance: dayjs().add(8, 'day').format('YYYY-MM-DD')
      },
      {
        id: 'motor_001',
        name: '驱动电机#1',
        type: '异步电机',
        status: 'online',
        lastUpdate: dayjs().toISOString(),
        parameters: {
          temperature: 65 + Math.random() * 15,
          vibration: 0.8 + Math.random() * 0.4,
          pressure: 0,
          flow: 0,
          power: 90 + Math.random() * 10,
          efficiency: 94 + Math.random() * 3
        },
        health: 88 + Math.random() * 8,
        nextMaintenance: dayjs().add(22, 'day').format('YYYY-MM-DD')
      },
      {
        id: 'valve_001',
        name: '调节阀#1',
        type: '电动调节阀',
        status: 'online',
        lastUpdate: dayjs().toISOString(),
        parameters: {
          temperature: 25 + Math.random() * 5,
          vibration: 0.2 + Math.random() * 0.1,
          pressure: 4.0 + Math.random() * 0.3,
          flow: 850 + Math.random() * 100,
          power: 2 + Math.random() * 1,
          efficiency: 95 + Math.random() * 3
        },
        health: 95 + Math.random() * 3,
        nextMaintenance: dayjs().add(30, 'day').format('YYYY-MM-DD')
      }
    ]
  }

  /**
   * 初始化预警规则
   */
  private initializeRules() {
    this.warningRules = [
      {
        id: 'rule_001',
        name: '高温预警',
        description: '设备温度超过安全阈值',
        category: 'temperature_abnormal',
        severity: 'high',
        enabled: true,
        conditions: [
          { parameter: 'temperature', operator: 'gt', value: 80, duration: 5 }
        ],
        actions: ['notify', 'log', 'reduce_load'],
        cooldownMinutes: 30,
        createdAt: dayjs().subtract(30, 'day').toISOString(),
        updatedAt: dayjs().subtract(5, 'day').toISOString()
      },
      {
        id: 'rule_002',
        name: '振动异常',
        description: '设备振动超过正常范围',
        category: 'vibration_high',
        severity: 'medium',
        enabled: true,
        conditions: [
          { parameter: 'vibration', operator: 'gt', value: 2.5, duration: 10 }
        ],
        actions: ['notify', 'log'],
        cooldownMinutes: 60,
        createdAt: dayjs().subtract(25, 'day').toISOString(),
        updatedAt: dayjs().subtract(3, 'day').toISOString()
      },
      {
        id: 'rule_003',
        name: '效率下降',
        description: '设备效率低于正常水平',
        category: 'efficiency_drop',
        severity: 'medium',
        enabled: true,
        conditions: [
          { parameter: 'efficiency', operator: 'lt', value: 70, duration: 30 }
        ],
        actions: ['notify', 'log', 'schedule_maintenance'],
        cooldownMinutes: 120,
        createdAt: dayjs().subtract(20, 'day').toISOString(),
        updatedAt: dayjs().subtract(1, 'day').toISOString()
      },
      {
        id: 'rule_004',
        name: '维护到期提醒',
        description: '设备维护计划到期提醒',
        category: 'maintenance_due',
        severity: 'low',
        enabled: true,
        conditions: [
          { parameter: 'days_to_maintenance', operator: 'lte', value: 7 }
        ],
        actions: ['notify', 'schedule_maintenance'],
        cooldownMinutes: 1440, // 24小时
        createdAt: dayjs().subtract(15, 'day').toISOString(),
        updatedAt: dayjs().toISOString()
      },
      {
        id: 'rule_005',
        name: '功率异常',
        description: '设备功率超出正常范围',
        category: 'performance_anomaly',
        severity: 'high',
        enabled: true,
        conditions: [
          { parameter: 'power', operator: 'gt', value: 120, duration: 15 }
        ],
        actions: ['notify', 'log', 'emergency_stop'],
        cooldownMinutes: 15,
        createdAt: dayjs().subtract(10, 'day').toISOString(),
        updatedAt: dayjs().toISOString()
      }
    ]
  }

  /**
   * 生成历史预警数据
   */
  private generateHistoricalWarnings() {
    const categories: WarningCategory[] = [
      'equipment_fault', 'performance_anomaly', 'maintenance_due',
      'energy_consumption', 'efficiency_drop', 'vibration_high',
      'temperature_abnormal', 'pressure_abnormal', 'flow_abnormal'
    ]

    const severities: WarningSeverity[] = ['critical', 'high', 'medium', 'low']

    // 生成过去30天的预警记录
    for (let i = 30; i >= 0; i--) {
      const date = dayjs().subtract(i, 'day')
      const warningCount = Math.floor(Math.random() * 5) + 1 // 每天1-5个预警

      for (let j = 0; j < warningCount; j++) {
        const category = categories[Math.floor(Math.random() * categories.length)]
        const severity = severities[Math.floor(Math.random() * severities.length)]
        const device = this.deviceStatuses[Math.floor(Math.random() * this.deviceStatuses.length)]
        
        const warning: WarningInfo = {
          id: `warning_${date.format('YYYYMMDD')}_${j + 1}`,
          type: severity === 'critical' ? 'error' : severity === 'low' ? 'info' : 'warning',
          title: this.generateWarningTitle(category, device.name),
          message: this.generateWarningMessage(category, device.name),
          timestamp: date.add(Math.random() * 24, 'hour').toISOString(),
          resolved: Math.random() > 0.3, // 70%的预警已解决
          category,
          severity,
          source: device.id,
          parameters: {
            deviceName: device.name,
            value: Math.random() * 100,
            threshold: Math.random() * 50 + 50
          },
          actions: [
            {
              id: `action_${Date.now()}_${Math.random()}`,
              name: '通知维护人员',
              description: '发送预警通知给相关维护人员',
              type: 'automatic',
              executed: true,
              executedAt: date.add(5, 'minute').toISOString(),
              result: '通知已发送'
            }
          ]
        }

        if (warning.resolved) {
          warning.resolvedBy = '维护人员'
          warning.resolvedAt = date.add(Math.random() * 12 + 1, 'hour').toISOString()
        }

        this.warningHistory.push(warning)
      }
    }
  }

  /**
   * 生成预警标题
   */
  private generateWarningTitle(category: WarningCategory, deviceName: string): string {
    const titles = {
      equipment_fault: `${deviceName} 设备故障`,
      performance_anomaly: `${deviceName} 性能异常`,
      maintenance_due: `${deviceName} 维护到期`,
      energy_consumption: `${deviceName} 能耗异常`,
      efficiency_drop: `${deviceName} 效率下降`,
      vibration_high: `${deviceName} 振动过高`,
      temperature_abnormal: `${deviceName} 温度异常`,
      pressure_abnormal: `${deviceName} 压力异常`,
      flow_abnormal: `${deviceName} 流量异常`,
      system_error: `${deviceName} 系统错误`
    }
    return titles[category] || `${deviceName} 未知预警`
  }

  /**
   * 生成预警消息
   */
  private generateWarningMessage(category: WarningCategory, deviceName: string): string {
    const messages = {
      equipment_fault: `检测到 ${deviceName} 出现设备故障，请立即检查设备状态`,
      performance_anomaly: `${deviceName} 运行参数超出正常范围，建议进行性能检查`,
      maintenance_due: `${deviceName} 计划维护时间即将到期，请安排维护工作`,
      energy_consumption: `${deviceName} 能耗水平异常，可能存在效率问题`,
      efficiency_drop: `${deviceName} 运行效率显著下降，建议检查设备状态`,
      vibration_high: `${deviceName} 振动水平超过安全阈值，请检查机械部件`,
      temperature_abnormal: `${deviceName} 运行温度异常，请检查冷却系统`,
      pressure_abnormal: `${deviceName} 压力参数超出正常范围，请检查管路系统`,
      flow_abnormal: `${deviceName} 流量参数异常，可能存在堵塞或泄漏`,
      system_error: `${deviceName} 系统出现错误，请检查控制系统`
    }
    return messages[category] || `${deviceName} 出现未知问题，请及时处理`
  }

  /**
   * 获取当前预警列表
   */
  getCurrentWarnings(): WarningInfo[] {
    // 模拟实时预警生成
    const now = dayjs()
    const recentWarnings = this.warningHistory.filter(w => 
      dayjs(w.timestamp).isAfter(now.subtract(24, 'hour'))
    )

    // 随机生成一些新的预警
    if (Math.random() > 0.7) {
      const device = this.deviceStatuses[Math.floor(Math.random() * this.deviceStatuses.length)]
      const categories: WarningCategory[] = ['temperature_abnormal', 'vibration_high', 'efficiency_drop']
      const category = categories[Math.floor(Math.random() * categories.length)]
      
      const newWarning: WarningInfo = {
        id: `warning_${Date.now()}`,
        type: 'warning',
        title: this.generateWarningTitle(category, device.name),
        message: this.generateWarningMessage(category, device.name),
        timestamp: now.toISOString(),
        resolved: false,
        category,
        severity: 'medium',
        source: device.id,
        parameters: {
          deviceName: device.name,
          value: Math.random() * 100,
          threshold: 80
        },
        actions: []
      }
      
      recentWarnings.unshift(newWarning)
    }

    return recentWarnings.sort((a, b) => 
      dayjs(b.timestamp).valueOf() - dayjs(a.timestamp).valueOf()
    )
  }

  /**
   * 获取设备状态列表
   */
  getDeviceStatuses(): DeviceStatus[] {
    // 更新设备参数
    this.deviceStatuses.forEach(device => {
      device.lastUpdate = dayjs().toISOString()
      
      // 模拟参数变化
      device.parameters.temperature += (Math.random() - 0.5) * 2
      device.parameters.vibration += (Math.random() - 0.5) * 0.1
      device.parameters.pressure += (Math.random() - 0.5) * 0.1
      device.parameters.flow += (Math.random() - 0.5) * 20
      device.parameters.power += (Math.random() - 0.5) * 5
      device.parameters.efficiency += (Math.random() - 0.5) * 2
      
      // 确保参数在合理范围内
      device.parameters.temperature = Math.max(20, Math.min(100, device.parameters.temperature))
      device.parameters.vibration = Math.max(0, Math.min(5, device.parameters.vibration))
      device.parameters.pressure = Math.max(0, Math.min(10, device.parameters.pressure))
      device.parameters.flow = Math.max(0, Math.min(2000, device.parameters.flow))
      device.parameters.power = Math.max(0, Math.min(150, device.parameters.power))
      device.parameters.efficiency = Math.max(50, Math.min(100, device.parameters.efficiency))
      
      // 更新健康度
      device.health = Math.max(60, Math.min(100, device.health + (Math.random() - 0.5) * 2))
      
      // 根据参数更新状态
      if (device.parameters.temperature > 80 || device.parameters.vibration > 3 || device.health < 70) {
        device.status = 'fault'
      } else if (device.health < 80) {
        device.status = 'maintenance'
      } else {
        device.status = 'online'
      }
    })

    return this.deviceStatuses
  }

  /**
   * 获取预警规则列表
   */
  getWarningRules(): WarningRule[] {
    return this.warningRules
  }

  /**
   * 获取预警统计
   */
  getWarningStatistics(): WarningStatistics {
    const now = dayjs()
    const today = now.startOf('day')
    const weekStart = now.startOf('week')
    const monthStart = now.startOf('month')

    const todayWarnings = this.warningHistory.filter(w => 
      dayjs(w.timestamp).isAfter(today)
    )
    const weekWarnings = this.warningHistory.filter(w => 
      dayjs(w.timestamp).isAfter(weekStart)
    )
    const monthWarnings = this.warningHistory.filter(w => 
      dayjs(w.timestamp).isAfter(monthStart)
    )

    const resolved = this.warningHistory.filter(w => w.resolved)
    const unresolved = this.warningHistory.filter(w => !w.resolved)

    // 按类别统计
    const byCategory: Record<WarningCategory, number> = {
      equipment_fault: 0,
      performance_anomaly: 0,
      maintenance_due: 0,
      energy_consumption: 0,
      efficiency_drop: 0,
      vibration_high: 0,
      temperature_abnormal: 0,
      pressure_abnormal: 0,
      flow_abnormal: 0,
      system_error: 0
    }

    // 按严重程度统计
    const bySeverity: Record<WarningSeverity, number> = {
      critical: 0,
      high: 0,
      medium: 0,
      low: 0
    }

    this.warningHistory.forEach(warning => {
      byCategory[warning.category]++
      bySeverity[warning.severity]++
    })

    // 计算平均处理时间
    const resolvedWithTime = resolved.filter(w => w.resolvedAt)
    const avgResolutionTime = resolvedWithTime.length > 0
      ? resolvedWithTime.reduce((sum, w) => {
          const duration = dayjs(w.resolvedAt!).diff(dayjs(w.timestamp), 'minute')
          return sum + duration
        }, 0) / resolvedWithTime.length
      : 0

    return {
      total: this.warningHistory.length,
      resolved: resolved.length,
      unresolved: unresolved.length,
      byCategory,
      bySeverity,
      avgResolutionTime: Math.round(avgResolutionTime),
      todayCount: todayWarnings.length,
      weekCount: weekWarnings.length,
      monthCount: monthWarnings.length
    }
  }

  /**
   * 解决预警
   */
  resolveWarning(warningId: string, resolvedBy: string): boolean {
    const warning = this.warningHistory.find(w => w.id === warningId)
    if (warning && !warning.resolved) {
      warning.resolved = true
      warning.resolvedBy = resolvedBy
      warning.resolvedAt = dayjs().toISOString()
      return true
    }
    return false
  }
}

// 导出单例实例
export const warningGenerator = new WarningGenerator()
