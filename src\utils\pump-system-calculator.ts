/**
 * 格兰富风格的水泵系统计算器
 * 实现并联、串联、变速等功能，完全按照格兰富网站的逻辑
 */

// 数据点接口
export interface PumpDataPoint {
  flow: number      // 流量 (m³/h)
  head: number      // 扬程 (m)
  efficiency1: number // 效率1 (%)
  efficiency2: number // 效率2 (%)
  powerP1: number   // 功率P1 (kW)
  powerP2: number   // 功率P2 (kW)
}

export interface NPSHDataPoint {
  flow: number      // 流量 (m³/h)
  npsh: number      // NPSH (m)
}

// 水力布局配置接口
export interface HydraulicLayoutConfig {
  installationType: 'single' | 'parallel'  // 安装类型
  pumpCount: number                         // 泵数量 (2-15)
  standbyPumps: number                      // 备用泵数量 (0-1)
  runningPumps: number                      // 运行泵数量
  seriesConnection: 'none' | 'same' | 'different'  // 串联: 否/相同泵/不同的泵
  solarRelevant: boolean                    // Solar-relevant curves
  variableSpeed: 'none' | 'single' | 'all' // 变速: 不是的/只有一个泵/所有泵
}

// 系统性能结果接口
export interface SystemPerformanceResult {
  singlePumpCurve: PumpDataPoint[]    // 单泵曲线
  systemCurve: PumpDataPoint[]        // 系统曲线
  npshCurve: NPSHDataPoint[]          // NPSH曲线
  operatingRange: {                   // 运行范围
    minFlow: number
    maxFlow: number
    minHead: number
    maxHead: number
  }
  efficiency: {                       // 效率信息
    maxEfficiency: number
    bestEfficiencyPoint: PumpDataPoint
  }
}

/**
 * 格兰富水泵系统计算器类
 */
export class GrundfosSystemCalculator {
  
  /**
   * 计算并联系统性能
   * 根据格兰富的并联计算逻辑
   */
  static calculateParallelSystem(
    singlePumpData: PumpDataPoint[],
    npshData: NPSHDataPoint[],
    config: HydraulicLayoutConfig
  ): SystemPerformanceResult {
    
    const { pumpCount, runningPumps, standbyPumps } = config
    const activePumps = runningPumps || pumpCount - standbyPumps
    
    // 并联系统计算逻辑：
    // 1. 流量 = 单泵流量 × 运行泵数量 × 效率系数
    // 2. 扬程 = 单泵扬程（不变）
    // 3. 效率 = 单泵效率（略有变化）
    // 4. 功率 = 单泵功率 × 运行泵数量
    
    // 并联效率系数 - 根据格兰富网站的实际数据拟合
    // 泵数量越多，效率系数越低，表示管路损失和相互影响
    const getParallelEfficiencyFactor = (pumps: number): number => {
      if (pumps <= 1) return 1.0;
      if (pumps === 2) return 0.98;
      if (pumps === 3) return 0.96;
      if (pumps === 4) return 0.94;
      if (pumps === 5) return 0.92; // 特别为5泵并联添加效率系数
      if (pumps <= 6) return 0.90;
      if (pumps <= 8) return 0.88;
      return 0.85; // 超过8个泵并联时的效率系数
    };
    
    const parallelFactor = getParallelEfficiencyFactor(activePumps);
    
    const systemCurve: PumpDataPoint[] = singlePumpData.map(point => ({
      flow: point.flow * activePumps * parallelFactor,
      head: point.head,
      efficiency1: point.efficiency1 * 0.98, // 并联效率略有下降
      efficiency2: point.efficiency2 * 0.98,
      powerP1: point.powerP1 * activePumps,
      powerP2: point.powerP2 * activePumps
    }))
    
    // NPSH曲线：并联时NPSH基本不变，但流量增加
    const npshCurve: NPSHDataPoint[] = npshData.map(point => ({
      flow: point.flow * activePumps * parallelFactor,
      npsh: point.npsh
    }))
    
    return this.buildSystemResult(singlePumpData, systemCurve, npshCurve)
  }
  
  /**
   * 计算串联系统性能
   * 根据格兰富的串联计算逻辑
   */
  static calculateSeriesSystem(
    singlePumpData: PumpDataPoint[],
    npshData: NPSHDataPoint[],
    config: HydraulicLayoutConfig,
    seriesPumpCount: number = 2
  ): SystemPerformanceResult {
    
    // 串联系统计算逻辑：
    // 1. 流量 = 单泵流量（不变）
    // 2. 扬程 = 单泵扬程 × 串联泵数量
    // 3. 效率 = 单泵效率 × 串联效率系数
    // 4. 功率 = 单泵功率 × 串联泵数量
    
    const seriesEfficiencyFactor = config.seriesConnection === 'same' ? 0.95 : 0.90
    
    const systemCurve: PumpDataPoint[] = singlePumpData.map(point => ({
      flow: point.flow,
      head: point.head * seriesPumpCount,
      efficiency1: point.efficiency1 * seriesEfficiencyFactor,
      efficiency2: point.efficiency2 * seriesEfficiencyFactor,
      powerP1: point.powerP1 * seriesPumpCount,
      powerP2: point.powerP2 * seriesPumpCount
    }))
    
    // NPSH曲线：串联时第一级泵的NPSH要求
    const npshCurve: NPSHDataPoint[] = npshData.map(point => ({
      flow: point.flow,
      npsh: point.npsh * 1.1 // 串联时NPSH要求略有增加
    }))
    
    return this.buildSystemResult(singlePumpData, systemCurve, npshCurve)
  }
  
  /**
   * 计算变速系统性能
   * 根据相似定律计算变速性能
   */
  static calculateVariableSpeedSystem(
    singlePumpData: PumpDataPoint[],
    npshData: NPSHDataPoint[],
    speedRatio: number // 转速比例 (0.5 - 1.2)
  ): SystemPerformanceResult {
    
    // 相似定律：
    // Q2/Q1 = (n2/n1)
    // H2/H1 = (n2/n1)²
    // P2/P1 = (n2/n1)³
    // NPSH2/NPSH1 = (n2/n1)²
    
    const systemCurve: PumpDataPoint[] = singlePumpData.map(point => ({
      flow: point.flow * speedRatio,
      head: point.head * Math.pow(speedRatio, 2),
      efficiency1: point.efficiency1, // 效率基本不变
      efficiency2: point.efficiency2,
      powerP1: point.powerP1 * Math.pow(speedRatio, 3),
      powerP2: point.powerP2 * Math.pow(speedRatio, 3)
    }))
    
    const npshCurve: NPSHDataPoint[] = npshData.map(point => ({
      flow: point.flow * speedRatio,
      npsh: point.npsh * Math.pow(speedRatio, 2)
    }))
    
    return this.buildSystemResult(singlePumpData, systemCurve, npshCurve)
  }
  
  /**
   * 计算复合系统性能（并联+串联+变速）
   */
  static calculateComplexSystem(
    singlePumpData: PumpDataPoint[],
    npshData: NPSHDataPoint[],
    config: HydraulicLayoutConfig,
    speedRatio: number = 1.0,
    variableSpeedPumpCount: number = 0
  ): SystemPerformanceResult {
    
    let result = this.buildSystemResult(singlePumpData, singlePumpData, npshData)

    // 1. 处理混合变频系统（部分泵变频，部分泵定频）
    if (config.installationType === 'parallel' && variableSpeedPumpCount > 0 && speedRatio !== 1.0) {
      result = this.calculateMixedVariableSpeedSystem(
        singlePumpData,
        npshData,
        config,
        speedRatio,
        variableSpeedPumpCount
      )
    } else {
      // 2. 先应用变速（全部泵变频）
      if (speedRatio !== 1.0) {
        result = this.calculateVariableSpeedSystem(
          result.systemCurve,
          result.npshCurve,
          speedRatio
        )
      }

      // 3. 再应用并联
      if (config.installationType === 'parallel' && config.pumpCount > 1) {
        result = this.calculateParallelSystem(
          result.systemCurve,
          result.npshCurve,
          config
        )
      }
    }
    
    // 3. 最后应用串联
    if (config.seriesConnection !== 'none') {
      const seriesPumpCount = 2 // 默认2台串联
      result = this.calculateSeriesSystem(
        result.systemCurve,
        result.npshCurve,
        config,
        seriesPumpCount
      )
    }
    
    return result
  }

  /**
   * 计算混合变频系统性能（部分泵变频，部分泵定频）
   */
  static calculateMixedVariableSpeedSystem(
    singlePumpData: PumpDataPoint[],
    npshData: NPSHDataPoint[],
    config: HydraulicLayoutConfig,
    speedRatio: number,
    variableSpeedPumpCount: number
  ): SystemPerformanceResult {

    const { runningPumps } = config
    const constantSpeedPumpCount = runningPumps - variableSpeedPumpCount

    // 计算变频泵的性能
    const variableSpeedCurve: PumpDataPoint[] = singlePumpData.map(point => ({
      flow: point.flow * speedRatio,
      head: point.head * Math.pow(speedRatio, 2),
      efficiency1: point.efficiency1,
      efficiency2: point.efficiency2,
      powerP1: point.powerP1 * Math.pow(speedRatio, 3),
      powerP2: point.powerP2 * Math.pow(speedRatio, 3)
    }))

    // 并联效率系数 - 根据格兰富网站的实际数据拟合
    // 泵数量越多，效率系数越低，表示管路损失和相互影响
    const getParallelEfficiencyFactor = (pumps: number): number => {
      if (pumps <= 1) return 1.0;
      if (pumps === 2) return 0.98;
      if (pumps === 3) return 0.96;
      if (pumps === 4) return 0.94;
      if (pumps === 5) return 0.92; // 特别为5泵并联添加效率系数
      if (pumps <= 6) return 0.90;
      if (pumps <= 8) return 0.88;
      return 0.85; // 超过8个泵并联时的效率系数
    };
    
    const parallelFactor = getParallelEfficiencyFactor(runningPumps);

    // 混合系统的总性能：变频泵 + 定频泵
    const systemCurve: PumpDataPoint[] = singlePumpData.map((point, index) => {
      const variablePoint = variableSpeedCurve[index]

      // 计算总流量时应用并联效率系数
      const totalFlow = ((point.flow * constantSpeedPumpCount) + 
                         (variablePoint.flow * variableSpeedPumpCount)) * parallelFactor;

      return {
        flow: totalFlow,
        head: point.head, // 并联时扬程不变
        efficiency1: (point.efficiency1 * constantSpeedPumpCount + variablePoint.efficiency1 * variableSpeedPumpCount) / runningPumps,
        efficiency2: (point.efficiency2 * constantSpeedPumpCount + variablePoint.efficiency2 * variableSpeedPumpCount) / runningPumps,
        powerP1: (point.powerP1 * constantSpeedPumpCount) + (variablePoint.powerP1 * variableSpeedPumpCount),
        powerP2: (point.powerP2 * constantSpeedPumpCount) + (variablePoint.powerP2 * variableSpeedPumpCount)
      }
    })

    // NPSH曲线：取最严格的要求
    const npshCurve: NPSHDataPoint[] = npshData.map((point, index) => {
      const variableNpsh = point.npsh * Math.pow(speedRatio, 2)
      const constantNpsh = point.npsh

      // 应用相同的并联效率系数到流量上
      const totalFlow = ((point.flow * constantSpeedPumpCount) + 
                         (point.flow * speedRatio * variableSpeedPumpCount)) * parallelFactor;

      return {
        flow: totalFlow,
        npsh: Math.max(variableNpsh, constantNpsh) // 取更严格的NPSH要求
      }
    })

    return this.buildSystemResult(singlePumpData, systemCurve, npshCurve)
  }

  /**
   * 构建系统结果
   */
  private static buildSystemResult(
    singlePumpCurve: PumpDataPoint[],
    systemCurve: PumpDataPoint[],
    npshCurve: NPSHDataPoint[]
  ): SystemPerformanceResult {
    
    // 计算运行范围
    const flows = systemCurve.map(p => p.flow)
    const heads = systemCurve.map(p => p.head)
    
    // 找到最佳效率点
    const maxEffPoint = systemCurve.reduce((max, point) => 
      point.efficiency1 > max.efficiency1 ? point : max
    )
    
    return {
      singlePumpCurve,
      systemCurve,
      npshCurve,
      operatingRange: {
        minFlow: Math.min(...flows),
        maxFlow: Math.max(...flows),
        minHead: Math.min(...heads),
        maxHead: Math.max(...heads)
      },
      efficiency: {
        maxEfficiency: maxEffPoint.efficiency1,
        bestEfficiencyPoint: maxEffPoint
      }
    }
  }
  
  /**
   * 获取格兰富标准配置选项
   */
  static getGrundfosConfigOptions() {
    return {
      installationTypes: [
        { value: 'single', label: '单泵' },
        { value: 'parallel', label: '并联泵' }
      ],
      parallelPumpCounts: Array.from({ length: 14 }, (_, i) => ({
        value: i + 2,
        label: `${i + 2}个并联泵`
      })),
      standbyOptions: [
        { value: 0, label: '0' },
        { value: 1, label: '1' }
      ],
      seriesOptions: [
        { value: 'none', label: '否' },
        { value: 'same', label: '相同泵' },
        { value: 'different', label: '不同的泵' }
      ],
      variableSpeedOptions: [
        { value: 'none', label: '不是的' },
        { value: 'single', label: '只有一个泵' },
        { value: 'all', label: '所有泵' }
      ]
    }
  }
}

/**
 * 格兰富系统配置验证器
 */
export class GrundfosConfigValidator {
  
  /**
   * 验证水力布局配置
   */
  static validateConfig(config: HydraulicLayoutConfig): string[] {
    const errors: string[] = []
    
    // 验证泵数量
    if (config.installationType === 'parallel') {
      if (config.pumpCount < 2 || config.pumpCount > 15) {
        errors.push('并联泵数量必须在2-15之间')
      }
      
      if (config.standbyPumps > config.pumpCount - 1) {
        errors.push('备用泵数量不能超过总泵数减1')
      }
      
      if (config.runningPumps > config.pumpCount - config.standbyPumps) {
        errors.push('运行泵数量不能超过可用泵数量')
      }
    }
    
    return errors
  }
  
  /**
   * 获取推荐配置
   */
  static getRecommendedConfig(
    requiredFlow: number,
    requiredHead: number,
    singlePumpData: PumpDataPoint[]
  ): HydraulicLayoutConfig {
    
    // 找到单泵最佳效率点
    const bepPoint = singlePumpData.reduce((max, point) => 
      point.efficiency1 > max.efficiency1 ? point : max
    )
    
    // 根据需求推荐配置
    const flowRatio = requiredFlow / bepPoint.flow
    const headRatio = requiredHead / bepPoint.head
    
    if (flowRatio > 1.5 && headRatio <= 1.2) {
      // 推荐并联
      return {
        installationType: 'parallel',
        pumpCount: Math.ceil(flowRatio),
        standbyPumps: 1,
        runningPumps: Math.ceil(flowRatio),
        seriesConnection: 'none',
        solarRelevant: false,
        variableSpeed: 'none'
      }
    } else if (headRatio > 1.5 && flowRatio <= 1.2) {
      // 推荐串联
      return {
        installationType: 'single',
        pumpCount: 1,
        standbyPumps: 0,
        runningPumps: 1,
        seriesConnection: 'same',
        solarRelevant: false,
        variableSpeed: 'none'
      }
    } else {
      // 推荐单泵
      return {
        installationType: 'single',
        pumpCount: 1,
        standbyPumps: 0,
        runningPumps: 1,
        seriesConnection: 'none',
        solarRelevant: false,
        variableSpeed: 'none'
      }
    }
  }
}
