import { defineStore } from 'pinia'
import type {
  WarningInfo,
  WarningRule,
  WarningStatistics,
  DeviceStatus,
  WarningCategory,
  WarningSeverity
} from '@/types'
import { warningGenerator } from '@/utils/warningGenerator'
// import { warningDAO, deviceDAO } from '@/utils/dataAccess'
// import { dbManager } from '@/utils/databaseConfig'

export const useWarningStore = defineStore('warning', {
  state: () => ({
    // 当前预警列表
    warnings: warningGenerator.getCurrentWarnings() as WarningInfo[],

    // 设备状态列表
    devices: warningGenerator.getDeviceStatuses() as DeviceStatus[],

    // 预警规则
    rules: warningGenerator.getWarningRules() as WarningRule[],
    
    // 预警统计
    statistics: warningGenerator.getWarningStatistics() as WarningStatistics,
    
    // 筛选条件
    filters: {
      category: null as WarningCategory | null,
      severity: null as WarningSeverity | null,
      resolved: null as boolean | null,
      dateRange: null as [string, string] | null
    },
    
    // 加载状态
    loading: false,
    
    // 实时更新
    realTimeEnabled: false,
    updateInterval: null as NodeJS.Timeout | null,
    
    // 选中的预警
    selectedWarning: null as WarningInfo | null,

    // 预警详情对话框
    showWarningDetail: false,

    // 规则配置对话框
    showRuleConfig: false,

    // 设备监控对话框
    showDeviceMonitor: false,

    // 数据加载状态
    dataLoaded: false
  }),

  getters: {
    // 获取筛选后的预警列表
    getFilteredWarnings: (state) => {
      let filtered = [...state.warnings]
      
      if (state.filters.category) {
        filtered = filtered.filter(w => w.category === state.filters.category)
      }
      
      if (state.filters.severity) {
        filtered = filtered.filter(w => w.severity === state.filters.severity)
      }
      
      if (state.filters.resolved !== null) {
        filtered = filtered.filter(w => w.resolved === state.filters.resolved)
      }
      
      if (state.filters.dateRange) {
        const [start, end] = state.filters.dateRange
        filtered = filtered.filter(w => {
          const warningDate = new Date(w.timestamp)
          return warningDate >= new Date(start) && warningDate <= new Date(end)
        })
      }
      
      return filtered.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      )
    },
    
    // 获取未解决的预警数量
    getUnresolvedCount: (state) => {
      return state.warnings.filter(w => !w.resolved).length
    },
    
    // 获取严重预警数量
    getCriticalCount: (state) => {
      return state.warnings.filter(w => 
        !w.resolved && (w.severity === 'critical' || w.severity === 'high')
      ).length
    },
    
    // 获取在线设备数量
    getOnlineDevicesCount: (state) => {
      return state.devices.filter(d => d.status === 'online').length
    },
    
    // 获取故障设备数量
    getFaultDevicesCount: (state) => {
      return state.devices.filter(d => d.status === 'fault').length
    },
    
    // 获取需要维护的设备数量
    getMaintenanceDevicesCount: (state) => {
      return state.devices.filter(d => d.status === 'maintenance').length
    },
    
    // 获取今日预警趋势
    getTodayWarningTrend: (state) => {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      const todayWarnings = state.warnings.filter(w => 
        new Date(w.timestamp) >= today
      )
      
      // 按小时分组
      const hourlyCount = Array.from({ length: 24 }, (_, i) => {
        const hour = i
        const count = todayWarnings.filter(w => {
          const warningHour = new Date(w.timestamp).getHours()
          return warningHour === hour
        }).length
        return { hour, count }
      })
      
      return hourlyCount
    },
    
    // 获取设备健康度分布
    getDeviceHealthDistribution: (state) => {
      const distribution = {
        excellent: 0, // 90-100
        good: 0,      // 80-89
        fair: 0,      // 70-79
        poor: 0       // <70
      }
      
      state.devices.forEach(device => {
        if (device.health >= 90) distribution.excellent++
        else if (device.health >= 80) distribution.good++
        else if (device.health >= 70) distribution.fair++
        else distribution.poor++
      })
      
      return distribution
    }
  },

  actions: {
    // 初始化数据
    async initializeData() {
      console.log('Database functionality disabled, using memory data')
      this.dataLoaded = true
    },

    // 从数据库加载数据
    async loadFromDatabase() {
      console.log('Database loading disabled')
    },

    // 生成初始数据
    async seedInitialData() {
      console.log('Database seeding disabled')
    },

    // 加载内存数据（备用方案）
    loadMemoryData() {
      console.log('Using memory data')
    },

    // 加载预警数据
    async loadWarnings() {
      this.loading = true
      try {
        // 使用内存数据
        console.log('Loading warnings from memory')
      } catch (error) {
        console.error('加载预警数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 解决预警
    async resolveWarning(warningId: string, resolvedBy: string = '操作员') {
      const warning = this.warnings.find(w => w.id === warningId)
      if (warning && !warning.resolved) {
        warning.resolved = true
        warning.resolvedBy = resolvedBy
        warning.resolvedAt = new Date().toISOString()

        // 更新统计数据
        this.statistics = warningGenerator.getWarningStatistics()
        return true
      }
      return false
    },
    
    // 批量解决预警
    async batchResolveWarnings(warningIds: string[], resolvedBy: string = '操作员') {
      let successCount = 0
      
      for (const id of warningIds) {
        const success = await this.resolveWarning(id, resolvedBy)
        if (success) successCount++
      }
      
      return successCount
    },
    
    // 设置筛选条件
    setFilter(key: keyof typeof this.filters, value: any) {
      this.filters[key] = value
    },
    
    // 清除筛选条件
    clearFilters() {
      this.filters = {
        category: null,
        severity: null,
        resolved: null,
        dateRange: null
      }
    },
    
    // 显示预警详情
    showWarningDetails(warning: WarningInfo) {
      this.selectedWarning = warning
      this.showWarningDetail = true
    },
    
    // 隐藏预警详情
    hideWarningDetails() {
      this.selectedWarning = null
      this.showWarningDetail = false
    },
    
    // 开启实时更新
    startRealTimeUpdate() {
      if (this.updateInterval) return
      
      this.realTimeEnabled = true
      this.updateInterval = setInterval(() => {
        this.loadWarnings()
      }, 30000) // 每30秒更新一次
    },
    
    // 停止实时更新
    stopRealTimeUpdate() {
      if (this.updateInterval) {
        clearInterval(this.updateInterval)
        this.updateInterval = null
      }
      this.realTimeEnabled = false
    },
    
    // 添加预警规则
    addWarningRule(rule: Omit<WarningRule, 'id' | 'createdAt' | 'updatedAt'>) {
      const newRule: WarningRule = {
        ...rule,
        id: `rule_${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      this.rules.push(newRule)
      return newRule
    },
    
    // 更新预警规则
    updateWarningRule(ruleId: string, updates: Partial<WarningRule>) {
      const rule = this.rules.find(r => r.id === ruleId)
      if (rule) {
        Object.assign(rule, updates, { updatedAt: new Date().toISOString() })
        return true
      }
      return false
    },
    
    // 删除预警规则
    deleteWarningRule(ruleId: string) {
      const index = this.rules.findIndex(r => r.id === ruleId)
      if (index !== -1) {
        this.rules.splice(index, 1)
        return true
      }
      return false
    },
    
    // 启用/禁用预警规则
    toggleWarningRule(ruleId: string) {
      const rule = this.rules.find(r => r.id === ruleId)
      if (rule) {
        rule.enabled = !rule.enabled
        rule.updatedAt = new Date().toISOString()
        return rule.enabled
      }
      return false
    },
    
    // 获取设备详细信息
    getDeviceDetails(deviceId: string) {
      return this.devices.find(d => d.id === deviceId)
    },
    
    // 更新设备状态
    updateDeviceStatus(deviceId: string, status: DeviceStatus['status']) {
      const device = this.devices.find(d => d.id === deviceId)
      if (device) {
        device.status = status
        device.lastUpdate = new Date().toISOString()
        return true
      }
      return false
    },
    
    // 导出预警报告
    async exportWarningReport(options: {
      format: 'pdf' | 'excel' | 'csv'
      dateRange?: [string, string]
      includeResolved?: boolean
    }) {
      // 模拟导出过程
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            success: true,
            filename: `warning_report_${new Date().toISOString().split('T')[0]}.${options.format}`,
            url: '#' // 实际应用中这里应该是下载链接
          })
        }, 2000)
      })
    },
    
    // 重置数据
    resetData() {
      this.warnings = []
      this.devices = []
      this.rules = []
      this.statistics = {
        total: 0,
        resolved: 0,
        unresolved: 0,
        byCategory: {
          equipment_fault: 0,
          performance_anomaly: 0,
          maintenance_due: 0,
          energy_consumption: 0,
          efficiency_drop: 0,
          vibration_high: 0,
          temperature_abnormal: 0,
          pressure_abnormal: 0,
          flow_abnormal: 0,
          system_error: 0
        },
        bySeverity: {
          critical: 0,
          high: 0,
          medium: 0,
          low: 0
        },
        avgResolutionTime: 0,
        todayCount: 0,
        weekCount: 0,
        monthCount: 0
      }
      this.clearFilters()
      this.hideWarningDetails()
    }
  }
})
