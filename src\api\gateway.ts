import { ref } from 'vue';
import type { GatewayDevice, MQTTConfig, InverterData, GatewayError } from '../types/gateway';
import { mqttService } from '../services/mqtt-service';

// 模拟API响应延迟
const simulateDelay = (ms = 300) => new Promise(resolve => setTimeout(resolve, ms));

// 模拟设备存储
let devices: GatewayDevice[] = [];
let nextId = 1;

/**
 * 获取所有网关设备
 */
export async function getAllDevices(): Promise<GatewayDevice[]> {
  await simulateDelay();
  return [...devices];
}

/**
 * 根据ID获取设备
 */
export async function getDeviceById(id: string): Promise<GatewayDevice | null> {
  await simulateDelay();
  const device = devices.find(d => d.id === id);
  return device ? { ...device } : null;
}

/**
 * 创建新设备
 */
export async function createDevice(device: Omit<GatewayDevice, 'id' | 'status'>): Promise<GatewayDevice> {
  await simulateDelay();
  
  const newDevice: GatewayDevice = {
    ...device,
    id: `device-${nextId++}`,
    status: 'offline'
  };
  
  devices.push(newDevice);
  return { ...newDevice };
}

/**
 * 更新设备
 */
export async function updateDevice(id: string, updates: Partial<GatewayDevice>): Promise<GatewayDevice | null> {
  await simulateDelay();
  
  const deviceIndex = devices.findIndex(d => d.id === id);
  if (deviceIndex === -1) return null;
  
  // 不允许直接更新设备状态
  const { status, ...safeUpdates } = updates;
  
  devices[deviceIndex] = {
    ...devices[deviceIndex],
    ...safeUpdates
  };
  
  return { ...devices[deviceIndex] };
}

/**
 * 删除设备
 */
export async function deleteDevice(id: string): Promise<boolean> {
  await simulateDelay();
  
  const initialLength = devices.length;
  devices = devices.filter(d => d.id !== id);
  
  return devices.length < initialLength;
}

/**
 * 获取设备MQTT配置
 */
export async function getDeviceMQTTConfig(deviceId: string): Promise<MQTTConfig | null> {
  await simulateDelay();
  
  const device = devices.find(d => d.id === deviceId);
  if (!device || !device.mqttConfig) return null;
  
  return { ...device.mqttConfig };
}

/**
 * 更新设备MQTT配置
 */
export async function updateDeviceMQTTConfig(deviceId: string, config: MQTTConfig): Promise<boolean> {
  await simulateDelay();
  
  const deviceIndex = devices.findIndex(d => d.id === deviceId);
  if (deviceIndex === -1) return false;
  
  devices[deviceIndex].mqttConfig = { ...config };
  return true;
}

/**
 * 测试MQTT连接
 */
export async function testMQTTConnection(config: MQTTConfig): Promise<{ success: boolean; message: string }> {
  try {
    const success = await mqttService.testConnection(config);
    
    return {
      success,
      message: success ? '连接成功' : '连接失败，请检查配置'
    };
  } catch (error) {
    return {
      success: false,
      message: `连接错误: ${(error as Error).message}`
    };
  }
}

/**
 * 连接设备
 */
export async function connectDevice(deviceId: string): Promise<boolean> {
  await simulateDelay();
  
  const device = devices.find(d => d.id === deviceId);
  if (!device || !device.mqttConfig) return false;
  
  try {
    await mqttService.connect(device.mqttConfig);
    
    // 更新设备状态为在线
    const deviceIndex = devices.findIndex(d => d.id === deviceId);
    if (deviceIndex !== -1) {
      devices[deviceIndex].status = 'online';
      devices[deviceIndex].lastConnectTime = new Date().toISOString();
    }
    
    return true;
  } catch (error) {
    console.error('设备连接失败:', error);
    return false;
  }
}

/**
 * 断开设备连接
 */
export async function disconnectDevice(deviceId: string): Promise<boolean> {
  await simulateDelay();
  
  try {
    await mqttService.disconnect();
    
    // 更新设备状态为离线
    const deviceIndex = devices.findIndex(d => d.id === deviceId);
    if (deviceIndex !== -1) {
      devices[deviceIndex].status = 'offline';
    }
    
    return true;
  } catch (error) {
    console.error('断开设备连接失败:', error);
    return false;
  }
}

/**
 * 获取设备实时数据
 */
export async function getDeviceData(deviceId: string): Promise<InverterData | null> {
  await simulateDelay();
  return mqttService.getDeviceData(deviceId);
}

/**
 * 获取设备错误日志
 */
export async function getDeviceErrors(deviceId: string): Promise<GatewayError[]> {
  await simulateDelay();
  return mqttService.getDeviceErrors(deviceId);
}

/**
 * 解决错误
 */
export async function resolveDeviceError(errorId: string): Promise<boolean> {
  await simulateDelay();
  return mqttService.resolveError(errorId);
}

/**
 * 重启设备
 * 这是一个模拟方法，实际应发送命令到设备
 */
export async function restartDevice(deviceId: string): Promise<boolean> {
  await simulateDelay(2000); // 重启需要更长时间
  
  const deviceIndex = devices.findIndex(d => d.id === deviceId);
  if (deviceIndex === -1) return false;
  
  // 模拟设备重启过程
  devices[deviceIndex].status = 'offline';
  
  // 模拟设备重启后恢复在线
  setTimeout(() => {
    if (devices[deviceIndex]) {
      devices[deviceIndex].status = 'online';
      devices[deviceIndex].lastConnectTime = new Date().toISOString();
    }
  }, 5000);
  
  return true;
}

/**
 * 发送命令到设备
 */
export async function sendCommand(deviceId: string, command: string, params: Record<string, any> = {}): Promise<boolean> {
  await simulateDelay();
  
  const device = devices.find(d => d.id === deviceId);
  if (!device || !device.mqttConfig) return false;
  
  try {
    const commandTopic = `${device.mqttConfig.publishTopic}/${deviceId}/command`;
    await mqttService.publish(commandTopic, {
      command,
      params,
      timestamp: Date.now()
    });
    
    return true;
  } catch (error) {
    console.error('发送命令失败:', error);
    return false;
  }
}

// 导出一个设备连接状态的响应式引用，以便组件可以监视连接状态
export const deviceConnectionStatus = ref<'connecting' | 'connected' | 'disconnected'>('disconnected'); 